﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <procedures>
	  <procedure owner="" name="WO_EquipItemCheckList_RefreshSignalPrep_insert" grant="">
		  <parameters>
			  <parameter name="OrderId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[ 
				INSERT INTO WO_TestOrderEquipItemCheckListSignal
				(OrderCheckId,SignalId, OrderId, EquipmentId, LimitDown, LimitUp)
				SELECT OrderCheckId,SignalId, OrderId, EquipmentId, LimitDown, LimitUp
				FROM (
					SELECT cc.OrderId, cc.OrderCheckId, cc.EquipmentId, s.SignalId, cc.LimitDown, cc.LimitUp
					FROM TBL_Signal s
					INNER JOIN TBL_Equipment e ON e.EquipmentTemplateId=s.EquipmentTemplateId
					INNER JOIN (
						SELECT c.OrderId, c.OrderCheckId, c.BaseTypeName, c.EquipmentId, c.BaseTypeId, c.LimitDown, c.LimitUp
						FROM WO_TestOrderEquipItemCheckList c
						WHERE 
							c.OrderId = :OrderId and c.CheckTypeId = 1 and c.IsPass=0)
						cc ON cc.EquipmentId=e.EquipmentId AND  s.BaseTypeId = cc.BaseTypeId 
				) r
				WHERE NOT EXISTS(
					SELECT 1 FROM WO_TestOrderEquipItemCheckListSignal 
					WHERE WO_TestOrderEquipItemCheckListSignal.OrderCheckId=r.OrderCheckId and WO_TestOrderEquipItemCheckListSignal.SignalId=r.SignalId);
        
			]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="WO_EquipItemCheckList_RefreshSignal_update1" grant="">
		  <parameters>
			  <parameter name="OrderId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[ 
			
				update WO_TestOrderEquipItemCheckListSignal c, TBL_ActiveSignal a 
				set c.FloatValue=a.FloatValue, c.SamplerTime = a.SampleTime, c.BaseTypeId = a.BaseTypeId
				where 
					OrderId = :OrderId and IsPass=0 and 
					c.EquipmentId=a.EquipmentId and c.SignalId=a.SignalId;

	
				update WO_TestOrderEquipItemCheckListSignal a
				set a.IsPass=1
				where 
					a.OrderId = :OrderId and a.IsPass=0 and 
					(
						(a.FloatValue >=a.LimitDown and a.FloatValue <=a.LimitUp)
						or
						(a.FloatValue is not null and (a.LimitDown is null or a.LimitUp is null)) 
					);	
			
			]]>
		  </body>
	  </procedure>
    <procedure owner="" name="WO_EquipItemCheckList_RefreshSignal_updateSignal" grant="">
      <parameters>
        <parameter name="OrderId"  type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 	
        UPDATE WO_TestOrderEquipItemCheckListSignal
        SET IsPass = 1
        WHERE OrderCheckId IN (SELECT OrderCheckId FROM WO_TestOrderEquipItemCheckList 
        WHERE CheckTypeId = 1 AND IsPass = 0 AND OrderId = :OrderId);
		]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_EquipItemCheckList_RefreshSignal_updateEquip" grant="">
      <parameters>
        <parameter name="OrderId"  type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 	
        UPDATE WO_TestOrderEquipItemCheckList AS c
        INNER JOIN WO_TestOrderEquipItemCheckListSignal AS s ON c.OrderCheckId = s.OrderCheckId
        SET c.PassNote = TO_CHAR(s.FloatValue), c.SamplerValue = s.FloatValue, c.SamplerTime = s.SamplerTime
        WHERE c.CheckTypeId = 1 AND c.IsPass = 0 AND s.IsPass = 1 AND c.OrderId = :OrderId; 
		]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_EquipItemCheckList_RefreshCmd" grant="">
		  <parameters>
			  <parameter name="OrderId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[ 
			  
				UPDATE WO_TestOrderEquipItemCheckList c, TBL_ActiveControl e
				SET c.IsPass = 1, c.PassNote = CONCAT('控制名称:', e.ControlName), c.SamplerTime = e.StartTime
				WHERE c.OrderId = :OrderId AND c.CheckTypeId = 3 AND c.IsPass = 0 AND c.HasConfig = 1
						AND e.EquipmentId = c.EquipmentId 
						AND FLOOR(e.BaseTypeId / 1000) = FLOOR(c.BaseTypeId / 1000) 
						AND e.ControlResult = '控制成功';

				-- 更新与 TBL_HistoryControl 关联的 WO_TestOrderEquipItemCheckList
				UPDATE WO_TestOrderEquipItemCheckList c, TBL_HistoryControl e
				SET c.IsPass = 1, c.PassNote = CONCAT('控制名称:', e.ControlName), c.SamplerTime = e.StartTime
				WHERE c.OrderId = :OrderId AND c.CheckTypeId = 3 AND c.IsPass = 0 AND c.HasConfig = 1
						AND e.EquipmentId = c.EquipmentId 
						AND FLOOR(e.BaseTypeId / 1000) = FLOOR(c.BaseTypeId / 1000) 
						AND e.ControlResult = '控制成功';
			 ]]>
		  </body>
	  </procedure>

	  <procedure owner="" name="WO_Debug_CheckItemSignal_select1" grant="">
		  <parameters>
			  <parameter name="OrderId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[ 
				SELECT OrderCheckId, OrderId, CheckTypeId, CheckType, HasConfig, IsPass, PassNote, PassFailReason, EquipmentId, 
				EquipmentName, EquipmentCategoryName, BaseEquipmentId, BaseEquipmentName, 
				BaseTypeId, BaseTypeName, Unit, LimitDown, LimitUp, Note, 
				CheckId, EquipmentLogicClass, LogicClass, StandardName, SignalType, SamplerValue, SamplerTime
				FROM WO_TestOrderEquipItemCheckList
				where WO_TestOrderEquipItemCheckList.OrderCheckId= :OrderId;
			 ]]>
		  </body>
	  </procedure>

    <procedure owner="" name="RefreshEvent_ValueInt" grant="">
      <body>
        <![CDATA[ 
				select ValueInt from WO_SysConfig where WO_SysConfig.ConfigId=1;
			 ]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_EquipItemCheckList_RefreshActiveEvent" grant="">
      <parameters>
        <parameter name="OrderId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ValueInt"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EventStartTime1" type="datetime" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
				update  WO_TestOrderEquipItemCheckList c, TBL_ActiveEvent e
	      set c.IsPass=1, c.PassNote=concat('告警名称:',e.EventName,';描述:',e.Meanings), c.SamplerTime=e.StartTime			
	      where 
		    c.OrderId = :OrderId and c.CheckTypeId = 2 and c.IsPass=0 and c.HasConfig=1
		    and e.EquipmentId=c.EquipmentId and e.StartTime >= :EventStartTime1
		    and TRUNC(e.BaseTypeId / 1000)=TRUNC(c.BaseTypeId / 1000); 
			 ]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_EquipItemCheckList_RefreshHistoryEvent" grant="">
      <parameters>
        <parameter name="OrderId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ValueInt"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EventStartTime1" type="datetime" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
				update WO_TestOrderEquipItemCheckList c, TBL_HistoryEvent e
	      set c.IsPass=1, c.PassNote=concat('告警名称:',e.EventName,';描述:',e.Meanings), c.SamplerTime=e.StartTime			
	      where 
		    c.OrderId = :OrderId and c.CheckTypeId = 2 and c.IsPass=0 and c.HasConfig=1
		    and e.EquipmentId=c.EquipmentId and e.StartTime >= :EventStartTime1
		    and TRUNC(e.BaseTypeId / 1000)=TRUNC(c.BaseTypeId / 1000); 
			 ]]>
      </body>
    </procedure>
  </procedures>
</root>
