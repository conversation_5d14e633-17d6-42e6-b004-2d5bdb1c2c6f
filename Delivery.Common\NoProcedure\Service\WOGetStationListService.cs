﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;

namespace Delivery.Common.NoProcedure.Service
{
    public class WOGetStationListService
    {
        private static WOGetStationListService _instance = null;

        public static WOGetStationListService Instance
        {
            get
            {
                if (_instance == null) _instance = new WOGetStationListService();
                return _instance;
            }
        }
        // 定义一个获取站点列表的方法，可选地根据站点名称或站点代码过滤
        public DataTable GetStationList(string stationName = "", string stationCode = "")
        {
            // 初始化一个DataTable来存储结果
            DataTable res = new DataTable();

            // 声明一些局部变量用于存储中间结果
            string standardType;
            string sCenterId;
            string CenterName;

            // 确保站点名称和站点代码不为null，如果为null则替换为空字符串
            stationName = stationName ?? string.Empty;
            stationCode = stationCode ?? string.Empty;

            // 调用WOTestOrderEquipItemAddService的FunctionWOGetType方法获取标准类型
            standardType = WOTestOrderEquipItemAddService.FunctionWOGetType();

            // 创建数据库帮助器对象
            DbHelper dbHelper = new DbHelper();

            // 创建执行数据库操作的帮助器对象，不启用事务
            ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);

            // 执行一个数据库查询，获取一个临时的DataTable作为中间结果
            DataTable temp = execHelper.ExecDataTable("WO_GetStationList_1", null);

            // 从临时结果中提取中心ID和中心名称
            sCenterId = temp.Rows[0]["StructureId"].ToString();
            CenterName = temp.Rows[0]["StructureName"].ToString();

            // 如果标准类型为"1"，则执行特定的数据库查询来获取结果
            if (standardType == "1")
            {
                Dictionary<string, object> realParams = new Dictionary<string, object>
        {
            { "CenterName", CenterName },
            { "SCenterId", sCenterId },
            { "StationName", stationName },
            { "StationCode", stationCode }
        };
                res = execHelper.ExecDataTable("WO_GetStationList_2", realParams);
            }

            // 如果标准类型为"3"，则执行另一个特定的数据库查询来获取结果
            if (standardType == "3")
            {
                Dictionary<string, object> realParams = new Dictionary<string, object>
        {
            { "CenterName", CenterName },
            { "SCenterId", sCenterId },
            { "StationName", stationName },
            { "StationCode", stationCode }
        };
                res = execHelper.ExecDataTable("WO_GetStationList_3", realParams);
            }

            // 返回获取到的站点列表
            return res;
        }


    }
}
