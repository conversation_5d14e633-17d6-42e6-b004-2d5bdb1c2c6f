﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class GetDevConfAck : BMessage
    {
        public GetDevConfAck() : base()
        {
            MessageType = (int)BMessageType.GET_DEV_CONF_ACK;
        }

        #region SiteWeb配置
        [Newtonsoft.Json.JsonIgnore]
        public int MyStationId { get; set; }

        #endregion

        /// <summary>
        /// 返回结果
        /// </summary>
        public EnumResult Result { get; set; }

        /// <summary>
        /// 失败的原因
        /// </summary>
        public string FailureCause { get; set; }

        public List<TThreshold> Values { get; set; }
    }
}