﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;

namespace Delivery.Common.NoProcedure.Service
{
    public class Public_ExecuteSqlService
    {
        private static Public_ExecuteSqlService _instance = null;

        public static Public_ExecuteSqlService Instance
        {
            get
            {
                if (_instance == null) _instance = new Public_ExecuteSqlService();
                return _instance;
            }
        }

        public DataTable NoStore_WRDataItem_GetAll()
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    res = execHelper.ExecDataTable("Public_NoStore_WRDataItem_GetAll_sql", null);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_WRDataItem_GetAll Error:{0}", ex));
                    return new DataTable();
                }
                return res;
            }
        }

        public string NoStore_TBLControlMeanings_InsertSql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TBLControlMeanings_InsertSql_sql", null);
        }

        public string NoStore_TBLEquipment_GetInsertSql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TBLEquipment_GetInsertSql_sql", null);
        }

        public string NoStore_TBLSignalProperty_InsertSql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TBLSignalProperty_InsertSql_sql", null);
        }

        public DataTable NoStore_GetTablesFromTwoTables()
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    DataTable res1 = execHelper.ExecDataTable("Public_NoStore_GetTablesFromTwoTables_sql1", null);
                    DataTable res2 = execHelper.ExecDataTable("Public_NoStore_GetTablesFromTwoTables_sql2", null);
                    res.Merge(res1);
                    res.Merge(res2);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_GetTablesFromTwoTables Error:{0}", ex));
                    return null;
                }
                return res;
            }
        }

        public string NoStore_TBLSignal_UpdateSql_UpdateUnitOfSignal_mysql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TBLSignal_UpdateSql_UpdateUnitOfSignal_mysql_sql", null);
        }

        public string NoStore_TBLSignal_UpdateSql_UpdateUnitOfSignal()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TBLSignal_UpdateSql_UpdateUnitOfSignal_sql", null);
        }

        public string NoStore_TBLSignalBaseMap_UpdateSql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TBLSignalBaseMap_UpdateSql_sql", null);
        }

        public string NoStore_TBLSignal_UpdateSql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TBLSignal_UpdateSql_sql", null);
        }

        public string NoStore_TBLEquipmentTemplate_GetInsertSql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TBLEquipmentTemplate_GetInsertSql_sql", null);
        }

        public void NoStore_TBLSignalMeanings_InsertSql(int equipmentTemplateId, int signalId, int stateValue, string meanings)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("equipmentTemplateId", CommonUtils.GetNullableValue(equipmentTemplateId));
                    realParams.Add("signalId", CommonUtils.GetNullableValue(signalId));
                    realParams.Add("stateValue", CommonUtils.GetNullableValue(stateValue));
                    realParams.Add("meanings", CommonUtils.GetNullableValue(meanings));
                    execHelper.ExecuteScalar("Public_NoStore_TBLSignalMeanings_InsertSql_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLSignalMeanings_InsertSql Error:{0}", ex));
                }
            }
        }

        public void NoStore_TBLSignal_InsertSql(int equipmentTemplateId, int signalId, string enable, string visible, string description,
            string signalName, int signalCategory, int signalType, int channelNo, int channelType,
            string expression, string dataType, string showPrecision, string unit, int displayIndex, int moduleNo)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("equipmentTemplateId", CommonUtils.GetNullableValue(equipmentTemplateId));
                    realParams.Add("signalId", CommonUtils.GetNullableValue(signalId));
                    realParams.Add("enable", CommonUtils.GetNullableValue(enable));
                    realParams.Add("visible", CommonUtils.GetNullableValue(visible));
                    realParams.Add("description", CommonUtils.GetNullableValue(description));
                    realParams.Add("signalName", CommonUtils.GetNullableValue(signalName));
                    realParams.Add("signalCategory", CommonUtils.GetNullableValue(signalCategory));
                    realParams.Add("signalType", CommonUtils.GetNullableValue(signalType));
                    realParams.Add("channelNo", CommonUtils.GetNullableValue(channelNo));
                    realParams.Add("channelType", CommonUtils.GetNullableValue(channelType));
                    realParams.Add("expression", CommonUtils.GetNullableValue(expression));
                    realParams.Add("dataType", CommonUtils.GetNullableValue(dataType));
                    realParams.Add("showPrecision", CommonUtils.GetNullableValue(showPrecision));
                    realParams.Add("unit", CommonUtils.GetNullableValue(unit));
                    realParams.Add("displayIndex", CommonUtils.GetNullableValue(displayIndex));
                    realParams.Add("moduleNo", CommonUtils.GetNullableValue(moduleNo));
                    execHelper.ExecuteScalar("Public_NoStore_TBLSignal_InsertSql_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLSignal_InsertSql Error:{0}", ex));
                }
            }
        }

        public string NoStore_TBLCommandBaseMap_UpdateSql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TBLCommandBaseMap_UpdateSql_sql", null);
        }

        public string NoStore_TBLControl_UpdateSql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TBLControl_UpdateSql_sql", null);
        }

        public string NoStore_TSLMonitorUnitCMCC_GetUpdateSql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TSLMonitorUnitCMCC_GetUpdateSql_sql", null);
        }

        public string NoStore_TBLControlMeanings_GetSql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TBLControlMeanings_GetSql_sql", null);
        }

        public string NoStore_TBLControl_GetSql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TBLControl_GetSql_sql", null);
        }

        public string NoStore_TBLEventCondition_UpdateSql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TBLEventCondition_UpdateSql_sql2", null);
        }

        public string NoStore_TSLSampler_GetSql2()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TSLSampler_GetSql1_sql2", null);
        }

        public string NoStore_TSLSampler_GetSql1()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TSLSampler_GetSql1_sql1", null);
        }

        public string NoStore_TBLEquipmentCMCC_GetByFSUIDAndDeviceID(string fSUID, string deviceID)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                object res;
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("fSUID", CommonUtils.GetNullableValue(fSUID));
                    realParams.Add("deviceID", CommonUtils.GetNullableValue(deviceID));
                    res = execHelper.ExecuteScalar("Public_NoStore_TBLEquipmentCMCC_GetByFSUIDAndDeviceID_sql", realParams);
                    if (res != DBNull.Value && res != null)
                        return res.ToString();
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLEquipmentCMCC_GetByFSUIDAndDeviceID Error:{0}", ex));
                    return null;
                }
                return null;
            }
        }

        public string NoStore_TBLEventBaseMap_UpdateSql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TBLEventBaseMap_UpdateSql_sql", null);
        }

        public DataTable NoStore_TSLMonitorUnitCMCC_GetInfo()
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    res = execHelper.ExecDataTable("Public_NoStore_TSLMonitorUnitCMCC_GetInfo_sql", null);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TSLMonitorUnitCMCC_GetInfo Error:{0}", ex));
                    return new DataTable();
                }
                return res;
            }
        }

        public DataTable NoStore_TSLMonitorUnitCMCC_GetFsuIdByStationId(int stationId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("stationId", CommonUtils.GetNullableValue(stationId));
                    res = execHelper.ExecDataTable("Public_NoStore_TSLMonitorUnitCMCC_GetFsuIdByStationId_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TSLMonitorUnitCMCC_GetFsuIdByStationId Error:{0}", ex));
                    return new DataTable();
                }
                return res;
            }
        }

        public string NoStore_TBLSignalProperty_GetSql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TBLSignalProperty_GetSql_sql", null);
        }

        public string NoStore_TBLSignal_GetSql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TBLSignal_GetSql_sql", null);
        }

        public DataTable NoStore_TBLEquipmentCMCC_GetTableByFsuId(string fsuId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("fsuId", CommonUtils.GetNullableValue(fsuId));
                    res = execHelper.ExecDataTable("Public_NoStore_TBLEquipmentCMCC_GetTableByFsuId_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLEquipmentCMCC_GetTableByFsuId Error:{0}", ex));
                    return new DataTable();
                }
                return res;
            }
        }

        public string NoStore_TBLSignalMeanings_GetSql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TBLSignalMeanings_GetSql_sql", null);
        }

        public string NoStore_TBLEventCondition_GetSql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TBLEventCondition_GetSql_sql", null);
        }

        public string NoStore_TBLEvent_GetSql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TBLEvent_GetSql_sql", null);
        }

        public DataTable NoStore_TSLMonitorUnitCMCC_GetInfosByFsuId(string fsuId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("fsuId", CommonUtils.GetNullableValue(fsuId));
                    res = execHelper.ExecDataTable("Public_NoStore_TSLMonitorUnitCMCC_GetInfosByFsuId_sql", null);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TSLMonitorUnitCMCC_GetInfosByFsuId Error:{0}", ex));
                    return new DataTable();
                }
                return res;
            }
        }

        public DataTable NoStore_TBLEquipmentCMCC_GetTableByMonitorUnitId(int monitorUnitId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("monitorUnitId", CommonUtils.GetNullableValue(monitorUnitId));
                    res = execHelper.ExecDataTable("Public_NoStore_TBLEquipmentCMCC_GetTableByMonitorUnitId_sql", null);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLEquipmentCMCC_GetTableByMonitorUnitId Error:{0}", ex));
                    return new DataTable();
                }
                return res;
            }
        }

        public string NoStore_TBLEquipment_GetByStationIdAndEquipmentId(int stationId, int equipmentId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                object res;
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("stationId", CommonUtils.GetNullableValue(stationId));
                    realParams.Add("equipmentId", CommonUtils.GetNullableValue(equipmentId));
                    res = execHelper.ExecuteScalar("Public_NoStore_TBLEquipment_GetByStationIdAndEquipmentId_sql", realParams);
                    if (res != DBNull.Value && res != null)
                        return res.ToString();
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLEquipment_GetByStationIdAndEquipmentId Error:{0}", ex));
                    return null;
                }
                return null;
            }
        }

        public void NoStore_TSLPort_DeleteByPortId(int? portId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("portId", CommonUtils.GetNullableValue(portId));
                    execHelper.ExecuteNonQuery("Public_NoStore_TSLPort_DeleteByPortId_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TSLMonitorUnit_GetByMonitorUnitId Error:{0}", ex));

                }
            }
        }

        public string NoStore_TSLMonitorUnit_GetByMonitorUnitId(int monitorUnitId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                object res;
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("monitorUnitId", CommonUtils.GetNullableValue(monitorUnitId));
                    res = execHelper.ExecuteScalar("Public_NoStore_TSLMonitorUnit_GetByMonitorUnitId_sql", realParams);
                    if (res != DBNull.Value && res != null)
                        return res.ToString();
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TSLMonitorUnit_GetByMonitorUnitId Error:{0}", ex));
                    return null;
                }
                return null;
            }
        }

        public void NoStore_UpdateTBLSignal(string absoluteVal, string relativeVal, string storageInterval, int equipmentTemplateId, int signalId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("absoluteVal", CommonUtils.GetNullableValue(absoluteVal));
                    realParams.Add("relativeVal", CommonUtils.GetNullableValue(relativeVal));
                    realParams.Add("storageInterval", CommonUtils.GetNullableValue(storageInterval));
                    realParams.Add("equipmentTemplateId", CommonUtils.GetNullableValue(equipmentTemplateId));
                    realParams.Add("signalId", CommonUtils.GetNullableValue(signalId));
                    execHelper.ExecuteNonQuery("Public_NoStore_UpdateTBLSignal_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_UpdateTBLSignal Error:{0}", ex));
                }
            }
        }

        public string NoStore_TBLHouse_GetByStationId(int stationID)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                object res;
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("stationID", CommonUtils.GetNullableValue(stationID));
                    res = execHelper.ExecuteScalar("Public_NoStore_TBLHouse_GetByStationId_sql", realParams);
                    if (res != DBNull.Value && res != null)
                        return res.ToString();
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLHouse_GetByStationId Error:{0}", ex));
                    return null;
                }
                return null;
            }
        }

        public string NoStore_TSLMonitorUnitCMCC_GetMonitorUnitIdByFSUID(string fSUID)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                object res;
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("FSUID", CommonUtils.GetNullableValue(fSUID));
                    res = execHelper.ExecuteScalar("Public_NoStore_TSLMonitorUnitCMCC_GetMonitorUnitIdByFSUID_sql", realParams);
                    if (res != DBNull.Value && res != null)
                        return res.ToString();
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TSLMonitorUnitCMCC_GetMonitorUnitIdByFSUID Error:{0}", ex));
                    return null;
                }
                return null;
            }
        }

        public string NoStore_TSLMonitorUnitCMCC_GetByFSUId(string fSUID)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                object res;
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("fSUID", CommonUtils.GetNullableValue(fSUID));
                    res = execHelper.ExecuteScalar("Public_NoStore_TSLMonitorUnitCMCC_GetByFSUId_sql", realParams);
                    if (res != DBNull.Value && res != null)
                        return res.ToString();
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TSLMonitorUnitCMCC_GetByFSUId Error:{0}", ex));
                    return null;
                }
                return null;
            }
        }

        public string NoStore_TBLHouse_GetHouseIdByStationIdAndHouseName(int stationId, string roomName)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                object res;
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("stationId", CommonUtils.GetNullableValue(stationId));
                    realParams.Add("roomName", CommonUtils.GetNullableValue(roomName));
                    res = execHelper.ExecuteScalar("Public_NoStore_TBLHouse_GetHouseIdByStationIdAndHouseName_sql", realParams);
                    if (res != DBNull.Value && res != null)
                        return res.ToString();
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLHouse_GetHouseIdByStationIdAndHouseName Error:{0}", ex));
                    return null;
                }
                return null;
            }
        }

        public DataTable NoStore_TBLHouse_GetHouseNameByStationId(int stationId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("stationId", CommonUtils.GetNullableValue(stationId));
                    res = execHelper.ExecDataTable("Public_NoStore_TBLHouse_GetHouseNameByStationId_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLHouse_GetHouseNameByStationId Error:{0}", ex));
                    return null;
                }
                return res;
            }
        }

        public void NoStore_TBLFsuTSignalCMCC_DeleteByFSUID(string FsuId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("FsuId", CommonUtils.GetNullableValue(FsuId));
                    execHelper.ExecDataTable("Public_NoStore_TBLFsuTSignalCMCC_DeleteByFSUID_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLFsuTSignalCMCC_DeleteByFSUID Error:{0}", ex));
                }
            }
        }

        public string NoStore_TSLMonitorUnitCMCC_GetFsuIdByMonitorUnitId(int monitorUnitId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                object res;
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("monitorUnitId", CommonUtils.GetNullableValue(monitorUnitId));
                    res = execHelper.ExecuteScalar("Public_NoStore_TSLMonitorUnitCMCC_GetFsuIdByMonitorUnitId_sql", realParams);
                    if (res != DBNull.Value && res != null)
                        return res.ToString();
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TSLMonitorUnitCMCC_GetFsuIdByMonitorUnitId Error:{0}", ex));
                    return null;
                }
                return null;
            }
        }

        public string NoStore_TABLES_GetSql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_TABLES_GetSql_sql", null);
        }

        public string NoStore_sysobjects_GetSql()
        {
            return XmlScriptProvider.Current.GetCommandSql("Public_NoStore_sysobjects_GetSql_sql", null);
        }

        public string NoStore_TBLSignal_GetMaxIdByEquipmentTemplateId(int equipmentTemplateId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                object res;
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("equipmentTemplateId", CommonUtils.GetNullableValue(equipmentTemplateId));
                    res = execHelper.ExecuteScalar("Public_NoStore_TBLSignal_GetMaxIdByEquipmentTemplateId_sql", realParams);
                    if (res != DBNull.Value && res != null)
                        return res.ToString();
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLSignal_GetMaxIdByEquipmentTemplateId Error:{0}", ex));
                    return null;
                }
                return null;
            }
        }

        public DataTable NoStore_TBLEquipmentCMCC_GetTableByFsuIdAndDeviceID(string fSUID, string deviceID)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("fSUID", CommonUtils.GetNullableValue(fSUID));
                    realParams.Add("deviceID", CommonUtils.GetNullableValue(deviceID));
                    res = execHelper.ExecDataTable("Public_NoStore_TBLEquipmentCMCC_GetTableByFsuIdAndDeviceID_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLEquipmentCMCC_GetTableByFsuIdAndDeviceID Error:{0}", ex));
                    return null;
                }
                return res;
            }
        }

        public DataTable NoStore_WRDataItem_GetDataDic(string entryId, string parentEntryId, string parentItemId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    if (!parentEntryId.Equals("-1")) {
                        realParams.Add("parentEntryIdStr", true);
                    }
                    if (!parentItemId.Equals("-1"))
                    {
                        realParams.Add("parentItemIdStr", true);
                    }
                    realParams.Add("EntryId", CommonUtils.GetNullableValue(entryId));
                    realParams.Add("ParentEntryId", CommonUtils.GetNullableValue(parentEntryId));
                    realParams.Add("ParentItemId", CommonUtils.GetNullableValue(parentItemId));
                    DataTable res1 = execHelper.ExecDataTable("Public_NoStore_WR_DataItem_GetDataDic_sql1", null);
                    DataTable res2 = execHelper.ExecDataTable("Public_NoStore_WR_DataItem_GetDataDic_sql2", realParams);
                    res.Merge(res1);
                    res.Merge(res2);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_WR_DataItem_GetDataDic Error:{0}", ex));
                    return null;
                }
                return res;
            }
        }

        public DataTable NoStore_TBLEquipmentCMCC_GetTableByStationIdAndEequipmentId(int stationId, int equipmentId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("equipmentId", CommonUtils.GetNullableValue(equipmentId));
                    realParams.Add("stationId", CommonUtils.GetNullableValue(stationId));
                    res = execHelper.ExecDataTable("Public_NoStore_TBLEquipmentCMCC_GetTableByStationIdAndEequipmentId_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLEquipmentCMCC_GetTableByStationIdAndEequipmentId Error:{0}", ex));
                    return null;
                }
                return res;
            }
        }

        public string NoStore_TBLEvent_GetMaxIdByEquipmentTemplateId(int equipmentTemplateId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                object res;
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("equipmentTemplateId", CommonUtils.GetNullableValue(equipmentTemplateId));
                    res = execHelper.ExecuteScalar("Public_NoStore_TBLEvent_GetMaxIdByEquipmentTemplateId_sql", realParams);
                    if (res != DBNull.Value && res != null)
                        return res.ToString();
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLEvent_GetMaxIdByEquipmentTemplateId Error:{0}", ex));
                    return null;
                }
                return null;
            }
        }

        public string NoStore_TBLControl_GetMaxIdByEquipmentTemplateId(int equipmentTemplateId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                object res;
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("equipmentTemplateId", CommonUtils.GetNullableValue(equipmentTemplateId));
                    res = execHelper.ExecuteScalar("Public_NoStore_TBLControl_GetMaxIdByEquipmentTemplateId_sql", realParams);
                    if (res != DBNull.Value && res != null)
                        return res.ToString();
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLControl_GetMaxIdByEquipmentTemplateId Error:{0}", ex));
                    return null;
                }
                return null;
            }
        }

        public DataTable NoStore_WODicEventCheckList_GetAll()
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    res = execHelper.ExecDataTable("Public_NoStore_WODicEventCheckList_GetAll_sql", null);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_WODicEventCheckList_GetAll Error:{0}", ex));
                    return new DataTable();
                }
                return res;
            }
        }

        public DataTable NoStore_GetDataUiDic(string type, string itemId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                DataTable res1 = new DataTable(), res2 = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("EntryId", CommonUtils.GetNullableValue(type));
                    realParams.Add("ItemId", CommonUtils.GetNullableValue(itemId));
                    if (type.Equals("7") && itemId.Equals("-1"))
                    {
                        res1 = execHelper.ExecDataTable("Public_NoStore_GetDataUiDic_sql1", null);
                        res2 = execHelper.ExecDataTable("Public_NoStore_GetDataUiDic_sql2", realParams);
                    }
                    else if (type.Equals("4") || type.Equals("5") || type.Equals("6") || type.Equals("7"))
                    {
                        res1 = execHelper.ExecDataTable("Public_NoStore_GetDataUiDic_sql1", null);
                        res2 = execHelper.ExecDataTable("Public_NoStore_GetDataUiDic_sql3", realParams);
                    }
                    else if (type.Equals("20") && itemId.Equals("20")) {
                        res1 = execHelper.ExecDataTable("Public_NoStore_GetDataUiDic_sql1", null);
                        res2 = execHelper.ExecDataTable("Public_NoStore_GetDataUiDic_sql4", realParams);
                    }
                    else 
                    {
                        res1 = execHelper.ExecDataTable("Public_NoStore_GetDataUiDic_sql5", null);
                    }

                    res.Merge(res1);
                    res.Merge(res2);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_GetDataUiDic Error:{0}", ex));
                    return null;
                }
                return res;
            }
        }

        public DataTable NoStore_WODicSigCheckList_GetAll()
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    res = execHelper.ExecDataTable("Public_NoStore_WODicSigCheckList_GetAll_sql", null);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_WODicSigCheckList_GetAll Error:{0}", ex));
                    return new DataTable();
                }
                return res;
            }
        }

        public void NoStore_DeleteTwoTablesByItemId(string ItemId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("ItemId", CommonUtils.GetNullableValue(ItemId));
                    execHelper.ExecuteNonQuery("Public_NoStore_DeleteTwoTablesByItemId_sql1", realParams);
                    execHelper.ExecuteNonQuery("Public_NoStore_DeleteTwoTablesByItemId_sql2", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_DeleteTwoTablesByItemId Error:{0}", ex));

                }

            }
        }

        public DataTable NoStore_WODicCmdCheckList_GetAll()
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    res = execHelper.ExecDataTable("Public_NoStore_WODicCmdCheckList_GetAll_sql", null);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_WODicCmdCheckList_GetAll Error:{0}", ex));
                    return new DataTable();
                }
                return res;
            }
        }

        public DataTable NoStore_TBLDataItem_GetAll()
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    res = execHelper.ExecDataTable("Public_NoStore_TBLDataItem_GetAll_sql", null);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLDataItem_GetAll Error:{0}", ex));
                    return new DataTable();
                }
                return res;
            }
        }

        public DataTable NoStore_WOInstallCompany_GetAll()
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    res = execHelper.ExecDataTable("Public_NoStore_WOInstallCompany_GetAll_sql", null);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_WOInstallCompany_GetAll Error:{0}", ex));
                    return new DataTable();
                }
                return res;
            }
        }

        public object NoStore_WOTestOrderEquipItem_JudgeExist(int orderId, int equipmentId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                object res = null;
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("orderId", CommonUtils.GetNullableValue(orderId));
                    realParams.Add("equipmentId", CommonUtils.GetNullableValue(equipmentId));
                    res = execHelper.ExecuteScalar("Public_NoStore_WOTestOrderEquipItem_JudgeExist_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_WOTestOrderEquipItem_JudgeExist Error:{0}", ex));
                    return null;
                }
                return res;
            }
        }

        public void NoStore_WOTestOrderEquipItemCheckList_UpdateByOrderCheckId(int orderCheckId, string passFailReason)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("orderCheckId", CommonUtils.GetNullableValue(orderCheckId));
                    realParams.Add("passFailReason", CommonUtils.GetNullableValue(passFailReason));
                    execHelper.ExecuteNonQuery("Public_NoStore_WOTestOrderEquipItemCheckList_UpdateByOrderCheckId_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_WOTestOrderEquipItemCheckList_UpdateByOrderCheckId Error:{0}", ex));
                }
            }
        }

        public DataTable NoStore_WOTestOrderEquipItem_GetByOrderIdAndEquipmentId(int orderId, int equipmentId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("orderId", CommonUtils.GetNullableValue(orderId));
                    realParams.Add("equipmentId", CommonUtils.GetNullableValue(equipmentId));
                    res = execHelper.ExecDataTable("Public_NoStore_WOTestOrderEquipItem_GetByOrderIdAndEquipmentId_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_WOTestOrderEquipItem_GetByOrderIdAndEquipmentId Error:{0}", ex));
                    return new DataTable();
                }
                return res;
            }
        }

        public DataTable NoStore_WOTestOrderEquipItem_GetAllByOrderId(int orderId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("orderId", CommonUtils.GetNullableValue(orderId));
                    res = execHelper.ExecDataTable("Public_NoStore_WOTestOrderEquipItem_GetAllByOrderId_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_WOTestOrderEquipItem_GetAllByOrderId Error:{0}", ex));
                    return new DataTable();
                }
                return res;
            }
        }

        public string NoStore_TBLSignal_GetMaxIdByEquipmentTemplateIdAndSId(int equipmentTemplateId, int sId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                object res;
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("equipmentTemplateId", CommonUtils.GetNullableValue(equipmentTemplateId));
                    realParams.Add("sId", CommonUtils.GetNullableValue(sId));
                    res = execHelper.ExecuteScalar("Public_NoStore_TBLSignal_GetMaxIdByEquipmentTemplateIdAndSId_sql", realParams);
                    if (res != DBNull.Value && res != null)
                        return res.ToString();
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLSignal_GetMaxIdByEquipmentTemplateIdAndSId Error:{0}", ex));
                    return null;
                }
                return null;
            }
        }

        public DataTable NoStore_WOTestOrderArtSelfCheckList_GetByOrderId(int orderId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("orderId", CommonUtils.GetNullableValue(orderId));
                    res = execHelper.ExecDataTable("Public_NoStore_WOTestOrderArtSelfCheckList_GetByOrderId_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_WOTestOrderArtSelfCheckList_GetByOrderId Error:{0}", ex));
                    return new DataTable();
                }
                return res;
            }
        }

        public DataTable NoStore_TSLMonitorUnitCMCC_GetByFsuCode(string fsuCode)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("fsuCode", CommonUtils.GetNullableValue(fsuCode));
                    res = execHelper.ExecDataTable("Public_NoStore_TSLMonitorUnitCMCC_GetByFsuCode_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TSLMonitorUnitCMCC_GetByFsuCode Error:{0}", ex));
                    return new DataTable();
                }
                return res;
            }
        }

        public void NoStore_WOTestOrder_UpdateByOrderId(string latitude, string longitude, string equipItems, string installCompany,
            string installClerk, string installCompanyId, string installClerkId, string orderId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("latitude", CommonUtils.GetNullableValue(latitude));
                    realParams.Add("longitude", CommonUtils.GetNullableValue(longitude));
                    realParams.Add("equipItems", CommonUtils.GetNullableValue(equipItems));
                    realParams.Add("installCompany", CommonUtils.GetNullableValue(installCompany));
                    realParams.Add("installClerk", CommonUtils.GetNullableValue(installClerk));
                    realParams.Add("installCompanyId", CommonUtils.GetNullableValue(installCompanyId));
                    realParams.Add("installClerkId", CommonUtils.GetNullableValue(installClerkId));
                    realParams.Add("orderId", CommonUtils.GetNullableValue(orderId));
                    execHelper.ExecuteNonQuery("Public_NoStore_WOTestOrder_UpdateByOrderId_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_WOTestOrder_UpdateByOrderId Error:{0}", ex));
                }
            }
        }

        public DataTable NoStore_WOInstallCompany_GetByKeyword(string keyword)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("companyName", CommonUtils.GetNullableValue(keyword));
                    res = execHelper.ExecDataTable("Public_NoStore_WOInstallCompany_GetByKeyword_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_WOInstallCompany_GetByKeyword Error:{0}", ex));
                    return new DataTable();
                }
                return res;
            }
        }

        public object NoStore_GetUserNameByUserId(int userId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                object rtn = null;
                try
                {
                    realParams.Add("userId", CommonUtils.GetNullableValue(userId));
                    rtn = execHelper.ExecuteScalar("Public_NoStore_GetUserNameByUserId_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_GetUserNameByUserId Error:{0}", ex));
                    return null;
                }
                return rtn;
            }
        }

        public DataTable NoStore_TBLStation_GetStationById(int stationId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("stationId", CommonUtils.GetNullableValue(stationId));
                    res = execHelper.ExecDataTable("Public_NoStore_TBLStation_GetStationById_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLStation_GetStationById Error:{0}", ex));
                    return new DataTable();
                }
                return res;
            }
        }

        public DataTable NoStore_WOInstallClerk_GetAll()
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    res = execHelper.ExecDataTable("Public_NoStore_WOInstallClerk_GetAll_sql", null);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_WOInstallClerk_GetAll Error:{0}", ex));
                    return new DataTable();
                }
                return res;
            }
        }

        public object NoStore_GetEquipmentCategoryName(int catId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                object rtn = null;
                try
                {
                    realParams.Add("CatId", CommonUtils.GetNullableValue(catId));
                    rtn = execHelper.ExecuteScalar("Public_NoStore_GetEquipmentCategoryName_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_GetEquipmentCategoryName Error:{0}", ex));
                    return null;
                }
                return rtn;
            }
        }

        public object NoStore_GetStationCategoryName(int catId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                object rtn = null;
                try
                {
                    realParams.Add("CatId", CommonUtils.GetNullableValue(catId));
                    rtn = execHelper.ExecuteScalar("Public_NoStore_GetStationCategoryName_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_GetStationCategoryName Error:{0}", ex));
                    return null;
                }
                return rtn;
            }
        }

        public DataTable NoStore_WOTestOrder_GetOneByOrderId(int orderId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = new DataTable();
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("orderId", CommonUtils.GetNullableValue(orderId));
                    res = execHelper.ExecDataTable("Public_NoStore_WOTestOrder_GetOneByOrderId_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_WOTestOrder_GetOneByOrderId Error:{0}", ex));
                    return new DataTable();
                }
                return res;
            }
        }


        public object NoStore_WOTestOrder_IsExistByOrderId(int orderId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                object rtn = null;
                try
                {
                    realParams.Add("orderId", CommonUtils.GetNullableValue(orderId));
                    rtn = execHelper.ExecuteScalar("Public_NoStore_WOTestOrder_IsExistByOrderId_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_WOTestOrder_IsExistByOrderId Error:{0}", ex));
                    return null;
                }
                return rtn;
            }
        }

        public DataTable NoStore_GetTestOrderArtSelfCheckList(int orderId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                DataTable rtn = new DataTable();
                try
                {
                    realParams.Add("orderId", CommonUtils.GetNullableValue(orderId));
                    rtn = execHelper.ExecDataTable("public_NoStore_GetTestOrderArtSelfCheckList_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_GetTestOrderArtSelfCheckList Error:{0}", ex));
                    return null;
                }
                return rtn;
            }
        }

        public DataTable NoStore_GetTestOrderExpertCheckList(int orderId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                DataTable rtn = new DataTable();
                try
                {
                    realParams.Add("orderId", CommonUtils.GetNullableValue(orderId));
                    rtn = execHelper.ExecDataTable("public_NoStore_GetTestOrderExpertCheckList_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_GetTestOrderExpertCheckList Error:{0}", ex));
                    return null;
                }
                return rtn;
            }
        }

        public DataTable NoStore_WOTestOrder_GetAll()
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                DataTable rtn = new DataTable();
                try
                {
                    rtn = execHelper.ExecDataTable("public_NoStore_WOTestOrder_GetAll_sql", null);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_WOTestOrder_GetAll Error:{0}", ex));
                    return null;
                }
                return rtn;
            }
        }
        public DataTable NoStore_TBLEquipment_GetOneByEquipmentId(int equipmentId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                DataTable rtn = new DataTable();
                try
                {
                    realParams.Add("equipmentId", CommonUtils.GetNullableValue(equipmentId));
                    rtn = execHelper.ExecDataTable("public_NoStore_TBLEquipment_GetOneByEquipmentId_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLEquipment_GetOneByEquipmentId Error:{0}", ex));
                    return null;
                }
                return rtn;
            }
        }
        public DataTable NoStore_TBLEquipment_GetEquipmentsByStationId(int StationId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                DataTable rtn = new DataTable();
                try
                {
                    realParams.Add("stationId", CommonUtils.GetNullableValue(StationId));
                    rtn = execHelper.ExecDataTable("public_NoStore_TBLEquipment_GetEquipmentsByStationId_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLEquipment_GetEquipmentsByStationId Error:{0}", ex));
                    return null;
                }
                return rtn;
            }
        }

        public void NoStore_TBLEquipmentCMCC_DeleteByFSUIDAndDeviceID(string FSUID, string DeviceID)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("FSUID", CommonUtils.GetNullableValue(FSUID));
                    realParams.Add("DeviceID", CommonUtils.GetNullableValue(DeviceID));
                    execHelper.ExecuteNonQuery("Public_NoStore_TBLEquipmentCMCC_DeleteByFSUIDAndDeviceID_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLEquipmentCMCC_DeleteByFSUIDAndDeviceID Error:{0}", ex));
                }
            }
        }

        public string GetInsertHeader_TBL_SignalProperty()
        {
            return XmlScriptProvider.Current.GetCommandSql("GetInsertHeader_TBL_SignalProperty_sql", null);
        }

        public string GetInsertHeader_TBL_SignalMeanings()
        {
            return XmlScriptProvider.Current.GetCommandSql("GetInsertHeader_TBL_SignalMeanings_sql", null);
        }

        public string GetInsertHeader_TBL_Signal()
        {
            return XmlScriptProvider.Current.GetCommandSql("GetInsertHeader_TBL_Signal_sql", null);
        }

        public string GetInsertHeader_TBL_Event()
        {
            return XmlScriptProvider.Current.GetCommandSql("GetInsertHeader_TBL_Event_sql", null);
        }

        public string GetInsertHeader_TBL_EventCondition()
        {
            return XmlScriptProvider.Current.GetCommandSql("GetInsertHeader_TBL_EventCondition_sql", null);
        }

        public string GetInsertHeader_TBL_ControlMeanings()
        {
            return XmlScriptProvider.Current.GetCommandSql("GetInsertHeader_TBL_ControlMeanings_sql", null);
        }

        public string GetInsertHeader_TBL_Control()
        {
            return XmlScriptProvider.Current.GetCommandSql("GetInsertHeader_TBL_Control_sql", null);
        }

        public string GetInsertHeader_TBL_FsuTSignalCMCC()
        {
            return XmlScriptProvider.Current.GetCommandSql("GetInsertHeader_TBL_FsuTSignalCMCC_sql", null);
        }

        public string GetInsertHeader_TBL_FSU_SendDevConfData_TSignal()
        {
            return XmlScriptProvider.Current.GetCommandSql("GetInsertHeader_TBL_FSU_SendDevConfData_TSignal_sql", null);
        }

        public void NoStore_InsertRowIntoTBLEventCondition(int eventConditionId, int equipmentTemplateId, int eventId, string startOperation, string startCompareValue, 
                                                   int startDelay, string endOperation, string endCompareValue, string endDelay, string frequency, 
                                                   string frequencyThreshold, string meanings, string equipmentState, string baseTypeId, int eventSeverity, string standardName)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("eventConditionId", CommonUtils.GetNullableValue(eventConditionId));
                    realParams.Add("equipmentTemplateId", CommonUtils.GetNullableValue(equipmentTemplateId));
                    realParams.Add("eventId", CommonUtils.GetNullableValue(eventId));
                    realParams.Add("startOperation", CommonUtils.GetNullableValue(startOperation));
                    realParams.Add("startCompareValue", CommonUtils.GetNullableValue(startCompareValue));
                    realParams.Add("startDelay", CommonUtils.GetNullableValue(startDelay));
                    realParams.Add("endOperation", CommonUtils.GetNullableValue(endOperation));
                    realParams.Add("endCompareValue", CommonUtils.GetNullableValue(endCompareValue));
                    realParams.Add("endDelay", CommonUtils.GetNullableValue(endDelay));
                    realParams.Add("frequency", CommonUtils.GetNullableValue(frequency));
                    realParams.Add("frequencyThreshold", CommonUtils.GetNullableValue(frequencyThreshold));
                    realParams.Add("meanings", CommonUtils.GetNullableValue(meanings));
                    realParams.Add("equipmentState", CommonUtils.GetNullableValue(equipmentState));
                    realParams.Add("baseTypeId", CommonUtils.GetNullableValue(baseTypeId));
                    realParams.Add("eventSeverity", CommonUtils.GetNullableValue(eventSeverity));
                    realParams.Add("standardName", CommonUtils.GetNullableValue(standardName));
                    execHelper.ExecuteNonQuery("Public_NoStore_InsertRowIntoTBLEventCondition_sql", realParams);
                    
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_InsertRowIntoTBLEventCondition Error:{0}", ex));
                }
            }
        }

        public void NoStore_InsertRowIntoTBLEvent(int equipmentTemplateId, int eventId, string eventName, int startType, int endType, 
                                          string startExpression, string suppressExpression, int eventCategory, string signalId, string enable, 
                                          string visible, string description, int? displayIndex, int moduleNo)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("equipmentTemplateId", CommonUtils.GetNullableValue(equipmentTemplateId));
                    realParams.Add("eventId", CommonUtils.GetNullableValue(eventId));
                    realParams.Add("eventName", CommonUtils.GetNullableValue(eventName));
                    realParams.Add("startType", CommonUtils.GetNullableValue(startType));
                    realParams.Add("endType", CommonUtils.GetNullableValue(endType));
                    realParams.Add("startExpression", CommonUtils.GetNullableValue(startExpression));
                    realParams.Add("suppressExpression", CommonUtils.GetNullableValue(suppressExpression));
                    realParams.Add("eventCategory", CommonUtils.GetNullableValue(eventCategory));
                    realParams.Add("signalId", CommonUtils.GetNullableValue(signalId));
                    realParams.Add("enable", CommonUtils.GetNullableValue(enable));
                    realParams.Add("visible", CommonUtils.GetNullableValue(visible));
                    realParams.Add("description", CommonUtils.GetNullableValue(description));
                    realParams.Add("displayIndex", CommonUtils.GetNullableValue(displayIndex));
                    realParams.Add("moduleNo", CommonUtils.GetNullableValue(moduleNo));
                    execHelper.ExecuteNonQuery("Public_NoStore_InsertRowIntoTBLEvent_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_InsertRowIntoTBLEvent Error:{0}", ex));
                }
            }
        }

        public string NoStore_TBLDataItem_GetByDeviceType(int? deviceType)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                string res = null;
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("deviceType", CommonUtils.GetNullableValue(deviceType));
                    object resObj = execHelper.ExecuteScalar("Public_NoStore_TBLDataItem_GetByDeviceType_sql", realParams);
                    if (resObj != null && resObj != DBNull.Value)
                        res = resObj.ToString();
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLDataItem_GetByDeviceType Error:{0}", ex));
                    return null;
                }
                return res;
            }
        }

        public void NoStore_InsertRowIntoTBLControl(int equipmentTemplateId, int controlId, string controlName, int controlCategory, string cmdToken, 
                                            string baseTypeId, int controlSeverity, string signalId, string timeOut, string retry, 
                                            string description, string enable, string visible, int displayIndex, int commandType,
                                            string controlType, string dataType, double maxValue, double minValue, string defaultValue, int moduleNo)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("equipmentTemplateId", CommonUtils.GetNullableValue(equipmentTemplateId));
                    realParams.Add("controlId", CommonUtils.GetNullableValue(controlId));
                    realParams.Add("controlName", CommonUtils.GetNullableValue(controlName));
                    realParams.Add("controlCategory", CommonUtils.GetNullableValue(controlCategory));
                    realParams.Add("cmdToken", CommonUtils.GetNullableValue(cmdToken));
                    realParams.Add("baseTypeId", CommonUtils.GetNullableValue(baseTypeId));
                    realParams.Add("controlSeverity", CommonUtils.GetNullableValue(controlSeverity));
                    realParams.Add("signalId", CommonUtils.GetNullableValue(signalId));
                    realParams.Add("timeOut", CommonUtils.GetNullableValue(timeOut));
                    realParams.Add("retry", CommonUtils.GetNullableValue(retry));
                    realParams.Add("description", CommonUtils.GetNullableValue(description));
                    realParams.Add("enable", CommonUtils.GetNullableValue(enable));
                    realParams.Add("visible", CommonUtils.GetNullableValue(visible));
                    realParams.Add("displayIndex", CommonUtils.GetNullableValue(displayIndex));
                    realParams.Add("commandType", CommonUtils.GetNullableValue(commandType));
                    realParams.Add("controlType", CommonUtils.GetNullableValue(controlType));
                    realParams.Add("dataType", CommonUtils.GetNullableValue(dataType));
                    realParams.Add("maxValue", CommonUtils.GetNullableValue(maxValue));
                    realParams.Add("minValue", CommonUtils.GetNullableValue(minValue));
                    realParams.Add("defaultValue", CommonUtils.GetNullableValue(defaultValue));
                    realParams.Add("moduleNo", CommonUtils.GetNullableValue(moduleNo));
                    execHelper.ExecuteNonQuery("Public_NoStore_InsertRowIntoTBLControl_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_InsertRowIntoTBLControl Error:{0}", ex));
                }
            }
        }

        public DataTable NoStore_TBLEquipmentCMCC_GetAnotherInfoByFsuId(string fSUID)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = null;
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("FSUID", CommonUtils.GetNullableValue(fSUID));
                    res = execHelper.ExecDataTable("Public_NoStore_TBLEquipmentCMCC_GetAnotherInfoByFsuId_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLEquipmentCMCC_GetAnotherInfoByFsuId Error:{0}", ex));
                    return null;
                }
                return res;
            }
        }

        public DataTable NoStore_TBLConfigChangeDefine_GetByEntityName(string entityName)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = null;
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("entityName", CommonUtils.GetNullableValue(entityName));
                    res = execHelper.ExecDataTable("Public_NoStore_TBLConfigChangeDefine_GetByEntityName_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLConfigChangeDefine_GetByEntityName Error:{0}", ex));
                    return null;
                }
                return res;
            }
        }

        public DataTable NoStore_TBLEquipmentCMCC_GetByFsuId(string fSUID)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                DataTable res = null;
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("FSUID", CommonUtils.GetNullableValue(fSUID));
                    res = execHelper.ExecDataTable("Public_NoStore_TBLEquipmentCMCC_GetByFsuId_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TBLEquipmentCMCC_GetByFsuId Error:{0}", ex));
                    return null;
                }
                return res;
            }
        }

        public void NoStore_InsertRowIntoTBLEquipmentCMCC(string deviceID, string deviceName, string fSUID, string stationId, string monitorUnitId, string equipmentId, string roomName, string deviceType, string deviceSubType, string model, string brand, string ratedCapacity, string version, string beginRunTime, string devDescribe, string extendField1, string extendField2)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("deviceID", CommonUtils.GetNullableValue(deviceID));
                    realParams.Add("deviceName", CommonUtils.GetNullableValue(deviceName));
                    realParams.Add("fSUID", CommonUtils.GetNullableValue(fSUID));
                    realParams.Add("stationId", CommonUtils.GetNullableValue(stationId));
                    realParams.Add("monitorUnitId", CommonUtils.GetNullableValue(monitorUnitId));
                    realParams.Add("equipmentId", CommonUtils.GetNullableValue(equipmentId));
                    realParams.Add("roomName", CommonUtils.GetNullableValue(roomName));
                    realParams.Add("deviceSubType", CommonUtils.GetNullableValue(deviceSubType));
                    realParams.Add("deviceType", CommonUtils.GetNullableValue(deviceType));
                    realParams.Add("model", CommonUtils.GetNullableValue(model));
                    realParams.Add("brand", CommonUtils.GetNullableValue(brand));
                    realParams.Add("ratedCapacity", CommonUtils.GetNullableValue(ratedCapacity));
                    realParams.Add("version", CommonUtils.GetNullableValue(version));
                    realParams.Add("beginRunTime", CommonUtils.GetNullableValue(beginRunTime));
                    realParams.Add("devDescribe", CommonUtils.GetNullableValue(devDescribe));
                    realParams.Add("extendField1", CommonUtils.GetNullableValue(extendField1));
                    realParams.Add("extendField2", CommonUtils.GetNullableValue(extendField2));
                    execHelper.ExecuteNonQuery("Public_NoStore_InsertRowIntoTBLEquipmentCMCC_sql", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_InsertRowIntoTBLEquipmentCMCC Error:{0}", ex));
                }
            }
        }

        public string NoStore_TSLMonitorUnitCMCC_JudgeExistByFsuId(string fSUID)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                string res = null;
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("FSUID", CommonUtils.GetNullableValue(fSUID));
                    object resObj = execHelper.ExecuteScalar("Public_NoStore_TSLMonitorUnitCMCC_JudgeExistByFsuId_sql", realParams);
                    if (resObj != null && resObj != DBNull.Value)
                        res = resObj.ToString();
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("NoStore_TSLMonitorUnitCMCC_JudgeExistByFsuId Error:{0}", ex));
                    return null;
                }
                return res;
            }
        }
    }
}
