﻿using BLL;
using DM.TestOrder.DAL;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web.Http;




namespace DM.TestOrder.Controllers {
        
    public class SiteController : BaseApiController{

        //http://localhost/orderapi/Site
        public HttpResponseMessage Get() {

            var dt = Station.GetAllInfo();

            var sReturn = JsonConvert.SerializeObject(dt);
            HttpResponseMessage result = new HttpResponseMessage {
                Content = new StringContent(sReturn, Encoding.GetEncoding("UTF-8"), "application/json")
            };
            return result; 
        }
        public HttpResponseMessage Get(int id) {

            var dt = Station.GetOne(id);

            var sReturn = JsonConvert.SerializeObject(dt);
            HttpResponseMessage result = new HttpResponseMessage {
                Content = new StringContent(sReturn, Encoding.GetEncoding("UTF-8"), "application/json")
            };
            return result;
        }
    }
}
