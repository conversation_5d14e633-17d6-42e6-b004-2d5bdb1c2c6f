﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 写动环设备配置数据应答报文
    /// </summary>
    public class SetDevConfDataAck: BMessage
    {
        public EnumResult Result { get; private set; }

        public string FailureCause { get; private set; }
        /// <summary>
        /// 成功设备ID的列表
        /// </summary>
        public List<string> SuccessList { get; private set; }
        /// <summary>
        /// 失败设备ID的列表
        /// </summary>
        public List<string> FailList { get; private set; }

        public SetDevConfDataAck(string fsuId, EnumResult result, string failureCause, List<string> successList, List<string> failList)
            : base()
        {
            MessageType = (int)BMessageType.SET_DEV_CONF_DATA_ACK;

            FSUID = fsuId;
            Result = result;
            FailureCause = failureCause;
            SuccessList = successList;
            FailList = failList;
        }

        public SetDevConfDataAck() : base() 
        {
            MessageType = (int)BMessageType.SET_DEV_CONF_DATA_ACK;
        }

        public static SetDevConfDataAck Deserialize(XmlDocument xmldoc)
        {
            SetDevConfDataAck setDevConfDataAck = null;
            try
            {
                string fsuId = xmldoc.SelectSingleNode("/Response/Info/FSUID").InnerText;
                string result = xmldoc.SelectSingleNode("/Response/Info/Result").InnerText;
                EnumResult enumResult = EnumResult.SUCCESS;
                if (result != null && result != "")
                {
                    enumResult = (EnumResult)int.Parse(result);
                }
                string failureCause = xmldoc.SelectSingleNode("/Response/Info/FailureCause").InnerText;

                XmlNodeList successNodelist = xmldoc.SelectNodes("/Response/Info/SuccessList/Device");
                List<string> successList = new List<string>();
                foreach (XmlNode node in successNodelist)
                {
                    successList.Add(((XmlElement)node).GetAttribute("ID"));
                }

                XmlNodeList failNodeList = xmldoc.SelectNodes("/Response/Info/FailList/Device");
                List<string> failList = new List<string>();
                foreach (XmlNode node in failNodeList)
                {
                    failList.Add(((XmlElement)node).GetAttribute("ID"));
                }

                setDevConfDataAck = new SetDevConfDataAck(fsuId, enumResult, failureCause, successList, failList);
                entityLogger.DebugFormat("SetDevConfDataAck.Deserialize(),xml:\r\n{0}", FormatXml(xmldoc.InnerXml));
                setDevConfDataAck.StringXML = xmldoc.InnerXml;
                return setDevConfDataAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetDevConfDataAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetDevConfDataAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + xmldoc.InnerXml);
                setDevConfDataAck = new SetDevConfDataAck();
                setDevConfDataAck.ErrorMsg = ex.Message;
                setDevConfDataAck.StringXML = xmldoc.InnerXml;
                return setDevConfDataAck;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();

            sb.AppendFormat("{0}, {1}, {2}:{3},{4}",
                MessageId, (BMessageType)MessageType, FSUID, Result.ToString(), FailureCause);
            sb.AppendLine();
            sb.Append("SuccessList:[");
            //sb.Append(SuccessList.ToArray());
            if (SuccessList.Count > 0)
            {
                foreach (string deviceId in SuccessList)
                {
                    sb.Append(deviceId).Append(",");
                }
                sb.Remove(sb.Length - 1, 1);
            }
            sb.Append("]");
            sb.AppendLine();
            sb.Append("FailList:[");
            //sb.Append(FailList.ToArray());
            if(FailList.Count > 0)
            {
                foreach (string deviceId in FailList)
                {
                    sb.Append(deviceId).Append(",");
                }
                sb.Remove(sb.Length - 1, 1);
            }
            sb.Append("]");

            return sb.ToString();
        }

    }
}
