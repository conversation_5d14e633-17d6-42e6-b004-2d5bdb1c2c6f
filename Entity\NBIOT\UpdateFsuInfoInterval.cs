﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    //更新FSU状态信息获取周期
    public class UpdateFsuinfoInterval : BMessage
    {
        //public short? Interval { get; set; }

        [Newtonsoft.Json.JsonProperty(Order = 1)]
        [Newtonsoft.Json.JsonConverter(typeof(CustomSringConverter))]
        public Int16 Interval { get; set; }

        public UpdateFsuinfoInterval() : base()
        {
            MessageType = (int)BMessageType.UPDATE_FSUINFO_INTERVAL;
        }

        //public UpdateFsuinfoInterval(string fsuId, short? interval):base()
        public UpdateFsuinfoInterval(string fsuId, Int16 interval)
            : base()
        {
            MessageType = (int)BMessageType.UPDATE_FSUINFO_INTERVAL;

            FSUID = fsuId;
            Interval = interval;
        }

        public override string Serialize()
        {
            string result = string.Empty;
            try
            {
                JsonSerializerSettings settings = new JsonSerializerSettings();
                settings.ContractResolver = new CustomContractResolver();
                TIotEntity iotEntity = new TIotEntity(this);
                result = JsonConvert.SerializeObject(iotEntity, settings);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("UpdateFsuinfoInterval.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("UpdateFsuinfoInterval.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
            }
            return result;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            string str = String.Format("{0},{1},{2}:{3}", MessageId, (BMessageType)MessageType, FSUID, Interval);
            sb.Append(str);

            return sb.ToString();
        }
    }
}