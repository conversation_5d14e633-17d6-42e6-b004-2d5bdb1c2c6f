﻿using Delivery.API;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using System;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace DM.TestOrder.Controllers
{
    public class EquipFileDownLoadController : BaseController
    {
        private readonly IHostingEnvironment _appEnvironment;

        public EquipFileDownLoadController(IHostingEnvironment appEnvironment)
        {
            _appEnvironment = appEnvironment;
        }
        [HttpGet]
        public FileStreamResult Get(string path)
        {
            
            string filePath = Path.Combine(_appEnvironment.WebRootPath, path);
            //HttpResponseMessage result = new HttpResponseMessage();
            //if (File.Exists(filePath))
            //{
            //    result.Content = new StreamContent(new FileStream(filePath, FileMode.Open));
            //    result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/x-zip-compressed");
            //    result.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment")
            //    {
            //        FileName = HttpUtility.UrlEncode(Path.GetFileName(filePath))
            //    };

            //}
            //return result;
            Stream stream = new FileStream(filePath, FileMode.Open);
            
            return File(stream, "application/x-zip-compressed", HttpUtility.UrlEncode(Path.GetFileName(filePath)));
            
        }
    }
}
