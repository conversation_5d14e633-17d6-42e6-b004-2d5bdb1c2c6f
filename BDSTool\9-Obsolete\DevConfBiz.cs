﻿

using BDSTool.BLL.Convert;
using BDSTool.BLL.S2;
using BDSTool.Entity;
using BDSTool.Entity.B;
using BDSTool.Entity.S2;
using BDSTool.Utility;

using Common.Logging.Pro;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;


namespace BDSTool.BLL.B
{
    public partial class DevConfBiz
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        #region public
        //public static bool GetFSUConfigByMonitorUnitId(int MonitorUnitId, ref  List<TDevConf> listDevice) {
        //    listDevice = new List<TDevConf>();
        //    try {

        //        List<TDevConfFromDB> listTDevConfEx = null;
        //        if (!EquipmentCMCCBiz.GetAllByMuId(MonitorUnitId, ref listTDevConfEx)) {
        //            LoggerBDSTool.Info("GetFSUConfigByMonitorUnitId();EquipmentCMCCBiz.GetDevListByMuId Failed");
        //            return false;
        //        }

        //        FillSignalList(listTDevConfEx);

        //        foreach (var devEx in listTDevConfEx) {
        //            listDevice.Add(devEx.device);
        //        }
        //    }
        //    catch (Exception ex) {
        //        logger.ErrorFormat("GetFSUConfigByMonitorUnitId();MonitorUnitId={0}; err={1}", MonitorUnitId, ex.Message);
        //        logger.Error(ex.StackTrace);
        //        return false;
        //    }
        //    return true;
        //}
        #endregion 

        #region private
        public static void FillSignalList(List<TDevConfFromDB> listTDevConfEx) {
            foreach (var devEx in listTDevConfEx) {
                int StationId = devEx.StationId;
                int EquipmentId = devEx.EquipmentId;
                var list = TSignalBiz.GetTSignalList(StationId, EquipmentId);
                devEx.device.Signals = list;


                if (list.Count == 0)
                    LoggerBDSTool.WarnFormat("FillSignalList(); device={0}; no tsignal", devEx.device.DeviceName);
                else
                    LoggerBDSTool.InfoFormat("FillSignalList(); device={0}; get Sig-Count={1}", devEx.device.DeviceName, list.Count);
            }
        }
        #endregion

    }
}
