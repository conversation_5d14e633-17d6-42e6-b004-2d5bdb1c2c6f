﻿using BDSTool.DBUtility.Common;
using BDSTool.Entity.DRHelper;
using BDSTool.Entity.S2;
using Common.Logging.Pro;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.BLL.S2
{
    class SamplerUnitBiz
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static bool GetSamplerIdOfSelfDiagnosis(ref int SamplerId) {
            try {
                //to do
                string sql;
                if (CommonUtils.IsNoProcedure)
                {
                    sql = Public_ExecuteSqlService.Instance.NoStore_TSLSampler_GetSql1();
                }
                else 
                {
                    sql = string.Format(@"SELECT TOP 1 SamplerId FROM TSL_Sampler WHERE  DllPath='KoloBusinessServer.exe'");
                }
                DbConfigPara para =  DBHelper.GetDbConfig();
                if (para.ProviderName.ToLower() == "mysql")
                {
                    if (CommonUtils.IsNoProcedure)
                    {
                        sql = Public_ExecuteSqlService.Instance.NoStore_TSLSampler_GetSql2();
                    }
                    else
                    {
                        sql = string.Format(@"SELECT SamplerId FROM TSL_Sampler WHERE  DllPath='KoloBusinessServer.exe' LIMIT 1");
                    }
                }
                var rtn = DBHelper.ExecuteScalar(sql);
                if (string.IsNullOrEmpty(rtn))
                    return false;
                SamplerId = int.Parse(rtn.ToString());
            }
            catch (Exception ex) {
                logger.ErrorFormat("GetSamplerIdOfSelfDiagnosis();Error={0}", ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }

    }
}
