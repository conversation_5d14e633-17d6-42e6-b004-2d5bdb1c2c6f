﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;
using Common.Logging.Pro;
using System.IO;

namespace ENPC.Kolo.Entity.B_CMCC
{
    public abstract class BMessage : Message
    {

        protected static readonly log4net.ILog entityLogger = LogManager.GetLogger("Entity");


        public new int MessageType
        {
            get
            {
                return messageType;
            }
            protected set
            {
                messageType = value;
                StringMessageType = ((BMessageType)messageType).ToString();
            }
        }

        public BMessage()
            : base()
        {
            // B接口中没有消息Id，在这生成随机的Id，目地是兼容原有的数据库存储逻辑
            Random rand = new Random();
            MessageId = (uint)rand.Next(0, int.MaxValue);
            
            MessageFamily = MessageFamily.B;
            MessageType = (int)BMessageType.UNDEFINED;
            UserType = Entity.UserType.CMCC;
        }

        protected XmlDocument CreateXmlDocument()
        {
            XmlDocument xmldoc = new XmlDocument();

            XmlDeclaration xmldecl;
            xmldecl = xmldoc.CreateXmlDeclaration("1.0", "UTF-8", null);
            xmldoc.AppendChild(xmldecl);

            return xmldoc;
        }

        protected void AppendPK_Type(XmlDocument xmldoc, string protocolType, string name)
        {
            XmlElement xmlelem = xmldoc.CreateElement("", protocolType, "");
            xmldoc.AppendChild(xmlelem);

            XmlNode root = xmldoc.SelectSingleNode(protocolType);

            XmlElement xe1 = xmldoc.CreateElement("PK_Type");
            XmlElement xesub1 = xmldoc.CreateElement("Name");
            xesub1.InnerText = name;
            xe1.AppendChild(xesub1);

            root.AppendChild(xe1);
        }

        /// <summary>
        /// 检查返回的Result字段是否是合法的枚举值
        /// 目前移动没有明确枚举到底是传值还是字符串，此方法的解析是两者兼容
        /// </summary>
        /// <param name="strResult">result字符串</param>
        /// <param name="errorMsg">错误信息字符串</param>
        /// <returns>返回枚举值</returns>
        public static EnumResult CheckEnumResult(string strResult, ref string errorMsg)
        {
            EnumResult result = EnumResult.FAILURE;
            errorMsg = string.Empty;
            switch(strResult)
            {
                case "SUCCESS":
                case "1":
                    result = EnumResult.SUCCESS; 
                    break;
                case "FAILURE":
                case "0":
                    result = EnumResult.FAILURE; 
                    break;
                default:
                    result = EnumResult.FAILURE;
                    errorMsg = "'Result' field is invalid";
                    break;
            }
            return result;
        }

        /// <summary>
        /// 检测输入字符串是否是数值类型
        /// </summary>
        public static bool CheckNumberValue(string strValue)
        {
            bool isNumber = false;
            try
            {
                Convert.ToInt32(strValue);
                isNumber = true;
            }
            catch(Exception ex)
            {
                entityLogger.ErrorFormat("{0} is not a number:{1}", strValue, ex.Message);
            }
            return isNumber;
        }

        /// <summary>
        /// 格式化xml
        /// </summary>
        /// <param name="sUnformattedXml"></param>
        /// <returns></returns>
        public static string FormatXml(string sUnformattedXml)
        {
            StringBuilder sb = new StringBuilder();
            try
            {
                XmlDocument xd = new XmlDocument();
                xd.LoadXml(sUnformattedXml);
                StringWriter sw = new StringWriter(sb);
                XmlTextWriter xtw = null;
                try
                {
                    xtw = new XmlTextWriter(sw);
                    xtw.Formatting = Formatting.Indented;
                    xtw.Indentation = 1;
                    xtw.IndentChar = '\t';
                    xd.WriteTo(xtw);
                }
                finally
                {
                    if (xtw != null)
                        xtw.Close();
                }
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("Input string is not xml format:{0},{1}", sUnformattedXml, ex.Message);
            }
            return sb.ToString();
        }
    }
}
