﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class SendHisAiData : BMessage
    {
        public List<Device> LDevice { get; set; }
        public SendHisAiData(string suids, string surids, List<Device> ldevice)
            : base()
        {
            MessageType = (int)BMessageType.SEND_HISAIDATA;
            SUId = suids;
            SURId = surids;
            LDevice = ldevice;
        }
        public SendHisAiData()
            : base()
        {
            MessageType = (int)BMessageType.SEND_HISAIDATA;
        }
        public static SendHisAiData Deserialize(XmlDocument xmlDoc)
        {
            SendHisAiData sendHisAiData = null;
            string errMsg = string.Empty;
            string suid = string.Empty;
            string surid = string.Empty;

            try
            {
                suid = xmlDoc.SelectSingleNode("/Request/Info/SUId").InnerText.Trim();
                surid = xmlDoc.SelectSingleNode("/Request/Info/SURId").InnerText.Trim();
                XmlNodeList nodes = xmlDoc.SelectNodes("/Request/Info/Values/DeviceList/Device");
                List<Device> ldevice = new List<Device>();
                foreach (XmlElement node in nodes)
                {
                    Device dev = new Device();
                    dev.Id = node.GetAttribute("Id").Trim();
                    dev.RId = node.GetAttribute("RId").Trim();
                    XmlNodeList ndlists = node.SelectNodes("Signal");
                    dev.SignalList = new List<Signal>();
                    foreach (XmlElement nd in ndlists)
                    {
                        Signal sdv = new Signal();
                        sdv.Id = nd.GetAttribute("Id").Trim();
                        //sdv.Value = string.IsNullOrEmpty(nd.GetAttribute("Value").Trim()) ? 0f : float.Parse(nd.GetAttribute("Value").Trim());
                        string strValue = nd.GetAttribute("Value").Trim();
                        if (string.IsNullOrEmpty(strValue))
                        {
                            errMsg = "SEND_HISAIDATA Signal Value is nullOrEmpty";
                            break;                            
                        }
                        else
                        {
                            sdv.Value = float.Parse(strValue);
                        }
                        sdv.RecordTime = nd.GetAttribute("RecordTime").Trim();
                        dev.SignalList.Add(sdv);
                    }
                    ldevice.Add(dev);
                }
                if (errMsg != string.Empty)
                {
                    entityLogger.ErrorFormat("SendHisAiData.Deserialize():{0}", errMsg);
                    sendHisAiData = GetErrorEntity(suid, surid, errMsg);
                }
                else
                {
                    sendHisAiData = new SendHisAiData(suid, surid, ldevice);
                }
                entityLogger.DebugFormat("SendHisAiData.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                sendHisAiData.StringXML = xmlDoc.InnerXml;
                return sendHisAiData;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SendHisAiData.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SendHisAiData.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                sendHisAiData = GetErrorEntity(suid, surid, ex.Message);
                sendHisAiData.StringXML = xmlDoc.InnerXml;
                return sendHisAiData;
            }
        }

        /// <summary>
        /// 反序列化异常时，构造带错误信息的实体
        /// </summary>
        /// <param name="errorMsg">解析错误信息</param>
        /// <returns></returns>
        private static SendHisAiData GetErrorEntity(string suId, string surId, string errorMsg)
        {
            SendHisAiData errorEntity = new SendHisAiData();
            SendHisAiDataAck ackEntity = new SendHisAiDataAck(suId, surId, EnumResult.FAILURE);
            ackEntity.ErrorMsg = errorMsg;
            errorEntity.ErrorMsg = ackEntity.Serialize();
            return errorEntity;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            if (LDevice.Count > 0)
            {
                foreach (Device de in LDevice)
                {
                    sb.AppendFormat("{0},{1},{2},{3},{4},{5}\n", MessageId, (BMessageType)MessageType, MessageType, SUId, SURId, de.PartFiledToString());
                }
            }
            else
            {
                sb.AppendFormat("{0},{1},{2},{3},{4},Device is null", MessageId, (BMessageType)MessageType, MessageType, SUId, SURId);
            }
            return sb.ToString();
        }
    }
}
