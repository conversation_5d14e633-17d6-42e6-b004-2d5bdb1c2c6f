﻿using BLL;
using Delivery.API;
using DM.TestOrder.Common.DBA;
using DM.TestOrder.DAL;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;




namespace DM.TestOrder.Controllers {

    public class CheckItemCmdController : BaseController {

        [HttpGet]
        public HttpResponseMessage Get(int id) {
            var sql = string.Format("WO_Debug_CheckItemCmd {0}", id);
            var dt = DBHelper.GetData(sql);

            var sReturn = JsonConvert.SerializeObject(dt);
            HttpResponseMessage result = new HttpResponseMessage {
                Content = new StringContent(sReturn, Encoding.GetEncoding("UTF-8"), "application/json")
            };
            return result; 


        }
    }
}
