﻿

using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using Delivery.DAL;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;


namespace DM.TestOrder.DAL {
    public class WoGeoDal
    {
        public static string Validate(decimal Latitude, decimal Longitude) {
            //string sql = string.Format("WO_ValidateGeoInfo {0},{1}", SHelper.GetPara(Latitude), SHelper.GetPara(Longitude));
            //var rtn = DBHelper.ExecuteScalar(sql);
            //return rtn;
            if (CommonUtils.IsNoProcedure)
            {
                var rtn1 = WoCommonService.Instance.WO_ValidateGeoInfo(Latitude,Longitude);
                return rtn1 == null ? null : rtn1.ToString();
            }
            var rtn =  new ExecuteSql().ExecuteStoredProcedureScalar("WO_ValidateGeoInfo", new QueryParameter[] {
                 new QueryParameter("Latitude", DataType.Number, Latitude.ToString()),
                 new QueryParameter("Longitude", DataType.Number, Longitude.ToString())
            });
            return rtn == null ? null : rtn.ToString();
        }
    }
}

