﻿using Common.Logging.Pro;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;



using BDSTool.Entity.S2;
using BDSTool.DBUtility.Common;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace BDSTool.BLL.S2
{
    public class ControlBiz
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static bool SetCommandMeanings(TBL_Control control) {
            try {

                foreach (var state in control.ParameterValue2Meanings) {
                    int ParameterValue = state.Key;
                    string Meanings=state.Value;

                    string sql;
                    if (CommonUtils.IsNoProcedure) {
                        string temp = Public_ExecuteSqlService.Instance.NoStore_TBLControlMeanings_InsertSql();
                        sql = string.Format(temp, control.EquipmentTemplateId, control.ControlId, ParameterValue, Meanings);
                    }
                    else 
                    {
                         sql = string.Format(@"INSERT INTO TBL_ControlMeanings
                            (EquipmentTemplateId, ControlId, ParameterValue, Meanings)
                            VALUES ({0}, {1}, {2}, '{3}')" , control.EquipmentTemplateId, control.ControlId, ParameterValue, Meanings);
                    }

                    DBHelper.ExecuteNonQuery(sql);
                }
            }
            catch (Exception ex) {
                logger.ErrorFormat("SetCommandMeanings();EquipmentTemplateId={0},ControlName={1}; Error={2}", 
                    control.EquipmentTemplateId, control.ControlName,ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }

        public static bool SaveEntityList(List<TBL_Control> controls) {
            if (BatchInsertHelper.Execute(controls) == false) {
                logger.InfoFormat("ControlBiz.SaveEntityList();TBL_Signal 批量插入失败");
                return false;
            }

            //------------------------------------------------------------------------
            var listSwitch = controls.Where(o => o.CommandType==2).ToList();//开关量
            var meaningsList = new List<TBL_ControlMeanings>();
            foreach (var o in listSwitch) {
                foreach (var sm in o.ParameterValue2Meanings) {
                    var m = new TBL_ControlMeanings {
                        EquipmentTemplateId = o.EquipmentTemplateId,
                        ControlId = o.ControlId,
                        ParameterValue = (short)sm.Key,
                        Meanings = sm.Value
                    };
                    meaningsList.Add(m);
                }
            }

            if (BatchInsertHelper.Execute(meaningsList) == false) {
                logger.InfoFormat("ControlBiz.SaveEntityList();meaningsList 批量插入失败");
                return false;
            }
            //------------------------------------------------------------------------
            ConfigHelper.UpdateBaseTypeIdOfControl(controls[0].EquipmentTemplateId);
            return true;
        }

    }
}
