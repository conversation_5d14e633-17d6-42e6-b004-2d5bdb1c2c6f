﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class SetAoDataAck : BMessage
    {
        public EnumResult Result { get; set; }

        //框架使用，此处不赋值
        public SetAoData MySetAoData { get; set; }

        #region SiteWeb配置

        public int MyStationId { get; set; }

        public int UserId { get; set; }
        public DateTime StartTime { get; set; }
        public int ControlId { get; set; }

        #endregion
        public SetAoDataAck() : base()
        {
            MessageType = (int)BMessageType.SET_AODATA_ACK;
        }
        public SetAoDataAck(string suids, string surids, EnumResult result) : base()
        {
            MessageType = (int)BMessageType.SET_AODATA_ACK;
            SUId = suids;
            SURId = surids;
            Result = result;
        }
        public static SetAoDataAck Deserialize(XmlDocument xmldoc)
        {
            SetAoDataAck setAoDataAck = null;
            try
            {
                string suid = xmldoc.SelectSingleNode("/Response/Info/SUId").InnerText.Trim();
                string surid = xmldoc.SelectSingleNode("/Response/Info/SURId").InnerText.Trim();
                string result = xmldoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim();
                setAoDataAck = new SetAoDataAck(suid, surid, string.IsNullOrEmpty(result) ? EnumResult.FAILURE : (EnumResult)int.Parse(result));
                entityLogger.DebugFormat("SetAoDataAck.Deserialize(),xml:\r\n{0}", FormatXml(xmldoc.InnerXml));
                setAoDataAck.StringXML = xmldoc.InnerXml;
                return setAoDataAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetAoDataAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetAoDataAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmldoc.InnerXml));
                setAoDataAck = new SetAoDataAck();
                setAoDataAck.ErrorMsg = ex.Message;
                setAoDataAck.StringXML = xmldoc.InnerXml;
                return setAoDataAck;
            }
        }

        public override string ToString()
        {
            return String.Format("{0}, {1},{2},{3}, {4}: {5}",
                MessageId, (BMessageType)MessageType,MessageType, SUId, SURId, Result);
        }
    }
}
