﻿using BLL;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using testorder.dal;

using DM.TestOrder.formData;
using DM.TestOrder.Model;

using DM.TestOrder.Entity;
using DM.TestOrder.DAL;


namespace DM.TestOrder.Service.Convert.FormData {
    //public static void CreateTestOrder(string ApplyOrderId, string ApplyUserId, string StationId, string StationName) {
    public partial class FormDataMapper {
        public static WO_TestOrder ToEntity4CreateOrder(FormNewOrder newOrder) {
            if (newOrder.StationId == 0)
                throw new Exception("no-station-data");

            //允许空设备，以重置工单
            //if (!ui.SectionOrder.MyEquips.Any())
            //    throw new Exception("no-device-data");

             
   


            var userName = Account.GetUserNameByUserId(newOrder.ApplyUserId);
            WO_TestOrder myOrder = new WO_TestOrder() {
                //OrderId = newOrder.OrderId 待数据库分配

                //新装入网=0/维护巡检=1
                OrderType = newOrder.OrderType,


                StationId = newOrder.StationId,
                Latitude = (decimal)newOrder.Latitude,
                Longitude = (decimal)newOrder.Longitude,

                InstallCompany = newOrder.InstallCompany,
                InstallClerk = newOrder.InstallClerk,


                ApplyUserId = newOrder.ApplyUserId,
                ApplyUserName = Account.GetUserNameByUserId(newOrder.ApplyUserId),


                StateSetUserId = newOrder.ApplyUserId,
                StateSetUserName = userName,

                OrderState = 1//newOrder.OrderState
            };
            //----------------------------------------------------------------------
            //设备
            if (newOrder.MyEquips != null) {
                var equipItemList = (from u in newOrder.MyEquips
                                 select new WO_TestOrderEquipItem() {
                                     EquipmentId = u
                                 }).ToList();
                myOrder.EquipItemList = equipItemList;
            }

            
            //----------------------------------------------------------------------
           
            //数据库填充
            //myOrder.TestOrderEquipItemCheckList = TestOrderEquipItemCheckList;



            return myOrder;
        }
 
    }
}
