﻿using DM.TestOrder.DAL;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web.Http;
using testorder.dal;



namespace DM.TestOrder.Controllers {
        
    public class NewCompanyController : BaseApiController{

        /*
        以Post方式提交 
        url : http://localhost/orderapi/NewCompany
        数据:
        {
            "CompanyName": "新增公司"
        }
        */
        public HttpResponseMessage POST([FromBody]JObject value) {
            var CompanyName = value["CompanyName"].ToString();

            var rtnMsg = WoInstallCompanyDal.AddOne(CompanyName);

            var rtn = new {
                errormsg = rtnMsg
            };


            var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(rtn);


            Debug.WriteLine(value.ToString());
            Debug.WriteLine(jsonRtn.ToString());

            return new HttpResponseMessage() {
                Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            };
        }
    }
}
