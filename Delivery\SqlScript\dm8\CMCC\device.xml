﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
		<procedure owner="" name="SP_Add_WRDevice_FindByHouseId" grant="">
			<parameters>
				<parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
		    
		  SELECT 'X' FROM WR_HouseManagement WHERE WR_HouseManagement.WRHouseId = :WRHouseId AND WR_HouseManagement.HouseStatus != 3
        
      ]]>
			</body>
		</procedure>

		<procedure owner="" name="SP_Add_WRDevice_GetStationId" grant="">
			<parameters>
				<parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
		    
				SELECT a.WRStationId,b.SWStationId,a.SWHouseId 
					FROM WR_HouseManagement a 
					INNER JOIN WR_StationManagement b ON a.WRStationId = b.WRStationId
					WHERE a.WRHouseId = :WRHouseId;
        
			]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Add_WRDevice_FindByStationIdAndDeviceName" grant="">
			<parameters>
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
		    
					SELECT 'X' FROM WR_DeviceManagement a WHERE a.SWStationId = :SWStationId AND a.DeviceName = :DeviceName;
					SELECT 'X' FROM TBL_Equipment b WHERE b.StationId = :SWStationId AND b.EquipmentName = :DeviceName;
        
      ]]>
			</body>
		</procedure>

		<procedure owner="" name="SP_Add_WRDevice_GetUser" grant="">
			<parameters>
				<parameter name="LogonId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
		    
		    SELECT  T.UserId , T.UserName FROM TBL_Account T  WHERE T.LogonId = :LogonId ;
        
      ]]>
			</body>
		</procedure>

		<procedure owner="" name="SP_Add_WRDevice_Insert" grant="">
			<parameters>
				<parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRFsuId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceType" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWUserName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Remark" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
		    
		  INSERT INTO WR_DeviceManagement
				(WRStationId,WRHouseId,WRFsuId,DeviceType,DeviceCode,DeviceName,UserId,SWUserName,ApplyTime,SWStationId,SWHouseId,Remark)
			VALUES 
				(:WRStationId,:WRHouseId,:WRFsuId,:DeviceType,:DeviceCode,:DeviceName, :UserId,:SWUserName, now(), :SWStationId, :SWHouseId, :Remark);
        
      ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Add_WRDevice_WRDeviceId" grant="">
			<parameters>
				<parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
					SELECT  a.WRDeviceId FROM WR_DeviceManagement a WHERE a.WRStationId = :WRStationId 
					AND a.DeviceName = :DeviceName;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Add_WRDevice_GetStationName" grant="">
			<parameters>
				<parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
		    
		     SELECT IFNULL(StationName,'')  FROM WR_StationManagement WHERE WR_StationManagement.WRStationId = :WRStationId;
        
      ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_GetDeviceSubCategory_DataItem" grant="">
			<parameters>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
		   SELECT CASE WHEN length(ItemId) = 1 THEN concat('0',TO_CHAR(ItemId)) 
		   ELSE TO_CHAR(ItemId) END  HouseCategoryId,ItemValue HouseCategoryName
		   FROM WR_DataItem WHERE WR_DataItem.EntryId = 9;
      ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_GetDeviceSubCategory_DataItem2" grant="">
			<parameters>
				<parameter name="iDeviceCategory" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
		 SELECT CASE WHEN length(ItemId) = 1 THEN concat('0',TO_CHAR(ItemId)) 
		 ELSE TO_CHAR(ItemId) END  HouseCategoryId, 
		 ItemValue HouseCategoryName
		 FROM WR_DataItem 
		 WHERE WR_DataItem.EntryId = 9 
		 AND WR_DataItem.ParentEntryId = 8 
		 AND WR_DataItem.ParentItemId = :iDeviceCategory;	
      ]]>
			</body>
		</procedure>
    <procedure owner="" name="SP_GetDeviceCategory" grant="">
      <body>
        <![CDATA[
		    SELECT CASE WHEN length(ItemId) = 1 THEN concat('0',to_char(ItemId)) 
		    ELSE to_char(ItemId) END  HouseCategoryId, ItemValue HouseCategoryName
	      FROM WR_DataItem WHERE WR_DataItem.EntryId = 8;
			]]>
      </body>
    </procedure>
	</procedures>
</root>
