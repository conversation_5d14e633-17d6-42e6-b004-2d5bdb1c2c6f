﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.6.16.	请求动环设备配置数据
    /// </summary>
    public sealed class GetDevConf : BMessage
    {
        //public List<string> DeviceIDs { get; private set; }
        //原来是个list，新文档改成单个设备ID
        public string DeviceID { get; private set; }

        public GetDevConf(string fsuId, string deviceId)
            : base()
        {
            MessageType = (int)BMessageType.GET_DEV_CONF;

            FSUID = fsuId;
            //DeviceIDs = deviceIds;
            DeviceID = deviceId; 
        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmldoc = CreateXmlDocument();
                AppendPK_Type(xmldoc, "Request", BMessageType.GET_DEV_CONF.ToString());

                XmlElement xe2 = xmldoc.CreateElement("Info");
                XmlElement xe21 = xmldoc.CreateElement("FSUID");
                xe21.InnerText = FSUID;
                xe2.AppendChild(xe21);
                //原来是个list，新文档改成单个设备ID
                //XmlElement xe22 = xmldoc.CreateElement("DeviceList");
                //foreach (string deviceId in DeviceIDs)
                //{
                //    XmlElement xe221 = xmldoc.CreateElement("Device");
                //    xe221.SetAttribute("ID", deviceId);
                //    xe22.AppendChild(xe221);
                //}
                XmlElement xe22 = xmldoc.CreateElement("DeviceID");
                xe22.InnerText = DeviceID;
                xe2.AppendChild(xe22);
                XmlNode root = xmldoc.SelectSingleNode("Request");
                root.AppendChild(xe2);

                StringXML = xmldoc.InnerXml;
                entityLogger.DebugFormat("GetDevConf.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmldoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetDevConf.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetDevConf.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            string str = String.Format("{0}, {1}, {2}:{3}",
                MessageId, (BMessageType)MessageType, FSUID, DeviceID);
            sb.Append(str);

            //foreach (string id in DeviceIDs)
            //{
            //    sb.AppendFormat("[{0}]", id);
            //}
            return sb.ToString();
        }

    }
}
