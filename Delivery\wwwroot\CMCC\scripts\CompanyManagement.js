﻿'use strict';

$(function () {
    var editIndex = -1; //编辑行的index

    //datagrid加载初始化
    $('#dg_Company').datagrid({
        title: '安装公司管理',
        toolbar: '#toolbar',
        method: 'GET',
        url: '../api/InstallCompany',
        rownumbers: true,
        width: document.body.clientWidth * 0.98,
        scrollbarSize: 0,
        fitColumns: true,
        singleSelect: true,
        striped: true,
        columns: [[
            { field: 'CompanyId', hidden: true },
            { field: 'CompanyName', title: '公司名称', width: 100, editor: { type: 'validatebox', options: { required: true, tipPosition: 'top', validateOnBlur: true } } }
        ]]
    });

    //添加按钮事件
    $('#btn_Add').on('click', function () {
        if (editIndex !== -1) {
            alertInfo('一次仅允许添加一条,请先保存上条新增记录！');
            return false;
        }
        var dg = $('#dg_Company');
        dg.datagrid('appendRow', { isNewRow: true });
        editIndex = dg.datagrid('getRows').length - 1;
        dg.datagrid('beginEdit', editIndex);
        var editor = dg.datagrid('getEditor', { index: editIndex, field: 'CompanyName' }).target[0];
        editor.focus();
    });

    //保存事件
    $('#btn_Save').on('click', function () {
        if (editIndex === -1) {
            return false;
        }
        var dg = $('#dg_Company');
        var editor = dg.datagrid('getEditor', { index: editIndex, field: 'CompanyName' });
        if (editor.target.validatebox('isValid')) {
            dg.datagrid('endEdit', editIndex);
            var row = dg.datagrid('getChanges')[0];
            $.messager.confirm('确认', '您确认保存吗？',
                function (r) {
                    if (r)
                        $.ajax({
                            type: "POST",
                            contentType: "application/json; charset=UTF-8",
                            url: '../api/NewCompany',
                            data: JSON.stringify({ CompanyName: row.CompanyName }),

                            success: function (data) {
                                if (data.errormsg === "OK") {
                                    editIndex = -1;
                                    alertInfo('保存成功！');
                                    dg.datagrid('reload');
                                } else {
                                    alertInfo(data.errormsg, 'error');
                                    dg.datagrid('selectRow', editIndex);
                                    dg.datagrid('beginEdit', editIndex);
                                }
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                                dg.datagrid('selectRow', editIndex);
                                dg.datagrid('beginEdit', editIndex);
                            }
                        });
                    else {
                        dg.datagrid('selectRow', editIndex);
                        dg.datagrid('beginEdit', editIndex);
                    }
                });
        } else 
            alertInfo('公司名称不允许为空！');
    });

    //取消事件
    $('#btn_Cancel').on('click', function () {
        if (editIndex !== -1) {
            //$('#dg_Company').datagrid('cancelEdit', editIndex);
            $('#dg_Company').datagrid('rejectChanges');
            editIndex = -1;
        }
    });

    $('div.mask').remove();
});