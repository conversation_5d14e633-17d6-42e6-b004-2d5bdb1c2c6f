
namespace BDSTool.Entity.S2
{
    using System;
    using System.Collections.Generic;
    
    public partial class TBL_EventCondition
    {
        public int EventConditionId { get; set; }
        public int EquipmentTemplateId { get; set; }
        public int EventId { get; set; }
        public string StartOperation { get; set; }
        public double StartCompareValue { get; set; }
        public int StartDelay { get; set; }
        public string EndOperation { get; set; }
        public Nullable<double> EndCompareValue { get; set; }
        public Nullable<int> EndDelay { get; set; }
        public Nullable<int> Frequency { get; set; }
        public Nullable<int> FrequencyThreshold { get; set; }
        public string Meanings { get; set; }
        public Nullable<short> EquipmentState { get; set; }
        public Nullable<decimal> BaseTypeId { get; set; }
        public int EventSeverity { get; set; }
        public Nullable<int> StandardName { get; set; }
    }
}
