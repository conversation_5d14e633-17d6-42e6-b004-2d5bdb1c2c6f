﻿using Microsoft.Extensions.Configuration;

using System;
using System.Collections.Generic;
using System.Text;

namespace Delivery.Common
{
    public static class ConfigHelper
    {
        private static readonly IConfiguration _config;
        static ConfigHelper()
        {
            string fileName = "appsettings.json";
            string directory = AppContext.BaseDirectory.Replace("\\", "/");
            string filePath = $"{directory}/{fileName}";
            _config = new ConfigurationBuilder().AddJsonFile(filePath, false, true).Build();
        }

        public static IConfiguration GetConfig()
        {
            return _config;
        }

        public static IConfigurationSection GetSection(string path)
        {
            return _config.GetSection(path);
        }

        public static string GetConnectionString(string name)
        {
            return _config.GetConnectionString(name);
        }

        #region No Procedure

        /// <summary>
        /// NoProcedure
        /// </summary>
        public static string NoProcedure
        {
            get
            {
                string noProcedure = _config.GetSection("NoProcedure").Value;
                if (string.IsNullOrWhiteSpace(noProcedure))
                    return string.Empty;
                else
                    return noProcedure;
            }
        }

        /// <summary>
        /// WorkFlow
        /// </summary>
        public static string WorkFlow
        {
            get
            {
                string noWorkFlow = _config.GetSection("WorkFlow").Value;
                if (string.IsNullOrWhiteSpace(noWorkFlow))
                    return string.Empty;
                else
                    return noWorkFlow;
            }
        }

        /// <summary>
        /// WorkFBaseUrllow
        /// </summary>
        public static string BaseUrl
        {
            get
            {
                string noBaseUrl = _config.GetSection("BaseUrl").Value;
                if (string.IsNullOrWhiteSpace(noBaseUrl))
                    return string.Empty;
                else
                    return noBaseUrl;
            }
        }
        /// <summary>
        /// HostUrl
        /// </summary>
        public static string HostUrl
        {
            get
            {
                string noHostUrl = _config.GetSection("HostUrl").Value;
                if (string.IsNullOrWhiteSpace(noHostUrl))
                    return string.Empty;
                else
                    return noHostUrl;
            }
        }

        /// <summary>
        /// HostUrl
        /// </summary>
        public static string Urls
        {
            get
            {
                string noUrls = _config.GetSection("Urls").Value;
                if (string.IsNullOrWhiteSpace(noUrls))
                    return string.Empty;
                else
                    return noUrls;
            }
        }


        /// <summary>
        /// Sqlpath
        /// </summary>
        public static string Sqlpath
        {
            get
            {
                string sqlpath = _config.GetSection("Sqlpath").Value;
                if (string.IsNullOrWhiteSpace(sqlpath))
                    return string.Empty;
                else
                    return sqlpath;
            }
        }

        /// <summary>
        /// DefaultNp providerName
        /// </summary>
        public static string DefaultNpProviderName
        {
            get
            {
                string providerName = _config.GetSection("Database:default_np:providerName").Value;
                if (string.IsNullOrWhiteSpace(providerName))
                    return "mysql";
                else
                    return providerName;
            }
        }

        /// <summary>
        /// DefaultNp ConnectionString
        /// </summary>
        public static string DefaultNpConnectionString
        {
            get
            {
                string connectionString = _config.GetSection("Database:default_np:connectionString").Value;
                if (string.IsNullOrWhiteSpace(connectionString))
                    return string.Empty;
                else
                    return connectionString;
            }
        }

        #endregion
    }
}
