﻿@page
@model Delivery.Pages.CMCC.ProjectMainModel
@{
    ViewData["Title"] = "接入管理";
}

@section Scripts {
    <script type="text/javascript" src="~/Scripts/lib/jquery-1.8.0.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/Math.uuid.js"></script>
    <script type="text/javascript" src="~/Scripts/Default.js"></script>
    <script type="text/javascript" src="~/Scripts/common/common.js"></script>
    <script type="text/javascript" src="~/Scripts/common/ie8.js"></script>
    <script type="text/javascript" src="~/CMCC/scripts/ProjectMain.js"></script>
}

@section Styles {
    <style type="text/css">
        * {
            font-size: 12px;
        }

        .normalmenu li {
            padding-top: 20px;
            padding-bottom: 20px;
        }

        .mask {
            position: fixed;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            overflow: hidden;
            background-color: #fff;
            background-attachment: scroll;
            z-index: 999;
            font-size: 12px;
        }

        #menu li a {
            text-decoration: none;
        }
    </style>
}

<noscript>
    <div style="position: absolute; z-index: 100000; height: 2046px; top: 0px; left: 0px;
            width: 100%; background: white; text-align: center;">
        <img src="images/noscript.gif" alt='抱歉，请开启脚本支持！' />
    </div>
</noscript>
<div class="mask">
    <div style="position: relative; top: 50%; left: 50%">
        <div style="position: absolute; top: -17px; left: -70px; height: 16px; padding: 12px 5px 10px 30px; background: #fff url(../Content/themes/default/images/pagination_loading.gif) no-repeat scroll 5px 10px; border: 2px solid #ccc; color: #000;">
            正在加载，请等待...
        </div>
    </div>
</div>
@*<div region="north" style="padding:5px;overflow:hidden">
    <div style="text-align: right;"><span><a style="text-decoration:none;" href="../../SiteWeb/Pages/Common/main.aspx">返回主页</a></span></div>
</div>*@
<div region="center" id="mainPanle" style="background: #eee;overflow:hidden;">
    <div id="tabs" class="easyui-tabs" data-options="closable:true" fit="true" border="false">

    </div>
</div>
<div region="west" split="true" title="导航菜单" style="width: 200px;overflow:hidden;" icon="icon-redo">
    <div id="menu" class="easyui-accordion" fit="true" border="false">
        <div title="待办" style="overflow:auto;" icon="icon-edit">
            <ul style="list-style-type:none;padding:0px;margin:0 auto;">
                <li>
                    <ul id="daibanul" class="easyui-tree" style="display:none;">
                        <li>
                            <span>资源申请审批</span>
                            <ul>
                                <li><span><a id="stationToDo" title="站址管理">站址入网</a><span style="cursor:pointer;color:red;margin-left:5px" onclick="$('#stationToDo').click()"></span></span></li>
                                <li><span><a id="houseToDo" title="机房管理">机房入网</a><span style="cursor:pointer;color:red;margin-left:5px" onclick="$('#houseToDo').click()"></span></span></li>
                                <li><span><a id="fsuToDo" title="FSU管理">FSU入网</a><span style="cursor:pointer;color:red;margin-left:5px" onclick="$('#fsuToDo').click()"></span></span></li>
                            </ul>
                        </li>
                        <li>
                            <span>割接单申请审批</span>
                            <ul>
                                <li><span><a id="applyToDo" title="综合查询">入网申请</a><span style="cursor:pointer;color:red;margin-left:5px" onclick="$('#applyToDo').click()"></span></span></li>
                                <li><span><a id="expertToDo" title="综合查询">专家组复核</a><span style="cursor:pointer;color:red;margin-left:5px" onclick="$('#expertToDo').click()"></span></span></li>
                                <li><span><a id="reviewToDo" title="综合查询">入网复审</a><span style="cursor:pointer;color:red;margin-left:5px" onclick="$('#reviewToDo').click()"></span></span></li>
                            </ul>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
        <div title="资源管理" style="overflow:auto; padding: 10px;" icon="icon-edit">
            <ul class="normalmenu" style="list-style:none;">
                <li>
                    <a id="StationManagement" href="javascript:void(0)">站址管理</a>
                </li>
                <li>
                    <a id="HouseManagement" href="javascript:void(0)">机房管理</a>
                </li>
                <li>
                    <a id="FsuManagement" href="javascript:void(0)">FSU管理</a>
                </li>
                <li>
                    <a id="DeviceManagement" href="javascript:void(0);">设备管理</a>
                </li>
                <li>
                    <a id="ImportConfig" href="javascript:void(0);">配置导入</a>
                </li>
            </ul>
        </div>
        <div title="工程割接" style="overflow:auto; padding: 10px;" icon="icon-edit">
            <ul class="normalmenu" style="list-style:none;" id="ul_cutover">
                <li style="display:none;">
                    <a id="NewOrder" href="javascript:void(0)">新建割接单</a>
                </li>
                <li style="display:none;">
                    <a id="ComprehensiveQuery" href="javascript:void(0)">综合查询</a>
                </li>
                <li style="display:none;">
                    <a id="CompanyManagement" href="javascript:void(0)">安装公司管理</a>
                </li>
                <li style="display:none;">
                    <a id="DicManagement" href="javascript:void(0)">字典管理</a>
                </li>
                <li style="display:none;">
                    <a id="StandardDicManagement" href="javascript:void(0)">标准化管理</a>
                </li>
                <li style="display: none;">
                    <a id="StationCategoryManagement" href="javascript:void(0)">站址类型管理</a>
                </li>
            </ul>
        </div>
       
    </div>
</div>