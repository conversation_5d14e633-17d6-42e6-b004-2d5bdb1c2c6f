﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B_CUCC
{
    /// <summary>
    /// 告警消息的结构
    /// </summary>
    public sealed class TAlarm
    {
        /// <summary>
        /// 告警序号
        /// </summary>
        public string SerialNo { get; set; }
        /// <summary>
        /// SU编码
        /// </summary>
        public string SUID { get; set; }
        /// <summary>
        /// SU资管编码
        /// </summary>
        public string SURId { get; set; }
        /// <summary>
        /// 设备编码
        /// </summary>
        public string DeviceID { get; set; }
        /// <summary>
        /// 设备资管编码
        /// </summary>
        public string DeviceRId { get; set; }
        /// <summary>
        /// 监控点ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 告警时间
        /// </summary>
        public DateTime? AlarmTime { get; set; }
        /// <summary>
        /// 告警的事件描述
        /// </summary>
        public string AlarmDesc { get; set; }

        /// <summary>
        /// 告警触发值
        /// </summary>
        public float? TriggerVal { get; set; }

        /// <summary>
        /// 告警标志
        /// </summary>
        public EnumFlag AlarmFlag { get; set; }

        #region SiteWeb配置

        public int MyMonitorUnitId { get; set; }
        public int MyStationId { get; set; }
        public int MyDeviceId { get; set; }
        public int MyEventId { get; set; }
        public int MyConditionId { get; set; }

        //2018-03-16新增
        public List<int> MyEventIdList { get; set; }
        public List<int> MyConditionIdList { get; set; }
        public List<string> IDList { get; set; }//联通监控点标准化ID
        
        /// <summary>
        /// 实体中的SerialNo可能不唯一，因此增加MySerialNo来标识(MySerialNo=StationId.EquipmentId.EventId.EventConditionId.SerialNo)
        /// </summary>
        public string MySerialNo { get; set; }
        #endregion

        /// <summary>
        /// 告警消息的结构
        /// </summary>
        public TAlarm() { }
        /// <summary>
        /// 告警消息的结构
        /// </summary>
        /// <param name="serialNo">告警序号</param>
        /// <param name="suId">SU编码</param>
        /// <param name="surId">SU资管编码</param>
        /// <param name="id">监控点ID</param>
        /// <param name="deviceId">设备ID</param>
        /// <param name="alarmTime">告警时间，YYYY-MM-DD<SPACE键>hh:mm:ss（采用24小时的时间制式）</param>
        /// <param name="alarmDesc">告警的事件描述</param>
        public TAlarm(string serialNo, string suId, string surId, string id, string deviceId, string devicerId, DateTime? alarmTime
            , string alarmDesc, float? triggerVal, EnumFlag flag)
        {
            SerialNo = serialNo;
            SUID = suId;
            SURId = surId;
            ID = id;
            DeviceID = deviceId;
            DeviceRId = devicerId;
            AlarmTime = alarmTime;
            AlarmDesc = alarmDesc;
            TriggerVal = triggerVal;
            AlarmFlag = flag;
        }

        public override string ToString()
        {
            return String.Format(
                "[{0}, {1} ,{2} , {3}, {4}, {5}, {6}, {7},{8},{9}]",
                SerialNo, SUID, SURId, ID, DeviceID, DeviceRId
                ,AlarmTime.HasValue ? Convert.ToDateTime(AlarmTime).ToString("yyyy-MM-dd HH:mm:ss") : "", AlarmDesc, TriggerVal, AlarmFlag.ToString());
        }

    }
}
