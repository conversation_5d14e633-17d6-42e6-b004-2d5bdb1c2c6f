﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class SetAoData : BMessage
    {
        public List<Device> LDeviceInfo { get; set; }

        #region 20161205
        public int UserId { get; set; }
        public DateTime StartTime { get; set; }
        public int ControlId { get; set; }
        #endregion
        public SetAoData(string suids, string surids, List<Device> lDeviceInfo) : base()
        {
            MessageType = (int)BMessageType.SET_AODATA;
            SUId = suids;
            SURId = surids;
            LDeviceInfo = lDeviceInfo;
        }
        private XmlElement GetElementInfo(XmlDocument xmlDoc)
        {
            XmlElement xmle = xmlDoc.CreateElement("DeviceList");
            if (LDeviceInfo.Count > 0)
            {
                foreach (Device dev in LDeviceInfo)
                {
                    XmlElement xmlT = xmlDoc.CreateElement("Device");
                    xmlT.SetAttribute("Id", dev.Id);
                    xmlT.SetAttribute("RId", dev.RId);
                    if (dev.SignalList.Count > 0)
                    {
                        foreach (Signal sdv in dev.SignalList)
                        {
                            XmlElement xmlid = xmlDoc.CreateElement("Signal");
                            xmlid.SetAttribute("Id", sdv.Id);
                            xmlid.SetAttribute("SetValue", sdv.SetValue.ToString());
                            xmlid.SetAttribute("HLimit", sdv.HLimit.ToString());
                            xmlid.SetAttribute("SHLimit", sdv.SHLimit.ToString());
                            xmlid.SetAttribute("LLimit", sdv.LLimit.ToString());
                            xmlid.SetAttribute("SLLimit", sdv.SLLimit.ToString());
                            xmlid.SetAttribute("Threshold", sdv.Threshold.ToString());
                            xmlid.SetAttribute("RelativeVal", sdv.RelativeVal.ToString());
                            xmlid.SetAttribute("IntervalTime", sdv.IntervalTime.ToString());
                            xmlT.AppendChild(xmlid);
                        }
                    }
                    xmle.AppendChild(xmlT);
                }
            }
            else
            {
                XmlElement xmlT = xmlDoc.CreateElement("Device");
                xmlT.SetAttribute("Id", "");
                xmlT.SetAttribute("RId", "");
                XmlElement xml1 = xmlDoc.CreateElement("SignalId");
                xml1.InnerText = "";
                XmlElement xml2 = xmlDoc.CreateElement("SignalId");
                xml2.InnerText = "";
                xmlT.AppendChild(xml1);
                xmlT.AppendChild(xml2);
                xmle.AppendChild(xmlT);
            }
            return xmle;
        }
        public override string Serialize()
        {
            try
            {
                XmlDocument xmlDoc = CreateXmlDocument();
                AppendPK_Type(xmlDoc, "Request", BMessageType.SET_AODATA.ToString(), MessageType.ToString());
                XmlElement xel = xmlDoc.CreateElement("Info");
                XmlElement xelone = xmlDoc.CreateElement("SUId");
                XmlElement xeltwo = xmlDoc.CreateElement("SURId");
                xelone.InnerText = SUId;
                xeltwo.InnerText = SURId;
                xel.AppendChild(xelone);
                xel.AppendChild(xeltwo);
                xel.AppendChild(GetElementInfo(xmlDoc));
                XmlNode node = xmlDoc.SelectSingleNode("Request");
                node.AppendChild(xel);
                StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("SetAoData.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmlDoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetAoData.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetAoData.Serialize() Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }
    }

}
