﻿using System;
using System.Net;
using System.IO;
using System.Text;
using System.Net.Sockets;
using System.Diagnostics;
using System.Threading;


namespace Delivery.Common
{
    //页面进度条更新委托
    public delegate void DownLoadProgressBarDelegate(bool hasError, string errMessage);

    public class FsuFtpClient
    {
        public class FtpException : Exception
        {
            public FtpException(string message, int code) :
                base(message)
            {
                ResultCode = code;
            }

            public FtpException(string message, Exception innerException, int code)
                : base(message, innerException)
            {
                ResultCode = code;
            }

            public int ResultCode { get; set; }
        }


        #region 变量
        private static int BUFFER_SIZE = 8192;

        private bool verboseDebugging = false;

        // defaults
        private string server = "localhost";
        private string remotePath = ".";
        private string username = "anonymous";
        private string password = "<EMAIL>";
        private string message = null;
        private string result = null;

        private int port = 21;
        private int bytes = 0;
        private int resultCode = 0;

        private bool loggedin = false;
        private bool binMode = false;

        private Byte[] buffer = new Byte[BUFFER_SIZE];
        private Socket clientSocket = null;

        private int timeoutSeconds = 15;
        //microsecond
        private int socketTimeout = 15 * 1000;
        private readonly Object m_lock = new Object();
        /// <summary>
        /// local file save path
        /// </summary>
        private string localFilePath = "";
        /// <summary>
        /// 进度条更新委托
        /// </summary>
        public DownLoadProgressBarDelegate progressBarDelegate;
        //private int downloadCount = 0;
        //ftp根目录下的文件列表
        private string[] rootPathFileList = null;

        //editby xhy 20181101
        public AddressFamily addressFamily = AddressFamily.InterNetwork;

        #endregion

        #region 构造

        /// <summary>
        /// Default contructor
        /// </summary>
        public FsuFtpClient()
        {
            this.rootPathFileList = null;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="server"></param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        public FsuFtpClient(string server, string username, string password)
        {
            this.server = server;
            this.username = username;
            this.password = password;
            this.rootPathFileList = null;
        }

        public FsuFtpClient(string server, string username, string password, string remotepath, string localpath)
        {
            this.server = server;
            this.username = username;
            this.password = password;
            this.remotePath = remotepath;
            this.localFilePath = localpath;
            this.rootPathFileList = null;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="server"></param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <param name="timeoutSeconds"></param>
        /// <param name="port"></param>
        public FsuFtpClient(string server, string username, string password, int timeoutSeconds, int port)
        {
            this.server = server;
            this.username = username;
            this.password = password;
            this.timeoutSeconds = timeoutSeconds;
            this.port = port;
            this.rootPathFileList = null;
        }

        #endregion

        #region 属性

        /// <summary>
        /// Display all communications to the debug log
        /// </summary>
        public bool VerboseDebugging
        {
            get
            {
                return this.verboseDebugging;
            }
            set
            {
                this.verboseDebugging = value;
            }
        }

        /// <summary>
        /// Remote server port. Typically TCP 21
        /// </summary>
        public int Port
        {
            get
            {
                return this.port;
            }
            set
            {
                this.port = value;
            }
        }

        /// <summary>
        /// Timeout waiting for a response from server, in seconds.
        /// </summary>
        public int Timeout
        {
            get
            {
                return this.timeoutSeconds;
            }
            set
            {
                this.timeoutSeconds = value;
            }
        }

        /// <summary>
        /// Gets and Sets the name of the FTP server.
        /// </summary>
        /// <returns></returns>
        public string Server
        {
            get
            {
                return this.server;
            }
            set
            {
                this.server = value;
            }
        }

        /// <summary>
        /// Gets and Sets the port number.
        /// </summary>
        /// <returns></returns>
        public int RemotePort
        {
            get
            {
                return this.port;
            }
            set
            {
                this.port = value;
            }
        }

        /// <summary>
        /// GetS and Sets the remote directory.
        /// </summary>
        public string RemotePath
        {
            get
            {
                return this.remotePath;
            }
            set
            {
                this.remotePath = value;
            }

        }
        /// <summary>
        /// GetS and Sets the local directory
        /// </summary>
        public string LocalFilePath
        {
            get
            {
                return localFilePath;
            }
            set
            {
                this.localFilePath = value;
            }
        }

        /// <summary>
        /// Gets and Sets the username.
        /// </summary>
        public string Username
        {
            get
            {
                return this.username;
            }
            set
            {
                this.username = value;
            }
        }

        /// <summary>
        /// Gets and Set the password.
        /// </summary>
        public string Password
        {
            get
            {
                return this.password;
            }
            set
            {
                this.password = value;
            }
        }

        /// <summary>
        /// If the value of mode is true, set binary mode for downloads, else, Ascii mode.
        /// </summary>
        public bool BinaryMode
        {
            get
            {
                return this.binMode;
            }
            set
            {
                if (this.binMode == value) return;

                if (value)
                    sendCommand("TYPE I");

                else
                    sendCommand("TYPE A");

                if (this.resultCode != 200) throw new FtpException(result.Substring(4), resultCode);
            }
        }

        public bool LoginStatus
        {
            get
            {
                return this.loggedin;
            }

        }
        /// <summary>
        /// 要下载的文件数
        /// </summary>
        public int DownloadCount
        {
            get
            {
                try
                {
                    string[] fileList = this.GetFileList();
                    this.rootPathFileList = fileList;

                    Logger.Log("------------------获取FTP文件列表如下-----------------------");
                    Logger.Log("共获得 " + fileList.Length.ToString() + " 个文件");
                    foreach (string fileName in fileList)
                    {
                        Logger.Log(fileName);
                    }
                    Logger.Log("------------------获取FTP文件列表完毕-----------------------");

                    return fileList.Length;
                }
                catch (Exception ex)
                {
                    Logger.Log("FsuFtpClient,Get download files' count error:" + ex.Message);
                    return 0;
                }
            }
            //set
            //{
            //    this.downloadCount = value;
            //}
        }

        #endregion

        /// <summary>
        /// Login to the remote server.
        /// </summary>
        public void Login()
        {
            if (this.loggedin)
            {
                this.Close();
            }

            IPAddress addr = null;
            IPEndPoint ep = null;

            try
            {
                //editby xhy 20181101
                addr = GetIPAddress(this.server);
                if (addr.AddressFamily == AddressFamily.InterNetwork)
                {
                    addressFamily = AddressFamily.InterNetwork;
                    this.clientSocket = new Socket(addressFamily, SocketType.Stream, ProtocolType.Tcp);
                }
                else
                {
                    addressFamily = AddressFamily.InterNetworkV6;
                    this.clientSocket = new Socket(addressFamily, SocketType.Stream, ProtocolType.Tcp);
                }

                this.clientSocket.ReceiveTimeout = socketTimeout;//receive timeout
                this.clientSocket.SendTimeout = socketTimeout;//send timeout
                //addr = GetIPAddress(this.server);
                ep = new IPEndPoint(addr, this.port);
                this.clientSocket.Connect(ep);
            }
            catch (Exception ex)
            {
                // doubtfull
                if (this.clientSocket != null && this.clientSocket.Connected)
                {
                    this.clientSocket.Close();
                }

                //m_logger.InfoFormat("clientSocket {0},resultCode={1}", ex.Message, resultCode);
                throw new FtpException("Couldn't clientSocket connect to remote server", ex, resultCode);
            }

            this.readResponse();
            if (string.IsNullOrEmpty(this.result))
            {
                throw new FtpException("Couldn't connect to remote server", new Exception("Couldn't connect to remote server"), 202);
            }

            if (this.resultCode != 220)
            {
                this.Close();
                throw new FtpException(this.result.Substring(4), resultCode);
            }

            this.sendCommand("USER " + username);

            if (!(this.resultCode == 331 || this.resultCode == 230))
            {
                this.cleanup();
                throw new FtpException(this.result.Substring(4), resultCode);
            }

            if (this.resultCode != 230)
            {
                this.sendCommand("PASS " + password);

                if (!(this.resultCode == 230 || this.resultCode == 202))
                {
                    this.cleanup();
                    throw new FtpException(this.result.Substring(4), resultCode);
                }
            }

            this.loggedin = true;

            Debug.WriteLine("Connected to " + this.server, "FtpClient");

            this.ChangeDir(this.remotePath);
        }

        /// <summary>
        /// Close the FTP connection.
        /// </summary>
        public void Close()
        {
            Debug.WriteLine("Closing connection to " + this.server, "FtpClient");

            if (this.clientSocket != null)
            {
                this.sendCommand("QUIT");
            }

            this.cleanup();
        }

        /// <summary>
        /// Return a string array containing the remote directory's file list.
        /// </summary>
        /// <returns></returns>
        public string[] GetFileList()
        {
            return this.GetFileList("*");
        }

        public string[] GetFile(string filename)
        {
            //return this.GetFileList(filename)[0];
            return this.GetFileList(filename);
        }

        /// <summary>
        /// Return a string array containing the remote directory's file list.
        /// </summary>
        /// <param name="mask"></param>
        /// <returns></returns>
        public string[] GetFileList(string mask)
        {
            if (!this.loggedin)
            {
                this.Login();
            }
            Socket cSocket = null;
            try
            {
                cSocket = createDataSocket();
                //--原始
                //this.sendCommand("LIST " + mask);
                //--修改1
                //this.sendCommand("LIST -al");
                this.sendCommand("NLST");

                Thread.Sleep(1200);

                if (!(this.resultCode == 150 || this.resultCode == 125 || this.resultCode == 226))
                {
                    throw new FtpException(this.result.Substring(4), resultCode);
                }

                this.message = "";

                DateTime timeout = DateTime.Now.AddSeconds(this.timeoutSeconds);

                while (timeout > DateTime.Now)
                {
                    int bytes = cSocket.Receive(buffer, buffer.Length, 0);
                    this.message += Encoding.GetEncoding("gb2312").GetString(buffer, 0, bytes); ;

                    if (bytes < this.buffer.Length)
                    {
                        break;
                    }

                }

                string[] msg = System.Text.RegularExpressions.Regex.Split(this.message, @"\r\n");
                if (this.message.IndexOf("No such file or directory") != -1)
                    msg = new string[] { };

                this.readResponse();
                if (this.resultCode != 226)
                    msg = new string[] { };

                return msg;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                if (cSocket != null)
                    cSocket.Close();
            }
        }

        private string[] GetSubFolderFileList()
        {
            if (!this.loggedin)
            {
                this.Login();
            }
            Socket cSocket = null;
            try
            {
                cSocket = createDataSocket();
                //this.sendCommand("LIST -al");
                this.sendCommand("NLST");
                Thread.Sleep(1200);

                if (!(this.resultCode == 150 || this.resultCode == 125 || this.resultCode == 226))
                {
                    throw new FtpException(this.result.Substring(4), resultCode);
                }

                this.message = "";

                DateTime timeout = DateTime.Now.AddSeconds(this.timeoutSeconds);

                while (timeout > DateTime.Now)
                {
                    int bytes = cSocket.Receive(buffer, buffer.Length, 0);
                    this.message += Encoding.GetEncoding("gb2312").GetString(buffer, 0, bytes); ;

                    if (bytes < this.buffer.Length)
                    {
                        break;
                    }

                }

                string[] msg = System.Text.RegularExpressions.Regex.Split(this.message, @"\r\n");
                string[] msg1 = System.Text.RegularExpressions.Regex.Split(this.message, @"\n");
                if (msg.Length < msg1.Length)
                    msg = msg1;
                if (this.message.IndexOf("No such file or directory") != -1)
                    msg = new string[] { };

                this.readResponse();
                if (this.resultCode != 226)
                    msg = new string[] { };

                return msg;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                if (cSocket != null)
                    cSocket.Close();
            }
        }

        /// <summary>
        /// Return the size of a file.
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public long GetFileSize(string fileName)
        {
            if (!this.loggedin)
            {
                this.Login();
            }

            this.sendCommand("SIZE " + fileName);
            long size = 0;

            if (this.resultCode == 213)
                size = long.Parse(this.result.Substring(4));

            else
                throw new FtpException(this.result.Substring(4), resultCode);

            return size;
        }
        /// <summary>
        /// Check is file or not
        /// </summary>
        private bool IsFile(string fileName)
        {
            string strName = fileName.Trim();
            if (string.IsNullOrEmpty(strName))
            {
                return false;
            }
            string firstChar = fileName.Substring(0, 1);
            if (firstChar == "-")
            {
                return true;
            }
            //windows环境下，如果fileName中包含<DIR>，则是文件夹
            //linux环境下,fileName中第一个字母不是"-",则是文件夹
            if (System.Text.RegularExpressions.Regex.IsMatch(firstChar, @"^\d+$"))
            {//以数字开始属于windows环境特征,含<DIR>表文件夹，否则就是文件
                if (strName.Contains("<DIR>"))
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }
            return false;
        }
        /// <summary>
        /// 检查是文件还是文件夹(发送NLST命令)
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        private bool IsFileInNLST(string fileName)
        {
            string strName = fileName.Trim();
            if (string.IsNullOrEmpty(strName))
            {
                return false;
            }
            int index = fileName.IndexOf(".");
            if (index > 0)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// Parse name of file or folder
        /// </summary>
        private string ParseFileOrFolderName(string fullName)
        {
            int iLastSpace = fullName.LastIndexOf(" ");
            string fileOrFolderName = fullName.Substring(iLastSpace + 1, fullName.Length - iLastSpace - 1);
            return fileOrFolderName;
        }

        /// <summary>
        /// Download files in remote path to the Assembly's local path
        /// </summary>
        public void Download()
        {
            try
            {
                string[] fileList = null;
                if (this.rootPathFileList == null)
                {
                    fileList = GetFileList();
                    rootPathFileList = fileList;
                }
                else
                {
                    fileList = rootPathFileList;
                }
                if (fileList.Length > 0)
                {
                    foreach (string fullName in fileList)
                    {
                        if (!string.IsNullOrEmpty(fullName.Trim()))
                        {
                            //if (IsFile(fullName))
                            if (fullName.Trim() == "." || fullName.Trim() == "..")
                            {
                                //do nothing, 跳过 . 和 ..//部分FSU厂家存在这个问题，此处做个兼容
                            }
                            else if (IsFileInNLST(fullName))
                            {
                                string fileName = ParseFileOrFolderName(fullName);
                                this.Download(fileName, localFilePath + fileName);
                            }
                            else
                            {
                                DownloadSubFolder(fullName, localFilePath);
                            }
                        }
                        if (progressBarDelegate != null)
                        {
                            progressBarDelegate.Invoke(false, string.Empty);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (progressBarDelegate != null)
                {
                    progressBarDelegate.Invoke(true, ex.Message);
                }
                Logger.Log("FsuFtpClient.Download():" + ex.Message);
                Logger.Log("FsuFtpClient.Download():" + ex.StackTrace);
                throw (ex);
            }
        }

        private void DownloadSubFolder(string subFolderName, string localPath)
        {
            try
            {
                string folderName = ParseFileOrFolderName(subFolderName);
                if (folderName == string.Empty || folderName == "." || folderName == "..")
                {
                    return;
                }
                string localSubFolder = localPath + folderName + "\\";
                if (!Directory.Exists(localSubFolder))
                {
                    Directory.CreateDirectory(localSubFolder);
                }
                this.ChangeDir(folderName);
                string[] filesInSubFolder = GetSubFolderFileList();
                if (filesInSubFolder.Length > 0)
                {
                    foreach (string fullName in filesInSubFolder)
                    {
                        if (!string.IsNullOrEmpty(fullName.Trim()))
                        {
                            if (IsFile(fullName))
                            {
                                string fileName = ParseFileOrFolderName(fullName);
                                this.Download(fileName, localSubFolder + fileName);
                            }
                            else
                            {
                                //递归下载子文件夹
                                DownloadSubFolder(fullName, localSubFolder);
                            }
                        }
                    }
                }
                this.ChangeDir("..");
            }
            catch (Exception ex)
            {
                Logger.Log(subFolderName + "下载出错");
                Logger.Log("FsuFtpClient.DownloadSubFolder():" + ex.Message);
                Logger.Log("FsuFtpClient.DownloadSubFolder():" + ex.StackTrace);
            }
        }

        /// <summary>
        /// Download a file to the Assembly's local directory,
        /// keeping the same file name.
        /// </summary>
        /// <param name="remFileName"></param>
        public void Download(string remFileName)
        {
            this.Download(remFileName, "", false);
        }

        /// <summary>
        /// Download a remote file to the Assembly's local directory,
        /// keeping the same file name, and set the resume flag.
        /// </summary>
        /// <param name="remFileName"></param>
        /// <param name="resume"></param>
        public void Download(string remFileName, Boolean resume)
        {
            this.Download(remFileName, "", resume);
        }

        /// <summary>
        /// Download a remote file to a local file name which can include
        /// a path. The local file name will be created or overwritten,
        /// but the path must exist.
        /// </summary>
        /// <param name="remFileName"></param>
        /// <param name="locFileName"></param>
        public void Download(string remFileName, string locFileName)
        {
            this.Download(remFileName, locFileName, false);
        }

        /// <summary>
        /// Download a remote file to a local file name which can include
        /// a path, and set the resume flag. The local file name will be
        /// created or overwritten, but the path must exist.
        /// </summary>
        /// <param name="remFileName"></param>
        /// <param name="locFileName"></param>
        /// <param name="resume"></param>
        public void Download(string remFileName, string locFileName, Boolean resume)
        {
            try
            {
                if (!this.loggedin)
                {
                    this.Login();
                }

                this.BinaryMode = true;

                //Debug.WriteLine("Downloading file " + remFileName + " from " + server + "/" + remotePath, "FtpClient");

                if (locFileName.Equals(""))
                {
                    locFileName = remFileName;
                }

                FileStream output = null;

                try
                {
                    if (!File.Exists(locFileName))
                    {
                        output = File.Create(locFileName);
                    }

                    else
                    {
                        output = new FileStream(locFileName, FileMode.Create);
                    }
                    Socket cSocket = null;
                    try
                    {
                        cSocket = createDataSocket();

                        long offset = 0;

                        if (resume)
                        {
                            offset = output.Length;

                            if (offset > 0)
                            {
                                this.sendCommand("REST " + offset);
                                if (this.resultCode != 350)
                                {
                                    //Server dosnt support resuming
                                    offset = 0;
                                    Debug.WriteLine("Resuming not supported:" + result.Substring(4), "FtpClient");
                                }
                                else
                                {
                                    Debug.WriteLine("Resuming at offset " + offset, "FtpClient");
                                    output.Seek(offset, SeekOrigin.Begin);
                                }
                            }
                        }

                        this.sendCommand("RETR " + remFileName);

                        if (this.resultCode != 150 && this.resultCode != 125)
                        {
                            Debug.WriteLine("RETR :" + resultCode);
                            throw new FtpException(this.result.Substring(4), resultCode);
                        }

                        DateTime timeout = DateTime.Now.AddSeconds(this.timeoutSeconds);

                        while (timeout > DateTime.Now)
                        {
                            this.bytes = cSocket.Receive(buffer, buffer.Length, 0);
                            output.Write(this.buffer, 0, this.bytes);

                            if (this.bytes <= 0)
                            {
                                break;
                            }
                        }

                    }
                    catch (Exception ex)
                    {
                        Logger.Log(string.Format("FsuFtpClient.Download()ErrorMessage:{0}", ex.Message));
                        throw;
                    }
                    finally
                    {
                        if (cSocket != null)
                            cSocket.Close();
                    }
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("FsuFtpClient.Download()ErrorMessage:{0}", ex.Message));
                    throw;
                }
                finally
                {
                    if (output != null)
                    {
                        output.Close();
                    }
                }

                this.readResponse();

                if (this.resultCode != 226 && this.resultCode != 250)
                    throw new FtpException(this.result.Substring(4), resultCode);
            }
            catch (Exception ex1)
            {
                Logger.Log(remFileName + "下载出错");
                Logger.Log("FsuFtpClient.Download():" + ex1.Message);
                Logger.Log("FsuFtpClient.Download():" + ex1.StackTrace);
            }
        }

        #region  上传
        /// <summary>
        /// Upload a file.
        /// </summary>
        /// <param name="fileName"></param>
        public void Upload(string fileName)
        {
            this.Upload(fileName, false);
        }

        /// <summary>
        /// Upload a file and set the resume flag.
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="resume"></param>
        public void Upload(string fileName, bool resume)
        {
            lock (m_lock)
            {
                if (!this.loggedin)
                {
                    this.Login();
                }

                Socket cSocket = null;
                long offset = 0;
                this.BinaryMode = true;
                if (resume)
                {
                    try
                    {
                        //this.BinaryMode = true;

                        offset = GetFileSize(Path.GetFileName(fileName));
                    }
                    catch (Exception)
                    {
                        // file not exist
                        offset = 0;
                    }
                }

                // open stream to read file
                FileStream input = new FileStream(fileName, FileMode.Open);

                if (resume && input.Length < offset)
                {
                    // different file size
                    Debug.WriteLine("Overwriting " + fileName, "FtpClient");
                    offset = 0;
                }
                else if (resume && input.Length == offset)
                {
                    // file done
                    input.Close();
                    Debug.WriteLine("Skipping completed " + fileName + " - turn resume off to not detect.", "FtpClient");
                    return;
                }

                // dont create untill we know that we need it
                cSocket = this.createDataSocket();

                if (offset > 0)
                {
                    this.sendCommand("REST " + offset);
                    if (this.resultCode != 350)
                    {
                        Debug.WriteLine("Resuming not supported", "FtpClient");
                        offset = 0;
                    }
                }

                this.sendCommand("STOR " + Path.GetFileName(fileName));

                if (this.resultCode != 125 && this.resultCode != 150)
                {
                    throw new FtpException(result.Substring(4), resultCode);
                }

                if (offset != 0)
                {
                    Debug.WriteLine("Resuming at offset " + offset, "FtpClient");
                    input.Seek(offset, SeekOrigin.Begin);
                }

                Debug.WriteLine("Uploading file " + fileName + " to " + remotePath, "FtpClient");

                while ((bytes = input.Read(buffer, 0, buffer.Length)) > 0)
                {
                    cSocket.Send(buffer, bytes, 0);
                }
                input.Close();
                if (cSocket.Connected)
                {
                    cSocket.Close();
                }

                if (this.resultCode != 226 && this.resultCode != 250)
                {
                    this.readResponse();
                    if (this.resultCode != 226 && this.resultCode != 250)
                    {
                        throw new FtpException(this.result.Substring(4), resultCode);
                    }
                }
            }

        }

        /// <summary>
        /// Upload a directory and its file contents
        /// </summary>
        /// <param name="path"></param>
        /// <param name="recurse">Whether to recurse sub directories</param>
        public void UploadDirectory(string path, bool recurse)
        {
            this.UploadDirectory(path, recurse, "*.*");
        }

        /// <summary>
        /// Upload a directory and its file contents
        /// </summary>
        /// <param name="path"></param>
        /// <param name="recurse">Whether to recurse sub directories</param>
        /// <param name="mask">Only upload files of the given mask - everything is '*.*'</param>
        public void UploadDirectory(string path, bool recurse, string mask)
        {
            string[] dirs = path.Replace("/", @"\").Split('\\');
            string rootDir = dirs[dirs.Length - 1];

            // make the root dir if it doed not exist
            if (this.ExistDir(rootDir))
            {
                this.MakeDir(rootDir);
            }

            this.ChangeDir(rootDir);

            foreach (string file in Directory.GetFiles(path, mask))
            {
                this.Upload(file, true);
            }
            if (recurse)
            {
                foreach (string directory in Directory.GetDirectories(path))
                {
                    this.UploadDirectory(directory, recurse, mask);
                }
            }

            this.ChangeDir("..");
        }

        /// <summary>
        /// Upload a directory and its file contents
        /// </summary>
        /// <param name="path"></param>
        /// <param name="recurse">Whether to recurse sub directories</param>
        /// <param name="mask">Only upload files of the given mask - everything is '*.*'</param>
        public void UploadDirectory(string localPath, string serverPath, bool recurse, string mask)
        {
            string[] dirs = localPath.Replace("/", @"\").Split('\\');
            string rootDir = dirs[dirs.Length - 1];

            // make the root dir if it doed not exist
            if (this.ExistDir(serverPath))
            {
                this.MakeDir(serverPath);
            }

            this.ChangeDir(serverPath);

            foreach (string file in Directory.GetFiles(localPath, mask))
            {
                this.Upload(file, true);
            }
            if (recurse)
            {
                foreach (string directory in Directory.GetDirectories(localPath))
                {
                    string[] tempdirs = directory.Replace("/", @"\").Split('\\');
                    string temprootDir = tempdirs[tempdirs.Length - 1];
                    this.UploadDirectory(directory, serverPath + "\\" + temprootDir, recurse, mask);
                }
            }

            this.ChangeDir("..");
        }

        #endregion
        public bool ExistDir(string path)
        {
            bool result = false;
            if (!this.loggedin)
            {
                this.Login();
            }
            try
            {
                Socket cSocket = createDataSocket();
                this.sendCommand("CWD " + path);
                if (this.resultCode == 250)
                {
                    result = true;
                }
            }
            catch (Exception ex)
            {
                Logger.Log(string.Format("FsuFtpClient.ExistDir()ErrorMessage:{0}", ex.Message));
                result = false;
            }
            return result;
        }

        /// <summary>
        /// Delete a file from the remote FTP server.
        /// </summary>
        /// <param name="fileName"></param>
        public void DeleteFile(string fileName)
        {
            if (!this.loggedin)
            {
                this.Login();
            }


            this.sendCommand("DELE " + fileName);

            if (this.resultCode != 250)
            {
                throw new FtpException(this.result.Substring(4), resultCode);
            }


            Debug.WriteLine("Deleted file " + fileName, "FtpClient");
        }

        /// <summary>
        /// Rename a file on the remote FTP server.
        /// </summary>
        /// <param name="oldFileName"></param>
        /// <param name="newFileName"></param>
        /// <param name="overwrite">setting to false will throw exception if it exists</param>
        public void RenameFile(string oldFileName, string newFileName, bool overwrite)
        {
            if (!this.loggedin)
            {
                this.Login();
            }


            this.sendCommand("RNFR " + oldFileName);

            if (this.resultCode != 350)
            {
                throw new FtpException(this.result.Substring(4), resultCode);
            }


            if (!overwrite && this.GetFileList(newFileName).Length > 0)
            {
                throw new FtpException("File already exists", resultCode);
            }


            this.sendCommand("RNTO " + newFileName);

            if (this.resultCode != 250)
            {
                throw new FtpException(this.result.Substring(4), resultCode);
            }


            Debug.WriteLine("Renamed file " + oldFileName + " to " + newFileName, "FtpClient");
        }

        /// <summary>
        /// Create a directory on the remote FTP server.
        /// </summary>
        /// <param name="dirName"></param>
        public void MakeDir(string dirName)
        {
            if (!this.loggedin)
            {
                this.Login();
            }


            this.sendCommand("MKD " + dirName);

            if (this.resultCode != 250 && this.resultCode != 257)
            {
                throw new FtpException(this.result.Substring(4), resultCode);
            }


            Debug.WriteLine("Created directory " + dirName, "FtpClient");
        }

        /// <summary>
        /// Delete a directory on the remote FTP server.
        /// </summary>
        /// <param name="dirName"></param>
        public void RemoveDir(string dirName)
        {
            if (!this.loggedin)
            {
                this.Login();
            }


            this.sendCommand("RMD " + dirName);

            if (this.resultCode != 250)
            {
                throw new FtpException(this.result.Substring(4), resultCode);
            }


            Debug.WriteLine("Removed directory " + dirName, "FtpClient");
        }

        /// <summary>
        /// Change the current working directory on the remote FTP server.
        /// </summary>
        /// <param name="dirName"></param>
        public void ChangeDir(string dirName)
        {
            if (dirName == null || dirName.Equals(".") || dirName.Length == 0)
            {
                return;
            }

            if (!this.loggedin)
            {
                this.Login();
            }


            this.sendCommand("CWD " + dirName);

            if (this.resultCode != 250)
            {
                throw new FtpException(result.Substring(4), resultCode);
            }


            this.sendCommand("PWD");

            if (this.resultCode != 257)
            {
                throw new FtpException(result.Substring(4), resultCode);
            }


            // gonna have to do better than this....
            this.remotePath = this.message.Split('"')[1];

            Debug.WriteLine("Current directory is " + this.remotePath, "FtpClient");
        }

        /// <summary>
        /// 
        /// </summary>
        private void readResponse()
        {
            this.message = "";
            this.result = this.readLine();

            if (this.result.Length > 3)
            {
                var code = this.result.Substring(0, 3);
                int tempcode = 0;
                if (int.TryParse(code, out tempcode))
                {
                    this.resultCode = tempcode;
                }
            }
            else
                this.result = null;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        private string readLine()
        {
            StringBuilder sb = new StringBuilder();
            while (true)
            {
                this.bytes = clientSocket.Receive(this.buffer, this.buffer.Length, 0);
                //this.message +=ASCII.GetString( this.buffer, 0, this.bytes );
                sb.Append(Encoding.GetEncoding("gb2312").GetString(this.buffer, 0, this.bytes));

                if (this.bytes < this.buffer.Length)
                {
                    break;
                }
            }
            this.message = sb.ToString();
            string[] msg = this.message.Split('\n');

            try
            {
                if (msg.Length > 2)
                    this.message = msg[msg.Length - 2];

                else
                    this.message = msg[0];

                if (!message.Substring(3, 1).Equals(" "))
                    return readLine();
            }
            catch
            {
                this.message = msg[0];
            }


            if (this.message.Length > 4 && !this.message.Substring(3, 1).Equals(" "))
            {
                return this.readLine();
            }


            if (this.verboseDebugging)
            {
                for (int i = 0; i < msg.Length - 1; i++)
                {
                    Debug.Write(msg[i], "FtpClient");
                }
            }

            return message;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="command"></param>
        private void sendCommand(String command)
        {
            if (this.verboseDebugging)
            {
                Debug.WriteLine(command, "FtpClient");
            }


            //Byte[] cmdBytes = Encoding.ASCII.GetBytes( ( command + "\r\n" ).ToCharArray() );
            Byte[] cmdBytes = Encoding.GetEncoding("gb2312").GetBytes((command + "\r\n").ToCharArray());
            clientSocket.Send(cmdBytes, cmdBytes.Length, 0);
            this.readResponse();
        }

        /// <summary>
        /// when doing data transfers, we need to open another socket for it.
        /// </summary>
        /// <returns>Connected socket</returns>
        private Socket createDataSocket()
        {
            //editby xhy 20181101
            if (addressFamily == AddressFamily.InterNetwork)
            {
                this.sendCommand("PASV");
                if (this.resultCode != 227)
                {
                    string strLog = string.Format("地址:{0},模式:PASV ,结果代码:{1}", this.server, resultCode);
                    Logger.Log(strLog);
                    throw new FtpException(this.result.Substring(4), resultCode);
                }
            }
            else if (addressFamily == AddressFamily.InterNetworkV6)
            {
                this.sendCommand("EPSV");
                if (this.resultCode != 229)
                {
                    string strLog = string.Format("地址:{0},模式:EPSV ,结果代码:{1}", this.server, resultCode);
                    Logger.Log(strLog);
                    throw new FtpException(this.result.Substring(4), resultCode);
                }
            }

            int index1 = this.result.IndexOf('(');
            int index2 = this.result.IndexOf(')');
            int port = 0;

            if (this.resultCode == 227)
            {
                string ipData = this.result.Substring(index1 + 1, index2 - index1 - 1);

                int[] parts = new int[6];

                int len = ipData.Length;
                int partCount = 0;
                string buf = "";

                for (int i = 0; i < len && partCount <= 6; i++)
                {
                    char ch = char.Parse(ipData.Substring(i, 1));

                    if (char.IsDigit(ch))
                        buf += ch;

                    else if (ch != ',' && ch != '.')
                        throw new FtpException("Malformed PASV result: " + result, resultCode);

                    if ((ch == ',' || ch == '.') || i + 1 == len)
                    {
                        try
                        {
                            parts[partCount++] = int.Parse(buf);
                            buf = "";
                        }
                        catch (Exception ex)
                        {
                            throw new FtpException("Malformed PASV result (not supported?): " + this.result, ex, resultCode);
                        }
                    }
                }

                //string ipAddress = parts[0] + "." + parts[1] + "." + parts[2] + "." + parts[3];
                port = (parts[4] << 8) + parts[5];
            }
            else if (this.resultCode == 229)
            {
                string strPort = this.result.Substring(index1 + 1, index2 - index1 - 1);
                strPort = strPort.TrimStart('|');
                strPort = strPort.TrimEnd('|');
                port = int.Parse(strPort);
            }

            Socket socket = null;
            IPEndPoint ep = null;

            try
            {
                //editby xhy 20181101
                //socket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
                socket = new Socket(addressFamily, SocketType.Stream, ProtocolType.Tcp);
                socket.ReceiveTimeout = socketTimeout;//receive timeout
                socket.SendTimeout = socketTimeout;//send timeout
                ep = new IPEndPoint(GetIPAddress(this.server), port);
                socket.Connect(ep);
            }
            catch (Exception ex)
            {
                // doubtfull....
                if (socket != null && socket.Connected)
                {
                    socket.Close();
                }
                throw new FtpException("Can't connect to remote server", ex, resultCode);
            }

            return socket;
        }

        private IPAddress GetIPAddress(string ip)
        {
            return IPAddress.Parse(ip);
        }

        /// <summary>
        /// Always release those sockets.
        /// </summary>
        private void cleanup()
        {
            if (this.clientSocket != null)
            {
                this.clientSocket.Close();
                this.clientSocket = null;
            }
            this.loggedin = false;
        }

        /// <summary>
        /// Destuctor
        /// </summary>
        ~FsuFtpClient()
        {
            this.cleanup();
        }

    }
}
