﻿using DM.TestOrder.DAL;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web.Http;
using testorder.dal;



namespace DM.TestOrder.Controllers {
        
    public class GeoController : BaseApiController{

        //http://localhost/orderapi/Geo
        public HttpResponseMessage Get(decimal latitude, decimal longitude) {

            var rtnMsg = WoGeoDal.Validate(latitude, longitude);


            Debug.WriteLine(latitude + "/t" + latitude);

            var rtn = new {
                errormsg = rtnMsg
            };
            var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(rtn);

            return new HttpResponseMessage() {
                Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            };

        }
    }
}
