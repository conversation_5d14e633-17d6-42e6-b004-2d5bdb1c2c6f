﻿namespace ENPC.Kolo.Entity.NBIOT
{
    public class TDevConf
    {
        public TDevConf() { }

        public TDevConf(string sId, float? threshold, EnumLevel alarmLevel)
        {
            SID = sId;
            Threshold = threshold;
            AlarmLevel = alarmLevel;
        }

        public string SID { get; set; }

        public float? Threshold { get; set; }

        public EnumLevel AlarmLevel { get; set; }

        public override string ToString()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
    }
}