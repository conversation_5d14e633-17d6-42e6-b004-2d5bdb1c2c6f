﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Odbc;
using System.Linq;
using System.Text;
//using Carrier.Kolo.SSTool;
using Carrier.BDSTool;

using ENPC.Kolo.Entity.B_CMCC;
using Common.Logging.Pro;

using BDSTool.Entity.B;
using BDSTool.BLL.S2;
using BDSTool.BLL.B;
using BDSTool.Utility;
using BDSTool.Entity;

using System.Xml;

namespace Carrier.BDSTool
{        
    public partial class BDSHelperCMCC
    {
        //1)	输入监控点存储规则，包括Device ID、Type、ID、SignalNumber、AbsoluteVal、 RelativeVal、 StorageInterval。

        //2)	功能描述
                //1. 基于输入的Device ID、ID、SignalNumber， 获取SiteWeb配置中的设备ID及信号ID
                //2. 基于输入的AbsoluteVal、 RelativeVal、 StorageInterval, 更新SiteWeb配置中的存储策略

        public static bool OnUpdateStoreRule(string FSUID,string DeviceID, EnumType sigType, string ID, string SignalNumber,
            float? absoluteVal, float? relativeVal, long? storageInterval) {
            try
            {
                LoggerBDSTool.InfoPartHeader();
                LoggerBDSTool.InfoFormat("OnUpdateStoreRule(); fsuId={0},deviceID={1},id={2},signalNumber={3}", 
                    FSUID, DeviceID, ID, SignalNumber);
                //-------------------------------------------------------------------------------------------------
                int StationId=0,  EquipmentId=0,   SignalId=0;
                if (!ConvertBID2SecId(FSUID, DeviceID, ID, SignalNumber, ref  StationId, ref  EquipmentId, ref   SignalId)) {
                    logger.WarnFormat("OnUpdateStoreRule(); ConvertBID2SecId failed; fsuId={0},deviceID={1},id={2},signalNumber={3}",
                    FSUID, DeviceID, ID, SignalNumber);
                    return false;
                }
                //-------------------------------------------------------------------------------------------------
                return EquipmentBiz.OnUpdateStoreRule(StationId, EquipmentId, SignalId, absoluteVal, relativeVal, storageInterval);
            }

            catch (Exception ex) {
                LoggerBDSTool.ErrorFormat("OnUpdateStoreRule(); fsuId={0},deviceID={1},id={2},signalNumber={3}; Error={4}",
                    FSUID, DeviceID, ID, SignalNumber, ex.Message);
                LoggerBDSTool.Error(ex.StackTrace);
                LoggerBDSTool.InfoPartEnd();
                return false;
            }

        }
    }
}
