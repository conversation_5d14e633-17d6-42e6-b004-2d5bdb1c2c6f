﻿using BDSTool.DBUtility;
using BDSTool.Entity.B;
using Common.Logging.Pro;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.Entity.DRHelper
{
    public class DevConfFromDBHelper
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static TDevConfFromDB FromDataRow(DataRow row) {
            var entity = new TDevConfFromDB();
            try {
                entity.device.DeviceID = row["DeviceID"].ToString();
                entity.device.DeviceName = row["DeviceName"].ToString();
                entity.device.RoomName = row["RoomName"].ToString();
                entity.device.DeviceType =SHelper.ToIntNullable( row["DeviceType"]);
                entity.device.DeviceSubType = SHelper.ToIntNullable(row["DeviceSubType"]);
                entity.device.Model = row["Model"].ToString();
                entity.device.Brand = row["Brand"].ToString();

                entity.device.RatedCapacity = SHelper.ToFloat(row["RatedCapacity"]);

                entity.device.Version = row["Version"].ToString();

                entity.device.BeginRunTime = SHelper.ToDateTime(row["BeginRunTime"]);

                entity.device.DevDescribe = row["DevDescribe"].ToString();
                //------------------------------------------------
                entity.StationId = SHelper.ToInt(row["StationId"]);
                entity.EquipmentId = SHelper.ToInt(row["EquipmentId"]);

            }
            catch (Exception ex) {
                logger.ErrorFormat("TBL_EquipmentCMCC.FromDataRow();error={0}", ex.Message);
                logger.Error(ex.StackTrace);
                return null;
            }
            return entity;
        }
    }
}
