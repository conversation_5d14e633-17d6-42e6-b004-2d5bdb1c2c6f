﻿



using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Text;
using testorder.dal;
using DM.TestOrder.DAL.Helper;

using DM.TestOrder.Model;
using DM.TestOrder.Entity;
using Delivery.DAL;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace DM.TestOrder.DAL {
    public class WoFileUploadRecDal
    {
        //@OrderId 		int, --工单号，作为目录名
        //@EquipmentId	int,
        //@FType			NVARCHAR(255),
        //@FSaveName		NVARCHAR(255),  --文件名, 类型+设备ID,示例: PIC-755000015.ZIP	
        //------------------------------------	
        //@Uri				NVARCHAR(1024),
        //------------------------------------			
        //@FOriginalName	NVARCHAR(255),
        //@FSize			int	  	   --文件大小
        public static void AddOrUpdateOne(WO_FileUploadRec rec) {
            //var sql = string.Format("WO_FileUploadRec_AddOrUpdate {0},{1},{2},{3},{4},{5},{6},{7}",
            //rec.OrderId,//		int, --工单号，作为目录名
            //rec.EquipmentId,
            //SHelper.GetPara(rec.FType),
            //SHelper.GetPara(rec.FSaveName),//			NVARCHAR(255),  --文件名, 类型+设备ID,示例: PIC-755000015.ZIP	
            //SHelper.GetPara(rec.Uri),//				NVARCHAR(1024),
            //SHelper.GetPara(rec.FOriginalName),//	NVARCHAR(255),
            //rec.FSize,//			int	  	   --文件大小
            //rec.UserId);
            //DBHelper.ExecuteNonQuery(sql);
            if(CommonUtils.IsNoProcedure)
            {
                 WoCommonService.Instance.WO_FileUploadRec_AddOrUpdate(rec.OrderId, rec.EquipmentId, rec.FType, rec.FSaveName, rec.Uri, rec.FOriginalName, rec.FSize, rec.UserId);
                 return;
            }
            new ExecuteSql().ExecuteStoredProcedureNoQuery("WO_FileUploadRec_AddOrUpdate", new QueryParameter[] {
                new QueryParameter("OrderId",DataType.Int,rec.OrderId.ToString()),
                new QueryParameter("EquipmentId",DataType.Int,rec.EquipmentId.ToString()),
                new QueryParameter("FType",DataType.String,rec.FType),
                new QueryParameter("FSaveName",DataType.String,rec.FSaveName),
                new QueryParameter("Uri",DataType.String,rec.Uri),
                new QueryParameter("FOriginalName",DataType.String,rec.FOriginalName),
                new QueryParameter("FSize",DataType.Int,rec.FSize.ToString()),
                new QueryParameter("UserId",DataType.Int,rec.UserId.ToString())
            });
            
        }
     }


}

