﻿



using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using DM.TestOrder.DAL.Helper;
using DM.TestOrder.Common.DBA;



namespace DM.TestOrder.DAL {

    public class SysConfigDal
    {

        public static void SetParaOfNeedUpload(int NeedUpload) {
            var sql = string.Format("Update WO_SysConfig set ValueInt={0} where ConfigId=2",NeedUpload);
            DBHelper.ExecuteNonQuery(sql);
        }
    }
}
