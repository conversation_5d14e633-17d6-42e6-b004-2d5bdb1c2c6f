﻿

using System;
using System.Configuration;
using Delivery.Common;

namespace DM.TestOrder.Common.DBA {
    public class DbConfigPara {
        public string ProviderName;
        public string ConnectionString;
        public int DataBaseTimeout = 60;        //数据库超时时间，单位：秒
    }
    public class DbConfig {


        public string ConnectionString;
        public int DataBaseTimeout = 60;        //数据库超时时间，单位：秒
        public string ProviderName;

        public DbConfig() {
            try {

                var conn = new ConnectionStringSettings("Default", ConfigHelper.GetSection("Database:Default:ConnectionString").Value, ConfigHelper.GetSection("Database:Default:ProviderName").Value);
                //var conn = ConfigurationManager.ConnectionStrings["Default"];
                if (conn == null) {
                    //throw new Exception("配置文件connectionStrings中无数据库连接配置");
                    MyLogger.Info("配置文件connectionStrings中无数据库连接配置");
                    return;
                }

                //ConnectionSettings.ConnectionString: Data Source='10.169.42.171';Database='test'; UID='sa'; PWD=''
                var decryConn = CryptoHelper.DecryptConnectionString(conn.ConnectionString);            //设定Connection对像的连接字符串       

                ConnectionString = decryConn;
                ProviderName = conn.ProviderName;

                //string key = GetAppSetting("DataBaseTimeout");
                // string key = ConfigurationManager.AppSettings.Get("DataBaseTimeout");
                string key = null;
                if (string.IsNullOrEmpty(key)) {
                    //默认60秒
                    DataBaseTimeout = 60;
                }
                else if (!int.TryParse(key, out DataBaseTimeout)) {
                    //默认60秒
                    DataBaseTimeout = 60;
                }
                else if (DataBaseTimeout < 60) {
                    //默认60秒
                    DataBaseTimeout = 60;
                }
                else if (DataBaseTimeout > 300) {
                    //默认最大300秒
                    DataBaseTimeout = 300;
                }

                MyLogger.InfoFormat("DbConfig(); ConnectionString={0}; DataBaseTimeout={1}", ConnectionString, DataBaseTimeout);

                //------------------------------------------------------------------------------------------------------
            }
            catch (Exception e) {
                MyLogger.ErrorFormat("DbConfig(); ex={0}", e.Message);
                MyLogger.Error(e.StackTrace);
            }
        }


    }
}
