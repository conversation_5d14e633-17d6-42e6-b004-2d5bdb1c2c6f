﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Odbc;
using System.Linq;
using System.Text;
//using Carrier.Kolo.SSTool;
using Carrier.BDSTool;

using ENPC.Kolo.Entity.B_CMCC;
using Common.Logging.Pro;

using BDSTool.Entity.B;
using BDSTool.BLL.S2;
using BDSTool.BLL.B;
using BDSTool.Utility;
using BDSTool.Entity;

using System.Xml;

namespace Carrier.BDSTool
{        
    public partial class BDSHelperCMCC
    {
        public static bool OnImportFSUConfig(XmlDocument ContentXML, string FSUID, List<TDevConf> listDevNew, ref string errmsg) {
            try
            {
                LoggerBDSTool.InfoPartHeader();
                LoggerBDSTool.InfoFormat("OnImportFSUConfig();FSU={0}", FSUID);
                //-------------------------------------------------------------------------------------------------
                var fsuBiz = new FsuBiz(ContentXML, FSUID, listDevNew);

                if (fsuBiz.OnImportConfig()) {
                    LoggerBDSTool.InfoPartEnd();
                    return true;
                }
                else {
                    errmsg = fsuBiz.ErrorMsg;
                    if (string.IsNullOrEmpty(errmsg))
                        errmsg = "导入FSU配置失败";
                    LoggerBDSTool.InfoPartEnd();
                    return false;
                }
            }

            catch (Exception ex) {
                LoggerBDSTool.ErrorFormat("OnImportFSUConfig();FSUID={0},Error={1}", FSUID, ex.Message);
                LoggerBDSTool.Error(ex.StackTrace);
                LoggerBDSTool.InfoPartEnd();
                return false;
            }

        }

        public static bool OnReceiveConfigData(SendDevConfData data) {
            try {
                if (!MonitorUnitCMCCBiz.Exist(data.FSUID)) {
                    LoggerBDSTool.WarnFormat("OnReceiveConfigData(); failed to FindByFSUID;FSU={0}", data.FSUID);
                    return false;
                }

                LoggerBDSTool.InfoPartHeader();
                bool rtn = new FsuBiz().OnReceiveConfigData(data);
                LoggerBDSTool.InfoFormat("OnReceiveConfigData();FSU={0}, Rtn={1}", data.FSUID,rtn);
                LoggerBDSTool.InfoPartEnd();
                return rtn;
            }
            catch (Exception ex) {
                LoggerBDSTool.ErrorFormat("OnReceiveConfigData();FSUID={0};Error={1}", data.FSUID, ex.Message);
                LoggerBDSTool.ErrorFormat("OnReceiveConfigData();Stack={0}", ex.StackTrace);
                return false;
            }
        }

    }
}
