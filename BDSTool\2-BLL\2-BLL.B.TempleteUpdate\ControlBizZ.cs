﻿using Common.Logging.Pro;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;


using BDSTool.Entity.S2;
using BDSTool.DBUtility;
using BDSTool.DBUtility.Common;
using BDSTool.Entity.B;
using ENPC.Kolo.Entity.B_CMCC;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace BDSTool.BLL.S2
{
    public class ControlBizZ
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static bool XAddOne(TBL_Control control, int dispIndex) {
            if (XSaveEntity(control) == false)
                return false;

            if (control.CommandType == 2)//开关量
                if (ControlBiz.SetCommandMeanings(control) == false)
                    return false;

            return true;
            
        }

        public static bool DeleteList(int EquipmentTemplateId, List<TSignalEx> controls) {
            try {
                if (controls.Count == 0)
                    return true;

                StringBuilder sb = new StringBuilder();
                DbConfigPara para= DBHelper.GetDbConfig();
                foreach (var ctrl in controls) {
                    string sql;
                    if (CommonUtils.IsNoProcedure)
                    {
                        var tempSql = Public_ExecuteSqlService.Instance.NoStore_TBLControl_GetSql();
                        sql = string.Format(tempSql, EquipmentTemplateId, ctrl.TSignalId);
                    }
                    else {
                        sql = string.Format("delete from TBL_Control where EquipmentTemplateId={0} and ControlId={1}", EquipmentTemplateId, ctrl.TSignalId);
                    }
                    
                    sb.AppendLine(DBHelper.GetExecSql(sql));
                }
                foreach (var ctrl in controls.Where(o => o.tsig.Type == EnumType.DO))
                {
                    string sql;
                    if (CommonUtils.IsNoProcedure)
                    {
                        var tempSql = Public_ExecuteSqlService.Instance.NoStore_TBLControlMeanings_GetSql();
                        sql = string.Format(tempSql, EquipmentTemplateId, ctrl.TSignalId);
                    }
                    else
                    {
                        sql = string.Format("delete from TBL_ControlMeanings where EquipmentTemplateId={0} and ControlId={1}", EquipmentTemplateId, ctrl.TSignalId);
                    }
                    sb.AppendLine(DBHelper.GetExecSql(sql));

                }

                var sqlAll = sb.ToString();
                DBHelper.ExecuteNonQuery(sqlAll);

            }
            catch (Exception ex) {
                logger.ErrorFormat("ControlBiz.DeleteList();EquipmentTemplateId={0},TSignalCount={1};Error={2}", 
                    EquipmentTemplateId, controls.Count, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }

        public static bool XSaveEntity(TBL_Control c) {
            try {
                if (CommonUtils.IsNoProcedure)
                {
                    Public_ExecuteSqlService.Instance.NoStore_InsertRowIntoTBLControl(c.EquipmentTemplateId, c.ControlId, SHelper.GetPara(c.ControlName), c.ControlCategory, SHelper.GetPara(c.CmdToken),
                SHelper.GetPara(c.BaseTypeId), c.ControlSeverity, SHelper.GetPara(c.SignalId), SHelper.GetPara(c.TimeOut), SHelper.GetPara(c.Retry), SHelper.GetPara(c.Description), SHelper.GetPara(c.Enable), SHelper.GetPara(c.Visible),
                c.DisplayIndex, c.CommandType, SHelper.GetPara(c.ControlType), SHelper.GetPara(c.DataType), c.MaxValue, c.MinValue, SHelper.GetPara(c.DefaultValue), c.ModuleNo);
                }
                else
                {
                    var sql = string.Format(@"INSERT INTO TBL_Control
                    (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, 
                    BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, 
                    DisplayIndex, CommandType, ControlType, DataType, MaxValue, MinValue, DefaultValue, ModuleNo)
                    values (
                    {0},{1},{2},{3},{4},
                    {5},{6},{7},{8},{9},
                    {10},{11},{12},{13},{14},
                    {15},{16},{17},{18},{19},
                    {20}
                    )",
                c.EquipmentTemplateId, c.ControlId, SHelper.GetPara(c.ControlName), c.ControlCategory, SHelper.GetPara(c.CmdToken),
                SHelper.GetPara(c.BaseTypeId), c.ControlSeverity, SHelper.GetPara(c.SignalId), SHelper.GetPara(c.TimeOut), SHelper.GetPara(c.Retry), SHelper.GetPara(c.Description), SHelper.GetPara(c.Enable), SHelper.GetPara(c.Visible),
                c.DisplayIndex, c.CommandType, SHelper.GetPara(c.ControlType), SHelper.GetPara(c.DataType), c.MaxValue, c.MinValue, SHelper.GetPara(c.DefaultValue), c.ModuleNo);

                    DBHelper.ExecuteNonQuery(sql);
                }
            }
            catch (Exception ex) {
                logger.ErrorFormat("XSaveEntity();EquipmentTemplateId={0},ControlName={1};Error={2}", c.EquipmentTemplateId, c.ControlName, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;

        }




    }
}
