﻿@page
@model Delivery.Pages.CMCC.ReviewManagementModel
@{
}

@section Scripts {
    <script type="text/javascript" src="~/Scripts/lib/jquery-1.8.0.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="~/Scripts/common/common.js"></script>
    <script type="text/javascript" src="~/Scripts/common/ie8.js"></script>
    <script type="text/javascript" src="~/CMCC/scripts/ReviewManagement.js"></script>
    
}

<div class="mask">
    <div style="position: relative; top: 50%; left: 50%">
        <div class="loading">
            正在加载，请等待...
        </div>
    </div>
</div>
<table style="margin: 0; padding: 0;">
    <tr>
        <td style="text-align: left; white-space: nowrap">申请开始日期:</td>
        <td>
            <input id="txtApplyTimeq" data-options="validType:['date[\'yyyy-MM-dd hh:mm:ss\']', 'less[\'#txtApplyTimeq2\']'],onChange:function(){$('#txtApplyTimeq2').datetimebox('validate')}" class="easyui-datetimebox" style="width: 140px;" />
        </td>
        <td style="text-align: left; white-space: nowrap">申请截止日期:</td>
        <td>
            <input id="txtApplyTimeq2" data-options="validType:['date[\'yyyy-MM-dd hh:mm:ss\']','greater[\'#txtApplyTimeq\']'],onChange:function(){$('#txtApplyTimeq').datetimebox('validate')}" class="easyui-datetimebox" style="width: 140px;" />
        </td>
        <td>
            <input type="button" id="btn_seach" value="查询" class="commonButton" style="width: 40pt;" />
        </td>
        <td><input type="button" id="btn_submitReview" value="提交审核" class="commonButton" style="width: 60pt;" /></td>
    </tr>
</table>

<div class="easyui-accordion" style="width: 100%;" data-options="multiple: true">
    <div title="站址管理" style="width: 100%; padding: 5px;">
        <table id="dg_StationReview"></table>
    </div>
</div>
<div class="easyui-accordion" style="width: 100%; margin-top: 10px;" data-options="multiple: true">
    <div title="机房管理" style="width: 100%; padding: 5px;">
        <table id="dg_HouseReview"></table>
    </div>
</div>
<div class="easyui-accordion" style="width: 100%; margin-top: 10px;" data-options="multiple: true">
    <div title="FSU管理" style="width: 100%; padding: 5px;">
        <table id="dg_FsuReview"></table>
    </div>
</div>


<div id="BackDialog" title="新增工作流" style="width: 550px; height: 250px;" class="easyui-dialog"
     data-options="modal:true,closed:true,closable:true">
    <table id="Table1">
        <tr>
            
            <td style="width: 150px; height: 45px; text-align: right;">工作流名称:</td>
            <td>
                <input type='text' placeholder="请输入工作流名称" data-options="prompt:'请输入工作流名称'" class="easyui-textbox" id="txtWorkflowNameq" style="width: 140px;" />
            </td>
        </tr>
        <tr>
            <td style="width: 150px; height: 45px; text-align: right;">备注:</td>
            <td>
                <textarea name="TxtRejectReason" id="TxtRejectReason" class="easyui-textbox" data-options="prompt:'备注'" style="width: 300px;"></textarea>
            </td>
            <td></td>
        </tr>
        <tr>
            <td style="width: 50px;"></td>
            <td>
                <input type="button" id="btn_AppBackRe" value="提交" class="commonButton" style="width: 40pt;" />
            </td>
            <td></td>
        </tr>
    </table>
</div>
