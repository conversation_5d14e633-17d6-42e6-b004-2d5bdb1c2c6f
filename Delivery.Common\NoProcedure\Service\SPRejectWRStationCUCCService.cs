﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;

namespace Delivery.Common.NoProcedure.Service
{
    public class SPRejectWRStationCUCCService
    {
        private static SPRejectWRStationCUCCService _instance = null;

        public static SPRejectWRStationCUCCService Instance
        {
            get
            {
                if (_instance == null) _instance = new SPRejectWRStationCUCCService();
                return _instance;
            }
        }
        public string RejectWRStationCUCC(string swsStationId, string StationCode, string RejectCause)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);

                Dictionary<string, object> realParams = new Dictionary<string, object>();
                realParams.Add("WRStationId", swsStationId == null ? DBNull.Value : CommonUtils.GetNullableValue(int.Parse(swsStationId)));

                try
                {
                    if (swsStationId == null || swsStationId == "")
                    {
                        return "-1";
                    }

                    Object resultSql = execHelper.ExecuteScalar("SP_RejectWRStationCUCC_1", realParams);
                    if (resultSql != null && !resultSql.ToString().Equals(""))
                    {
                        if (Convert.ToInt32(resultSql) == 3)
                        {
                            return "-2";
                        }
                    }

                    realParams.Add("StationCode", StationCode);
                    realParams.Add("RejectCause", RejectCause);

                    execHelper.ExecuteNonQuery("SP_RejectWRStationCUCC_2", realParams);
                    return "1";
                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    return "0";
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

    }
}
