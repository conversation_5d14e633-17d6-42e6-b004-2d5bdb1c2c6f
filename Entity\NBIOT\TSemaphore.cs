﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 信号量值的结构
    /// </summary>
    public class TSemaphore : BStruct
    {  
        /// <summary>
        /// 监控点ID
        /// </summary>
        [JsonIgnore]
        public string ID { get; set; }

        /// <summary>
        /// 同设备同类监控点顺序号
        /// </summary>
        [JsonIgnore]
        public string SignalNumber { get; set; }

        /// <summary>
        /// 实测值
        /// </summary>
        [JsonIgnore]
        public float? MeasuredVal { get; set; }

        /// <summary>
        /// 设置值
        /// </summary>
        [JsonIgnore]
        public float? SetupVal { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [JsonIgnore]
        public EnumState Status { get; set; }

        /// <summary>
        /// 时间
        /// </summary>
        [JsonIgnore]
        public DateTime? Time { get; set; }

        /// <summary>
        /// 数据描述
        /// </summary>
        [JsonIgnore]
        public string Description { get; set; }

        [JsonIgnore]
        public int MySignalId { get; set; }

        public TSemaphore() 
        {
        }

        /// <summary>
        /// 信号量的值的结构
        /// </summary>
        /// <param name="enumType">监控系统的数据种类</param>
        /// <param name="id">监控点ID</param>
        /// <param name="measureValue">实测值</param>
        /// <param name="setupValue">设置值</param>
        /// <param name="enumState">状态</param>
        /// <param name="time">时间</param>
        public TSemaphore(EnumType enumType, string id, string signalNumber, float? measureValue, float? setupValue, EnumState enumState, DateTime? time)
        {
            Type = enumType;
            ID = id;
            SignalNumber = signalNumber;
            MeasuredVal = measureValue;
            SetupVal = setupValue;
            Status = enumState;
            Time = time;
        }

        public TSemaphore(EnumType enumType, string id, string signalNumber, float? measureValue, float? setupValue, EnumState enumState, DateTime? time, string description)
        {
            Type = enumType;
            ID = id;
            SignalNumber = signalNumber;
            MeasuredVal = measureValue;
            SetupVal = setupValue;
            Status = enumState;
            Time = time;
            Description = description;
        }

        public override string ToString()
        {
            return String.Format("[{0}, {1}, {2}, {3:#0.00}, {4:#0.00}, {5}, {6}]",
               Type, ID, SignalNumber, MeasuredVal, SetupVal, Status, Time.HasValue ? Convert.ToDateTime(Time).ToString("yyyy-MM-dd HH:mm:ss") : "");
        }
    }
}