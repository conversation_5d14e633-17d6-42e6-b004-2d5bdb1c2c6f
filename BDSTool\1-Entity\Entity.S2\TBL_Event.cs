
namespace BDSTool.Entity.S2
{
    using System;
    using System.Collections.Generic;
    
    public partial class TBL_Event
    {
        public int EquipmentTemplateId { get; set; }
        public int EventId { get; set; }
        public string EventName { get; set; }
        public int StartType { get; set; }
        public int EndType { get; set; }
        public string StartExpression { get; set; }
        public string SuppressExpression { get; set; }
        public int EventCategory { get; set; }
        public Nullable<int> SignalId { get; set; }
        public bool Enable { get; set; }
        public bool Visible { get; set; }
        public string Description { get; set; }
        public Nullable<int> DisplayIndex { get; set; }
        public int ModuleNo { get; set; }
    }
}
