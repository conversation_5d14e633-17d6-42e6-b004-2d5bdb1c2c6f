﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.NBIOT
{
    class CustomSringConverter : CustomCreationConverter<string>
    {
        public override void Write<PERSON>son(JsonWriter writer, object value, JsonSerializer serializer)
        {
            if (value == null)
            {
                writer.WriteNull();
            }
            else if (string.Compare(value.GetType().Name,"Int16",true) == 0)
            {
                writer.WriteValue(value.ToString());
            }
            else
            {
                writer.WriteValue(((int)value).ToString());
            }
        }

        public override bool CanWrite { get { return true; } }

        public override string Create(Type objectType)
        {
            return string.Empty;
        }
    }

    
}
