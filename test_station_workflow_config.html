<!DOCTYPE html>
<html>
<head>
    <title>站址管理 - WorkFlow配置功能测试</title>
    <link rel="stylesheet" type="text/css" href="https://www.jeasyui.com/easyui/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css" href="https://www.jeasyui.com/easyui/themes/icon.css">
    <script type="text/javascript" src="https://code.jquery.com/jquery-1.8.0.min.js"></script>
    <script type="text/javascript" src="https://www.jeasyui.com/easyui/jquery.easyui.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .config-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; background-color: #f9f9f9; }
        .status { font-weight: bold; padding: 5px 10px; border-radius: 3px; }
        .enabled { background-color: #d4edda; color: #155724; }
        .disabled { background-color: #f8d7da; color: #721c24; }
        .demo-form { margin: 10px 0; }
        .demo-form table { width: 100%; }
        .demo-form td { padding: 5px 10px; }
    </style>
</head>
<body>
    <h1>站址管理 - WorkFlow配置功能测试</h1>
    
    <div class="config-section">
        <h2>功能说明</h2>
        <p>本功能根据 <code>appsettings.json</code> 中的 <code>WorkFlow</code> 属性来决定站址名称的输入方式：</p>
        <ul>
            <li><strong>WorkFlow = false：</strong>显示站址名称输入框，用户手动输入站址名称</li>
            <li><strong>WorkFlow = true：</strong>显示站址下拉选择框，从TIr_EomsStation表中选择预存的站址</li>
        </ul>
    </div>

    <div class="config-section">
        <h3>当前配置状态</h3>
        <p>WorkFlow状态: <span id="workflowStatus" class="status">检测中...</span></p>
        <button onclick="toggleWorkFlow()">切换WorkFlow状态（模拟）</button>
        <button onclick="refreshConfig()">重新检测配置</button>
    </div>

    <div class="test-section">
        <h3>站址名称输入测试</h3>
        <div class="demo-form">
            <table>
                <tr>
                    <td style="text-align: left; white-space: nowrap; width: 100px;">站址名称:</td>
                    <td>
                        <input type='text' class="easyui-textbox" data-options="prompt:'请输入站址名称',required:true" id="txtStationName" style="width: 200px; display: none;" />
                        <select id="cboAssetStation" class="easyui-combobox" data-options="valueField:'ItemId',textField:'ItemValue',prompt:'请选择站址',required:true" style="width: 200px; display: none;"></select>
                    </td>
                </tr>
            </table>
        </div>
        <div style="margin-top: 10px;">
            <button onclick="validateStationName()">校验站址名称</button>
            <button onclick="getStationNameValue()">获取站址名称值</button>
            <button onclick="clearStationName()">清空站址名称</button>
        </div>
        <div id="validationResult" style="margin-top: 10px;"></div>
    </div>

    <div class="test-section">
        <h3>模拟资产站址数据</h3>
        <div id="assetStationList"></div>
    </div>

    <div class="test-section">
        <h3>功能对比</h3>
        <table border="1" style="width: 100%; border-collapse: collapse;">
            <tr>
                <th style="padding: 8px;">配置状态</th>
                <th style="padding: 8px;">显示控件</th>
                <th style="padding: 8px;">数据来源</th>
                <th style="padding: 8px;">传值方式</th>
            </tr>
            <tr>
                <td style="padding: 8px;">WorkFlow = false</td>
                <td style="padding: 8px;">文本输入框</td>
                <td style="padding: 8px;">用户手动输入</td>
                <td style="padding: 8px;">直接获取输入框值</td>
            </tr>
            <tr>
                <td style="padding: 8px;">WorkFlow = true</td>
                <td style="padding: 8px;">下拉选择框</td>
                <td style="padding: 8px;">TIr_EomsStation表</td>
                <td style="padding: 8px;">获取选中项的文本值</td>
            </tr>
        </table>
    </div>

    <script>
        // 模拟配置状态
        var isWorkFlowEnabled = false;
        
        // 模拟资产站址数据
        var assetStationData = [
            { ItemId: '1001', ItemValue: '北京移动大厦站址' },
            { ItemId: '1002', ItemValue: '上海陆家嘴站址' },
            { ItemId: '1003', ItemValue: '广州天河站址' },
            { ItemId: '1004', ItemValue: '深圳福田站址' },
            { ItemId: '1005', ItemValue: '杭州西湖站址' }
        ];

        // 获取应用配置信息（模拟）
        function getAppSettings() {
            // 模拟AJAX调用
            console.log('模拟获取应用配置...');
            // isWorkFlowEnabled 在toggleWorkFlow中设置
        }

        // 获取资产站址列表（模拟）
        function getAssetStationList() {
            if (!isWorkFlowEnabled) {
                return;
            }

            console.log('模拟获取资产站址列表...');

            // 模拟AJAX调用和JSON解析过程
            try {
                // 模拟后端返回的JSON字符串
                var jsonString = JSON.stringify(assetStationData);
                console.log('模拟后端返回的JSON字符串：', jsonString);

                // 模拟前端解析过程
                var parsedData = JSON.parse(jsonString);
                console.log('解析后的数据：', parsedData);

                $('#cboAssetStation').combobox('loadData', parsedData);
            } catch (e) {
                console.error('JSON解析失败：', e);
                $('#cboAssetStation').combobox('loadData', []);
            }
        }

        // 动态更新站址名称控件显示
        function updateStationNameControls() {
            // 等待EasyUI控件完全初始化
            setTimeout(function() {
                if (isWorkFlowEnabled) {
                    // WorkFlow启用时，显示下拉选择框，隐藏输入框
                    $('#txtStationName').textbox('disable').hide();
                    $('#cboAssetStation').combobox('enable').show();
                    console.log('WorkFlow模式：显示下拉选择框');
                } else {
                    // WorkFlow未启用时，显示输入框，隐藏下拉选择框
                    $('#cboAssetStation').combobox('disable').hide();
                    $('#txtStationName').textbox('enable').show();
                    console.log('普通模式：显示输入框');
                }
            }, 100);
        }

        // 更新状态显示
        function updateStatusDisplay() {
            var statusElement = document.getElementById('workflowStatus');
            if (isWorkFlowEnabled) {
                statusElement.textContent = '已启用';
                statusElement.className = 'status enabled';
            } else {
                statusElement.textContent = '未启用';
                statusElement.className = 'status disabled';
            }
        }

        // 切换WorkFlow状态（模拟）
        function toggleWorkFlow() {
            isWorkFlowEnabled = !isWorkFlowEnabled;
            updateStatusDisplay();
            getAssetStationList();
            updateStationNameControls();
            
            var resultDiv = document.getElementById('validationResult');
            resultDiv.innerHTML = '<p style="color: blue;">配置已更新，请重新测试站址名称功能</p>';
        }

        // 重新检测配置
        function refreshConfig() {
            getAppSettings();
            getAssetStationList();
            updateStatusDisplay();
            updateStationNameControls();
            
            var resultDiv = document.getElementById('validationResult');
            resultDiv.innerHTML = '<p style="color: green;">配置已刷新</p>';
        }

        // 校验站址名称
        function validateStationName() {
            var isValid = true;
            var message = '';

            if (isWorkFlowEnabled) {
                var selectedValue = $('#cboAssetStation').combobox('getValue');
                if (!selectedValue || selectedValue === '') {
                    isValid = false;
                    message = '请选择站址！';
                } else {
                    message = '站址选择有效';
                }
            } else {
                var inputValue = $('#txtStationName').textbox('getValue');
                if (!inputValue || inputValue === '') {
                    isValid = false;
                    message = '站址名称没有输入！';
                } else {
                    message = '站址名称输入有效';
                }
            }
            
            var resultDiv = document.getElementById('validationResult');
            var html = '<h4>校验结果：</h4>';
            html += '<p><strong>WorkFlow状态：</strong>' + (isWorkFlowEnabled ? '启用' : '未启用') + '</p>';
            html += '<p><strong>校验结果：</strong>' + (isValid ? '通过' : '失败') + '</p>';
            html += '<p><strong>提示信息：</strong>' + message + '</p>';
            
            resultDiv.innerHTML = html;
            resultDiv.style.color = isValid ? 'green' : 'red';
        }

        // 获取站址名称值
        function getStationNameValue() {
            var stationNameValue = '';

            if (isWorkFlowEnabled) {
                stationNameValue = $('#cboAssetStation').combobox('getText');
            } else {
                stationNameValue = $('#txtStationName').textbox('getValue');
            }
            
            var resultDiv = document.getElementById('validationResult');
            var html = '<h4>获取站址名称值：</h4>';
            html += '<p><strong>WorkFlow状态：</strong>' + (isWorkFlowEnabled ? '启用' : '未启用') + '</p>';
            html += '<p><strong>站址名称值：</strong>' + stationNameValue + '</p>';
            
            resultDiv.innerHTML = html;
            resultDiv.style.color = 'blue';
        }

        // 清空站址名称
        function clearStationName() {
            if (isWorkFlowEnabled) {
                $('#cboAssetStation').combobox('setValue', '');
            } else {
                $('#txtStationName').textbox('setValue', '');
            }
            
            var resultDiv = document.getElementById('validationResult');
            resultDiv.innerHTML = '<p style="color: orange;">站址名称已清空</p>';
        }

        // 显示资产站址列表
        function displayAssetStationList() {
            var listDiv = document.getElementById('assetStationList');
            var html = '<h4>模拟的TIr_EomsStation表数据：</h4><ul>';
            assetStationData.forEach(function(station) {
                html += '<li><strong>ID:</strong> ' + station.ItemId + ' - <strong>名称:</strong> ' + station.ItemValue + '</li>';
            });
            html += '</ul>';
            listDiv.innerHTML = html;
        }

        // 初始化
        $(document).ready(function() {
            updateStatusDisplay();
            updateStationNameControls();
            displayAssetStationList();
        });
    </script>
</body>
</html>
