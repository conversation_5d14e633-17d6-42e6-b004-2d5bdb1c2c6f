
namespace BDSTool.Entity.S2
{
    using System;
    using System.Collections.Generic;
    
    public partial class TBL_Control
    {
        public int EquipmentTemplateId { get; set; }
        public int ControlId { get; set; }
        public string ControlName { get; set; }
        public int ControlCategory { get; set; }
        public string CmdToken { get; set; }
        public Nullable<decimal> BaseTypeId { get; set; }
        public int ControlSeverity { get; set; }
        public Nullable<int> SignalId { get; set; }
        public Nullable<double> TimeOut { get; set; }
        public Nullable<int> Retry { get; set; }
        public string Description { get; set; }
        public bool Enable { get; set; }
        public bool Visible { get; set; }
        public int DisplayIndex { get; set; }
        public int CommandType { get; set; }
        public Nullable<short> ControlType { get; set; }
        public Nullable<short> DataType { get; set; }
        public double MaxValue { get; set; }
        public double MinValue { get; set; }
        public Nullable<double> DefaultValue { get; set; }
        public int ModuleNo { get; set; }
    }
}
