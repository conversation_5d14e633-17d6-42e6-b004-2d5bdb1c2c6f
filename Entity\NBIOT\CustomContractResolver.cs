﻿using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class CustomContractResolver : DefaultContractResolver
    {
        /// <summary>
        /// 属性列表来源于 父类 Message 中的所有公共属性
        /// </summary>
        readonly List<string> lsPropertyName = new List<string>(){"MessageId", "MessageFamily", "MessageType", "messageType", "Address"
            , "StringMessageType", "ErrorMsg", "StringXML", "SourceHostId", "UserType"};

        /// <summary>
        /// 忽略FSUID的消息类型
        /// </summary>
        //readonly List<string> igoreFsuidTypeList = new List<string>() { "LoginAck", "SetLoginInfo" };
        readonly List<string> igoreFsuidTypeList = new List<string>() { };

        protected override IList<JsonProperty> CreateProperties(Type type, MemberSerialization memberSerialization)
        {
            
            IList<JsonProperty> list = base.CreateProperties(type, memberSerialization);
            //list = list.Where(p => { return !lsPropertyName.Contains(p.PropertyName); }).ToList();
            if (igoreFsuidTypeList.Contains(type.Name))
            {
                return list.Where(p => { return p.PropertyName != "FSUID" || !lsPropertyName.Contains(p.PropertyName); }).ToList();
            }
            else
                return list.Where(p => { return !lsPropertyName.Contains(p.PropertyName); }).ToList() ;
        }
    }
}
