﻿using Delivery.Common.NoProcedure.Utils;
using Delivery.DAL;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using BDSTool.DBUtility.Common;

namespace DM.TestOrder.DAL
{
    public class WoStandardDicSigCheckListDal
    {
        public static DataTable GetAll()
        {
            var sql = "SELECT EquipmentLogicClassId, EquipmentLogicClass, SignalLogicClass, StandardDicId, SignalStandardName, LimitDown, LimitUp, ExtendFiled1, ifnull(CheckIsMust,'否') AS CheckIsMust FROM tbl_standarddicsig";
            DataTable tb = new ExecuteSql().ExecuteSQL(sql);
            return tb;
        }


        public static string UpdateOne(int StandardDicId, string IsMust, float? LimitDown, float? LimitUp)
        {
            try
            {
                var sql = "";
                if (IsMust== "1")
                {
                     sql = string.Format("Update tbl_standarddicsig set CheckIsMust='{0}', LimitDown='{1}', LimitUp='{2}' where StandardDicId={3}", IsMust, LimitDown, LimitUp, StandardDicId);
                }else if(IsMust == "0")
                {
                    sql = string.Format("Update tbl_standarddicsig set CheckIsMust='{0}', LimitDown=null, LimitUp=null where StandardDicId={1}", IsMust, StandardDicId);
                }
              
                var rtn = DBHelper.ExecuteScalar(sql);
            }
            catch (Exception ex)
            {
                return null;
            }
            return  "OK" ;
        }
    }
}
