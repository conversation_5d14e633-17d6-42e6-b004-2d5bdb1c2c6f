﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B
{
    /// <summary>
    /// 5.2.11.4　用户请求监控点历史数据
    /// </summary>
    public sealed class GetHisData : BMessage
    {
        public string DeviceId { get; private set; }
        public string DeviceCode { get; private set; }

        public DateTime StartTime { get; private set; }
        public DateTime EndTime { get; private set; }

        public string[] Ids { get; private set; }

        public GetHisData(string fsuId, string fsuCode, string deviceId, string deviceCode, DateTime startTime, DateTime endTime, params string[] ids)
            : base()
        {
            MessageType = (int)BMessageType.GetHisData;

            FsuId = fsuId;
            FsuCode = fsuCode;
            StartTime = startTime;
            EndTime = endTime;
            DeviceId = deviceId;
            DeviceCode = deviceCode;
            Ids = ids;
        }

        public override string Serialize()
        {
            XmlDocument xmldoc = CreateXmlDocument();
            AppendPK_Type(xmldoc, "Request", "GET_HISDATA", "403");


            XmlElement xe2 = xmldoc.CreateElement("Info");
            XmlElement xe21 = xmldoc.CreateElement("FsuId");
            xe21.InnerText = FsuId;
            xe2.AppendChild(xe21);
            XmlElement xe22 = xmldoc.CreateElement("FsuCode");
            xe22.InnerText = FsuCode;
            xe2.AppendChild(xe22);
            XmlElement xe23 = xmldoc.CreateElement("StartTime");
            xe23.InnerText = string.Format("{0:yyyy-MM-dd HH:mm:ss}", StartTime);
            xe2.AppendChild(xe23);
            XmlElement xe24 = xmldoc.CreateElement("EndTime");
            xe24.InnerText = string.Format("{0:yyyy-MM-dd HH:mm:ss}", EndTime);
            xe2.AppendChild(xe24);

            XmlElement xe25= xmldoc.CreateElement("DeviceList");
            XmlElement xe251 = xmldoc.CreateElement("Device");
            xe251.SetAttribute("Id", DeviceId);
            xe251.SetAttribute("Code", DeviceCode);
            xe25.AppendChild(xe251);

            foreach (string id in Ids)
            {
                XmlElement xe2511 = xmldoc.CreateElement("Id");
                xe2511.InnerText = id;
                xe251.AppendChild(xe2511);
            }
            xe2.AppendChild(xe25);
            XmlNode root = xmldoc.SelectSingleNode("Request"); 
            root.AppendChild(xe2);

            return xmldoc.InnerXml;
        }

        public static GetData Deserialize(string raw)
        {
            throw new NotImplementedException();
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}: {2}.{3}, {4}.{5}, [{6} ~ {7}], [",
                MessageId, (BMessageType)MessageType, FsuId, DeviceId, FsuCode, DeviceCode,
                StartTime.ToString("yyyy-MM-dd HH:mm:ss"), EndTime.ToString("yyyy-MM-dd HH:mm:ss"));

            foreach (string id in Ids)
            {
                sb.AppendFormat("{0}, ", id);
            }

            sb.Append("]");

            return sb.ToString();
        }
    }
}
