﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class TSetSemaphore : TSemaphore
    {
        /// <summary>
        /// 监控点ID
        /// </summary>
        public string SID { get; set; }

        ///// <summary>
        ///// 设置值
        ///// </summary>
        //public float? SetupVal { get; set; }

        /// <summary>
        /// 用于JSON序列化，因为协议中SetupVal是字符串类型
        /// DO控制的SetVal转换为整型
        /// </summary>
        [JsonProperty(PropertyName = "SetupVal")]
        public string SetUpValString
        {
            get
            {
                if (!SetupVal.HasValue)
                    return string.Empty;

                return IsDO(SID) ? Math.Floor(SetupVal.Value).ToString() : SetupVal.ToString();
             }
        }

        private bool IsDO(string BcontrolId)
        {
            return BcontrolId.Substring(3, 1).Equals("1");
        }

        public TSetSemaphore() { }

        public TSetSemaphore(string sId, float? setupValue)
        {
            SID = sId;
            SetupVal = setupValue;
        }
    }
}
