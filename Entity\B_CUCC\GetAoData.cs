﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class GetAoData : BMessage
    {
        public List<Device> LDeviceInfo { get; set; }
        public GetAoData(string suids, string surids, List<Device> lDeviceInfo) : base()
        {
            MessageType = (int)BMessageType.GET_AODATA;
            SUId = suids;
            SURId = surids;
            LDeviceInfo = lDeviceInfo;
        }

        public GetAoData(string suids, string surids, List<Device> lDeviceInfo, string strDeviceList)
            : base()
        {
            MessageType = (int)BMessageType.GET_AODATA;
            SUId = suids;
            SURId = surids;
            LDeviceInfo = lDeviceInfo;
            if ((lDeviceInfo == null || lDeviceInfo.Count == 0) && !string.IsNullOrEmpty(strDeviceList))
            {
                string[] strDeviceIds = strDeviceList.Split('|');
                if (strDeviceIds != null && strDeviceIds.Length > 0)
                {
                    List<Device> deviceList = new List<Device>();
                    foreach (string deviceId in strDeviceIds)
                    {
                        string Id = deviceId.Substring(0, deviceId.IndexOf(','));
                        string RId = deviceId.Substring(deviceId.IndexOf(',') + 1);
                        Device device = new Device(Id, RId);
                        deviceList.Add(device);
                    }
                    LDeviceInfo = deviceList;
                }
            }
        }

        private XmlElement GetElementInfo(XmlDocument xmlDoc)
        {
            XmlElement xmle = xmlDoc.CreateElement("DeviceList");
            if (LDeviceInfo.Count > 0)
            {
                foreach (Device dev in LDeviceInfo)
                {
                    XmlElement xmlT = xmlDoc.CreateElement("Device");
                    xmlT.SetAttribute("Id", dev.Id);
                    xmlT.SetAttribute("RId", dev.RId);
                    if (dev.SignalList != null && dev.SignalList.Count > 0)
                    {
                        foreach (Signal signal in dev.SignalList)
                        {
                            //XmlElement xmlid = xmlDoc.CreateElement("Id");
                            //xmlid.InnerText = signal.Id;
                            //xmlT.AppendChild(xmlid);
                            XmlElement xmlSignal = xmlDoc.CreateElement("Signal");
                            xmlSignal.SetAttribute("Id", signal.Id);
                            xmlT.AppendChild(xmlSignal);
                        }
                    }
                    xmle.AppendChild(xmlT);
                }
            }
            //else
            //{
            //    XmlElement xmlT = xmlDoc.CreateElement("Device");
            //    xmlT.SetAttribute("Id", "");
            //    xmlT.SetAttribute("RId", "");
            //    XmlElement xml1 = xmlDoc.CreateElement("Id");
            //    xml1.InnerText = "";
            //    XmlElement xml2 = xmlDoc.CreateElement("Id");
            //    xml2.InnerText = "";
            //    xmlT.AppendChild(xml1);
            //    xmlT.AppendChild(xml2);
            //    xmle.AppendChild(xmlT);
            //}
            return xmle;
        }
        public override string Serialize()
        {
            try
            {
                XmlDocument xmlDoc = CreateXmlDocument();
                AppendPK_Type(xmlDoc, "Request", BMessageType.GET_AODATA.ToString(), MessageType.ToString());
                XmlElement xel = xmlDoc.CreateElement("Info");
                XmlElement xelone = xmlDoc.CreateElement("SUId");
                XmlElement xeltwo = xmlDoc.CreateElement("SURId");
                xelone.InnerText = SUId;
                xeltwo.InnerText = SURId;
                xel.AppendChild(xelone);
                xel.AppendChild(xeltwo);
                xel.AppendChild(GetElementInfo(xmlDoc));
                XmlNode node = xmlDoc.SelectSingleNode("Request");
                node.AppendChild(xel);
                StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("GetAoData.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmlDoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetAoData.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetAoData.Serialize() Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }
        
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            if (LDeviceInfo.Count > 0)
            {
                foreach (Device de in LDeviceInfo)
                {
                    sb.AppendFormat("{0},{1},{2},{3},{4},{5}", MessageId, (BMessageType)MessageType,MessageType, SUId, SURId, de.ToString());
                }
            }
            else
            {
                sb.AppendFormat("{0},{1},{2},{3},{4},device is null", MessageId, (BMessageType)MessageType,MessageType, SUId, SURId);
            }
            return sb.ToString();
        }
    }
}
