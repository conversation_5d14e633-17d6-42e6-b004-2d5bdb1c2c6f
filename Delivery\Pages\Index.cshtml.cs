﻿using System;
using System.Data;
using System.Text;
using System.Web;
using Delivery.Common;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using Delivery.DAL;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using static System.Net.WebRequestMethods;

namespace Delivery.Pages
{
    public class IndexModel : PageModel
    {
        private readonly ILogger<IndexModel> _logger;

        public IndexModel(ILogger<IndexModel> logger, IConfiguration configuration)
        {
            _logger = logger;
        }

        // 解码 Base64 编码的字符串
        private string DecodeBase64(string encodedValue)
        {
            byte[] bytes = Convert.FromBase64String(encodedValue);
            return Encoding.UTF8.GetString(bytes);
        }

        public void OnGet()
        {
            var queryParams = HttpContext.Request.Query;
            string encodedLogonId = queryParams["SU1Mb2dvbklk"];
            string encodedUserId = queryParams["SU1Vc2Vy"];
            if (!string.IsNullOrEmpty(encodedLogonId) && !string.IsNullOrEmpty(encodedUserId))
            {
                NoNeedToLogin(DecodeBase64(encodedLogonId), DecodeBase64(encodedUserId));
            }
            else
            {
                Console.WriteLine("One or more query parameters are missing or empty.");
            }
        }

        private void NoNeedToLogin(string loginId, string userId)
        {
            try
            {
                HttpContext.Session.SetString("LogonId", loginId);
                HttpContext.Session.SetString("UserId", userId);
                CookieOptions cookieOptions = new CookieOptions();
                cookieOptions.Expires = DateTime.Now.AddDays(1);
                HttpContext.Response.Cookies.Append(
                    "userinfo",
                    $"LogonId={HttpUtility.UrlEncode(loginId)}&UserId={userId}",
                    cookieOptions
                );
                string operatorType = (ConfigHelper.GetSection("BInterfaceType")?.Value ?? string.Empty).ToString(); //运营商类型
                HttpContext.Session.SetString("BInterfaceType", operatorType);
                switch (operatorType)
                {
                    case "1":
                        Response.Redirect("./CMCC/ProjectMain");
                        break;
                    case "3":
                        Response.Redirect("./CUCC/ProjectMain");
                        break;
                    default:
                        throw new ArgumentException("B接口类型配*置无效！");
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public IActionResult OnPost(string txt_userid, string hid_password)
        {
            try
            {
                DataTable dt;
                ExecuteSql exeSql = new ExecuteSql();
                if (CommonUtils.IsNoProcedure)
                {
                   dt = SP_LoginUserInfoService.Instance.SP_CheckLoginUserInfo(txt_userid, hid_password);
                } else
                {
                    dt = exeSql.ExecuteStoredProcedure("SP_CheckLoginUserInfo", new QueryParameter[]
                    {
                    new QueryParameter("LogonId", DataType.String, txt_userid),
                    new QueryParameter("Password", DataType.String, hid_password),
                    new QueryParameter("NewPassword", DataType.String, FormatPass(hid_password))
                    });
                }
             
                if (dt != null && dt.Rows.Count > 0 && dt.Rows[0]["LoginResult"].ToString() == "1")
                {
                    HttpContext.Session.SetString("LogonId", txt_userid);
                    HttpContext.Session.SetString("UserId", dt.Rows[0]["UserId"].ToString());
                    DataTable dt1;
                    if (CommonUtils.IsNoProcedure)
                    {
                        dt1 = SP_LoginUserInfoService.Instance.SP_GetLoginUserInfo(int.Parse(HttpContext.Session.GetString("UserId")));
                    } else
                    {
                        dt1 = exeSql.ExecuteStoredProcedure("SP_GetLoginUserInfo", new QueryParameter[]
                    {
                        new QueryParameter("UserId", DataType.Int, HttpContext.Session.GetString("UserId"))
                    });

                    }
                    Console.WriteLine(dt1);
                    Console.WriteLine(dt1.Rows.Count);
                    if (dt1 != null && dt1.Rows.Count > 0)
                    {
                        CookieOptions cookieOptions = new CookieOptions();
                        cookieOptions.Expires = DateTime.Now.AddDays(1);
                        HttpContext.Response.Cookies.Append("userinfo", $"LogonId={HttpUtility.UrlEncode(dt1.Rows[0]["LogonId"].ToString())}&UserId={dt1.Rows[0]["UserId"].ToString()}", cookieOptions);
                        //HttpContext.Response.Cookies.Append("userinfo", $"LogonId={"admin"}&UserId={-1}", cookieOptions);
                        string operatorType = (ConfigHelper.GetSection("BInterfaceType")?.Value ?? string.Empty).ToString(); //运营商类型
                        HttpContext.Session.SetString("BInterfaceType", operatorType);

                        if (!string.IsNullOrEmpty(Request.Query["path"]))
                        {
                            return RedirectToPage(HttpUtility.UrlDecode(Request.Query["path"]));
                        }

                        if (operatorType == "1")
                            return RedirectToPage("./CMCC/ProjectMain");
                        else if (operatorType == "3")
                            return RedirectToPage("./CUCC/ProjectMain");
                        else
                            throw new ArgumentException("B接口类型配置无效！");
                    }
                }
                else if (dt != null && dt.Rows.Count > 0 && dt.Rows[0]["LoginResult"].ToString() == "0")
                {
                    ViewData["error"] = dt.Rows[0]["Remark"].ToString();
                }
                else
                {
                    ViewData["error"] = "未知错误";
                    //CookieOptions cookieOptions = new CookieOptions();
                    //cookieOptions.Expires = DateTime.Now.AddDays(1);
                    //HttpContext.Response.Cookies.Append("userinfo", $"LogonId={Base64Encrypt("admin")}&UserId={Base64Encrypt("-1")}", cookieOptions);
                    //return RedirectToPage("./CMCC/ProjectMain");
                }
            }
            catch (Exception ex)
            {
                ViewData["error"] = ex.Message;
            }

            return Page();
            //return RedirectToPage("./Index");
        }

        private string FormatPass(string oldPwd)
        {
            var xSb = "";

            for (int i = 0; i < 40; i += 2)
            {
                xSb += Convert.ToInt32(oldPwd.Substring(i, 2), 16).ToString();
            }

            return xSb;
        }

        public string Base64Encrypt(string input)
        {
            return Base64Encrypt(input, new UTF8Encoding());
        }

        public string Base64Encrypt(string input, Encoding encode)
        {
            return Convert.ToBase64String(encode.GetBytes(input));
        }
    }
}