﻿using Serilog;
using System;
namespace Delivery.Common
{
    public class Logger
    {
        private static ILogger _logger;
        static Logger()
        {
            _logger = new LoggerConfiguration().ReadFrom.Configuration(ConfigHelper.GetConfig(), "Serilog").CreateLogger();
        }
        //private static ILog _logger = LogManager.GetLogger("Common");
        public static void Log(Exception ex)
        {
            //需要日志记录
            _logger.Error(ex.Message);
            _logger.Error(ex.StackTrace);
            _logger.Error(ex.ToString());
        }

        public static void Log(string info, LogType type = LogType.Info)
        {
            switch (type)
            {
                case LogType.Verbose:
                    _logger.Verbose(info);
                    break;
                case LogType.DeBug:
                    _logger.Debug(info);
                    break;
                case LogType.Info:
                    _logger.Information(info);
                    break;
                case LogType.Warn:
                    _logger.Warning(info);
                    break;
                case LogType.Error:
                    _logger.Error(info);
                    break;
                default:
                    break;
            }
        }
    }

    public enum LogType
    {
        Verbose = 0,
        DeBug = 1,
        Info = 2,
        Warn = 3,
        Error = 4
        
    }
}
