﻿

using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using Delivery.DAL;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;


namespace DM.TestOrder.DAL {
    public class WoAccountDal
    {
        public static DataTable GetAccount(int userid) {
            //var sql = string.Format("WO_AcountInfoQuery {0}", userid);
            //var tb = DBHelper.GetTable(sql);
            if (CommonUtils.IsNoProcedure)
            {
                var tb1 = WoCommonService.Instance.WO_AcountInfoQuery(userid);
                return tb1;
            }
            var tb = new ExecuteSql().ExecuteStoredProcedure("WO_AcountInfoQuery", new QueryParameter[] {
                new QueryParameter("UserId", DataType.Int, userid.ToString())
            });
            return tb;
        }

    }
}

