﻿using Delivery.API;
using DM.TestOrder.DAL;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;

namespace DM.TestOrder.Controllers {

    public class SiteWebStationTypeController : BaseController {
        [HttpGet]
        public JsonResult Get() {
            var dt = SiteWebStationTypeDal.GetAll();
            DataTableColumnMapper.RenameColumns(dt, DicTypeFieldMap);
            //var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(dt);

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};
            return new JsonResult(dt);

        }

        private static readonly Dictionary<string, string> DicTypeFieldMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "entryitemid", "EntryItemId" },
            { "parententryid", "ParentEntryId" },
            { "parentitemid", "ParentItemId" },
            { "entryid", "EntryId" },
            { "itemid", "ItemId" },
            { "itemvalue", "ItemValue" },
            { "itemalias", "ItemAlias" },
            { "enable", "Enable" },
            { "issystem", "IsSystem" },
            { "isdefault", "IsDefault" },
            { "description", "Description" },
            { "extendfield1", "ExtendField1" },
            { "extendfield2", "ExtendField2" },
            { "extendfield3", "ExtendField3" },
            { "extendfield4", "ExtendField4" },
            { "extendfield5", "ExtendField5" }
        };
    }
}
