﻿using Delivery.API;
using DM.TestOrder.DAL;
using Microsoft.AspNetCore.Mvc;



namespace DM.TestOrder.Controllers {

    public class SiteWebStationTypeController : BaseController {
        [HttpGet]
        public JsonResult Get() {
            var dt = SiteWebStationTypeDal.GetAll();
            //var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(dt);

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};
            return new JsonResult(dt);

        }
    }
}
