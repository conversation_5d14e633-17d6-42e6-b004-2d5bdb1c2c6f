﻿



using DM.TestOrder.Entity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using Delivery.DAL;
using DM.TestOrder.DAL.Helper;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace DM.TestOrder.DAL {

    public class Station
    {
        public static TBL_Station GetStationById(int stationId) {
            var stn = new TBL_Station();
            //var sql = string.Format("select * from TBL_Station where stationId ={0}", stationId);
            //var tb = DBHelper.GetTable(sql);
            DataTable tb;
            if (CommonUtils.IsNoProcedure)
            {
                tb = Public_ExecuteSqlService.Instance.NoStore_TBLStation_GetStationById(stationId);
            }
            else
            {
                var sql = "select * from TBL_Station where stationId =@stationId";
                tb = new ExecuteSql().ExecuteSQL(sql, new QueryParameter[] {
                new QueryParameter("stationId", DataType.Int, stationId.ToString())
                });
            }

            if (tb.Rows.Count == 0)
                return null;

            return TBL_StationHelper.FromDataRow(tb.Rows[0]);
        }


        public static DataTable GetAllInfo() {
            if (CommonUtils.IsNoProcedure)
            {
                var tb1 = WoCommonService.Instance.WO_SiteWebGetStation();
                return tb1;
            }
            var items = new List<TBL_Station>();
            //var tb = DBHelper.GetTable("WO_SiteWebGetStation");
            var tb = new ExecuteSql().ExecuteStoredProcedure("WO_SiteWebGetStation", new QueryParameter[0]);
            return tb;
        }

        public static DataTable GetOne(int stationid) {
            if (CommonUtils.IsNoProcedure)
            {
                var tb1 = WoCommonService.Instance.WO_SiteWebGetStation(stationid);
                return tb1;
            }
            var items = new List<TBL_Station>();
            //var sql = string.Format("WO_SiteWebGetStation {0}", stationid);
            //var tb = DBHelper.GetTable(sql);
            var tb = new ExecuteSql().ExecuteStoredProcedure("WO_SiteWebGetStation", new QueryParameter[] {
                new QueryParameter("StatationId", DataType.Int, stationid.ToString())
            });
                
            return tb;
        }
    }
}
