﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <UserSecretsId>50360638-ad91-4cbc-9e93-6050066c8b6a</UserSecretsId>
    <ApplicationIcon />
    <OutputType>Exe</OutputType>
    <StartupObject />
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Controllers\Cutover\**" />
    <Content Remove="Controllers\Cutover\**" />
    <EmbeddedResource Remove="Controllers\Cutover\**" />
    <None Remove="Controllers\Cutover\**" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Delivery.API\Delivery.API.csproj" />
    <ProjectReference Include="..\Delivery.BSL\Delivery.BSL.csproj" />
    <ProjectReference Include="..\Delivery.Common\Delivery.Common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.12" />
  </ItemGroup>

  <ItemGroup>
    <None Update="SqlScript\dm8\BM_DeviceConfig.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\BM_GetFSUMainConfig.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\BslPartSP.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\CallableSP.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\CMCC\device.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\CMCC\fsu.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\CMCC\house.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\CMCC\station.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\CUCC\device.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\CUCC\fsu.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\CUCC\house.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\CUCC\station.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\PblConfigChangeLog.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\PCT_DeleteMonitorUnit.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\Public_ExecuteSql.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\Public_Stored.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\SP_GetStructureInfo.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\SP_Get_NeedApproveResourceCUCC.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\SP_LoginUserInfo.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\SP_WRFsu.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\SP_WRStation.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\WoPartSP.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\WO_EquipItemCheckList.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\WO_SubmitExpertDecision.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\WO_TestOrderDal.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\WO_TestOrderEquipItemAdd.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\WO_TestOrderQuery.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\BM_DeviceConfig.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\BM_GetFSUMainConfig.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\BslPartSP.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\CallableSP.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\CMCC\device.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\CMCC\fsu.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\CMCC\house.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\CMCC\station.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\CUCC\device.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\CUCC\fsu.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\CUCC\house.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\CUCC\station.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\PblConfigChangeLog.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\PCT_DeleteMonitorUnit.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\Public_ExecuteSql.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\Public_Stored.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\SP_GetStructureInfo.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\SP_Get_NeedApproveResourceCUCC.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\SP_LoginUserInfo.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\SP_WRFsu.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\SP_WRStation.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\station.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\WoPartSP.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\WO_EquipItemCheckList.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\WO_SubmitExpertDecision.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\WO_TestOrderDal.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\WO_TestOrderEquipItemAdd.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\WO_TestOrderQuery.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\EventCategory.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\WoCommon.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\EventCategory.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\WoCommon.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\WO_GetStationList.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\WO_GetStationList.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\WO_BGetSiteCode.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\WO_BGetSiteCode.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\SP_Get_WRDevice.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\SP_Get_WRDevice.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\SP_Add_WRStation.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\SP_Add_WRStation.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\SP_Upd_WRStation.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\SP_Upd_WRStation.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\SP_Del_WRHouse.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\SP_Del_WRHouse.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\SP_Del_WRHouseCUCC.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\SP_Del_WRHouseCUCC.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\SP_RejectWRStationCUCC.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\SP_RejectWRStationCUCC.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\dm8\SP_ApproveWRStationCUCC.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SqlScript\mysql\SP_ApproveWRStationCUCC.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ProjectExtensions><VisualStudio><UserProperties appsettings_1json__JsonSchema="" /></VisualStudio></ProjectExtensions>

</Project>
