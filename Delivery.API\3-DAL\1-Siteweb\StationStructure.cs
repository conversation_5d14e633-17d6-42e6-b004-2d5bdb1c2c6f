﻿


using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using DM.TestOrder.Common.DBA;

namespace DM.TestOrder.DAL {

    public class StationStructure
    {
        //派单
        static public string GetCenterName() {
            var sql = string.Format("select StructureName from TBL_StationStructure where ParentStructureId =0");
            var rtn = DBHelper.ExecuteScalar(sql);
            return rtn;
        }

        static public string GetStationStructrueName(int StationId, int StructureGroupId) {
            string sql = string.Format("SELECT StructureName FROM TBL_StationStructure A, TBL_StationStructureMap B WHERE A.StructureId = B.StructureId AND B.StationId = {0} AND A.StructureGroupId={1}", StationId, StructureGroupId);
            var rtn = DBHelper.ExecuteScalar(sql);
            return rtn;
        }
    }
}
