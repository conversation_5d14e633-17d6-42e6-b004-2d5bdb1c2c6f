﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class GetAiData : BMessage
    {
        public List<Device> LDevice { get; set; }
        public GetAiData(string suids, string surids, List<Device> ldevice) : base()
        {
            MessageType = (int)BMessageType.GET_AIDATA;
            SUId = suids;
            SURId = surids;
            LDevice = ldevice;
        }

        public GetAiData(string suids, string surids, List<Device> ldevice, string strDeviceList)
            : base()
        {
            MessageType = (int)BMessageType.GET_AIDATA;
            SUId = suids;
            SURId = surids;
            LDevice = ldevice;
            entityLogger.DebugFormat("GetAiData.strDeviceList 传入字原始符串:{0}", strDeviceList);
            try
            {
                if ((ldevice == null || ldevice.Count == 0) && !string.IsNullOrEmpty(strDeviceList))
                {
                    string[] strDeviceIds = strDeviceList.Split('|');
                    if (strDeviceIds != null && strDeviceIds.Length > 0)
                    {
                        List<Device> deviceList = new List<Device>();
                        foreach (string deviceId in strDeviceIds)
                        {
                            string Id = deviceId.Substring(0, deviceId.IndexOf(','));
                            string RId = deviceId.Substring(deviceId.IndexOf(',') + 1);
                            Device device = new Device(Id, RId);
                            deviceList.Add(device);
                        }
                        LDevice = deviceList;
                        entityLogger.DebugFormat("解析结果：GetAiData.LDevice count:{0}", LDevice.Count.ToString());
                    }
                }
            }
            catch(Exception ex)
            {
                entityLogger.ErrorFormat("GetAiData 构造函数解析device字符串出错:{0}", ex.Message);
            }
        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmlDoc = CreateXmlDocument();
                AppendPK_Type(xmlDoc, "Request", BMessageType.GET_AIDATA.ToString(), MessageType.ToString());
                XmlElement xel = xmlDoc.CreateElement("Info");
                XmlElement xelone = xmlDoc.CreateElement("SUId");
                XmlElement xeltwo = xmlDoc.CreateElement("SURId");
                XmlElement xelthree = xmlDoc.CreateElement("DeviceList");
                if (LDevice.Count > 0)
                {
                    foreach (Device device in LDevice)
                    {
                        XmlElement xelfour = xmlDoc.CreateElement("Device");
                        xelfour.SetAttribute("Id", device.Id);
                        xelfour.SetAttribute("RId", device.RId);
                        if (device.SignalList != null && device.SignalList.Count > 0)
                        {
                            foreach (Signal signal in device.SignalList)
                            {
                                //XmlElement xelfive = xmlDoc.CreateElement("Id");
                                //xelfive.InnerText = signal.Id;
                                XmlElement xelfive = xmlDoc.CreateElement("Signal");
                                xelfive.SetAttribute("Id", signal.Id);
                                xelfour.AppendChild(xelfive);
                            }
                        }
                        //else
                        //{
                        //    XmlElement xelfive = xmlDoc.CreateElement("Id");
                        //    xelfive.InnerText = "";
                        //    xelfour.AppendChild(xelfive);
                        //}
                        xelthree.AppendChild(xelfour);
                    }
                }
                //else
                //{
                //    XmlElement xelfour = xmlDoc.CreateElement("Device");
                //    xelfour.SetAttribute("Id", "");
                //    xelfour.SetAttribute("RId", "");
                //    xelthree.AppendChild(xelfour);
                //}

                xelone.InnerText = SUId;
                xeltwo.InnerText = SURId;
                xel.AppendChild(xelone);
                xel.AppendChild(xeltwo);
                xel.AppendChild(xelthree);
                XmlNode node = xmlDoc.SelectSingleNode("Request");
                node.AppendChild(xel);
                StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("GetAiData.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmlDoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetAiData.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetAiData.Serialize() Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            if (LDevice.Count > 0)
            {
                foreach (Device de in LDevice)
                {
                    sb.AppendFormat("{0},{1},{2},{3},{4}\n", MessageId, (BMessageType)MessageType, SUId, SURId, de.ToString());
                }
            }
            else
            {
                sb.AppendFormat("{0},{1},{2},{3},Device is null", MessageId, (BMessageType)MessageType, SUId, SURId);
            }
            return sb.ToString();
        }
    }
}
