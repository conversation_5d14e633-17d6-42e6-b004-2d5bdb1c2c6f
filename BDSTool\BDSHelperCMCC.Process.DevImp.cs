﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Odbc;
using System.Linq;
using System.Text;
//using Carrier.Kolo.SSTool;
using Carrier.BDSTool;

using ENPC.Kolo.Entity.B_CMCC;
using Common.Logging.Pro;

using BDSTool.Entity.B;
using BDSTool.BLL.S2;
using BDSTool.BLL.B;
using BDSTool.Utility;
using BDSTool.Entity;

using System.Xml;

namespace Carrier.BDSTool
{        
    public partial class BDSHelperCMCC
    {
        public static bool OnImportFSUConfigOnlyOneDevice(string InnerXML, string FSUID, TDevConf devNew, ref string errmsg) {
            try
            {
                LoggerBDSTool.InfoPartHeader();
                LoggerBDSTool.InfoFormat("OnImportFSUConfigOnlyOneDevice();FSU={0}", FSUID);
                //-------------------------------------------------------------------------------------------------
                var listDevNew = new List<TDevConf>();
                listDevNew.Add(devNew);
                var fsuBiz = new FsuBiz(InnerXML, FSUID, listDevNew);

                if (fsuBiz.OnImportConfigOnlyOneDevice()) {
                    LoggerBDSTool.InfoPartEnd();
                    return true;
                }
                else {
                    errmsg = fsuBiz.ErrorMsg;
                    if (string.IsNullOrEmpty(errmsg))
                        errmsg = "导入FSU配置失败";
                    LoggerBDSTool.InfoPartEnd();
                    return false;
                }
            }

            catch (Exception ex) {
                LoggerBDSTool.ErrorFormat("OnImportFSUConfigOnlyOneDevice();FSUID={0},Error={1}", FSUID, ex.Message);
                LoggerBDSTool.Error(ex.StackTrace);
                LoggerBDSTool.InfoPartEnd();
                return false;
            }

        }
    }
}
