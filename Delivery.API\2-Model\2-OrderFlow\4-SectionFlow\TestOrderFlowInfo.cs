﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace DM.TestOrder.Model {
    public class TestOrderFlowInfo {
        public int OrderFlowId {
            get;
            set;
        }
        public int OrderId {
            get;
            set;
        }
        public int OldOrderState {
            get;
            set;
        }
        public int NewOrderState {
            get;
            set;
        }
        public int StateSetUserId {
            get;
            set;
        }
        public string StateSetUserName {
            get;
            set;
        }
        public string Decision {
            get;
            set;
        }
        public string Note {
            get;
            set;
        }
        public int IsApprove {
            get;
            set;
        }
        public string FlowText {
            get;
            set;
        }
        public System.DateTime SaveTime {
            get;
            set;
        }

        public string OpActionString {
            get {
                switch (OldOrderState) {
                    case 0:
                        return "新建割接单";
                    case 1:
                        return "入网申请";
                    case 2:
                        return "专家组审核";
                    case 3:
                        return "入网复审";
                    default:
                        return "exception";
                }
            }
        }

        public string IsApproveString {
            get {
                return IsApprove == 1 ? "通过" : "退回";
            }
        }
        
    }
}