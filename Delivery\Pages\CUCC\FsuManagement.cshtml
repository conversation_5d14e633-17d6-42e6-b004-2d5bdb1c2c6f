﻿@page
@model Delivery.Pages.CUCC.FsuManagementModel
@{
}
@section Scripts {
    <script type="text/javascript" src="~/Scripts/lib/jquery-1.8.0.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="~/Scripts/common/ie8.js"></script>
    <script type="text/javascript" src="~/Scripts/common/common.js"></script>
    <script type="text/javascript" src="~/CUCC/scripts/FsuManagement.js"></script>
}

@section Styles {
    <style type="text/css">
        #dialogtable tr td {
            padding: 5px 10px 5px 10px;
        }

        input.myNoInput {
            background: rgb(244, 244, 244);
            border-radius: 3px;
            height: 20px;
        }
    </style>
}

<div class="mask">
    <div style="position: relative; top: 50%; left: 50%">
        <div class="loading">
            正在加载，请等待...
        </div>
    </div>
</div>
<div id="FsuDialog" title="SU管理" style="width: 850px; height: 450px;" class="easyui-dialog"
     data-options="modal:true,closed:true,closable:true">
    <table id="dialogtable" style="margin-left: 30px; margin-top: 45px">
        <tr style="height:50px;">
            <td style="text-align: left; white-space: nowrap">
                所属机房
            </td>
            <td style="width: 120px; margin-right: 0px">
                <div id="divHouseName">
                    <input type="hidden" id="txtWRFsuId" />
                    <input type="hidden" id="txtWRHouseId" />
                    <input type="hidden" id="txtRegPassHide" />
                    <input type='text' class="easyui-textbox" placeholder="请选择机房" data-options="prompt:'请选择机房',required:'true'" id="txtHouseName" style="width: 140px;" />
                </div>
            </td>
            <td style="width: 5px; margin-left: 0px; margin-right: 0px">
                <img id="btn_LookHouse" src="../images/queryico.png" style="margin-left: -20px;" />
            </td>
            <td style="text-align: left; white-space: nowrap">
                SU名称
            </td>
            <td style="width: 140px;">
                <input type='text' class="easyui-textbox" placeholder="请输入SU名称" data-options="prompt:'请输入SU名称',required:'true'" id="txtFsuName"
                       style="width: 140px;" />
            </td>
            <td style="text-align: left; white-space: nowrap">
                SU编码
            </td>
            <td>
                <input type='text' class="easyui-textbox" id="txtFsuCode" data-options="prompt:'请输入SU编码',required:'true',validType:'rangelength[12,20]'" style="width: 140px;" />
            </td>
        </tr>
        <tr style="height:50px;">
            <td style="text-align: left; white-space: nowrap">
                SU厂家
            </td>
            <td>
                <select id="ManufacturerId" class="easyui-combobox" title="选择厂家" data-options="prompt:'选择厂家'" style="width: 140px;">
                    <option value=""></option>
                    <option value="1">AA</option>
                    <option value="2">Emerson</option>
                    <option value="3">Huawei</option>
                </select>
            </td>
            <td></td>
            <td style="text-align: left; white-space: nowrap">
                备注
            </td>
            <td colspan="3">
                <input type='text' class="easyui-textbox" placeholder="请输入备注信息" data-options="prompt:'请输入备注信息',required:'true'" id="txtRemark" style="width: 380px;" />
            </td>
        </tr>
        <tr style="height:50px;">
            <td style="text-align: left; white-space: nowrap">
                注册用户名
            </td>
            <td colspan="2">
                <input type='text' class="easyui-textbox" placeholder="请输入注册用户名" data-options="prompt:'请输入注册用户名',required:'true'" id="txtRegName"
                       style="width: 140px;" />
            </td>
            <td style="text-align: left; white-space: nowrap">
                注册口令
            </td>
            <td>
                <input type="password" class="easyui-textbox" data-options="prompt:'请输入注册口令',required:'true'" id="txtRegPass" style="width: 140px;" />
            </td>
            <td>
                FTP用户名
            </td>
            <td>
                <input type='text' class="easyui-textbox" data-options="prompt:'请输入FTP用户名',required:'true'" id="txtFtpUser"
                       style="width: 140px;" />
            </td>
        </tr>
        <tr style="height:50px;">
            <td>
                FTP口令
            </td>
            <td>
                <input type='text' class="easyui-textbox" data-options="prompt:'请输入FTP口令',required:'true'" id="txtFtpPass" style="width: 140px;" />
            </td>
            <td></td>
            <td style="text-align: left; white-space: nowrap">IP地址</td>
            <td style="text-align: left; white-space: nowrap">
                <input type='text' class="easyui-textbox" id="txtIPAddress" data-options="prompt:'请输入IP地址'" style="width: 140px;" />
            </td>
            <td style="text-align: left; white-space: nowrap">SU资源ID</td>
            <td colspan="2" style="text-align: left; white-space: nowrap">
                <input type='text' class="easyui-textbox" id="txtFsuRId" data-options="prompt:'请输入SU资源ID',required:'true'" style="width: 140px;" />
            </td>
        </tr>
        <tr style="height:50px;">

            <td style="text-align: left; white-space: nowrap">
                合同号
            </td>
            <td>
                <input type='text' class="easyui-textbox" data-options="prompt:'请输入合同号',required:'true'" id="txtContractNo" style="width: 140px;" />
            </td>
            <td></td>
            <td style="text-align: left; white-space: nowrap">
                工程名称
            </td>
            <td>
                <input type='text' class="easyui-textbox" data-options="prompt:'请输入工程名',required:'true'" id="txtProjectName" style="width: 140px;" />
            </td>
        </tr>
        <tr>
            <td colspan="6" style="height:30px"></td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td style="width: 5px;"></td>
            <td>
                <input type="button" id="btn_new" value="提交" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btn_modify" value="修改" class="commonButton" style="width: 40pt;" />
            </td>
            <td>
                <input type="button" id="btn_close" value="关闭" class="commonButton" style="width: 40pt;" />
            </td>
            <td></td>
            <td></td>
        </tr>
    </table>
</div>
<div>
    <table style="width: 100%; margin: 0; padding: 0;">
        <tr>
            <td style="text-align: left; white-space: nowrap">
                SU名称:
            </td>
            <td>
                <input type='text' placeholder="请输入SU名称" data-options="prompt:'请输入SU名称'" class="easyui-textbox" id="txtFsuNameq"
                       style="width: 140px;" />
            </td>
            <td style="text-align: left; white-space: nowrap">
                SU编码:
            </td>
            <td>
                <input type='text' data-options="prompt:'请输入SU编码'" class="easyui-textbox" id="txtFsuCodeq"
                       style="width: 140px;" />
            </td>
            <td style="text-align: left; white-space: nowrap">
                SU厂家:
            </td>
            <td>
                <select id="ManufacturerIdq" class="easyui-combobox" style="width: 110px; background-color: #EFEFEF;">
                    <option value=""></option>
                    <option value="aid">aname</option>
                </select>
            </td>
        </tr>
        <tr>
            <td style="text-align: left; white-space: nowrap">
                申请开始日期:
            </td>
            <td>
                <input id="txtApplyTimeq" data-options="validType:['date[\'yyyy-MM-dd hh:mm:ss\']', 'less[\'#txtApplyTimeq2\']'],onChange:function(){$('#txtApplyTimeq2').datetimebox('validate')}" class="easyui-datetimebox" style="width: 140px;" />
            </td>
            <td style="text-align: left; white-space: nowrap">
                申请截止日期:
            </td>
            <td>
                <input id="txtApplyTimeq2" data-options="validType:['date[\'yyyy-MM-dd hh:mm:ss\']','greater[\'#txtApplyTimeq\']'],onChange:function(){$('#txtApplyTimeq').datetimebox('validate')}" class="easyui-datetimebox" style="width: 140px;" />
            </td>
            <td style="text-align: left; white-space: nowrap">
                申请单状态:
            </td>
            <td>
                <select id="FsuStatusq" class="easyui-combobox" style="width: 110px;">
                    <option value=""></option>
                    <option value="入网申请">入网申请</option>
                    <option value="专家组审核">专家组审核</option>
                    <option value="入网复审">入网复审</option>
                    <option value="归档">归档</option>
                </select>
            </td>
        </tr>
        <tr>
            <td>分组</td>
            <td style="text-align: left;">
                <input id="StructureIdLook" class="easyui-combotree" style="width:140px" />
            </td>
            <td colspan="4" style="text-align: center;">
                <input type="button" id="btn_seach" value="查询" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btn_Reset" value="重置" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btn_Edit" value="编辑" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btn_Delete" value="删除" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btn_Pass" value="通过" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btn_back" value="退回" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btn_Excel" value="导出Excel" class="commonButton" style="width: 60pt;" />
                <input type="button" id="btn_opendialog" value="新增SU" class="commonButton" style="width: 80pt;" />
                <button id="btn_help" class="commonButton" style="width: 80pt;">
                    <i style="display:inline-block; vertical-align: middle;width: 16px; height: 16px; background-image: url(../Content/themes/icons/help.png); background-size: contain;"></i>规范浏览
                </button>
            </td>
        </tr>
    </table>
</div>
<div>
    <table id="FsuManagementTable">
    </table>
</div>
<div id="BackDialog" title="SU管理--驳回申请" style="width: 550px; height: 250px;" class="easyui-dialog"
     data-options="modal:true,closed:true,closable:true">
    <table id="Table1">
        <tr style="height: 50px;">
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td style="width: 150px; height: 80px; text-align: right;">请填写退回原因:</td>
            <td>
                <textarea name="TxtRejectReason" id="TxtRejectReason" class="easyui-textbox" data-options="prompt:'请填写退回原因',required:'true'" style="width: 300px;"></textarea>
            </td>
            <td></td>
        </tr>
        <tr style="height: 10px;">
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td style="width: 50px;"></td>
            <td>
                <input type="button" id="btn_AppBackRe" value="提交" class="commonButton" style="width: 40pt;" />
            </td>
            <td></td>
        </tr>
    </table>
</div>
<div id="divLookforHouse" title="查询" style="width: 850px; height: 450px; padding: 20px 10px 5px 10px;"
     class="easyui-dialog" data-options="modal:true,closed:true,closable:true">
    <div>
        <table style="width: 100%; margin: 0; padding: 0;">
            <tr>
                <td colspan="2" style="text-align: left; width: 120px;"></td>
                <td style="text-align: left; white-space: nowrap">
                    站址名称:
                </td>
                <td>
                    <input type='text' placeholder="请输入站址名称" class="easyui-textbox" id="looktxtStationNameq"
                           style="width: 140px;" />
                </td>
                <td style="text-align: left; white-space: nowrap">
                    站址编码:
                </td>
                <td>
                    <input type='text' placeholder="请输入站址编码" class="easyui-textbox" id="looktxtStationCodeq"
                           style="width: 140px;" />
                </td>
            </tr>
            <tr>
                <td colspan="6"></td>
            </tr>
            <tr style="height: 30px">
                <td></td>
                <td></td>
                <td style="text-align: left;">
                    机房名称:
                </td>
                <td>
                    <input type='text' placeholder="请输入机房名称" class="easyui-textbox" id="looktxtHouseNameq"
                           style="width: 140px;" />
                </td>
                <td colspan="2" style="text-align: left;">
                    <input type="button" id="lookbtn_seach" value="查询" class="commonButton" style="width: 40pt;" />
                    <input type="button" id="lookbtn_Reset" value="重置" class="commonButton" style="width: 40pt;" />
                    <input type="button" id="lookbtn_close" value="关闭" class="commonButton" style="width: 40pt;" />
                    <input type="button" id="lookbtn_Select" value="选择" class="commonButton" style="width: 40pt;" />
                </td>
            </tr>
        </table>
    </div>
    <div>
        <table id="lookHouseTable">
        </table>
    </div>
</div>
<div id="standardDoc" class="easyui-dialog" title="规范浏览" style="padding: 0px;" data-options="iconCls:'icon-help', closed: true, resizable: true, width:500">
    <textarea id="standardContent" style="margin: auto; width: 466px; height:300px; padding: 10px; border-width: 0; outline: none; resize: none; color:#000; font-family: Arial; font-size: 16px;" disabled="disabled">编码规范</textarea>
    <div id="toolbar" style="text-align: center; padding: 10px 0;">
        <a href="javascript:void(0);" id="btn_docEdit" class="easyui-linkbutton" data-options="iconCls:'icon-edit'">编辑</a>
        <a href="javascript:void(0);" id="btn_docSave" class="easyui-linkbutton" data-options="iconCls:'icon-save'">保存</a>
    </div>
</div>
