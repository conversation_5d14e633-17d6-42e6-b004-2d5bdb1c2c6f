﻿using Delivery.Common;

using MySql.Data.MySqlClient;

using System;
using System.Configuration;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Security.Cryptography;
using System.Text;

namespace Delivery.DAL
{
    public class DatabaseHelper : IDisposable
    {
        #region Field
        //加密密钥
        private static string DESKEY = "Enpc_SiteWeb_beWetiScpnE";
        private static string DESIV = "EnpcSite";
        //加密头，表明该连接字符串是已经加密过的
        public static string ENCRYPTHEADER = "[ENCRYPTCONNECT]";

        public static ConnectionStringSettings ConnSettings;
        #endregion

        public DatabaseHelper()
        {
            InitDatabaseConnection();
        }

        public DbConnection Connection
        {
            get;
            set;
        }

        public ConnectionStringSettings ConnectionSettings
        {
            get;
            set;
        }

        private int m_commandTimeOut = 600;
        public int CommandTimeOut
        {
            get { return m_commandTimeOut; }
            set { m_commandTimeOut = value; }
        }

        private void InitDatabaseConnection()
        {
            //ConnectionSettings = ConfigurationManager.ConnectionStrings["Default"];

            ConnectionSettings = new ConnectionStringSettings("Default", ConfigHelper.GetSection("Database:Default:ConnectionString").Value, ConfigHelper.GetSection("Database:Default:ProviderName").Value);
            if (ConnectionSettings == null)
            {
                throw new Exception("Kolo:can not find database connection string.");
            }

            ConnSettings = ConnectionSettings;

            CreateConnection();
        }

        private DbProviderFactory GetClientFactory()
        {
            DbProviderFactory db = null;
            if (ConnectionSettings.ProviderName.Trim() == "Sybase.Data.AseClient")
            {
                //return AseClientFactory.Instance;
            }
            else if(ConnectionSettings.ProviderName.Trim() == "MySql")
            {
                db = MySqlClientFactory.Instance;
            }
            else
            {
                db = SqlClientFactory.Instance;
            }

            return db;
        }

        public bool CreateConnection()
        {
            DbProviderFactory fact = GetClientFactory();

            //创建Connection对像
            Connection = fact.CreateConnection();

            //设定Connection对像的连接字符串
            Connection.ConnectionString = DecryptConnectionString(ConnectionSettings.ConnectionString);

            try
            {
                Connection.Open();
            }
            catch (Exception ex)
            {
                if (Connection != null)
                    Connection.Dispose();
                throw ex;
            }

            return true;
        }

        /// <summary>
        /// 执行SQL语句
        /// </summary>
        /// <param name="sql">SQL语句</param>
        /// <returns>影响行数</returns>
        public int ExecuteNoQuery(string sql)
        {
            if (this.Connection == null) throw new Exception("Kolo:Database is not connected.");

            DbCommand cmd = Connection.CreateCommand();
            cmd.CommandText = sql;
            cmd.CommandTimeout = m_commandTimeOut;
            int res = 0;
            try
            {
                res = cmd.ExecuteNonQuery();
                
            }
            catch(Exception ex)
            {
                Logger.Log("执行sql语句：" + sql, LogType.Error);
                Logger.Log(ex);
            }
            finally
            {
                if (cmd != null)
                    cmd.Dispose();
            }
            return res;
        }

        /// <summary>
        /// 执行SQL语句
        /// </summary>
        /// <param name="sql">SQL语句</param>
        /// <returns>首行首列内容</returns>
        public object ExecuteScalar(string sql)
        {
            if (this.Connection == null) throw new Exception("Kolo:Database is not connected.");

            DbCommand cmd = Connection.CreateCommand();
            cmd.CommandText = sql;
            cmd.CommandTimeout = m_commandTimeOut;
            object obj = null;
            try
            {
                obj = cmd.ExecuteScalar();                
            }
            catch (Exception ex)
            {
                Logger.Log("执行sql语句：" + sql, LogType.Error);
                Logger.Log(ex);
            }
            finally
            {
                if (cmd != null)
                    cmd.Dispose();
            }
            return obj;
        }

        /// <summary>
        /// 执行SQL语句
        /// </summary>
        /// <param name="sql">SQL语句</param>
        /// <returns>结果数据表</returns>
        public DataTable ExecuteDataTable(string sql)
        {
            if (this.Connection == null) throw new Exception("Kolo:Database is not connected.");

            DbCommand cmd = Connection.CreateCommand();
            cmd.CommandText = sql;
            cmd.CommandTimeout = m_commandTimeOut;

            DataTable dt = new DataTable();
            DbDataAdapter adapter = null;

            try
            {
                DbProviderFactory fact = GetClientFactory();
                adapter = fact.CreateDataAdapter();
                adapter.SelectCommand = cmd;
                adapter.Fill(dt);
            }
            catch (Exception ex)
            {
                Logger.Log("执行sql语句：" + sql, LogType.Error);
                Logger.Log(ex);
            }
            finally
            {
                if (adapter != null)
                    adapter.Dispose();
                if (cmd != null)
                    cmd.Dispose();
            }

            return dt;
        }

        /// <summary>
        /// 执行SQL语句
        /// </summary>
        /// <param name="sql">SQL语句</param>
        /// <returns>结果数据集</returns>
        public DataSet ExecuteDataSet(string sql)
        {
            if (this.Connection == null) throw new Exception("Kolo:Database is not connected.");

            DbCommand cmd = Connection.CreateCommand();
            cmd.CommandText = sql;
            cmd.CommandTimeout = m_commandTimeOut;

            DataSet ds = new DataSet();
            DbDataAdapter adapter = null;

            try
            {
                DbProviderFactory fact = GetClientFactory();
                adapter = fact.CreateDataAdapter();
                adapter.SelectCommand = cmd;
                adapter.Fill(ds);
            }
            catch (Exception ex)
            {
                Logger.Log("执行sql语句：" + sql, LogType.Error);
                Logger.Log(ex);
            }
            finally
            {
                if (adapter != null)
                    adapter.Dispose();
                if (cmd != null)
                    cmd.Dispose();
            }

            return ds;
        }

        /// <summary>
        /// 构建存储过程命令
        /// </summary>
        public DbCommand NewProcedureCommand(string procedureName)
        {
            if (this.Connection == null) throw new Exception("Kolo:Database is not connected.");
            DbCommand cmd = Connection.CreateCommand();
            cmd.CommandText = procedureName;
            cmd.CommandType = CommandType.StoredProcedure;
            return cmd;
        }

        public DbCommand NewSQLCommand(string sql)
        {
            if (this.Connection == null) throw new Exception("Kolo:Database is not connected.");
            DbCommand cmd = Connection.CreateCommand();
            cmd.CommandText = sql;
            cmd.CommandType = CommandType.Text;
            return cmd;
        }

        public DbCommand NewSQLCommand(string sql, DbParameter[] paras)
        {
            if (this.Connection == null) throw new Exception("Kolo:Database is not connected.");
            DbCommand cmd = Connection.CreateCommand();
            foreach (DbParameter para in paras)
            {
                cmd.Parameters.Add(para);
            }
            cmd.CommandText = sql;
            cmd.CommandType = CommandType.Text;
            return cmd;
        }


        /// <summary>
        /// 取得参数
        /// </summary>
        /// <param name="cmd">DbCommand</param>
        /// <param name="parameterName">参数名称</param>
        /// <param name="dbType">参数类型</param>
        /// <param name="parameterValue">值</param>
        /// <returns>数据参数</returns>
        public DbParameter GetDbParameter(DbCommand cmd, string parameterName,
                                                    DbType dbType, object parameterValue)
        {
            return GetDbParameter(cmd, parameterName, dbType, parameterValue, ParameterDirection.Input, "");
        }

        /// <summary>
        /// 取得参数
        /// </summary>
        /// <param name="cmd">DbCommand</param>
        /// <param name="parameterName">参数名称</param>
        /// <param name="dbType">参数类型</param>
        /// <param name="sourceColumn">对应的数据列名称</param>
        /// <param name="parameterValue">值</param>
        /// <returns>数据参数</returns>        
        public DbParameter GetDbParameter(DbCommand cmd, string parameterName,
                                                    DbType dbType, object parameterValue,
                                                    string sourceColumn)
        {
            return GetDbParameter(cmd, parameterName, dbType, parameterValue, ParameterDirection.Input, sourceColumn);
        }

        /// <summary>
        /// 取得参数
        /// </summary>
        /// <param name="cmd">DbCommand</param>
        /// <param name="parameterName">参数名称</param>
        /// <param name="dbType">参数类型</param>        
        /// <param name="parameterValue">值</param>
        /// <param name="parameterDirection">方向</param>
        /// <returns>数据参数</returns>
        public DbParameter GetDbParameter(DbCommand cmd, string parameterName,
                                                    DbType dbType, object parameterValue,
                                                    ParameterDirection parameterDirection)
        {
            return GetDbParameter(cmd, parameterName, dbType, parameterValue, parameterDirection, "");
        }

        /// <summary>
        /// 取得参数
        /// </summary>
        /// <param name="cmd">DbCommand</param>
        /// <param name="parameterName">参数名称</param>
        /// <param name="dbType">参数类型</param>        
        /// <param name="sourceColumn">对应的数据列名称</param>   
        /// <param name="parameterValue">值</param>
        /// <param name="parameterDirection">方向</param>
        /// <returns>数据参数</returns>
        public DbParameter GetDbParameter(DbCommand cmd, string parameterName,
                                                    DbType dbType, object parameterValue,
                                                    ParameterDirection parameterDirection, string sourceColumn)
        {
            if (cmd == null)
                throw new ArgumentNullException("cmd");

            if (!Enum.IsDefined(typeof(ParameterDirection), parameterDirection))
                throw new ArgumentOutOfRangeException("parameterDirection");

            DbParameter DbParameter = cmd.CreateParameter();
            DbParameter.ParameterName = BuildParameterName(parameterName);
            DbParameter.DbType = dbType;
            DbParameter.SourceColumn = sourceColumn;

            DbParameter.Direction = parameterDirection;
            if (parameterValue == null && dbType == DbType.AnsiString)
                parameterValue = DBNull.Value;

            DbParameter.Value = parameterValue;

            return DbParameter;
        }

        private static string BuildParameterName(string parameterName)
        {
            if(ConnSettings.ProviderName.Trim().ToLower() == "mysql")
            {
                return  parameterName;
            }
            //Sybase,SQLServer前缀需加@
            return "@" + parameterName;
        }

        /// <summary>
        /// 执行命令
        /// </summary>
        /// <param name="cmd">DbCommand</param>
        /// <returns>影响行数</returns>
        public int ExecuteNoQuery(DbCommand cmd)
        {
            return ExecuteNoQuery(cmd, true);
        }

        /// <summary>
        /// 执行命令
        /// </summary>
        /// <param name="cmd">DbCommand</param>
        /// <returns>影响行数</returns>
        public int ExecuteNoQuery(DbCommand cmd, Boolean isDisposed)
        {
            if (this.Connection == null) throw new Exception("Kolo:Database is not connected.");
            int res = 0;
            try
            {
                cmd.CommandTimeout = m_commandTimeOut;
                res = cmd.ExecuteNonQuery();
                if (res > 0)
                    res = 1;
            }
            catch (Exception ex)
            {
                Logger.Log("执行命令：" + cmd.CommandText + " ,命令类型: "+ cmd.CommandType.ToString(), LogType.Error);
                Logger.Log(ex);
            }
            finally
            {
                if (cmd != null && isDisposed)
                    cmd.Dispose();
            }
            return res;
        }

        /// <summary>
        /// 执行命令
        /// </summary>
        /// <param name="cmd">DbCommand</param>
        /// <returns>首行首列内容</returns>
        public object ExecuteScalar(DbCommand cmd)
        {
            if (this.Connection == null) throw new Exception("Kolo:Database is not connected.");
            object obj = null;
            try
            {
                cmd.CommandTimeout = m_commandTimeOut;
                obj = cmd.ExecuteScalar();
            }
            catch (Exception ex)
            {
                Logger.Log("执行命令：" + cmd.CommandText + " ,命令类型: " + cmd.CommandType.ToString(), LogType.Error);
                Logger.Log(ex);
            }
            finally
            {
                if (cmd != null)
                    cmd.Dispose();
            }
            return obj;
        }

        /// <summary>
        /// 执行命令
        /// </summary>
        /// <param name="cmd">DbCommand</param>
        /// <returns>结果数据表</returns>
        public DataTable ExecuteDataTable(DbCommand cmd)
        {
            if (this.Connection == null) throw new Exception("Kolo:Database is not connected.");

            cmd.CommandTimeout = m_commandTimeOut;

            DataTable dt = new DataTable();
            DbDataAdapter adapter = null;

            try
            {
                DbProviderFactory fact = GetClientFactory();
                adapter = fact.CreateDataAdapter();
                adapter.SelectCommand = cmd;
                adapter.Fill(dt);
            }
            catch (Exception ex)
            {
                Logger.Log("执行命令：" + cmd.CommandText + " ,命令类型: " + cmd.CommandType.ToString(), LogType.Error);
                Logger.Log(ex);
            }
            finally
            {
                if (adapter != null)
                    adapter.Dispose();
                if (cmd != null)
                    cmd.Dispose();
            }

            return dt;
        }

        /// <summary>
        /// 执行命令
        /// </summary>
        /// <param name="cmd">DbCommand</param>
        /// <returns>结果数据集</returns>
        public DataSet ExecuteDataSet(DbCommand cmd)
        {
            if (this.Connection == null) throw new Exception("Kolo:Database is not connected.");

            cmd.CommandTimeout = m_commandTimeOut;

            DataSet ds = new DataSet();
            DbDataAdapter adapter = null;

            try
            {
                DbProviderFactory fact = GetClientFactory();
                adapter = fact.CreateDataAdapter();
                adapter.SelectCommand = cmd;
                adapter.Fill(ds);
            }
            catch (Exception ex)
            {
                Logger.Log("执行命令：" + cmd.CommandText + " ,命令类型: " + cmd.CommandType.ToString(), LogType.Error);
                Logger.Log(ex);
            }
            finally
            {
                if (adapter != null)
                    adapter.Dispose();
                if (cmd != null)
                    cmd.Dispose();
            }

            return ds;
        }

        #region Helper Function
        /// <summary>
        /// 加密连接字符串
        /// </summary>
        public static string EncryptConnectionString(string inputValue)
        {
            if (string.IsNullOrEmpty(inputValue))
                return string.Empty;
            UTF8Encoding byteConverter = new UTF8Encoding();
            var des = new TripleDESCryptoServiceProvider();
            des.Key = byteConverter.GetBytes(DESKEY);
            des.IV = byteConverter.GetBytes(DESIV);
            byte[] inputByteArray = byteConverter.GetBytes(inputValue);
            using (System.IO.MemoryStream ms = new System.IO.MemoryStream())
            {
                using (CryptoStream cs = new CryptoStream(ms, des.CreateEncryptor(), CryptoStreamMode.Write))
                {
                    cs.Write(inputByteArray, 0, inputByteArray.Length);
                    cs.FlushFinalBlock();
                    cs.Close();
                }
                return string.Format("{0}{1}", ENCRYPTHEADER, Convert.ToBase64String(ms.ToArray()));
            }
        }

        /// <summary>
        /// 解密连接字符串
        /// </summary>
        public static string DecryptConnectionString(string inputValue)
        {
            if (string.IsNullOrEmpty(inputValue))
                return string.Empty;
            if (inputValue.StartsWith(ENCRYPTHEADER) == false)
                return inputValue;
            else
            {
                //inputValue = inputValue.TrimStart(ENCRYPTHEADER.ToCharArray());加密串为"[ENCRYPTCONNECT]EC0Ov0WDX/Y="时会出错  
                inputValue = inputValue.Substring(ENCRYPTHEADER.Length);
            }
            UTF8Encoding byteConverter = new UTF8Encoding();
            TripleDESCryptoServiceProvider des = new TripleDESCryptoServiceProvider();
            des.Key = byteConverter.GetBytes(DESKEY);
            des.IV = byteConverter.GetBytes(DESIV);
            byte[] inputByteArray = Convert.FromBase64String(inputValue);
            using (System.IO.MemoryStream ms = new System.IO.MemoryStream())
            {
                using (CryptoStream cs = new CryptoStream(ms, des.CreateDecryptor(), CryptoStreamMode.Write))
                {
                    cs.Write(inputByteArray, 0, inputByteArray.Length);
                    cs.FlushFinalBlock();
                    cs.Close();
                }
                return byteConverter.GetString(ms.ToArray());
            }
        }
        #endregion

        #region IDisposable 成员

        public void Dispose()
        {
            if (this.Connection != null)
            {
                Connection.Close();
                Connection.Dispose();
            }
        }

        #endregion
    }
}