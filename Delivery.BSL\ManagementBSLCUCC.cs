﻿using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Service.CUCC;
using Delivery.Common.NoProcedure.Utils;
using Delivery.DAL;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Delivery.BSL
{
    public class DeliveryDataBslCucc
    {
        readonly ExecuteSql exesql = new ExecuteSql();

        public DataTable GetGroupInfo()
        {
            DataTable dt;
            if (CommonUtils.IsNoProcedure)
            {
                dt = SP_GetStructureInfoService.Instance.GetStructureInfo();
            }
            else
            {
                dt = exesql.ExecuteStoredProcedure("SP_Get_StructureInfo", new QueryParameter[] {
                new QueryParameter("StructureId",DataType.Int,"0")
                });
            }
            if (dt == null)
                return new DataTable();
            return dt;
        }

        public DataTable GetNeedApproveResource(string LoginId)
        {
            if (CommonUtils.IsNoProcedure)
            {
                return SP_Get_NeedApproveResourceCUCCService.Instance.GetNeedApproveResourceCUCC(LoginId);
            }
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Get_NeedApproveResourceCUCC", new QueryParameter[] { new QueryParameter("LogonId", DataType.String, LoginId) });
            if (dt == null)
                return new DataTable();
            return dt;
        }
        ///<summary>
        ///查询角色身份
        ///</summary>
        public DataTable GetUserRoleType(string logonid)
        {
            DataTable dt;
            if (CommonUtils.IsNoProcedure)
            {
                dt = SP_GetStructureInfoService.Instance.GetUserInfo(logonid);
            }
            else
            {
                dt = exesql.ExecuteStoredProcedure("SP_GetUserInfo", new QueryParameter[] { new QueryParameter("LogonId", DataType.String, logonid) });
            }
            if (dt == null)
                return new DataTable();
            return dt;
        }

        /// <summary>
        /// 查询 FSU入网申请 信息
        /// </summary>
        public DataTable GetFsuManagement(string startTime, string endTime, string structureId, string fsuName, string fsuCode, string manufactureId, string status, string LogonId)
        {
            string endstring = DateTime.Now.AddMonths(1).ToString("yyyy-MM-dd");
            startTime = string.IsNullOrEmpty(startTime) ? "2018-03-02" : startTime;
            endTime = string.IsNullOrEmpty(endTime) ? endstring : endTime;

            if(CommonUtils.IsNoProcedure)
            {
                DataTable dtt = BslPartSPCUCCService.Instance.GetWRFsuCUCC(startTime, endTime, structureId, fsuName, fsuCode, manufactureId, status, LogonId);
                return dtt == null ? new DataTable() : dtt;
            }

            DataTable dt = exesql.ExecuteStoredProcedure("SP_Get_WRFsuCUCC", new QueryParameter[] {
                new QueryParameter("StartTime",DataType.DateTime,startTime),
                new QueryParameter("EndTime",DataType.DateTime,endTime),
                new QueryParameter("StructureId",DataType.Int,structureId),
                new QueryParameter("FsuName",DataType.String,fsuName),
                new QueryParameter("FsuCode",DataType.String,fsuCode),
                new QueryParameter("ManufacturerId",DataType.Int,manufactureId),
                new QueryParameter("Status", DataType.Int, status),
                new QueryParameter("LogonId",DataType.String,LogonId)
            });
            if (dt == null)
                return new DataTable();
            return dt;
        }
        /// <summary>
        /// 联通查询
        /// </summary>     
        public DataTable GetStationInfo(string structureId, string StationName, string StationCode, string strBegin, string strEnd, string stationType, string stationStatus, string LogonId)
        {
            string endstring = DateTime.Now.AddMonths(1).ToString("yyyy-MM-dd");
            strBegin = string.IsNullOrEmpty(strBegin) ? "2018-03-02" : strBegin;
            strEnd = string.IsNullOrEmpty(strEnd) ? endstring : strEnd;
            DataTable dt;
            if (CommonUtils.IsNoProcedure)
            {
                dt = SP_WRStationService.Instance.GetWRStation(structureId, StationName, StationCode, strBegin, strEnd, stationType, stationStatus, LogonId);
            }
            else
            {
                dt = exesql.ExecuteStoredProcedure("SP_Get_WRStationCUCC", new QueryParameter[] {
                new QueryParameter("StartTime",DataType.DateTime,strBegin),
                new QueryParameter("EndTime",DataType.DateTime,strEnd),
                new QueryParameter("StructureId",DataType.Int,structureId),
                new QueryParameter("StationCategory",DataType.Int,stationType),
                new QueryParameter("StationName",DataType.String,StationName),
                new QueryParameter("StationCode",DataType.String,StationCode),
                new QueryParameter("Status",DataType.Int,stationStatus),
                new QueryParameter("LogonId",DataType.String,LogonId)
            });
            }
            if (dt == null)
                return new DataTable();
            return dt;
        }

        public DataTable GetDeviceInfo(string startTime, string endTime, string stationId, string fsuId, string deviceType, string deviceName, string deviceCode, string logonId)
        {
            string endstring = DateTime.Now.AddMonths(1).ToString("yyyy-MM-dd");
            startTime = string.IsNullOrEmpty(startTime) ? "2018-03-02" : startTime;
            endTime = string.IsNullOrEmpty(endTime) ? endstring : endTime;
            if (CommonUtils.IsNoProcedure)
            {
                return DeviceService.Instance.GetDeviceInfo(startTime, endTime, stationId, fsuId, deviceType, deviceName, deviceCode, logonId);
            }
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Get_WRDeviceCUCC", new QueryParameter[] {
                new QueryParameter("StartTime",DataType.DateTime,startTime),
                new QueryParameter("EndTime",DataType.DateTime,endTime),
                new QueryParameter("WRStationId",DataType.Int,stationId),
                new QueryParameter("WRFsuId",DataType.Int, fsuId),
                new QueryParameter("DeviceType",DataType.String,deviceType),
                new QueryParameter("DeviceName",DataType.String,deviceName),
                new QueryParameter("DeviceCode",DataType.String,deviceCode),
                new QueryParameter("LogonId",DataType.String,logonId)
            });
            if (dt == null)
                return new DataTable();
            return dt;
        }

        /// <summary>
        /// 联通查询
        /// </summary>
        /// <param name="structureId"></param>    
        public DataTable GetStationInfoCheck(string structureId, string StationName, string StationCode, string strBegin, string strEnd, string stationType, string stationStatus, string LogonId)
        {
            string endstring = DateTime.Now.AddMonths(1).ToString("yyyy-MM-dd");
            strBegin = string.IsNullOrEmpty(strBegin) ? "2018-03-02" : strBegin;
            strEnd = string.IsNullOrEmpty(strEnd) ? endstring : strEnd;
            DataTable dt;
            if (CommonUtils.IsNoProcedure)
            {
                dt = SP_WRStationService.Instance.GetWRStationConditionCUCC(structureId, StationName, StationCode, strBegin, strEnd, stationType);
            }
            else
            {
                dt = exesql.ExecuteStoredProcedure("SP_Get_WRStationConditionCUCC", new QueryParameter[] {
                new QueryParameter("StartTime",DataType.DateTime,strBegin),
                new QueryParameter("EndTime",DataType.DateTime,strEnd),
                new QueryParameter("StructureId",DataType.Int,structureId),
                new QueryParameter("StationCategory",DataType.Int,stationType),
                new QueryParameter("StationName",DataType.String,StationName),
                new QueryParameter("StationCode",DataType.String,StationCode)
            });
            }
            if (dt == null)
                return new DataTable();
            return dt;
        }


        public DataTable GetHouseInfo(string structureId, string StationCode, string strBegin, string strEnd, string StationName, string HouseName, string HouseCode, string Status, string LogonId)
        {

            if (CommonUtils.IsNoProcedure)
            {
                return HouseService.Instance.SP_Get_WRHouseCUCC(structureId, StationCode, strBegin, strEnd, StationName, HouseName, HouseCode, Status, LogonId);
            }
            string endstring = DateTime.Now.AddMonths(1).ToString("yyyy-MM-dd");
            strBegin = string.IsNullOrEmpty(strBegin) ? "2018-03-02" : strBegin;
            strEnd = string.IsNullOrEmpty(strEnd) ? endstring : strEnd;
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Get_WRHouseCUCC", new QueryParameter[] {
                new QueryParameter("StartTime",DataType.DateTime,strBegin),
                new QueryParameter("EndTime",DataType.DateTime,strEnd),
                 new QueryParameter("StructureId",DataType.Int,structureId),
                new QueryParameter("StationName",DataType.String,StationName),
                new QueryParameter("StationCode",DataType.String,StationCode),
                new QueryParameter("HouseName",DataType.String,HouseName),
                new QueryParameter("HouseCode",DataType.String,HouseCode),
                new QueryParameter("Status",DataType.Int,Status),
                new QueryParameter("LogonId",DataType.String,LogonId)
            });
            if (dt == null)
                return new DataTable();
            return dt;
        }
        public DataTable GetHouseInfoCheck(string structureId, string StationCode, string strBegin, string strEnd, string StationName, string HouseName, string HouseCode, string Status, string LogonId)
        {
            if (CommonUtils.IsNoProcedure)
            {
                return HouseService.Instance.SP_Get_WRHouseConditionCUCC(structureId, StationCode, strBegin, strEnd, StationName, HouseName, HouseCode, Status, LogonId);
            }
            string endstring = DateTime.Now.AddMonths(1).ToString("yyyy-MM-dd");
            strBegin = string.IsNullOrEmpty(strBegin) ? "2018-03-02" : strBegin;
            strEnd = string.IsNullOrEmpty(strEnd) ? endstring : strEnd;
            if (string.IsNullOrEmpty(structureId))
                structureId = "-1";
            if (string.IsNullOrEmpty(StationCode))
                StationCode = "";
            if (string.IsNullOrEmpty(StationName))
                StationName = "";
            if (string.IsNullOrEmpty(HouseName))
                HouseName = "";
            if (string.IsNullOrEmpty(HouseCode))
                HouseCode = "";
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Get_WRHouseConditionCUCC", new QueryParameter[] {
                new QueryParameter("StartTime",DataType.DateTime,strBegin),
                new QueryParameter("EndTime",DataType.DateTime,strEnd),
                 new QueryParameter("StructureId",DataType.Int,structureId),
                new QueryParameter("StationName",DataType.String,StationName),
                new QueryParameter("StationCode",DataType.String,StationCode),
                new QueryParameter("HouseName",DataType.String,HouseName),
                new QueryParameter("HouseCode",DataType.String,HouseCode)
            });
            if (dt == null)
                return new DataTable();
            return dt;
        }

        public int Add_WRFsu(string WRHouseId, string FsuCode, string FSURId, string FsuName, string IPAddress, string UserName, string Password, string FtpUserName, string FtpPassword, string ManufacturerId, string Remark, string ContractNo, string ProjectName, string LogonId)
        {
            if(CommonUtils.IsNoProcedure)
            {
              return  HouseService.Instance.SP_Add_WRFsuCUCC(WRHouseId, FsuCode, FSURId, FsuName, IPAddress, UserName, Password, FtpUserName, FtpPassword, ManufacturerId, Remark, ContractNo, ProjectName, LogonId);
            }
            int _result = 0;
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Add_WRFsuCUCC", new QueryParameter[] {
                new QueryParameter("WRHouseId",DataType.Int,WRHouseId),
                new QueryParameter("FsuCode",DataType.String,FsuCode),
                new QueryParameter("FsuName",DataType.String,FsuName),
                new QueryParameter("IPAddress",DataType.String,IPAddress),
                new QueryParameter("UserName",DataType.String,UserName),
                new QueryParameter("Password",DataType.String,Password),
                new QueryParameter("FtpUserName",DataType.String,FtpUserName),
                new QueryParameter("FtpPassword",DataType.String,FtpPassword),
                new QueryParameter("ManufacturerId",DataType.String,ManufacturerId),
                new QueryParameter("Remark",DataType.String,Remark),
                new QueryParameter("ContractNo",DataType.String,ContractNo),
                new QueryParameter("ProjectName",DataType.String,ProjectName),
                new QueryParameter("FSURId",DataType.String,FSURId),
                new QueryParameter("LogonId",DataType.String,LogonId)
            });
            if (dt == null)
            {
                return 0;
            }
            else if (dt.Rows.Count > 0)
            {
                _result = int.Parse(dt.Rows[0][0].ToString());
            }
            return _result;
        }

        public int Add_WRDevice(string houseId, string fsuId, string deviceType, string deviceName, string deviceRId, string remark, string logonId)
        {
            if(CommonUtils.IsNoProcedure)
            {
              return  DeviceService.Instance.SP_Add_WRDeviceCUCC(houseId, fsuId, deviceType, deviceName, deviceRId, remark, logonId);
            }
            int _result = 0;
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Add_WRDeviceCUCC", new QueryParameter[] {
                new QueryParameter("WRHouseId",DataType.Int,houseId),
                new QueryParameter("DeviceType",DataType.String,deviceType),
                new QueryParameter("DeviceName",DataType.String,deviceName),
                new QueryParameter("Remark",DataType.String,remark),
                new QueryParameter("DeviceRId",DataType.String,deviceRId),
                new QueryParameter("WRFsuId",DataType.Int,fsuId),
                new QueryParameter("LogonId",DataType.String,logonId)
            });
            if (dt == null)
            {
                return 0;
            }
            else if (dt.Rows.Count > 0)
            {
                _result = int.Parse(dt.Rows[0][0].ToString());
            }
            return _result;
        }

        public int InsertHouse(string WRStationId, string HouseCode, string HouseRId, string HouseName, string Remark, string LogonId)
        {
            if(CommonUtils.IsNoProcedure)
            {
                return HouseService.Instance.InsertHouseCUCC(WRStationId, HouseCode, HouseRId, HouseName, Remark, LogonId);
            }
            int _result = 0;
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Add_WRHouseCUCC", new QueryParameter[] {
                new QueryParameter("WRStationId",DataType.Int,WRStationId),
                new QueryParameter("HouseCode",DataType.String,HouseCode),
                new QueryParameter("HouseName",DataType.String,HouseName),
                new QueryParameter("Remark",DataType.String,Remark),
                new QueryParameter("HouseRId",DataType.String,HouseRId),
                new QueryParameter("LogonId",DataType.String,LogonId)
            });
            if (dt == null)
            {
                return 0;
            }
            else if (dt.Rows.Count > 0)
            {
                _result = int.Parse(dt.Rows[0][0].ToString());
            }
            return _result;

        }
        public int UpdateHouse(string WRHouseId, string WRStationId, string HouseCode, string HouseRId, string HouseName, string Remark)
        {
            int _result = 0;
            DataTable dt;
            if (CommonUtils.IsNoProcedure)
            {
                dt = HouseService.Instance.SP_Upd_WRHouseCUCC(WRHouseId, WRStationId, HouseCode, HouseName, HouseRId, Remark);
            }
            else {
                dt = exesql.ExecuteStoredProcedure("SP_Upd_WRHouseCUCC", new QueryParameter[] {
                new QueryParameter("WRHouseId",DataType.Int,WRHouseId),
                new QueryParameter("WRStationId",DataType.Int,WRStationId),
                new QueryParameter("HouseCode",DataType.String,HouseCode),
                new QueryParameter("HouseName",DataType.String,HouseName),
                new QueryParameter("HouseRId",DataType.String,HouseRId),
                new QueryParameter("Remark",DataType.String,Remark)
                });
            }
            if (dt == null)
            {
                return 0;
            }
            else if (dt.Rows.Count > 0)
            {
                _result = int.Parse(dt.Rows[0][0].ToString());
            }
            return _result;

        }

        public int Update_WRDevice(string deviceId, string houseId, string fsuId, string deviceType, string deviceName, string deviceRId, string remark, string logonId)
        {
            int _result = 0;
            DataTable dt;
            if (CommonUtils.IsNoProcedure)
            {
                dt = DeviceService.Instance.SP_Upd_WRDeviceCUCC(deviceId, houseId, fsuId, deviceType, deviceName, deviceRId, remark, logonId);
            }
            else {
                dt = exesql.ExecuteStoredProcedure("SP_Upd_WRDeviceCUCC", new QueryParameter[] {
                new QueryParameter("WRDeviceId",DataType.Int,deviceId),
                new QueryParameter("WRHouseId",DataType.Int,houseId),
                new QueryParameter("WRFsuId",DataType.Int,fsuId),
                new QueryParameter("DeviceType",DataType.String,deviceType),
                new QueryParameter("DeviceName",DataType.String,deviceName),
                new QueryParameter("Remark",DataType.String,remark),
                new QueryParameter("DeviceRId",DataType.String,deviceRId),
                new QueryParameter("LogonId",DataType.String,logonId)
                });
            }
            if (dt == null)
            {
                return 0;
            }
            else if (dt.Rows.Count > 0)
            {
                _result = int.Parse(dt.Rows[0][0].ToString());
            }
            return _result;

        }

        public string Delete_WRDevice(string deviceId, string logonId)
        {
            string _result = "0";
            string checkRight = "1";

            if(CommonUtils.IsNoProcedure)
            {
                if (!int.TryParse(deviceId, out int deviceIdInt)) return _result;
                DataTable dtt = BslPartSPCUCCService.Instance.DelWRDeviceCUCC(deviceIdInt, logonId, int.Parse(checkRight));
                if(dtt != null && dtt.Rows.Count > 0)
                {
                    _result = dtt.Rows[0][0].ToString();
                }
                return _result;
            }

            DataTable dt = exesql.ExecuteStoredProcedure("SP_Del_WRDeviceCUCC", new QueryParameter[] {
                new QueryParameter("WRDeviceId",DataType.Int,deviceId),
                new QueryParameter("LogonId",DataType.String,logonId),
                new QueryParameter("CheckRight",DataType.String,checkRight)
            });
            if (dt.Rows.Count > 0)
            {
                _result = dt.Rows[0][0].ToString();
            }
            return _result;

        }

        public int UpdateFsu(string WRFsuId, string WRHouseId, string FsuCode, string FSURId, string FsuName, string IPAddress, string UserName, string Password, string FtpUserName, string FtpPassword, string ManufacturerId, string Remark, string ContractNo, string ProjectName, string LogonId)
        {
            int _result = 0;
            DataTable dt;
            if (CommonUtils.IsNoProcedure)
            {
                dt = SP_WRFsuService.Instance.UpdWRFsuCUCC(WRFsuId, WRHouseId, FsuCode, FsuName, IPAddress, UserName, Password, FtpUserName, FtpPassword, ManufacturerId, Remark, ContractNo, ProjectName, FSURId, LogonId);
            }
            else
            {
                dt = exesql.ExecuteStoredProcedure("SP_Upd_WRFsuCUCC", new QueryParameter[] {
                new QueryParameter("WRFsuId",DataType.Int,WRFsuId),
                new QueryParameter("WRHouseId",DataType.Int,WRHouseId),
                new QueryParameter("FsuCode",DataType.String,FsuCode),
                new QueryParameter("FsuName",DataType.String,FsuName),
                new QueryParameter("IPAddress",DataType.String,IPAddress),
                new QueryParameter("UserName",DataType.String,UserName),
                new QueryParameter("Password",DataType.String,Password),
                new QueryParameter("FtpUserName",DataType.String,FtpUserName),
                new QueryParameter("FtpPassword",DataType.String,FtpPassword),
                 new QueryParameter("ManufacturerId",DataType.String,ManufacturerId),
                new QueryParameter("Remark",DataType.String,Remark),
                new QueryParameter("ContractNo",DataType.String,ContractNo),
                new QueryParameter("ProjectName",DataType.String,ProjectName),
                new QueryParameter("FSURId",DataType.String,FSURId),
                new QueryParameter("LogonId",DataType.String,LogonId)
                });
            }

            if (dt == null)
            {
                return 0;
            }
            else if (dt.Rows.Count > 0)
            {
                _result = int.Parse(dt.Rows[0][0].ToString());
            }
            return _result;


        }

        public int InsertStation(string StructureId, string Province, string City, string County, string StationCategory, string StationRId, string StationName, string Address, string Remark, string ContractNo, string ProjectName, string loginId)
        {
            int _result = 0; 

            if(CommonUtils.IsNoProcedure)
            {
                DataTable dtt = BslPartSPCUCCService.Instance.AddWRStationCUCC(Province, City, County, StructureId, StationCategory, StationRId, StationName, Address, Remark, ContractNo, ProjectName, loginId);
                return dtt == null ? 0 : int.Parse(dtt.Rows[0][0].ToString());
            }

             DataTable dt = exesql.ExecuteStoredProcedure("SP_Add_WRStationCUCC", new QueryParameter[] {
                new QueryParameter("Province",DataType.Int,Province),
                new QueryParameter("City",DataType.Int,City),
                new QueryParameter("County",DataType.Int,County),
                new QueryParameter("StructureId",DataType.Int,StructureId),
                new QueryParameter("StationCategory",DataType.Int,StationCategory),
                new QueryParameter("StationName",DataType.String,StationName),
                new QueryParameter("Address",DataType.String,Address),
                new QueryParameter("Remark",DataType.String,Remark),
                new QueryParameter("ContractNo",DataType.String,ContractNo),
                new QueryParameter("ProjectName",DataType.String,ProjectName),
                new QueryParameter("StationRId",DataType.String,StationRId),
                new QueryParameter("LogonId",DataType.String,loginId)
            });
            if (dt == null)
            {
                return 0;
            }
            else if (dt.Rows.Count > 0)
            {
                _result = int.Parse(dt.Rows[0][0].ToString());
            }
            return _result;
        }

        public int UpdateStation(string swsStationId, string StructureId, string Province, string City, string County, string StationCategory, string StationRId, string StationName, string Address, string Remark, string ContractNo, string ProjectName, string loginId)
        {
            int _result = 0;
            DataTable dt;
            if (CommonUtils.IsNoProcedure) {
                dt = SP_WRStationService.Instance.UpdWRStationCUCC(swsStationId,StructureId,Province,City,County,StationCategory,StationRId,StationName,Address,Remark,ContractNo,ProjectName,loginId);
            }
            else
            {
                dt = exesql.ExecuteStoredProcedure("SP_Upd_WRStationCUCC", new QueryParameter[] {
                new QueryParameter("WRStationId",DataType.Int,swsStationId),
                new QueryParameter("Province",DataType.Int,Province),
                new QueryParameter("City",DataType.Int,City),
                new QueryParameter("County",DataType.Int,County),
                new QueryParameter("StructureId",DataType.Int,StructureId),
                new QueryParameter("StationCategory",DataType.Int,StationCategory),
                new QueryParameter("StationName",DataType.String,StationName),
                new QueryParameter("Address",DataType.String,Address),
                new QueryParameter("Remark",DataType.String,Remark),
                new QueryParameter("ContractNo",DataType.String,ContractNo),
                new QueryParameter("ProjectName",DataType.String,ProjectName),
                new QueryParameter("StationRId",DataType.String,StationRId),
                new QueryParameter("LogonId",DataType.String,loginId)
            });
            }
            if (dt == null)
            {
                return 0;
            }
            else if (dt.Rows.Count > 0)
            {
                _result = int.Parse(dt.Rows[0][0].ToString());
            }
            return _result;
        }
        public string DeleteHouse(string WRHouseId, string logonId)
        {
            string _result = "0";
            string checkRight = "1";
            if (CommonUtils.IsNoProcedure)
            {
                return SPDelWRHouseCUCCService.Instance.DeleteHouseCUCC(WRHouseId, logonId);
            }
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Del_WRHouseCUCC", new QueryParameter[] {
                new QueryParameter("WRHouseId",DataType.Int, WRHouseId),
                new QueryParameter("LogonId",DataType.String, logonId),
                new QueryParameter("CheckRight",DataType.String, checkRight)
            });

            if (dt.Rows.Count > 0)
            {
                _result = dt.Rows[0][0].ToString();
            }
            return _result;
        }
        public string DeleteStation(string WRStationId, string logonId)
        {
            string _result = "0";
            DataTable dt;
            if(CommonUtils.IsNoProcedure)
            {
                dt = BslPartSPCUCCService.Instance.DelStationCUCC(int.Parse(WRStationId), logonId);
            }
            else
            {
                dt = exesql.ExecuteStoredProcedure("SP_Del_StationCUCC", new QueryParameter[] {
                new QueryParameter("WRStationId",DataType.Int,WRStationId),
                new QueryParameter("LogonId",DataType.String, logonId)
                });
            }
            if (dt.Rows.Count > 0)
            {
                _result = dt.Rows[0][0].ToString();
            }
            return _result;
        }
        public string ApproveStation(string swsStationId)
        {
            DataTable dt;
            if (CommonUtils.IsNoProcedure)
            {
                int intSwsStationId = int.Parse(swsStationId);
                dt = SPApproveWRStationCUCCService.Instance.ApproveWRStationCUCC(intSwsStationId);
            }
            else
            {
                dt = exesql.ExecuteStoredProcedure("SP_ApproveWRStationCUCC", new QueryParameter[] {
                new QueryParameter("WRStationId",DataType.Int,swsStationId)
                });
            }
            if (dt != null)
            {
                return dt.Rows[0][0].ToString();
            }
            else
            {
                return "0";
            }
        }
        public string ApproveWRHouse(string WRHouseId)
        {
            if(CommonUtils.IsNoProcedure)
            {
                DataTable dt2 = HouseService.Instance.SP_ApproveWRHouseCUCC(WRHouseId);
                if (dt2 != null)
                {
                    return dt2.Rows[0][0].ToString();
                }
                else
                {
                    return "0";
                }
            }
            DataTable dt = exesql.ExecuteStoredProcedure("SP_ApproveWRHouseCUCC", new QueryParameter[] {
                new QueryParameter("WRHouseId",DataType.Int,WRHouseId)
            });
            if (dt != null)
            {
                return dt.Rows[0][0].ToString();
            }
            else
            {
                return "0";
            }
        }


        public string ApproveWRFsu(string WRFsuId)
        {
            if (CommonUtils.IsNoProcedure)
            {
                return FsuService.Instance.ApproveWRFsu(WRFsuId);
            }

            DataTable dt = exesql.ExecuteStoredProcedure("SP_ApproveWRFsuCUCC", new QueryParameter[] {
                new QueryParameter("WRFsuId",DataType.Int,WRFsuId)
            });
            if (dt != null)
            {
                return dt.Rows[0][0].ToString();
            }
            else
            {
                return "0";
            }
        }


        public string RejectWRStation(string swsStationId, string StationCode, string RejectCause)
        {
            if (CommonUtils.IsNoProcedure)
            {
                return SPRejectWRStationCUCCService.Instance.RejectWRStationCUCC(swsStationId, StationCode, RejectCause);
            }

            DataTable dt = exesql.ExecuteStoredProcedure("SP_RejectWRStationCUCC", new QueryParameter[] {
                new QueryParameter("WRStationId",DataType.Int,swsStationId),
                new QueryParameter("StationCode",DataType.String,StationCode),
                new QueryParameter("RejectCause",DataType.String,RejectCause)
            });
            if (dt != null)
            {
                return dt.Rows[0][0].ToString();
            }
            else
            {
                return "0";
            }
        }
        public string RejectWRHouse(string WRHouseId, string RejectCause)
        {
            if(CommonUtils.IsNoProcedure)
            {
                return HouseService.Instance.RejectWRHouse(WRHouseId, RejectCause);
            }
            DataTable dt = exesql.ExecuteStoredProcedure("SP_RejectWRHouseCUCC", new QueryParameter[] {
                new QueryParameter("WRHouseId",DataType.Int,WRHouseId),
                new QueryParameter("RejectCause",DataType.String,RejectCause)
            });
            if (dt != null)
            {
                return dt.Rows[0][0].ToString();
            }
            else
            {
                return "0";
            }
        }

        public string RejectWRFsu(string WRFsuId, string RejectCause)
        {
            if(CommonUtils.IsNoProcedure)
            {
                return FsuService.Instance.RejectWRFsu(WRFsuId, RejectCause);
            }
            DataTable dt = exesql.ExecuteStoredProcedure("SP_RejectWRFsuCUCC", new QueryParameter[] {
                new QueryParameter("WRFsuId",DataType.Int,WRFsuId),
                new QueryParameter("RejectCause",DataType.String,RejectCause)
            });
            if (dt != null)
            {
                return dt.Rows[0][0].ToString();
            }
            else
            {
                return "0";
            }
        }
        public string DeleteWRFsu(string WRFsuId, string logonId)
        {
            string _result = "0";
            string checkRight = "1";
            DataTable dt;
            if(CommonUtils.IsNoProcedure)
            {
                dt = BslPartSPCUCCService.Instance.DelWRFsuCUCC(Convert.ToInt32(WRFsuId), logonId, 1);
            }
            else
            {
                dt = exesql.ExecuteStoredProcedure("SP_Del_WRFsuCUCC", new QueryParameter[] {
                new QueryParameter("WRFsuId",DataType.Int,WRFsuId),
                new QueryParameter("LogonId",DataType.String,logonId),
                new QueryParameter("CheckRight",DataType.Int,checkRight)
                });
            }
            if (dt.Rows.Count > 0)
            {
                _result = dt.Rows[0][0].ToString();
            }
            return _result;
        }

        public DataTable GetWRFsuId(string stationId, string houseId, string logonId)
        {
            if(CommonUtils.IsNoProcedure)
            {
                int strStationId = stationId == null ? -1 : int.Parse(stationId);
                int strHouseId = houseId == null ? -1 : int.Parse(houseId);


                return FsuService.Instance.GetWRFsuId(strStationId, strHouseId, logonId);
            }
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Get_WRFsuByStationOrHouseCUCC", new QueryParameter[] {
                new QueryParameter("WRStationId",DataType.Int,stationId),
                new QueryParameter("WRHouseId",DataType.Int,houseId),
                new QueryParameter("LogonId",DataType.String,logonId)
            });
            return dt;
        }

        public bool RemoveCooperationCompany(string userId, string companyId)
        {
            if (exesql.ExecuteStoredProcedureNoQuery("V_PJ_RemoveCooperationCompany", new QueryParameter[] {
                new QueryParameter("CompanyId",DataType.Int,companyId),
                new QueryParameter("UserId",DataType.Int,userId)
            }) > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
    }
}
