﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <procedures>
    <procedure owner="" name="SP_Get_WRDeviceCUCC" grant="">
      <parameters>
        <parameter name="WRStationId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="DeviceType" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="DeviceName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="DeviceCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="WRFsuId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StartTime" type="datetime" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EndTime" type="datetime" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="iUserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
        <replace keyName ="WhereWRSationId"> and a.WRStationId = @WRStationId</replace>
        <replace keyName ="WhereDeviceTypeCode"> and a.DeviceType = @DeviceType</replace>
        <replace keyName ="WhereDeviceName"> and a.DeviceName like @DeviceName</replace>
        <replace keyName ="WhereDeviceCode">  and a.DeviceCode like @DeviceCode</replace>
        <replace keyName ="WhereFsuId"> and a.WRFsuId  = @WRFsuId</replace>
        <replace keyName="WhereTime">
          <![CDATA[ AND a.ApplyTime >= @StartTime AND a.ApplyTime <= @EndTime ]]>
        </replace>
        <replace keyName ="RoleMapNotNull">
          SELECT DISTINCT a.UserId FROM WR_FsuManagementCUCC a
        </replace>
        <replace keyName ="RoleMapNull">
          SELECT DISTINCT a.UserId FROM TBL_UserRoleMap a
          WHERE a.RoleId IN (SELECT RoleId FROM TBL_UserRoleMap b
          WHERE b.UserId = @iUserId)
        </replace>
        <replace keyName ="SqlStr">
          <![CDATA[ 
          SELECT ROW_NUMBER() over(order by a.ApplyTime DESC) as RowNumber,
		      a.WRDeviceId, a.WRStationId, c.StationName, a.WRHouseId, b.HouseName, 
		      a.WRFsuId, e.FsuCode, e.FsuName, a.DeviceType, a.DeviceRId,
		      d.ItemValue DeviceTypeName, a.DeviceCode, a.DeviceName, a.UserId, 
		      a.SWUserName, a.ApplyTime, a.SWStationId, a.SWHouseId, a.Remark		
	        FROM WR_DeviceManagementCUCC a
          ]]>
        </replace>
      </replaces>
      <body>
        <![CDATA[
        $[SqlStr]
        INNER JOIN WR_HouseManagementCUCC b ON a.WRStationId = b.WRStationId AND a.WRHouseId = b.WRHouseId
	      INNER JOIN WR_StationManagementCUCC c ON a.WRStationId = c.WRStationId
	      INNER JOIN TBL_DataItem d ON a.DeviceType = d.ExtendField2 AND d.EntryId = 7 
	      LEFT JOIN WR_FsuManagementCUCC e ON a.WRFsuId = e.WRFsuId
	      INNER JOIN ( $[RoleMapNotNull]$[RoleMapNull] ) f on a.UserId = f.UserId
	      WHERE 1 = 1 
        $[WhereTime] 
        $[WhereWRSationId]
        $[WhereDeviceTypeCode]
        $[WhereDeviceName]
        $[WhereDeviceCode]
        $[WhereFsuId]
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_GenerateCUCCDeviceCode_getMaxNo" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="WRFsuId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="DeviceType" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[
        SELECT max(Convert( substring(WR_DeviceManagementCUCC.DeviceCode,4,length(WR_DeviceManagementCUCC.DeviceCode) - 3),signed)) maxNo
	      FROM WR_DeviceManagementCUCC 
	      WHERE WR_DeviceManagementCUCC.WRStationId = @WRStationId AND WR_DeviceManagementCUCC.WRFsuId = @WRFsuId AND 
        LEFT(WR_DeviceManagementCUCC.DeviceCode, 3) = @DeviceType;
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_GenerateCUCCDeviceCode_getDeviceCount" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="WRFsuId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="DeviceType" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[
        SELECT count(*) DeviceCount FROM WR_DeviceManagementCUCC 
	      WHERE WR_DeviceManagementCUCC.WRStationId = @WRStationId AND WR_DeviceManagementCUCC.WRFsuId = @WRFsuId AND
        LEFT(WR_DeviceManagementCUCC.DeviceCode, 3) = @DeviceType;
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_GenerateCUCCDeviceCode_ifDeviceExist" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="WRFsuId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="tmpCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[
        SELECT 'X' FROM WR_DeviceManagementCUCC WHERE WR_DeviceManagementCUCC.WRStationId = @WRStationId 
        AND WR_DeviceManagementCUCC.WRFsuId = @WRFsuId AND WR_DeviceManagementCUCC.DeviceCode = @tmpCode
      ]]>
      </body>
    </procedure>
	  <procedure owner="" name="SP_Add_WRDeviceCUCC_FindByHouse" grant="">
		  <parameters>
			  <parameter name="WRHouseId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
          SELECT 'X' FROM WR_HouseManagementCUCC WHERE WR_HouseManagementCUCC.WRHouseId = @WRHouseId 
	      AND WR_HouseManagementCUCC.HouseStatus != 3
             ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Add_WRDeviceCUCC_StationInfo" grant="">
		  <parameters>
			  <parameter name="WRHouseId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
          SELECT  a.WRStationId WRStationId,b.SWStationId SWStationId,a.SWHouseId SWHouseId 
		  FROM WR_HouseManagementCUCC a 
		  INNER JOIN WR_StationManagementCUCC b ON a.WRStationId = b.WRStationId
		  WHERE a.WRHouseId =@WRHouseId;
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Add_WRDeviceCUCC_FindByStationIdAndDeviceName" grant="">
		  <parameters>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="DeviceName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
          SELECT 'X' FROM WR_DeviceManagementCUCC a WHERE a.SWStationId = @SWStationId AND a.DeviceName =@DeviceName;
		  SELECT 'X' FROM TBL_Equipment b WHERE b.StationId = @SWStationId AND b.EquipmentName =@DeviceName;
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Add_WRDeviceCUCC_UserInfo" grant="">
		  <parameters>
			  <parameter name="LogonId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
          SELECT  a.UserId UserId, a.UserName SWUserName FROM TBL_Account a 
	      WHERE a.LogonId =@LogonId; 
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Add_WRDeviceCUCC_Insert_WR_DeviceManagementCUCC" grant="">
		  <parameters>
			  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="WRHouseId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="WRFsuId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="DeviceType" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="DeviceRId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="DeviceCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="DeviceName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWUserName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWHouseId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="Remark" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
          INSERT INTO WR_DeviceManagementCUCC
		  (WRStationId,WRHouseId,WRFsuId,DeviceType,DeviceRId,DeviceCode,DeviceName,UserId,SWUserName,ApplyTime,SWStationId,SWHouseId,Remark)
	      VALUES 
		 (@WRStationId,@WRHouseId,@WRFsuId,@DeviceType,@DeviceRId,@DeviceCode,@DeviceName,@UserId,@SWUserName, now(),@SWStationId,@SWHouseId,@Remark);
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Add_WRDeviceCUCC_WRDeviceId" grant="">
		  <parameters>
			  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="DeviceName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
         SELECT WRDeviceId WRDeviceId FROM WR_DeviceManagementCUCC WHERE WR_DeviceManagementCUCC.WRStationId = @WRStationId AND WR_DeviceManagementCUCC.DeviceName = @DeviceName;
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Add_WRDeviceCUCC_StationName" grant="">
		  <parameters>
			  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
        SELECT IFNULL(StationName,'') StationName FROM WR_StationManagementCUCC WHERE WR_StationManagementCUCC.WRStationId = @WRStationId;
         ]]>
		  </body>
	  </procedure>






	  <procedure owner="" name="SP_Upd_WRDeviceCUCC_JuedgeExist1" grant="">
		  <parameters>
			  <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
                    SELECT 'X' FROM WR_HouseManagementCUCC a WHERE a.WRHouseId = @WRHouseId AND a.HouseStatus != 3 limit 1;
            ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Upd_WRDeviceCUCC_GetFieldValue1" grant="">
		  <parameters>
			  <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
                    SELECT a.WRStationId,b.SWStationId,a.SWHouseId 
					FROM WR_HouseManagementCUCC a 
					INNER JOIN WR_StationManagementCUCC b ON a.WRStationId = b.WRStationId
					WHERE a.WRHouseId = @WRHouseId;
            ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Upd_WRDeviceCUCC_JuedgeExist2" grant="">
		  <parameters>
			  <parameter name="SWStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="WRDeviceId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="DeviceName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
                    SELECT 'X' FROM WR_DeviceManagementCUCC b WHERE b.SWStationId = @SWStationId AND b.WRDeviceId != @WRDeviceId AND b.DeviceName = @DeviceName limit 1;
            ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Upd_WRDeviceCUCC_GetFieldValue2" grant="">
		  <parameters>
			  <parameter name="WRDeviceId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT  a.DeviceType AS OriDeviceType, a.DeviceCode 
					FROM WR_DeviceManagementCUCC a
					WHERE a.WRDeviceId = @WRDeviceId;
            ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Upd_WRDeviceCUCC_GetFieldValue3" grant="">
		  <parameters>
			  <parameter name="LogonId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT a.UserId , a.UserName AS SWUserName 
					FROM TBL_Account a
					WHERE a.LogonId = @LogonId; 
            ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Upd_WRDeviceCUCC_GetFieldValue4" grant="">
		  <parameters>
			  <parameter name="WRDeviceId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT 	concat('WRHouseId:' , convert(WRHouseId,CHAR) ,
	    					'-DeviceType:' , convert(DeviceType,CHAR) ,
	    					'-DeviceRId:' , convert(DeviceRId,CHAR) ,
	    					'-DeviceCode:' , convert(DeviceCode,CHAR) , 
	    					'-DeviceName:' , convert(DeviceName,CHAR) ,
	    					'-UserId:' , convert(UserId,CHAR) ,
	    					'-Remark:' , convert(Remark,CHAR) ,
	    					'-WRFsuId:' , convert(WRFsuId,CHAR)) 
	    					FROM WR_DeviceManagementCUCC WHERE WR_DeviceManagementCUCC.WRDeviceId = @WRDeviceId limit 1;
            ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Upd_WRDeviceCUCC_GetFieldValue5" grant="">
		  <parameters>
			  <parameter name="WRStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT IFNULL(StationName,'') AS StationName FROM WR_StationManagementCUCC WHERE WR_StationManagementCUCC.WRStationId = @WRStationId limit 1;
            ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Upd_WRDeviceCUCC_UpdateWRDeviceManagementCUCC" grant="">
		  <parameters>
			  <parameter name="WRStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="WRHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="DeviceType" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="DeviceRId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="DeviceCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="DeviceName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="UserId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWUserName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="Remark" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="WRFsuId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="WRDeviceId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					set sql_safe_updates = 0;
					UPDATE WR_DeviceManagementCUCC
					SET WR_DeviceManagementCUCC.WRStationId = @WRStationId
						,WR_DeviceManagementCUCC.WRHouseId = @WRHouseId
						,WR_DeviceManagementCUCC.DeviceType = @DeviceType
						,WR_DeviceManagementCUCC.DeviceRId = @DeviceRId
						,WR_DeviceManagementCUCC.DeviceCode = @DeviceCode
						,WR_DeviceManagementCUCC.DeviceName = @DeviceName
						,WR_DeviceManagementCUCC.UserId = @UserId
						,WR_DeviceManagementCUCC.SWUserName = @SWUserName
						,WR_DeviceManagementCUCC.SWStationId = @SWStationId
						,WR_DeviceManagementCUCC.SWHouseId = @SWHouseId 
						,WR_DeviceManagementCUCC.Remark = @Remark
						,WR_DeviceManagementCUCC.WRFsuId = @WRFsuId
					WHERE WR_DeviceManagementCUCC.WRDeviceId = @WRDeviceId;
					set sql_safe_updates = 1;
            ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Upd_WRDeviceCUCC_GetFieldValue6" grant="">
		  <parameters>
			  <parameter name="WRDeviceId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT  b.EquipmentId AS SWEquipmentId 
					FROM WR_DeviceManagementCUCC a 
					INNER JOIN WR_FsuManagementCUCC c ON a.WRFsuId = c.WRFsuId
					INNER JOIN TBL_EquipmentCUCC b ON a.SWStationId = b.StationId AND a.DeviceCode = b.DeviceID  AND b.SUID = c.FsuCode
					WHERE a.WRDeviceId = @WRDeviceId limit 1;
            ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Upd_WRDeviceCUCC_UpdateTBLEquipment" grant="">
		  <parameters>
			  <parameter name="DeviceName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWEquipmentId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					set sql_safe_updates = 0;
					UPDATE TBL_Equipment 
					SET TBL_Equipment.EquipmentName = @DeviceName, TBL_Equipment.HouseId = @SWHouseId
					WHERE TBL_Equipment.StationId = @SWStationId AND TBL_Equipment.EquipmentId = @SWEquipmentId;
					set sql_safe_updates = 1;
				]]>
		  </body>
	  </procedure>
  </procedures>
</root>
