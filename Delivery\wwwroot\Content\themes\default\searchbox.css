.searchbox {
  display: inline-block;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  border-width: 1px;
  border-style: solid;
  overflow: hidden;
}
.searchbox .searchbox-text {
  font-size: 12px;
  border: 0;
  margin: 0;
  padding: 0;
  line-height: 20px;
  height: 20px;
  *margin-top: -1px;
  *height: 18px;
  *line-height: 18px;
  _height: 18px;
  _line-height: 18px;
  vertical-align: baseline;
}
.searchbox .searchbox-prompt {
  font-size: 12px;
  color: #ccc;
}
.searchbox-button {
  width: 18px;
  height: 20px;
  overflow: hidden;
  display: inline-block;
  vertical-align: top;
  cursor: pointer;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.searchbox-button-hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.searchbox a.l-btn-plain {
  height: 20px;
  border: 0;
  padding: 0 6px 0 0;
  vertical-align: top;
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.searchbox a.l-btn .l-btn-left {
  padding: 2px 0 2px 4px;
}
.searchbox a.l-btn-plain:hover {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
  border: 0;
  padding: 0 6px 0 0;
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.searchbox a.m-btn-plain-active {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
}
.searchbox-button {
  background: url('images/searchbox_button.png') no-repeat center center;
}
.searchbox {
  border-color: #95B8E7;
  background-color: #fff;
}
.searchbox a.l-btn-plain {
  background: #E0ECFF;
}
