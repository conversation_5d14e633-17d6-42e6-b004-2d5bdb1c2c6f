﻿using BDSTool.BLL.S2;
using BDSTool.DBUtility;
using BDSTool.DBUtility.Common;
using BDSTool.Entity.B;
using BDSTool.Entity.DRHelper;
using Common.Logging.Pro;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.BLL.B
{
    public  partial class MonitorUnitCMCCBiz
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");
        //public static bool UpdateMonitorUnitId(string FSUID, int MonitorUnitId) {
        //    try {
        //        var sql = string.Format(" Update TSL_MonitorUnitCMCC set MonitorUnitId={0} where FSUID={1}", MonitorUnitId, SHelper.GetPara(FSUID));
        //        DBHelper.ExecuteNonQuery(sql);
        //    }
        //    catch (Exception ex) {
        //        logger.ErrorFormat("MonitorUnitCMCCBiz.UpdateMonitorUnitId();Error={0}", ex.Message);
        //        logger.Error(ex.StackTrace);
        //        return false;
        //    }
        //    return true;
        //}

        /// <summary>
        /// 4P1-ID转换: B接口ID(FSUID)  -> MonitorUnitId
        /// </summary>
        /// <param name="FSUID"></param>
        /// <returns></returns>
        public static bool GetMonitorUnitIdByFSUID(string FSUID, ref int MonitorUnitId) {
            try {
                string rtn = null;
                if (CommonUtils.IsNoProcedure) {
                    rtn = Public_ExecuteSqlService.Instance.NoStore_TSLMonitorUnitCMCC_GetMonitorUnitIdByFSUID(FSUID);
                }
                else
                {
                    var sql = string.Format("SELECT MonitorUnitId FROM TSL_MonitorUnitCMCC WHERE FSUId = '{0}'", FSUID);
                    rtn = DBHelper.ExecuteScalar(sql);
                }
                if (string.IsNullOrEmpty(rtn)) {
                    return false;
                }
                MonitorUnitId = int.Parse(rtn.ToString());
            }
            catch (Exception ex) {
                logger.ErrorFormat("GetMonitorUnitIdByFSUID();FSU={0};Error={1}", FSUID, ex.Message);
                logger.ErrorFormat("GetMonitorUnitIdByFSUID();Stack={0}", ex.StackTrace);
                return false;
            }

            return true;
        }



        public static bool GetFSUIDByMonitorUnitId( int MonitorUnitId,ref string FSUID) {
            try {
                string rtn = null;
                if (CommonUtils.IsNoProcedure)
                {
                    rtn = Public_ExecuteSqlService.Instance.NoStore_TSLMonitorUnitCMCC_GetFsuIdByMonitorUnitId(MonitorUnitId);
                }
                else 
                {
                    var sql = string.Format("SELECT FSUId FROM TSL_MonitorUnitCMCC WHERE MonitorUnitId = {0}", MonitorUnitId);
                    rtn = DBHelper.ExecuteScalar(sql);
                }
                
                if (string.IsNullOrEmpty(rtn)) {
                    return false;
                }
                FSUID = rtn.ToString();
            }
            catch (Exception ex) {
                logger.ErrorFormat("GetFSUIDByMonitorUnitId();FSU={0};Error={1}", FSUID, ex.Message);
                logger.ErrorFormat("GetFSUIDByMonitorUnitId();Stack={0}", ex.StackTrace);
                return false;
            }

            return true;
        }

        public static TSL_MonitorUnitCMCCEx GetMainConfigByFSUID(string FSUID) {
            try {
                var sql = string.Format("BM_GetFSUMainConfig {0}", SHelper.GetPara(FSUID));

                //var table = DBHelper.GetTable(sql);
                DataTable table = null;
                try
                {
                    if(CommonUtils.IsNoProcedure)
                    {
                        table = BM_GetFSUMainConfigService.Instance.BM_GetFSUMainConfig(SHelper.GetProcPara(FSUID));
                    }
                    else
                    {
                        using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                        {
                            dbHelper.CreateProcedureCommand("BM_GetFSUMainConfig");
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("FSUID", DbType.AnsiString, SHelper.GetProcPara(FSUID)));
                            table = dbHelper.ExecuteDataTable();
                        }
                    }
                }
                catch (Exception ex)
                {
                    DBHelper.LogError(sql, ex);
                }
                if (table.Rows.Count == 0) {
                    logger.WarnFormat("no record in TSL_MonitorUnitCMCCEx for fsu:{0}", FSUID);
                    return null;
                }                
                return TSL_MonitorUnitCMCCExHelper.FromDataRow(table.Rows[0]);
            }
            catch (Exception ex) {
                logger.ErrorFormat("GetMainConfigByFSUID();FSU =  {0};Error =  {1}", FSUID, ex.Message);
                logger.Error(ex.StackTrace);
                return null;
            }
        }

         public static bool Exist(string FSUID) {
            try {
                string rtn = null;
                if (CommonUtils.IsNoProcedure)
                {
                    rtn = Public_ExecuteSqlService.Instance.NoStore_TSLMonitorUnitCMCC_JudgeExistByFsuId(FSUID);
                }
                else
                {
                    var sql = string.Format(@"SELECT 1 FROM TSL_MonitorUnitCMCC WHERE FSUId = {0}", SHelper.GetPara(FSUID));

                    rtn = DBHelper.ExecuteScalar(sql);
                }
                if (string.IsNullOrEmpty(rtn)) {
                    return false;
                }
                else
                    return true;
            }
            catch (Exception ex) {
                logger.ErrorFormat("Exist();FSU ={0};Error =  {1}", FSUID, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
         }

        //public static TSL_MonitorUnitCMCC FindByFSUID(string FSUID) {
        //    try {
        //        var sql = string.Format(@"SELECT FSUID, FSUName, SiteID, RoomID, StationId, MonitorUnitId, UserName, PassWord, FSUIP, FSUMAC, FSUVER, Result, FailureCause, CPUUsage, MEMUsage, GetFSUInfoResult, GetFSUFaliureCause, GetFSUTime, FTPUserName, FTPPassWord, ExtendField1, ExtendField2 FROM dbo.TSL_MonitorUnitCMCC WHERE FSUId = {0}", SHelper.GetPara(FSUID));

        //        var table = DBHelper.GetTable(sql);
        //        if (table.Rows.Count == 0) {
        //            logger.WarnFormat("no record in TSL_MonitorUnitCMCC for fsu:{0}", FSUID);
        //            return null;
        //        }

        //        DataRow row = table.Rows[0];

        //        var fsu = new TSL_MonitorUnitCMCC();
        //        fsu.FSUID = FSUID;

        //        fsu.StationId = SHelper.ToInt(row["StationId"]);
        //        fsu.FSUName = row["FSUName"].ToString();
        //        fsu.SiteID = row["SiteID"].ToString();
        //        fsu.RoomID = row["RoomID"].ToString();

        //        fsu.MonitorUnitId = SHelper.ToInt(row["MonitorUnitId"]);
        //        fsu.UserName = row["UserName"].ToString();
        //        fsu.PassWord = row["PassWord"].ToString();
        //        fsu.FSUIP = row["FSUIP"].ToString();
        //        fsu.FSUMAC = row["FSUMAC"].ToString();
        //        fsu.FSUVER = row["FSUVER"].ToString();
        //        fsu.Result = SHelper.ToIntNullable(row["Result"]);
        //        fsu.FailureCause = row["FailureCause"].ToString();
        //        fsu.CPUUsage = SHelper.ToDoubleNullable(row["CPUUsage"]);
        //        fsu.MEMUsage = SHelper.ToDoubleNullable(row["MEMUsage"]);
        //        fsu.GetFSUInfoResult = SHelper.ToIntNullable(row["GetFSUInfoResult"]);
        //        fsu.GetFSUFaliureCause = row["GetFSUFaliureCause"].ToString();
        //        fsu.GetFSUTime = SHelper.ToDateTimeNullable(row["GetFSUTime"]);
        //        fsu.FTPUserName = row["FTPUserName"].ToString();
        //        fsu.FTPPassWord = row["FTPPassWord"].ToString();
        //        fsu.ExtendField1 = row["ExtendField1"].ToString();
        //        fsu.ExtendField2 = row["ExtendField2"].ToString();


        //        return fsu;
        //    }
        //    catch (Exception ex) {
        //        logger.ErrorFormat("FindByFSUID();FSU =  {0};Error =  {1}", FSUID, ex.Message);
        //        logger.Error(ex.StackTrace);
        //        return null;
        //    }
        //}
    }
}
