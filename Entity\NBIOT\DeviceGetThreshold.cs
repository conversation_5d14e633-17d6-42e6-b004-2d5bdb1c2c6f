﻿using System.Collections.Generic;
using System.Text;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class DeviceGetThreshold
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        [Newtonsoft.Json.JsonProperty("DevID")]
        public string DeviceID { get; set; }

        /// <summary>
        /// 监控点ID列表
        /// </summary>
        [Newtonsoft.Json.JsonProperty("SID")]
        public List<string> IDs { get; set; }

        public DeviceGetThreshold() { }

        public DeviceGetThreshold(string devId, List<string> sId)
        {
            DeviceID = devId;
            IDs = new List<string>();
            foreach (string id in sId)
            {
                IDs.Add(id.Insert(id.Length, ":000"));
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("[{0}:", DeviceID);
            if (IDs != null && IDs.Count > 0)
            {
                foreach (string id in IDs)
                {
                    sb.Append(id).Append(",");
                }
                sb.Remove(sb.Length - 1, 1);
            }
            sb.Append("]");
            return sb.ToString();
            //return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
    }
}