﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public class TimeInfo
    {
        public string StrYear { get; set; }
        public string StrMonth { get; set; }
        public string StrDay { get; set; }
        public string StrHour { get; set; }
        public string StrMinute { get; set; }
        public string StrSecond { get; set; }
        public TimeInfo(string strYear,string strMonth,string strDay,string strHour,string strMinute,string strSecond)
        {
            StrYear = strYear;
            StrMonth = strMonth;
            StrDay = strDay;
            StrHour = strHour;
            StrMinute = strMinute;
            StrSecond = strSecond;
        }
        public TimeInfo(DateTime datetime)
        {    
            StrYear = datetime.Year.ToString("####");
            StrMonth = datetime.Month.ToString("0#");
            StrDay = datetime.Day.ToString("0#");
            StrHour = datetime.Hour.ToString("0#");
            StrMinute = datetime.Minute.ToString("0#");
            StrSecond = datetime.Second.ToString("0#");

        }
    }
}
