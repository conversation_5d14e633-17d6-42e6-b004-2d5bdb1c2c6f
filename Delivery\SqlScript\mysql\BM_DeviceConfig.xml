﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
		<procedure owner="" name="BM_DeviceConfigDel_Signal" grant="">
			<parameters>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_Signal a WHERE a.EquipmentTemplateId=@EquipmentTemplateId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigDel_SignalProperty" grant="">
			<parameters>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_SignalProperty a WHERE a.EquipmentTemplateId=@EquipmentTemplateId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigDel_SignalMeanings" grant="">
			<parameters>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_SignalMeanings a WHERE a.EquipmentTemplateId=@EquipmentTemplateId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigDel_Control" grant="">
			<parameters>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_Control a WHERE a.EquipmentTemplateId=@EquipmentTemplateId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigDel_ControlMeanings" grant="">
			<parameters>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_ControlMeanings a WHERE a.EquipmentTemplateId=@EquipmentTemplateId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigDel_Event" grant="">
			<parameters>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_Event a WHERE a.EquipmentTemplateId=@EquipmentTemplateId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigDel_EventCondition" grant="">
			<parameters>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_EventCondition a WHERE a.EquipmentTemplateId=@EquipmentTemplateId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigDel_EquipmentTemplate" grant="">
			<parameters>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_EquipmentTemplate a WHERE a.EquipmentTemplateId=@EquipmentTemplateId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigDel_Equipment" grant="">
			<parameters>
				<parameter name="StationId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_Equipment a WHERE a.StationId=@StationId and a.EquipmentId=@EquipmentId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigDel_EquipmentCMCC" grant="">
			<parameters>
				<parameter name="StationId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_EquipmentCMCC a WHERE a.StationId=@StationId and a.EquipmentId=@EquipmentId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigDel_FsuTSignalCMCC" grant="">
			<parameters>
				<parameter name="FSUID" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceID" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_FsuTSignalCMCC a WHERE a.FSUID=@FSUID and a.DeviceID=@DeviceID;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigUpdate_EquipmentTemplate" grant="">
			<parameters>
				<parameter name="EquipmentTemplateName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentCategory" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DevDescribe" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Model" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Brand" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				update TBL_EquipmentTemplate a
				set
				a.EquipmentTemplateName=@EquipmentTemplateName,
				a.EquipmentCategory=@EquipmentCategory,
				a.Description=@DevDescribe,
				a.EquipmentStyle=@Model,
				a.Vendor=@Brand
				where a.EquipmentTemplateId=@EquipmentTemplateId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigUpdate_Equipment" grant="">
			<parameters>
				<parameter name="DeviceName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceID" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentTemplateName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentCategory" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DevDescribe" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Model" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Brand" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="BeginRunTime" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="HouseId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="RatedCapacity" type="float" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="StationId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				update TBL_Equipment a
				set
				a.EquipmentName=@DeviceName,
				a.EquipmentNo=@DeviceID,
				a.EquipmentStyle=@Model,
				a.UsedDate=@BeginRunTime,
				a.Vendor=@Brand,
				a.EquipmentCategory=@EquipmentCategory,
				a.Description=@DevDescribe,
				a.HouseId=@HouseId,
				a.UpdateTime=now(),
				a.RatedCapacity=@RatedCapacity
				where
				a.StationId=@StationId and a.EquipmentId=@EquipmentId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigUpdate_EquipmentCMCC" grant="">
			<parameters>
				<parameter name="DeviceName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="RoomName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceSubType" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceType" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceID" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentTemplateName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentCategory" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DevDescribe" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Model" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Brand" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="BeginRunTime" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="HouseId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="RatedCapacity" type="float" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Version" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FSUID" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				update 	TBL_EquipmentCMCC a
				set
				a.DeviceName=@DeviceName,
				a.RoomName=@RoomName,
				a.DeviceType=@DeviceType,
				a.DeviceSubType=@DeviceSubType,
				a.Model=@Model,
				a.Brand=@Brand,
				a.RatedCapacity=@RatedCapacity,
				a.Version=@Version,
				a.BeginRunTime=@BeginRunTime,
				a.DevDescribe=@DevDescribe
				where a.FSUID=@FSUID and a.DeviceID=@DeviceID;
			</body>
		</procedure>
	</procedures>
</root>
