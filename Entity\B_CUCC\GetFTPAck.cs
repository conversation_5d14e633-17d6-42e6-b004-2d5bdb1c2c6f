﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class GetFTPAck : BMessage
    {
        public string UserName { get; set; }
        public string Password { get; set; }
        public EnumResult Result { get; set; }

        public GetFTPAck() : base()
        {
            MessageType = (int)BMessageType.GET_FTP_ACK;
        }
        public GetFTPAck(string suids, string surids, string userName, string password, EnumResult result) : base()
        {
            MessageType = (int)BMessageType.GET_FTP_ACK;
            SUId = suids;
            SURId = surids;
            UserName = userName;
            Password = password;
            Result = result;
        }
        public static GetFTPAck Deserialize(XmlDocument xmlDoc)
        {
            GetFTPAck getFTPParameterAck = null;
            try
            {
                string suid = xmlDoc.SelectSingleNode("/Response/Info/SUId").InnerText.Trim();
                string surid = xmlDoc.SelectSingleNode("/Response/Info/SURId").InnerText.Trim();
                string userName = xmlDoc.SelectSingleNode("/Response/Info/UserName").InnerText.Trim();
                string password = xmlDoc.SelectSingleNode("/Response/Info/Password").InnerText.Trim();
                string result = xmlDoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim();
                getFTPParameterAck = new GetFTPAck(suid, surid, userName, password, string.IsNullOrEmpty(result) ? EnumResult.FAILURE : (EnumResult)int.Parse(result));
                entityLogger.DebugFormat("GetFTPAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                getFTPParameterAck.StringXML = xmlDoc.InnerXml;
                return getFTPParameterAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetFTPAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetFTPAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                getFTPParameterAck = new GetFTPAck();
                getFTPParameterAck.ErrorMsg = ex.Message;
                getFTPParameterAck.StringXML = xmlDoc.InnerXml;
                return getFTPParameterAck;
            }
        }
        public override string ToString()
        {
            return String.Format("{0}, {1},{2},{3}, {4}: {5}",
                MessageId, (BMessageType)MessageType,MessageType, SUId, SURId, Result);
        }
    }
}
