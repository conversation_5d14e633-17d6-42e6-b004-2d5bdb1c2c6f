﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 写监控点的设置值应答
    /// </summary>
    public sealed class SetPointAck: BMessage
    {
        public EnumResult Result { get; private set; }

        public string FailureCause { get; private set; }

        public List<DeviceSetPointAck> Devices { get; private set; }

        #region SiteWeb配置

        public int MyStationId { get; set; }

        public int UserId { get; set; }
        public DateTime StartTime { get; set; }
        public int ControlId { get; set; }

        #endregion

        public SetPointAck(string fsuId, EnumResult result, string failureCause, List<DeviceSetPointAck> devices):base()
        {
            MessageType = (int)BMessageType.SET_POINT_ACK;

            FSUID = fsuId;
            Result = result;
            FailureCause = failureCause;
            Devices = devices;
        }

        public SetPointAck() : base() 
        {
            MessageType = (int)BMessageType.SET_POINT_ACK;
        }

        public static SetPointAck Deserialize(XmlDocument xmldoc)
        {
            SetPointAck setPointAck = null;
            try
            {
                string fsuId = xmldoc.SelectSingleNode("/Response/Info/FSUID").InnerText;
                string result = xmldoc.SelectSingleNode("/Response/Info/Result").InnerText;
                EnumResult enumResult = EnumResult.SUCCESS;
                if (result != null && result != "")
                {
                    enumResult = (EnumResult)int.Parse(result);
                }
                string failureCause = xmldoc.SelectSingleNode("/Response/Info/FailureCause").InnerText;

                XmlNodeList nodelist = xmldoc.SelectNodes("/Response/Info/DeviceList/Device");

                List<DeviceSetPointAck> deviceSetPointAckList = new List<DeviceSetPointAck>();

                foreach (XmlNode xn in nodelist)//遍历所有子节点 
                {
                    XmlElement xe = (XmlElement)xn;
                    string deviceId = xe.GetAttribute("ID");

                    List<TSignalMeasurementId> successList = new List<TSignalMeasurementId>();
                    List<TSignalMeasurementId> failList = new List<TSignalMeasurementId>();

                    XmlNode xns = xe.SelectSingleNode("SuccessList");
                    if (xns != null)
                    {
                        foreach (XmlNode xnSub in xns.ChildNodes)
                        {
                            TSignalMeasurementId tsmid = new TSignalMeasurementId();
                            tsmid.ID = xnSub.Attributes["ID"].Value.Trim();
                            string strSignalNumber = xnSub.Attributes["SignalNumber"].Value.Trim();
                            if (string.IsNullOrEmpty(strSignalNumber) || strSignalNumber.ToUpper() =="NULL")
                            {
                                entityLogger.ErrorFormat("SetStorageRuleAck.Deserialize():ID = {0}, SignalNumber is NullOrEmpty", tsmid.ID);
                            }
                            else
                            {
                                tsmid.SignalNumber = strSignalNumber;
                            }
                            successList.Add(tsmid);
                        }
                    }
                    XmlNode xnf = xe.SelectSingleNode("FailList");
                    if (xnf != null)
                    {
                        foreach (XmlNode xnSub in xnf.ChildNodes)
                        {
                            //failList.Add(xnSub.InnerText);
                            TSignalMeasurementId tsmid = new TSignalMeasurementId();
                            tsmid.ID = xnSub.Attributes["ID"].Value.Trim();
                            string strSignalNumber = xnSub.Attributes["SignalNumber"].Value.Trim();
                            if (string.IsNullOrEmpty(strSignalNumber) || strSignalNumber.ToUpper() == "NULL" )
                            {
                                entityLogger.ErrorFormat("SetStorageRuleAck.Deserialize():ID = {0}, SignalNumber is NullOrEmpty", tsmid.ID);
                            }
                            else
                            {
                                tsmid.SignalNumber = strSignalNumber;
                            }
                            failList.Add(tsmid);
                        }
                    }
                    DeviceSetPointAck deviceSetPointAck = new DeviceSetPointAck(deviceId, successList, failList);
                    deviceSetPointAckList.Add(deviceSetPointAck);
                }
                setPointAck = new SetPointAck(fsuId, enumResult, failureCause, deviceSetPointAckList);
                entityLogger.DebugFormat("SetPointAck.Deserialize(),xml:\r\n{0}", FormatXml(xmldoc.InnerXml));
                setPointAck.StringXML = xmldoc.InnerXml;
                return setPointAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetPointAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetPointAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmldoc.InnerXml));
                setPointAck = new SetPointAck();
                setPointAck.ErrorMsg = ex.Message;
                setPointAck.StringXML = xmldoc.InnerXml;
                return setPointAck;
            }            
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();

            sb.AppendFormat("{0}, {1}, {2}, {3}:",
                MessageId, (BMessageType)MessageType, FSUID, Devices != null ? Devices.Count : 0);
            sb.AppendLine();
            if (Devices != null)
            {
                foreach (DeviceSetPointAck item in Devices)
                {
                    sb.AppendFormat("{0}", item.ToString());
                    sb.AppendLine();
                }
            }

            return sb.ToString();
        }
    }
}
