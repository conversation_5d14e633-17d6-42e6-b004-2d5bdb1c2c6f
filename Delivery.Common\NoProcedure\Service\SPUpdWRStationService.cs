﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using MySqlX.XDevAPI.Relational;
using NPOI.HSSF.Record;
using Delivery.Common.NoProcedure.Service.CMCC;

namespace Delivery.Common.NoProcedure.Service
{
    public class SPUpdWRStationService
    {
        private static SPUpdWRStationService _instance = null;

        public static SPUpdWRStationService Instance
        {
            get
            {
                if (_instance == null) _instance = new SPUpdWRStationService();
                return _instance;
            }
        }
        public int UpdateStation(string WRStationId, string StructureId, string Province, string City, string County, string StationCategory, string StationName, string Address, string Remark, string ContractNo, string ProjectName, string loginId)
        {
            int res = -1;
            int UserId;
            string SWUserName;
            object myStatus = 1;
            string myCode;
            object swStationId = DBNull.Value;
            object oriCounty = DBNull.Value;
            int intCounty = Convert.ToInt32(County);
            int intStationCategory = Convert.ToInt32(StationCategory);
            object oriStationCategory = DBNull.Value;
            string lastString;
            string updateString;
            int SWStationCategory;
            if (string.IsNullOrEmpty(WRStationId))
            {
                res = -1;
                return res;
            }



            DbHelper dbHelper = new DbHelper();
            ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
            Dictionary<string, object> realParams = new Dictionary<string, object>();
            // 先查询是否有重复的站点
            realParams.Add("StationName", StationName);
            realParams.Add("WRStationId", WRStationId);
            DataTable temp_1 = execHelper.ExecDataTable("SP_Upd_WRStation_1", realParams);
            if (temp_1.Rows.Count > 0)
            {
                res = -1;
                return res;
            }
            DataTable temp_2 = execHelper.ExecDataTable("SP_Upd_WRStation_2", realParams);
            myStatus = CommonUtils.GetNullableValue(temp_2.Rows[0].Field<int?>("StationStatus"));
            myCode = temp_2.Rows[0]["StationCode"].ToString();
            swStationId = CommonUtils.GetNullableValue(temp_2.Rows[0].Field<int?>("SWStationId"));
            oriCounty = CommonUtils.GetNullableValue(temp_2.Rows[0].Field<int?>("County"));
            oriStationCategory = CommonUtils.GetNullableValue(temp_2.Rows[0].Field<int?>("StationCategory"));

            DataTable temp_3 = execHelper.ExecDataTable("SP_Upd_WRStation_3", realParams);
            realParams.Add("LogonId", loginId);
            lastString = temp_3.Rows[0][0].ToString();
            updateString = $"StructureId:{StructureId}" +
                          $"-StationName:{StationName}" +
                          $"-StationCategory:{StationCategory}" +
                          $"-Address:{Address}" +
                          $"-Remark:{Remark}" +
                          $"-ContractNo:{ContractNo}" +
                          $"-ProjectName:{ProjectName}";

            int WRIntStationId = Convert.ToInt32(WRStationId);
            int WRIntWRStationId = Convert.ToInt32(WRStationId);
            Public_StoredService.Instance.SP_WR_OperationRecord(WRIntStationId, StationName, 1, WRIntWRStationId, StationName, updateString, lastString, loginId);
            try
            {
                if ((int)myStatus == 3)
                {

                    realParams.Add("StructureId", StructureId);
                    realParams.Add("StationCategory", StationCategory);
                    realParams.Add("Province", Province);
                    realParams.Add("City", City);
                    realParams.Add("County", County);
                    realParams.Add("Address", Address);
                    realParams.Add("Remark", Remark);
                    realParams.Add("ContractNo", ContractNo);
                    realParams.Add("ProjectName", ProjectName);
                    execHelper.ExecDataTable("SP_Upd_WRStation_4", realParams);
                    realParams.Add("StationId", swStationId);
                    realParams.Add("SWStationId", swStationId);
                    DataTable temp_5 = execHelper.ExecDataTable("SP_Upd_WRStation_5", realParams);
                    SWStationCategory = (int)temp_5.Rows[0][0];
                    realParams.Add("SWStationCategory", SWStationCategory);
                    execHelper.ExecDataTable("SP_Upd_WRStation_6", realParams);
                    execHelper.ExecDataTable("SP_Upd_WRStation_7", realParams);
                    execHelper.ExecDataTable("SP_Upd_WRStation_8", realParams);
                    execHelper.ExecDataTable("SP_Upd_WRStation_9", realParams);
                    return 1;
                }
                else
                {
                    //realParams.Add("WRStationId", WRStationId);
                    realParams.Add("StructureId", StructureId);
                    //realParams.Add("StationName", StationName);
                    realParams.Add("StationCategory", StationCategory);
                    realParams.Add("Province", Province);
                    realParams.Add("City", City);
                    realParams.Add("County", County);
                    realParams.Add("Address", Address);
                    realParams.Add("Remark", Remark);
                    realParams.Add("ContractNo", ContractNo);
                    realParams.Add("ProjectName", ProjectName);
                    //realParams.Add("LogonId", loginId);
                    realParams.Add("StationCode", myCode);
                    realParams.Add("StationStatus", myStatus);
                    DataTable temp_10 = execHelper.ExecDataTable("SP_Upd_WRStation_10", realParams);
                    UserId = (int)temp_10.Rows[0]["UserId"];
                    SWUserName = temp_10.Rows[0]["UserName"].ToString();
                    if ((int)oriCounty != intCounty || (int)oriStationCategory != intStationCategory)
                    {
                        myCode = null;

                        Public_StoredService.Instance.SP_GenerateStationCode(intCounty, intStationCategory, out myCode);
                        if (myCode == null)
                        {
                            res = -2;
                            return res;
                        }
                    }
                    realParams.Add("myCode", myCode);
                    execHelper.ExecDataTable("SP_Upd_WRStation_11", realParams);
                    return 1;
                }
            }
            catch (System.Exception ex)
            {

                return -1;
            }
        }



    }
}
