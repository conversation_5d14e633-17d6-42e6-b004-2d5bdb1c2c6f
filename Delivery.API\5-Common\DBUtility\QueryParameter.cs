﻿

using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
//
//using Sybase.Data.AseClient;
//using ENPC.Kolo.DataServer;

namespace DM.TestOrder.Common.DBA
{
    public class QueryParameter {
        public QueryParameter(string name, DataType dataType, string value) {
            Name = name;
            DataType = dataType;
            Value = value;
        }

        public String Name {
            get;
            set;
        }

        public DataType DataType {
            get;
            set;
        }

        public String Value {
            get;
            set;
        }
    }

}
