﻿using System;
using Newtonsoft.Json;
using System.Data;
using Newtonsoft.Json.Linq;
using System.Text;

namespace Delivery.API
{
    public static class JsonHelper
    {
        public delegate void UpdateDataTable_SetRow(DataRow row, int index);

        public static string SerializeData(object data)
        {
            return JsonConvert.SerializeObject(data);
        }

        public static string DataTable2Json(DataTable dt)
        {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.Append("[");
            stringBuilder.Append("[");
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                stringBuilder.Append("[");
                for (int j = 0; j < dt.Columns.Count; j++)
                {
                    if (j == 0)
                    {
                        stringBuilder.Append("'");
                        stringBuilder.Append(dt.Rows[i][j].ToString());
                        stringBuilder.Append("',");
                    }
                    else
                    {
                        stringBuilder.Append(dt.Rows[i][j].ToString());
                    }
                }
                stringBuilder.Append("],");
            }
            StringBuilder expr_C2 = stringBuilder;
            expr_C2.Remove(expr_C2.Length - 1, 1);
            stringBuilder.Append("]");
            stringBuilder.Append("]");
            return stringBuilder.ToString();
        }

        public static string DataTabletoJson(DataTable dt)
        {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.Append("[");
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                stringBuilder.Append("[");
                for (int j = 0; j < dt.Columns.Count; j++)
                {
                    int num;
                    float num2;
                    if (int.TryParse(dt.Rows[i][j].ToString(), out num))
                    {
                        stringBuilder.Append(dt.Rows[i][j].ToString());
                    }
                    else if (float.TryParse(dt.Rows[i][j].ToString(), out num2))
                    {
                        stringBuilder.Append(dt.Rows[i][j].ToString());
                    }
                    else
                    {
                        stringBuilder.Append("'");
                        stringBuilder.Append(dt.Rows[i][j].ToString());
                        stringBuilder.Append("'");
                    }
                    if (j != dt.Columns.Count - 1)
                    {
                        stringBuilder.Append(",");
                    }
                }
                stringBuilder.Append("],");
            }
            StringBuilder expr_13F = stringBuilder;
            expr_13F.Remove(expr_13F.Length - 1, 1);
            stringBuilder.Append("]");
            return stringBuilder.ToString();
        }

        public static string DataTable2RealJson(DataTable dt)
        {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.Append("[");
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                stringBuilder.Append("{");
                for (int j = 0; j < dt.Columns.Count; j++)
                {
                    stringBuilder.Append("\"");
                    stringBuilder.Append(dt.Columns[j].ColumnName);
                    stringBuilder.Append("\":\"");
                    stringBuilder.Append(dt.Rows[i][j].ToString());
                    stringBuilder.Append("\",");
                }
                StringBuilder expr_96 = stringBuilder;
                expr_96.Remove(expr_96.Length - 1, 1);
                stringBuilder.Append("},");
            }
            StringBuilder expr_C7 = stringBuilder;
            expr_C7.Remove(expr_C7.Length - 1, 1);
            stringBuilder.Append("]");
            return stringBuilder.ToString();
        }

        public static string DataSetToJson(DataSet ds)
        {
            string text = string.Empty;
            try
            {
                if (ds.Tables.Count == 0)
                {
                    throw new Exception("DataSet中Tables为0");
                }
                text = "{";
                for (int i = 0; i < ds.Tables.Count; i++)
                {
                    text = string.Concat(new object[]
                    {
                        text,
                        "T",
                        i + 1,
                        ":["
                    });
                    for (int j = 0; j < ds.Tables[i].Rows.Count; j++)
                    {
                        text += "[";
                        for (int k = 0; k < ds.Tables[i].Columns.Count; k++)
                        {
                            int num;
                            float num2;
                            if (int.TryParse(ds.Tables[i].Rows[j][k].ToString(), out num))
                            {
                                text += ds.Tables[i].Rows[j][k].ToString();
                            }
                            else if (float.TryParse(ds.Tables[i].Rows[j][k].ToString(), out num2))
                            {
                                text += ds.Tables[i].Rows[j][k].ToString();
                            }
                            else
                            {
                                text = text + "'" + ds.Tables[i].Rows[j][k].ToString() + "'";
                            }
                            if (k != ds.Tables[i].Columns.Count - 1)
                            {
                                text += ",";
                            }
                        }
                        text += "]";
                        if (j != ds.Tables[i].Rows.Count - 1)
                        {
                            text += ",";
                        }
                    }
                    text += "]";
                    if (i != ds.Tables.Count - 1)
                    {
                        text += ",";
                    }
                }
                text += "}";
            }
            catch (Exception arg_24B_0)
            {
                throw new Exception(arg_24B_0.Message);
            }
            return text;
        }

        public static string DataSetToRealJson(DataSet ds)
        {
            string text = string.Empty;
            try
            {
                if (ds.Tables.Count == 0)
                {
                    throw new Exception("DataSet中Tables为0");
                }
                text = "{";
                for (int i = 0; i < ds.Tables.Count; i++)
                {
                    text = string.Concat(new object[]
                    {
                        text,
                        "T",
                        i + 1,
                        ":["
                    });
                    for (int j = 0; j < ds.Tables[i].Rows.Count; j++)
                    {
                        text += "{";
                        for (int k = 0; k < ds.Tables[i].Columns.Count; k++)
                        {
                            text = string.Concat(new string[]
                            {
                                text,
                                ds.Tables[i].Columns[k].ColumnName,
                                ":'",
                                ds.Tables[i].Rows[j][k].ToString(),
                                "'"
                            });
                            if (k != ds.Tables[i].Columns.Count - 1)
                            {
                                text += ",";
                            }
                        }
                        text += "}";
                        if (j != ds.Tables[i].Rows.Count - 1)
                        {
                            text += ",";
                        }
                    }
                    text += "]";
                    if (i != ds.Tables.Count - 1)
                    {
                        text += ",";
                    }
                }
                text += "}";
            }
            catch (Exception arg_1B9_0)
            {
                throw new Exception(arg_1B9_0.Message);
            }
            return text;
        }

        public static T DeserializeData<T>(string jsonString)
        {
            return JsonConvert.DeserializeObject<T>(jsonString);
        }

        public static JObject GetJsonObject(string jsonString)
        {
            return JObject.Parse(jsonString);
        }

        public static string GetJsonObject(JObject jsnValues, string filedName)
        {
            JToken jToken = jsnValues[filedName];
            if (jToken != null && jToken.Type != JTokenType.Null)
            {
                return Convert.ToString(jToken).Replace("\"", string.Empty);
            }
            return string.Empty;
        }

        public static void Json_UpdateDataTable(string jsonString, DataTable dt, JsonHelper.UpdateDataTable_SetRow addRow)
        {
            DataTable dataTable = JsonHelper.DeserializeData<DataTable>(jsonString);
            int num = 0;
            if (dataTable != null && dataTable.Rows.Count > 0)
            {
                bool flag = dt.Rows.Count == 0;
                foreach (DataRow dataRow in dataTable.Rows)
                {
                    DataRow dataRow2 = null;
                    if (flag)
                    {
                        dataRow2 = dt.NewRow();
                        if (addRow != null)
                        {
                            addRow(dataRow2, num);
                        }
                    }
                    else
                    {
                        dataRow2 = dt.Rows[num];
                    }
                    foreach (DataColumn dataColumn in dataTable.Columns)
                    {
                        dataRow2[dataColumn.ColumnName] = dataRow[dataColumn.ColumnName];
                    }
                    num++;
                    if (flag)
                    {
                        dt.Rows.Add(dataRow2);
                    }
                    else
                    {
                        flag = (dt.Rows.Count <= num);
                    }
                }
            }
            if (dt.Rows.Count > num)
            {
                for (int i = dt.Rows.Count - num; i > 0; i--)
                {
                    dt.Rows[dt.Rows.Count - i].Delete();
                }
            }
        }

        public static string DataTable2Array(DataTable dt)
        {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.Append("[[");
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                stringBuilder.Append("[\"");
                stringBuilder.Append(Convert.ToString(dt.Rows[i]["ItemName"]));
                stringBuilder.Append("\",");
                stringBuilder.Append(Convert.ToString(dt.Rows[i]["ItemValue"]));
                stringBuilder.Append("],");
            }
            if (stringBuilder.ToString() != "[[")
            {
                StringBuilder expr_A3 = stringBuilder;
                expr_A3.Remove(expr_A3.Length - 1, 1);
            }
            stringBuilder.Append("]]");
            return stringBuilder.ToString();
        }
    }
}
