﻿using DM.TestOrder.Common.DBA;
using DM.TestOrder.DAL;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web.Http;
using testorder.dal;

namespace DM.TestOrder.Controllers {
    //[MyRequestAuthorizeAttribute]
    public class InstallCompanyController : BaseApiController{

        //http://localhost:56324/api/InstallCompany?keyword=1
        public HttpResponseMessage Get(string keyword="") {
            keyword = SHelper.RegulateParam(keyword);
                

            Debug.WriteLine(keyword);
            var dt = WoInstallCompanyDal.GetByKeyword(keyword);
            var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(dt);

            return new HttpResponseMessage() {
                Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            };

        }
        //public HttpResponseMessage Get() {
        //    var dt = WoInstallCompanyDal.GetAll();
        //    var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(dt);

        //    return new HttpResponseMessage() {
        //        Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
        //    };

        //}

    }
}
