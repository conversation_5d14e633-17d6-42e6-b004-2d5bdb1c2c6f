﻿using BLL;
using Delivery.API;
using DM.TestOrder.Common.DBA;
using DM.TestOrder.DAL;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;




namespace DM.TestOrder.Controllers {

    public class DicSigCheckListController : BaseController {
        [HttpGet]
        public JsonResult Get() {
            var dt = WoDicSigCheckListDal.GetAll();
            DataTableColumnMapper.RenameColumns(dt, DicSigFieldMap);
            return new JsonResult(dt);
            //var sReturn = JsonConvert.SerializeObject(dt);
            //HttpResponseMessage result = new HttpResponseMessage {
            //    Content = new StringContent(sReturn, Encoding.GetEncoding("UTF-8"), "application/json")
            //};
            //return result;
        }

        private static readonly Dictionary<string, string> DicSigFieldMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "baseequipmentid", "BaseEquipmentId" },
            { "baseequipmentname", "BaseEquipmentName" },
            { "basetypeid", "BaseTypeId" },
            { "basetypename", "BaseTypeName" },
            { "checktype", "CheckType" },
            { "limitdown", "LimitDown" },
            { "limitup", "LimitUp" },
            { "note", "Note" },
            { "checkid", "CheckId" },
            { "ismust", "IsMust" }
        };

        [HttpPost]
        public JsonResult POST([FromBody] JObject value)
        {
            var IsMust = value["IsMust"].ToString(); // 是 或 否

            var rtnMsg = "";
            if (IsMust == "是")
            {
                if (string.IsNullOrEmpty(value["LimitDown"].ToString()) || string.IsNullOrEmpty(value["LimitUp"].ToString()))
                    rtnMsg = "设置为必测信号, 上限和下限不能为空";
                else
                {
                    var CheckId = int.Parse(value["CheckId"].ToString());
                    var LimitDown = float.Parse(value["LimitDown"].ToString());
                    var LimitUp = float.Parse(value["LimitUp"].ToString());

                    rtnMsg = WoDicSigCheckListDal.UpdateOne(CheckId, IsMust, LimitDown, LimitUp);
                }
            }
            else
            {
                if (!string.IsNullOrEmpty(value["LimitDown"].ToString()) || !string.IsNullOrEmpty(value["LimitUp"].ToString()))
                    rtnMsg = "设置为非必测信号, 上限和下限必须为空";
                else
                {
                    var CheckId = int.Parse(value["CheckId"].ToString());

                    rtnMsg = WoDicSigCheckListDal.UpdateOne(CheckId, IsMust, null, null);

                }
            }
            return new JsonResult(new
            {
                errormsg = rtnMsg
            });

            //var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject( new {
            //    errormsg = rtnMsg
            //});

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};
        }
    }
}
