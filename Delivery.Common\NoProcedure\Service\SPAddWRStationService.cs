﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service.CMCC;

namespace Delivery.Common.NoProcedure.Service
{
    public class SPAddWRStationService
    {
        private static SPAddWRStationService _instance = null;

        public static SPAddWRStationService Instance
        {
            get
            {
                if (_instance == null) _instance = new SPAddWRStationService();
                return _instance;
            }
        }
        public int InsertStation(string StructureId, string Province, string City, int County, int StationCategory, string StationName, string Address, string Remark, string ContractNo, string ProjectName, string loginId)
        {
            int res = -1;
            int UserId;
            string SWUserName;
            int myStatus = 1;
            int WRStationId;
            
            DbHelper dbHelper = new DbHelper();
            ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
            Dictionary<string, object> realParams = new Dictionary<string, object>();

            realParams.Add("StationName", StationName);
            // 先查询是否有重复的站点
            DataTable temp_1 = execHelper.ExecDataTable("SP_Add_WRStation_1", realParams);
            if (temp_1.Rows[0][0].ToString() == "1")
            {
                res = -1;
                return res;
            }

            realParams.Clear();
            realParams.Add("LogonId", loginId);
            DataTable temp_2 = execHelper.ExecDataTable("SP_Add_WRStation_2", realParams);
            UserId = Convert.ToInt32(temp_2.Rows[0][0].ToString());
            SWUserName = temp_2.Rows[0][1].ToString();


            string myCode;
            Public_StoredService.Instance.SP_GenerateStationCode (County, StationCategory, out myCode);
            if (myCode == null)
            {
                res = -2;
                return res;
            }
            realParams.Clear();
            realParams.Add("StructureId", StructureId);
            realParams.Add("myCode", myCode);
            realParams.Add("StationName", StationName);
            realParams.Add("StationCategory", StationCategory);
            realParams.Add("myStatus", myStatus);
            realParams.Add("Address", Address);
            realParams.Add("UserId", UserId);
            realParams.Add("SWUserName", SWUserName);
            realParams.Add("Province", Province);
            realParams.Add("City", City);
            realParams.Add("County", County);
            realParams.Add("Remark", Remark);
            realParams.Add("ContractNo", ContractNo);
            realParams.Add("ProjectName", ProjectName);
            execHelper.ExecDataTable("SP_Add_WRStation_3", realParams);
            realParams.Clear();
            realParams.Add("StationName", StationName);
            DataTable temp_4 = execHelper.ExecDataTable("SP_Add_WRStation_4", realParams);
            WRStationId = Convert.ToInt32(temp_4.Rows[0][0].ToString());
            Public_StoredService.Instance.SP_WR_OperationRecord(WRStationId, StationName,2, WRStationId,StationName,"","",loginId);
            res = 1;
            return res;
        }


    }
}
