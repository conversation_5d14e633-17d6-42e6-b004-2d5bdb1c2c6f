﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public enum BMessageType
    {
        /// <summary>
        /// 未定义
        /// </summary>
        UNDEFINED = 0,

        /// <summary>
        /// 注册/响应，FSU向LSC注册
        /// </summary>
        LOGIN = 101,
        LOGIN_ACK = 102,
        /// <summary>
        /// 登出/响应
        /// </summary>
        LOGOUT = 103,
        LOGOUT_ACK = 104,
        /// <summary>
        /// 设置采集服务器IP/响应
        /// </summary>
        SET_IP = 105,
        SET_IP_ACK = 106,

        /// <summary>
        /// 获取遥测量数据/响应
        /// </summary>
        GET_AIDATA = 201,
        GET_AIDATA_ACK = 202,

        /// <summary>
        /// 遥测量变化上报/响应
        /// </summary>
        SEND_AIDATA = 203,
        SEND_AIDATA_ACK = 204,

        /// <summary>
        /// 遥测量历史数据上报/响应
        /// </summary>
        SEND_HISAIDATA = 205,
        SEND_HISAIDATA_ACK = 206,

        /// <summary>
        /// 获取遥信量数据/响应
        /// </summary>
        GET_DIDATA = 301,
        GET_DIDATA_ACK = 302,

        /// <summary>
        /// 遥信量状态变化上报/响应
        /// </summary>
        //SEND_DI = 303,
        //SEND_DI_ACK = 304,

        SEND_DIDATA = 303,
        SEND_DIDATA_ACK=304,

        /// <summary>
        /// 遥信量历史数据上报/响应
        /// </summary>
        SEND_HISDIDATA = 305,
        SEND_HISDIDATA_ACK = 306,

        /// <summary>
        /// 获取遥调量参数/响应
        /// </summary>
        GET_AODATA = 401,
        GET_AODATA_ACK = 402,

        /// <summary>
        /// 遥调量参数设置/响应
        /// </summary>
        SET_AODATA = 403,
        SET_AODATA_ACK = 404,

        /// <summary>
        /// 获取遥控量信息/响应
        /// </summary>
        GET_DO = 501,
        GET_DO_ACK = 502,

        /// <summary>
        /// 下发控制命令/响应
        /// </summary>
        SET_DODATA = 503,
        SET_DODATA_ACK = 504,

        /// <summary>
        /// 获取告警量数据/响应
        /// </summary>
        GET_ALARM = 601,
        GET_ALARM_ACK = 602,

        /// <summary>
        /// 告警量上送/响应
        /// </summary>
        SEND_ALARM = 603,
        SEND_ALARM_ACK = 604,

        /// <summary>
        /// 告警量历史数据上报/响应
        /// </summary>
        SEND_HISALARM = 605,
        SEND_HISALARM_ACK = 606,

        /// <summary>
        /// 获取SU的FTP参数/响应
        /// </summary>
        GET_FTP = 701,
        GET_FTP_ACK = 702,

        /// <summary>
        /// 设置SU的FTP参数/响应
        /// </summary>
        SET_FTP = 703,
        SET_FTP_ACK = 704,

        /// <summary>
        /// 发送时钟消息/响应
        /// </summary>
        SET_TIME = 801,
        SET_TIME_ACK = 802,

        /// <summary>
        /// 获取SU的状态参数/响应
        /// </summary>
        GET_SUINFO = 901,
        GET_SUINFO_ACK = 902,

        /// <summary>
        /// 重启SU/响应
        /// </summary>
        SET_SUREBOOT = 1001,
        SET_SUREBOOT_ACK = 1002,

        /// <summary>
        /// 获取告警量属性
        /// </summary>
        GET_AlarmProperty = 1101,
        GET_AlarmProperty_ACK = 1102,

        /// <summary>
        /// 获取告警量属性
        /// </summary>
        SET_AlarmProperty = 1103,
        SET_AlarmProperty_ACK = 1104,

        /// <summary>
        /// 获取SU端口配置信息
        /// </summary>
        GET_SUPORT = 8001,
        GET_SUPORT_ACK = 8002,
    }
}
