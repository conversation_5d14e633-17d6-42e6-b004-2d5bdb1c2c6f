﻿namespace BDSTool.Entity.B
{
    using System;
    using System.Collections.Generic;
    
    public partial class TBL_FSU_SendDevConfData_TSignal
    {
        public decimal LogId { get; set; }
        public string FSUID { get; set; }
        public string DeviceID { get; set; }
        public string ID { get; set; }
        public Nullable<int> Type { get; set; }
        public string SignalNumber { get; set; }
        public string SignalName { get; set; }
        public Nullable<int> AlarmLevel { get; set; }
        public Nullable<double> Threshold { get; set; }
        public Nullable<double> AbsoluteVal { get; set; }
        public Nullable<double> RelativeVal { get; set; }
        public string Describe { get; set; }
        public string NMAlarmID { get; set; }
    }
}
