﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace DM.TestOrder.Model {
    public class ItemCheck{
        public int OrderCheckId {
            get;
            set;
        }
        public int EquipmentId{get;set;}
        #region ui
        //设备名称	
        public string EquipmentName {
            get;
            set;
        }
        //设备类型	
        public string EquipmentCategoryName {
            get;
            set;
        }
        #region 基类
        //基类设备类型	
        public string BaseEquipmentName {
            get;
            set;
        }


        //基类类别	
        public int CheckTypeId {
            get;
            set;
        }


        //基类类别	
        public string CheckType {
            get;
            set;
        }
        //基类名称	
        public string BaseTypeName {
            get;
            set;
        }

        //单位	
        public string Unit {
            get;
            set;
        }        
        #endregion

        #region 验收标准
        //标准下限	
        public Nullable<double> LimitDown {
            get;
            set;
        }
        //标准上限	
        public Nullable<double> LimitUp {
            get;
            set;
        }        
        #endregion

        #region 运营商信息
        public string EquipmentLogicClass {
            get;
            set;
        }

        //逻辑分类	：输入告警

        public string LogicClass {
            get;
            set;
        }
        //标准名	
        public string StandardName {
            get;
            set;
        }


        //信号类型	：        
        public string SignalType {
            get;
            set;
        }        
        #endregion

        #region 测试
        //测试结果	
        public int IsPass {
            get;
            set;
        }
        public string IsPassString {
            get;
            set;
        }
        
        //测试结果描述	
        public string PassNote {
            get;
            set;
        }
        //未测试通过原因
        public string PassFailReason {
            get;
            set;
        }
        
        #endregion

	    #endregion
     

  

       
    }
}
