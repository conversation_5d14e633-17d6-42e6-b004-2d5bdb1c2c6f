﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 获取FSU状态信息响应
    /// </summary>
    public sealed class GetFsuInfoAck: BMessage
    {
        public TFSUStatus FsuStatus { get; private set; }
        public EnumResult Result { get; private set; }
        public string FailureCause { get; private set; }

        public int MyHostId { get; set; }

        public GetFsuInfoAck(string fsuId, TFSUStatus fsuStatus, EnumResult result, string failureCause)
            : base()
        {
            MessageType = (int)BMessageType.GET_FSUINFO_ACK;

            FSUID = fsuId;
            FsuStatus = fsuStatus;
            Result = result;
            FailureCause = failureCause;

        }

        public GetFsuInfoAck() : base() 
        {
            MessageType = (int)BMessageType.GET_FSUINFO_ACK;
        }

        public static GetFsuInfoAck Deserialize(XmlDocument xmlDoc)
        {
            GetFsuInfoAck getFsuInfoAck = null;
            try
            {
                string fsuId = xmlDoc.SelectSingleNode("/Response/Info/FSUID").InnerText.Trim();
                string cpuUsage = xmlDoc.SelectSingleNode("/Response/Info/TFSUStatus/CPUUsage").InnerText.Trim();
                string memUsage = xmlDoc.SelectSingleNode("/Response/Info/TFSUStatus/MEMUsage").InnerText.Trim();
                string hardDiskUsage = xmlDoc.SelectSingleNode("/Response/Info/TFSUStatus/HardDiskUsage").InnerText.Trim();
                string resultString = xmlDoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim();
                string failureCause = xmlDoc.SelectSingleNode("/Response/Info/FailureCause").InnerText.Trim();
                EnumResult result;
                if (string.IsNullOrEmpty(resultString) || resultString.ToUpper() == "NULL")
                {
                    result = EnumResult.FAILURE;
                }
                else
                {
                    result = (EnumResult)int.Parse(resultString);
                }
                float? cpuFloat = null;
                if (!string.IsNullOrEmpty(cpuUsage) && cpuUsage.ToUpper()!= "NULL")
                {
                    cpuFloat = float.Parse(cpuUsage);
                }
                float? memFloat = null;
                if (!string.IsNullOrEmpty(memUsage) && memUsage.ToUpper() != "NULL")
                {
                    memFloat = float.Parse(memUsage);
                }
                float? hardDiskFloat = null;
                if (!string.IsNullOrEmpty(hardDiskUsage) && hardDiskUsage.ToUpper() != "NULL")
                {
                    hardDiskFloat = float.Parse(hardDiskUsage);
                }
                TFSUStatus status = new TFSUStatus(cpuFloat, memFloat, hardDiskFloat);
                getFsuInfoAck = new GetFsuInfoAck(fsuId, status, result, failureCause);
                entityLogger.DebugFormat("GetFsuInfoAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                getFsuInfoAck.StringXML = xmlDoc.InnerXml;
                return getFsuInfoAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetFsuInfoAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetFsuInfoAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                getFsuInfoAck = new GetFsuInfoAck();
                getFsuInfoAck.ErrorMsg = ex.Message;
                getFsuInfoAck.StringXML = xmlDoc.InnerXml;
                return getFsuInfoAck;
            }
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}, {2}, {3}: {4}, {5}", 
                MessageId, (BMessageType)MessageType, FSUID, FsuStatus.ToString(), Result, FailureCause);
        }
    }
}
