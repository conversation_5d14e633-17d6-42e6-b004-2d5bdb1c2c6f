﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
		<procedure owner="" name="BM_DeviceConfigDel_Signal" grant="">
			<parameters>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_Signal WHERE EquipmentTemplateId=@EquipmentTemplateId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigDel_SignalProperty" grant="">
			<parameters>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_SignalProperty WHERE EquipmentTemplateId=@EquipmentTemplateId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigDel_SignalMeanings" grant="">
			<parameters>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_SignalMeanings WHERE EquipmentTemplateId=@EquipmentTemplateId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigDel_Control" grant="">
			<parameters>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_Control WHERE EquipmentTemplateId=@EquipmentTemplateId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigDel_ControlMeanings" grant="">
			<parameters>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_ControlMeanings WHERE EquipmentTemplateId=@EquipmentTemplateId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigDel_Event" grant="">
			<parameters>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_Event WHERE EquipmentTemplateId=@EquipmentTemplateId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigDel_EventCondition" grant="">
			<parameters>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_EventCondition WHERE EquipmentTemplateId=@EquipmentTemplateId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigDel_EquipmentTemplate" grant="">
			<parameters>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_EquipmentTemplate WHERE EquipmentTemplateId=@EquipmentTemplateId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigDel_Equipment" grant="">
			<parameters>
				<parameter name="StationId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_Equipment WHERE StationId=@StationId and EquipmentId=@EquipmentId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigDel_EquipmentCMCC" grant="">
			<parameters>
				<parameter name="StationId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				DELETE FROM TBL_EquipmentCMCC WHERE StationId=@StationId and EquipmentId=@EquipmentId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigDel_FsuTSignalCMCC" grant="">
			<parameters>
				<parameter name="FSUID" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceID" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
        DELETE FROM TBL_FsuTSignalCMCC WHERE FSUID=CAST(@FSUID AS TEXT) and DeviceID=CAST(@DeviceID AS TEXT);
      </body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigUpdate_EquipmentTemplate" grant="">
			<parameters>
				<parameter name="EquipmentTemplateName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentCategory" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DevDescribe" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Model" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Brand" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				update TBL_EquipmentTemplate 
				set
				EquipmentTemplateName=@EquipmentTemplateName,
				EquipmentCategory=@EquipmentCategory,
				Description=@DevDescribe,
				EquipmentStyle=@Model,
				Vendor=@Brand
				where EquipmentTemplateId=@EquipmentTemplateId;
			</body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigUpdate_Equipment" grant="">
			<parameters>
				<parameter name="DeviceName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceID" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentTemplateName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentCategory" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DevDescribe" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Model" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Brand" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="BeginRunTime" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="HouseId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="RatedCapacity" type="float" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="StationId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
        update TBL_Equipment 
        set
        EquipmentName=@DeviceName,
        EquipmentNo=@DeviceID,
        EquipmentStyle=@Model,
        UsedDate=@BeginRunTime,
        Vendor=@Brand,
        EquipmentCategory=@EquipmentCategory,
        Description=@DevDescribe,
        HouseId=@HouseId,
        UpdateTime=now(),
        RatedCapacity=@RatedCapacity
        where
        StationId=@StationId and EquipmentId=@EquipmentId;
      </body>
		</procedure>
		<procedure owner="" name="BM_DeviceConfigUpdate_EquipmentCMCC" grant="">
			<parameters>
				<parameter name="DeviceName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="RoomName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceSubType" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceType" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceID" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentTemplateName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentCategory" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DevDescribe" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Model" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Brand" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="BeginRunTime" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="HouseId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="RatedCapacity" type="float" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Version" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentId" type="int" direction="Input" size="400" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FSUID" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				update 	TBL_EquipmentCMCC 
				set
				DeviceName=@DeviceName,
				RoomName=@RoomName,
				DeviceType=@DeviceType,
				DeviceSubType=@DeviceSubType,
				Model=@Model,
				Brand=@Brand,
				RatedCapacity=@RatedCapacity,
				Version=@Version,
				BeginRunTime=@BeginRunTime,
				DevDescribe=@DevDescribe
				where FSUID=@FSUID and DeviceID=@DeviceID;
			</body>
		</procedure>
	</procedures>
</root>
