﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>

		<procedure owner="" name="SP_ApproveWRFsuCUCC_GetInfo" grant="">
			<parameters>
				<parameter name="WRFsuId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
		
	          SELECT a.FsuStatus,a.FsuName,c.SWStationId,c.StationStatus,b.HouseStatus 
				FROM WR_FsuManagementCUCC a 
				INNER JOIN WR_HouseManagementCUCC b ON a.WRHouseId = b.WRHouseId
				INNER JOIN WR_StationManagementCUCC c ON b.WRStationId = c.WRStationId
				WHERE a.WRFsuId = :WRFsuId;
			  
         ]]>
			</body>
		</procedure>

		<procedure owner="" name="SP_ApproveWRFsuCUCC_insertMonitorUnit" grant="">
			<parameters>
				<parameter name="WRFsuId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
		
	         INSERT INTO TSL_MonitorUnit
				(
					MonitorUnitId,MonitorUnitName,MonitorUnitCategory,MonitorUnitCode,
					StationId,IpAddress,RunMode,ConnectState,UpdateTime,
					IsSync,IsConfigOK,AppCongfigId,CanDistribute,ENABLE,InstallTime
				)
				SELECT  :SWMonitorUnitId,:FsuName,10 MonitorUnitCategory, cast(:SWMonitorUnitId as varchar) MonitorUnitCode,
					c.SWStationId,a.IPAddress, 1 RunMode, 2 ConnectState, NOW() UpdateTime,
					0 IsSync, 0 IsConfigOK, 1 AppCongfigId, 1 CanDistribute, 1 ENABLE, a.ApplyTime
				FROM WR_FsuManagementCUCC a
				INNER JOIN WR_HouseManagementCUCC b ON a.WRHouseId = b.WRHouseId 
				INNER JOIN WR_StationManagementCUCC c ON b.WRStationId = c.WRStationId
				WHERE a.WRFsuId = :WRFsuId;
			  
         ]]>
			</body>
		</procedure>

		<procedure owner="" name="SP_ApproveWRFsuCUCC_insertMonitorUnitCUCC" grant="">
			<parameters>
				<parameter name="WRFsuId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
				  INSERT INTO TSL_MonitorUnitCUCC
				  (
				  StationId,MonitorUnitId,SUID,SUName,SURID,
				  UserName,PassWord,SUIP,FTPUserName,FTPPassWord
				  )
				  SELECT c.SWStationId, :SWMonitorUnitId, a.FsuCode, a.FsuName,a.FsuRId,
				  a.UserName, a.Password, a.IPAddress, a.FtpUserName, a.FtpPassword
				  FROM WR_FsuManagementCUCC a
				  INNER JOIN WR_HouseManagementCUCC b ON a.WRHouseId = b.WRHouseId
				  INNER JOIN WR_StationManagementCUCC c ON b.WRStationId = c.WRStationId
				  WHERE a.WRFsuId = :WRFsuId;
		  ]]>
			</body>
		</procedure>

		<procedure owner="" name="SP_ApproveWRFsuCUCC_insertPort" grant="">
			<parameters>
				<parameter name="SWPortId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
			  INSERT INTO TSL_Port(PortId,MonitorUnitId,PortNo,PortName,PortType,Setting,PhoneNumber,LinkSamplerUnitId,Description)
			  VALUES (:SWPortId, :SWMonitorUnitId,5,'COM5',5,'comm_host_dev.so','',0,'');
			]]>
			</body>
		</procedure>

		<procedure owner="" name="SP_ApproveWRFsuCUCC_getSamplerInfo" grant="">
			<parameters>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
			  SELECT  SamplerId,SamplerName,DllPath 
			  FROM TSL_Sampler
			  WHERE TSL_Sampler.SamplerType = 18 AND TSL_Sampler.SamplerName LIKE '%BInterface-HOST%' LIMIT 1;
		]]>
			</body>
		</procedure>


		<procedure owner="" name="SP_ApproveWRFsuCUCC_insertSamplerUnit" grant="">
			<parameters>
				<parameter name="SWSamplerUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWPortId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWSamplerId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SamplerUnitName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="swDllPath" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
			  INSERT INTO TSL_SamplerUnit
			  (SamplerUnitId,PortId,MonitorUnitId,SamplerId,ParentSamplerUnitId,SamplerType,SamplerUnitName,Address,SpUnitInterval,DllPath,ConnectState,UpdateTime)
			  VALUES
			  (:SWSamplerUnitId,:SWPortId,:SWMonitorUnitId,:SWSamplerId,0,18,:SamplerUnitName,1,2,:swDllPath,0,NOW());
			  ]]>
			</body>
		</procedure>

		<procedure owner="" name="SP_ApproveWRFsuCUCC_getEquipmentTemplateId" grant="">
			<parameters>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
			  SELECT  a.EquipmentTemplateId
			  FROM TBL_EquipmentTemplate a
			  WHERE a.EquipmentCategory = 99 AND a.EquipmentTemplateName = 'BInterface-HOST设备';
			   
		 ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsuCUCC_GetSomeFiledName11" grant="">
			<parameters>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
                    SELECT Min(a.HouseId) AS v_DefaultHouseId from TBL_House a where a.StationId = :SWStationId;
                ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_ApproveWRFsuCUCC_insertEquipment" grant="">
			<parameters>
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="swEquipmentId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="swEquipmentName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="swEquipmentTemplateId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWSamplerUnitId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="v_DefaultHouseId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
			  INSERT INTO TBL_Equipment (StationId, EquipmentId, EquipmentName,EquipmentNo, EquipmentCategory, EquipmentType, EquipmentClass,
			  EquipmentState, Property, EquipmentTemplateId, HouseId, MonitorUnitId, SamplerUnitId, DisplayIndex, ConnectState, UpdateTime, InstalledModule)
			  VALUES (:SWStationId, :swEquipmentId, :swEquipmentName,'', 99, 2, -1, 1, '',:swEquipmentTemplateId, :v_DefaultHouseId, :SWMonitorUnitId, :SWSamplerUnitId, 0, 2, NOW(),'');
		 ]]>
			</body>
		</procedure>

		<procedure owner="" name="SP_ApproveWRFsuCUCC_updateFsuManagementCUCC" grant="">
			<parameters>
				<parameter name="WRFsuId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
			  
				  UPDATE WR_FsuManagementCUCC
				  SET WR_FsuManagementCUCC.FsuStatus = 3
				  ,WR_FsuManagementCUCC.ApproveTime = NOW()
				  ,WR_FsuManagementCUCC.SWMonitorUnitId = :SWMonitorUnitId
				  ,WR_FsuManagementCUCC.RejectCause = ''
				  WHERE WR_FsuManagementCUCC.WRFsuId = :WRFsuId;
			
			   ]]>
			</body>
		</procedure>

		<procedure owner="" name="SP_ApproveWRFsuCUCC_insetConfigChangeMicroLogSWStation" grant="">
			<parameters>
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[

			  INSERT INTO TBL_ConfigChangeMicroLog(ObjectId,ConfigId,EditType,UpdateTime)
			  SELECT concat(cast(:SWStationId as varchar),'.',cast(:SWMonitorUnitId as varchar))  ObjectId, 15 ConfigId, 1 EditType, NOW() UpdateTime;
	  
			   ]]>
			</body>
		</procedure>

		<procedure owner="" name="SP_ApproveWRFsuCUCC_insetConfigChangeMicroLogSWMonitorUnit" grant="">
			<parameters>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWPortId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
			INSERT INTO TBL_ConfigChangeMicroLog(ObjectId,ConfigId,EditType,UpdateTime)
			SELECT concat(cast(:SWMonitorUnitId as varchar),'.',cast(:SWPortId as varchar))  ObjectId, 16 ConfigId, 1 EditType, NOW() UpdateTime;
	  
			   ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_ApproveWRFsuCUCC_insetConfigChangeMicroLogSamplerUnit" grant="">
			<parameters>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWPortId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWSamplerUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
				INSERT INTO TBL_ConfigChangeMicroLog(ObjectId,ConfigId,EditType,UpdateTime)
					SELECT concat(cast(:SWMonitorUnitId as varchar),'.',cast(:SWPortId as varchar),'.',cast(:SWSamplerUnitId  as varchar)) ObjectId, 17 ConfigId, 1 EditType, NOW() UpdateTime;
	  
			   ]]>
			</body>
		</procedure>

		<procedure owner="" name="SP_ApproveWRFsuCUCC_insetConfigChangeMicroLogEquipmentId" grant="">
			<parameters>
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="swEquipmentId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[

				INSERT INTO TBL_ConfigChangeMicroLog(ObjectId,ConfigId,EditType,UpdateTime)
				SELECT concat(cast(:SWStationId as varchar),'.',cast(:swEquipmentId as varchar))  ObjectId, 3 ConfigId, 1 EditType, NOW() UpdateTime;
	  
			   ]]>
			</body>
		</procedure>

		<procedure owner="" name="SP_ApproveWRFsuCUCC_insetConfigChangeMicroLogMonitorUnit" grant="">
			<parameters>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
						INSERT INTO TBL_ConfigChangeMacroLog(ObjectId,ConfigId,EditType,UpdateTime)
				SELECT cast(:SWMonitorUnitId as varchar)  ObjectId, 15 ConfigId, 2 EditType, NOW() UpdateTime;
	  
			   ]]>
			</body>
		</procedure>

		<procedure owner="" name="SP_ApproveWRFsuCUCC_updateConfigChangeMacroLog" grant="">
			<parameters>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
				
						UPDATE TBL_ConfigChangeMacroLog
						SET TBL_ConfigChangeMacroLog.EditType = 2, TBL_ConfigChangeMacroLog.UpdateTime = NOW();
				
	  
			   ]]>
			</body>
		</procedure>


		<procedure owner="" name="SP_ApproveWRFsuCUCC_insetMonitorUnitProjectInfo" grant="">
			<parameters>
				<parameter name="WRFsuId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
				  INSERT INTO TBL_MonitorUnitProjectInfo(StationId,MonitorUnitId,ProjectName,ContractNo,InstallTime)
				  SELECT :SWStationId , SWMonitorUnitId, ProjectName, ContractNo, ApplyTime
				  FROM WR_FsuManagementCUCC
				  WHERE WR_FsuManagementCUCC.WRFsuId = :WRFsuId;
			 ]]>
			</body>
		</procedure>

    <procedure owner="" name="SP_RejectWRFsuCUCC_FsuStatus" grant="">
      <parameters>
        <parameter name="WRFsuId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
					SELECT WR_FsuManagementCUCC.FsuStatus FsuStatus
	        FROM WR_FsuManagementCUCC 
	        WHERE WR_FsuManagementCUCC.WRFsuId = :WRFsuId;
				]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_RejectWRFsuCUCC_FsuManagement" grant="">
      <parameters>
        <parameter name="WRFsuId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="RejectCause" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
					UPDATE WR_FsuManagementCUCC
	        SET WR_FsuManagementCUCC.FsuStatus = 2 , WR_FsuManagementCUCC.RejectCause = :RejectCause
	        WHERE WR_FsuManagementCUCC.WRFsuId =  :WRFsuId; 
				]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_WRFsuByStationOrHouseCUCC_UserId" grant="">
      <parameters>
        <parameter name="LogonId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
					SELECT TBL_Account.UserId FROM TBL_Account WHERE TBL_Account.LogonId = :LogonId ; 
				]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_WRFsuByStationOrHouseCUCC_UserRoleMap" grant="">
      <parameters>
        <parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
			SELECT 'X' FROM TBL_UserRoleMap a WHERE a.UserId = :UserId AND a.RoleId = -1
			 ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_WRFsuByStationOrHouseCUCC_FsuManagement" grant="">
      <replaces>
      </replaces>
      <body>
        <![CDATA[
			SELECT DISTINCT UserId FROM WR_FsuManagementCUCC
			 ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_WRFsuByStationOrHouseCUCC_UserRoleMap" grant="">
      <parameters>
        <parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
			SELECT DISTINCT a.UserId FROM TBL_UserRoleMap a 
		  WHERE a.RoleId IN (SELECT RoleId FROM TBL_UserRoleMap b
			WHERE b.UserId = :UserId)
			 ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_WRFsuByStationOrHouseCUCC_Connect_FsuManagement" grant="">
      <parameters>
        <parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
			  SELECT a.WRFsuId, a.FsuName 
		    FROM WR_FsuManagementCUCC a
		    INNER JOIN ($[filterUser]) d ON a.UserId = d.UserId
		    WHERE a.FsuStatus = 3;		
			 ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_WRFsuByStationOrHouseCUCC_Connect_FsuManagement2" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
			  SELECT a.WRFsuId, a.FsuName 
		    FROM WR_FsuManagementCUCC a 
		    INNER JOIN WR_HouseManagementCUCC b ON a.WRHouseId = b.WRHouseId
		    INNER JOIN WR_StationManagementCUCC c ON b.WRStationId = c.WRStationId 
		    INNER JOIN ($[filterUser]) d ON a.UserId = d.UserId
		    WHERE c.WRStationId = :WRStationId AND a.FsuStatus = 3;
			 ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_WRFsuByStationOrHouseCUCC_iWRSatationId" grant="">
      <parameters>
        <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
			  SELECT a.WRStationId iWRSatationId
		    FROM WR_HouseManagementCUCC a
		    WHERE a.WRHouseId = :WRHouseId LIMIT 1;	
			 ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_WRFsuByStationOrHouseCUCC" grant="">
      <parameters>
        <parameter name="iWRSatationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
			  SELECT a.WRFsuId, a.FsuName 
		    FROM WR_FsuManagementCUCC a 
		    INNER JOIN WR_HouseManagementCUCC b ON a.WRHouseId = b.WRHouseId
		    INNER JOIN WR_StationManagementCUCC c ON b.WRStationId = c.WRStationId 
		    INNER JOIN ($[filterUser]) d ON a.UserId = d.UserId
		    WHERE c.WRStationId = :iWRSatationId AND a.FsuStatus = 3;
			 ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_WRFsuByStationOrHouseCUCC2" grant="">
      <parameters>
        <parameter name="iWRSatationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
			  SELECT a.WRFsuId, a.FsuName 
		    FROM WR_FsuManagementCUCC a
		    INNER JOIN ($[filterUser]) d ON a.UserId = d.UserId
		    WHERE 1=2 and a.FsuStatus = 3;		
			 ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_WRFsuByStationOrHouseCUCC3" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
			  SELECT a.WRFsuId, a.FsuName 
		    FROM WR_FsuManagementCUCC a 
		    INNER JOIN WR_HouseManagementCUCC b ON a.WRHouseId = b.WRHouseId
		    INNER JOIN WR_StationManagementCUCC c ON b.WRStationId = c.WRStationId 
		    INNER JOIN ($[filterUser]) d ON a.UserId = d.UserId
		    WHERE c.WRStationId = :WRStationId AND b.WRHouseId = :WRHouseId 
		    AND a.FsuStatus = 3	;
			 ]]>
      </body>
    </procedure>
	</procedures>
</root>
