﻿using BDSTool.BLL.Convert;
using BDSTool.BLL.S2;
using BDSTool.Entity.B;
using BDSTool.Entity.S2;

using Common.Logging.Pro;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.BLL.B
{
    public partial class TSignalBiz
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static string GetDescribe(TSignal tsig, Dictionary<int,string> dicStandard) {
            //信号类型，其中：1为遥控信号，4为状态信号（遥信）
            //格式举例：0&正常;1&告警
            int bid;
            if (int.TryParse(tsig.ID, out bid) == false) {
                logger.WarnFormat("GetDescribe(); tsig.ID={0}", tsig.ID);
                return null;
            }

            //20171011 fix test bug, 导入的ID没有序号
            //int idBase=bid/1000;
            int idBase = bid;
            int idStandard=idBase*1000+1;
            
            string desVal;
            if (!dicStandard.TryGetValue(idStandard, out desVal)) {
                logger.WarnFormat("GetDescribe(); tsig.ID={0}; ID-Standard={1}", tsig.ID, idStandard);
            }

            return desVal;
        }

        public static bool ValidateDescribe(TSignal tsig) {
            //信号类型，其中：1为遥控信号，4为状态信号（遥信）
            //格式举例：0&正常;1&告警 
            //if (tsig.Type == EnumType.DI || tsig.Type == EnumType.DO)
            //    if (string.IsNullOrEmpty(tsig.Describe) || tsig.Describe.IndexOf('&') == -1)
            //        return false;

            if (tsig.Type == EnumType.DI)
                if (string.IsNullOrEmpty(tsig.Describe) || tsig.Describe.IndexOf('&') == -1)
                    return false;

            if (tsig.Type == EnumType.DO) {
                if (string.IsNullOrEmpty(tsig.Describe)) {
                    return true;
                }

                if (tsig.Describe.IndexOf('&') == -1)
                    return false;
            }
            return true;
        }

        //public static bool AddTSignals(int equipmentTemplateId, List<TSignalEx> listTSignalsEx) {
        //    try {
        //        var signals = new List<TBL_Signal>();
        //        var events = new List<TBL_Event>();
        //        var controls = new List<TBL_Control>();

        //      //----------------------------------------------------------------------------------
        //    if (!SiteWebSECConvert.ListOfTSignals2NewSiteWebSECs(equipmentTemplateId, listTSignalsEx, ref signals, ref events, ref controls, IsAppend: true))
        //            return false;

        //        if (signals.Count > 0) {
        //            if (!SignalBiz.SaveEntityList(signals))
        //                return false;
        //        }

        //        if (events.Count > 0) {
        //            if (!EventBiz.SaveEntityList(events))
        //                return false;
        //        }

        //        if (controls.Count > 0) {
        //            if (!ControlBiz.SaveEntityList(controls))
        //                return false;
        //        }

        //        //if (!EquipmentTemplateBiz.AddSignals(equipmentTemplateId, tsignals, firstDispIndex)) {
        //        //   logger.ErrorFormat("AddTSignals(); Failed to Add sigs; equipmentTemplateId={0}",equipmentTemplateId );
        //        //    return false;
        //        //}

        //        //-------------------------------------------------------------------------------------------------------------------------
        //        //var controls = tsignals.Where(o => o.Type == EnumType.AO || o.Type == EnumType.DO).ToList();
        //        //rtn = DBHelper.ExecuteScalar("select max(dispIndex) from TBL_Control where EquipmentTemplateId=" + equipmentTemplateId);
        //        //var firstDispIndex4Control = int.Parse(rtn) + 1;
        //        //if (!EquipmentTemplateBiz.AddControls(equipmentTemplateId, controls, firstDispIndex4Control)) {
        //        //   logger.ErrorFormat("AddTSignals(); Failed to Add controls; equipmentTemplateId={0}",equipmentTemplateId );
        //        //    return false;
        //        //}
        //        ////-------------------------------------------------------------------------------------------------------------------------
        //        //var events = tsignals.Where(o => o.Type == EnumType.ALARM).ToList();
        //        //rtn = DBHelper.ExecuteScalar("select max(dispIndex) from TBL_Event where EquipmentTemplateId=" + equipmentTemplateId);
        //        //var firstDispIndex4Event = int.Parse(rtn) + 1;
        //        //if (!EquipmentTemplateBiz.AddEvents(equipmentTemplateId, events, firstDispIndex4Event)) {
        //        //    logger.ErrorFormat("AddTSignals(); Failed to Add Events; equipmentTemplateId={0}", equipmentTemplateId);
        //        //    return false;
        //        //}         
        //    }

        //    catch (Exception ex) {
        //        logger.ErrorFormat("AddTSignals();equipmentTemplateId={0}, Error={1}; ", equipmentTemplateId, ex.Message);
        //        logger.Error(ex.StackTrace);
        //        return false;
        //    }

        //    return true;
        //}

    }
}
