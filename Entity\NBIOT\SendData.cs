﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 上报实时数据
    /// </summary>
    public class SendData : BMessage
    {
         //public List<TSemaphore<TRealTimeData>> Values { get; set; }

        public List<Device> Values { get; set; }

        public SendData(string fsuId, List<Device> values)
            : base()
        {
            MessageType = (int)BMessageType.SEND_DATA;

            FSUID = fsuId;
            Values = values;
        }

        public SendData()
            : base()
        {
            MessageType = (int)BMessageType.SEND_DATA;

            //Device dev = new Device();
            //dev.PK_Type = BMessageType.SEND_DATA.ToString();
        }

        public static SendData Deserialize(string json)
        {
            SendData info = null;
            try
            {
                info = JsonConvert.DeserializeObject<SendData>(json);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SendData.Deserialize:{0}" , ex.Message);
                entityLogger.ErrorFormat("SendData.Deserialize:{0}" , ex.StackTrace);
                info = new SendData();
                info.ErrorMsg = ex.Message;
                info.StringXML = json;
            }
            return info;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0},{1},{2}:", MessageId, MessageType, FSUID);
            sb.AppendLine();
            if (Values != null)
            {
                foreach (Device device in Values)
                {
                    sb.AppendFormat("{0}\r\n", device.ToString());
                }
            }
            return sb.ToString();
        }
    }
}