﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 设备采集点标识
    /// </summary>
    public class TSignalMeasurementId
    {
        /// <summary>
        /// 监控点ID
        /// </summary>
        [Newtonsoft.Json.JsonIgnore]
        public string ID { get; set; }

        /// <summary>
        /// 同设备同类监控点顺序号
        /// </summary>
        [Newtonsoft.Json.JsonIgnore]
        public string SignalNumber { get; set; }

        public TSignalMeasurementId() { }

        public override string ToString()
        {
            return string.Format("[{0},{1}]", ID,SignalNumber);
        }
    }
}
