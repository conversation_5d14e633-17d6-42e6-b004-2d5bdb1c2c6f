﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;

namespace Delivery.Common.NoProcedure.Service
{
    public class WOBGetSiteCodeService
    {
        private static WOBGetSiteCodeService _instance = null;

        public static WOBGetSiteCodeService Instance
        {
            get
            {
                if (_instance == null) _instance = new WOBGetSiteCodeService();
                return _instance;
            }
        }
        public string GetData(int StationId)
        {

            string res = "";

            // 变量定义
            int StandardType;
         
            StandardType = 1; // 假设这个方法返回一个int值

            DbHelper dbHelper = new DbHelper();
            ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
            Dictionary<string, object> realParams = new Dictionary<string, object>();
            realParams.Add("StationId", StationId);
            if (StandardType == 1)
            {
                DataTable temp = execHelper.ExecDataTable("WO_BGetSiteCode_1", realParams);
                if (temp.Rows.Count > 0)
                {
                    res = temp.Rows[0]["SiteID"].ToString();
                }
            }
            return res;
        }


    }
}
