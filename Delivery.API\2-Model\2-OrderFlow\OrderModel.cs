﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace DM.TestOrder.Model {
    

    public partial class OrderModel {
        public int OrderId;
        public int OrderState;
        public string OrderStateString {
            get {
                switch (OrderState) {
                    case 1:
                        return "入网申请";
                    case 2:
                        return "专家组审核";
                    case 3:
                        return "入网复审";
                    case 4:
                        return "归档";
                    default:
                        return "exception";
                }
            }
        }

        public SectionOrder SectionOrder = new SectionOrder();
        public SectionExpertApprove SectionExpertApprove = new SectionExpertApprove();
        public SectionFinalApprove SectionFinalApprove = new SectionFinalApprove();

        public SectionFlow SectionFlow = new SectionFlow(); 
    }
}
