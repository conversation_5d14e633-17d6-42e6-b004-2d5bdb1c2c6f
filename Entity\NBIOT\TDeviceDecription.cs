﻿namespace ENPC.Kolo.Entity.NBIOT
{
    public class TDeviceDecription
    {
        public TDeviceDecription() { }

        public TDeviceDecription(string devId, string[] sId)
        {
            DevID = devId;
            SID = sId;
        }

        public string DevID { get; set; }

        public string[] SID { get; set; }

        public override string ToString()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
    }
}