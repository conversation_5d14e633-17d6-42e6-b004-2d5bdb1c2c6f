namespace BDSTool.Entity.S2
{
    using System;
    using System.Collections.Generic;
    //SamplerId	SamplerName	SamplerType	DllPath

    public partial class TSL_SamplerUnitMini
    {
        public int SamplerUnitId { get; set; }
        public int PortId { get; set; }
        public int MonitorUnitId { get; set; }
        public int SamplerId { get; set; }        

        public int SamplerType { get; set; }
        public string SamplerUnitName { get; set; }        
        public string DllPath { get; set; }
        public string Description { get; set; }
    }
}
