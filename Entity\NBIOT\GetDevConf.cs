﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class GetDevConf : BMessage
    {
        [Newtonsoft.Json.JsonProperty(Order = 1)]
        public string DevID { get; set; }

        [Newtonsoft.Json.JsonProperty(Order = 2)]
        public string SID { get; set; }

        public GetDevConf() : base()
        {
            MessageType = (int)BMessageType.GET_DEV_CONF;
        }

        public GetDevConf(string fsuId, string devId, string sId) : base()
        {
            MessageType = (int)BMessageType.GET_DEV_CONF;
            DevID = devId;
            SID = sId;
            FSUID = fsuId;
        }
    }
}