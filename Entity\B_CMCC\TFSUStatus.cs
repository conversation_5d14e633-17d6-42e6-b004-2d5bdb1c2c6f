﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B_CMCC
{
    public class TFSUStatus
    {
        /// <summary>
        /// CPU使用率
        /// </summary>
        public float? CPUUsage { get; private set; }

        /// <summary>
        /// 内存使用率
        /// </summary>
        public float? MEMUsage { get; private set; }

        /// <summary>
        /// FSU硬盘占用率（含SD卡等存储介质）
        /// </summary>
        public float? HardDiskUsage { get; private set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="cpuUsage">CPU使用率</param>
        /// <param name="memUsage">内存使用率</param>
        public TFSUStatus(float? cpuUsage, float? memUsage, float? hardDiskUsage)
        {
            CPUUsage = cpuUsage;
            MEMUsage = memUsage;
            HardDiskUsage = hardDiskUsage;
        }

        public override string ToString()
        {
            return string.Format("{0:#0.0},{1:#0.0},{2:#0.0}", CPUUsage, MEMUsage, HardDiskUsage);
        }

    }
}
