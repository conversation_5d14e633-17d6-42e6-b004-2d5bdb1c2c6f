﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace DM.TestOrder.Controllers {
    public class WithExtensionMultipartFormDataStreamProvider : MultipartFormDataStreamProvider {
        public string FSaveName { get; set; }
        public string FOriginalName;

        public WithExtensionMultipartFormDataStreamProvider(string rootPath, string toSaveFileName)
            : base(rootPath) {
            this.FSaveName = toSaveFileName;
        }

        public override string GetLocalFileName(System.Net.Http.Headers.HttpContentHeaders headers) {

            FOriginalName = GetValidFileName(headers.ContentDisposition.FileName);

            string extension = !string.IsNullOrWhiteSpace(headers.ContentDisposition.FileName) ? Path.GetExtension(FOriginalName) : "";
            return FSaveName + extension.ToLower();
        }

        private string GetValidFileName(string filePath) {
            char[] invalids = System.IO.Path.GetInvalidFileNameChars();
            return String.Join("_", filePath.Split(invalids, StringSplitOptions.RemoveEmptyEntries)).TrimEnd('.');
        }
    }  
}
