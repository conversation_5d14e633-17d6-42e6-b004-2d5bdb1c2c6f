﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.6.5.请求监控点门限数据
    /// </summary>
    public sealed class GetThreshold:BMessage
    {
        public List<DeviceGetThreshold> Values { get; set; }

        public GetThreshold(string fsuId, List<DeviceGetThreshold> values):base()
        {
            MessageType = (int)BMessageType.GET_THRESHOLD;

            FSUID = fsuId;
            Values = values;
        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmldoc = CreateXmlDocument();
                AppendPK_Type(xmldoc, "Request", BMessageType.GET_THRESHOLD.ToString());

                XmlElement xe2 = xmldoc.CreateElement("Info");
                XmlElement xe21 = xmldoc.CreateElement("FSUID");
                xe21.InnerText = FSUID;
                xe2.AppendChild(xe21);

                XmlElement xe22 = xmldoc.CreateElement("DeviceList");
                foreach (DeviceGetThreshold device in Values)
                {
                    XmlElement xe221 = xmldoc.CreateElement("Device");
                    xe221.SetAttribute("ID", device.DeviceID);
                    if (device.IDs != null && device.IDs.Count > 0)
                    {
                        foreach (string id in device.IDs)
                        {
                            XmlElement xe2211 = xmldoc.CreateElement("ID");
                            xe2211.InnerText = id;
                            xe221.AppendChild(xe2211);
                        }
                    }
                    xe22.AppendChild(xe221);
                }
                xe2.AppendChild(xe22);

                XmlNode root = xmldoc.SelectSingleNode("Request");
                root.AppendChild(xe2);

                StringXML = xmldoc.InnerXml;
                entityLogger.DebugFormat("GetThreshold.Serialize(), xml:\r\n{0}", FormatXml(StringXML));
                return xmldoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetThreshold.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetThreshold.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}, {2}: ",
                MessageId, (BMessageType)MessageType, FSUID);
            foreach (DeviceGetThreshold value in Values)
            {
                sb.AppendFormat("{0}", value.ToString());
            }
            return sb.ToString();
        }
    }

    /// <summary>
    /// 请求设备监控点数据结构
    /// </summary>
    public class DeviceGetThreshold
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceID { get; set; }

        /// <summary>
        /// 监控点ID号列表
        /// </summary>
        public List<string> IDs { get; set; }

        public DeviceGetThreshold(string deviceId, List<string> ids)
        {
            DeviceID = deviceId;
            IDs = ids;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("[{0}:", DeviceID);
            if (IDs != null && IDs.Count > 0)
            {
                foreach (string id in IDs)
                {
                    sb.Append(id).Append(",");
                }
                sb.Remove(sb.Length - 1, 1);
            }
            sb.Append("]");
            return sb.ToString();
        }

    }
}
