﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
		<procedure owner="" name="CheckItem_EquipmentId" grant="">
			<parameters>
				<parameter name="OrderCheckId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
            SELECT BaseTypeId,EquipmentId FROM WO_TestOrderEquipItemCheckList 
            where WO_TestOrderEquipItemCheckList.OrderCheckId=@OrderCheckId;
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="CheckItem_OrderInfo" grant="">
			<parameters>
				<parameter name="OrderCheckId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
            SELECT OrderCheckId, OrderId, CheckTypeId, CheckType, HasConfig, IsPass, PassNote, PassFailReason, EquipmentId, 
	        EquipmentName, EquipmentCategoryName, BaseEquipmentId, BaseEquipmentName, 
	        BaseTypeId, BaseTypeName, Unit, LimitDown, LimitUp, Note, 
	        CheckId, EquipmentLogicClass, LogicClass, StandardName, SignalType, SamplerValue, SamplerTime
	        FROM WO_TestOrderEquipItemCheckList
	        where WO_TestOrderEquipItemCheckList.OrderCheckId=@OrderCheckId;
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="CheckItem_AllConfig" grant="">
			<parameters>
				<parameter name="OrderCheckId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<parameters>
				<parameter name="BaseTypeId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
            SELECT s.DisplayIndex, 
	        e.EquipmentId, s.EquipmentTemplateId, 
	        ec.EventId, EventConditionId, EventName,
	        ec.BaseTypeId,
	        CASE 
		        WHEN TRUNC(ec.BaseTypeId / 1000) = TRUNC(@BaseTypeId / 1000) THEN 1 
		        ELSE 0 
		    END AS ToBeCheck
	        FROM TBL_Event s
	        INNER JOIN WO_TestOrderEquipItemCheckList c ON c.OrderCheckId=@OrderCheckId
	        INNER JOIN TBL_Equipment e ON e.EquipmentId= c.EquipmentId AND e.EquipmentTemplateId = s.EquipmentTemplateId
	        INNER JOIN TBL_EventCondition ec on ec.EventId=s.EventId 
	        ORDER BY ToBeCheck desc, s.DisplayIndex;
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="CheckItem_EventByStartTime" grant="">
			<parameters>
				<parameter name="OrderCheckId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<parameters>
				<parameter name="BaseTypeId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<parameters>
				<parameter name="EventStartTime1" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<parameters>
				<parameter name="EquipmentId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
            SELECT EventId, EventConditionId, EventName, BaseTypeId, BaseTypeName, StartTime,EndTime,
	        CASE 
		        WHEN TRUNC(ec.BaseTypeId / 1000) = TRUNC(@BaseTypeId / 1000) THEN 1 
		        ELSE 0 
		    END AS ToBeCheck
	        from TBL_ActiveEvent  e
	        where e.EquipmentId=@EquipmentId and e.StartTime>=@EventStartTime1
	        union all 
	        SELECT EventId,  EventConditionId, EventName, BaseTypeId, BaseTypeName, StartTime,EndTime,
	        CASE 
		        WHEN TRUNC(ec.BaseTypeId / 1000) = TRUNC(@BaseTypeId / 1000) THEN 1 
		        ELSE 0 
		    END AS ToBeCheck
	        from TBL_HistoryEvent  e
	        where e.EquipmentId=@EquipmentId and e.StartTime>=@EventStartTime1
	        ORDER BY ToBeCheck desc, EventName;
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="TestOrder_RoleInfo" grant="">
			<parameters>
				<parameter name="UserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
            SELECT m.RoleId,r.RoleName
	        from TBL_Account a
	        INNER JOIN TBL_UserRoleMap m ON a.UserId=m.UserId
	        INNER JOIN TBL_UserRole  r ON m.RoleId=r.RoleId
	        WHERE a.UserId= @UserId;
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="TestOrder_IsAdmin" grant="">
			<body>
				<![CDATA[ 
            SELECT OrderState, Count(1) as OrderCount
		    FROM WO_TestOrder
		    WHERE WO_TestOrder.OrderState=3
		    group by WO_TestOrder.OrderState;
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="TestOrder_NotAdmin" grant="">
			<parameters>
				<parameter name="UserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
            SELECT OrderState, Count(1) as OrderCount
		    FROM WO_TestOrder
		    WHERE (WO_TestOrder.OrderState =1 or WO_TestOrder.OrderState =2)  AND WO_TestOrder.ApplyUserId=@UserId	
		    group by WO_TestOrder.OrderState;	
            ]]>
			</body>
		</procedure>

		<procedure owner="" name="WO_GetWRStationCategoryName_FuncBGetType" grant="">
			<body>
				<![CDATA[ 
			SELECT CAST(ConfigValue AS INTEGER) AS BType 
			FROM TBL_SysConfig  
			WHERE ConfigKey = 'StandardCategory';
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="WO_GetWRStationCategoryName_StationCategoryName1" grant="">
			<parameters>
				<parameter name="stationId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
            SELECT ItemValue StationCategoryName FROM WR_StationManagement s 
            INNER JOIN WR_DataItem d  ON d.EntryId = 6 AND s.StationCategory = d.ItemId WHERE  SWStationId=@stationId;	
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="WO_GetWRStationCategoryName_StationCategoryName3" grant="">
			<parameters>
				<parameter name="stationId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
            SELECT ItemValue StationCategoryName FROM WR_StationManagementCUCC s 
            INNER JOIN WR_DataItem d  ON d.EntryId = 6 AND s.StationCategory = d.ItemId WHERE  SWStationId=@stationId;	
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="WO_GetWRStationCategoryName_StationCategoryName" grant="">
			<parameters>
				<parameter name="stationId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
            SELECT ItemValue StationCategoryName
			FROM TBL_Station s, TBL_DataItem d	
			WHERE s.StationId =@stationId and  d.EntryId=71 and d.ItemId=s.StationCategory;	
            ]]>
			</body>
		</procedure>
	
        <procedure owner="" name="WO_Equipment_Query_Role" grant="">
			<parameters>
				<parameter name="UserId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
            SELECT m.RoleId,RoleName
	        from TBL_Account a
	        INNER JOIN TBL_UserRoleMap m ON a.UserId=m.UserId
	        INNER JOIN TBL_UserRole  r ON m.RoleId=r.RoleId
	        WHERE a.UserId=@UserId;	
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="WO_Equipment_Query_AllInfo" grant="">
			<parameters>
				<parameter name="OrderType" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="UserId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="StationId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="HouseName" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			    <parameter name="EquipmentName" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
		        <parameter name="EquipmentNo" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
		    </parameters>
			<body>
				<![CDATA[ 
            SELECT e.EquipmentId, 
		    h.HouseName, 
		    r.RoomID, 
		    m.MonitorUnitName, 
		    f.FSUID,
		    e.EquipmentName, e.EquipmentNo ,
		    d.ItemValue EquipmentCategoryName, 
		    e.Vendor, 
		    e.EquipmentStyle  
		    FROM TBL_Equipment e
		    INNER JOIN TBL_House h ON  h.StationId=e.StationId and h.HouseId=e.HouseId
		    Left join TBL_RoomCMCC r on h.StationId=r.StationId and h.HouseId=r.HouseId
		    Left JOIN TSL_MonitorUnitCMCC f on e.MonitorUnitId=f.MonitorUnitId 
		    INNER JOIN TSL_MonitorUnit m ON m.MonitorUnitId=e.MonitorUnitId
		    inner join TBL_DataItem d on d.EntryId=7 and d.ItemId=e.EquipmentCategory
		    WHERE e.StationId=@StationId
		    and (HouseName ='' OR HouseName like @HouseName)
		    and (EquipmentName ='' OR EquipmentName like @EquipmentName)
		    and (EquipmentNo ='' OR EquipmentNo like @EquipmentNo)
		    and 
		   (@OrderType=1 or 
			not exists (select 1 from WO_TestOrderEquipItem ei
			inner join WO_TestOrder t on  
									OrderState<=4  
									and ei.OrderId = t.OrderId and t.OrderType=0
			where ei.EquipmentId=e.EquipmentId)
		    )
		    and EquipmentCategory<>99; 
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="WO_SiteWebGetStation" grant="">
			<parameters>
				<parameter name="StationId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
            select lsc.StructureName 	StationCenter,ss.StructureName	StationGroup,
	        s.StationName,site.SiteID StationCode,
	        s.StationId, d.ItemValue StationCategory,
	        s.Latitude, s.Longitude
	        from TBL_Station s
	        INNER JOIN TBL_DataItem d ON EntryId=71 AND s.StationCategory=ItemId
	        INNER JOIN  TBL_StationStructure  lsc ON  lsc.ParentStructureId=0
	        inner JOIN TBL_StationStructure ss ON 
		    ss.StructureGroupId=1 
	        inner JOIN TBL_StationStructureMap m on 
		    ss.StructureId = m.StructureId AND m.StationId = S.StationId	
	        LEFT JOIN TBL_StationCMCC site ON  
			site.StationId=s.StationId 		
	        where s.StationId>0  and (@StationId=-1 or s.StationId=@StationId);
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="WO_AcountInfoQuery_UserRoleMap" grant="">
        <parameters>
          <parameter name="UserId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
        </parameters>
			<body>
				<![CDATA[ 
           SELECT 1 FROM TBL_UserRoleMap a WHERE a.UserId = @UserId AND a.RoleId = -1
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="WO_AcountInfoQuery_RoleInfo" grant="">
			<parameters>
				<parameter name="UserId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
              SELECT a.UserId,
              a.UserName,
              c.RoleName RoleTypeName,
              c.RoleName
              FROM TBL_Account a
              Inner JOIN TBL_UserRoleMap b ON b.RoleId = -1 AND a.UserId = b.UserId 
              Inner JOIN TBL_UserRole c ON b.RoleId = c.RoleId
              WHERE a.UserId = @UserId ;
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="WO_AcountInfoQuery_CountUserRoleMap" grant="">
			<parameters>
				<parameter name="UserId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
             SELECT count(1) count FROM TBL_UserRoleMap a WHERE a.UserId =@UserId
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="WO_AcountInfoQuery_GetNotAdminInfo" grant="">
			<parameters>
				<parameter name="UserId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
             SELECT a.UserId,
              a.UserName,
              '厂商' RoleTypeName,
              c.RoleName
              FROM TBL_Account a
              Inner JOIN TBL_UserRoleMap b ON a.UserId = b.UserId 
              Inner JOIN TBL_UserRole c ON b.RoleId = c.RoleId
              WHERE a.UserId = @UserId ;
            ]]>
			</body>
		</procedure>

		<procedure owner="" name="WO_FileUploadRec_AddOrUpdate_MapOrdereId" grant="">
			<parameters>
				<parameter name="OrderId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FSaveName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
            SELECT 1 FROM WO_FileUploadRec where WO_FileUploadRec.OrderId=@OrderId and WO_FileUploadRec.FSaveName=@FSaveName
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="WO_FileUploadRec_AddOrUpdate_FileUploadRec" grant="">
			<parameters>
				<parameter name="FOriginalName" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FSize" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="CurrentTime" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="OrderId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FType" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
            update WO_FileUploadRec 
		    set FOriginalName=@FOriginalName, FSize=@FSize, UploadTime=@CurrentTime::timestamp
		    where WO_FileUploadRec.OrderId=@OrderId and WO_FileUploadRec.EquipmentId=@EquipmentId and WO_FileUploadRec.FType=@FType;
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="WO_FileUploadRec_AddOrUpdate_Insert_FileUploadRec" grant="">
			<parameters>
				<parameter name="FOriginalName" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FSize" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="CurrentTime" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="OrderId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FSaveName" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Uri" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FType" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="UserId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
            INSERT INTO WO_FileUploadRec 
		    ( OrderId,  EquipmentId, FType,  FSaveName, Uri, FOriginalName, FSize, UploadTime, UserId)
		    VALUES 
		    (@OrderId, @EquipmentId,@FType, @FSaveName, @Uri, @FOriginalName, @FSize, @CurrentTime::timestamp, @UserId);	
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="WO_FileUploadRec_AddOrUpdate_Update_TestOrderEquipItem" grant="">
			<parameters>
				<parameter name="OrderId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Uri" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
             Update WO_TestOrderEquipItem 
		     set UriImage = @Uri
		     where  WO_TestOrderEquipItem.OrderId=@OrderId and WO_TestOrderEquipItem.EquipmentId=@EquipmentId;		
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="WO_FileUploadRec_AddOrUpdate_Update_TestOrderEquipItem2" grant="">
			<parameters>
				<parameter name="OrderId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Uri" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
            Update WO_TestOrderEquipItem 
		    set UriProtocol=@Uri
		    where  WO_TestOrderEquipItem.OrderId=@OrderId and WO_TestOrderEquipItem.EquipmentId=@EquipmentId;		
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="WO_TestOrderArtSelfCheckList_Update" grant="">
			<parameters>
				<parameter name="OrderId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="CheckDicId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IsPass" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
           update WO_TestOrderArtSelfCheckList
	       set  IsPass=@IsPass
	       where WO_TestOrderArtSelfCheckList.OrderId=@OrderId and WO_TestOrderArtSelfCheckList.CheckDicId=@CheckDicId;	
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="WO_InstallClerk_QueryForOneCompany" grant="">
			<parameters>
				<parameter name="CompanyId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="KwClerk" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
      <replaces>
        <replace keyName ="WhereInstallClerk">
          <![CDATA[ and k.ClerkName like concat('%',@KwClerk,'%') ]]>
        </replace>
      </replaces>
			<body>
				<![CDATA[ 
            select c.CompanyId, c.CompanyName, k.ClerkId, k.ClerkName
	        from WO_InstallClerk k
	        INNER JOIN WO_InstallCompany c ON c.CompanyId=k.CompanyId 
	        where (@CompanyId=-1 or k.CompanyId = @CompanyId)
		      $[WhereInstallClerk]
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="WO_InstallClerk_AddToCompany_ByClerkName" grant="">
			<parameters>
				<parameter name="CompanyId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ClerkName" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
           select 1 from WO_InstallClerk where WO_InstallClerk.ClerkName=@ClerkName
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="WO_InstallClerk_AddToCompany_Insert_InstallClerk" grant="">
			<parameters>
				<parameter name="CompanyId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ClerkName" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
             insert into WO_InstallClerk 
	         (CompanyId,ClerkName)
	         values
	         (@CompanyId, @ClerkName);
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="WO_InstallClerk_QueryEx" grant="">
			<parameters>
				<parameter name="kwCompany" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="strKwCompany" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			    <parameter name="kwClerk" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			    <parameter name="strKwClerk" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
            select c.CompanyName, k.ClerkName from WO_InstallClerk k
	        INNER JOIN WO_InstallCompany c ON c.CompanyId=k.CompanyId and (@kwCompany ='' OR c.CompanyName like @strKwCompany)
	        where (@kwClerk ='' OR k.ClerkName like @strKwClerk);
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="WO_InstallCompany_Add_CompanyConfig" grant="">
			<parameters>
				<parameter name="CompanyName" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
           select 1 from WO_InstallCompany where WO_InstallCompany.CompanyName=@CompanyName
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="WO_InstallCompany_CompanyName" grant="">
			<parameters>
				<parameter name="CompanyName" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
           insert into WO_InstallCompany (CompanyName) VALUES (@CompanyName);
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="Update_TestOrderArtSelfCheckList" grant="">
			<parameters>
				<parameter name="OrderId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			    <parameter name="CheckDicId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			    <parameter name="IsPass" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
           update WO_TestOrderArtSelfCheckList
	       set IsPass=@IsPass
	       where WO_TestOrderArtSelfCheckList.OrderId=@OrderId and WO_TestOrderArtSelfCheckList.CheckDicId=@CheckDicId;
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="Update_TestOrderExpertCheckList" grant="">
			<parameters>
				<parameter name="OrderId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			    <parameter name="CheckDicId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			    <parameter name="IsPass" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			    <parameter name="PassNote" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
               update WO_TestOrderExpertCheckList
	           set  IsPass=@IsPass, PassNote=@PassNote
	           where WO_TestOrderExpertCheckList.OrderId=@OrderId and WO_TestOrderExpertCheckList.CheckDicId=@CheckDicId;
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="WO_TestOrderEquipItem_Del" grant="">
			<parameters>
				<parameter name="OrderId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			    <parameter name="EquipmentId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
               delete from WO_TestOrderEquipItem where WO_TestOrderEquipItem.orderId=@OrderId and WO_TestOrderEquipItem.equipmentId=@EquipmentId;
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="WO_TestOrderEquipItem_Del2" grant="">
			<parameters>
				<parameter name="OrderId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			    <parameter name="EquipmentId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
               delete from WO_TestOrderEquipItemCheckList where WO_TestOrderEquipItemCheckList.orderId=@OrderId and WO_TestOrderEquipItemCheckList.equipmentId=@EquipmentId;
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="DicStationTypeMap_ItemId" grant="">
			<parameters>
				<parameter name="WR_ItemId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			    <parameter name="SiteWeb_ItemId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
               select 1 from WO_DicStationTypeMap where WR_ItemId=@WR_ItemId
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="Update_DicStationTypeMap" grant="">
			<parameters>
				<parameter name="WR_ItemId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			    <parameter name="SiteWeb_ItemId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="currentTime" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
               update WO_DicStationTypeMap  set SiteWeb_ItemId=@SiteWeb_ItemId ,SaveTime=@currentTime::timestamp
		       where  WR_ItemId=@WR_ItemId;
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="Insert_DicStationTypeMap" grant="">
			<parameters>
				<parameter name="WR_ItemId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			    <parameter name="SiteWeb_ItemId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="currentTime" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
              INSERT INTO WO_DicStationTypeMap (WR_ItemId, SiteWeb_ItemId, SaveTime) 	VALUES (@WR_ItemId, @SiteWeb_ItemId,@currentTime::timestamp);
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="Update_DicCmdCheckList" grant="">
			<parameters>
				<parameter name="CheckId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
              update WO_DicCmdCheckList set IsMust='是'
		      where CheckId=@CheckId;
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="Update_DicCmdCheckList2" grant="">
			<parameters>
				<parameter name="CheckId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
             update WO_DicCmdCheckList set IsMust=null where CheckId=@CheckId;
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="Select_DicCmdCheckList" grant="">
			<parameters>
				<parameter name="CheckId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
              select * from WO_DicCmdCheckList 
              where WO_DicCmdCheckList.CheckId=@CheckId;
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="Update_DicEventCheckList" grant="">
			<parameters>
				<parameter name="CheckId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
              update WO_DicEventCheckList set  IsMust='是'
		      where CheckId=@CheckId;
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="Update_DicEventCheckList2" grant="">
			<parameters>
				<parameter name="CheckId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
             update WO_DicEventCheckList set IsMust=null  where CheckId=@CheckId;
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="Select_DicEventCheckList" grant="">
			<parameters>
				<parameter name="CheckId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
             select * from WO_DicEventCheckList where WO_DicEventCheckList.CheckId=@CheckId;
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="Update_DicSigCheckList" grant="">
			<parameters>
				<parameter name="LimitDown" type="float" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			    <parameter name="LimitUp" type="float" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			    <parameter name="CheckId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
		   </parameters>
			<body>
				<![CDATA[ 
            update WO_DicSigCheckList set  IsMust='是',
			LimitDown=@LimitDown, LimitUp=@LimitUp
		    where WO_DicSigCheckList.CheckId=@CheckId;
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="Update_DicSigCheckList2" grant="">
	       <parameters>
			    <parameter name="CheckId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
		   </parameters>
			<body>
				<![CDATA[ 
            update WO_DicSigCheckList set IsMust=null,LimitDown=NULL, LimitUp=NULL
		    where WO_DicSigCheckList.CheckId=@CheckId;
            ]]>
			</body>
		</procedure>
	    <procedure owner="" name="Select_DicSigCheckList" grant="">
	       <parameters>
			    <parameter name="CheckId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
		   </parameters>
			<body>
				<![CDATA[ 
            select * from WO_DicSigCheckList where WO_DicSigCheckList.CheckId=@CheckId;
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="Select_WRDataItem" grant="">
			<parameters>
				<parameter name="ItemId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
            select 1 from WR_DataItem where WR_DataItem.EntryId=6 and WR_DataItem.ItemId=@ItemId
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="Update_WRDataItem" grant="">
			<parameters>
				<parameter name="ItemId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ItemValue" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
            update WR_DataItem
		    set ItemValue=@ItemValue, ExtendField3=''
		    where  EntryId=6 and ItemId=@ItemId;
            ]]>
			</body>
		</procedure>
		<procedure owner="" name="Insert_WRDataItem" grant="">
			<parameters>
				<parameter name="ItemId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ItemValue" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="CurrentTime" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
            INSERT INTO WR_DataItem (EntryId, ItemId, ParentEntryId, ParentItemId, ItemValue, LastUpdateDate, ExtendField1, ExtendField2, ExtendField3)
	        VALUES (6, @ItemId, 0, 0, @ItemValue, @CurrentTime::timestamp, '', '', '');
            ]]>
			</body>
		</procedure>
	</procedures>
</root>
