﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 请求监控点门限数据应答
    /// </summary>
    public class GetThresholdAck : BMessage
    {
        public EnumResult Result { get; set; }

        public string FailureCause { get; set; }

        public List<DeviceThreshold> Values { get; set; }

        public GetThresholdAck() : base()
        {
            MessageType = (int)BMessageType.GET_THRESHOLD_ACK;
        }

        public GetThresholdAck(string fsuId, EnumResult result, string failureCause, List<DeviceThreshold> values)
            : base()
        {
            MessageType = (int)BMessageType.GET_THRESHOLD_ACK;

            FSUID = fsuId;
            Result = result;
            FailureCause = failureCause;
            Values = values;
        }

        public static GetThresholdAck Deserialize(string json)
        {
            GetThresholdAck info = null;
            try
            {
                info = JsonConvert.DeserializeObject<GetThresholdAck>(json);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetThresholdAck.Deserialize:{0}",ex.Message);
                entityLogger.ErrorFormat("GetThresholdAck.Deserialize:{0}" ,ex.StackTrace);
                info = new GetThresholdAck();
                info.ErrorMsg = ex.Message;
                info.StringXML = json;
            }
            return info;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}:{2},{3}, {4}", MessageId, MessageType, FSUID, Result, FailureCause);
            sb.AppendLine();
            if (Values != null)
            {
                foreach (DeviceThreshold device in Values)
                {
                    sb.AppendFormat("{0}\r\n", device.ToString());
                }
            }
            return sb.ToString();
        }
    }
}