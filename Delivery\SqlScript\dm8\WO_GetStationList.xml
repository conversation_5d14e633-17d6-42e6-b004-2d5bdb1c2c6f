﻿<?xml version="1.0" encoding="utf-8"?>
<?xml-stylesheet type="text/xsl" href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <procedures>
        <procedure owner="" name="WO_GetStationList_1" grant="">
            <body>
                <![CDATA[
                SELECT StructureId,StructureName
                FROM TBL_StationStructure
                WHERE TBL_StationStructure.StructureType = 2 AND TBL_StationStructure.ParentStructureId = 0;
                ]]>
            </body>
        </procedure>
        <procedure owner="" name="WO_GetStationList_2" grant="">
            <parameters>
                <parameter name="SCenterId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
                <parameter name="CenterName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
                <parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
                <parameter name="StationCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
            </parameters>
            <body>
                <![CDATA[
                SELECT :SCenterId CenterId, :CenterName CenterName,
                s.StationId,
                s.StationName,
                s.Latitude,
                s.Longitude,
                w.StructureId, b.StructureName, w.StationCode,
                w.StationCategory, c.ItemValue StationCategoryName,
                w.StationStatus,
                w.Address,
                w.Province, f.ItemValue ProvinceName,
                w.City, g.ItemValue CityName,
                w.County, h.ItemValue CountyName,
                w.SWStationId,
                w.Remark,
                w.StationStatus
                FROM WR_StationManagement w
                INNER JOIN TBL_Station s ON w.SWStationId = s.StationId
                INNER JOIN TBL_StationStructure b ON w.StructureId = b.StructureId
                INNER JOIN WR_DataItem c ON w.StationCategory = c.ItemId AND c.EntryId = 6
                LEFT JOIN WR_DataItem f ON w.Province = f.ItemId AND f.EntryId = 1
                LEFT JOIN WR_DataItem g ON w.City = g.ItemId AND g.EntryId = 2
                LEFT JOIN WR_DataItem h ON w.County = h.ItemId AND h.EntryId = 3
                WHERE
                (:StationName = '' OR s.StationName LIKE '%' || :StationName || '%')
                AND
                (:StationCode = '' OR w.StationCode LIKE '%' || :StationCode || '%');
                ]]>
            </body>
        </procedure>
        <procedure owner="" name="WO_GetStationList_3" grant="">
            <parameters>
                <parameter name="SCenterId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
                <parameter name="CenterName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
                <parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
                <parameter name="StationCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
            </parameters>
            <body>
                <![CDATA[
                SELECT :SCenterId CenterId, :CenterName CenterName,
                s.StationId,
                s.StationName,
                s.Latitude,
                s.Longitude,
                w.StructureId, b.StructureName, w.StationCode,
                w.StationCategory, c.ItemValue StationCategoryName,
                w.StationStatus,
                w.Address,
                w.Province, f.ItemValue ProvinceName,
                w.City, g.ItemValue CityName,
                w.County, h.ItemValue CountyName,
                w.SWStationId,
                w.Remark,
                w.StationStatus
                FROM WR_StationManagementCUCC w
                INNER JOIN TBL_Station s ON w.SWStationId = s.StationId
                INNER JOIN TBL_StationStructure b ON w.StructureId = b.StructureId
                INNER JOIN WR_DataItem c ON w.StationCategory = c.ItemId AND c.EntryId = 6
                LEFT JOIN WR_DataItem f ON w.Province = f.ItemId AND f.EntryId = 1
                LEFT JOIN WR_DataItem g ON w.City = g.ItemId AND g.EntryId = 2
                LEFT JOIN WR_DataItem h ON w.County = h.ItemId AND h.EntryId = 3
                WHERE
                (:StationName = '' OR s.StationName LIKE '%' || :StationName || '%')
                AND
                (:StationCode = '' OR w.StationCode LIKE '%' || :StationCode || '%');
                ]]>
            </body>
        </procedure>
    </procedures>
</root>