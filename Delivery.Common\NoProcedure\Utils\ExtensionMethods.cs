﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Delivery.Common.NoProcedure.Utils
{
    public static class ExtensionMethods
    {
        public static TValue GetByKey<TKey, TValue>(this Dictionary<TKey, TValue> dictionary, TKey key)
        {
            return dictionary.TryGetValue(key, out TValue value) ? value : default(TValue);
        }
        /// <summary> 将日期格式化为 yyyy-MM-dd HH:mm:ss 格式的字符串 </summary>
        public static string ToStr(this DateTime dateTime)
        {
            return dateTime != null ? dateTime.ToString("yyyy-MM-dd HH:mm:ss") : "";
        }
    }
}
