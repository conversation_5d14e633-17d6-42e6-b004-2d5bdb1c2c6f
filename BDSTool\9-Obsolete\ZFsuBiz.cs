﻿using BDSTool.BLL.S2;
using BDSTool.Entity;
using BDSTool.Entity.B;
using BDSTool.Utility;


using Common.Logging.Pro;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Xml;
using System.Xml.Linq;

namespace BDSTool.BLL.B
{
    public partial class ZFsuBiz
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");
        private static readonly log4net.ILog loggerConfig = LogManager.GetLogger("BDSToolConfig");
        public static bool IsBMonitorUnit(int MonitorUnitId) {
            try {
                var MonitorUnitCategory = MonitorUnitBiz.ZGetMonitorUnitCategory(MonitorUnitId);

                //TBL_DataItem entryId=34 itemId=9 itemname=B接口
                if (MonitorUnitCategory ==  ConstsFsu.MonitorUnitCategory)
                    return true;

                return false;
            }
            catch (Exception ex) {
                logger.ErrorFormat("IsBMonitorUnit();MonitorUnitId={0};Error={1}", MonitorUnitId, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
        }
    }
}
