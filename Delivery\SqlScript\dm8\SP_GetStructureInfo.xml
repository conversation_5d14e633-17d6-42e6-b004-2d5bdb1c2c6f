﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <procedures>
    <procedure owner="" name="SP_Get_StructureInfo" grant="">
      <body>
        <![CDATA[
        SELECT TBL_StationStructure.StructureId id,TBL_StationStructure.StructureName "text",TBL_StationStructure.ParentStructureId parentId 
        FROM TBL_StationStructure WHERE TBL_StationStructure.IsUngroup = 0;  
         ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_StructureUserId" grant="">
      <parameters>
        <parameter name="LogonId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT TBL_Account.UserId FROM TBL_Account WHERE TBL_Account.LogonId = :LogonId;
         ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_UserRoleMap" grant="">
      <parameters>
        <parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT 'X' FROM TBL_UserRoleMap WHERE TBL_UserRoleMap.UserId = :UserId AND TBL_UserRoleMap.RoleId = -1;
         ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_StationCount" grant="">
      <parameters>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT count(*) stationCount FROM WR_StationManagement a WHERE a.StationStatus = 1 AND a.UserId IN
        (SELECT t1.UserId FROM (SELECT DISTINCT a.UserId FROM WR_FsuManagement a
        UNION SELECT DISTINCT b.UserId FROM WR_HouseManagement b
        UNION SELECT DISTINCT c.UserId FROM WR_StationManagement c) t1);
         ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_HouseCount" grant="">
      <parameters>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT count(*) houseCount FROM WR_HouseManagement b WHERE b.HouseStatus = 1 AND b.UserId IN
        (SELECT t2.UserId FROM (SELECT DISTINCT a.UserId FROM WR_FsuManagement a
        UNION SELECT DISTINCT b.UserId FROM WR_HouseManagement b
        UNION SELECT DISTINCT c.UserId FROM WR_StationManagement c) t2)
         ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_FsuCount" grant="">
      <parameters>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT count(*) fsuCount FROM WR_FsuManagement c WHERE c.FsuStatus = 1 AND c.UserId IN
        (SELECT t3.UserId FROM (SELECT DISTINCT a.UserId FROM WR_FsuManagement a
        UNION SELECT DISTINCT b.UserId FROM WR_HouseManagement b
        UNION SELECT DISTINCT c.UserId FROM WR_StationManagement c) t3);
         ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_NeedApproveResourceCount" grant="">
      <parameters>
        <parameter name="StationCount" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="HouseCount" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FsuCount" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT :StationCount StationCount, :HouseCount HouseCount, :FsuCount FsuCount;
         ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_NeedApproveResourceCount1" grant="">
      <parameters>
        <parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT StationCount, HouseCount, FsuCount  FROM
		   (SELECT count(*) StationCount FROM WR_StationManagement WHERE WR_StationManagement.StationStatus = 2 AND WR_StationManagement.UserId = :UserId) T1,	
		   (SELECT count(*) HouseCount FROM WR_HouseManagement WHERE WR_HouseManagement.HouseStatus = 2 AND WR_HouseManagement.UserId = :UserId) T2,
		   (SELECT count(*) FsuCount FROM WR_FsuManagement WHERE WR_FsuManagement.FsuStatus = 2 AND WR_FsuManagement.UserId = :UserId) T3;	
         ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_GetUserInfoAccount" grant="">
      <parameters>
        <parameter name="LogonId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT 1 FROM TBL_Account a Inner JOIN TBL_UserRoleMap b ON a.UserId = b.UserId WHERE a.LogonId = :LogonId AND b.RoleId = -1;	
         ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_GetUserInfo" grant="">
      <parameters>
        <parameter name="LogonId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT a.UserId,a.UserName, CASE c.RoleId WHEN -1 THEN c.RoleName ELSE '厂商 or 其他' END as RoleTypeName,
        c.RoleName FROM TBL_Account a
        Inner JOIN TBL_UserRoleMap b ON a.UserId = b.UserId 
        Inner JOIN TBL_UserRole c ON b.RoleId = c.RoleId
        WHERE a.LogonId = :LogonId AND b.RoleId = -1;	
         ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_GetUserInfo1" grant="">
      <parameters>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT a.UserId,a.UserName,CASE c.RoleId WHEN -1 THEN c.RoleName ELSE '厂商 or 其他' END as RoleTypeName,
        c.RoleName FROM TBL_Account a
        Inner JOIN TBL_UserRoleMap b ON a.UserId = b.UserId 
        Inner JOIN TBL_UserRole c ON b.RoleId = c.RoleId WHERE 1=2;
         ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_WRFsu_AssociatedDevice" grant="">
      <parameters>
        <parameter name="FsuCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT station.StationCode, station.StationName, di.ItemValue StationCategory, station.Address, fsu.FsuCode,
		    fsu.FsuName, house.HouseCode, house.HouseName, monitor.FSUIP IPAddress, monitor.UserName,
		    monitor.PassWord, monitor.FTPUserName, monitor.FTPPassWord
	      FROM WR_FsuManagement fsu
	      INNER JOIN WR_HouseManagement house ON fsu.WRHouseId = house.WRHouseId
	      INNER JOIN WR_StationManagement station ON house.WRStationId = station.WRStationId
	      INNER JOIN WR_DataItem di ON station.StationCategory = di.ItemId AND di.EntryId = 6
	      INNER JOIN TSL_MonitorUnitCMCC monitor ON fsu.SWMonitorUnitId = monitor.MonitorUnitId AND station.SWStationId = monitor.StationId
	      WHERE fsu.FsuCode = :FsuCode;
         ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_WRFsu_AssociatedDevice1" grant="">
      <parameters>
        <parameter name="FsuCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT di.ItemValue DeviceCategory, di1.ItemValue DeviceSubCategory,
		    device.DeviceName, device.DeviceCode, house.HouseCode, house.HouseName
	      FROM WR_DeviceManagement device
	      INNER JOIN WR_FsuManagement fsu ON device.WRFsuId = fsu.WRFsuId
	      INNER JOIN WR_HouseManagement house ON device.WRHouseId = house.WRHouseId
	      INNER JOIN WR_DataItem di ON TO_NUMBER(LEFT(device.DeviceType,2) AS signed) =di.ItemId AND di.EntryId = 8
	      INNER JOIN WR_DataItem di1 ON TO_NUMBER(RIGHT(device.DeviceType,2) AS signed) =di1.ItemId AND di1.EntryId = 9
		    AND di1.ParentItemId = di.ItemId AND di1.ParentEntryId = 8
	      WHERE fsu.FsuCode = :FsuCode; 
         ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_AllFsuFtpInfo" grant="">
      <parameters>
        <parameter name="LogonId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT CASE WHEN E.NeedSyncFlag IS NULL THEN 0
        WHEN E.NeedSyncFlag = 1 THEN 0 
        WHEN E.NeedSyncFlag = 0 THEN 1
        ELSE 0 END IsSynced, 
        D.StationName, B.FSUID, B.FSUName,B.FSUIP,B.FTPUserName,B.FTPPassWord,ifnull(B.GetConfigFlag,0) GetConfigFlag
        FROM WR_FsuManagement A
        INNER JOIN TSL_MonitorUnitCMCC B ON A.SWMonitorUnitId = B.MonitorUnitId
        INNER JOIN (SELECT DISTINCT A.UserId FROM TBL_UserRoleMap A
	      INNER JOIN (SELECT RoleId 
				FROM TBL_UserRoleMap WHERE UserId = (SELECT UserId FROM TBL_Account WHERE TBL_Account.LogonId = :LogonId)) B
		    ON A.RoleId = B.RoleId OR B.RoleId = -1) C ON A.UserId = C.UserId
        INNER JOIN TBL_Station D ON B.StationId = D.StationId
        LEFT JOIN TBL_FsuConfig E ON B.FSUID = E.FSUID
        WHERE D.StationName LIKE concat('%',IfNULL(:StationName, ''),'%');
         ]]>
      </body>
    </procedure>
  </procedures>
</root>
