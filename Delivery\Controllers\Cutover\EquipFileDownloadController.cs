﻿using Delivery.Controllers;
using Microsoft.AspNetCore.Mvc;
using System;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Delivery.Controllers.Cutover
{
    public class EquipFileDownLoadController : BaseController
    {
        [HttpGet]
        public async Task<FileStreamResult> Get(string path)
        {
            string filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, path);
            HttpResponseMessage result = new HttpResponseMessage();
            //if (File.Exists(filePath))
            //{
            //    result.Content = new StreamContent(new FileStream(filePath, FileMode.Open));
            //    result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/x-zip-compressed");
            //    result.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment")
            //    {
            //        FileName = HttpUtility.UrlEncode(Path.GetFileName(filePath))
            //    };

            //}
            using (Stream stream = new FileStream(filePath, FileMode.Open))
            {
                return File(stream, "application/x-zip-compressed", HttpUtility.UrlEncode(Path.GetFileName(filePath)));
            }
        }
    }
}
