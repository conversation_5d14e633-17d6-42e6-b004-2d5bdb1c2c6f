﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using BDSTool.DBUtility.Common;
using BDSTool.DBUtility;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace BDSTool.Entity.S2
{
    partial class TBL_Signal : IBatchInsertRow
    {
        #region for batch insert
        private static string _InsertHeader = @"INSERT INTO TBL_Signal( EquipmentTemplateId , SignalId , Enable , Visible , Description, SignalName , SignalCategory , SignalType , ChannelNo , ChannelType , Expression , DataType, ShowPrecision , Unit , DisplayIndex , ModuleNo ,AbsValueThreshold, PercentThreshold) ";

        public string GetInsertHeader() {
            if (CommonUtils.IsNoProcedure)
            {
                return Public_ExecuteSqlService.Instance.GetInsertHeader_TBL_Signal();
            }
            return _InsertHeader;
        }

        public string GetDataRowValueString() {
            //return String.Format(@"({0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12})",
            //LogId, SHelper.GetPara(FSUID), SHelper.GetPara(DeviceID), (int)Type, SHelper.GetPara(ID),
            //SHelper.GetPara(SignalName), SHelper.GetPara((int)AlarmLevel), SHelper.GetPara(Threshold), SHelper.GetPara(AbsoluteVal), SHelper.GetPara(RelativeVal),
            //SHelper.GetPara(Describe), SHelper.GetPara(NMAlarmID), SHelper.GetPara(SignalNumber));

            return string.Format(@"({0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13},{14},{15},{16},{17})",
                EquipmentTemplateId, SignalId, SHelper.GetPara(Enable), SHelper.GetPara(Visible), SHelper.GetPara(Description),
                SHelper.GetPara(SignalName), SignalCategory, SignalType, ChannelNo, ChannelType,
                SHelper.GetPara(Expression), SHelper.GetPara(DataType), SHelper.GetPara(ShowPrecision), SHelper.GetPara(Unit), DisplayIndex,
                ModuleNo,
                SHelper.GetPara(AbsValueThreshold), SHelper.GetPara(PercentThreshold));
        }

        #endregion

        public List<int> SignalPropertyIds = new List<int>();
        public Dictionary<int,string> StateValue2Meanings=new Dictionary<int,string> ();

    }
}
