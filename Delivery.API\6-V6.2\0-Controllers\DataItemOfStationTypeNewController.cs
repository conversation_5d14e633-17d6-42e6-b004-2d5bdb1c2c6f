﻿using Delivery.API;
using DM.TestOrder.DAL;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;



namespace DM.TestOrder.Controllers {

    public class DataItemOfStationTypeNewController : BaseController {
        [HttpPost]
        public JsonResult POST([FromBody]JObject value) {
            var ItemId = int.Parse(value["ItemId"].ToString());
            var ItemValue = value["ItemValue"].ToString();

            var rtnMsg = WrDataItemOfStationTypeDal.AddOne(ItemId, ItemValue);

            var rtn = new {
                errormsg = rtnMsg
            };

            return new JsonResult(rtn);
            //var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(rtn);


            //Debug.WriteLine(value.ToString());
            //Debug.WriteLine(jsonRtn.ToString());

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};
        }
    }
}
