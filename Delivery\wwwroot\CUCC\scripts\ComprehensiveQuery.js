﻿'use strict';

//global
var cookie = getCookie();
var CurrentUserId = cookie['userinfo']['UserId'];

//回车事件
document.onkeydown = function (event) {
    var e = event || window.event;
    if (e.keyCode === 13 && document.activeElement.tagName !== 'TEXTAREA') {
        e.returnValue = false;
        e.cancel = true;
        $("#btnSeach").click();
    }
};

//检查输入框长度
var checkTextBoxLength = function (id) {
    if (!$('#' + id).textbox('isValid')) {
        alertInfo("提示", "请输入正确长度！");
        return false;
    }
    return true;
};

//检查查询输入是否有效
var CheckValid = function () {
    if (!$("#dtbApplyStartTime").datebox('getValue') && $("#dtbApplyEndTime").datebox('getValue')
        || $("#dtbApplyStartTime").datebox('getValue') && !$("#dtbApplyEndTime").datebox('getValue')) {
        alertInfo("开始时间和截止时间必须同时存在！");
        return false;
    }
    if (!$("#dtbArchivesStartTime").datebox('getValue') && $("#dtbArchivesEndTime").datebox('getValue')
        || $("#dtbArchivesStartTime").datebox('getValue') && !$("#dtbArchivesEndTime").datebox('getValue')) {
        alertInfo("提示", "开始时间和截止时间必须同时存在！");
        return false;
    }
    if ($("#dtbApplyStartTime").datebox("isValid") && $("#dtbApplyEndTime").datebox("isValid") && $("#dtbArchivesStartTime").datebox("isValid")
        && $("#dtbArchivesEndTime").datebox("isValid") && $("#txtStationNameQuery").datebox("isValid") && $("#txtStationCodeQuery").datebox("isValid"))
        return true;
    alertInfo("请正确输入查询条件！");
    return false;
};

var InitComprehensiveQueryGrid = function () {
    $('#dgComprehensiveQuery').datagrid({
        title: '割接单信息列表',
        singleSelect: true,
        width: function () { return document.body.clientWidth * 0.9; },
        //height: $(window).height() - 150,
        //fitColumns: true,
        rownumbers: true,
        remoteSort: false,
        //multiSort:true,
        pagination: true,
        pageSize: 50,
        pageList: [50, 100, 150, 200],
        loadFilter: pagerFilter,
        columns: [[
            { field: 'OrderId', title: '', hidden: true },
            { field: 'OrderState', title: '', hidden: true },
            {
                field: 'MyOrderId', title: '割接单号', width: 120, align: 'left', halign: 'left', sortable: true,
                formatter: function (value, row, index) {
                    //return '<a href=# onclick=showOrderApplyDialog("' + row["OrderId"] + '","' + row["MyOrderId"] + '")>' + value + '</a>';
                    return '<a href=\"#\" onclick=\"showOrderApplyTab(' + row["OrderId"] + ',\'割接单审核' + value + '\')\">' + value + '</a>';
                }
            },
            { field: 'OrderTypeString', title: '割接类型', width: 80, align: 'left', halign: 'left', sortable: true },
            { field: 'FinalGeneralReuslt', title: '总体测试结果', width: 100, align: 'left', halign: 'left', sortable: true },
            { field: 'StationGroup', title: '分组', width: 80, align: 'left', halign: 'left', sortable: true },
            { field: 'StationName', title: '站址名称', width: 100, align: 'left', halign: 'left', sortable: true },
            { field: 'StationCode', title: '站址编码', width: 100, align: 'left', halign: 'left', sortable: true },
            { field: 'ApplyUserFsuVendor', title: 'SU厂家', width: 100, align: 'left', halign: 'left', sortable: true },
            { field: 'ApplyTime', title: '申请时间', width: 150, align: 'left', halign: 'left', sortable: true },
            { field: 'SubmitTime', title: '受理时间', width: 150, align: 'left', halign: 'left', sortable: true },
            { field: 'ApproveTime', title: '归档时间', width: 150, align: 'left', halign: 'left', sortable: true },
            { field: 'OrderStateString', title: '申请单状态', width: 100, align: 'left', halign: 'left', sortable: true },
            { field: 'Dealer', title: '当前处理人', width: 100, align: 'left', halign: 'left', sortable: true }/*,
			{
			    field: '浏览', title: ' ', width: 60, hidden: false, align: 'left', halign: 'left',
                    formatter: function (value, row, index) {
                        return '<a href=# onclick=showOrderApplyDialog("' + row["OrderId"] + '","' + row["MyOrderId"] + '")>详情</a>';
                    }
            }*/
        ]]
    });
};

//获取割接单申请列表
var getOrderApplyInfo = function () {
    var paras = {
        UserId: CurrentUserId,
        Cond_StationName: $('#txtStationNameQuery').val(),
        Cond_StationGroup: $('#cbxStructureId').combobox('getText') === '请选择' ? '' : $('#cbxStructureId').combobox('getText'),
        Cond_StationCode: $('#txtStationCodeQuery').val(),
        Cond_FsuVendor: $('#cbxFsuVendor').combobox('getText') === '请选择' ? '' : $('#cbxFsuVendor').combobox('getText'),
        Cond_OrderType: $('#cbxOrderType').combobox('getText') === '请选择' ? '' : $('#cbxOrderType').combobox('getText'),
        Cond_ApplyTime1: $('#dtbApplyStartTime').datebox('getValue') ? $('#dtbApplyStartTime').datebox('getValue') : '',
        Cond_ApplyTime2: $('#dtbApplyEndTime').datebox('getValue') ? $('#dtbApplyEndTime').datebox('getValue') : '',
        Cond_ApproveTime1: $('#dtbArchivesStartTime').datebox('getValue') ? $('#dtbArchivesStartTime').datebox('getValue') : '',
        Cond_ApproveTime2: $('#dtbArchivesEndTime').datebox('getValue') ? $('#dtbArchivesEndTime').datebox('getValue') : '',
        Cond_OrderState: $('#cbxOrderState').combobox('getText') === '请选择' ? '' : $('#cbxOrderState').combobox('getText')
    };

    $.ajax({
        type: "POST",
        contentType: "application/json; charset=UTF-8",
        url: '../api/OrderList',
        data: JSON.stringify(paras),
        success: function (data) {
            $('#dgComprehensiveQuery').datagrid('loadData', data);
            $('#hid_Cond_StationName').val(paras.Cond_StationName);
            $('#hid_Cond_StationGroup').val(paras.Cond_StationGroup);
            $('#hid_Cond_StationCode').val(paras.Cond_StationCode);
            $('#hid_Cond_FsuVendor').val(paras.Cond_FsuVendor === "-1" ? "" : paras.Cond_FsuVendor);
            $('#hid_Cond_OrderType').val(paras.Cond_OrderType);
            $('#hid_Cond_ApplyTime1').val(paras.Cond_ApplyTime1);
            $('#hid_Cond_ApplyTime2').val(paras.Cond_ApplyTime2);
            $('#hid_Cond_ApproveTime1').val(paras.Cond_ApproveTime1);
            $('#hid_Cond_ApproveTime2').val(paras.Cond_ApproveTime2);
            $('#hid_Cond_OrderState').val(paras.Cond_OrderState);
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
};

var InitSeachControl = function () {
    loadDicUiDropControl($("#cbxFsuVendor"), 4, 4);
    loadDicUiDropControl($('#cbxStructureId'), 20, 20);
    $("#dtbApplyStartTime").datetimebox("setValue", new Date(new Date().setDate(1)).format('yyyy-MM-dd') + ' 00:00:00');
    $("#dtbApplyEndTime").datetimebox("setValue", new Date(new Date().getTime() + 1000 * 60 * 60).format('yyyy-MM-dd hh:mm:ss'));
    $("#btnSeach").click(function () {
        if (!CheckValid())
            return false;
        getOrderApplyInfo();
        //$("#btnToExcel").show();
    });
    $("#btnDelete").click(function () {
        var row = $('#dgComprehensiveQuery').datagrid('getSelected');
        if (row) {
            $.messager.confirm('确认', '您确认删除吗？',
                function (r) {
                    if (r)
                        $.ajax({
                            type: "POST",
                            contentType: "application/json; charset=UTF-8",
                            url: '../api/Remove',
                            data: JSON.stringify({ orderid: row.OrderId }),
                            success: function (data) {
                                if (data.errormsg === 'OK') {
                                    alertInfo('删除成功！');
                                    $("#btnSeach").click();
                                } else
                                    alertInfo(data.errormsg, 'error');
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                            }
                        });
                });
        } else
            alertInfo('请选择要删除的行！');
    });
    $("#btnReset").click(function () {
        $("#cbxStructureId").combobox("select", $("#cbxStructureId").combobox('getData')[0].ItemId);
        $("#txtStationNameQuery").textbox("setValue", "");
        $("#txtStationCodeQuery").textbox("setValue", "");
        $("#cbxFsuVendor").combobox("select", $("#cbxFsuVendor").combobox('getData')[0].ItemId);
        $("#cbxOrderType").combobox("select", $("#cbxOrderType").find('option').first().val());
        $("#dtbApplyStartTime").datebox("setValue", "");
        $("#dtbApplyEndTime").datebox("setValue", "");
        $("#dtbArchivesStartTime").datebox("setValue", "");
        $("#dtbArchivesEndTime").datebox("setValue", "");
        $("#cbxOrderState").combobox("select", $("#cbxOrderState").find('option').first().val());
    });
    $("#btnToExcel").click(function () {
        var f = $('<form action="../api/ExportToExcel" method="post" id="fm1" accept-charset="UTF-8"></form>');
        $('<input type="hidden"  name="UserId"/>').val(CurrentUserId).appendTo(f);
        $('<input type="hidden"  name="Cond_StationName"/>').val($('#hid_Cond_StationName').val()).appendTo(f);
        $('<input type="hidden"  name="Cond_StationGroup"/>').val($('#hid_Cond_StationGroup').val()).appendTo(f);
        $('<input type="hidden"  name="Cond_StationCode"/>').val($('#hid_Cond_StationCode').val()).appendTo(f);
        $('<input type="hidden"  name="Cond_FsuVendor"/>').val($('#hid_Cond_FsuVendor').val()).appendTo(f);
        $('<input type="hidden"  name="Cond_OrderType"/>').val($('#hid_Cond_OrderType').val()).appendTo(f);
        $('<input type="hidden"  name="Cond_ApplyTime1"/>').val($('#hid_Cond_ApplyTime1').val()).appendTo(f);
        $('<input type="hidden"  name="Cond_ApplyTime2"/>').val($('#hid_Cond_ApplyTime2').val()).appendTo(f);
        $('<input type="hidden"  name="Cond_ApproveTime1"/>').val($('#hid_Cond_ApproveTime1').val()).appendTo(f);
        $('<input type="hidden"  name="Cond_ApproveTime2"/>').val($('#hid_Cond_ApproveTime2').val()).appendTo(f);
        $('<input type="hidden"  name="Cond_OrderState"/>').val($('#hid_Cond_OrderState').val()).appendTo(f);
        f.appendTo(document.body).submit().remove();
    });
};

function showOrderApplyTab(orderId, subtitle) {
    var url = "CutoverAudit?orderId=" + orderId;
    
    if (!parent.$('#tabs').tabs('exists', subtitle)) {
        var tabs = parent.$('#tabs').tabs('tabs');
        var count = 0;
        for (var i = 0; i < tabs.length; i++) {
            if (/割接单审核\w*/i.test(tabs[i].panel('options').title))
                if (++count >= 5) {
                    alertInfo('最多打开5笔割接单！');
                    return;
                }
        }
        
        parent.$('#tabs').tabs('add', {
            title: subtitle,
            content: '<iframe id="mytabframe"  frameborder="0"  scrolling="auto"  style="width:100%;height:100%;" src="' + url + '"></iframe>',
            //href: url,
            closable: true,
            width: $('#mainPanle').width() - 10,
            height: $('#mainPanle').height() - 26
        });

        //IE6下Frame Bug真他妹的多
        //http://www.cnblogs.com/xiaochaohuashengmi/archive/2010/08/12/1797797.html
        if (navigator.userAgent.indexOf("MSIE 6.0") > -1)//浏览器判断 如果是IE6,再重新加载一次Iframe
        {
            var ie6reloadTabFrame = parent.$('#tabs').tabs('getTab', subtitle).find('iframe')[0];
            if (ie6reloadTabFrame)
                ie6reloadTabFrame.contentWindow.location.href = url;
        }
    } else
        parent.$('#tabs').tabs('select', subtitle);
    //else {
    //    var tab = parent.$('#tabs').tabs('getTab',subtitle);
    //    parent.$('#tabs').tabs('update', { tab: tab, options: { content: '<iframe id="mytabframe"  frameborder="0"  scrolling="auto"  style="width:100%;height:100%;" src="' + url + '"></iframe>'} }).tabs('select', subtitle);
    //}
    
}

$(document).ready(function () {
    if (CurrentUserId !== '-1')
    {
        $("#btnDelete").remove();
    }
    InitComprehensiveQueryGrid();
    InitSeachControl();

    var orderState = getQueryString('orderState');
    if (orderState)
        $('#cbxOrderState').combobox("select", orderState);
    $("#btnSeach").click();

    $('div.mask').remove();
});

