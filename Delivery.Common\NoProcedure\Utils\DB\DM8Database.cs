﻿using Dm;
using System;
using System.Data;
using System.Data.Common;

namespace Delivery.Common.NoProcedure.Utils.DB
{
    public class DM8Database : DataAccessObject{

        public const String DMRETURNPARAMETER = "ret";//TODO: 待修改

        public DM8Database(string connectString) 
        {
            this.ConnectionString = connectString;
        }
        /// <summary>
        /// 新建数据ProviderF工厂
        /// </summary>
        /// <returns></returns>
        public override DbProviderFactory GetClientFactory()
       {
            return DmClientFactory.Instance;
       }

        public override  DbParameter GetReturnDbParameter()
        {
            DbParameter dbParamter = null;

            dbParamter = GetDbParameter(DMRETURNPARAMETER, DbType.Int32, 0, ParameterDirection.ReturnValue);


            return dbParamter;
        }

        public override Object GetReturnDbParameterValue() 
        {
            var parameterName = string.Empty;
            parameterName = BuildParameterName(DMRETURNPARAMETER);
            return Command.Parameters[parameterName].Value;
        }

        private static string BuildParameterName(string parameterName)
        {
            //达梦数据库 sql语句中的占位符必须是冒号(:)开头，但是绑定参数时，好像冒号开头或@开头都可以
            return ":" + parameterName;
        }

    }
}
