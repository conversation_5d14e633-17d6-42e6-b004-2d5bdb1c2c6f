﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Common;
using System.Text;

namespace Delivery.Common.NoProcedure.Utils
{
    public abstract class DataAccessObject:IDataAccessObject,IDisposable
    {


        protected const int DefaultTimeOut = 60;

        /// <summary>
        /// 命令执行超时时间
        /// </summary>
        protected int CommandTimeout { get; set; }

        /// <summary>
        /// 执行命令返回的最大行数
        /// </summary>
        protected int ExecuteRowCount { get; set; }

        /// <summary>
        ///连接字符串
        /// </summary>
        protected  string ConnectionString { get; set; }

        /// <summary>
        /// 驱动名称
        /// </summary>
        protected string ProviderName { get; set; }


        /// <summary>
        /// 数据库连接
        /// </summary>
        public DbConnection Connection
        {
            get;
            set;
        }

        /// <summary>
        /// 数据库命令
        /// </summary>
        public DbCommand Command
        {
            get;
            set;
        }

        /// <summary>
        /// 数据库适配器
        /// 不可外部调用
        /// </summary>
        DbDataAdapter DataAdapter
        {
            get;
            set;
        }

        /// <summary>
        /// 查询返回值(@Return)
        /// </summary>
        Object QueryReturnValue
        {
            get;
            set;
        }
        #region Base Function
        /// <summary>
        /// 新建数据库连接
        /// </summary>
        protected void CreateDatabaseConnection()
        {
            OpenConnection();
        }

        /// <summary>
        /// 打开数据库连接
        /// </summary>
        /// <returns></returns>
        protected bool OpenConnection()
        {
            DbProviderFactory fact = null;

            try
            {
                //创建工厂
                fact = GetClientFactory();
                if (Connection != null)
                    Connection.Dispose();

                //创建Connection对像
                Connection = fact.CreateConnection();

                //设定Connection对像的连接字符串
                Connection.ConnectionString = CryptoHelper.DecryptConnectionString(ConnectionString);

                //打开连接
                Connection.Open();
            }
            catch (Exception ex)
            {
                if (Connection != null)
                    Connection.Dispose();
                throw ex;
            }

            return true;
        }

        /// <summary>
        /// 初始化数据库命令
        /// </summary>
        protected void CreateDatabaseCommand()
        {
            try
            {
                Command = Connection.CreateCommand();
                Command.CommandTimeout = CommandTimeout < 0 ? DefaultTimeOut : CommandTimeout;
            }
            catch (Exception ex)
            {
                if (Command != null)
                    Command.Dispose();
                throw ex;
            }
        }

        /// <summary>
        /// 新建数据ProviderF工厂
        /// </summary>
        /// <returns></returns>
        public abstract DbProviderFactory GetClientFactory();

        #endregion

        #region helper method

        /// <summary>
        /// 获取布尔值
        /// </summary>
        public static Boolean GetBooleanValue(DataRow drValue, String columnName, Boolean defaultValue)
        {
            return GetValue<Boolean>(drValue, columnName, defaultValue, v => Convert.ToBoolean(v));
        }

        /// <summary>
        /// 获取字符串值
        /// </summary>
        public static String GetStringValue(DataRow drValue, String columnName, String defaultValue)
        {
            return GetValue<String>(drValue, columnName, defaultValue == null ? String.Empty : defaultValue, v => Convert.ToString(v).Trim());
        }

        /// <summary>
        /// 获取可空长整型,张伟平20130917添加
        /// </summary>
        /// <param name="drValue"></param>
        /// <param name="columnName"></param>
        /// <param name="defaultValue"></param>
        /// <returns></returns>
        public static long GetLongValue(DataRow drValue, String columnName, long defaultValue)
        {
            return GetValue<long>(drValue, columnName, defaultValue, v => Convert.ToInt64(v));
        }

        /// <summary>
        /// 获取整形值
        /// </summary>
        public static Int32 GetInt32Value(DataRow drValue, String columnName, Int32 defaultValue)
        {
            return GetValue<Int32>(drValue, columnName, defaultValue, v => Convert.ToInt32(v));
        }

        /// <summary>
        /// 获取可空整形值
        /// </summary>
        public static Int32? GetInt32NullableValue(DataRow drValue, String columnName, Int32? defaultValue)
        {
            return GetValue<Int32?>(drValue, columnName, defaultValue, v => Convert.ToInt32(v));
        }

        /// <summary>
        /// 获取浮点值
        /// </summary>
        public static float GetFloatValue(DataRow drValue, String columnName, float defaultValue)
        {
            return GetValue<float>(drValue, columnName, defaultValue, v => float.Parse(v.ToString()));
        }

        /// <summary>
        /// 获取可空浮点值
        /// </summary>
        public static float? GetFloatNullableValue(DataRow drValue, String columnName, float? defaultValue)
        {
            return GetValue<float?>(drValue, columnName, defaultValue, v => float.Parse(v.ToString()));
        }

        /// <summary>
        /// 获取浮点值
        /// </summary>
        public static double GetDoubleValue(DataRow drValue, String columnName, double defaultValue)
        {
            return GetValue<double>(drValue, columnName, defaultValue, v => double.Parse(v.ToString()));
        }

        /// <summary>
        /// 获取可空浮点值
        /// </summary>
        public static double? GetDoubleNullableValue(DataRow drValue, String columnName, double? defaultValue)
        {
            return GetValue<double?>(drValue, columnName, defaultValue, v => double.Parse(v.ToString()));
        }

        /// <summary>
        /// 获取可空日期值
        /// </summary>
        public static DateTime? GetDateTimeNullableValue(DataRow drValue, String columnName, DateTime? defaultValue)
        {
            return GetValue<DateTime?>(drValue, columnName, defaultValue, v => Convert.ToDateTime(v));
        }

        /// <summary>
        /// 获取日期值
        /// </summary>
        public static DateTime GetDateTimeValue(DataRow drValue, String columnName, DateTime defaultValue)
        {
            return GetValue<DateTime>(drValue, columnName, defaultValue, v => Convert.ToDateTime(v));
        }

        private static T GetValue<T>(DataRow drValue, String columnName, T defaultValue, Func<Object, T> func)
        {
            return (!drValue.Table.Columns.Contains(columnName)
                    || drValue[columnName] == null
                    || drValue[columnName] == DBNull.Value)
                    ? defaultValue
                    : func(drValue[columnName]);
        }

        public bool TestConnection()
        {
            try
            {
                return OpenConnection();
            }
            catch (Exception)
            {
                return false;
            }
        }
        #endregion


        #region Create DbParameter

        public abstract DbParameter GetReturnDbParameter();

        /// <summary>
        /// 添加参数
        /// </summary>
        /// <param name="parameter"></param>
        public void AddDbParameter(DbParameter parameter)
        {
            Command.Parameters.Add(parameter);
        }

        public void AddDbParameter(string parameterName, DbType dbType, object parameterValue) 
        {
            DbParameter dbParameter = GetDbParameter(parameterName, dbType, parameterValue);
            AddDbParameter(dbParameter);
        }
        /// <summary>
        /// 取得返回参数值
        /// </summary>
        public abstract Object GetReturnDbParameterValue();


        /// <summary>
        /// 取得参数
        /// </summary>
        /// <param name="cmd">DbCommand</param>
        /// <param name="parameterName">参数名称</param>
        /// <param name="dbType">参数类型</param>
        /// <param name="parameterValue">值</param>
        /// <returns>数据参数</returns>
        public DbParameter GetDbParameter(string parameterName, DbType dbType, object parameterValue, Int32 size)
        {
            var parameter = GetDbParameter(parameterName, dbType, parameterValue, ParameterDirection.Input, "");
            parameter.Size = size;
            return parameter;
        }

        /// <summary>
        /// 取得参数
        /// </summary>
        /// <param name="cmd">DbCommand</param>
        /// <param name="parameterName">参数名称</param>
        /// <param name="dbType">参数类型</param>
        /// <param name="parameterValue">值</param>
        /// <returns>数据参数</returns>
        public DbParameter GetDbParameter(string parameterName, DbType dbType, object parameterValue)
        {
            return GetDbParameter(parameterName, dbType, parameterValue, ParameterDirection.Input, "");
        }

        /// <summary>
        /// 取得参数
        /// </summary>
        /// <param name="cmd">DbCommand</param>
        /// <param name="parameterName">参数名称</param>
        /// <param name="dbType">参数类型</param>
        /// <param name="sourceColumn">对应的数据列名称</param>
        /// <param name="parameterValue">值</param>
        /// <returns>数据参数</returns>        
        public DbParameter GetDbParameter(string parameterName, DbType dbType, object parameterValue, string sourceColumn)
        {
            return GetDbParameter(parameterName, dbType, parameterValue, ParameterDirection.Input, sourceColumn);
        }

        /// <summary>
        /// 取得参数
        /// </summary>
        /// <param name="cmd">DbCommand</param>
        /// <param name="parameterName">参数名称</param>
        /// <param name="dbType">参数类型</param>        
        /// <param name="parameterValue">值</param>
        /// <param name="parameterDirection">方向</param>
        /// <returns>数据参数</returns>
        public DbParameter GetDbParameter(string parameterName, DbType dbType, object parameterValue, ParameterDirection parameterDirection)
        {
            return GetDbParameter(parameterName, dbType, parameterValue, parameterDirection, "");
        }

        /// <summary>
        /// 取得参数
        /// </summary>
        /// <param name="cmd">DbCommand</param>
        /// <param name="parameterName">参数名称</param>
        /// <param name="dbType">参数类型</param>        
        /// <param name="sourceColumn">对应的数据列名称</param>   
        /// <param name="parameterValue">值</param>
        /// <param name="parameterDirection">方向</param>
        /// <returns>数据参数</returns>
        public DbParameter GetDbParameter(string parameterName, DbType dbType, object parameterValue, ParameterDirection parameterDirection, string sourceColumn)
        {
            if (!Enum.IsDefined(typeof(ParameterDirection), parameterDirection))
                throw new ArgumentOutOfRangeException("parameterDirection");

            var dbParameter = Command.CreateParameter();
            dbParameter.ParameterName = BuildParameterName(parameterName); ;
            dbParameter.DbType = dbType;
            dbParameter.SourceColumn = sourceColumn;

            dbParameter.Direction = parameterDirection;
            if (parameterValue == null && dbType == DbType.AnsiString)
                parameterValue = DBNull.Value;

            dbParameter.Value = parameterValue;

            return dbParameter;
        }

        private static string BuildParameterName(string parameterName)
        {
            //Sybase,SQLServer前缀需加@
            return "@" + parameterName;
        }
        #endregion


        #region Execute Function
        /// <summary>
        /// 构建存储过程命令
        /// </summary>
        public DbCommand CreateProcedureCommand(string procedureName)
        {
            CreateDatabaseConnection();
            CreateDatabaseCommand();
            Command.CommandText = procedureName;
            Command.CommandType = CommandType.StoredProcedure;
            return Command;
        }

        /// <summary>
        /// 构建文本命令
        /// </summary>
        public DbCommand CreateTextCommand(string sql)
        {
            CreateDatabaseConnection();
            CreateDatabaseCommand();
            Command.CommandText = sql;
            Command.CommandType = CommandType.Text;
            return Command;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public IDataReader ExecuteReader() 
        {
            IDataReader  res;
            try
            {
                res = Command.ExecuteReader();
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return res;
        }
        /// <summary>
        /// 执行命令
        /// </summary>
        /// <param name="cmd">DbCommand</param>
        /// <returns>影响行数</returns>
        public int ExecuteNoQuery()
        {
            var res = 0;
            try
            {
                res = Command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return res;
        }

        /// <summary>
        /// 执行命令
        /// </summary>
        /// <param name="cmd">DbCommand</param>
        /// <returns>首行首列内容</returns>
        public object ExecuteScalar()
        {
            var res = new Object();
            try
            {
                res = Command.ExecuteScalar();
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return res;
        }

        /// <summary>
        /// 执行命令
        /// </summary>
        /// <param name="cmd">DbCommand</param>
        /// <returns>结果数据表</returns>
        public DataTable ExecuteDataTable()
        {
            var result = new DataTable();
            try
            {
                var fact = GetClientFactory();
                DataAdapter = fact.CreateDataAdapter();
                DataAdapter.SelectCommand = Command;
                DataAdapter.Fill(result);
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return result;
        }

        /// <summary>
        /// 执行命令
        /// </summary>
        /// <param name="cmd">DbCommand</param>
        /// <returns>结果数据表</returns>
        public void ExecuteDataTable(DataTable result)
        {
            try
            {
                var fact = GetClientFactory();
                DataAdapter = fact.CreateDataAdapter();
                DataAdapter.SelectCommand = Command;
                DataAdapter.Fill(result);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        /// <summary>
        /// 执行命令
        /// </summary>
        /// <param name="cmd">DbCommand</param>
        /// <returns>结果数据集</returns>
        public DataSet ExecuteDataSet()
        {
            var result = new DataSet();
            try
            {
                var fact = GetClientFactory();
                DataAdapter = fact.CreateDataAdapter();
                DataAdapter.SelectCommand = Command;
                DataAdapter.Fill(result);
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return result;
        }

        public void CreateNewCommand()
        {
            CreateDatabaseConnection();
            CreateDatabaseCommand();
        }

        #endregion
        public void Dispose()
        {
            if (Connection != null)
            {
                Connection.Close();
                Connection.Dispose();
            }
            if (DataAdapter != null)
            {
                DataAdapter.Dispose();
            }
            if (Command != null)
            {
                Command.Dispose();
            }
        }
    }
}
