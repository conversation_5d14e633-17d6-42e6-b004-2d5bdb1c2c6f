﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// LSC应答FSU注册报文
    /// </summary>
    public sealed class LoginAck: BMessage
    {
        public EnumResult Result { get; private set; }

        public string FailureCause { get; private set; }

        public LoginAck(EnumResult result, string failureCause):base()
        {
            MessageType = (int)BMessageType.LOGIN_ACK;
            Result = result;
            FailureCause = failureCause;
        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmlDoc = CreateXmlDocument();
                AppendPK_Type(xmlDoc, "Response", BMessageType.LOGIN_ACK.ToString());

                XmlElement xe2 = xmlDoc.CreateElement("Info");
                XmlElement xe21 = xmlDoc.CreateElement("Result");
                xe21.InnerText = ((int)Result).ToString();
                XmlElement xe22 = xmlDoc.CreateElement("FailureCause");
                xe22.InnerText = FailureCause;

                xe2.AppendChild(xe21);
                xe2.AppendChild(xe22);
                XmlNode root = xmlDoc.SelectSingleNode("Response");
                root.AppendChild(xe2);

                StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("LoginAck.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmlDoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("LoginAck.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("LoginAck.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}: {2}, {3}", MessageId, (BMessageType)MessageType, Result, FailureCause);
        }

    }
}
