﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 更新FSU状态信息获取周期
    /// </summary>
    public class UpdateFsuinfoIntervalAck : BMessage
    {
        /// <summary>
        /// 返回结果
        /// </summary>
        public EnumResult Result { get; set; }

        /// <summary>
        /// 失败的原因
        /// </summary>
        public string FailureCause { get; set; }

        public UpdateFsuinfoIntervalAck()
            : base()
        {
            MessageType = (int)BMessageType.UPDATE_FSUINFO_INTERVAL_ACK;
        }

        public UpdateFsuinfoIntervalAck(string fsuId, EnumResult result, string failureCause)
            : base()
        {
            MessageType = (int)BMessageType.UPDATE_FSUINFO_INTERVAL_ACK;

            FSUID = fsuId;
            Result = result;
            FailureCause = failureCause;
        }

        public static UpdateFsuinfoIntervalAck Deserialize(string json)
        {
            UpdateFsuinfoIntervalAck info = null;
            try
            {
                info = JsonConvert.DeserializeObject<UpdateFsuinfoIntervalAck>(json);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("UpdateFsuinfoIntervalAck.Deserialize:{0}", ex.Message);
                entityLogger.ErrorFormat("UpdateFsuinfoIntervalAck.Deserialize:{0}", ex.StackTrace);
                info = new UpdateFsuinfoIntervalAck();
                info.ErrorMsg = ex.Message;
                info.StringXML = json;
            }
            return info;
        }

        public override string ToString()
        {
            return String.Format("{0},{1},{2}:{3},{4}",
                MessageId,(BMessageType)MessageType,FSUID,Result,FailureCause);
        }
    }
}