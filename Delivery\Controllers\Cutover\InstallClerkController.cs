﻿
using DM.TestOrder.Common.DBA;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web.Http;
using testorder.dal;
using DM.TestOrder.Common;
using DM.TestOrder.DAL;


namespace DM.TestOrder.Controllers {
     
    public class InstallClerkController : BaseApiController{


 
        //http://localhost:56324/api/InstallClerk?companyId=1&kwClerk=tom
        public HttpResponseMessage Get(int companyId=-1, string kwClerk = "") {
            kwClerk = SHelper.RegulateParam(kwClerk);

            Debug.WriteLine(companyId + "/t" + kwClerk);

            if (!string.IsNullOrEmpty(kwClerk))
                kwClerk = kwClerk.Replace("\'", "");

            var dt = WoInstallClerkDal.GetByKeywordForOneCompany(companyId, kwClerk);
            var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(dt);

            return new HttpResponseMessage() {
                Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            };

        }

        //未用，暂时保留
        public HttpResponseMessage Post([FromBody]JObject value) {
            var kwCompany = value["KwCompany"].ToString();
            var kwClerk = value["KwClerk"].ToString();

            kwCompany = SHelper.RegulateParam(kwCompany);
            kwClerk = SHelper.RegulateParam(kwClerk);


            var dt = WoInstallClerkDal.GetByKeyword(kwCompany, kwClerk);
            var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(dt);

            return new HttpResponseMessage() {
                Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            };

        }

        //作废
        //http://localhost:56324/api/InstallClerk?kwCompany=1&kwClerk=tom
        //public HttpResponseMessage Get(string kwCompany = "", string kwClerk = "") {
        //    MyLogger.Info(kwCompany);

        //    Debug.WriteLine(kwCompany + "/t" + kwClerk);

        //    if (!string.IsNullOrEmpty(kwCompany))
        //        kwCompany = kwCompany.Replace("\'", "");
        //    if (!string.IsNullOrEmpty(kwClerk))
        //        kwClerk = kwClerk.Replace("\'", "");

        //    var dt = InstallClerkDal.GetByKeyword(kwCompany, kwClerk);
        //    var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(dt);

        //    return new HttpResponseMessage() {
        //        Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
        //    };

        //}
    }
}
