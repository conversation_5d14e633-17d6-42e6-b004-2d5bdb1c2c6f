﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class SetThresholdAck : BMessage
    {
        public EnumResult Result { get; set; }

        public string FailureCause { get; set; }

        [Newtonsoft.Json.JsonProperty("DeviceList")]
        public List<DeviceSetThresholdAck> Devices { get; set; }

        #region SiteWeb配置

        [Newtonsoft.Json.JsonIgnore]
        public int MyStationId { get; set; }

        #endregion

        public SetThresholdAck() : base()
        {
            MessageType = (int)BMessageType.SET_THRESHOLD_ACK;
        }

        public SetThresholdAck(string fsuId, EnumResult result, string failureCause, List<DeviceSetThresholdAck> devices)
            : base()
        {
            MessageType = (int)BMessageType.SET_POINT_ACK;

            FSUID = fsuId;
            Result = result;
            FailureCause = failureCause;
            Devices = devices;
        }

        public static SetThresholdAck Deserialize(string json)
        {
            SetThresholdAck info = null;
            try
            {
                info = JsonConvert.DeserializeObject<SetThresholdAck>(json);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetThresholdAck.Deserialize:{0}", ex.Message);
                entityLogger.ErrorFormat("SetThresholdAck.Deserialize:{0}", ex.StackTrace);
                info = new SetThresholdAck();
                info.ErrorMsg = ex.Message;
                info.StringXML = json;
            }
            return info;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();

            sb.AppendFormat("{0}, {1}, {2}, {3}:",
                MessageId, (BMessageType)MessageType, FSUID, Devices != null ? Devices.Count : 0);
            sb.AppendLine();
            if (Devices != null)
            {
                foreach (DeviceSetThresholdAck item in Devices)
                {
                    sb.AppendFormat("{0}", item.ToString());
                    sb.AppendLine();
                }
            }

            return sb.ToString();
        }
    }

    /// <summary>
    /// 设备设置门限应答数据结构
    /// </summary>
    public sealed class DeviceSetThresholdAck
    {
        public string DeviceId { get; private set; }

        List<string> _successList;
        [JsonProperty("SuccessList")]
        public List<string> SuccessIds
        {
            get { return _successList; }
            set
            {
                _successList = value;
                SuccessIds2 = DeviceSetPointAck.GetTSignalMeasurementIds(value);
            }
        }

        List<string> _failList;
        [JsonProperty("FailList")]
        public List<string> FailIds
        {
            get { return _failList; }
            set
            {
                _failList = value;
                FailIds2 = DeviceSetPointAck.GetTSignalMeasurementIds(value);
            }
        }

        public List<TSignalMeasurementId> SuccessIds2 { get; private set; }
        public List<TSignalMeasurementId> FailIds2 { get; private set; }

        #region SiteWeb配置

        public int MyDeviceId { get; set; }

        #endregion

        public DeviceSetThresholdAck() { }

        public DeviceSetThresholdAck(string deviceId, List<TSignalMeasurementId> successIds, List<TSignalMeasurementId> failIds)
        {
            DeviceId = deviceId;

            SuccessIds2 = successIds;
            FailIds2 = failIds;

            SuccessIds = new List<string>();
            FailIds = new List<string>();

            if (SuccessIds2 != null && SuccessIds2.Count > 0)
            {
                foreach (TSignalMeasurementId tsmid in SuccessIds2)
                {
                    SuccessIds.Add(tsmid.ID + tsmid.SignalNumber);
                }
            }

            if (FailIds2 != null && FailIds2.Count > 0)
            {
                foreach (TSignalMeasurementId tsmid in FailIds2)
                {
                    FailIds.Add(tsmid.ID + tsmid.SignalNumber);
                }
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}", DeviceId);
            sb.AppendLine();

            sb.Append("SuccessList:");
            sb.AppendLine();
            foreach (TSignalMeasurementId item in SuccessIds2)
            {
                sb.AppendFormat("{0}", item);
                sb.AppendLine();
            }

            sb.Append("FailList:");
            sb.AppendLine();
            foreach (TSignalMeasurementId item in FailIds2)
            {
                sb.AppendFormat("{0}", item);
                sb.AppendLine();
            }

            return sb.ToString();
        }

    }
}