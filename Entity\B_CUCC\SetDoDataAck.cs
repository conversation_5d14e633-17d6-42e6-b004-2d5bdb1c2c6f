﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class SetDoDataAck : BMessage
    {
        public EnumResult Result { get; set; }

        //框架使用，此处不赋值
        public SetDoData MySetDoData { get; set; }

        #region SiteWeb配置

        public int MyStationId { get; set; }

        public int UserId { get; set; }
        public DateTime StartTime { get; set; }
        public int ControlId { get; set; }

        #endregion
        public SetDoDataAck() : base()
        {
            MessageType = (int)BMessageType.SET_DODATA_ACK;

        }
        public SetDoDataAck(string suids, string surids, EnumResult result) : base()
        {
            MessageType = (int)BMessageType.SET_DODATA_ACK;
            SUId = suids;
            SURId = surids;
            Result = result;
        }
        public static SetDoDataAck Deserialize(XmlDocument xmlDoc)
        {
            SetDoDataAck setTelecontrolDoDataAck = null;
            try
            {
                string suid = xmlDoc.SelectSingleNode("/Response/Info/SUId").InnerText.Trim();
                string surid = xmlDoc.SelectSingleNode("/Response/Info/SURId").InnerText.Trim();
                string result = xmlDoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim();
                setTelecontrolDoDataAck = new SetDoDataAck(suid, surid, string.IsNullOrEmpty(result) ? EnumResult.FAILURE : (EnumResult)int.Parse(result));
                entityLogger.DebugFormat("SetDoDataAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                setTelecontrolDoDataAck.StringXML = xmlDoc.InnerXml;
                return setTelecontrolDoDataAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetDoDataAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetDoDataAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                setTelecontrolDoDataAck = new SetDoDataAck();
                setTelecontrolDoDataAck.ErrorMsg = ex.Message;
                setTelecontrolDoDataAck.StringXML = xmlDoc.InnerXml;
                return setTelecontrolDoDataAck;
            }
        }
        public override string ToString()
        {
            return String.Format("{0}, {1},{2},{3}, {4}:{5}",
                MessageId, (BMessageType)MessageType,MessageType, SUId, SURId, Result.ToString());
        }
    }
}
