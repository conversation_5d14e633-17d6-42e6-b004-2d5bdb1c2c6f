﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B
{
    /// <summary>
    /// *********　用户获取FSU的状态信息响应
    /// </summary>
    public sealed class GetFsuInfoAck : BMessage
    {
        public TFsuStatus FsuStatus { get; private set; }
        public EnumResult Result { get; private set; }

        public int MyHostId { get; set; }

        public GetFsuInfoAck(string fsuId, string fsuCode, TFsuStatus status, EnumResult result)
            : base()
        {
            MessageType = (int)BMessageType.GetFsuInfoAck;
            FsuId = fsuId;
            FsuCode = fsuCode;

            FsuStatus = status;
            Result = result;
        }

        public override string Serialize()
        {
            throw new NotImplementedException();
        }

        public static GetFsuInfoAck Deserialize(XmlDocument xmldoc)
        {
            string fsuId = xmldoc.SelectSingleNode("/Response/Info/FsuId").InnerText;
            string fsuCode = xmldoc.SelectSingleNode("/Response/Info/FsuCode").InnerText;
            string cpuUsage = xmldoc.SelectSingleNode("/Response/Info/TFSUStatus/CPUUsage").InnerText;
            string memUsage = xmldoc.SelectSingleNode("/Response/Info/TFSUStatus/MEMUsage").InnerText;
            string resultString = xmldoc.SelectSingleNode("/Response/Info/Result").InnerText;
            float  cpuFloat = float.Parse(cpuUsage);
            float  memFloat = float.Parse(memUsage);

            TFsuStatus status = new TFsuStatus(cpuFloat, memFloat);

            EnumResult enumResult = new EnumResult();
            if (resultString != null && resultString != "")
            {
                enumResult = (EnumResult)Enum.Parse(typeof(EnumResult), resultString, false);
            }

            GetFsuInfoAck getFsuInfoAck = new GetFsuInfoAck(fsuId, fsuCode, status, enumResult);

            return getFsuInfoAck;
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}: {2}, {3}, {4}, {5}", MessageId, (BMessageType)MessageType, FsuId, FsuCode, FsuStatus, Result);
        }
    }
}
