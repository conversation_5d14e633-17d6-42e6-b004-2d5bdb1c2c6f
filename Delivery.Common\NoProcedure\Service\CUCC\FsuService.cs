﻿using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace Delivery.Common.NoProcedure.Service.CUCC
{
    public class FsuService
    {
        private static FsuService _instance = null;

        public static FsuService Instance
        {
            get
            {
                if (_instance == null) _instance = new FsuService();
                return _instance;
            }
        }

        public string ApproveWRFsu(string WRFsuId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, true);
                try
                {
                    if (WRFsuId == null || WRFsuId == "")
                        return "0";

                    Dictionary<string, object> realParams = new Dictionary<string, object>();

                    object FsuStatus = DBNull.Value; ;
                    object FsuName = DBNull.Value; ;
                    object SWStationId = DBNull.Value; ;
                    object StationStatus = DBNull.Value; ;
                    object HouseStatus = DBNull.Value; ;

                    realParams.Add("WRFsuId",Int32.Parse(WRFsuId));
                    DataTable dt = execHelper.ExecDataTable("SP_ApproveWRFsuCUCC_GetInfo", realParams);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        DataRow dr = dt.Rows[0];
                        FsuStatus = CommonUtils.GetNullableValue(dr.Field<int?>("FsuStatus"));
                        FsuName = CommonUtils.GetNullableValue(dr.Field<string>("FsuName"));
                        SWStationId = CommonUtils.GetNullableValue(dr.Field<int?>("SWStationId"));
                        StationStatus = CommonUtils.GetNullableValue(dr.Field<int?>("StationStatus"));
                        HouseStatus = CommonUtils.GetNullableValue(dr.Field<int?>("HouseStatus"));
                    }

                    if ((int)HouseStatus != 3)
                        return "-1";

                    if ((int)StationStatus != 3)
                        return "-2";

                    if ((int)FsuStatus == 3)
                        return "-3";


                    int SWMonitorUnitId = 0;

                    //此处预留，等负责这个存储过程的同事修改后调用
                    SWMonitorUnitId = 0;  //CALL SP_GenerateSiteWebId('TSL_MonitorUnit', SWMonitorUnitId);
                    Public_StoredService.Instance.SP_GenerateSiteWebId("TSL_MonitorUnit", out SWMonitorUnitId, execHelper);

                    realParams.Add("SWMonitorUnitId", SWMonitorUnitId);
                    realParams.Add("FsuName", FsuName);
                    execHelper.ExecuteNonQuery("SP_ApproveWRFsuCUCC_insertMonitorUnit", realParams);
                    execHelper.ExecuteNonQuery("SP_ApproveWRFsuCUCC_insertMonitorUnitCUCC", realParams);

                    int SWPortId = 0;
                    //此处预留，等负责这个存储过程的同事修改后调用
                    SWPortId = 0;  //CALL SP_GenerateSiteWebId('TSL_Port', SWPortId);
                    Public_StoredService.Instance.SP_GenerateSiteWebId("TSL_Port", out SWPortId, execHelper);

                    realParams.Clear();
                    realParams.Add("SWPortId", SWPortId);
                    realParams.Add("SWMonitorUnitId", SWMonitorUnitId);
                    execHelper.ExecuteNonQuery("SP_ApproveWRFsuCUCC_insertPort", realParams);


                    int SWSamplerUnitId = 0;
                    //此处预留，等负责这个存储过程的同事修改后调用
                    SWSamplerUnitId = 0;  //CALL SP_GenerateSiteWebId('TSL_SamplerUnit', SWSamplerUnitId );
                    Public_StoredService.Instance.SP_GenerateSiteWebId("TSL_SamplerUnit", out SWSamplerUnitId, execHelper);

                    int SWSamplerId = 0;
                    string SamplerUnitName = "";
                    string swDllPath = "";

                    realParams.Clear();
                    DataTable dtt = execHelper.ExecDataTable("SP_ApproveWRFsuCUCC_getSamplerInfo", realParams);
                    if (dtt != null && dtt.Rows.Count > 0)
                    {
                        SWSamplerId = Convert.ToInt32(dtt.Rows[0][0]);
                        SamplerUnitName = dtt.Rows[0][1].ToString();
                        swDllPath = dtt.Rows[0][2].ToString();
                    }

                    realParams.Clear();
                    realParams.Add("SWSamplerUnitId", SWSamplerUnitId);
                    realParams.Add("SWPortId", SWPortId);
                    realParams.Add("SWMonitorUnitId", SWMonitorUnitId);
                    realParams.Add("SWSamplerId", SWSamplerId);
                    realParams.Add("SamplerUnitName", SamplerUnitName);
                    realParams.Add("swDllPath", swDllPath);
                    execHelper.ExecuteNonQuery("SP_ApproveWRFsuCUCC_insertSamplerUnit", realParams);


                    int swEquipmentId = 0;
                    //此处预留，等负责这个存储过程的同事修改后调用
                    swEquipmentId = 0;  // CALL SP_GenerateSiteWebId('TBL_Equipment', swEquipmentId);
                    Public_StoredService.Instance.SP_GenerateSiteWebId("TBL_Equipment", out swEquipmentId, execHelper);

                    int swEquipmentTemplateId = 0;
                    swEquipmentTemplateId = (int)execHelper.ExecuteScalar("SP_ApproveWRFsuCUCC_getEquipmentTemplateId", realParams);

                    string swEquipmentName = "BInterface自诊断设备-"+FsuName;

                    realParams.Clear();
                    realParams.Add("SWStationId", SWStationId);
                    realParams.Add("swEquipmentId", swEquipmentId);
                    realParams.Add("swEquipmentName", swEquipmentName);
                    realParams.Add("swEquipmentTemplateId", swEquipmentTemplateId);
                    realParams.Add("SWMonitorUnitId", SWMonitorUnitId);
                    realParams.Add("SWSamplerUnitId", SWSamplerUnitId);
                    //获取v_DefaultHouseId
                    int? v_DefaultHouseId = null;
                    DataTable filedNameDt11 = execHelper.ExecDataTable("SP_WRFsu_ApproveWRFsuCUCC_GetSomeFiledName11", realParams);
                    if (filedNameDt11 != null && filedNameDt11.Rows.Count > 0)
                    {
                        v_DefaultHouseId = filedNameDt11.Rows[0].Field<int?>("v_DefaultHouseId");
                    }
                    realParams.Add("v_DefaultHouseId", CommonUtils.GetNullableValue(v_DefaultHouseId));

                    execHelper.ExecuteNonQuery("SP_ApproveWRFsuCUCC_insertEquipment", realParams);

                    realParams.Clear();
                    realParams.Add("WRFsuId", WRFsuId);
                    realParams.Add("SWMonitorUnitId", SWMonitorUnitId);
                    execHelper.ExecuteNonQuery("SP_ApproveWRFsuCUCC_updateFsuManagementCUCC", realParams);

                    realParams.Clear();
                    realParams.Add("SWStationId", SWStationId);
                    realParams.Add("SWMonitorUnitId", SWMonitorUnitId);
                    execHelper.ExecuteNonQuery("SP_ApproveWRFsuCUCC_insetConfigChangeMicroLogSWStation", realParams);

                    realParams.Clear();
                    realParams.Add("SWMonitorUnitId", SWMonitorUnitId);
                    realParams.Add("SWPortId", SWPortId);
                    execHelper.ExecuteNonQuery("SP_ApproveWRFsuCUCC_insetConfigChangeMicroLogSWMonitorUnit", realParams);
                                      
                    realParams.Add("SWSamplerUnitId", SWSamplerUnitId);
                    execHelper.ExecuteNonQuery("SP_ApproveWRFsuCUCC_insetConfigChangeMicroLogSamplerUnit", realParams);

                    realParams.Clear();
                    realParams.Add("SWStationId", SWStationId);
                    realParams.Add("swEquipmentId", swEquipmentId);
                    execHelper.ExecuteNonQuery("SP_ApproveWRFsuCUCC_insetConfigChangeMicroLogEquipmentId", realParams);

                    realParams.Clear();
                    realParams.Add("SWMonitorUnitId", SWMonitorUnitId);
                    execHelper.ExecuteNonQuery("SP_ApproveWRFsuCUCC_insetConfigChangeMicroLogMonitorUnit", realParams);

                    realParams.Clear();                  
                    execHelper.ExecuteNonQuery("SP_ApproveWRFsuCUCC_updateConfigChangeMacroLog", realParams);

                    realParams.Clear();
                    realParams.Add("WRFsuId", WRFsuId);
                    realParams.Add("SWStationId", SWStationId);
                    execHelper.ExecuteNonQuery("SP_ApproveWRFsuCUCC_insetMonitorUnitProjectInfo", realParams);
                    execHelper.Commit();

                    return "1";

                }
                catch (Exception ex)
                {
                    execHelper.Rollback();
                    Logger.Log(ex);
                    return "0";
                }
                finally
                {
                    dbHelper.Connection.Close();
                }               
            }
        }

        public string RejectWRFsu(string WRFsuId, string RejectCause)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);

                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    if (WRFsuId == null)
                    {
                        return "0";
                    }
                    realParams.Add("WRFsuId", Convert.ToInt32(WRFsuId));
                    int FsuStatus = 0;
                    DataTable dt = execHelper.ExecDataTable("SP_RejectWRFsuCUCC_FsuStatus", realParams);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        FsuStatus = Convert.ToInt32(dt.Rows[0][0]);
                    }
                    if (FsuStatus == 3)
                    {
                        return "-1";
                    }
                    realParams.Add("RejectCause", RejectCause);
                    execHelper.ExecuteNonQuery("SP_RejectWRFsuCUCC_FsuManagement", realParams);
                    return "1";
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("SP_RejectWRFsuCUCC:{0}", ex));
                    return "0";
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public DataTable GetWRFsuId(int? WRStationId, int? WRHouseId, string LogonId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    WRStationId = WRStationId == null ? -1 : WRStationId;
                    WRHouseId = WRHouseId == null ? -1 : WRHouseId;
                    realParams.Add("LogonId", LogonId);
                    realParams.Add("WRStationId", WRStationId);
                    realParams.Add("WRHouseId", WRHouseId);
                    object UserId = DBNull.Value;
                    DataTable dt = execHelper.ExecDataTable("SP_Get_WRFsuByStationOrHouseCUCC_UserId", realParams);
                    if(dt != null && dt.Rows.Count > 0)
                    {
                        UserId = CommonUtils.GetNullableValue(dt.Rows[0].Field<int?>("UserId"));
                    }
                    realParams.Add("UserId", UserId);
                    DataTable dt1 = execHelper.ExecDataTable("SP_Get_WRFsuByStationOrHouseCUCC_UserRoleMap", realParams);
                    string filterUser;
                    if(dt1 != null && dt1.Rows.Count > 0)
                    {
                        filterUser = XmlScriptProvider.Current.GetCommandSql("SP_Get_WRFsuByStationOrHouseCUCC_FsuManagement", realParams);
                    }
                    else
                    {
                        filterUser = XmlScriptProvider.Current.GetCommandSql("SP_Get_WRFsuByStationOrHouseCUCC_UserRoleMap", realParams);
                    }
                    realParams.Add("filterUser", filterUser);
                    if(WRStationId == 0 && WRHouseId == -1)
                    {
                        return execHelper.ExecDataTable("SP_Get_WRFsuByStationOrHouseCUCC_Connect_FsuManagement", realParams);
                    }
                    else if(WRStationId != -1 && WRHouseId == -1)
                    {
                        return execHelper.ExecDataTable("SP_Get_WRFsuByStationOrHouseCUCC_Connect_FsuManagement2", realParams);
                    }
                    else if(WRStationId == -1 && WRHouseId != -1)
                    {
                        object iWRSatationId = DBNull.Value;
                        DataTable dt2 = execHelper.ExecDataTable("SP_Get_WRFsuByStationOrHouseCUCC_iWRSatationId", realParams);
                        if(dt2 != null && dt2.Rows.Count > 0)
                        {
                            iWRSatationId = CommonUtils.GetNullableValue(dt2.Rows[0].Field<int?>("iWRSatationId"));
                        }
                        realParams.Add("iWRSatationId", iWRSatationId);
                        return execHelper.ExecDataTable("SP_Get_WRFsuByStationOrHouseCUCC", realParams);
                    }
                    else if(WRStationId == -1 && WRHouseId == -1)
                    {
                         return execHelper.ExecDataTable("SP_Get_WRFsuByStationOrHouseCUCC2", realParams);
                    }
                    else
                    {
                        return execHelper.ExecDataTable("SP_Get_WRFsuByStationOrHouseCUCC3", realParams);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("SP_Get_WRFsuByStationOrHouseCUCC:{0}", ex));
                    return new DataTable();
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
    }
}
