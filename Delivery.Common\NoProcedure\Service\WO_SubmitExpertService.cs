﻿using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace Delivery.Common.NoProcedure.Service
{
    public class WO_SubmitExpertService
    {
        private static WO_SubmitExpertService _instance = null;

        public static WO_SubmitExpertService Instance
        {
            get
            {
                if (_instance == null) _instance = new WO_SubmitExpertService();
                return _instance;
            }
        }

        public void ExpertCheckUpdateList(int OrderId, int CheckDicId, int IsPass, string PassNote)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("OrderId", OrderId);
                    realParams.Add("CheckDicId", CheckDicId);
                    realParams.Add("IsPass", IsPass);
                    realParams.Add("PassNote", PassNote);
                    execHelper.ExecuteNonQuery("WO_TestOrderExpertCheckList_Update", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("WO_TestOrderExpertCheckList_Update:{0}", ex));
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public string SubmitFinalDecision(int OrderId, int StateSetUserId, string FinalGeneralReuslt, string FinalDecision, string FinalNote, int FinalIsApprove)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                string resultState = "";
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("OrderId", OrderId);
                    realParams.Add("StateSetUserId", StateSetUserId);
                    realParams.Add("FinalGeneralReuslt", FinalGeneralReuslt);
                    realParams.Add("FinalDecision", FinalDecision);
                    realParams.Add("FinalNote", FinalNote);
                    realParams.Add("FinalIsApprove", FinalIsApprove);
                    DataTable dt = execHelper.ExecDataTable("SubmitFinal_OrderState", realParams);
                    if(dt == null || dt.Rows.Count == 0)
                    {
                        resultState = "工单不存在";
                        return resultState;
                    }
                    else
                    {
                        int OldOrderState = Convert.ToInt32(dt.Rows[0]["OldOrderState"]);
                        realParams.Add("OldOrderState", OldOrderState);
                        if (OldOrderState != 3)
                        {
                            resultState = "工单非待入网复审状态";
                            return resultState;
                        }
                        object nameObject = execHelper.ExecuteScalar("SubmitFinal_StateSetUserName", realParams);
                        string StateSetUserName = nameObject == null ? "" : nameObject.ToString();
                        realParams.Add("StateSetUserName", StateSetUserName);
                        int NewOrderState = 0;
                        string IsApproveString = "";
                        if (FinalIsApprove == 1)
                        {
                            NewOrderState = 4;
                            IsApproveString = "通过";
                        }
                        else
                        {
                            NewOrderState = 1;
                            IsApproveString = "返回";
                        }
                        realParams.Add("NewOrderState", NewOrderState);
                        string nowString = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                        string FlowText = nowString + "_入网复审" + IsApproveString + "_审批人:" + StateSetUserName;
                        realParams.Add("FlowText", FlowText);
                        realParams.Add("SaveTime", nowString);
                        execHelper.ExecuteNonQuery("SubmitFinal_InsertTestOrderFlow", realParams);
                        execHelper.ExecuteNonQuery("SubmitFinal_UpdateTestOrder", realParams);

                        if(NewOrderState == 4)
                        {
                            execHelper.ExecuteNonQuery("SubmitFinal_UpdateOrderApproveTime", realParams);
                            execHelper.ExecuteNonQuery("SubmitFinal_InsertTestOrderEquipItem", realParams);
                            execHelper.ExecuteNonQuery("SubmitFinal_DeleteTestOrderEquipItemt", realParams);
                            execHelper.ExecuteNonQuery("SubmitFinal_DeleteTestOrderEquipItemtSignal", realParams);
                        }
                        return "OK";
                    }

                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("WO_SubmitFinalDecision:{0}", ex));
                    return "";
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
    }
}
