﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    /// <summary>
    /// SU向SC注册请求响应报文
    /// </summary>
    public sealed class LoginAck : BMessage
    {
        public EnumRightMode RightLevel { get; private set; }

        public LoginAck(EnumRightMode rightLevel)
            : base()
        {
            MessageType = (int)BMessageType.LOGIN_ACK;
            RightLevel = rightLevel;
        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmlDoc = CreateXmlDocument();
                AppendPK_Type(xmlDoc, "Response", BMessageType.LOGIN_ACK.ToString(), ((int)BMessageType.LOGIN_ACK).ToString());

                XmlElement xe2 = xmlDoc.CreateElement("Info");
                XmlElement xe21 = xmlDoc.CreateElement("RightLevel");
                xe21.InnerText = ((int)RightLevel).ToString();

                xe2.AppendChild(xe21);
                XmlNode root = xmlDoc.SelectSingleNode("Response");
                root.AppendChild(xe2);

                StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("LoginAck.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmlDoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("LoginAck.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("LoginAck.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}: {2}", MessageId, (BMessageType)MessageType, RightLevel.ToString());
        }

    }

}
