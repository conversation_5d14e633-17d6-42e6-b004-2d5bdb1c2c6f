﻿using BDSTool.BLL.B;
using BDSTool.BLL.S2;
using BDSTool.Entity.B;
using BDSTool.Entity.S2;
using Common.Logging.Pro;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace BDSTool.BLL.Convert
{
    public class EquipmentConvert
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static bool TryParse(DevConfExBiz devEx,  ref TBL_Equipment newEquip) {
            var dev = devEx.TDevice;
            //-----------------------------------------------------------------------------
            //关联配置
            newEquip.EquipmentId = devEx.Equipment.EquipmentId;
            newEquip.EquipmentTemplateId = devEx.EquipmentTemplate.EquipmentTemplateId;
            newEquip.StationId = devEx.StationId;
            newEquip.MonitorUnitId = devEx.MonitorUnitId.Value;//工作站自诊断虚拟监控单元
            newEquip.SamplerUnitId = devEx.SamplerUnitId.Value;//SamplerUnitName = '工作站自诊断设备'
            newEquip.HouseId = devEx.HouseId;
            newEquip.EquipmentCategory = devEx.EquipmentCategory;//同模板，来源于映射表
            newEquip.HouseId = devEx.HouseId;
            //-----------------------------------------------------------------------------
            //来源于dev
            //同模板
            newEquip.EquipmentStyle = dev.Model;//devEx.EquipmentTemplate.EquipmentStyle;//kkk:  ui.EquipmentStyle=设备型号
            newEquip.Vendor = dev.Brand;//devEx.EquipmentTemplate.Vendor;//kkk ui-厂商
            newEquip.Description = dev.DevDescribe;//devEx.EquipmentTemplate.Description;

            //设备特有
            newEquip.EquipmentName = dev.DeviceName;

            //20180502 测试问题 该字段在SiteWeb中实际的意义是资产编号，跟B接口的设备编码不是一回事。所以导入时不需要填充该字段
            //newEquip.EquipmentNo = dev.DeviceID;//kkk UI-资产编号
            newEquip.EquipmentNo = "";

            newEquip.UsedDate = dev.BeginRunTime;//ui投用日期//BeginRunTime	//Char [TIME_LEN]	启用时间
            if (dev.RatedCapacity.HasValue)
                newEquip.RatedCapacity = dev.RatedCapacity.ToString();
            //-----------------------------------------------------------------------------
            //缺省值
            newEquip.EquipmentType = 1;//  //同模板 非监控设备(1)
            newEquip.InstalledModule = "0";//from carrier
            newEquip.EquipmentClass = -1;//SiteWeb导入电表模板后，大类也是-1

            newEquip.EquipmentState = 1;
            newEquip.DisplayIndex = 0;
            newEquip.ConnectState = 1;//0:中断, 1:连接中；2：异常

            newEquip.UpdateTime = DateTime.Now;
            return true;
        }

    }
}
