﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B
{
    /// <summary>
    /// SC向FSU注册请求应答报文
    /// </summary>
    public sealed class BoolMessage : BMessage
    {
        public bool Result { get; private set; }

        public BoolMessage(bool result)
            : base()
        {
            Result = result;
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}: {2}", MessageId, (BMessageType)MessageType, Result);
        }
    }
}
