﻿using Common.Logging.Pro;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;




using BDSTool.Entity.S2;
using BDSTool.DBUtility.Common;

namespace BDSTool.BLL.S2
{
    public class SignalBiz
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static bool SaveEntityList(List<TBL_Signal> signals) {
            //------------------------------------------------------------------------
            if (BatchInsertHelper.Execute(signals) == false) {
                logger.InfoFormat("SignalBiz.SaveEntityList();TBL_Signal 批量插入失败");
                return false;
            }
            //------------------------------------------------------------------------
            var propList = signals.ConvertAll(o => new TBL_SignalProperty {
                EquipmentTemplateId = o.EquipmentTemplateId,
                SignalId = o.SignalId,
                SignalPropertyId = 27
            }).ToList();
            if (BatchInsertHelper.Execute(propList) == false) {
                logger.InfoFormat("SignalBiz.SaveEntityList();TBL_SignalProperty 批量插入失败");
                return false;
            }
            //------------------------------------------------------------------------
            var listSwitch = signals.Where(o => o.SignalCategory != 1).ToList();//开关量
            var meaningsList = new List<TBL_SignalMeanings>();
            foreach (var o in listSwitch) {
                foreach(var sm in o.StateValue2Meanings){
                    var m=new TBL_SignalMeanings {
                    EquipmentTemplateId = o.EquipmentTemplateId,
                    SignalId = o.SignalId,
                    StateValue = (short)sm.Key,
                    Meanings = sm.Value                  
                    };
                    meaningsList.Add(m);
                }
            }
  
            if (BatchInsertHelper.Execute(meaningsList) == false) {
                logger.InfoFormat("SignalBiz.SaveEntityList();TBL_SignalMeanings 批量插入失败");
                return false;
            }
            //------------------------------------------------------------------------
            ConfigHelper.UpdateBaseTypeIdOfSignal(signals[0].EquipmentTemplateId);

            ConfigHelper.UpdateUnitOfSignal(signals[0].EquipmentTemplateId);
            return true;
        }
    }
}
