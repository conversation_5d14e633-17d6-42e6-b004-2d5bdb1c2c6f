﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Delivery.Common
{
    public class FileSHA256Helper
    {
        public static string sha256(string randomString)
        {
            System.Security.Cryptography.SHA256 sha = new System.Security.Cryptography.SHA256Managed();
            byte[] pwdArray = Encoding.UTF8.GetBytes(randomString);
            byte[] result = null;
            result = sha.ComputeHash(pwdArray);

            StringBuilder sb = new StringBuilder();
            foreach (byte b in result)
            {
                sb.AppendFormat("{0:x2}", b);
            }
            return sb.ToString().ToUpper();

        }
    }
}
