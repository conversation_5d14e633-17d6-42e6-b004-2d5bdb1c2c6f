﻿using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Text;

namespace Delivery.Common.NoProcedure.AccessProvider
{
    public class ExecuteHelper
    {
        private DbHelper dbHelper;
		private DbTransaction transaction;

		public ExecuteHelper(<PERSON>b<PERSON><PERSON>per helper, bool beginTransaction = false)
        {
            this.dbHelper = helper;
			dbHelper.CreateNewCommand();
			if(beginTransaction)
            {
				BeginTransaction();
			}
		}

		/// <summary> 打开一个事务 </summary>
		/// <returns>true-成功</returns>
		private bool BeginTransaction()
        {
			if(transaction == null)
			{
				transaction = dbHelper.Connection.BeginTransaction();
				dbHelper.Command.Transaction = transaction;
			}
			return true;
        }
		/// <summary> 提交事务 </summary>
		public bool Commit()
		{
			if (transaction == null)
            {
				return false;
            } else
			{
				transaction.Commit();
				return true;
			}
		}
		/// <summary> 回滚事务 </summary>
		public bool Rollback()
        {

			if (transaction == null)
			{
				return false;
			}
			else
			{
				transaction.Rollback();
				return true;
			}
		}

		public DataTable ExecDataTable(string keyName, Dictionary<string, object> realParams)
		{
			PreExec(keyName, realParams);
			return dbHelper.ExecuteDataTable();
		}

		public DataSet ExecDataSet(string keyName, Dictionary<string, object> realParams)
		{
			PreExec(keyName, realParams);
			return dbHelper.ExecuteDataSet();
		}

		public int ExecuteNonQuery(string keyName, Dictionary<string, object> realParams)
		{
			PreExec(keyName, realParams);
			return dbHelper.ExecuteNonQuery();
		}

		public IDataReader ExecuteDataReader(string keyName, Dictionary<string, object> realParams)
		{
			PreExec(keyName, realParams);
			return dbHelper.ExecuteDataReader();
		}

		public object ExecuteScalar(string keyName, Dictionary<string, object> realParams)
		{
			PreExec(keyName, realParams);
			return dbHelper.ExecuteScalar();
		}

		private void PreExec(string keyName, Dictionary<string, object> realParams)
		{
			Procedure oneProcedure = XmlScriptProvider.Current.GetProcedureByName(keyName);
			DbCommand command = XmlScriptProvider.Current.GetCommandWrapper(oneProcedure, keyName, realParams, dbHelper, false);//拼接替换后的sql
			command.Parameters.Clear();//每次调用前，清理上一次使用时可能残留的参数
			XmlScriptProvider.Current.TryToBindingParameters(oneProcedure.Parameters, realParams, dbHelper);
		}

	}
}
