﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    public enum BMessageType
    {
        /// <summary>
        /// 未定义
        /// </summary>
        UNDEFINED = 0,

        LOGIN = 101,
        LOGIN_ACK = 102,

        SEND_ALARM = 201,
        SEND_ALARM_ACK = 202,

        SEND_DATA = 301,
        SEND_DATA_ACK = 302,

        SEND_CHANGED_DATA = 401,
        SEND_CHANGED_DATA_ACK = 402,

        GET_DATA = 501,
        GET_DATA_ACK = 502,

        GET_ASYN_DATA = 601,
        GET_ASYN_DATA_ACK = 602,
        GET_ASYN_DATA_PUSH = 603,

        SET_POINT = 701,
        SET_POINT_ACK = 702,

        GET_THRESHOLD = 801,
        GET_THRESHOLD_ACK = 802,

        SET_THRESHOLD = 901,
        SET_THRESHOLD_ACK = 902,

        GET_LOGININFO = 1001,
        GET_LOGININFO_ACK = 1002,

        SET_LOGININFO = 1101,
        SET_LOGININFO_ACK = 1102,

        TIME_CHECK = 1201,
        TIME_CHECK_ACK = 1202,

        GET_FSUINFO = 1301,
        GET_FSUINFO_ACK = 1302,

        UPDATE_FSUINFO_INTERVAL = 1401,
        UPDATE_FSUINFO_INTERVAL_ACK = 1402,

        SET_FSUREBOOT = 1501,
        SET_FSUREBOOT_ACK = 1502,

        GET_STATION_CONF = 1601,
        GET_STATION_CONF_ACK = 1602,

        GET_DEV_CONF = 1701,
        GET_DEV_CONF_ACK = 1702,

        SET_DEV_STATION_CONF = 1801,
        SET_DEV_STATION_CONF_ACK = 1802,

        SET_DEV_CONF_DATA = 1901,
        SET_DEV_CONF_DATA_ACK = 1902,

        SEND_DEV_CONF_DATA = 2001,
        SEND_DEV_CONF_DATA_ACK = 2002,
    }
}