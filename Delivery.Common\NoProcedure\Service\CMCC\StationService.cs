﻿using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace Delivery.Common.NoProcedure.Service.CMCC
{
    public class StationService
    {
        private static StationService _instance = null;

        public static StationService Instance
        {
            get
            {
                if (_instance == null) _instance = new StationService();
                return _instance;
            }
        }

        public DataTable GetStationInfo(string structureId, string StationName, string StationCode, string strBegin, string strEnd, string stationType, string stationStatus, string LogonId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("LogonId", LogonId);
                    DataTable dt = execHelper.ExecDataTable("StationInfo_userId", realParams);
                    int iUserId = Convert.ToInt32(dt.Rows[0]["iUserId"]);
                    realParams.Add("iUserId", iUserId);
                    DataTable dt1 = execHelper.ExecDataTable("StationInfo_userRole", realParams);
                    if(dt1 != null && dt1.Rows.Count > 0)
                    {
                        realParams.Add("RoleMapNotNull", true);
                    }
                    else
                    {
                        realParams.Add("RoleMapNull", true);
                    }
                    string sCenterId = "";
                    string CenterName = "";
                    DataTable dt2 = execHelper.ExecDataTable("StationInfo_structureInfo", realParams);
                    if(dt2 != null && dt2.Rows.Count > 0)
                    {
                        sCenterId = dt2.Rows[0]["sCenterId"].ToString();
                        CenterName = dt2.Rows[0]["CenterName"].ToString();
                    }
                    realParams.Add("sCenterId", sCenterId);
                    realParams.Add("CenterName", CenterName);

                    string endstring = DateTime.Now.AddMonths(1).ToString("yyyy-MM-dd");
                    strBegin = string.IsNullOrEmpty(strBegin) ? "2018-03-02" : strBegin;
                    strEnd = string.IsNullOrEmpty(strEnd) ? endstring : strEnd;
                    DateTime startTime = Convert.ToDateTime(strBegin);
                    DateTime endTime = Convert.ToDateTime(strEnd);
                    //ApplyTime
                    realParams.Add("WhereApplyTime", true);
                    realParams.Add("StartTime", startTime);
                    realParams.Add("EndTime", endTime);
                    if (structureId != null && structureId != "-1")
                    {
                        realParams.Add("WhereStructureId", true);
                        realParams.Add("StructureId", "%" + structureId + "%");
                    }
                    if(stationType != null && stationType != "-1" )
                    {
                        realParams.Add("WhereStationCategory", true);
                        realParams.Add("StationCategory", stationType);
                    }
                    if(StationName!= null && StationName.Trim() != "")
                    {
                        realParams.Add("WhereStationName", true);
                        realParams.Add("WhereSWStationName", true);
                        realParams.Add("StationName", "%" + StationName.Trim() + "%");
                    }
                    if(StationCode != null && StationCode.Trim() != "" )
                    {
                        realParams.Add("WhereStationCode", true);
                        realParams.Add("StationCode", "%" + StationCode.Trim() + "%");
                    }
                    if(stationStatus != null && stationStatus != "-1")
                    {
                        realParams.Add("WhereStatus", true);
                        realParams.Add("StationStatus", stationStatus);
                    }
                    realParams.Add("SqlStr1", true);
                    realParams.Add("SqlStr2", true);
                    DataTable resultDt1 = execHelper.ExecDataTable("StationInfo_GetData1", realParams);
                    DataTable resultDt2 = execHelper.ExecDataTable("StationInfo_GetData2", realParams);
                    resultDt1.Merge(resultDt2);
                    if(resultDt1.Rows.Count > 0)
                    {
                        resultDt1.DefaultView.Sort = "ApplyTime DESC";
                        resultDt1 = resultDt1.DefaultView.ToTable();
                        resultDt1.Columns.Add("RowNumber", typeof(int));
                        for (int i = 0; i < resultDt1.Rows.Count; i++)
                        {
                            DataRow row = resultDt1.Rows[i];
                            row["RowNumber"] = i + 1;
                        }
                    }
                    return resultDt1;
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("SP_Get_WRStation:{0}", ex));
                    return new DataTable();
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public string RejectWRStation(string swsStationId, string StationCode, string RejectCause)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);

                Dictionary<string, object> realParams = new Dictionary<string, object>();
                realParams.Add("WRStationId", swsStationId);           

                try
                {
                    if (swsStationId == null || swsStationId == "")
                    {
                        return "-1";
                    }

                    Object resultSql = execHelper.ExecuteScalar("SP_RejectWRStation_StationStatus", realParams);
                    if (resultSql != null && !resultSql.ToString().Equals(""))
                    {
                        if (Convert.ToInt32(resultSql) == 3)
                        {
                            return "-2";
                        } 
                    }

                    realParams.Add("StationCode", StationCode);
                    realParams.Add("RejectCause", RejectCause);

                    execHelper.ExecuteNonQuery("SP_RejectWRStation_Update", realParams);                   
                    return "1";
                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    return "0";
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public string DeleteStation(string WRStationId, string LogonId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, true);
                try
                {
                    if (WRStationId.Equals(""))
                        return "0";

                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("LogonId", LogonId);
                    object UserId = DBNull.Value;
                    DataTable dt = execHelper.ExecDataTable("SP_Del_Station_GetUserId", realParams);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        UserId = CommonUtils.GetNullableValue(dt.Rows[0].Field<int?>("UserId"));
                    }
                    realParams.Add("UserId", UserId);
                    int IsAdminRole = 0;
                    DataTable dtAdmin = execHelper.ExecDataTable("SP_Del_Station_isAdminRole", realParams);
                    if (dtAdmin != null && dtAdmin.Rows.Count == 4)
                        IsAdminRole = 1;

                    if (IsAdminRole != 1)
                        return "-1";

                    int SWStationId = -1;
                    int StationStatus = -1;

                    realParams.Clear();
                    realParams.Add("WRStationId", WRStationId);
                    DataTable dtFsuId;
                    try
                    {
                        DataTable dtStationId = execHelper.ExecDataTable("SP_Del_Station_GetStationId", realParams);
                        if (dtStationId != null && dtStationId.Rows.Count > 0)
                        {
                            SWStationId = (int)dtStationId.Rows[0][0];
                            StationStatus = (int)dtStationId.Rows[0][1];
                        }
                        dtFsuId = execHelper.ExecDataTable("SP_Del_Station_GetFsuId", realParams);
                    }
                    catch (Exception)
                    {
                        execHelper.Rollback();
                        return "-2";
                    }
                    try
                    {
                        foreach (DataRow row in dtFsuId.Rows)
                        {
                            int WRFsuId = (int)row[0];
                            BslPartSPService.Instance.DelWRFsu(WRFsuId, LogonId, 0, execHelper);
                        }
                    }
                    catch (Exception)
                    {
                        execHelper.Rollback();
                        return "-3";
                    }
                    DataTable dtHouseId;
                    try
                    {
                        dtHouseId = execHelper.ExecDataTable("SP_Del_Station_GetHouseId", realParams);
                    }
                    catch (Exception)
                    {
                        execHelper.Rollback();
                        return "-4";
                    }

                    try
                    {
                        foreach (DataRow row in dtHouseId.Rows)
                        {
                            string WRHouseId = row[0].ToString();
                            SPDelWRHouseService.Instance.DeleteHouse(WRHouseId, LogonId);
                        }
                    }
                    catch (Exception)
                    {
                        execHelper.Rollback();
                        return "-5";
                    }
                    try
                    {
                        string StationName = "";
                        DataTable nameDt = execHelper.ExecDataTable("SP_Del_Station_GetStationName", realParams);
                        if (nameDt != null && nameDt.Rows.Count > 0)
                        {
                            StationName = nameDt.Rows[0][0].ToString();
                        }
                        string LastString = "SWStationId:" + WRStationId;
                        Public_StoredService.Instance.SP_WR_OperationRecord(Int32.Parse(WRStationId), StationName, 2, Int32.Parse(WRStationId), StationName, "", LastString, LogonId);
                        execHelper.ExecuteNonQuery("SP_Del_Station_DelStationManagement", realParams);

                    }
                    catch (Exception)
                    {
                        execHelper.Rollback();
                        return "-6";
                    }

                    if (StationStatus == 3)
                    {
                        try
                        {
                            Public_StoredService.Instance.PCT_DeleteStation(SWStationId);
                            String ObjectId = SWStationId.ToString();
                            PblConfigChangeLogService.Instance.DoExecute(ObjectId, 1, 3);
                        }
                        catch (Exception)
                        {
                            execHelper.Rollback();
                            return "-7";
                        }

                        realParams.Clear();
                        realParams.Add("SWStationId", SWStationId);
                        try
                        {
                            execHelper.ExecuteNonQuery("SP_Del_Station_DelStationCMCC", realParams);
                        }
                        catch (Exception)
                        {
                            execHelper.Rollback();
                            return "-10";
                        }
                        try
                        {
                            execHelper.ExecuteNonQuery("SP_Del_Station_DelMonitorUnitCMCC", realParams);
                        }
                        catch (Exception)
                        {
                            execHelper.Rollback();
                            return "-8";
                        }
                        try
                        {
                            execHelper.ExecuteNonQuery("SP_Del_Station_DelEquipmentCMCC", realParams);
                        }
                        catch (Exception)
                        {
                            execHelper.Rollback();
                            return "-9";
                        }
                    }
                    execHelper.Commit();
                    return "1";
                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    return "0";
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public DataTable GetStationInfoCheck(string structureId, string StationName, string StationCode, string strBegin, string strEnd, string stationType, string stationStatus, string LogonId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    DateTime startTime = Convert.ToDateTime(strBegin);
                    DateTime endTime = Convert.ToDateTime(strEnd);
                    if(startTime > endTime)
                    {
                        return null;
                    }
                    endTime = endTime == null ? DateTime.Now : endTime;
                    startTime = startTime == null ? endTime.AddMonths(-1) : startTime;
                    realParams.Add("WhereTime", true);
                    realParams.Add("StartTime", startTime);
                    realParams.Add("EndTime", endTime);
                    if(structureId != null && structureId != "-1")
                    {
                        realParams.Add("WhereStructureId", true);
                        realParams.Add("StructureId", "%" + structureId + "%");
                    }
                    if(stationType != null && stationType != "-1")
                    {
                        realParams.Add("WhereStationCategory", true);
                        realParams.Add("StationCategory", stationType);
                    }
                    if(StationName != null && StationName.Trim() != "")
                    {
                        realParams.Add("WhereStationName", true);
                        realParams.Add("WhereSWStationName", true);
                        realParams.Add("StationName", "%" + StationName.Trim() + "%");
                    }
                    if(StationCode != null && StationCode.Trim() != "")
                    {
                        realParams.Add("WhereStationCode", true);
                        realParams.Add("StationCode", "%" + StationCode.Trim() + "%");
                    }
                    string sCenterId = "";
                    string CenterName = "";
                    DataTable dt = execHelper.ExecDataTable("StationInfo_structureInfo", realParams);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        sCenterId = dt.Rows[0]["sCenterId"].ToString();
                        CenterName = dt.Rows[0]["CenterName"].ToString();
                    }
                    realParams.Add("sCenterId", sCenterId);
                    realParams.Add("CenterName", CenterName);
                    realParams.Add("SqlStr1", true);
                    realParams.Add("SqlStr2", true);
                    DataTable rTable = execHelper.ExecDataTable("SP_Get_WRStationCondition", realParams);
                    return rTable;
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("SP_Get_WRStationCondition:{0}", ex));
                    return null;
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }


        public DataTable SP_ApproveWRStation(int?WRStationId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                realParams.Add("WRStationId", WRStationId);
               
                try
                {
                    int myStatus;int UserId;int SWStationId;int CenterId;
                    int AreaId;int StructureId;
                    if(WRStationId == null)
                    {
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { 0 } }
                        };
                    }
                    DataTable dt = executeHelper.ExecDataTable("SP_ApproveWRStation_Select_StationManagement", realParams);
                    if(dt!=null && dt.Rows.Count > 0)
                    {
                        myStatus = (int)dt.Rows[0]["myStatus"];
                        UserId = (int)dt.Rows[0]["UserId"];
                        realParams.Add("UserId", UserId);
                        realParams.Add("myStatus", myStatus);
                        if(myStatus.Equals(3))
                        {
                            return new DataTable
                            {
                                Columns = { new DataColumn("ReturnValue", typeof(int)) },
                                Rows = { new object[] { -1 } }
                            };
                        }
                    }
                    try
                    {
                        Public_StoredService.Instance.SP_GenerateSiteWebId("TBL_Station", out SWStationId);
                        realParams.Add("SWStationId", SWStationId);
                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] {-2 } }
                        };

                    }
                    string currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    realParams.Add("currentTime", currentTime);
                    
                    try
                    { 
                        executeHelper.ExecuteNonQuery("SP_ApproveWRStation_Update_StationManagement", realParams);

                    }catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -3 } }
                        };
                    }
                 
                    DataTable dt2 = executeHelper.ExecDataTable("SP_ApproveWRStation_Select_StationStructure", realParams);
                    if (dt2 != null && dt.Rows.Count > 0)
                    {
                        CenterId = (int)dt2.Rows[0]["CenterId"];
                        realParams.Add("CenterId", CenterId);
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRStation_Insert_Station", realParams);

                    }catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -4 } }
                        };
                    }  
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRStation_Insert_StationCMCC", realParams);
                    }catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -5 } }
                        };
                    }
                    
                    DataTable dt3 = executeHelper.ExecDataTable("SP_ApproveWRStation_StructureId", realParams);
                    if(dt3!=null && dt3.Rows.Count > 0)
                    {
                        StructureId = (int)dt3.Rows[0]["StructureId"];
                        realParams.Add("StructureId", StructureId);
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRStation_Insert_StationStructureMap", realParams);
                    }catch(Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -6 } }
                        };
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRStation_Insert_House", realParams);
                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -7 } }
                        };
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRStation_Insert_RoomCMCC", realParams);
                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -7 } }
                        };
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRStation_Insert_ConfigChangeMicroLog", realParams);
                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -8 } }
                        };
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRStation_Insert_ConfigChangeMicroLog2", realParams);
                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -9 } }
                        };
                    }

                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRStation_Insert_ConfigChangeMacroLog", realParams);
                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -10 } }
                        };
                    } 
                    DataTable dt4 = executeHelper.ExecDataTable("SP_ApproveWRStation_Select_UserRoleMap", realParams);
                    if(dt4!=null && dt4.Rows.Count > 0)
                    {
                        AreaId = (int)dt4.Rows[0]["AreaId"];
                        realParams.Add("AreaId", AreaId);
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRStation_Insert_AreaMap", realParams);
                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -11 } }
                        };
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRStation_Insert_StationProjectInfo", realParams);
                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -12 } }
                        };
                    }
                    
                    executeHelper.Commit();
                    return new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { 1 } }
                    };
                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    executeHelper.Rollback();
                    return new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { -2 } }
                    };
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
    }
}
