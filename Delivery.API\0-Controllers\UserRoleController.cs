﻿using Delivery.BSL;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Web;

namespace Delivery.API._0_Controllers
{

    public class UserRoleController: BaseController
    {
        [HttpGet]
        public string Get()
        {

            return HttpUtility.UrlEncode(GetUserRoleType(LogonId), Encoding.UTF8);
        }

        private string GetUserRoleType(string logonid)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            DataTable dt = ccbsl.GetUserRoleType(logonid);
            if (dt != null && dt.Rows.Count > 0)
                return dt.Rows[0]["RoleTypeName"].ToString();
            else
                return string.Empty;
        }
    }
}
