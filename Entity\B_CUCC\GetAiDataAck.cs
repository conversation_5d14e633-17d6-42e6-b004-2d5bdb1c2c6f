﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class GetAiDataAck : BMessage
    {
        public string ReportTime { get; set; }
        public List<Device> LDevice { get; set; }

        #region SiteWeb配置

        public int MyStationId { get; set; }

        #endregion
        public GetAiDataAck() : base()
        {
            MessageType = (int)BMessageType.GET_AIDATA_ACK;
        }
        public GetAiDataAck(string suids, string surids, string reportTime, List<Device> device) : base()
        {
            MessageType = (int)BMessageType.GET_AIDATA_ACK;
            SUId = suids;
            SURId = surids;
            ReportTime = reportTime;
            LDevice = device;
        }
        public static GetAiDataAck Deserialize(XmlDocument xmlDoc)
        {
            GetAiDataAck getAiDataAck = null;
            string errMsg = string.Empty;
            string suid = string.Empty;
            string surid = string.Empty;
            try
            {
                suid = xmlDoc.SelectSingleNode("/Response/Info/SUId").InnerText.Trim();
                surid = xmlDoc.SelectSingleNode("/Response/Info/SURId").InnerText.Trim();
                string reportTime = xmlDoc.SelectSingleNode("/Response/Info/ReportTime").InnerText.Trim();
                XmlNodeList nodes = xmlDoc.SelectNodes("/Response/Info/Values/DeviceList/Device");
                List<Device> list = new List<Device>();
                foreach (XmlElement node in nodes)
                {
                    string id = node.GetAttribute("Id");
                    string rId = node.GetAttribute("RId");
                    List<Signal> lst = new List<Signal>();
                    List<string> signalIds = new List<string>();
                    XmlNodeList lists = node.SelectNodes("Signal");
                    foreach (XmlElement n in lists)
                    {
                        string strSignalId = n.GetAttribute("Id");
                        if (signalIds.Contains(strSignalId))
                        {
                            entityLogger.ErrorFormat(" GET_AIDATA_ACK 协议中发现信号ID有重复, ID={0}", strSignalId);
                            continue;
                        }
                        else
                        {
                            signalIds.Add(strSignalId);
                        }
                        Signal signal = new Signal();
                        signal.Id = strSignalId;
                        string strValue = n.GetAttribute("Value").Trim();
                        if (!string.IsNullOrEmpty(strValue))
                        {
                            signal.Value = float.Parse(strValue);
                        }
                        else
                        {
                            //errMsg = "GET_AIDATA_ACK Signal Value is nullOrEmpty";
                            //break;//联通比较特殊，SC获取AI量配置也是走此协议，因此允许信号值为空,取配置值时可以之送信号ID不送值
                            entityLogger.InfoFormat("GET_AIDATA_ACK Signal Value is nullOrEmpty, Signal Id : {0}", signal.Id);
                        }
                        lst.Add(signal);
                    }
                    Device device = new Device(id, rId, lst);
                    list.Add(device);
                }
                if (errMsg != string.Empty)
                {
                    getAiDataAck = GetErrorEntity(suid, surid, errMsg);
                }
                else
                {
                    getAiDataAck = new GetAiDataAck(suid, surid, reportTime, list);
                }
                entityLogger.DebugFormat("GetAiDataAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                getAiDataAck.StringXML = xmlDoc.InnerXml;
                return getAiDataAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetAiDataAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetAiDataAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                getAiDataAck = new GetAiDataAck();
                getAiDataAck.ErrorMsg = ex.Message;
                getAiDataAck.StringXML = xmlDoc.InnerXml;
                return getAiDataAck;
            }
        }

        /// <summary>
        /// 反序列化异常时，构造带错误信息的实体
        /// </summary>
        /// <param name="errorMsg">解析错误信息</param>
        /// <returns></returns>
        private static GetAiDataAck GetErrorEntity(string suId, string surId, string errorMsg)
        {
            GetAiDataAck errorEntity = new GetAiDataAck();
            errorEntity.SUId = suId;
            errorEntity.SURId = surId;
            errorEntity.ErrorMsg = errorMsg;
            return errorEntity;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            if (LDevice.Count > 0)
            {
                foreach (Device de in LDevice)
                {
                    sb.AppendFormat("{0},{1},{2},{3},{4},{5}", MessageId, (BMessageType)MessageType,MessageType, SUId, SURId, de.PartValueToString());
                }
            }
            else
            {
                sb.AppendFormat("{0},{1},{2},{3},{4},Device is null", MessageId, (BMessageType)MessageType,MessageType, SUId, SURId);
            }
            return sb.ToString();
        }
    }
}
