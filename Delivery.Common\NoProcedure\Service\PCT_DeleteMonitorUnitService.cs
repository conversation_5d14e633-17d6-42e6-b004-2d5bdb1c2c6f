﻿using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace Delivery.Common.NoProcedure.Service
{
    public class PCT_DeleteMonitorUnitService
    {
        private static PCT_DeleteMonitorUnitService _instance = null;

        public static PCT_DeleteMonitorUnitService Instance
        {
            get
            {
                if (_instance == null) _instance = new PCT_DeleteMonitorUnitService();
                return _instance;
            }
        }

        public void PCT_DeleteMonitorUnit(int MonitorUnitId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("MonitorUnitId", MonitorUnitId);
                    execHelper.ExecuteNonQuery("PCT_DeleteMonitorUnit_ProjectInfo", realParams);
                    execHelper.ExecuteNonQuery("PCT_DeleteMonitorUnit_EquipmentProjectInfo", realParams);
                    execHelper.ExecuteNonQuery("PCT_DeleteMonitorUnit_Equipment", realParams);
                    execHelper.ExecuteNonQuery("PCT_DeleteMonitorUnit_Signal", realParams);
                    execHelper.ExecuteNonQuery("PCT_DeleteMonitorUnit_Event", realParams);
                    execHelper.ExecuteNonQuery("PCT_DeleteMonitorUnit_Control", realParams);
                    execHelper.ExecuteNonQuery("PCT_DeleteMonitorUnit_SamplerUnit", realParams);
                    execHelper.ExecuteNonQuery("PCT_DeleteMonitorUnit_Port", realParams);
                    object vStationId = DBNull.Value;
                    DataTable dt = execHelper.ExecDataTable("PCT_DeleteMonitorUnit_getStationId", realParams);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        DataRow dr = dt.Rows[0];
                        vStationId = CommonUtils.GetNullableValue(dr.Field<int?>("myStatus"));
                    }
                    execHelper.ExecuteNonQuery("PCT_DeleteMonitorUnit_MonitorUnit", realParams);
                    realParams.Add("vStationId", vStationId);
                    DataTable dt1 = execHelper.ExecDataTable("PCT_DeleteMonitorUnit_getCount", realParams);
                    if(dt1 != null && dt1.Rows.Count > 0)
                    {
                        if(Convert.ToInt32(dt1.Rows[0][0]) < 1)
                        {
                            execHelper.ExecuteNonQuery("PCT_DeleteMonitorUnit_updateStation", realParams);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("PCT_DeleteMonitorUnit:{0}", ex));
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
    }
}
