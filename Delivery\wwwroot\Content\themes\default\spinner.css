.spinner {
  display: inline-block;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  border-width: 1px;
  border-style: solid;
  overflow: hidden;
  vertical-align: middle;
}
.spinner .spinner-text {
  font-size: 12px;
  border: 0px;
  line-height: 20px;
  height: 20px;
  margin: 0;
  padding: 0 2px;
  *margin-top: -1px;
  *height: 18px;
  *line-height: 18px;
  _height: 18px;
  _line-height: 18px;
  vertical-align: baseline;
}
.spinner-arrow {
  display: inline-block;
  overflow: hidden;
  vertical-align: top;
  margin: 0;
  padding: 0;
}
.spinner-arrow-up,
.spinner-arrow-down {
  opacity: 0.6;
  filter: alpha(opacity=60);
  display: block;
  font-size: 1px;
  width: 18px;
  height: 10px;
}
.spinner-arrow-hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.spinner-arrow-up {
  background: url('images/spinner_arrows.png') no-repeat 1px center;
}
.spinner-arrow-down {
  background: url('images/spinner_arrows.png') no-repeat -15px center;
}
.spinner {
  border-color: #95B8E7;
}
.spinner-arrow {
  background-color: #E0ECFF;
}
.spinner-arrow-hover {
  background-color: #eaf2ff;
}
