﻿@page
@model Delivery.Pages.CUCC.NewOrderModel
@{
}

@section Scripts {
    <script type="text/javascript" src="~/Scripts/lib/jquery-1.8.0.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="~/Scripts/common/ie8.js"></script>
    <script type="text/javascript" src="~/Scripts/common/common.js"></script>
    <script type="text/javascript" src="~/CUCC/scripts/NewOrder.js"></script>
}

@section Styles {
    <style type="text/css">
        * {
            font-size: 12px;
        }

        .labTdStyle {
            text-align: left;
            white-space: nowrap;
            width: 50px;
        }

        .contorlTdStyle {
            text-align: left;
            white-space: nowrap;
            width: 300px;
        }

        .textbox.textbox-readonly > input {
            background-color: #eaefed;
        }
    </style>
}

<div class="mask">
    <div style="position: relative; top: 50%; left: 50%">
        <div class="loading">
            正在加载，请等待...
        </div>
    </div>
</div>
<div id="StationDialog" title="站址选择" style="width: 700px; height: 550px; padding: 5px; display: none;" class="easyui-dialog" data-options="modal:true,closed:true,closable:true">
    <table style="width: 100%;">
        <tr>
            <td style="text-align: left; white-space: nowrap; width: 50px;">
                站址名称
            </td>
            <td style="text-align: left; white-space: nowrap; width: 200px;">
                <input id="StationName" class="easyui-textbox" style="width: 100%;" data-options="validType:'maxLength[255]'" />
            </td>
            <td style="text-align: right; white-space: nowrap; width: 50px;">
                站址编码
            </td>
            <td style="text-align: left; white-space: nowrap; width: 200px;">
                <input id="StationCode" class="easyui-textbox" style="width: 100%;" data-options="validType:'maxLength[255]'" />
            </td>
            <td style="width: 300px;"></td>
        </tr>
        <tr>
            <td style="text-align: left; white-space: nowrap;" colspan="3"></td>
            <td style="text-align: right;">
                <input type="button" id="btnSeach" value="查询" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btnResetCondition" value="重置" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btnChoose" value="选择" class="commonButton" style="width: 40pt;" />
            </td>
        </tr>
    </table>
    <div>
        <table id="dgStation"></table>
    </div>
</div>
<div id="EquipsDialog" title="设备选择" style="width: 700px; height: 550px; padding: 5px; display: none;" class="easyui-dialog" data-options="modal:true,closed:true,closable:true">
    <table style="width: 100%;">
        <tr>
            <td style="text-align: left; white-space: nowrap; width: 50px;">
                机房名称
            </td>
            <td style="text-align: left; white-space: nowrap; width: 200px;">
                <input id="txtHouseName" class="easyui-textbox" style="width: 100%;" data-options="validType:'maxLength[255]'" />
            </td>
            <td style="text-align: right; white-space: nowrap; width: 50px;">
                设备名称
            </td>
            <td style="text-align: left; white-space: nowrap; width: 200px;">
                <input id="txtEquipName" class="easyui-textbox" style="width: 100%;" data-options="validType:'maxLength[255]'" />
            </td>
            <td style="width: 300px;"></td>
        </tr>
        <tr>

            <td style="text-align: right; white-space: nowrap; width: 50px;">
                设备编码
            </td>
            <td style="text-align: left; white-space: nowrap; width: 200px;">
                <input id="txtEquipCode" class="easyui-textbox" style="width: 100%;" data-options="validType:'maxLength[255]'" />
            </td>
            <td style="text-align: right; white-space: nowrap;" colspan="2">
                <input type="button" id="btnEquipsSeach" value="查询" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btnEquipsReset" value="重置" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btnEquipsChoose" value="选择" class="commonButton" style="width: 40pt;" />
            </td>
        </tr>
    </table>
    <div>
        <table id="dgEquipsQuery"></table>
    </div>
</div>
<div id="InstallCompanyDialog" title="安装公司选择" style="width: 700px; height: 550px; padding: 5px; display: none;" class="easyui-dialog" data-options="modal:true,closed:true,closable:true">
    <table style="width: 100%;">
        <tr>
            <td style="text-align: left; white-space: nowrap; width: 50px;">
                公司名称
            </td>
            <td style="text-align: left; white-space: nowrap; width: 200px;">
                <input id="txtCompanyNameC" class="easyui-textbox" style="width: 100%;" data-options="validType:'maxLength[255]'" />
            </td>
            <td style="text-align: right; white-space: nowrap;">
                <input type="button" id="btnInstallCompanySeach" value="查询" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btnInstallCompanyReset" value="重置" onclick="$('#txtCompanyNameC').textbox('setValue', '');" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btnInstallCompanyChoose" value="选择" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btnInstallCompanyAdd" value="录入" class="commonButton" style="width: 40pt; display: none;" />
            </td>
            <td style="width: 50px" />
        </tr>
        <tr style="display: none;">
            <td colspan="4" style="color: #f00;">*初始无安装公司数据时,请填写公司名称点击录入安装公司</td>
        </tr>
    </table>
    <div>
        <table id="dgInstallCompanyGrid"></table>
    </div>
</div>
<div id="InstallerDialog" title="安装人员选择" style="width: 700px; height: 550px; padding: 5px; display: none;" class="easyui-dialog" data-options="modal:true,closed:true,closable:true">
    <table style="width: 100%;">
        <tr>
            <td style="text-align: right; white-space: nowrap; width: 50px;">
                人员名称
            </td>
            <td style="text-align: left; white-space: nowrap; width: 200px;">
                <input id="txtInstallerName" class="easyui-textbox" style="width: 100%;" data-options="validType:'maxLength[255]'" />
            </td>
            <td style="width: 300px;"></td>
            <td style="text-align: right; white-space: nowrap;" colspan="4">
                <input type="button" id="btnInstallerSeach" value="查询" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btnInstallerReset" value="重置" onclick="$('#txtCompanyNameI').textbox('setValue', ''); $('#txtInstallerName').textbox('setValue', '');" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btnInstallerChoose" value="选择" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btnInstallerAdd" value="录入" class="commonButton" style="width: 40pt;" />
            </td>
        </tr>
        <tr>
            <td colspan="4" style="color: #f00;">*初始无安装人员数据时,请填写人员名称点击录入安装人员</td>
        </tr>
    </table>
    <div>
        <table id="dgInstallerGrid"></table>
    </div>
</div>
<div style="min-width: 1000px; width: 100%; padding: 10px;" class="easyui-panel">
    <table style="width: 100%;">
        <tr>
            <td class="labTdStyle">割接类型</td>
            <td class="contorlTdStyle">
                <select id="OrderType" class="easyui-combobox" style="width: 200px;" editable="false">
                    <option value="0">新装入网</option>
                    <option value="1">维护巡检</option>
                </select>
            </td>
            <td class="labTdStyle"></td>
            <td class="contorlTdStyle">
                <a href="#" id="btnChooseStation" class="easyui-linkbutton" data-options="iconCls:'icon-search'" style="width: 100px" onclick="SearchStationDialog();">选择站址</a>
            </td>
        </tr>
    </table>
</div>
<h1></h1>
<div id="p" class="easyui-panel" title="站址信息" style="min-width: 1000px; width: 100%; padding: 10px; text-align: center;">
    <table style="width: 100%;">
        <tr>
            <td class="labTdStyle">监控中心</td>
            <td class="contorlTdStyle">
                <input id="txtCenterName" class="easyui-textbox" style="width: 200px;" readonly="true" />
            </td>
            <td class="labTdStyle">分    组</td>
            <td class="contorlTdStyle">
                <input id="txtGroupName" class="easyui-textbox" style="width: 200px;" readonly="true" />
            </td>
        </tr>
        <tr>
            <td class="labTdStyle">站址名称</td>
            <td class="contorlTdStyle">
                <input id="txtStationName" class="easyui-textbox" style="width: 200px;" readonly="true" />
            </td>
            <td class="labTdStyle">站址编码</td>
            <td class="contorlTdStyle">
                <input id="txtStationCode" class="easyui-textbox" style="width: 200px;" readonly="true" />
            </td>
        </tr>
        <tr>
            <td class="labTdStyle">站址类型</td>
            <td class="contorlTdStyle">
                <input id="txtStationCategory" class="easyui-textbox" style="width: 200px;" readonly="true" />
            </td>
            <td class="labTdStyle">经    度</td>
            <td class="contorlTdStyle">
                <input id="txtLongitude" type="text" class="easyui-numberbox" value="100" data-options="min:0,max:180,precision:17,value:0" style="width: 200px;" />
                <span style="color: red">范围:(0,180]</span>
            </td>
        </tr>
        <tr>
            <td colspan="2" class="labTdStyle"></td>
            <td class="labTdStyle">纬    度</td>
            <td class="contorlTdStyle">
                <input id="txtLatitude" type="text" class="easyui-numberbox" value="100" data-options="min:0,max:90,precision:17,value:0" style="width: 200px;" />
                <span style="color: red">范围:(0,90]</span>
            </td>
        </tr>
    </table>
</div>
<h1></h1>
<div id="p1" class="easyui-panel" title="设备信息" style="min-width: 1000px; width: 100%; padding: 10px; text-align: center;">
    <table>
        <tr>
            <td>
                <a href="#" id="btnChooseEquips" class="easyui-linkbutton" data-options="iconCls:'icon-search'" style="width: 100px" onclick="if(SelectedStationId==-1){$.messager.alert('提示','请先选择站址！','error');return false;} else { SearchEuipsDialog();}">选择设备</a>
            </td>
        </tr>
    </table>
    <div>
        <table id="dgEquips"></table>
    </div>
</div>
<h1></h1>
<div id="p2" class="easyui-panel" title="工程信息" style="min-width: 1000px; width: 100%; padding: 10px; text-align: center;">
    <table style="width: 100%;">
        <tr>
            <td class="labTdStyle">安装公司</td>
            <td class="contorlTdStyle">
                <input id="txtInstallCompany" style="width: 200px" data-options="
                            prompt: '请选择安装公司',
			                icons:[{
				                iconCls:'icon-search',
				                handler: function(e){
					                var v = $(e.data.target).textbox('getValue');
                                    $('#InstallCompanyDialog').dialog('open');
                                    $('#btnInstallCompanyReset').click();
                                    $('#btnInstallCompanySeach').click();
				                }
			                }]" />

            </td>
            <td class="labTdStyle">安装人员</td>
            <td class="contorlTdStyle">
                <input id="txtInstaller" style="width: 200px" data-options="
                            prompt: '请选择安装人员',
			                icons:[{
				                iconCls:'icon-search',
				                handler: function(e){
                                    if(SelectedCompanyId==-1){
                                        $.messager.alert('提示','请先选择安装公司');
                                        return;
                                    }
					                var v = $(e.data.target).textbox('getValue');
                                    $('#InstallerDialog').dialog('open');
                                    $('#btnInstallerReset').click();
                                    $('#btnInstallerSeach').click();
				                }
			                }]" />
            </td>
        </tr>
    </table>
</div>
<h1></h1>
<div style="min-width: 1000px; width: 100%; text-align: center;">
    <table style="width: 100%;">
        <tr style="width: 100%;">
            <td style="width: 100%; text-align: center">
                <input type="button" id="btnSubmit" value="申    请" onclick="NewOrder();" class="commonButton" style="width: 100px;" />
            </td>
        </tr>
    </table>
</div>