﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class GetFTP : BMessage
    {
        public GetFTP(string suids, string surids) : base()
        {
            MessageType = (int)BMessageType.GET_FTP;
            SUId = suids;
            SURId = surids;
        }
        public override string Serialize()
        {
            try
            {
                XmlDocument xmlDoc = CreateXmlDocument();
                AppendPK_Type(xmlDoc, "Request", BMessageType.GET_FTP.ToString(), MessageType.ToString());
                XmlElement xel1 = xmlDoc.CreateElement("Info");
                XmlElement xel12 = xmlDoc.CreateElement("SUId");
                XmlElement xel13 = xmlDoc.CreateElement("SURId");
                xel12.InnerText = SUId;
                xel13.InnerText = SURId;
                xel1.AppendChild(xel12);
                xel1.AppendChild(xel13);
                XmlNode node = xmlDoc.SelectSingleNode("Request");
                node.AppendChild(xel1);
                StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("GetFTP.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmlDoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetFTP.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetFTP.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1},{2},{3}", MessageId, (BMessageType)MessageType, SUId, SURId);
            return sb.ToString();
        }
    }
}
