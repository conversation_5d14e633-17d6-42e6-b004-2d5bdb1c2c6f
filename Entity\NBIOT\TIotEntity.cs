﻿using Newtonsoft.Json;
using System;
using System.IO;
using log4net;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class TIotEntity
    {
        protected static ILog manager = null;

        public static ILog iotLogger
        {
            get
            {
                if (manager == null)
                {
                    log4net.Repository.ILoggerRepository repository = log4net.LogManager.CreateRepository("ds");
                    var fileInfo = new FileInfo("log4net.config");
                    log4net.Config.XmlConfigurator.Configure(repository, fileInfo);
                    log4net.Config.BasicConfigurator.Configure(repository);
                    manager = LogManager.GetLogger(repository.Name, "ds");
                }

                return manager;
            }
        }

        public TIotEntity() { }

        public TIotEntity(BMessage message)
        {
            PK_Type = ((BMessageType)message.MessageType).ToString();
            //_info = message;
        }

        public string PK_Type { get; set; }

        //private dynamic _info;
        //public dynamic Info
        //{
        //    get { return _info; }
        //    set
        //    {
        //        if (_info == null && !string.IsNullOrEmpty(PK_Type) && !(value is BMessage))
        //        {
        //            _info = GetInfoByPkType(PK_Type, value.ToString());
        //        }
        //        else
        //        {
        //            _info = value;
        //        }
        //    }
        //}

        /// <summary>
        /// 根据报文类型反序列化json获得Info
        /// </summary>
        /// <param name="pkType">报文类型</param>
        /// <param name="json">json</param>
        /// <returns></returns>
        private BMessage GetInfoByPkType(string pkType, string json)
        {
            BMessageType type;
            BMessage info = null;
            Enum.TryParse<BMessageType>(pkType, out type);
            try
            {

                switch (type)
                {
                    case BMessageType.GET_ASYN_DATA_ACK:
                        info = JsonConvert.DeserializeObject<GetAsynDataAck>(json);
                        break;
                    case BMessageType.GET_ASYN_DATA_PUSH:
                        info = JsonConvert.DeserializeObject<GetAsynDataPush>(json);
                        break;
                    case BMessageType.GET_DATA_ACK:
                        info = JsonConvert.DeserializeObject<GetDataAck>(json);
                        break;
                    case BMessageType.GET_DEV_CONF_ACK:
                        info = JsonConvert.DeserializeObject<GetDevConfAck>(json);
                        break;
                    case BMessageType.GET_FSUINFO_ACK:
                        info = JsonConvert.DeserializeObject<GetFsuInfoAck>(json);
                        break;
                    case BMessageType.GET_LOGININFO_ACK:
                        info = JsonConvert.DeserializeObject<GetLoginInfoAck>(json);
                        break;
                    case BMessageType.GET_STATION_CONF_ACK:
                        info = JsonConvert.DeserializeObject<GetStationConfAck>(json);
                        break;
                    case BMessageType.GET_THRESHOLD_ACK:
                        info = JsonConvert.DeserializeObject<GetThresholdAck>(json);
                        break;
                    case BMessageType.LOGIN:
                        info = JsonConvert.DeserializeObject<Login>(json);
                        break;
                    case BMessageType.SEND_ALARM:
                        info = JsonConvert.DeserializeObject<SendAlarm>(json);
                        break;
                    case BMessageType.SEND_CHANGED_DATA:
                        info = JsonConvert.DeserializeObject<SendChangedData>(json);
                        break;
                    case BMessageType.SEND_DATA:
                        info = JsonConvert.DeserializeObject<SendData>(json);
                        break;
                    case BMessageType.SEND_DEV_CONF_DATA:
                        info = JsonConvert.DeserializeObject<SendDevConfData>(json);
                        break;
                    case BMessageType.SET_DEV_CONF_DATA_ACK:
                        info = JsonConvert.DeserializeObject<SetDevConfDataAck>(json);
                        break;
                    case BMessageType.SET_DEV_STATION_CONF_ACK:
                        info = JsonConvert.DeserializeObject<SetDevStationConfAck>(json);
                        break;
                    case BMessageType.SET_FSUREBOOT_ACK:
                        info = JsonConvert.DeserializeObject<SetFsuRebootAck>(json);
                        break;
                    case BMessageType.SET_LOGININFO_ACK:
                        info = JsonConvert.DeserializeObject<SetLoginInfoAck>(json);
                        break;
                    case BMessageType.SET_POINT_ACK:
                        info = JsonConvert.DeserializeObject<SetPointAck>(json);
                        break;
                    case BMessageType.SET_THRESHOLD_ACK:
                        info = JsonConvert.DeserializeObject<SetThresholdAck>(json);
                        break;
                    case BMessageType.TIME_CHECK_ACK:
                        info = JsonConvert.DeserializeObject<TimeCheckAck>(json);
                        break;
                    case BMessageType.UPDATE_FSUINFO_INTERVAL_ACK:
                        info = JsonConvert.DeserializeObject<UpdateFsuinfoIntervalAck>(json);
                        break;
                    case BMessageType.TIME_CHECK:
                        info = JsonConvert.DeserializeObject<TimeCheck>(json);
                        break;
                    case BMessageType.UNDEFINED:
                    default:
                        break;
                }
            }
            catch(Exception ex)
            {
                iotLogger.ErrorFormat("TIotEntity.GetInfoByPkType() 反序列化异常:{0}" , ex.Message);
                iotLogger.ErrorFormat("TIotEntity.GetInfoByPkType() 详细信息:{0}" , ex.StackTrace);
                info = CreateErrorInfo(type);
                info.ErrorMsg = ex.Message;
            }
            return info;
        }


        /// <summary>
        /// 出错时，构建相应消息类型的空实例
        /// </summary>
        /// <param name="type">BMessageType枚举对象</param>
        /// <returns></returns>
        private BMessage CreateErrorInfo(BMessageType type)
        {
            BMessage info = null;
            switch (type)
            {
                case BMessageType.GET_ASYN_DATA_ACK:
                    info = new GetAsynDataAck();
                    break;
                case BMessageType.GET_ASYN_DATA_PUSH:
                    info = new GetAsynDataPush();
                    break;
                case BMessageType.GET_DATA_ACK:
                    info = new GetDataAck();
                    break;
                case BMessageType.GET_DEV_CONF_ACK:
                    info = new GetDevConfAck();
                    break;
                case BMessageType.GET_FSUINFO_ACK:
                    info = new GetFsuInfoAck();
                    break;
                case BMessageType.GET_LOGININFO_ACK:
                    info = new GetLoginInfoAck();
                    break;
                case BMessageType.GET_STATION_CONF_ACK:
                    info = new GetStationConfAck();
                    break;
                case BMessageType.GET_THRESHOLD_ACK:
                    info = new GetThresholdAck();
                    break;
                case BMessageType.LOGIN:
                    info = new Login();
                    break;
                case BMessageType.SEND_ALARM:
                    info = new SendAlarm();
                    break;
                case BMessageType.SEND_CHANGED_DATA:
                    info = new SendChangedData();
                    break;
                case BMessageType.SEND_DATA:
                    info = new SendData();
                    break;
                case BMessageType.SEND_DEV_CONF_DATA:
                    info = new SendDevConfData();
                    break;
                case BMessageType.SET_DEV_CONF_DATA_ACK:
                    info = new SetDevConfDataAck();
                    break;
                case BMessageType.SET_DEV_STATION_CONF_ACK:
                    info = new SetDevStationConfAck();
                    break;
                case BMessageType.SET_FSUREBOOT_ACK:
                    info = new SetFsuRebootAck();
                    break;
                case BMessageType.SET_LOGININFO_ACK:
                    info = new SetLoginInfoAck();
                    break;
                case BMessageType.SET_POINT_ACK:
                    info = new SetPointAck();
                    break;
                case BMessageType.SET_THRESHOLD_ACK:
                    info = new SetThresholdAck();
                    break;
                case BMessageType.TIME_CHECK_ACK:
                    info = new TimeCheckAck();
                    break;
                case BMessageType.UPDATE_FSUINFO_INTERVAL_ACK:
                    info = new UpdateFsuinfoIntervalAck();
                    break;
                case BMessageType.UNDEFINED:
                default:
                    break;
            }
            return info;
        }


    }
}
