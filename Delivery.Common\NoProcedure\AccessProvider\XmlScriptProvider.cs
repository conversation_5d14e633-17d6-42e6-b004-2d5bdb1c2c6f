﻿using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Common;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Xml.Serialization;

namespace Delivery.Common.NoProcedure.AccessProvider
{
	/// <summary>
	/// Summary description for Procedures.
	/// </summary>
	[Serializable()]
	[XmlRoot("root")]
	public sealed class XmlScriptProvider
	{
		private ArrayList procedures;
		private ArrayList customProcedures;
		private Hashtable shortNameProcedures;
		private Hashtable longNameProcedures;

		private const string prefix = "$[";
		private const string suffix = "]";
		private string replacePattern = null;

		private static XmlScriptProvider current = null;
		private static object syncRoot = new object();

		/// <summary>
		/// Creates a new <see cref="XmlScriptProvider"/> instance.
		/// </summary>
		public XmlScriptProvider()
		{
			procedures = new ArrayList();
			customProcedures = new ArrayList();
			replacePattern = $"(?<={Regex.Escape(prefix)})\\w+?(?={Regex.Escape(suffix)})";
		}

		/// <summary>
		/// Gets the current <see cref="XmlScriptProvider"/> singleton instance.
		/// </summary>
		/// <value><see cref="XmlScriptProvider"/></value>
		public static XmlScriptProvider Current
		{
			get
			{
				if (current == null)
				{
					lock (syncRoot)
					{
						if (current == null)
						{
							current =  XmlScriptProvider.Deserialize();
						}
					}
				}
				return current;
			}
		}

		/// <summary>
		/// Gets or sets the procedures.
		/// </summary>
		/// <value>An <see cref="ArrayList"/> of <see cref="Procedure"/> objects.</value>
		[XmlArray("procedures")]
		[XmlArrayItem("procedure", typeof(Procedure))]
		public ArrayList Procedures
		{
			get
			{
				return procedures;
			}
			set
			{
				procedures = value;
				InitializeProcedureLookupTables();
			}
		}

		#region Public

		/// <summary>
		/// Gets or sets the <see cref="Procedure"/> instance with the specified name.
		/// </summary>
		public Procedure this[string name]
		{
			get
			{
				lock (syncRoot)
				{
					if (longNameProcedures == null)
					{
						InitializeProcedureLookupTables();
					}
				}

				Procedure result = null;
				result = longNameProcedures[name] as Procedure;
				if (result == null)
				{
					result = shortNameProcedures[name] as Procedure;
				}
				return result;
			}
		}

		/// <summary>
		/// Gets or sets the <see cref="Procedure"/> instance at the specified index.
		/// </summary>
		public Procedure this[int index]
		{
			get
			{
				return (Procedure)procedures[index];
			}
		}

		#endregion

		#region Serialization

		/// <summary>
		/// Serializes the specified filename.
		/// </summary>
		/// <param name="filename">Filename.</param>
		public void Serialize(string filename)
		{
			if (System.IO.File.Exists(filename))
			{
				System.IO.File.Delete(filename);
			}
			System.IO.FileStream fileStream = new System.IO.FileStream(filename, System.IO.FileMode.CreateNew, System.IO.FileAccess.ReadWrite, System.IO.FileShare.ReadWrite);
			XmlSerializer serializer = new XmlSerializer(this.GetType());
			serializer.Serialize(fileStream, this);
			fileStream.Close();
		}

		#endregion

		#region Deserialization

		/// <summary>
		/// Deserializes this instance.
		/// </summary>
		/// <returns></returns>
		private static XmlScriptProvider Deserialize()
		{
			XmlScriptProvider result = null;
			string mRealPath = "";
			string operatorType = (ConfigHelper.GetSection("BInterfaceType")?.Value ?? string.Empty).ToString(); //运营商类型
			if (operatorType == "1")
				mRealPath = AppDomain.CurrentDomain.BaseDirectory + CommonUtils.SqlPath + CommonUtils.DbProviderName  + "/" + "CMCC" + "/";
			else if (operatorType == "3")
				mRealPath = AppDomain.CurrentDomain.BaseDirectory + CommonUtils.SqlPath + CommonUtils.DbProviderName + "/" + "CUCC" + "/";
			string xmlPath = AppDomain.CurrentDomain.BaseDirectory + CommonUtils.SqlPath + CommonUtils.DbProviderName + "/";


			string[] xmlFiles1 = Directory.GetFiles(mRealPath, "*.xml");//拿到相应目录下的所有xml文件
            string[] xmlFiles2 = Directory.GetFiles(xmlPath, "*.xml");
			string[] xmlFiles = xmlFiles1.Concat(xmlFiles2).ToArray();


			if (xmlFiles != null && xmlFiles.Length > 0)
            {
				for(int i=0,len=xmlFiles.Length; i < len; i++)
                {
					string onexml = xmlFiles[i];
					XmlScriptProvider tmpObj = null;
					using (FileStream fs = File.OpenRead(onexml))
					{
						XmlSerializer serializer = new XmlSerializer(typeof(XmlScriptProvider));
						tmpObj = (XmlScriptProvider)serializer.Deserialize(fs);
					}
					if(tmpObj == null)
                    {
						continue;
                    } 
					else
                    {
						if(tmpObj.longNameProcedures == null)
						{
							tmpObj.InitializeProcedureLookupTables();
						}
					}
					if(result == null)
					{
						result = tmpObj;
					} 
					else
					{
						Hashtable resultShortHt = result.shortNameProcedures;
						Hashtable shortHt = tmpObj.shortNameProcedures;
						foreach (var key in shortHt.Keys)
						{
							resultShortHt.Add(key, shortHt[key]);
						}
						Hashtable resultLongHt = result.longNameProcedures;
						Hashtable longHt = tmpObj.longNameProcedures;
						foreach (var key in longHt.Keys)
						{
							resultLongHt.Add(key, longHt[key]);
						}
					}
				}
            }
			return result;
		}
		#endregion

		/// <summary>
		/// 初始化
		/// </summary>
		public void InitializeProcedureLookupTables()
		{
			lock (syncRoot)
			{

				shortNameProcedures = Hashtable.Synchronized(new Hashtable());
				longNameProcedures = Hashtable.Synchronized(new Hashtable());

				foreach (Procedure procedure in procedures)
				{
					shortNameProcedures[procedure.Name] = procedure;
					longNameProcedures[procedure.Owner + "." + procedure.Name] = procedure;
				}

				foreach (Procedure procedure in customProcedures)
				{
					shortNameProcedures[procedure.Name] = procedure;
					longNameProcedures[procedure.Owner + "." + procedure.Name] = procedure;
				}
			}
		}

		#region exec function
		/// <summary> 得到此sql代码段的<body>体中的内容 </summary>
		public string GetBodyFromEmbeddedResource(string name)
		{
			return GetProcedureByName(name).Body;
		}
		/// <summary> 得到此sql代码段的<body>体中的内容(替换占位符) </summary>
		public string GetBodyFromEmbeddedResource(string name, Dictionary<string, object> realParams)
		{
			Procedure procedure = GetProcedureByName(name);
			if (realParams == null) realParams = new Dictionary<string, object>();
			return ReplacePlaceholder(procedure.Body, procedure.GetReplaceDic(), realParams);
		}
		/// <summary> 通过name得到某一片断对象 </summary>
		public Procedure GetProcedureByName(string name)
		{
			XmlScriptProvider bp = XmlScriptProvider.Current;
			Procedure oneProcedure = bp[name];

			if (oneProcedure == null)
			{
				Thread.Sleep(0);
				oneProcedure = bp[name];
				if (oneProcedure == null)
				{
					throw new ApplicationException("cannot find the query '" + name + "' sqlFragment in the embedded xml file.");
				}
				else
				{
					return oneProcedure;
				}
			}
			else
			{
				return oneProcedure;
			}
		}

		public DataTable ExecDataTable(string keyName, Dictionary<string, object> realParams)
		{
			using (DbHelper dbHelper = new DbHelper())
			{
				PreExec(keyName, realParams, dbHelper);
				return dbHelper.ExecuteDataTable();
			}
		}

		public DataSet ExecDataSet(string keyName, Dictionary<string, object> realParams)
		{
			using (DbHelper dbHelper = new DbHelper())
			{
				PreExec(keyName, realParams, dbHelper);
				return dbHelper.ExecuteDataSet();
			}
		}

		public IDataReader ExecDataReader(string keyName, Dictionary<string, object> realParams)
		{
			using (DbHelper dbHelper = new DbHelper())
			{
				PreExec(keyName, realParams, dbHelper);
				return dbHelper.ExecuteDataReader();
			}
		}

		public object ExecScalar(string keyName, Dictionary<string, object> realParams)
		{
			using (DbHelper dbHelper = new DbHelper())
			{
				PreExec(keyName, realParams, dbHelper);
				return dbHelper.ExecuteScalar();
			}
		}

		public int ExecNonQuery(string keyName, Dictionary<string, object> realParams)
		{
			using (DbHelper dbHelper = new DbHelper())
			{
				PreExec(keyName, realParams, dbHelper);
				return dbHelper.ExecuteNonQuery();
			}
		}

		private void PreExec(string keyName, Dictionary<string, object> realParams, DbHelper dbHelper)
		{
			Procedure oneProcedure = GetProcedureByName(keyName);
			GetCommandWrapper(oneProcedure, keyName, realParams, dbHelper);
			TryToBindingParameters(oneProcedure.Parameters, realParams, dbHelper);
		}
		public DbCommand GetCommandWrapper(Procedure oneProcedure, string sqlKeyName, Dictionary<string, object> realParams, DbHelper dbHelper, bool reConnect = true)
		{
			if (oneProcedure == null)
			{
				throw new NullReferenceException("sql[" + sqlKeyName + "] can not find");
			}
			if (realParams == null)
			{
				realParams = new Dictionary<string, object>();
			}
			string sql = oneProcedure.Body;
			sql = ReplacePlaceholder(sql, oneProcedure.GetReplaceDic(), realParams);
			//Console.WriteLine("query sql:" + sql);
			Logger.Log("query sql:" + sql);
			if(reConnect)
			{
				return dbHelper.CreateTextCommand(sql);
			} 
			else
            {
				dbHelper.Command.CommandText = sql;
				dbHelper.Command.CommandType = CommandType.Text;
				return dbHelper.Command;
			}
		}
		/// <summary>
		/// 拿到某个key值对应的完成替换之后的sql语句
		/// </summary>
		/// <param name="sqlKeyName"></param>
		/// <param name="realParams"></param>
		/// <returns></returns>
		public string GetCommandSql(string sqlKeyName, Dictionary<string, object> realParams)
		{
			Procedure oneProcedure = XmlScriptProvider.Current.GetProcedureByName(sqlKeyName);
			if (oneProcedure == null)
			{
				throw new NullReferenceException("sql[" + sqlKeyName + "] can not find");
			}
			if (realParams == null)
			{
				realParams = new Dictionary<string, object>();
			}
			string sql = oneProcedure.Body;
			return ReplacePlaceholder(sql, oneProcedure.GetReplaceDic(), realParams); 
		}

		/// <summary>
		/// 替换文本中的占位符
		/// </summary>
		/// <param name="sourceStr"></param>
		/// <param name="realParams"></param>
        private string ReplacePlaceholder(string sourceStr, Dictionary<string, string> replacesDic, Dictionary<string, object> realParams)
        {
			if (realParams == null) realParams = new Dictionary<string, object>();
			if(!string.IsNullOrEmpty(sourceStr))
			{
				Regex regex = new Regex(replacePattern);
				MatchCollection matches = regex.Matches(sourceStr);
				foreach (Match match in matches)
				{
					string keyName = match.Value;
					object paramValue;
					if(realParams.TryGetValue(keyName, out paramValue) && paramValue != null)
                    {
						string replaceValue;
						if(replacesDic.TryGetValue(keyName, out replaceValue) && replaceValue != null)
                        {
							sourceStr = sourceStr.Replace(prefix + keyName + suffix, replaceValue);
							continue;
						} 
						else
                        {
							sourceStr = sourceStr.Replace(prefix + keyName + suffix, paramValue.ToString());
							continue;
						}
                    }
					sourceStr = sourceStr.Replace(prefix + keyName + suffix, "");
				}
			}
			return sourceStr;

		}

        public bool TryToBindingParameters(ArrayList needParams, Dictionary<string, object> realParams, DbHelper dbHelper)
		{
			if (needParams == null || needParams.Count < 1)
			{//不需要绑定参数
				return true;
			}
			else
			{
				if (realParams == null)
				{
					realParams = new Dictionary<string, object>();
				}
				dbHelper.BindingParameters(needParams, realParams);
				return true;
			}
		}
		#endregion

	}

	/// <summary>
	/// Represents a sql query.
	/// </summary>
	[Serializable()]
	public class Procedure
	{
		/// <summary>
		/// Creates a new <see cref="Procedure"/> instance.
		/// </summary>
		public Procedure()
		{
			parameters = new ArrayList();
			replaces = new ArrayList();
			replaceDic = new Dictionary<string, string>();
		}

		private ArrayList parameters;
		private ArrayList replaces;
		private Dictionary<string, string> replaceDic;

		/// <summary>
		/// Gets or sets the parameters.
		/// </summary>
		/// <value></value>
		[XmlArray("parameters")]
		[XmlArrayItem("parameter", typeof(Parameter))]
		public ArrayList Parameters
		{
			get { return parameters; }
			set { parameters = value; }
		}

		[XmlArray("replaces")]
		[XmlArrayItem("replace", typeof(Replace))]
		public ArrayList Replaces
		{
			get { return replaces; }
			set { replaces = value; }
		}

		public Dictionary<string, string> GetReplaceDic()
        {
			if(replaceDic.Count == 0 && replaces.Count > 0)
			{
				foreach (Replace one in replaces)
				{
					replaceDic.Add(one.KeyName, one.Text);
				}
			}
			return replaceDic;
        }

		private string name;

		/// <summary>
		/// Gets or sets the name.
		/// </summary>
		/// <value></value>
		[XmlAttribute("name")]
		public string Name
		{
			get { return name; }
			set
			{
				name = value;
			}
		}

		private string owner;

		/// <summary>
		/// Gets or sets the owner.
		/// </summary>
		/// <value></value>
		[XmlAttribute("owner")]
		public string Owner
		{
			get { return owner; }
			set
			{
				owner = value;
			}
		}

		private string comment;

		/// <summary>
		/// Gets or sets the comment.
		/// </summary>
		/// <value></value>
		[XmlElement("comment")]
		public string Comment
		{
			get
			{
				return comment;
			}
			set
			{
				comment = value;
			}
		}

		private string body;

		/// <summary>
		/// Gets or sets the body.
		/// </summary>
		/// <value></value>
		[XmlElement("body")]
		public string Body
		{
			get { return body; }
			set { body = value; }
		}

		/// <summary>
		/// Get the parameter from the specified name of the param.
		/// </summary>
		/// <param name="paramName">Name of the param.</param>
		/// <returns></returns>
		public Parameter Parameter(string paramName)
		{
			foreach (Parameter parameter in parameters)
			{
				if (parameter.Name == paramName)
				{
					return parameter;
				}
			}
			return null;
		}
	}

	/// <summary>
	/// Respresents a query parameter.
	/// </summary>
	[Serializable()]
	public class Parameter
	{
		/// <summary>
		/// Creates a new <see cref="Parameter"/> instance.
		/// </summary>
		public Parameter() { }

		private string name;

		/// <summary>
		/// Gets or sets the Name.
		/// </summary>
		/// <value></value>
		[XmlAttribute("name")]
		public string Name
		{
			get { return name; }
			set { name = value; }
		}

		private string sqltype;
		/// <summary>
		/// Gets or sets the SqlType.
		/// </summary>
		/// <value></value>
		[XmlAttribute("type")]
		public string SqlType
		{
			get { return sqltype; }
			set { sqltype = value; }
		}

		private System.Data.ParameterDirection direction;
		/// <summary>
		/// Gets or sets the Direction.
		/// </summary>
		/// <value></value>
		[XmlAttribute("direction")]
		public System.Data.ParameterDirection Direction
		{
			get { return direction; }
			set { direction = value; }
		}

		private int size;
		/// <summary>
		/// Gets or sets the size.
		/// </summary>
		/// <value></value>
		[XmlAttribute("size")]
		public int Size
		{
			get { return size; }
			set { size = value; }
		}

		private int precision;
		/// <summary>
		/// Gets or sets the precision.
		/// </summary>
		/// <value></value>
		[XmlAttribute("precision")]
		public int Precision
		{
			get { return precision; }
			set { precision = value; }
		}

		private int scale;
		/// <summary>
		/// Gets or sets the scale.
		/// </summary>
		/// <value></value>
		[XmlAttribute("scale")]
		public int Scale
		{
			get { return scale; }
			set { scale = value; }
		}
	}

	[Serializable()]
	public class Replace
	{
		[XmlAttribute("keyName")]
		public string KeyName { get; set; }

		[XmlText]
		public string Text { get; set; }
	}
}
