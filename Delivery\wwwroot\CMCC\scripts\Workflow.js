'use strict';

//global
var cookie = getCookie();
var userRole = getUserRole();
var AutoIdAll
var AutoIdData
var WorkflowIdData
var WorkflowNameData
var BaseUrl

//详细信息
var detailConfig = function (AutoId, WorkflowId, WorkflowStatus, WorkflowType, orderId, WorkflowName) {
    AutoIdData = AutoId
    WorkflowIdData = WorkflowId
    WorkflowNameData = WorkflowName
    if (WorkflowType == 1) {
        loadWorkflowDetailGridControl(AutoId)
        if (WorkflowStatus == 0) {
            $('#btn_Delete').parent().show();
            $('#btn_Resubmit').parent().show();
        }
        $('#WorkflowDetailDialog').dialog('open');
    } else {
        showOrderApplyTab(orderId, WorkflowName, WorkflowStatus)
    }
};

function showOrderApplyTab(orderId, subtitle, WorkflowStatus) {
    var url = "CutoverAudit?orderId=" + orderId + "&WorkflowStatus=" + WorkflowStatus;

    if (!parent.$('#tabs').tabs('exists', subtitle)) {
        var tabs = parent.$('#tabs').tabs('tabs');
        var count = 0;
        for (var i = 0; i < tabs.length; i++) {
            if (/割接单审核\w*/i.test(tabs[i].panel('options').title))
                if (++count >= 5) {
                    alertInfo('最多打开5笔割接单！');
                    return;
                }
        }

        parent.$('#tabs').tabs('add', {
            title: subtitle,
            content: '<iframe id="mytabframe"  frameborder="0"  scrolling="auto"  style="width:100%;height:100%;" src="' + url + '"></iframe>',
            //href: url,
            closable: true,
            width: $('#mainPanle').width() - 10,
            height: $('#mainPanle').height() - 26
        });

        //IE6下Frame Bug真他妹的多
        //http://www.cnblogs.com/xiaochaohuashengmi/archive/2010/08/12/1797797.html
        if (navigator.userAgent.indexOf("MSIE 6.0") > -1)//浏览器判断 如果是IE6,再重新加载一次Iframe
        {
            var ie6reloadTabFrame = parent.$('#tabs').tabs('getTab', subtitle).find('iframe')[0];
            if (ie6reloadTabFrame)
                ie6reloadTabFrame.contentWindow.location.href = url;
        }
    } else
        parent.$('#tabs').tabs('select', subtitle);

}

//初始化获取appsettings
function InitappSetting() {
    $.ajax({
        //url: `${WORKFLOW_HANDLER}/DeleteWorkflowDetail`,
        url: `${WORKFLOW_HANDLER}/GetAppSettings`,
        type: 'GET',
        dataType: 'json',
        async: false,
        success: function (result) {
            console.log(result)
            if (result.Data == 'true') {
                BaseUrl = result.url
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
}

var reviewDetail = function (AutoId, WorkflowId,WorkflowType) {
    AutoIdAll = AutoId
    $.ajax({
        type: "GET",
        contentType: "application/json; charset=UTF-8",
        url: BaseUrl + '/siteweb-flow/api/processInstance/getSimpleAuditRecord/' + WorkflowId,
        async: true,
        success: function (data) {
            if (data.code == 200) {
                var data = data.data

                $('#currentProcessStatusName').text(data.currentProcessStatusName);
                $('#currentTask').text(data.currentTask);
                var mainStr = ''
                data.auditRecords.forEach((e) => {
                    if (e.status == 2) {
                        mainStr += `

                        <div class="easyui-panel" style="width:240px;height:220px;padding:10px;color: #00bb10;border: solid 1px;margin: 3px;">
                            <h4>${e.name}</h4>
                            <ul>
                                <li>审核人：<span id="name">${e.auditUserName}</span></li>
                                <li>审核状态：<span id="statusName">${e.statusName}</span></li>
                                <li>开始时间：<span id="startTime">${e.startTime}</span></li>
                                <li>结束时间：<span id="endTime">${e.endTime}</span></li>
                                <li><p>审核意见：<span id="reason">${e.reason}</span></p></li>
                            </ul>
                        </div>

                            `
                    } else {
                        mainStr += `

                        <div class="easyui-panel" style="width:240px;height:220px;padding:10px;border: solid 1px;margin: 3px;">
                            <h4>${e.name}</h4>
                            <ul>
                                <li>审核人：<span id="name">${e.auditUserName}</span></li>
                                <li>审核状态：<span id="statusName">${e.statusName}</span></li>
                                <li>开始时间：<span id="startTime">${e.startTime}</span></li>
                                <li>结束时间：<span id="endTime">${e.endTime}</span></li>
                                <li><p>审核意见：<span id="reason">${e.reason}</span></p></li>
                            </ul>
                        </div>

                            `
                    }

                })
                //拼接完字符串数组后用innerHTML把它渲染到父元素中
                document.getElementById('auditRecords').innerHTML = mainStr
                var btn = document.getElementById('btn_OK');
                if (WorkflowType == '2') {
                    $('#btn_OK').css({ 'display': 'none' });
                } else {
                    $('#btn_OK').css({ 'display': 'inline-block' });
                }
                if (data.currentProcessStatus == 2) {
                    btn.disabled = false
                } else {
                    btn.disabled = true
                }
                $('#ReviewWorkflowDialog').dialog('open');
            } else {
                alertInfo(data.msg, 'error');
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });


};

var loadWorkflowProcessData = function () {
    $.ajax({
        type: "POST",
        contentType: "application/x-www-form-urlencoded",
        url: `${WORKFLOW_HANDLER}/ProcessPendingWorkflows`,
        data: {
        },
        success: function (data) {
            console.log(data)
            if (data.Success) {
                alertInfo(data.Message);
                $('#WorkflowTable').datagrid('reload');
            } else
                alertInfo(data.Message);
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
            $(selector).datagrid('selectRow', editingIndexArr[0]);
            $(selector).datagrid('beginEdit', editingIndexArr[0]);
        }
    });
}


var loadWorkflowData = function () {
   
    $('#WorkflowTable').datagrid({
        method: 'GET',
        url: `${WORKFLOW_HANDLER}/GetWorkflowInfo`,
        title: '工作流信息列表',  //rownumbers: true, //行号  striped: true, //隔行变色                
        idField: 'WorkflowId', //主键
        singleSelect: true,
        pagination: true,
        pageSize: 5,
        pageList: [5],
        queryParams: {
            WorkflowType: $('#WorkflowTypeq').combotree('getValue') || 1,
        },
        width: function () { return document.body.clientWidth * 0.9; }, //设置表格宽度为客户端页面的0.9
        height: 'auto',
        //fitColumns: true,
        columns: [[
           
            { field: 'WorkflowName', title: '工作流名称', width: 180, align: 'center' },
            {
                title: '工作流状态', field: 'WorkflowStatus', width: 180, align: 'center',
                formatter: function (value, row, index) {
                    var v0 = '退回'
                    var v1 = '审核中'
                    var v2 = '审核通过'
                    var v3 = '审核不通过'
                    if (value == 1)
                        return '<font color="red">' + v1 + '</font>';
                    else if (value == 0)
                        return '<font color="red">' + v0 + '</font>';
                    else if (value == 2)
                        return '<font color="green">' + v2 + '</font>';
                    else 
                        return '<font color="green">' + v3 + '</font>';
                }
            },

            { field: 'ApplyTime', title: '申请日期', width: 180, align: 'left' },
            { field: 'UserName', title: '申请人', width: 120, align: 'left' },
            {
                field: 'detailbtn', title: '详细信息', width: 160, align: 'center',
                formatter: function (value, row, index) {
                    return '<button type="button" onclick="detailConfig(\'' + row.AutoId + '\',\'' + row.WorkflowId + '\',\'' + row.WorkflowStatus + '\',\'' + row.WorkflowType + '\',\'' + row.ReviewerL1 + '\',\'' + row.WorkflowName + '\')">详细信息</button>';
                }
            },
            {
                field: 'reviewbtn', title: '审核流程', width: 160, align: 'center',
                formatter: function (value, row, index) {
                    return '<button type="button" onclick="reviewDetail(\'' + row.AutoId + '\',\'' + row.WorkflowId + '\',\'' + row.WorkflowType + '\')">审核流程</button>';
                }
            },
            { field: 'Remark', title: '备注', width: 280, align: 'left' }
        ]],
        onClickRow: function (index, row) {

        },
        onLoadError: function () {
            alertInfo('查询出错！', 'error');
        }
    });

    //loadWorkflowProcessData();
}

function loadWorkflowDetailGridControl(AutoId) {
    $('#dgDetail').datagrid({
        method: 'GET',
        url: `${WORKFLOW_HANDLER}/GetWorkflowDetailInfo`,
        singleSelect: true,
        width: function () { return document.body.clientWidth * 0.9 },
        height: 'auto',
        fitColumns: true,
        rownumbers: true,
        checkOnSelect: true,
        queryParams: {
           AutoId: AutoId,
        },
        columns: [[
            { field: 'AssetType', title: '类型', width: 100 },
            { field: 'AssetName', title: '名称', width: 80 },
        ]],
        loadFilter: pagerFilter,
        pagination: true,//分页设置
        pageSize: 10,
        pageList: [10, 20, 50],
        onDblClickRow: function (index, row) {
           
        }
    });
}

$(function () {
    InitappSetting()
    //提交
    $('#btn_OK').click(function () {
        $.messager.confirm('提示框', '你确认要提交吗?', function (r) {
            if (r) {
                console.log(AutoIdAll)
                $.ajax({
                    type: "POST",
                    contentType: "application/json; charset=UTF-8",
                    url: `${WORKFLOW_HANDLER}/ApproveWorkflow`,
                    data: JSON.stringify({
                        AutoId: AutoIdAll,
                    }),
                    success: function (data) {
                        console.log(data)
                        if (data.Success) {
                            alertInfo('提交成功！');
                            $('#ReviewWorkflowDialog').dialog('close');
                        } else {
                            alertInfo(data, 'error');
                        }
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                    }
                });
            }
        });
    });

    $('#btn_seach').click(function () {
        loadWorkflowData();
    });
    
    $('#btn_refresh').click(function () {
        loadWorkflowProcessData();
    })

    $('#btn_Resubmit').click(function () {
        var myData = $('#dgDetail').datagrid('getData').rows;
        if (myData) {
            var stationData = []
            var houseData = []
            var fsuData = []
            myData.forEach((value, key) => {
                if (value.AssetTypeId == "1") {
                    var data = {
                        "title": "站址名称",
                        "value": value.AssetName,
                        "field": "StationName"
                    }
                    stationData.push(data)
                } else if (value.AssetTypeId == "2") {
                    var data = {
                        "title": "机房名称",
                        "value": value.AssetName,
                        "field": "HouseName"
                    }
                    houseData.push(data)
                } else {
                    var data = {
                        "title": "FSU名称",
                        "value": value.AssetName,
                        "field": "FsuName"
                    }
                   
                    fsuData.push(data)
                }
            })
            var param = [
                {
                    "businessId": "",
                    "businessName": "站址申请",
                    "businessData": stationData
                },
                {
                    "businessId": "",
                    "businessName": "机房申请",
                    "businessData": houseData
                },
                {
                    "businessId": "",
                    "businessName": "FSU申请",
                    "businessData": fsuData
                }
            ]
            var paras = {
                processInstanceId: WorkflowIdData,
                startUserId: cookie['userinfo']['UserId'],
                processDefinitionKey: "w_leave",
                processInstanceName: WorkflowNameData,
                variables: { data: param }
            };
            console.log(paras)

            $.messager.confirm('提示框', '你确认要提交吗?', function (r) {
                if (r) {
                    $.ajax({
                        type: 'POST',
                        url: `${BaseUrl}/siteweb-flow/api/processInstance/create`,
                        contentType: 'application/json;charset=UTF-8',
                        data: JSON.stringify(paras),
                        async: true,
                        success: function (data) {
                            if (data.code === 200) {
                                alertInfo('提交成功');
                                //$('#dgDetail').datagrid('reload');
                            } else
                                alertInfo(data.msg + ': 提交失败！');
                        },
                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                        }
                    });
                }
            })
        }
    })


    $('#btn_Delete').click(function () {
        var row = $('#dgDetail').datagrid('getSelected');
        if (row) {
            $.messager.confirm('提示框', '你确认要删除吗?', function (r) {
                if (r) {
                    $.ajax({
                        type: "POST",
                        contentType: "application/x-www-form-urlencoded",
                        url: `${WORKFLOW_HANDLER}/DeleteWorkflowDetail`,
                        data: {
                            AutoId: AutoIdData,
                            AssetId:row.AssetId,
                            AssetTypeId:row.AssetTypeId,
                        },
                        success: function (data) {
                            if (data.Success) {
                                alertInfo('删除成功');
                                $('#dgDetail').datagrid('reload');
                            } else
                                alertInfo(data.msg + ': 删除失败！');
                        },
                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                        }
                    });
                }
            })
        }
         else
            alertInfo('请选择要删除的行！');
 
    })
    
    //$('#txtApplyTimeq').datetimebox('setValue', new Date(new Date().setDate(1)).format('yyyy-MM-dd') + ' 00:00:00');
    //$('#txtApplyTimeq2').datetimebox('setValue', new Date(new Date().getTime() + 1000 * 60 * 60).format('yyyy-MM-dd hh:mm:ss'));
    loadWorkflowData();
    //InitSubmitReview();



    $('div.mask').remove();
});