﻿<?xml version="1.0" encoding="utf-8"?>
<?xml-stylesheet type="text/xsl" href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
		<procedure owner="" name="SP_Del_WRHouseCUCC_1" grant="">
			<parameters>
				<parameter name="LogonId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT a.UserId
				FROM TBL_Account a WHERE a.LogonId = @LogonId ;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Del_WRHouseCUCC_2" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT 'X' FROM WR_HouseManagementCUCC
				WHERE WR_HouseManagementCUCC.WRHouseId = CAST(@WRHouseId AS INTEGER) AND WR_HouseManagementCUCC.UserId = @UserId
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Del_WRHouseCUCC_3" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT 'X' FROM WR_DeviceManagementCUCC
				WHERE WR_DeviceManagementCUCC.WRHouseId = CAST(@WRHouseId AS INTEGER)
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Del_WRHouseCUCC_4" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT 'X' FROM WR_FsuManagementCUCC
				WHERE WR_FsuManagementCUCC.WRHouseId = CAST(@WRHouseId AS INTEGER)
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Del_WRHouseCUCC_5" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT b.SWStationId,a.SWHouseId,a.HouseStatus
				FROM WR_HouseManagementCUCC a
				INNER JOIN WR_StationManagementCUCC b ON a.WRStationId = b.WRStationId
				WHERE a.WRHouseId =CAST(@WRHouseId AS INTEGER);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Del_WRHouseCUCC_6" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[ DELETE FROM WR_HouseManagementCUCC WHERE WR_HouseManagementCUCC.WRHouseId = CAST(@WRHouseId AS INTEGER); ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Del_WRHouseCUCC_7" grant="">
			<parameters>
				<parameter name="SWHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
        <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
      </parameters>
			<body>
				<![CDATA[ DELETE FROM TBL_House WHERE TBL_House.StationId = @SWStationId AND TBL_House.HouseId = @SWHouseId; ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Del_WRHouseCUCC_8" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
        <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
      </parameters>
			<body>
				<![CDATA[ DELETE FROM TBL_RoomCMCC WHERE TBL_RoomCMCC.StationId = @SWStationId AND TBL_RoomCMCC.HouseId = CAST(@WRHouseId AS INTEGER); ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Del_WRHouseCUCC_9" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[ SELECT  WRStationId,HouseName FROM WR_HouseManagementCUCC WHERE WR_HouseManagementCUCC.WRHouseId = CAST(@WRHouseId AS INTEGER); ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Del_WRHouseCUCC_10" grant="">
			<parameters>
				<parameter name="WRStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[ SELECT  COALESCE(StationName,'') as StationName FROM WR_StationManagementCUCC WHERE WR_StationManagementCUCC.WRStationId = CAST(@WRStationId AS INTEGER); ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Del_WRHouseCUCC_11" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[ DELETE FROM WR_HouseManagementCUCC WHERE WR_HouseManagementCUCC.WRHouseId = CAST(@WRHouseId AS INTEGER); ]]>
			</body>
		</procedure>
	</procedures>
</root>
