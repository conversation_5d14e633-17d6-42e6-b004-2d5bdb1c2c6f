﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B
{
    /// <summary>
    /// 上报告警信息应答报文
    /// </summary>
    public sealed class SendAlarmAck : BMessage
    {
        public EnumResult Result { get; private set; }

        public SendAlarmAck(EnumResult result)
            : base()
        {
            MessageType = (int)BMessageType.SendAlarmAck;
            Result = result;
        }

        public override string Serialize()
        {
            XmlDocument xmldoc = CreateXmlDocument();
            AppendPK_Type(xmldoc, "Response", "SEND_ALARM_ACK", "502");

            XmlElement xe2 = xmldoc.CreateElement("Info");
            XmlElement xe21 = xmldoc.CreateElement("Result");
            xe21.InnerText = Result.ToString();
            xe2.AppendChild(xe21);

            XmlNode root = xmldoc.SelectSingleNode("Response");
            root.AppendChild(xe2);

            return xmldoc.InnerXml;
        }

        public static SendAlarmAck Deserialize(string raw)
        {
            throw new NotImplementedException();
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}: {2}", MessageId, (BMessageType)MessageType, Result);
        }
    }
}
