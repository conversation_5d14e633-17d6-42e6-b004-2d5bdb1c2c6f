﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;


namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.6.18.	查询监控点存储规则
    /// </summary>
    public sealed class GetStorageRule : BMessage
    {

        public List<Device> Devices { get; set; }

        public GetStorageRule(string fsuId, List<Device> devices)
            : base()
        {
            MessageType = (int)BMessageType.GET_STORAGERULE;

            FSUID = fsuId;
            Devices = devices;
        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmlDoc = CreateXmlDocument();
                AppendPK_Type(xmlDoc, "Request", BMessageType.GET_STORAGERULE.ToString());

                XmlElement xe2 = xmlDoc.CreateElement("Info");
                XmlElement xe21 = xmlDoc.CreateElement("FSUID");
                xe21.InnerText = FSUID;
                xe2.AppendChild(xe21);

                XmlElement xe22 = xmlDoc.CreateElement("DeviceList");
                foreach (Device device in Devices)
                {
                    XmlElement xeDevice = xmlDoc.CreateElement("Device");
                    xeDevice.SetAttribute("ID", device.DeviceId);
                    foreach (TSignalMeasurementId tsmid in device.TSignalMeasurementIds)
                    {
                        XmlElement xeTSignalMeasurementId = xmlDoc.CreateElement("TSignalMeasurementId");
                        xeTSignalMeasurementId.SetAttribute("ID", tsmid.ID);
                        string strSignalNumber = tsmid.SignalNumber.ToString().Trim();
                        if (strSignalNumber.Length == 1)
                        {
                            strSignalNumber = "00" + strSignalNumber;
                        }
                        else if (strSignalNumber.Length == 2)
                        {
                            strSignalNumber = "0" + strSignalNumber;
                        }
                        xeTSignalMeasurementId.SetAttribute("SignalNumber", strSignalNumber);
                        xeDevice.AppendChild(xeTSignalMeasurementId);
                    }
                    xe22.AppendChild(xeDevice);
                }
                xe2.AppendChild(xe22);

                XmlNode root = xmlDoc.SelectSingleNode("Request");
                root.AppendChild(xe2);
                StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("GetStorageRule.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmlDoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetStorageRule.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetStorageRule.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}: {2}", MessageId, (BMessageType)MessageType, FSUID);
            foreach (Device device in Devices)
            {
                sb.AppendFormat("[{0}]", device.ToString());
            }
            return sb.ToString();
        }


    }
}
