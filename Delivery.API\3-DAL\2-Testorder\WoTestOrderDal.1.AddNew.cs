﻿
using DM.TestOrder.Entity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using DM.TestOrder.DAL.Helper;
using testorder.dal;
using Delivery.DAL;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace DM.TestOrder.DAL {

    public partial class WoTestOrderDal {
        public static void AddOne(WO_TestOrder newOrder, out int newOrderId, out string newMyOrderId) {
            var now = DateTime.Now;
            newOrder.ApplyTime = now;
            newOrder.StateChangeTime = now;

            //冗余
            if (newOrder.EquipItemList != null)
                newOrder.EquipItems = string.Join(",", newOrder.EquipItemList.Select(o => o.EquipmentId).ToList());
            DataTable tb = null;
            if(CommonUtils.IsNoProcedure)
            {
                tb = WO_TestOrderDalService.Instance.WO_CreateTestOrder(newOrder.OrderType, newOrder.StationId, newOrder.Latitude, newOrder.Longitude, newOrder.EquipItems, newOrder.InstallCompany, newOrder.InstallClerk, newOrder.ApplyUserId);
            }
            else
            {
                tb = new ExecuteSql().ExecuteStoredProcedure("WO_CreateTestOrder", new QueryParameter[] {
                new QueryParameter("OrderType", DataType.Int, newOrder.OrderType.ToString()),
                new QueryParameter("StationId", DataType.Int, newOrder.StationId.ToString()),
                new QueryParameter("Latitude", DataType.Number, newOrder.Latitude.ToString()),
                new QueryParameter("Longitude", DataType.Number, newOrder.Longitude.ToString()),
                new QueryParameter("EquipItems", DataType.String, newOrder.EquipItems),
                new QueryParameter("InstallCompany", DataType.String, newOrder.InstallCompany),
                new QueryParameter("InstallClerk", DataType.String, newOrder.InstallClerk),
                new QueryParameter("ApplyUserId", DataType.Int, newOrder.ApplyUserId.ToString())
                });
            }
            if (tb.Rows.Count == 0) {
                throw new Exception("工单记录创建失败");
            }
            newOrderId = int.Parse(tb.Rows[0]["OrderId"].ToString());
            newMyOrderId = tb.Rows[0]["MyOrderId"].ToString();

            //save equip
            if (newOrder.EquipItemList != null)
                foreach (var e in newOrder.EquipItemList) {
                    WoTestOrderEquipItemDal.DBAddOne(newOrderId, e.EquipmentId);
                }


        }
    }
}