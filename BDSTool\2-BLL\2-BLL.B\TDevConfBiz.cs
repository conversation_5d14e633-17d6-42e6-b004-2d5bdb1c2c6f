﻿using BDSTool.Entity.B;
using Common.Logging.Pro;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace BDSTool.BLL.B
{
    public class TDevConfBiz
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");
        public static bool ValidateTSigId(TDevConf device, ref string ErrorMsg) {
              //信号顺序号有效性检查
             var ids=new List<int>();
            foreach (var tsig in device.Signals) {
                int sigNumber;
                if (tsig.SignalNumber != "NULL" && !int.TryParse(tsig.SignalNumber, out sigNumber)) {
                    ErrorMsg = string.Format("待导入监控点信号TSignal的信号顺序号无效.取值应为'NULL'或序号.设备:{0},监控点ID:{1},信号顺序号:{2}",
                        device.DeviceName, tsig.ID, string.IsNullOrEmpty(tsig.SignalNumber) ? "空" : tsig.SignalNumber);
                    logger.WarnFormat("ValidateTSigId();" + ErrorMsg);
                    return false;
                }
                         
                int siteWebId=0;
                if(!TSignalEx.ConvertBID2SiteWebId( tsig.ID, tsig.SignalNumber, ref siteWebId)){
                    ErrorMsg = string.Format("待导入监控点信号TSignal的信号编码错误.设备:{0},监控点ID:{1}", device.DeviceName, tsig.ID);
                    logger.WarnFormat("ValidateTSigId();" + ErrorMsg);
                    return false;
                }

                ids.Add(siteWebId);
            }

            //信号ID重复性检查
            var dupSigs = ids.GroupBy(o => o)
            .Where(g =>  g.Count() >  1)
            .Select(g =>  g.Key)
            .ToList();

            if (dupSigs.Count()>0) {
                var dup=dupSigs.First();
                ErrorMsg = string.Format("待导入监控点信号TSignal存在重复编号.设备:{0},监控点ID:{1}", device.DeviceName, dup/1000);
                logger.WarnFormat("ValidateTSigId();" + ErrorMsg);
                return false;
            }

             return true;
        }
    }
}
