﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Odbc;
using System.Linq;
using System.Text;
//using Carrier.Kolo.SSTool;
using Carrier.BDSTool;

using ENPC.Kolo.Entity.B_CMCC;
using Common.Logging.Pro;

using BDSTool.Entity.B;
using BDSTool.BLL.S2;
using BDSTool.BLL.B;
using BDSTool.Utility;
using BDSTool.Entity;

using System.Xml;
using System.Configuration;

namespace Carrier.BDSTool
{        
    public partial class BDSHelperCMCC
    {
        // 20180523 指定部分告警基类->SiteWeb告警类别 的映射
        public static volatile bool Global_IsNeedSetEventKind = false;
        static BDSHelperCMCC() {
            try {
                logger.Info("");
                logger.Info("");
                logger.Info(new string('+', 80));
                logger.Info("");


                Global_IsNeedSetEventKind = EventCategoryMapBiz.IsNeedSetEventKind();

                //可在AppSetting中屏蔽该功能
                //<add key="bds_skip_eventcat_map" value="1"/>
                if (Global_IsNeedSetEventKind) {
                    // string sValue = ConfigurationManager.AppSettings.Get("bds_skip_eventcat_map");
                    string sValue = null;
                    if (!string.IsNullOrEmpty(sValue)) {
                        int value;
                        if (int.TryParse(sValue, out value)) {
                            if (value == 1) {
                                Global_IsNeedSetEventKind = false;
                                logger.Info("BDSHelperCMCC(); AppSettings::bds_skip_eventcat_map=1; 忽略-SiteWeb告警类别处理");
                            }
                        }

                    }
                }

                logger.InfoFormat("BDSHelperCMCC(); ### SiteWeb告警类别处理 ={0};", Global_IsNeedSetEventKind ? "加载" : "忽略");    
                    
                logger.Info(new string('+', 80));
                logger.Info("");
                logger.Info("");

            }
            catch(Exception e){
                logger.ErrorFormat("static BDSHelperCMCC(); ex={0}", e.Message);
            }
        }
    }
}
