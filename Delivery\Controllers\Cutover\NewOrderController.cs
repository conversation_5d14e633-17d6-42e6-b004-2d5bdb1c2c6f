﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web.Http;

using DM.TestOrder.formData;
using DM.TestOrder.Service.Interface;



namespace DM.TestOrder.Controllers {
    
    public class NewOrderController : BaseApiController{

        //http://localhost:34380/api/neworder
        //[RequestAuthorize]
        public HttpResponseMessage  Post([FromBody]JObject value) {
            // var re = Request;
            //var headers = re.Headers;

            //if (headers.Contains("Custom"))
            //{
            //    string token = headers.GetValues("Custom").First();
            //}


            //return Debug.WriteLine(value.ToString());
            //int newOrderId;
            //var errmsg = TestOrderApi4Json.Instance.SubmitNewOrderAsJson(value.ToString(), out newOrderId);


            int newOrderId;
            string newMyOrderId;
            FormNewOrder orderInfo = Newtonsoft.Json.JsonConvert.DeserializeObject<FormNewOrder>(value.ToString());

            var errmsg = TestOrderApi.Instance.SubmitNewOrder(orderInfo, out newOrderId, out newMyOrderId);


            if (errmsg != "OK") {
                Debug.WriteLine(errmsg);
                Debug.WriteLine(value.ToString());
            }
            else {
                var ApplyOrderId = value["ApplyOrderId"];


                Debug.WriteLine("新工单OK");
                Debug.WriteLine(value.ToString());
            }
            var rtn = new {
                errormsg = errmsg,
                orderid = newOrderId,
                myorderid = newMyOrderId
            };
            var json = Newtonsoft.Json.JsonConvert.SerializeObject(rtn);
            //var rtn=string.Format("{errormsg:{0}\}",errmsg);
  
            var response = Request.CreateResponse(HttpStatusCode.OK);
            response.StatusCode = HttpStatusCode.OK;
            response.Content = new StringContent(json);    // 响应内容
            return response;
        }
    }
}
