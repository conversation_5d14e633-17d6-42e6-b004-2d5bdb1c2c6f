﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 信号数据存储规则的结构
    /// </summary>
    public class TStorageRule : BStruct
    {
        //注意，这里没有用TSignalId ，是因为没必要用，保持跟协议文档中的 XML 样例一致，更方便
        //public TSignalMeasurementId TSignalId { get; set; }

        /// <summary>
        /// 监控点ID
        /// </summary>
        public string ID { get; set; }

        /// <summary>
        /// 同设备同类监控点顺序号
        /// </summary>
        public string SignalNumber { get; set; }

        /// <summary>
        /// 绝对阀值
        /// </summary>
        public float? AbsoluteVal { get; set; }

        /// <summary>
        /// 百分比阀值
        /// </summary>
        public float? RelativeVal { get; set; }

        /// <summary>
        /// 存储时间间隔（单位：分钟）
        /// </summary>
        public long? StorageInterval { get; set; }

        /// <summary>
        /// 存储参考时间，格式YYYY-MM-DD<SPACE键>hh:mm:ss（采用24小时的时间制式）
        /// </summary>
        public string StorageRefTime { get; set; }

        /// <summary>
        /// 信号数据存储规则的结构
        /// </summary>
        /// <param name="type">数据类型-监控系统数据的种类</param>
        /// <param name="id">监控点ID</param>
        /// <param name="signalNumber">同设备同类监控点顺序号</param>
        /// <param name="absoluteVal">绝对阀值</param>
        /// <param name="relativeVal">百分比阀值</param>
        /// <param name="storageInterval">存储时间间隔（单位：分钟）</param>
        /// <param name="storageRefTime">存储参考时间，格式YYYY-MM-DD<SPACE键>hh:mm:ss（采用24小时的时间制式）</param>
        public TStorageRule(EnumType type, string id, string signalNumber, float? absoluteVal, float? relativeVal
            , long? storageInterval, string storageRefTime) 
        {
            Type = type;
            ID   = id;
            SignalNumber = signalNumber;
            AbsoluteVal = absoluteVal;
            RelativeVal = relativeVal;
            StorageInterval = storageInterval;
            StorageRefTime = storageRefTime;
        }

        public override string ToString()
        {
            return String.Format(
                "[{0}, {1} ,{2} , {3}, {4}, {5}, {6}]",
                Type, ID, SignalNumber, AbsoluteVal, RelativeVal, StorageInterval, StorageRefTime);
        } 
    }
}
