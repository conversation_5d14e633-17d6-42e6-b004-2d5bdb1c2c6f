﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// *******　FSU向LSC注册
    /// </summary>
    public sealed class Login : BMessage
    {
        public string UserName { get; private set; }

        public string PassWord { get; private set; }

        public string FSUIP { get; private set; }

        public string FSUMAC { get; private set; }

        public string FSUVER { get; private set; }

        public int MyMonitoringUnitId { get; set; }

        public Login(string userName, string password, string fsuId, string fsuIp, string fsuMac, string fsuVer):base()
        {
            MessageType = (int)BMessageType.LOGIN;
            UserName = userName;
            PassWord = password;
            FSUID = fsuId;
            FSUIP = fsuIp;
            FSUMAC = fsuMac;
            FSUVER = fsuVer;
        }

        public Login() : base() { }

        public override string Serialize()
        {
            throw new NotImplementedException();
        }

        public static Login Deserialize(XmlDocument xmlDoc)
        {
            Login login = null;
            try
            {
                string userName = xmlDoc.SelectSingleNode("/Request/Info/UserName").InnerText.Trim();
                ////2016-08-22 按阳老师和教授要求，注册用户密码（MD5加密），在解析后，转成大写
                ////string password = xmlDoc.SelectSingleNode("/Request/Info/PassWord").InnerText.Trim().ToUpper();
                //2016-09-18注册用户密码（MD5加密），在解析后，不转大写
                string password = xmlDoc.SelectSingleNode("/Request/Info/PassWord").InnerText.Trim();
                string fsuId = xmlDoc.SelectSingleNode("/Request/Info/FSUID").InnerText.Trim();
                string fsuIp = xmlDoc.SelectSingleNode("/Request/Info/FSUIP").InnerText.Trim();
                string fsuMac = xmlDoc.SelectSingleNode("/Request/Info/FSUMAC").InnerText.Trim();
                string fsuVer = xmlDoc.SelectSingleNode("/Request/Info/FSUVER").InnerText.Trim();

                login = new Login(userName, password, fsuId, fsuIp, fsuMac, fsuVer);
                entityLogger.DebugFormat("Login.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                login.StringXML = xmlDoc.InnerXml;
                return login;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("Login.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("Login.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                login = new Login();
                login.ErrorMsg = ex.Message;
                login.StringXML = xmlDoc.InnerXml;
                return login;
            }
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}: {2}, {3}, {4}, {5}, {6}, {7}",
                MessageId, (BMessageType)MessageType, UserName.Trim(), PassWord.Trim(), FSUID, FSUIP, FSUMAC, FSUVER);
        }
    }
}
