﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Odbc;
using System.Linq;
using System.Text;
using Carrier.BDSTool;

using ENPC.Kolo.Entity.B_CMCC;
using BDSTool.Entity.S2;
using BDSTool.BLL.B;
using BDSTool.Utility;
using Common.Logging.Pro;

namespace Carrier.BDSTool
{
    public partial class BDSHelperCMCC
    {
        public static bool GetFSUConfigByFSUId(string FSUID, ref List<TDevConf> deviceList) {
            try {
                logger.InfoFormat("GetFSUConfigByFSUId();FSUID={0}", FSUID);

                int MonitorUnitId = 0;
                if (!MonitorUnitCMCCBiz.GetMonitorUnitIdByFSUID(FSUID, ref MonitorUnitId)) {
                    logger.Warn("GetMonitorUnitIdByFSUID() failed");
                    return false;
                }

                if (!DevConfBiz.ZGetFSUConfigByMonitorUnitId(MonitorUnitId, ref deviceList)) {
                    logger.Warn("GetFSUConfigByMonitorUnitId() failed");
                    return false;
                }
            }
            catch (Exception ex) {
                logger.ErrorFormat("GetFSUConfigByMonitorUnitId();fsuID={0};;Error={1}", FSUID, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            LoggerBDSTool.InfoPartEnd();
            return true;
        }

        public static bool GetFSUConfigByDeviceIds(string FSUID, List<string> DevIds, ref List<TDevConf> deviceList) {
            try {
                int MonitorUnitId = 0;
                if (!MonitorUnitCMCCBiz.GetMonitorUnitIdByFSUID(FSUID, ref MonitorUnitId)) {
                    logger.Warn("GetFSUConfigByEquipIds(); GetMonitorUnitIdByFSUID() failed");
                    return false;
                }

                return DevConfBiz.ZGetFSUConfigByDeviceIds(MonitorUnitId, DevIds, ref  deviceList);
            }
            catch (Exception ex) {
                LoggerBDSTool.ErrorFormat("GetFSUConfigByEquipIds();Error={0}", ex.Message);
                LoggerBDSTool.Error(ex.StackTrace);
                return false;
            }

        }

    }
}
