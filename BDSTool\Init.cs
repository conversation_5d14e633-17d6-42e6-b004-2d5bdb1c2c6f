﻿
using BDSTool.DBUtility.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace BDSTool
{
    public class Init
    {
        /// <summary>
        /// 初始化数据库连接参数，调用程序启动时调用
        /// </summary>
        /// <param name="connectstring">connectstring样例： Data Source='10.169.42.171';Database='test'; UID='sa'; PWD=''</param>
        /// <param name="dataBaseTimeout">数据库超时时间，单位：秒</param>
        public static void SetDBConfig(string connectstring, int dataBaseTimeout=60, string providerName="mysql") {
            DbConfig dbConfig = new DbConfig();
            dbConfig.ConnectionString = connectstring;
            dbConfig.ProviderName = providerName;
            dbConfig.DataBaseTimeout = dataBaseTimeout;
            DBHelper.SetDbConfig(dbConfig);
        }
    }
}
