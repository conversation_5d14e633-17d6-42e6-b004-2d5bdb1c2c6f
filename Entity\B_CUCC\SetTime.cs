﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class SetTime : BMessage
    {
        public TTime Time { get; set; }
        public DateTime DT { get; set; }
        public SetTime(string suids, string surids, TTime timeInfo) : base()
        {
            MessageType = (int)BMessageType.SET_TIME;
            SUId = suids;
            SURId = surids;
            Time = timeInfo;
        }
        public SetTime(string suids, string surids, DateTime timeInfo)
        {
            MessageType = (int)BMessageType.SET_TIME;
            SUId = suids;
            SURId = surids;
            DT = timeInfo;
            Time = null;
        }
        public override string Serialize()
        {
            try
            {
                XmlDocument xmldoc = CreateXmlDocument();
                AppendPK_Type(xmldoc, "Request", BMessageType.SET_TIME.ToString(), MessageType.ToString());
                XmlElement el1 = xmldoc.CreateElement("Info");
                XmlElement el12 = xmldoc.CreateElement("SUId");
                XmlElement el13 = xmldoc.CreateElement("SURId");
                XmlElement el14 = xmldoc.CreateElement("Time");
                XmlElement el141 = xmldoc.CreateElement("Year");
                XmlElement el142 = xmldoc.CreateElement("Month");
                XmlElement el143 = xmldoc.CreateElement("Day");
                XmlElement el144 = xmldoc.CreateElement("Hour");
                XmlElement el145 = xmldoc.CreateElement("Minute");
                XmlElement el146 = xmldoc.CreateElement("Second");
                el12.InnerText = SUId;
                el13.InnerText = SURId;
                if (Time == null)
                {
                    el141.InnerText = DT.Year.ToString();
                    el142.InnerText = DT.Month.ToString();
                    el143.InnerText = DT.Day.ToString();
                    el144.InnerText = DT.Hour.ToString();
                    el145.InnerText = DT.Minute.ToString();
                    el146.InnerText = DT.Second.ToString();
                }
                else
                {
                    el141.InnerText = Time.Years.ToString();
                    el142.InnerText = Time.Month;
                    el143.InnerText = Time.Day;
                    el144.InnerText = Time.Hour;
                    el145.InnerText = Time.Minute;
                    el146.InnerText = Time.Second;
                }
                el14.AppendChild(el141);
                el14.AppendChild(el142);
                el14.AppendChild(el143);
                el14.AppendChild(el144);
                el14.AppendChild(el145);
                el14.AppendChild(el146);
                el1.AppendChild(el12);
                el1.AppendChild(el13);
                el1.AppendChild(el14);
                XmlNode root = xmldoc.SelectSingleNode("Request");
                root.AppendChild(el1);
                StringXML = xmldoc.InnerXml;
                entityLogger.DebugFormat("SetTime.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmldoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetTime.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetTime.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }

        }
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1},{2},{3},{4}", MessageId, (BMessageType)MessageType, SUId, SURId, Time.ToString());
            return sb.ToString();
        }
    }
}
