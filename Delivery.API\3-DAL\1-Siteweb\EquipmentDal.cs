﻿

using DM.TestOrder.Entity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using DM.TestOrder.DAL.Helper;
using Delivery.DAL;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;
using Delivery.API;

namespace BLL
{
    public class EquipmentDal
    {
        private static readonly Dictionary<string, string> EquipmentFieldMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            {"equipmentid","EquipmentId"},
            {"housename","HouseName"},
            {"roomid","RoomID"},
            {"monitorunitname","MonitorUnitName"},
            {"fsuid","FSUID"},
            {"equipmentname","EquipmentName"},
            {"equipmentno","EquipmentNo"},
            {"equipmentcategoryname","EquipmentCategoryName"},
            {"vendor","Vendor"},
            {"equipmentstyle","EquipmentStyle"}
        };

        static public DataTable GetByKeyword(int orderType, int userId, int stationId, string houseName, string equipmentName, string equipmentNo)
        {
            if (CommonUtils.IsNoProcedure)
            {
                var tb1 = WoCommonService.Instance.WO_Equipment_Query(orderType,userId,stationId,houseName,equipmentName,equipmentNo);
                DataTableColumnMapper.RenameColumns(tb1, EquipmentFieldMap);
                return tb1;
            }
            if (string.IsNullOrWhiteSpace(houseName))
                houseName = "";
            if (string.IsNullOrWhiteSpace(equipmentName))
                equipmentName = "";
            if (string.IsNullOrWhiteSpace(equipmentNo))
                equipmentNo = "";

            //var sql = string.Format(@"WO_Equipment_Query {0},{1},{2},{3},{4},{5}",
            //      orderType, 
            //      SHelper.GetPara(userId), SHelper.GetPara(stationId), 
            //      SHelper.GetPara(houseName),
            //      SHelper.GetPara(equipmentName),
            //      SHelper.GetPara(equipmentNo));

            //  var tb = DBHelper.GetTable(sql);
            var tb = new ExecuteSql().ExecuteStoredProcedure("WO_Equipment_Query", new QueryParameter[] {
                new QueryParameter("OrderType", DataType.Int, orderType.ToString()),
                new QueryParameter("UserId", DataType.Int, userId.ToString()),
                new QueryParameter("StationId", DataType.Int, stationId.ToString()),
                new QueryParameter("HouseName", DataType.String, houseName),
                new QueryParameter("EquipmentName", DataType.String, equipmentName),
                new QueryParameter("EquipmentNo", DataType.String, equipmentNo),
            });
            return tb;
        }

        public static TBL_Equipment GetOne(int equipmentId)
        {
            var stn = new TBL_Equipment();
            //var sql = string.Format("select * from TBL_Equipment where EquipmentId ={0}", equipmentId);
            //var tb = DBHelper.GetTable(sql);
            DataTable tb;
            if (CommonUtils.IsNoProcedure)
            {
                tb = Public_ExecuteSqlService.Instance.NoStore_TBLEquipment_GetOneByEquipmentId(equipmentId);
                DataTableColumnMapper.RenameColumns(tb, EquipmentFieldMap);
            }
            else 
            {
                var sql = string.Format("select * from TBL_Equipment where EquipmentId = @equipmentId");
                tb = new ExecuteSql().ExecuteSQL(sql, new QueryParameter[]
                {
                new QueryParameter("equipmentId", DataType.Int, equipmentId.ToString())
                });
            }


            if (tb.Rows.Count == 0)
                return null;

            return TBL_EquipmentHelper.FromDataRow(tb.Rows[0]);
        }
        public static List<TBL_Equipment> GetEquipmentsByStationId(int StationId)
        {
            var items = new List<TBL_Equipment>();
            //var sql = string.Format("select * from TBL_Equipment where StationId={0}", StationId);
            //var tb = DBHelper.GetTable(sql);var sql = string.Format("select * from TBL_Equipment where EquipmentId = @equipmentId");
            DataTable tb;
            if (CommonUtils.IsNoProcedure)
            {
                tb = Public_ExecuteSqlService.Instance.NoStore_TBLEquipment_GetEquipmentsByStationId(StationId);
            }
            else
            {
                var sql = "select * from TBL_Equipment where StationId=@stationId";
                tb = new ExecuteSql().ExecuteSQL(sql, new QueryParameter[]
                {
                new QueryParameter("stationId", DataType.Int, StationId.ToString())
                });
            }
            if (tb.Rows.Count == 0)
                return items;
            foreach (DataRow row in tb.Rows)
            {
                var item = TBL_EquipmentHelper.FromDataRow(row);
                items.Add(item);
            }
            return items;
        }
    }
}
