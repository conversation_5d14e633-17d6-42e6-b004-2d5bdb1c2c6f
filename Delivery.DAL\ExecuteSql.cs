﻿using Delivery.Common;

using System;
using System.Data;
using System.Data.Common;

namespace Delivery.DAL
{
    public class ExecuteSql
    {

        #region IReportDataService 成员

        public DataTable ExecuteSQL(string SQL)
        {
            DataTable table = null;
            DatabaseHelper db = null;
            DbCommand cmd = null;

            try
            {
                db = new DatabaseHelper();
                cmd = db.NewSQLCommand(SQL);

                DataSet ds = db.ExecuteDataSet(cmd);
                if (ds.Tables.Count > 0)
                    table = ds.Tables[0];

                cmd.Dispose();
                db.Dispose();
            }
            catch (Exception e)
            {
                //需要日志记录

                Logger.Log("["+SQL+"]", LogType.Error);
                Logger.Log(e);
                //logger.Error(e.Message);
                //throw e;
            }
            finally
            {
                if (db != null)
                {
                    db.Dispose();
                }

                if (cmd != null)
                {
                    cmd.Dispose();
                }
            }

            return table;
        }

        public object ExecuteSQLScalar(string sql, QueryParameter[] queryParameters)
        {
            object obj = null;
            DatabaseHelper db = null;
            DbCommand cmd = null;

            try
            {
                db = new DatabaseHelper();
                cmd = db.NewSQLCommand(sql);
                for (var i = 0; i < queryParameters.Length; i++)
                {
                    DbType dbtype = DbType.Object;
                    Object paramValue = null;
                    switch (queryParameters[i].DataType)
                    {
                        case DataType.Int:
                            dbtype = DbType.Int32;
                            int tempValue;
                            if (Int32.TryParse(queryParameters[i].Value, out tempValue))
                            {
                                paramValue = tempValue;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.Number:
                            dbtype = DbType.Single;
                            Single tempSingleValue;
                            if (Single.TryParse(queryParameters[i].Value, out tempSingleValue))
                            {
                                paramValue = tempSingleValue;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.String:
                            dbtype = DbType.AnsiString;
                            paramValue = queryParameters[i].Value;
                            break;
                        case DataType.DateTime:
                            dbtype = DbType.DateTime;
                            if (queryParameters[i].Value != null)
                            {
                                paramValue = queryParameters[i].Value;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.Bool:
                            dbtype = DbType.Boolean;
                            paramValue = Boolean.Parse(queryParameters[i].Value);
                            break;
                    }

                    DbParameter param = db.GetDbParameter(cmd, queryParameters[i].Name, dbtype, paramValue);

                    cmd.Parameters.Add(param);
                }

                obj = db.ExecuteScalar(cmd);
            }
            catch (Exception ex)
            {
                //需要日志记录
                Logger.Log("[SQL:" + sql + "]", LogType.Error);
                for (int i = 0; i < queryParameters.Length; i++)
                {
                    Logger.Log("[Q:" + queryParameters[i].Name + ":" + queryParameters[i].Value + "]", LogType.Error);
                }
                Logger.Log(ex);
            }
            finally
            {
                if (db != null)
                {
                    db.Dispose();
                }

                if (cmd != null)
                {
                    cmd.Dispose();
                }
            }
            return obj;
        }


        public DataTable ExecuteSQL(string sql, QueryParameter[] queryParameters)
        {
            DataTable table = null;
            DatabaseHelper db = null;
            DbCommand cmd = null;

            try
            {
                db = new DatabaseHelper();
                cmd = db.NewSQLCommand(sql);
                for (var i = 0; i < queryParameters.Length; i++)
                {
                    DbType dbtype = DbType.Object;
                    Object paramValue = null;
                    switch (queryParameters[i].DataType)
                    {
                        case DataType.Int:
                            dbtype = DbType.Int32;
                            int tempValue;
                            if (Int32.TryParse(queryParameters[i].Value, out tempValue))
                            {
                                paramValue = tempValue;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.Number:
                            dbtype = DbType.Single;
                            Single tempSingleValue;
                            if (Single.TryParse(queryParameters[i].Value, out tempSingleValue))
                            {
                                paramValue = tempSingleValue;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.String:
                            dbtype = DbType.AnsiString;
                            paramValue = queryParameters[i].Value;
                            break;
                        case DataType.DateTime:
                            dbtype = DbType.DateTime;
                            if (queryParameters[i].Value != null)
                            {
                                paramValue = queryParameters[i].Value;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.Bool:
                            dbtype = DbType.Boolean;
                            paramValue = Boolean.Parse(queryParameters[i].Value);
                            break;
                    }

                    DbParameter param = db.GetDbParameter(cmd, queryParameters[i].Name, dbtype, paramValue);

                    cmd.Parameters.Add(param);
                }

                table = db.ExecuteDataTable(cmd);
            }
            catch (Exception ex)
            {
                //需要日志记录
                Logger.Log("[SQL:" + sql + "]", LogType.Error);
                for (int i = 0; i < queryParameters.Length; i++)
                {
                    Logger.Log("[Q:" + queryParameters[i].Name + ":" + queryParameters[i].Value + "]", LogType.Error);
                }
                Logger.Log(ex);
            }
            finally
            {
                if (db != null)
                {
                    db.Dispose();
                }

                if (cmd != null)
                {
                    cmd.Dispose();
                }
            }
            return table;
        }

       

        public DataTable ExecuteSQL(string SQL,DbParameter[] paras)
        {
            DataTable table = null;
            DatabaseHelper db = null;
            DbCommand cmd = null;

            try
            {
                db = new DatabaseHelper();
                cmd = db.NewSQLCommand(SQL,paras);

                DataSet ds = db.ExecuteDataSet(cmd);
                if (ds.Tables.Count > 0)
                    table = ds.Tables[0];

                cmd.Dispose();
                db.Dispose();
            }
            catch (Exception ex)
            {
                //需要日志记录
                Logger.Log("[" + SQL + "]", LogType.Error);
                Logger.Log(ex);
                //logger.Error(e.Message);
                //throw e;
            }
            finally
            {
                if (db != null)
                {
                    db.Dispose();
                }

                if (cmd != null)
                {
                    cmd.Parameters.Clear();
                    cmd.Dispose();
                }
            }

            return table;
        }

        //public bool ExecuteSQLTranWithSqlBulkCopy(string tableName, DataTable dt)
        //{
        //    string connectionString = System.Configuration.ConfigurationManager.ConnectionStrings["Default"].ConnectionString;
        //    connectionString = DatabaseHelper.DecryptConnectionString(connectionString);
        //    using (SqlBulkCopy sqlbulkcopy = new SqlBulkCopy(connectionString, SqlBulkCopyOptions.UseInternalTransaction))
        //    {
        //        try
        //        {
        //            sqlbulkcopy.DestinationTableName = tableName;
        //            for (int i = 0; i < dt.Columns.Count; i++)
        //            {
        //                sqlbulkcopy.ColumnMappings.Add(dt.Columns[i].ColumnName, dt.Columns[i].ColumnName);
        //            }
        //            sqlbulkcopy.WriteToServer(dt);
        //            return true;
        //        }
        //        catch (System.Exception ex)
        //        {
        //            Logger.Log(ex);
        //            return false;
        //        }
        //    } 
        //}

        public bool ExecuteSQLNoQuery(string SQL)
        {
            DatabaseHelper db = null;
            DbCommand cmd = null;

            try
            {
                db = new DatabaseHelper();
                cmd = db.NewSQLCommand(SQL);

                db.ExecuteNoQuery(cmd);

                cmd.Dispose();
                db.Dispose();
            }
            catch (Exception ex)
            {
                //需要日志记录
                Logger.Log("[" + SQL + "]", LogType.Error);
                Logger.Log(ex);
                return false;
            }
            finally
            {
                if (db != null)
                {
                    db.Dispose();
                }

                if (cmd != null)
                {
                    cmd.Dispose();
                }
            }

            return true;
        }

        public int ExecuteSQLIntQuery(string SQL)
        {
            DatabaseHelper db = null;
            DbCommand cmd = null;
            int effect = -2;
            try
            {
                db = new DatabaseHelper();
                cmd = db.NewSQLCommand(SQL);

                effect=db.ExecuteNoQuery(cmd);

                cmd.Dispose();
                db.Dispose();
            }
            catch (Exception ex)
            {
                //需要日志记录
                Logger.Log(ex);
                throw ex;
                //return effect;
            }
            finally
            {
                if (db != null)
                {
                    db.Dispose();
                }

                if (cmd != null)
                {
                    cmd.Dispose();
                }
            }

            return effect;
        }

        public bool ExecuteSQLNoQuery(string SQL,DbParameter[] paras)
        {
            DatabaseHelper db = null;
            DbCommand cmd = null;

            try
            {
                db = new DatabaseHelper();
                cmd = db.NewSQLCommand(SQL,paras);

                db.ExecuteNoQuery(cmd);

                cmd.Dispose();
                db.Dispose();
            }
            catch (Exception ex)
            {
                //需要日志记录
                Logger.Log(ex);
                return false;
            }
            finally
            {

                if (db != null)
                {
                    db.Dispose();
                }

                if (cmd != null)
                {
                    cmd.Parameters.Clear();
                    cmd.Dispose();
                }
            }

            return true;
        }


        public bool ExecuteSQLNoQuery(string SQL, QueryParameter[] queryParameters)
        {
            DatabaseHelper db = null;
            DbCommand cmd = null;

            try
            {
                db = new DatabaseHelper();
                cmd = db.NewSQLCommand(SQL);
                for (var i = 0; i < queryParameters.Length; i++)
                {
                    DbType dbtype = DbType.Object;
                    Object paramValue = null;
                    switch (queryParameters[i].DataType)
                    {
                        case DataType.Int:
                            dbtype = DbType.Int32;
                            int tempValue;
                            if (Int32.TryParse(queryParameters[i].Value, out tempValue))
                            {
                                paramValue = tempValue;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.Number:
                            dbtype = DbType.Single;
                            Single tempSingleValue;
                            if (Single.TryParse(queryParameters[i].Value, out tempSingleValue))
                            {
                                paramValue = tempSingleValue;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.String:
                            dbtype = DbType.AnsiString;
                            paramValue = queryParameters[i].Value;
                            break;
                        case DataType.DateTime:
                            dbtype = DbType.DateTime;
                            if (queryParameters[i].Value != null)
                            {
                                paramValue = queryParameters[i].Value;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.Bool:
                            dbtype = DbType.Boolean;
                            paramValue = Boolean.Parse(queryParameters[i].Value);
                            break;
                    }

                    DbParameter param = db.GetDbParameter(cmd, queryParameters[i].Name, dbtype, paramValue);

                    cmd.Parameters.Add(param);
                }

                db.ExecuteNoQuery(cmd);

                cmd.Dispose();
                db.Dispose();
            }
            catch (Exception ex)
            {
                //需要日志记录
                Logger.Log(ex);
                return false;
            }
            finally
            {

                if (db != null)
                {
                    db.Dispose();
                }

                if (cmd != null)
                {
                    cmd.Parameters.Clear();
                    cmd.Dispose();
                }
            }

            return true;
        }

        public DataTable ExecuteStoredProcedure(string stroreProcedure, QueryParameter[] queryParameters)
        {
            DataTable table = null;
            DatabaseHelper db = null;
            DbCommand cmd = null;

            try
            {
                db = new DatabaseHelper();
                cmd = db.NewProcedureCommand(stroreProcedure);
                for (var i = 0; i < queryParameters.Length; i++)
                {
                    DbType dbtype = DbType.Object;
                    Object paramValue = null;
                    switch (queryParameters[i].DataType)
                    {
                        case DataType.Int:
                            dbtype = DbType.Int32;
                            int tempValue;
                            if (Int32.TryParse(queryParameters[i].Value, out tempValue))
                            {
                                paramValue = tempValue;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.Number:
                            dbtype = DbType.Single;
                            Single tempSingleValue;
                            if (Single.TryParse(queryParameters[i].Value, out tempSingleValue))
                            {
                                paramValue = tempSingleValue;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.String:
                            dbtype = DbType.AnsiString;
                            paramValue = queryParameters[i].Value;
                            break;
                        case DataType.DateTime:
                            dbtype = DbType.DateTime;
                            if (queryParameters[i].Value != null)
                            {
                                paramValue = queryParameters[i].Value;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.Bool:
                            dbtype = DbType.Boolean;
                            paramValue = Boolean.Parse(queryParameters[i].Value);
                            break;
                    }

                    DbParameter param = db.GetDbParameter(cmd, queryParameters[i].Name, dbtype, paramValue);

                    cmd.Parameters.Add(param);
                }

                table = db.ExecuteDataSet(cmd).Tables[0];
            }
            catch (Exception ex)
            {
                //需要日志记录
                Logger.Log("[P:" + stroreProcedure + "]", LogType.Error);
                for(int i=0;i< queryParameters.Length; i++) {
                    Logger.Log("[Q:"+queryParameters[i].Name+":"+queryParameters[i].Value+"]", LogType.Error);
                }
                Logger.Log(ex);
                //logger.Error(ex.Message);
                //throw ex;
            }
            finally
            {
                if (db != null)
                {
                    db.Dispose();
                }

                if (cmd != null)
                {
                    cmd.Dispose();
                }
            }
            return table;

        }


        public object ExecuteStoredProcedureScalar(string stroreProcedure, QueryParameter[] queryParameters)
        {
            object obj = null;
            DatabaseHelper db = null;
            DbCommand cmd = null;

            try
            {
                db = new DatabaseHelper();
                cmd = db.NewProcedureCommand(stroreProcedure);
                for (var i = 0; i < queryParameters.Length; i++)
                {
                    DbType dbtype = DbType.Object;
                    Object paramValue = null;
                    switch (queryParameters[i].DataType)
                    {
                        case DataType.Int:
                            dbtype = DbType.Int32;
                            int tempValue;
                            if (Int32.TryParse(queryParameters[i].Value, out tempValue))
                            {
                                paramValue = tempValue;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.Number:
                            dbtype = DbType.Single;
                            Single tempSingleValue;
                            if (Single.TryParse(queryParameters[i].Value, out tempSingleValue))
                            {
                                paramValue = tempSingleValue;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.String:
                            dbtype = DbType.AnsiString;
                            paramValue = queryParameters[i].Value;
                            break;
                        case DataType.DateTime:
                            dbtype = DbType.DateTime;
                            if (queryParameters[i].Value != null)
                            {
                                paramValue = queryParameters[i].Value;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.Bool:
                            dbtype = DbType.Boolean;
                            paramValue = Boolean.Parse(queryParameters[i].Value);
                            break;
                    }

                    DbParameter param = db.GetDbParameter(cmd, queryParameters[i].Name, dbtype, paramValue);

                    cmd.Parameters.Add(param);
                }

                obj = db.ExecuteScalar(cmd);
            }
            catch (Exception ex)
            {
                //需要日志记录
                Logger.Log("[P:" + stroreProcedure + "]", LogType.Error);
                for (int i = 0; i < queryParameters.Length; i++)
                {
                    Logger.Log("[Q:" + queryParameters[i].Name + ":" + queryParameters[i].Value + "]", LogType.Error);
                }
                Logger.Log(ex);
                //logger.Error(ex.Message);
                //throw ex;
            }
            finally
            {
                if (db != null)
                {
                    db.Dispose();
                }

                if (cmd != null)
                {
                    cmd.Dispose();
                }
            }
            return obj;

        }


        public DataSet ExecuteStoredProcedureDataSet(string stroreProcedure, QueryParameter[] queryParameters)
        {
            DataSet dataset = null;
            DatabaseHelper db = null;
            DbCommand cmd = null;

            try
            {
                db = new DatabaseHelper();
                cmd = db.NewProcedureCommand(stroreProcedure);
                for (var i = 0; i < queryParameters.Length; i++)
                {
                    DbType dbtype = DbType.Object;
                    Object paramValue = null;
                    switch (queryParameters[i].DataType)
                    {
                        case DataType.Int:
                            dbtype = DbType.Int32;
                            int tempValue;
                            if (Int32.TryParse(queryParameters[i].Value, out tempValue))
                            {
                                paramValue = tempValue;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.Number:
                            dbtype = DbType.Single;
                            Single tempSingleValue;
                            if (Single.TryParse(queryParameters[i].Value, out tempSingleValue))
                            {
                                paramValue = tempSingleValue;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.String:
                            dbtype = DbType.AnsiString;
                            paramValue = queryParameters[i].Value;
                            break;
                        case DataType.DateTime:
                            dbtype = DbType.DateTime;
                            if (queryParameters[i].Value != null)
                            {
                                paramValue = queryParameters[i].Value;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.Bool:
                            dbtype = DbType.Boolean;
                            paramValue = Boolean.Parse(queryParameters[i].Value);
                            break;
                    }

                    DbParameter param = db.GetDbParameter(cmd, queryParameters[i].Name, dbtype, paramValue);

                    cmd.Parameters.Add(param);
                }

                dataset = db.ExecuteDataSet(cmd);
            }
            catch (Exception ex)
            {
                //需要日志记录
                Logger.Log("[P:" + stroreProcedure + "]", LogType.Error);
                for (int i = 0; i < queryParameters.Length; i++)
                {
                    Logger.Log("[Q:" + queryParameters[i].Name + ":" + queryParameters[i].Value + "]", LogType.Error);
                }
                Logger.Log(ex);
                //logger.Error(ex.Message);
                //throw ex;
            }
            finally
            {
                if (db != null)
                {
                    db.Dispose();
                }

                if (cmd != null)
                {
                    cmd.Dispose();
                }
            }
            return dataset;
        }


        public int ExecuteStoredProcedureNoQuery(string stroreProcedure, QueryParameter[] queryParameters)
        {
            DatabaseHelper db = null;
            DbCommand cmd = null;
            int iExe = -2; //如果程序异常,返回-2

            try
            {
                db = new DatabaseHelper();
                cmd = db.NewProcedureCommand(stroreProcedure);
                for (var i = 0; i < queryParameters.Length; i++)
                {
                    DbType dbtype = DbType.Object;
                    Object paramValue = null;
                    switch (queryParameters[i].DataType)
                    {
                        case DataType.Int:
                            dbtype = DbType.Int32;
                            int tempValue;
                            if (Int32.TryParse(queryParameters[i].Value, out tempValue))
                            {
                                paramValue = tempValue;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.Number:
                            dbtype = DbType.Single;
                            Single tempSingleValue;
                            if (Single.TryParse(queryParameters[i].Value, out tempSingleValue))
                            {
                                paramValue = tempSingleValue;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.String:
                            dbtype = DbType.AnsiString;
                            paramValue = queryParameters[i].Value;
                            break;
                        case DataType.DateTime:
                            dbtype = DbType.DateTime;
                            if (queryParameters[i].Value !=null)
                            {
                                paramValue = queryParameters[i].Value;
                            }
                            else
                            {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.Bool:
                            dbtype = DbType.Boolean;
                            paramValue = Boolean.Parse(queryParameters[i].Value);
                            break;
                    }

                    DbParameter param = db.GetDbParameter(cmd, queryParameters[i].Name, dbtype, paramValue);

                    cmd.Parameters.Add(param);
                }

                iExe =db.ExecuteNoQuery(cmd);
            }
            catch (Exception ex)
            {
                //需要日志记录
                Logger.Log("[P:" + stroreProcedure + "]", LogType.Error);
                for (int i = 0; i < queryParameters.Length; i++)
                {
                    Logger.Log("[Q:" + queryParameters[i].Name + ":" + queryParameters[i].Value + "]", LogType.Error);
                }
                Logger.Log(ex);
            }
            finally
            {
                if (db != null)
                {
                    db.Dispose();
                }

                if (cmd != null)
                {
                    cmd.Dispose();
                }
            }
            return iExe;
        }

        public DataTable[] ExecuteSQLMultiReturn(string SQL)
        {
            DataTable[] tables = null;
            DatabaseHelper db = null;
            DbCommand cmd = null;

            try
            {
                db = new DatabaseHelper();
                cmd = db.NewSQLCommand(SQL);

                DataSet ds = db.ExecuteDataSet(cmd);
                if (ds.Tables.Count > 0)
                {
                    tables = new DataTable[ds.Tables.Count];
                    for (int j = 0; j < ds.Tables.Count; j++)
                        tables[j] = ds.Tables[j];
                }

                cmd.Dispose();
                db.Dispose();
            }
            catch (Exception e)
            {
                Logger.Log("[" + SQL + "]", LogType.Error);
                Logger.Log(e);
                return null;

            }
            finally
            {
                if (db != null)
                {
                    db.Dispose();
                }

                if (cmd != null)
                {
                    cmd.Dispose();
                }
            }

            return tables;
        }

        #endregion
    }
}
