﻿using Delivery.API;
using DM.TestOrder.DAL;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Text;

namespace DM.TestOrder.Controllers
{
    public class StandardDicCmdCheckListController : BaseController{
        [HttpGet]
        public JsonResult Get()
        {
            var dt = WoStandardDicCmdCheckListDal.GetAll();
            return new JsonResult(dt);
        }

        [HttpPost]
        public JsonResult POST([FromBody] JObject value)
        {
            var StandardDicId = int.Parse(value["StandardDicId"].ToString());
            var IsMust = value["CheckIsMust"].ToString(); // 是 或 否

            var rtnMsg = WoStandardDicCmdCheckListDal.UpdateOne(StandardDicId, IsMust);
            return new JsonResult(new
            {
                errormsg = rtnMsg
            });

        }
    }
}
