{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*",
  "Database": {
    "Default": {
      "ConnectionString": "server=*************;uid=root;pwd=**********;database=siteweb_1000w",
      "ProviderName": "MySql"
    },
    "default_np": {
      "connectionString": "server='*************';port='3306';Database='siteweb_1000w';UID='root';PWD='**********';Allow User Variables=true;",
      "providerName": "mysql"

      //"connectionString": "server='*************';port='5236';user='siteweb';password='**********';",
      //"providerName": "dm8"

      //"connectionString": "Host=*************;Port=5432;Username=postgres;Password=**********;Database=siteweb;",
      //"providerName": "npgsql"

      //"connectionString": "Server=*************;Port=5432;User Id=postgres;Password=**********;Database=siteweb;",
      //"providerName": "opengauss"

      //"connectionString": "Host='*************';port='5432';Username='postgres';Password='**********';Database=siteweb;",
      //"providerName": "hangao"
    }
  },
  //���ô洢����
  "NoProcedure": "true",
  "Sqlpath": "/SqlScript/",
  "BInterfaceType": "1",
  "WorkFlow": "false",
  "ApiSettings": {
    "BaseUrl": "http://127.0.0.1:8080"
  },

  "DefaultRedisServer": "siteweb1!@*************:6379",
  "DefaultReadRedisServer": "siteweb1!@*************:6379",
  "DefaultWriteRedisServer": "siteweb1!@*************:6379",

  "Serilog": {
    "MinimumLevel": "Debug",
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "Logs/log.txt",
          "rollOnFileSizeLimit": true,
          "fileSizeLimitBytes": "10485760",
          "retainedFileCountLimit": 10
        }
      }
    ]
  },
  "Urls": "http://*:5000"
}
