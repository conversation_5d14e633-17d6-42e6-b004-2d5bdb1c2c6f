﻿

using BDSTool.DBUtility;
using ENPC.Kolo.Entity.B_CMCC;

using log4net;

using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.Entity.DRHelper
{
    public class TSignalHeper
    {
        private static readonly ILog logger = LogManager.GetLogger("BDSTool");
        //public string Type          {get;set;}	  //EnumType	数据类型
        //public string ID            {get;set;}	//Char[ID_LENGTH]	监控点ID
        //public string SignalName    {get;set;}	//Char[NAME_LENGTH]	信号名称
        //public string AlarmLevel    {get;set;}	//EnumState	告警等级
        //public string Threshold     {get;set;}	//Float	门限值
        //public string AbsoluteVal   {get;set;}	//Float	绝对阀值
        //public string RelativeVal   {get;set;}	//Float	百分比阀值
        //public string Describe      {get;set;}	//Char [DES_LENGTH]	描述信息。状态信号为状态描述,，格式举例：0&正常;1&告警 。模拟信号为单位。
        //public string NMAlarmID     {get;set;}	//Char[NMALARMID_LEN]	网管告警编号（参照《中国移动动环命名及编码指导意见》） 
   
        public static TSignal FromDataRow(DataRow row) {

            var entity = new TSignal();
                
            entity.ID = row["ID"].ToString();

            entity.SignalNumber = row["SignalNumber"].ToString();

            entity.SignalName = row["SignalName"].ToString();


            entity.Type =  SHelper.ToEnumType(row["Type"]); 
            entity.AlarmLevel = SHelper.ToEnumState(row["AlarmLevel"]);
            entity.Threshold = SHelper.ToFloat(row["Threshold"]);
            entity.AbsoluteVal = SHelper.ToFloat(row["AbsoluteVal"]);
            entity.RelativeVal = SHelper.ToFloat(row["RelativeVal"]);


            entity.Describe = row["Describe"].ToString();
            entity.NMAlarmID = row["NMAlarmID"].ToString();
            
            return entity;
        }
    }
}
