﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class GetAlarmAck : BMessage
    {
        public List<TAlarm> LAlarm { get; set; }

        #region SiteWeb配置

        public int MyStationId { get; set; }

        #endregion
        public GetAlarmAck() : base()
        {
            MessageType = (int)BMessageType.GET_ALARM_ACK;
        }
        public GetAlarmAck(List<TAlarm> list) : base()
        {
            MessageType = (int)BMessageType.GET_ALARM_ACK;
            LAlarm = list;
        }
        public static GetAlarmAck Deserialize(XmlDocument xmlDoc)
        {
            GetAlarmAck getAlarmAck = null;
            string errMsg = string.Empty;
            string suId = string.Empty;
            string surId = string.Empty;
            try
            {
                suId = xmlDoc.SelectSingleNode("/Response/Info/SUId").InnerText.Trim();
                surId = xmlDoc.SelectSingleNode("/Response/Info/SURId").InnerText.Trim();
                List<TAlarm> list = new List<TAlarm>();
                XmlNodeList nodelist = xmlDoc.SelectNodes("/Response/Info/Values/TAlarmList/TAlarm");
                foreach (XmlNode node in nodelist)
                {
                    string serialNo = node.SelectSingleNode("SerialNo").InnerText.Trim();
                    //suId = node.SelectSingleNode("SUId").InnerText.Trim();
                    //surId = node.SelectSingleNode("SURId").InnerText.Trim();
                    string deviceId = node.SelectSingleNode("DeviceId").InnerText.Trim();
                    string deviceRId = node.SelectSingleNode("DeviceRId").InnerText.Trim();
                    //string id = node.SelectSingleNode("Id").InnerText.Trim();
                    XmlNode signalNode = node.SelectSingleNode("Signal");
                    string id = signalNode.Attributes["Id"].Value.Trim();
                    string strAlarmTime = node.SelectSingleNode("AlarmTime").InnerText.Trim();
                    DateTime? alarmTime;
                    if(string.IsNullOrEmpty(strAlarmTime))
                    {
                        errMsg = "GetAlarmAck TAlarm.AlarmTime is NullOrEmtpy";
                        break;
                    }
                    else
                    {
                        alarmTime = Convert.ToDateTime(strAlarmTime);
                    }
                    string alarmDesc = node.SelectSingleNode("AlarmDesc").InnerText.Trim();
                    string strTriggerVal = node.SelectSingleNode("TriggerVal").InnerText.Trim();
                    float? triggerVal;
                    if(string.IsNullOrEmpty(strTriggerVal))
                    {
                        errMsg = "GetAlarmAck TAlarm.TriggerVal is NullOrEmtpy";
                        break;
                    }
                    else
                    {
                        strTriggerVal = strTriggerVal.ToUpper();
                        strTriggerVal = strTriggerVal.Replace('V', ' ').Replace('A', ' ').Replace('℃', ' ').Replace('%', ' ').Replace('R', ' ').Replace('H', ' ');
                        strTriggerVal = strTriggerVal.Replace('K', ' ').Replace('B', ' ').Replace('Y', ' ').Replace('T', ' ').Replace('E', ' ').Replace('D', ' ');
                        strTriggerVal = strTriggerVal.Replace('D', ' ').Replace('M', ' ').Trim();
                        triggerVal = float.Parse(strTriggerVal);
                    }
                    string strAlarmFlag = node.SelectSingleNode("AlarmFlag").InnerText.Trim();
                    EnumFlag enumFlag;
                    if(string.IsNullOrEmpty(strAlarmFlag))
                    {
                        errMsg = "GetAlarmAck TAlarm.AlarmFlag is NullOrEmtpy";
                        break;
                    }
                    else
                    {
                        if (strAlarmFlag.ToUpper() == "BEGIN")
                        {
                            enumFlag = EnumFlag.BEGIN;
                        }
                        else if (strAlarmFlag.ToUpper() == "END")
                        {
                            enumFlag = EnumFlag.END;
                        }
                        else
                        {
                            enumFlag = (EnumFlag)int.Parse(strAlarmFlag);
                        }
                    }
                    TAlarm alarm = new TAlarm(serialNo, suId, surId, id, deviceId, deviceRId, alarmTime, alarmDesc, triggerVal, enumFlag);
                    list.Add(alarm);
                }
                getAlarmAck = new GetAlarmAck();
                if (errMsg != string.Empty)
                {
                    getAlarmAck.ErrorMsg = errMsg;
                }
                else
                {
                    getAlarmAck.LAlarm = list;
                }
                getAlarmAck.SUId = suId;
                getAlarmAck.SURId = surId;
                entityLogger.DebugFormat("GetAlarmAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                getAlarmAck.StringXML = xmlDoc.InnerXml;
                return getAlarmAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetAlarmAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetAlarmAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                getAlarmAck = new GetAlarmAck();
                getAlarmAck.ErrorMsg = ex.Message;
                getAlarmAck.StringXML = xmlDoc.InnerXml;
                return getAlarmAck;
            }
        }
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            if (LAlarm.Count > 0)
            {
                foreach (TAlarm ta in LAlarm)
                {
                    sb.AppendFormat("{0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11}\n",
                     MessageId, (BMessageType)MessageType, ta.SerialNo, ta.SUID, ta.SURId, ta.ID, ta.DeviceID, ta.DeviceRId, 
                        ta.AlarmTime, ta.AlarmDesc, ta.TriggerVal, ta.AlarmFlag.ToString());
                }
            }
            else
            {
                sb.AppendFormat("{0},{1},is null.", MessageId, (BMessageType)MessageType);
            }
            return sb.ToString();
        }
    }
}
