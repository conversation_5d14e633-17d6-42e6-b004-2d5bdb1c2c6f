﻿'use strict';
var cookie = getCookie();
var roleTypeName;

$(document).ready(function () {
    if (!cookie || !cookie["userinfo"])
        location.href = '/';

    $.ajax({
        type: "GET",
        url: '../api/Account?userid=' + cookie['userinfo']['UserId'],
        dataType: 'json',
        async: false,
        success: function (data) {
            if (data && data.length > 0) {
                roleTypeName = data[0].RoleTypeName;
                if (roleTypeName === '系统管理员') {
                    $('#ComprehensiveQuery').parent().show();
                    $('#CompanyManagement').parent().show();
                    if (cookie['userinfo']['UserId'] === '-1') {
                        $('#DicManagement').parent().show();
                        $('#StationCategoryManagement').parent().show();
                    }
                }
                else if (roleTypeName === '厂商') {
                    $('#NewOrder').parent('').show();
                    $('#ComprehensiveQuery').parent().show();
                }

            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
    GetNeedApproveResource();
    GetNeedApproveOrder();
    $('#daibanul').show();

    //资源管理
    $("#StationManagement").attr('rel', './StationManagement');
    $("#HouseManagement").attr('rel', './HouseManagement');
    $("#FsuManagement").attr('rel', './FsuManagement');
    $("#DeviceManagement").attr('rel', './DeviceManagement');
    //待办-资源管理
    $("#stationToDo").attr('rel', './StationManagement');
    $("#houseToDo").attr('rel', './HouseManagement');
    $("#fsuToDo").attr('rel', './FsuManagement');

    //工程割接
    $("#NewOrder").attr('rel', './NewOrder?AndersCheung=' + Math.uuid(15));
    $("#ComprehensiveQuery").attr('rel', './ComprehensiveQuery');
    $("#CompanyManagement").attr('rel', './CompanyManagement');
    $("#DicManagement").attr('rel', './DicManagement');
    $("#StationCategoryManagement").attr('rel', './StationCategoryManagement');
    
    //待办-工程割接
    $("#applyToDo").attr('rel', './ComprehensiveQuery?orderState=1');
    $("#expertToDo").attr('rel', './ComprehensiveQuery?orderState=2');
    $("#reviewToDo").attr('rel', './ComprehensiveQuery?orderState=3');
    $('#applyToDo,#expertToDo,#reviewToDo').unbind('click').click(function () {
        var tabTitle = '综合查询';
        var url = $(this).attr("rel");
        addTab(tabTitle, url);
        if ($('#tabs').tabs('exists', tabTitle))
            $('#tabs').tabs('getTab', tabTitle).find('iframe').eq(0).attr('src', url);

        $('.easyui-accordion li div').removeClass("selected");
        $(this).parent().addClass("selected");
    });

    //割接单审核tab页关闭时清除锁
    $('#tabs').tabs({
        onBeforeClose: function (title, index) {
            var tab = $('#tabs').tabs('getTab', title);
            if (/割接单审核\w*/i.test(tab.panel('options').title))
                tab.find('iframe').eq(0).get(0).contentWindow.clearLock();
        }
    });

    $('div.mask').remove();
});

//获取资源管理待办项
function GetNeedApproveResource() {
    $.ajax({
        type: "GET",
        contentType: "application/x-www-form-urlencoded",
        url: `${STATION_HANDLER}/GetNeedApproveResource`,
        async: false,
        //data: { action: 'GetNeedApproveResource' },
        success: function (data) {
            //console.log(data);
            var obj = JSON.parse(data);
            if (obj.StationCount === '0' && obj.HouseCount === '0' && obj.FsuCount === '0')
                $('#daibanul').children('li').eq(0).hide();
            else {
                if (obj.StationCount !== '0') {
                    $('#stationToDo').next().html(obj.StationCount);
                    $('#stationToDo').parentsUntil('ul').last().show();
                }
                else
                    $('#stationToDo').parentsUntil('ul').last().hide();
                if (obj.HouseCount !== '0') {
                    $('#houseToDo').next().html(obj.HouseCount);
                    $('#houseToDo').parentsUntil('ul').last().show();
                }
                else
                    $('#houseToDo').parentsUntil('ul').last().hide();
                if (obj.FsuCount !== '0') {
                    $('#fsuToDo').next().html(obj.FsuCount);
                    $('#fsuToDo').parentsUntil('ul').last().show();
                }
                else {
                    $('#fsuToDo').parentsUntil('ul').last().hide();
                }
                $('#daibanul').children('li').eq(0).show();
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
}

//获取工程割接待办项
function GetNeedApproveOrder() {
    $.ajax({
        type: "GET",
        contentType: "application/json; charset=UTF-8",
        url: '../api/OrderList/' + cookie['userinfo']['UserId'],
        async: false,
        success: function (data) {
            if (data && data.length > 0) {
                var arr = [];
                for (var i = 0; i < data.length; i++) {
                    arr.push(data[i].OrderState);
                }
                if (arr.indexOf(1) < 0)
                    $('#applyToDo').parentsUntil('ul').last().hide();
                if (arr.indexOf(2) < 0)
                    $('#expertToDo').parentsUntil('ul').last().hide();
                if (arr.indexOf(3) < 0)
                    $('#reviewToDo').parentsUntil('ul').last().hide();
                for (var j = 0; j < data.length; j++) {
                    if (data[j].OrderState === 1) {
                        if (data[j].OrderCount) {
                            $('#applyToDo').parentsUntil('ul').last().show();
                            $('#applyToDo').next().html(data[j].OrderCount);
                        } else
                            $('#applyToDo').parentsUntil('ul').last().hide();
                    } else if (data[j].OrderState === 2) {
                        if (data[j].OrderCount) {
                            $('#expertToDo').parentsUntil('ul').last().show();
                            $('#expertToDo').next().html(data[j].OrderCount);
                        } else
                            $('#expertToDo').parentsUntil('ul').last().hide();
                    } else if (data[j].OrderState === 3) {
                        if (data[j].OrderCount) {
                            $('#reviewToDo').parentsUntil('ul').last().show();
                            $('#reviewToDo').next().html(data[j].OrderCount);
                        } else
                            $('#reviewToDo').parentsUntil('ul').last().hide();
                    }
                }
                $('#daibanul').children('li').eq(1).show();
            } else
                $('#daibanul').children('li').eq(1).hide();
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
}