﻿using DM.TestOrder.Entity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using DM.TestOrder.DAL.Helper;
using Delivery.DAL;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common;

namespace testorder.dal
{
    public partial class WoTestOrderEquipItemDal
    {

        private readonly static string _workflow = ConfigHelper.GetSection("WorkFlow").Value;
        public static bool IsExist(int orderId, int EquipmentId) {
            object rtn;
            if (CommonUtils.IsNoProcedure)
            {
                rtn = Public_ExecuteSqlService.Instance.NoStore_WOTestOrderEquipItem_JudgeExist(orderId, EquipmentId);
            }
            else
            {
                var sql = "select 1 from WO_TestOrderEquipItem where OrderId =@orderId and EquipmentId=@equipmentId";
                rtn = new ExecuteSql().ExecuteSQLScalar(sql, new QueryParameter[]
                {
                new QueryParameter("orderId", DataType.Int, orderId.ToString()),
                new QueryParameter("equipmentId", DataType.Int, EquipmentId.ToString())
                });
            }
            return rtn == null;
        }
        public static WO_TestOrderEquipItem GetOne(int orderId, int EquipmentId) {

            DataTable tb = new DataTable();
            if (CommonUtils.IsNoProcedure)
            {
                tb = Public_ExecuteSqlService.Instance.NoStore_WOTestOrderEquipItem_GetByOrderIdAndEquipmentId(orderId, EquipmentId);
            }
            else
            {
                var sql = "select *  from WO_TestOrderEquipItem where OrderId =@orderId and EquipmentId=@equipmentId";
                tb = new ExecuteSql().ExecuteSQL(sql, new QueryParameter[]
                {
                new QueryParameter("orderId", DataType.Int, orderId.ToString()),
                new QueryParameter("equipmentId", DataType.Int, EquipmentId.ToString())
                });
            }
            if (tb.Rows.Count == 0)
                return null;

            return WO_TestOrderEquipItemHelper.FromDataRow(tb.Rows[0]);

        }

        public static List<WO_TestOrderEquipItem> GetAllByOrderId(int orderId) {
            var items = new List<WO_TestOrderEquipItem>();
            DataTable tb = new DataTable();
            if (CommonUtils.IsNoProcedure)
            {
                tb = Public_ExecuteSqlService.Instance.NoStore_WOTestOrderEquipItem_GetAllByOrderId(orderId);
            }
            else
            {
                var sql = "select *  from WO_TestOrderEquipItem where OrderId =@orderId";
                tb = new ExecuteSql().ExecuteSQL(sql, new QueryParameter[]
                {
                new QueryParameter("orderId", DataType.Int, orderId.ToString())
                });
            }

            foreach (DataRow row in tb.Rows) {
                var item = WO_TestOrderEquipItemHelper.FromDataRow(row);
                items.Add(item);
            }

            return items;

        }


        public static void DBAddOne(int orderId, int equipmentId, string UriProtocol = null, string UriImage = null) {
            //var sql = string.Format("WO_TestOrderEquipItem_Add {0},{1},{2},{3}"
            //    , SHelper.GetPara(orderId)
            //    , SHelper.GetPara(equipmentId)
            //    , SHelper.GetPara(UriProtocol)
            //    , SHelper.GetPara(UriImage)
            //    );
            //DBHelper.ExecuteNonQuery(sql);
            if (CommonUtils.IsNoProcedure){
                WOTestOrderEquipItemAddService.Instance.TestOrderEquipItemAdd(orderId, equipmentId, UriProtocol, UriImage);
            }
            else {
                if (_workflow == "true") {
                    new ExecuteSql().ExecuteStoredProcedureNoQuery("WO_TestOrderEquipItem_Add1", new QueryParameter[] {
                    new QueryParameter("OrderId", DataType.Int, orderId.ToString()),
                    new QueryParameter("EquipmentId", DataType.Int, equipmentId.ToString()),
                    new QueryParameter("UriProtocol", DataType.String, UriProtocol),
                    new QueryParameter("UriImage", DataType.String, UriImage)});
                }
                else {
                    new ExecuteSql().ExecuteStoredProcedureNoQuery("WO_TestOrderEquipItem_Add", new QueryParameter[] {
                    new QueryParameter("OrderId", DataType.Int, orderId.ToString()),
                    new QueryParameter("EquipmentId", DataType.Int, equipmentId.ToString()),
                    new QueryParameter("UriProtocol", DataType.String, UriProtocol),
                    new QueryParameter("UriImage", DataType.String, UriImage)
                    });
                }
                
            }
            
        }


        public static void DBDeleteOne(int orderId, int equipmentId) {
            if(CommonUtils.IsNoProcedure)
            {
                 WoCommonService.Instance.WO_TestOrderEquipItem_Del(orderId, equipmentId);
                return;
            }
            new ExecuteSql().ExecuteStoredProcedureNoQuery("WO_TestOrderEquipItem_Del", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, orderId.ToString()),
                new QueryParameter("EquipmentId", DataType.Int, equipmentId.ToString())
            });
        }


        //public static void DBUpdate(int orderId, int equipmentId, string UriProtocol = null, string UriImage = null) {
        //    var sql = string.Format("update WO_TestOrderEquipItem set UriProtocol= {0}, UriImage={1} where orderId={2} and equipmentId={3}"
        //        , SHelper.GetPara(UriProtocol)
        //        , SHelper.GetPara(UriImage)
        //        , SHelper.GetPara(orderId)
        //        , SHelper.GetPara(equipmentId)
        //        );
        //    DBHelper.ExecuteNonQuery(sql);
        //}

        

    }
}
