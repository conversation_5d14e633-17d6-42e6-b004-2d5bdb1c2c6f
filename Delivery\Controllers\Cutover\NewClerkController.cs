﻿using DM.TestOrder.DAL;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web.Http;
using testorder.dal;



namespace DM.TestOrder.Controllers {
        
    public class NewClerkController : BaseApiController{

        //var paras = {
        //        "CompanyName": "新增公司",
        //        "ClerkName": "新增某人",
        //    };
        public HttpResponseMessage POST([FromBody]JObject value) {
            var CompanyId =int.Parse( value["CompanyId"].ToString());
            //var CompanyName = value["CompanyName"].ToString();
            var ClerkName = value["ClerkName"].ToString();

            var rtnMsg = WoInstallClerkDal.AddOneToCompany(CompanyId, ClerkName);


            var rtn = new {
                errormsg = rtnMsg
            };
            var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(rtn);


            Debug.WriteLine(value.ToString());
            Debug.WriteLine(jsonRtn.ToString());

            return new HttpResponseMessage() {
                Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            };
        }
    }
}
