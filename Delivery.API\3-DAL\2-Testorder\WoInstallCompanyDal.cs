﻿

using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using Delivery.DAL;
using System.Data;


namespace DM.TestOrder.DAL {
    public class WoInstallCompanyDal
    {
        public static DataTable  GetAll() {
            DataTable tb;
            if (CommonUtils.IsNoProcedure)
            {
                tb = Public_ExecuteSqlService.Instance.NoStore_WOInstallCompany_GetAll();
            }
            else
            {
                var sql = "select * from WO_InstallCompany";
                // var tb = DBHelper.GetTable(sql);
                tb = new ExecuteSql().ExecuteSQL(sql);
            }
            return tb;
        }

        public static DataTable GetByKeyword(string keyword) {
            DataTable tb;
            if (CommonUtils.IsNoProcedure)
            {
                tb = Public_ExecuteSqlService.Instance.NoStore_WOInstallCompany_GetByKeyword(keyword);
            }
            else
            {
                var sql = "select * from WO_InstallCompany  where CompanyName like @companyName";
                tb = new ExecuteSql().ExecuteSQL(sql, new QueryParameter[] {
                new QueryParameter("companyName", DataType.String, $"%{keyword}%")
                });
            }

            return tb;
        }

        public static string AddOne(string CompanyName) {
            if (CommonUtils.IsNoProcedure)
            {
                var rtn1 = WoCommonService.Instance.WO_InstallCompany_Add(CompanyName);
                return rtn1 == null ? null : rtn1.ToString();
            }
            //var sql = string.Format("WO_InstallCompany_Add {0}", SHelper.GetPara(CompanyName));
            //var rtn = DBHelper.ExecuteScalar(sql);
            var rtn = new ExecuteSql().ExecuteStoredProcedureScalar("WO_InstallCompany_Add", new QueryParameter[] {
                 new QueryParameter("CompanyName", DataType.String, CompanyName)
            });
            return rtn == null ? null : rtn.ToString();
        
        } 
    }
}

