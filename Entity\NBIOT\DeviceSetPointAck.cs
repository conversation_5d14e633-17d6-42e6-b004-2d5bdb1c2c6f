﻿using System.Collections.Generic;
using System.Text;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class DeviceSetPointAck
    {
        [Newtonsoft.Json.JsonProperty("DevID")]
        public string DeviceId { get; set; }

        List<string> _successIdList;
        [Newtonsoft.Json.JsonProperty("SuccessList")]
        public List<string> SuccessIds
        {
            get { return _successIdList; }
            set
            {
                _successIdList = value;
                SuccessIds2 = GetTSignalMeasurementIds(value);
            }
        }

        List<string> _failIds;
        [Newtonsoft.Json.JsonProperty("FailList")]
        public List<string> FailIds
        {
            get { return _failIds; }
            set
            {
                _failIds = value;
                FailIds2 =  GetTSignalMeasurementIds(value);
            }
        }

        [Newtonsoft.Json.JsonIgnore]
        public List<TSignalMeasurementId> SuccessIds2 { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public List<TSignalMeasurementId> FailIds2 { get; set; }

        /// <summary>
        /// 将成功和失败的SID列表拆分赋值给阳老师定义的成功/失败列表
        /// </summary>
        /// <param name="idList"></param>
        /// <returns></returns>
        public static List<TSignalMeasurementId> GetTSignalMeasurementIds(List<string> idList)
        {
            List<TSignalMeasurementId> signalList = new List<TSignalMeasurementId>();
            if (idList != null && idList.Count > 0)
            {
                foreach (string item in idList)
                {
                    TSignalMeasurementId id = new TSignalMeasurementId();
                    string[] itemStr = item.Split(new char[] { ':' });
                    id.ID = itemStr[0];
                    id.SignalNumber = itemStr[1];
                    signalList.Add(id);
                }
            }
            return signalList;
        }

        #region SiteWeb配置

        [Newtonsoft.Json.JsonIgnore]
        public int MyDeviceId { get; set; }

        #endregion

        public DeviceSetPointAck() { }

        public DeviceSetPointAck(string deviceId, List<TSignalMeasurementId> successIds, List<TSignalMeasurementId> failIds)
        {
            DeviceId = deviceId;

            SuccessIds2 = successIds;
            FailIds2 = failIds;
            SuccessIds = new List<string>();
            FailIds = new List<string>();
            if (SuccessIds2 != null && SuccessIds2.Count > 0)
            {
                foreach (TSignalMeasurementId tsmid in SuccessIds2)
                {
                    SuccessIds.Add(tsmid.ID + tsmid.SignalNumber);
                }
            }
            if (FailIds2 != null && FailIds2.Count > 0)
            {
                foreach (TSignalMeasurementId tsmid in FailIds2)
                {
                    FailIds.Add(tsmid.ID + tsmid.SignalNumber);
                }
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}", DeviceId);
            sb.AppendLine();

            sb.Append("SuccessList:");
            sb.AppendLine();
            foreach (TSignalMeasurementId item in SuccessIds2)
            {
                sb.AppendFormat("{0}", item.ToString());
                sb.AppendLine();
            }

            sb.Append("FailList:");
            sb.AppendLine();
            foreach (TSignalMeasurementId item in FailIds2)
            {
                sb.AppendFormat("{0}", item.ToString());
                sb.AppendLine();
            }
            return sb.ToString();
        }
    }
}