namespace BDSTool.Entity.S2
{
    using System;
    using System.Collections.Generic;
    
    public partial class TBL_Equipment
    {
        public int StationId { get; set; }
        public int EquipmentId { get; set; }
        public string EquipmentName { get; set; }
        public string EquipmentNo { get; set; }
        public string EquipmentModule { get; set; }
        public string EquipmentStyle { get; set; }
        public Nullable<int> AssetState { get; set; }
        public Nullable<double> Price { get; set; }
        public Nullable<double> UsedLimit { get; set; }
        public Nullable<System.DateTime> UsedDate { get; set; }
        public Nullable<System.DateTime> BuyDate { get; set; }
        public string Vendor { get; set; }
        public string Unit { get; set; }
        public int EquipmentCategory { get; set; }
        public int EquipmentType { get; set; }
        public Nullable<int> EquipmentClass { get; set; }
        public int EquipmentState { get; set; }
        public string EventExpression { get; set; }
        public Nullable<double> StartDelay { get; set; }
        public Nullable<double> EndDelay { get; set; }
        public string Property { get; set; }
        public string Description { get; set; }
        public Nullable<int> EquipmentTemplateId { get; set; }
        public Nullable<int> HouseId { get; set; }
        public int MonitorUnitId { get; set; }
        public Nullable<int> WorkStationId { get; set; }
        public int SamplerUnitId { get; set; }
        public int DisplayIndex { get; set; }
        public int ConnectState { get; set; }
        public System.DateTime UpdateTime { get; set; }
        public string ParentEquipmentId { get; set; }
        public string RatedCapacity { get; set; }
        public string InstalledModule { get; set; }
        public string ProjectName { get; set; }
        public string ContractNo { get; set; }
        public Nullable<System.DateTime> InstallTime { get; set; }
        public string EquipmentSN { get; set; }
        public string SO { get; set; }
    }
}
