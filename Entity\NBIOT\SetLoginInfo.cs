﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    //批量设置FSU注册信息
    public class SetLoginInfo : BMessage
    {
        public string UserName { get; set; }

        public string PassWord { get; set; }

        public SetLoginInfo() : base()
        {
            MessageType = (int)BMessageType.SET_LOGININFO;
        }

        public SetLoginInfo(string userName, string passWord) : base()
        {
            MessageType = (int)BMessageType.SET_LOGININFO;
            UserName = userName;
            PassWord = passWord;
        }

        public override string Serialize()
        {
            string result = string.Empty;
            try
            {
                JsonSerializerSettings settings = new JsonSerializerSettings();
                settings.ContractResolver = new CustomContractResolver();
                TIotEntity iotEntity = new TIotEntity(this);
                result = JsonConvert.SerializeObject(iotEntity, settings);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetLoginInfo.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetLoginInfo.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
            }
            return result;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0},{1}:{2},{3}", MessageId, (BMessageType)MessageType, UserName, PassWord);
            return sb.ToString();
        }
    }
}