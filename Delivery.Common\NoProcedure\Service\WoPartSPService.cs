﻿using System;
using System.Collections.Generic;
using System.Data;
using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;

namespace Delivery.Common.NoProcedure.Service
{
    public class WoPartSPService
    {
        private static WoPartSPService _instance = null;

        public static WoPartSPService Instance
        {
            get
            {
                if (_instance == null) _instance = new WoPartSPService();
                return _instance;
            }
        }
        /// <summary> 存储过程 WO_CR1_RemoveOrder 的实现 </summary>
        public string WoCR1RemoveOrder(int orderId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                try
                {
                    object orderStateObj = executeHelper.ExecuteScalar("CR1_RemoveOrder_GetOrderState", new Dictionary<string, object>() { { "OrderId", orderId } });
                    if(orderStateObj == null || orderStateObj == DBNull.Value)
                    {
                        return "工单不存在";
                    }
                    int orderState = int.Parse(orderStateObj.ToString());
                    if(orderState == 9)
                    {
                        return "工单已删除";
                    }
                    int newOrderState = 9;
                    string stateSetUserName = "系统管理员";
                    int stateSetUserId = -1;
                    string finalDecision = "作废";
                    int finalIsApprove = 0;
                    string finalGeneralReuslt = "";
                    string finalNote = "";
                    string isApproveString = "作废";
                    string flowText = DateTime.Now.ToStr() + "_作废" + isApproveString + "_审批人:" + stateSetUserName;
                    DateTime saveTime = DateTime.Now;
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("OrderId", orderId);
                    realParams.Add("OldOrderState", orderState);
                    realParams.Add("NewOrderState", newOrderState);
                    realParams.Add("StateSetUserId", stateSetUserId);
                    realParams.Add("StateSetUserName", stateSetUserName);
                    realParams.Add("FinalDecision", finalDecision);
                    realParams.Add("FinalIsApprove", finalIsApprove);
                    realParams.Add("FlowText", flowText);
                    realParams.Add("SaveTime", saveTime);
                    executeHelper.ExecuteNonQuery("CR1_RemoveOrder_InsertOrderFlow", realParams);

                    realParams.Add("FinalGeneralReuslt", finalGeneralReuslt);
                    realParams.Add("FinalNote", finalNote);
                    executeHelper.ExecuteNonQuery("CR1_RemoveOrder_UpdateTestOrder", realParams);

                    executeHelper.ExecuteNonQuery("CR1_RemoveOrder_InsertListHis", realParams);

                    executeHelper.ExecuteNonQuery("CR1_RemoveOrder_DelCheckList", realParams);

                    executeHelper.ExecuteNonQuery("CR1_RemoveOrder_DelCheckListSignal", realParams);

                    executeHelper.Commit();

                    return "OK";
                }
                catch (Exception ex)
                {
                    executeHelper.Rollback();
                    Logger.Log("WoPartSPService.WoCR1RemoveOrder throw Exception: ", LogType.Error);
                    Logger.Log(ex);
                    return "-1";//原逻辑中没有对异常作处理
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        /// <summary> 存储过程 WO_SubmitOnlineApply 的实现 </summary>
        public string WoSubmitOnlineApply(int orderId, int stateSetUserId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                try
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>
                    {
                        { "OrderId", orderId },
                        { "StateSetUserId", stateSetUserId }
                    };
                    int newOrderState = 2;
                    int isApprove = 1;
                    DateTime currentDateTime = DateTime.Now;
                    int? oldOrderState = null, applyUserId = null;
                    DataTable infoDt = executeHelper.ExecDataTable("SubmitOnlineApply_GetOldInfo", realParams);
                    if(infoDt != null && infoDt.Rows.Count >= 1)
                    {
                        oldOrderState = infoDt.Rows[0].Field<int?>("OrderState");
                        applyUserId = infoDt.Rows[0].Field<int?>("ApplyUserId");
                    }
                    if(applyUserId == null || stateSetUserId != applyUserId.Value)
                    {
                        return "非本人工单";
                    }
                    if (oldOrderState == null)
                    {
                        return "工单不存在";
                    }
                    if(oldOrderState.Value != 1)
                    {
                        return "工单非待入网申请状态";
                    }
                    object userNameObj = executeHelper.ExecuteScalar("SubmitOnlineApply_GetUserName", realParams);
                    string stateSetUserName = userNameObj == null || userNameObj == DBNull.Value ? "" : userNameObj.ToString();
                    string flowText = currentDateTime.ToStr() + "_提交专家组审核_提交人:" + stateSetUserName;

                    realParams.Add("OldOrderState", oldOrderState);
                    realParams.Add("NewOrderState", newOrderState);
                    realParams.Add("StateSetUserName", stateSetUserName);
                    realParams.Add("Decision", "");
                    realParams.Add("Note", "");
                    realParams.Add("IsApprove", isApprove);
                    realParams.Add("FlowText", flowText);
                    realParams.Add("CurrentDateTime", currentDateTime);
                    executeHelper.ExecuteNonQuery("SubmitOnlineApply_Insert", realParams);
                    executeHelper.ExecuteNonQuery("SubmitOnlineApply_Update", realParams);

                    executeHelper.Commit();
                    return "OK";
                }
                catch (Exception ex)
                {
                    executeHelper.Rollback();
                    Logger.Log("WoPartSPService.WoSubmitOnlineApply throw Exception: ", LogType.Error);
                    Logger.Log(ex);
                    return "-1"; 
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        /// <summary> 存储过程 WO_SubmitExpertDecision 的实现 </summary>
        public string SubmitExpertDecision(int orderId, int stateSetUserId, string expertDecision
                            , string expertNote, int expertIsApprove)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                try
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>
                    {
                        { "OrderId", orderId },
                        { "StateSetUserId", stateSetUserId },
                        { "ExpertDecision", expertDecision },
                        { "ExpertNote", expertNote },
                        { "ExpertIsApprove", expertIsApprove }
                    };
                    object oldOrderStateObj = executeHelper.ExecuteScalar("SubmitExpertDecision_GetOldOrderState", realParams);
                    int? oldOrderState = null;
                    if (oldOrderStateObj != null && oldOrderStateObj != DBNull.Value)
                    {
                        oldOrderState = int.Parse(oldOrderStateObj.ToString());
                    }
                    if (oldOrderState == null)
                    {
                        return "工单不存在";
                    }
                    if (oldOrderState != 2)
                    {
                        return "工单非待专家组审核状态";
                    }
                    object stateSetUserNameObj = executeHelper.ExecuteScalar("SubmitExpertDecision_GetStateSetUserName", realParams);
                    string stateSetUserName = stateSetUserNameObj == null || stateSetUserNameObj == DBNull.Value ? "" : stateSetUserNameObj.ToString();
                    DateTime saveTime = DateTime.Now;
                    int newOrderState;
                    string isApproveString;
                    if (expertIsApprove == 1)
                    {
                        newOrderState = 3;
                        isApproveString = "通过";
                    }
                    else
                    {
                        newOrderState = 1;
                        isApproveString = "退回";
                    }
                    string flowText = saveTime.ToStr() + "_专家组审核" + isApproveString + "_审批人" + stateSetUserName;

                    realParams.Add("OldOrderState", oldOrderState);
                    realParams.Add("NewOrderState", newOrderState);
                    realParams.Add("StateSetUserName", stateSetUserName);
                    realParams.Add("FlowText", flowText);
                    realParams.Add("SaveTime", saveTime);
                    executeHelper.ExecuteNonQuery("SubmitExpertDecision_Insert", realParams);
                    executeHelper.ExecuteNonQuery("SubmitExpertDecision_Update", realParams);

                    executeHelper.Commit();
                    return "OK";
                }
                catch (Exception ex)
                {
                    executeHelper.Rollback();
                    Logger.Log("WoPartSPService.SubmitExpertDecision throw Exception: ", LogType.Error);
                    Logger.Log(ex);
                    return "-1";
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        /// <summary> 存储过程 WO_SubmitFinalDecision_SyncSiteWeb 的实现 </summary>
        public string SubmitFinalDecisionSyncSiteWeb(int orderId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper);
                try
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>
                    {
                        { "OrderId", orderId }
                    };
                    DateTime currentDateTime = DateTime.Now;
                    object userId = DBNull.Value, stationId = DBNull.Value;
                    object latitude = null, longitude = null;

                    DataTable infoDt = executeHelper.ExecDataTable("SubmitFinalDecision_SyncSiteWeb_GetOldInfo", realParams);
                    if(infoDt != null && infoDt.Rows.Count >=1)
                    {
                        DataRow row = infoDt.Rows[0];
                        userId = CommonUtils.GetNullableValue(row.Field<int?>("ApplyUserId"));
                        stationId = CommonUtils.GetNullableValue(row.Field<int?>("StationId"));
                        latitude = CommonUtils.GetNullableValue(row.Field<decimal?>("Latitude"));
                        longitude = CommonUtils.GetNullableValue(row.Field<decimal?>("Longitude"));
                    }
                    if(stationId == DBNull.Value)
                    {
                        return "工单不存在";
                    }
                    realParams.Add("StationId", stationId);
                    object sCountObj = executeHelper.ExecuteScalar("SubmitFinalDecision_SyncSiteWeb_FindStationCount", realParams);
                    int sCount = sCountObj == null || sCountObj == DBNull.Value ? 0 : int.Parse(sCountObj.ToString());
                    if(sCount > 0)
                    {
                        realParams.Add("Longitude", longitude);
                        realParams.Add("Latitude", latitude);
                        executeHelper.ExecuteNonQuery("SubmitFinalDecision_SyncSiteWeb_UpdateStation", realParams);
                        PblConfigChangeLogService.Instance.DoExecute(stationId.ToString(), 1, 2);
                    }

                    DataTable existsEquipmentMaintainDt = executeHelper.ExecDataTable("SubmitFinalDecision_SyncSiteWeb_GetExistsEquipmentMaintain", realParams);
                    if(existsEquipmentMaintainDt != null && existsEquipmentMaintainDt.Rows.Count > 0)
                    {
                        foreach (DataRow row in existsEquipmentMaintainDt.Rows)
                        {
                            int? tmpEquipmentId = row.Field<int?>("EquipmentId");
                            if(tmpEquipmentId == null)
                            {
                                Logger.Log("tmpEquipmentId get Null. continue foreach");
                                continue;
                            }
                            Logger.Log("cancel engr state , eq = " + tmpEquipmentId);

                            CallableSPService.Instance.PblSaveEquipmentMaitain(stationId, tmpEquipmentId.Value, 1, null, null, userId, null, null);

                            string operationContent = "Delete Equipment Project Status Equipment id:" + stationId + "." + tmpEquipmentId + " Date:" + currentDateTime.ToStr();
                            CallableSPService.Instance.PamSaveOperationRecord(userId, stationId, 2, 73, operationContent);
                        }
                    }
                    return "OK";
                }
                catch (Exception ex)
                {
                    Logger.Log("WoPartSPService.SubmitFinalDecisionSyncSiteWeb throw Exception: ", LogType.Error);
                    Logger.Log(ex);
                    return "-1";
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
    }
}
