namespace BDSTool.Entity.B
{
    using System;
    using System.Collections.Generic;
    
    public partial class TSL_MonitorUnitCMCC
    {
        public int StationId { get; set; }
        public int MonitorUnitId { get; set; }
        public string FSUID { get; set; }
        public string FSUName { get; set; }
        public string SiteID { get; set; }
        public string RoomID { get; set; }
        public string UserName { get; set; }
        public string PassWord { get; set; }
        public string FSUIP { get; set; }
        public string FSUMAC { get; set; }
        public string FSUVER { get; set; }
        public Nullable<int> Result { get; set; }
        public string FailureCause { get; set; }
        public Nullable<double> CPUUsage { get; set; }
        public Nullable<double> MEMUsage { get; set; }
        public Nullable<int> GetFSUInfoResult { get; set; }
        public string GetFSUFaliureCause { get; set; }
        public Nullable<System.DateTime> GetFSUTime { get; set; }
        public string FTPUserName { get; set; }
        public string FTPPassWord { get; set; }
        public string ExtendField1 { get; set; }
        public string ExtendField2 { get; set; }
    }
}
