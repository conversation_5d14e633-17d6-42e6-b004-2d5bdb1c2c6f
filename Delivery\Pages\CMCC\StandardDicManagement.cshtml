﻿@page
@model Delivery.Pages.CMCC.StandardDicManagementModel
@{
}

@section Scripts {
    <script type="text/javascript" src="~/Scripts/lib/jquery-1.8.0.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="~/Scripts/common/ie8.js"></script>
    <script type="text/javascript" src="~/Scripts/common/common.js"></script>
    <script type="text/javascript" src="~/Scripts/common/datagird-filter.js"></script>
    <script type="text/javascript" src="~/CMCC/scripts/StandardDicManagement.js"></script>
}


<div class="mask">
    <div style="position: relative; top: 50%; left: 50%">
        <div class="loading">
            正在加载，请等待...
        </div>
    </div>
</div>
<div class="easyui-accordion">
    <div title="控制" style="width: 100%; padding: 5px;">
        <table id="tbl_cmd" class="easyui-datagrid" data-type="cmd"></table>
        <div id="toolbar_cmd" style="display: none;" data-grid="#tbl_cmd">
            <a id="btn_EditCmd" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls:'icon-edit',plain:true">编辑</a>
            <a id="btn_SaveCmd" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls:'icon-save',plain:true">保存</a>
            <a id="btn_CancelCmd" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls:'icon-undo',plain:true">取消</a>
        </div>
    </div>
    <div title="事件" style="width: 100%; padding: 5px;">
        <table id="tbl_event" class="easyui-datagrid" data-type="event"></table>
        <div id="toolbar_event" style="display: none;" data-grid="#tbl_event">
            <a id="btn_EditEvent" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls:'icon-edit',plain:true">编辑</a>
            <a id="btn_SaveEvent" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls:'icon-save',plain:true">保存</a>
            <a id="btn_CancelEvent" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls:'icon-undo',plain:true">取消</a>
        </div>
    </div>
    <div title="信号" style="width: 100%; padding: 5px;">
        <table id="tbl_signal" class="easyui-datagrid" data-type="signal"></table>
        <div id="toolbar_signal" style="display: none;" data-grid="#tbl_signal">
            <a id="btn_EditSignal" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls:'icon-edit',plain:true">编辑</a>
            <a id="btn_SaveSignal" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls:'icon-save',plain:true">保存</a>
            <a id="btn_CancelSignal" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls:'icon-undo',plain:true">取消</a>
        </div>
    </div>
</div>