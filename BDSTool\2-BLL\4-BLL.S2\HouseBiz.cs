﻿

using BDSTool.DBUtility;
using BDSTool.DBUtility.Common;
using Common.Logging.Pro;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.BLL.S2
{
    public partial class HouseBiz
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");
        public static bool ExistAnyHouse(int StationID) {
            try {
                string rtn = null;
                if (CommonUtils.IsNoProcedure)
                {
                    rtn = Public_ExecuteSqlService.Instance.NoStore_TBLHouse_GetByStationId(StationID);
                }
                else
                {
                    var sql = string.Format(@"SELECT 1 FROM TBL_House WHERE  StationId={0}", StationID);
                    rtn = DBHelper.ExecuteScalar(sql);
                }
                if (string.IsNullOrEmpty(rtn))
                    return false;
            }
            catch (Exception ex) {
                logger.ErrorFormat("ExistAnyHouse();Error={0}", ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }

            return true;
        }

        public static bool GetHouseIdByRoomName(int StationId, string RoomName, ref int HouseId) {
            try {
                //to do
                string rtn = null;
                if (CommonUtils.IsNoProcedure)
                {
                    rtn = Public_ExecuteSqlService.Instance.NoStore_TBLHouse_GetHouseIdByStationIdAndHouseName(StationId, SHelper.GetPara(RoomName));
                }
                else
                {
                    var sql = string.Format(@"SELECT HouseId FROM TBL_House WHERE StationId={0} and HouseName={1}",
                    StationId, SHelper.GetPara(RoomName));
                    rtn = DBHelper.ExecuteScalar(sql);
                }
                if (string.IsNullOrEmpty(rtn))
                    return false;

                HouseId = int.Parse(rtn.ToString());
            }
            catch (Exception ex) {
                logger.ErrorFormat("GetHouseIdByRoomName();StationId={0},RoomName={1};Error={2}", StationId, RoomName, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }


        public static List<string> GetAllHouseName(int StationId) {
            try {
                var allHouseName = new List<string>();
                DataTable table = null;
                if (CommonUtils.IsNoProcedure)
                {
                    table = Public_ExecuteSqlService.Instance.NoStore_TBLHouse_GetHouseNameByStationId(StationId);
                }
                else 
                {
                    var sql = string.Format(@"SELECT HouseName  FROM TBL_House WHERE StationId={0}", StationId);
                    table = DBHelper.GetTable(sql);
                }
                if (table != null) {
                    foreach (DataRow row in table.Rows) {
                        var house = row[0].ToString();
                        allHouseName.Add(house);
                    }
                }
                return allHouseName;
            }
            catch (Exception ex) {
                logger.ErrorFormat("GetAllHouseName();StationId={0},;Error={1}", StationId,  ex.Message);
                logger.Error(ex.StackTrace);
                return null;
            }
           
        }
        //public static bool GetTopOneHouseId(int StationId, ref int HouseId) {
        //    try {
        //        var sql = string.Format(@"SELECT top 1 HouseId FROM TBL_House WHERE  StationId={0}", StationId);

        //        var rtn = DBHelper.ExecuteScalar(sql);
        //        if (string.IsNullOrEmpty(rtn))
        //            return false;
        //        HouseId = int.Parse(rtn);
        //    }
        //    catch (Exception ex) {
        //        logger.ErrorFormat("ExistAnyHouse();Error={0}", ex.Message);
        //        logger.Error(ex.StackTrace);
        //        return false;
        //    }

        //    return true;
        //}
    }
}
