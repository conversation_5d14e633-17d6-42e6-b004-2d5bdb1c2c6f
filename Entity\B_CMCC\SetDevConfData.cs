﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.6.18.	写动环设备的配置数据
    /// </summary>
    public sealed class SetDevConfData: BMessage
    {

        public List<TDevConf> Values { get; private set; }

        public SetDevConfData(string fsuId, List<TDevConf> devConfList):base()
        {
            MessageType = (int)BMessageType.SET_DEV_CONF_DATA;

            FSUID = fsuId;
            Values = devConfList;

        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmlDoc = CreateXmlDocument();
                AppendPK_Type(xmlDoc, "Request", BMessageType.SET_DEV_CONF_DATA.ToString());

                XmlElement xe2 = xmlDoc.CreateElement("Info");
                XmlElement xe21 = xmlDoc.CreateElement("FSUID");
                xe21.InnerText = FSUID;
                xe2.AppendChild(xe21);


                XmlElement xe22 = xmlDoc.CreateElement("Values");
                //XmlElement xe221 = xmlDoc.CreateElement("DeviceList");
                foreach (TDevConf devConf in Values)
                {
                    XmlElement xeDevice = xmlDoc.CreateElement("Device");
                    xeDevice.SetAttribute("DeviceID", devConf.DeviceID);
                    xeDevice.SetAttribute("DeviceName", devConf.DeviceName);

                    xeDevice.SetAttribute("SiteID", devConf.SiteID);
                    xeDevice.SetAttribute("RoomID", devConf.RoomID);

                    xeDevice.SetAttribute("SiteName", devConf.SiteName);
                    xeDevice.SetAttribute("RoomName", devConf.RoomName);
                    xeDevice.SetAttribute("DeviceType", devConf.DeviceType.ToString());
                    xeDevice.SetAttribute("DeviceSubType", devConf.DeviceSubType.ToString());
                    xeDevice.SetAttribute("Model", devConf.Model);
                    xeDevice.SetAttribute("Brand", devConf.Brand);
                    xeDevice.SetAttribute("RatedCapacity", devConf.RatedCapacity.ToString());
                    xeDevice.SetAttribute("Version", devConf.Version);
                    xeDevice.SetAttribute("BeginRunTime", 
                        devConf.BeginRunTime.HasValue?Convert.ToDateTime(devConf.BeginRunTime).ToString("yyyy-MM-dd HH:mm:ss"):"");
                    xeDevice.SetAttribute("DevDescribe", devConf.DevDescribe);
                    xeDevice.SetAttribute("ConfRemark", devConf.ConfRemark);
                    XmlElement xeSignals = xmlDoc.CreateElement("Signals");
                    xeSignals.SetAttribute("Count", devConf.Signals.Count.ToString());
                    foreach (TSignal signal in devConf.Signals)
                    {
                        XmlElement xeSignal = xmlDoc.CreateElement("Signal");
                        xeSignal.SetAttribute("Type", ((int)signal.Type).ToString());
                        xeSignal.SetAttribute("ID", signal.ID);
                        xeSignal.SetAttribute("SignalName", signal.SignalName);
                        xeSignal.SetAttribute("AlarmLevel", ((int)signal.AlarmLevel).ToString());
                        xeSignal.SetAttribute("Threshold", signal.Threshold.ToString());
                        //xeSignal.SetAttribute("AbsoluteVal", signal.AbsoluteVal.ToString());
                        //xeSignal.SetAttribute("RelativeVal", signal.RelativeVal.ToString());
                        //xeSignal.SetAttribute("Describe", signal.Describe);
                        xeSignal.SetAttribute("NMAlarmID", signal.NMAlarmID);
                        xeSignal.SetAttribute("SignalNumber", signal.SignalNumber);
                        xeSignals.AppendChild(xeSignal);
                    }
                    xeDevice.AppendChild(xeSignals);
                    //xe221.AppendChild(xeDevice);
                    xe22.AppendChild(xeDevice);
                }
                //xe22.AppendChild(xe221);
                xe2.AppendChild(xe22);

                XmlNode root = xmlDoc.SelectSingleNode("Request");
                root.AppendChild(xe2);
                StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("SetDevConfData.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmlDoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetDevConfData.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetDevConfData.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            string str = String.Format("{0}, {1}, {2}:",
                MessageId, (BMessageType)MessageType, FSUID);
            sb.Append(str);

            foreach (TDevConf devConf in Values)
            {
                sb.AppendFormat("[{0}]", devConf.ToString());
            }
            return sb.ToString();
        }

    }
}
