﻿'use strict';

var STATION_HANDLER = 'api/Station',
    HOUSE_HANDLER = 'api/House',
    FSU_HANDLER = 'api/Fsu',
    DEVICE_HANDLER = 'api/Device',
    WORKFLOW_HANDLER = 'api/Workflow',
    GET_USEROLE_HANDLER = '../api/UserRole',
    GET_STANDARDDOC_HANDLER = '../api/StandardDoc',
    SAVE_STANDARDDOC_HANDLER = '../api/StandardDoc',
    GET_DATADIC_HANDLER = '../api/GetDataDic',
    GET_NAVIGATIONINFO_HANDLER = '../api/GetNavigationInfo';

var getParams = function (params) {
    return Object.keys(params).reduce((acc, cur) => {
        if (cur !== 'action') {
            acc[cur] = params[cur];
        }
        return acc;
    }, {});
};

//生成guid
var guid = function () {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0,
            v = c === 'x' ? r : r & 0x3 | 0x8;
        return v.toString(16);
    });
};

//Object扩展
if (!Object.values)
    Object.values = function (obj) {
        if (obj !== Object(obj))
            throw new TypeError('Object.values called on a non-object');
        var val = [], key;
        for (key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                val.push(obj[key]);
            }
        }
        return val;
    };

//String扩展
if (!String.prototype.lpad)
    String.prototype.lpad = function (len, s) {
        var a = new Array(this);
        var n = len - this.length;
        for (var i = 0; i < n; i++) {
            a.unshift(s);
        }
        return a.join("");
    };

String.prototype.trim = function (pattern) {
    return this.replace(pattern, "");
};

//Array扩展
if (!Array.prototype.includes) {
    Array.prototype.includes = function (searchElement, fromIndex) {
        if (!this) {
            throw new TypeError('"this" is null or not defined');
        }
        var o = Object(this);
        var len = o.length >>> 0;
        if (len === 0) {
            return false;
        }
        var n = fromIndex | 0;
        var k = Math.max(n >= 0 ? n : len - Math.abs(n), 0);

        function sameValueZero(x, y) {
            return x === y || typeof x === 'number' && typeof y === 'number' && isNaN(x) && isNaN(y);
        }
        while (k < len) {
            if (sameValueZero(o[k], searchElement)) {
                return true;
            }
            k++;
        }

        return false;
    };
}

//清除cookie
var clearAllCookie = function () {
    document.cookie = 'userinfo=;expires=' + new Date(0).toUTCString() + ';path=/';
};

//清除cookie重新登陆
var reload = function () {
    clearAllCookie();
    window.location.reload();
};

//获取cookie对象
var getCookie = function () {
    var obj;
    var cookies = document.cookie ? document.cookie.split('; ') : [];
    if (cookies.length > 0) {
        obj = {};
        for (var i = 0; i < cookies.length; i++) {
            var arr = cookies[i].split('=');
            var name = arr.shift();
            //var arr1 = arr.join('=').split('&');
            var arr1 = decodeURIComponent(arr.join('=')).split('&');
            obj[name] = {};
            for (var j = 0; j < arr1.length; j++) {
                var key = arr1[j].split('=')[0];
                var value = arr1[j].split('=')[1];
                obj[name][key] = value ? decodeURIComponent(value) : value;
            }
        }
    }
    return obj;
};

//获取角色身份
var getUserRole = function () {
    var userRole;
    $.ajax({
        type: "GET",
        contentType: "application/x-www-form-urlencoded",
        url: GET_USEROLE_HANDLER,
        async: false,
        success: function (data) {
            userRole = decodeURI(data);
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
    return userRole;
};

//获取url的GET参数
var getQueryString = function (name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var r = window.location.search.substr(1).match(reg);  //获取url中"?"符后的字符串并正则匹配
    var context = "";
    if (r)
        context = r[2];
    return !context ? "" : context;
};

//获取上层url路径
var getBasePath = function () {
    var strFullPath = window.document.location.href;
    var strPath = window.document.location.pathname;
    var pos = strFullPath.indexOf(strPath);
    var prePath = strFullPath.substring(0, pos);
    var postPath = strPath.substring(0, strPath.substr(1).indexOf('/') + 1) + "/";
    return prePath + postPath;
};

Date.prototype.format = function (format) {
    var date = {
        "M+": this.getMonth() + 1,
        "d+": this.getDate(),
        "h+": this.getHours(),
        "m+": this.getMinutes(),
        "s+": this.getSeconds(),
        "q+": Math.floor((this.getMonth() + 3) / 3),
        "S+": this.getMilliseconds()
    };
    if (/(y+)/i.test(format)) {
        format = format.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    for (var k in date) {
        if (new RegExp("(" + k + ")").test(format)) {
            format = format.replace(RegExp.$1, RegExp.$1.length === 1
                   ? date[k] : ("00" + date[k]).substr(("" + date[k]).length));
        }
    }
    return format;
};

$.extend($.fn.validatebox.defaults.rules, {
    date: {
        validator: function (value) {
            //格式yyyy-MM-dd或yyyy-M-d
            return /^(?:(?!0000)[0-9]{4}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)-02-29) (?:[01]\d|2[0-3])(?::[0-5]\d){2}$/i.test(value);// (?:[01]\d|2[0-3])(?::[0-5]\d){2}
        },
        message: '请输入合适的日期(格式: {0}).'
    },
    greater: {
        validator: function (value, param) {
            if ($(param[0]).datebox('getValue')) {
                var d1 = $.fn.datebox.defaults.parser($(param[0]).datebox('getValue'));
                var d2 = $.fn.datebox.defaults.parser(value);
                //$(param[0]).datebox('validate');

                return d2 >= d1;
            }
            return true;
        },
        message: '输入时间点需大于等于起始时间.'
    },
    less: {
        validator: function (value, param) {
            if ($(param[0]).val()) {
                var d1 = $.fn.datebox.defaults.parser($(param[0]).val());
                var d2 = $.fn.datebox.defaults.parser(value);
                return d2 <= d1;
            }
            return true;
        },
        message: '输入时间点需小于等于结束时间.'
    },
    maxLength: {
        validator: function (value, param) {
            return value.length <= param[0];
        },
        message: "字符长度不能超过{0}"
    }
});

//获取datagrid编辑行
$.extend($.fn.datagrid.methods, {
    getEditingRowIndexs: function (jq) {
        var rows = $.data(jq[0], "datagrid").panel.find('.datagrid-row-editing');
        var indexs = [];
        rows.each(function (i, row) {
            var index = row.sectionRowIndex;
            if (indexs.indexOf(index) === -1) {
                indexs.push(index);
            }
        });
        return indexs;
    }
});

//datagrid客户端分页器
var pagerFilter = function (data) {
    if (typeof data.length === 'number' && typeof data.splice === 'function') {    // 判断数据是否是数组
        data = {
            total: data.length,
            rows: data
        };
    }
    var dg = $(this);
    var opts = dg.datagrid('options');
    var pager = dg.datagrid('getPager');
    pager.pagination({
        onSelectPage: function (pageNum, pageSize) {
            opts.pageNumber = pageNum;
            opts.pageSize = pageSize;
            pager.pagination('refresh', {
                pageNumber: pageNum,
                pageSize: pageSize
            });
            dg.datagrid('loadData', data);
        }
    });
    if (!data.originalRows) {
        data.originalRows = data.rows;
    }
    var start = (opts.pageNumber - 1) * parseInt(opts.pageSize);
    var end = start + parseInt(opts.pageSize);
    data.rows = data.originalRows.slice(start, end);
    return data;
};

//提示框
var alertInfo = function (msg, icon, func) {
    var obj = { title: '提示' };
    obj.msg = msg;
    obj.icon = icon ? icon : 'info';
    if (func)
        obj.onClose = func;
    else if (msg === '请重新登陆！')
        obj.onClose = reload;
    $.messager.alert(obj);
    //if (icon === 'error')
        //console.log(msg);
};

//基础数据下拉框
var loadDicDropControl = function (control, entryId, parentEntryId, parentItemId) {
    $.ajax({
        type: 'GET',
        url: GET_DATADIC_HANDLER,
        contentType: "application/x-www-form-urlencoded",
        data: { entryId: entryId, parentEntryId: parentEntryId, parentItemId: parentItemId },
        async: false,
        success: function (data) {
            if (data) {
                var obj = JSON.parse(data);
                obj = obj.map(function (item) {
                    return {
                        ItemId: item.ItemId || item.itemid || item.itemId,
                        ItemValue: item.ItemValue || item.itemvalue || item.itemValue
                    };
                });
                control.combobox({ disabled: false, editable: false, valueField: 'ItemId', textField: 'ItemValue', data: obj });
                if (Array.isArray(obj) && obj.length > 0 && obj[0] && obj[0].ItemId != null) {
                    control.combobox('select', obj[0].ItemId);
                }
            }
            else
                alertInfo('出现错误！', 'error');
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
};
var loadDicUiDropControl = function (control, typeId, itemId) {
    $.ajax({
        type: 'GET',
        url: GET_DATADIC_HANDLER,
        contentType: "application/x-www-form-urlencoded",
        async: false,
        data: { typeId: typeId, itemId: itemId },
        success: function (data) {
            if (data) {
                var obj = JSON.parse(data);
                obj = obj.map(function (item) {
                    return {
                        ItemId: item.ItemId || item.itemid || item.itemId,
                        ItemValue: item.ItemValue || item.itemvalue || item.itemValue
                    };
                });
                control.combobox({ disabled: false, editable: false, valueField: 'ItemId', textField: 'ItemValue', data: obj });
                if (Array.isArray(obj) && obj.length > 0 && obj[0] && obj[0].ItemId != null) {
                    control.combobox('select', obj[0].ItemId);
                }
            }
            else
                alertInfo('出现错误！', 'error');
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
};

//刷新父页面导航-资源管理
var refreshNeedApproveResource = function () {
    $.ajax({
        type: "GET",
        contentType: "application/x-www-form-urlencoded",
        url: GET_NAVIGATIONINFO_HANDLER,
        success: function (data) {
            var obj = JSON.parse(data);
            if (obj.StationCount === '0' && obj.HouseCount === '0' && obj.FsuCount === '0')
                parent.$('#daibanul').children('li').eq(0).hide();
            else {
                if (obj.StationCount !== '0') {
                    parent.$('#stationToDo').next().html(obj.StationCount);
                    parent.$('#stationToDo').parentsUntil('ul').last().show();
                }
                else
                    parent.$('#stationToDo').parentsUntil('ul').last().hide();
                if (obj.HouseCount !== '0') {
                    parent.$('#houseToDo').next().html(obj.HouseCount);
                    parent.$('#houseToDo').parentsUntil('ul').last().show();
                }
                else
                    parent.$('#houseToDo').parentsUntil('ul').last().hide();
                if (obj.FsuCount !== '0') {
                    parent.$('#fsuToDo').next().html(obj.FsuCount);
                    parent.$('#fsuToDo').parentsUntil('ul').last().show();
                }
                else {
                    parent.$('#fsuToDo').parentsUntil('ul').last().hide();
                }
                parent.$('#daibanul').children('li').eq(0).show();
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            console.log('刷新导航栏出错！');
        }
    });
};

//刷新父页面导航-割接管理
var updateNeedApproveOrder = function () {
    $.ajax({
        type: "GET",
        contentType: "application/json; charset=UTF-8",
        url: '../api/OrderList/' + cookie['userinfo']['UserId'],
        success: function (data) {
            if (data && data.length > 0) {
                var arr = [];
                for (var i = 0; i < data.length; i++) {
                    arr.push(data[i].OrderState);
                }
                if (arr.indexOf(1) < 0)
                    parent.$('#applyToDo').parentsUntil('ul').last().hide();
                if (arr.indexOf(2) < 0)
                    parent.$('#expertToDo').parentsUntil('ul').last().hide();
                if (arr.indexOf(3) < 0)
                    parent.$('#reviewToDo').parentsUntil('ul').last().hide();
                for (var j = 0; j < data.length; j++) {
                    if (data[j].OrderState === 1) {
                        if (data[j].OrderCount) {
                            parent.$('#applyToDo').parentsUntil('ul').last().show();
                            parent.$('#applyToDo').next().html(data[j].OrderCount);
                        } else
                            parent.$('#applyToDo').parentsUntil('ul').last().hide();
                    } else if (data[j].OrderState === 2) {
                        if (data[j].OrderCount) {
                            parent.$('#expertToDo').parentsUntil('ul').last().show();
                            parent.$('#expertToDo').next().html(data[j].OrderCount);
                        } else
                            parent.$('#expertToDo').parentsUntil('ul').last().hide();
                    } else if (data[j].OrderState === 3) {
                        if (data[j].OrderCount) {
                            parent.$('#reviewToDo').parentsUntil('ul').last().show();
                            parent.$('#reviewToDo').next().html(data[j].OrderCount);
                        } else
                            parent.$('#reviewToDo').parentsUntil('ul').last().hide();
                    }
                }
                parent.$('#daibanul').children('li').eq(1).show();
            } else
                parent.$('#daibanul').children('li').eq(1).hide();
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
};

//获取分组信息
var getGroupInfo = function () {
    var groupInfo;
    $.ajax({
        type: 'GET',
        contentType: 'application/x-www-form-urlencoded',
        url: `${STATION_HANDLER}/getGroupInfo`,
        //data: { action: 'getGroupInfo' },
        async: false,
        success: function (data) {
            groupInfo = data;
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
    return groupInfo;
};
