﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Collections.Concurrent;
using Common.Logging.Pro;
using BDSTool.Entity.S2;
using BDSTool.DBUtility.Common;
using BDSTool.DBUtility;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace BDSTool.BLL.S2
{
    public class StandardDicBiz
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");
        public static Dictionary<int, string> LoadStandardDic() {
            var dicStandard =new Dictionary<int, string>();
            
            try {
                DataRowCollection table;
                
                if (CommonUtils.IsNoProcedure)
                {
                    DataTable res = Public_ExecuteSqlService.Instance.NoStore_GetTablesFromTwoTables();
                    table = res.Rows;
                }
                else {
                    var sql = @"SELECT StandardDicId, Description FROM TBL_StandardDicSig WHERE StandardType=1 AND Description IS NOT NULL  AND Description<>''
                                UNION ALL 
                                SELECT StandardDicId, Description FROM TBL_StandardDicControl WHERE StandardType=1 AND Description IS NOT NULL  AND Description<>''";
                
                    table = DBHelper.GetTable(sql).Rows;
                }

                if (table.Count == 0) {
                    logger.WarnFormat("LoadStandardDic(); 无数据");
                }
                else {
                    foreach (DataRow row in table) {
                        var sid = SHelper.ToInt(row["StandardDicId"]);
                        var describe = row["Description"].ToString();
                        dicStandard.Add(sid, describe);
                    }
                }
                logger.InfoFormat("LoadStandardDic(); Count={0}", dicStandard.Count);

            }
            catch (Exception ex) {
                logger.ErrorFormat("LoadStandardDic();Error={0}", ex.Message);
                logger.Error(ex.StackTrace);
            }
            return dicStandard;
        }

    }
}
