﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class SetAlarmPropertyAck : BMessage
    {
        public EnumResult Result { get; set; }

        public SetAlarmPropertyAck()
            : base()
        {
            MessageType = (int)BMessageType.SET_AlarmProperty_ACK;
        }

        public SetAlarmPropertyAck(string suid, string surid, EnumResult result)
            : base()
        {
            MessageType = (int)BMessageType.SET_AlarmProperty_ACK;
            SUId = suid;
            SURId = surid;
            Result = result;
        }

        public static SetAlarmPropertyAck Deserialize(XmlDocument xmldoc)
        {
            SetAlarmPropertyAck setAlarmPropertyAck = null;
            try
            {
                string suid = xmldoc.SelectSingleNode("/Response/Info/SUId").InnerText.Trim();
                string surid = xmldoc.SelectSingleNode("/Response/Info/SURId").InnerText.Trim();
                string result = xmldoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim();
                setAlarmPropertyAck = new SetAlarmPropertyAck(suid, surid, string.IsNullOrEmpty(result) ? EnumResult.FAILURE : (EnumResult)int.Parse(result));
                entityLogger.DebugFormat("SetAlarmPropertyAck.Deserialize(),xml:\r\n{0}", FormatXml(xmldoc.InnerXml));
                setAlarmPropertyAck.StringXML = xmldoc.InnerXml;
                return setAlarmPropertyAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetAlarmPropertyAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetAlarmPropertyAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmldoc.InnerXml));
                setAlarmPropertyAck = new SetAlarmPropertyAck();
                setAlarmPropertyAck.ErrorMsg = ex.Message;
                setAlarmPropertyAck.StringXML = xmldoc.InnerXml;
                return setAlarmPropertyAck;
            }
        }

        public override string ToString()
        {
            return String.Format("{0}, {1},{2},{3}, {4}: {5}",
                MessageId, (BMessageType)MessageType, MessageType, SUId, SURId, Result);
        }


    }
}
