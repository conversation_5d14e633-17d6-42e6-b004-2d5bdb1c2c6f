﻿
using DM.TestOrder.Common.DBA;


using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using DM.TestOrder.Common;
using DM.TestOrder.Entity;

namespace DM.TestOrder.DAL.Helper
{
    public class TBL_EquipmentHelper
    {
        public static TBL_Equipment FromDataRow(DataRow row) {
            var e = new TBL_Equipment();
            try {
                e.StationId = SHelper.ToInt(row["StationId"]);
                e.EquipmentId =  SHelper.ToInt(row["EquipmentId"]);
                e.EquipmentName = row["EquipmentName"].ToString();
                e.EquipmentCategory = SHelper.ToInt(row["EquipmentCategory"]);
                e.Vendor = row["Vendor"].ToString();
                e.EquipmentStyle = row["EquipmentStyle"].ToString();

                //maybe useless e.EquipmentNo = row["EquipmentNo"].ToString();
            }
            catch (Exception ex) {
                MyLogger.ErrorFormat("TBL_EquipmentHelper.FromDataRow();error={0}", ex.Message);
                MyLogger.Error(ex.StackTrace);
                return null;
            }
            return e;
        }
    }
}
