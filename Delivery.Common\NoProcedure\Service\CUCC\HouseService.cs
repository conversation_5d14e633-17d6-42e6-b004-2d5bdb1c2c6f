﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.AccessProvider;

namespace Delivery.Common.NoProcedure.Service.CUCC
{
   public class HouseService
    {
        private static HouseService _instance = null;

        public static HouseService Instance
        {
            get
            {
                if (_instance == null) _instance = new HouseService();
                return _instance;
            }
        }
        public DataTable SP_Get_WRHouseCUCC(string StructureId, string StationCode, string StartTime, string EndTime, string StationName, string HouseName, string HouseCode, string Status, string LogonId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                realParams.Add("LogonId", LogonId);
                try
                {
                    string WhereTime;string WhereStructure;string WhereStationName;
                    string WhereSWStationName;string WhereStationCode;string WhereHouseName;
                    string WhereSWHouseName;string WhereHouseCode;string WhereStatus;
                    int? iUserId = null;
                    DateTime endTime = string.IsNullOrEmpty(EndTime)? DateTime.Now: DateTime.Parse(EndTime);
                    DateTime startTime = string.IsNullOrEmpty(StartTime)? endTime.AddMonths(-1) : DateTime.Parse(StartTime);
                    
                    if (startTime.CompareTo(endTime) > 0)
                    {
                        return null;
                    }
                    WhereTime = "and a.ApplyTime >= '" + startTime.ToStr()
                        + "' and a.ApplyTime <='" + endTime.ToStr() + "'";
                    realParams.Add("WhereTime", WhereTime);
                    WhereStructure = "";
                    if(StructureId != null && StructureId != "-1")
                    {
                        WhereStructure = " and e.LevelPath LIKE '%" + StructureId.ToString() + "%'";
                        realParams.Add("WhereStructure", WhereStructure);
                    }
                    WhereStationName = "";
                    if (StationName != null && (StationName.TrimStart()).TrimEnd()!= "" )
                    {
                        WhereStationName = " and b.StationName like '%" + StationName + "%'";
                        realParams.Add("WhereStationName", WhereStationName);
                    }
                    WhereSWStationName = "";
                    if (StationName != null && (StationName.TrimStart()).TrimEnd() != "")
                    {
                        WhereSWStationName = " and f.StationName like '%" + StationName + "%'";
                        realParams.Add("WhereSWStationName", WhereSWStationName);
                    }
                    WhereStationCode = "";
                    if (StationCode != null && (StationCode.TrimStart()).TrimEnd() != "")
                    {
                        WhereStationCode = " and b.StationCode like '%" + StationCode + "%'";
                        realParams.Add("WhereStationCode", WhereStationCode);
                    }
                    WhereHouseName = "";
                    if (HouseName != null && (HouseName.TrimStart()).TrimEnd() != "")
                    {
                        WhereHouseName = " and a.HouseName like '%" + HouseName + "%'";
                        realParams.Add("WhereHouseName", WhereHouseName);
                    }
                    WhereSWHouseName = "";
                    if (HouseName != null && (HouseName.TrimStart()).TrimEnd() != "")
                    {
                        WhereSWHouseName = " and g.HouseName like '%" + HouseName + "%'";
                        realParams.Add("WhereSWHouseName", WhereSWHouseName);
                    }

                    WhereHouseCode = "";
                    if (HouseCode != null && (HouseCode.TrimStart()).TrimEnd() != "")
                    {
                        WhereHouseCode = " and a.HouseCode like '%" + HouseCode + "%'";
                        realParams.Add("WhereHouseCode", WhereHouseCode);
                    }

                    WhereStatus = "";
                    if(!string.IsNullOrEmpty(Status) && Status != "-1")
                    {
                        WhereStatus = " and a.HouseStatus =" + Status.ToString();
                        realParams.Add("WhereStatus", WhereStatus);
                    }
                    DataTable dt = executeHelper.ExecDataTable("SP_Get_WRHouseCUCC_iUserId", realParams);
                    if(dt!=null && dt.Rows.Count > 0)
                    {
                        iUserId = (int)dt.Rows[0]["iUserId"];  
                    }
                    realParams.Add("iUserId", iUserId);
                    object xFlag = executeHelper.ExecuteScalar("SP_Get_WRHouseCUCC_X", realParams);
                    string FilterUser;
                    if (xFlag!=null && xFlag!= DBNull.Value)
                    {
                        FilterUser = XmlScriptProvider.Current.GetCommandSql("SP_Get_WRHouseCUCC_FilterUser", realParams);
                    } else
                    {
                        FilterUser = XmlScriptProvider.Current.GetCommandSql("SP_Get_WRHouseCUCC_FilterUser2", realParams);
                    }
                    realParams.Add("FilterUser", FilterUser);
                    DataTable result1 = executeHelper.ExecDataTable("SP_Get_WRHouseCUCC_Result", realParams);
                    DataTable result2 = executeHelper.ExecDataTable("SP_Get_WRHouseCUCC_Result2", realParams);
                    result1.Merge(result2);
                    if(result1.Rows.Count > 0)
                    {
                        result1.DefaultView.Sort = "ApplyTime DESC";
                        result1 = result1.DefaultView.ToTable();
                        int rowNumber = 1;
                        foreach(DataRow row in result1.Rows)
                        {
                            row["RowNumber"] =rowNumber++;
                        }
                    }
                    return result1;

                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    return new DataTable();
                }finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
        public DataTable SP_Get_WRHouseConditionCUCC(string StructureId, string StationCode, string StartTime, string EndTime, string StationName, string HouseName, string HouseCode, string Status, string LogonId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper,false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                realParams.Add("LogonId", LogonId);
                try
                {
                    string WhereTime; string WhereStructure; string WhereStationName;
                    string WhereSWStationName; string WhereStationCode; string WhereHouseName;
                    string WhereSWHouseName; string WhereHouseCode;
                    DateTime endTime = string.IsNullOrEmpty(EndTime) ? DateTime.Now : DateTime.Parse(EndTime);
                    DateTime startTime = string.IsNullOrEmpty(StartTime) ? endTime.AddMonths(-1) : DateTime.Parse(StartTime);

                    if (startTime.CompareTo(endTime) > 0)
                    {
                        return new DataTable();
                    }
                    WhereTime = "and a.ApplyTime >= '" + startTime.ToStr()
                        + "' and a.ApplyTime <='" + endTime.ToStr() + "'";
                    realParams.Add("WhereTime", WhereTime);
                    WhereStructure = "";
                    if (StructureId != null && StructureId != "-1")
                    {
                        WhereStructure = " and e.LevelPath LIKE '%" + StructureId.ToString() + "%'";
                        realParams.Add("WhereStructure", WhereStructure);
                    }
                    WhereStationName = "";
                    if (StationName != null && (StationName.TrimStart()).TrimEnd() != "")
                    {
                        WhereStationName = " and b.StationName like '%" + StationName + "%'";
                        realParams.Add("WhereStationName", WhereStationName);
                    }
                    WhereSWStationName = "";
                    if (StationName != null && (StationName.TrimStart()).TrimEnd() != "")
                    {
                        WhereSWStationName = " and f.StationName like '%" + StationName + "%'";
                        realParams.Add("WhereSWStationName", WhereSWStationName);
                    }
                    WhereStationCode = "";
                    if (StationCode != null && (StationCode.TrimStart()).TrimEnd() != "")
                    {
                        WhereStationCode = " and b.StationCode like '%" + StationCode + "%'";
                        realParams.Add("WhereStationCode", WhereStationCode);
                    }
                    WhereHouseName = "";
                    if (HouseName != null && (HouseName.TrimStart()).TrimEnd() != "")
                    {
                        WhereHouseName = " and a.HouseName like '%" + HouseName + "%'";
                        realParams.Add("WhereHouseName", WhereHouseName);
                    }
                    WhereSWHouseName = "";
                    if (HouseName != null && (HouseName.TrimStart()).TrimEnd() != "")
                    {
                        WhereSWHouseName = " and g.HouseName like '%" + HouseName + "%'";
                        realParams.Add("WhereSWHouseName", WhereSWHouseName);
                    }

                    WhereHouseCode = "";
                    if (HouseCode != null && (HouseCode.TrimStart()).TrimEnd() != "")
                    {
                        WhereHouseCode = " and a.HouseCode like '%" + HouseCode + "%'";
                        realParams.Add("WhereHouseCode", WhereHouseCode);
                    } 
                    DataTable result1 = executeHelper.ExecDataTable("SP_Get_WRHouseConditionCUCC_Result", realParams);
                    DataTable result2 = executeHelper.ExecDataTable("SP_Get_WRHouseConditionCUCC_Result2", realParams);
                    result1.Merge(result2);
                    if (result1.Rows.Count > 0)
                    {
                        result1.DefaultView.Sort = "ApplyTime DESC";
                        result1 = result1.DefaultView.ToTable();
                        int rowNumber = 1;
                        foreach (DataRow row in result1.Rows)
                        {
                            row["RowNumber"] = rowNumber++;
                        }
                    }
                    return result1;

                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    return new DataTable();
                }finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
        public int SP_Add_WRFsuCUCC(string WRHouseId, string FsuCode, string FSURId, string FsuName, string IPAddress, string UserName, string Password, string FtpUserName, string FtpPassword, string ManufacturerId, string Remark, string ContractNo, string ProjectName, string LogonId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                realParams.Add("WRHouseId", WRHouseId);
                realParams.Add("FsuCode", FsuCode);
                realParams.Add("FSURId", FSURId);
                realParams.Add("FsuName", FsuName);
                if(IPAddress == null)
                {
                    realParams.Add("IPAddress", DBNull.Value);
                }
                else
                {
                    realParams.Add("IPAddress", IPAddress);
                }
                
                realParams.Add("UserName", UserName);
                realParams.Add("Password", Password);
                realParams.Add("FtpUserName", FtpUserName);
                realParams.Add("FtpPassword", FtpPassword);
                realParams.Add("ManufacturerId", ManufacturerId);
                realParams.Add("Remark", Remark);
                realParams.Add("ContractNo", ContractNo);
                realParams.Add("ProjectName", ProjectName);
                realParams.Add("LogonId", LogonId);
                try
                {
                    object WRStationId=DBNull.Value; object SWStationId= DBNull.Value; int?UserId=null;
                    string SWUserName = "";int?Status=null;object WRFsuId=DBNull.Value;
                    string StationName = null;
                    if (WRHouseId == null || WRHouseId == "" || FsuCode == null || FsuCode == "" || FsuName == null || FsuName == "")
                        return 0;

                    DataSet resultSql = execHelper.ExecDataSet("SP_Add_WRFsuCUCC_FindByFsuCode", realParams);
                    if (resultSql != null && (resultSql.Tables[0].Rows.Count > 0 || resultSql.Tables[1].Rows.Count > 0))
                        return -1;
                    DataTable dt = execHelper.ExecDataTable("SP_Add_WRFsuCUCC_GetStationId", realParams);
                    if (dt.Rows.Count > 0)
                    {
                        WRStationId = CommonUtils.GetNullableValue(dt.Rows[0].Field<int?>("WRStationId"));
                        SWStationId = CommonUtils.GetNullableValue(dt.Rows[0].Field<int?>("SWStationId"));
                        //SWStationId = (int)dt.Rows[0]["SWStationId"];
                    }
                    realParams.Add("WRStationId", WRStationId);
                    realParams.Add("SWStationId", SWStationId);

                    DataSet resultSql1 = execHelper.ExecDataSet("SP_Add_WRFsuCUCC_FindByStationIdAndFsuName", realParams);
                    if (resultSql1 != null && (resultSql1.Tables[0].Rows.Count > 0 || resultSql1.Tables[1].Rows.Count > 0))
                        return -2;
                    if (IPAddress != null && IPAddress != "")
                    {
                        DataSet resultSql2 = execHelper.ExecDataSet("SP_Add_WRFsuCUCC_FindIPAddressAndFsuName", realParams);
                        if (resultSql2 != null && (resultSql2.Tables[0].Rows.Count > 0 || resultSql2.Tables[1].Rows.Count > 0))
                            return -4;
                    }
                    DataTable dtUser = execHelper.ExecDataTable("SP_Add_WRFsuCUCC_GetUser", realParams);
                    if (dtUser.Rows.Count > 0)
                    {
                        UserId = (int)dtUser.Rows[0]["UserId"];
                        SWUserName = dtUser.Rows[0]["SWUserName"].ToString();
                        
                    }
                    realParams.Add("UserId", UserId);
                    realParams.Add("SWUserName", SWUserName);
                    Status = 1;
                    realParams.Add("Status", Status);
                    execHelper.ExecuteNonQuery("SP_Add_WRFsuCUCC_Insert_FsuManagementCUCC", realParams);

                    WRFsuId = CommonUtils.GetNullableValue(execHelper.ExecuteScalar("SP_Add_WRFsuCUCC_WRFsuId", realParams));
                    object objStationName = execHelper.ExecuteScalar("SP_Add_WRFsuCUCC_StationName", realParams);
                    if(objStationName!=null)
                    {
                        StationName = objStationName.ToString();
                    }
                    Public_StoredService.Instance.SP_WR_OperationRecord(WRStationId, StationName, 7, WRFsuId, FsuName, "","", LogonId);
                    return 1;
                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    return -1;
                }finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public DataTable SP_Upd_WRHouseCUCC(string WRHouseId, string WRStationId, string HouseCode, string HouseName, string HouseRId, string Remark)
        {
            if (WRHouseId == null)
                return null;
            using (DbHelper dbHelper = new DbHelper())
            {
                //返回字段表
                DataTable ReturnTable = new DataTable();
                DataColumn newColumn = new DataColumn("ReturnValue", typeof(int));
                ReturnTable.Columns.Add(newColumn);
                DataRow newRow = ReturnTable.NewRow();

                //定义字段
                int? HStatus = null, SWHouseId = null, SWStationId = null;
                string OutputHouseCode = null, OriHouseCode = null, UpdateString = null, LastString = null, StationName = null;

                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("WRHouseId", string.IsNullOrWhiteSpace(WRHouseId) ? DBNull.Value : CommonUtils.GetNullableValue(int.Parse(WRHouseId)));

                    //获取部分字段值
                    DataTable filedValueDt1 = execHelper.ExecDataTable("SP_Upd_WRHouseCUCC_GetFieldValue1", realParams);
                    if(filedValueDt1 != null && filedValueDt1.Rows.Count > 0)
                    {
                        HStatus = filedValueDt1.Rows[0].Field<int?>("HStatus");
                        SWHouseId = filedValueDt1.Rows[0].Field<int?>("SWHouseId");
                        SWStationId = filedValueDt1.Rows[0].Field<int?>("SWStationId");
                    }

                    realParams.Add("HStatus", HStatus);
                    realParams.Add("SWHouseId", CommonUtils.GetNullableValue(SWHouseId));
                    realParams.Add("Remark", CommonUtils.GetNullableValue(Remark));
                    realParams.Add("SWStationId", CommonUtils.GetNullableValue(SWStationId));
                    //获取OriHouseCode值
                    object OriHouseCodeObj = execHelper.ExecuteScalar("SP_Upd_WRHouseCUCC_GetFieldValue2", realParams);
                    if(OriHouseCodeObj != DBNull.Value && OriHouseCodeObj != null)
                    {
                        OriHouseCode = OriHouseCodeObj.ToString();
                        // 获取左边8个字符
                        string temp = OriHouseCode.Substring(0, Math.Min(8, OriHouseCode.Length));
                        OriHouseCode = temp;
                        if (OriHouseCode == HouseCode)
                        {
                            HouseCode = OriHouseCode;
                        }
                        else
                        {
                            OutputHouseCode = Public_StoredService.Instance.SP_GenerateHouseCode((int)SWStationId, HouseCode, "CUCC", OutputHouseCode);
                            HouseCode = OutputHouseCode;
                        }
                    }
                    else
                    {
                        OutputHouseCode = Public_StoredService.Instance.SP_GenerateHouseCode((int)SWStationId, HouseCode, "CUCC", OutputHouseCode);
                        HouseCode = OutputHouseCode;
                    }

                    realParams.Add("HouseCode", string.IsNullOrWhiteSpace(HouseCode) ? DBNull.Value : CommonUtils.GetNullableValue(HouseCode));
                    realParams.Add("HouseName",CommonUtils.GetNullableValue(HouseName));
                    realParams.Add("WRStationId", string.IsNullOrWhiteSpace(WRStationId) ? DBNull.Value : CommonUtils.GetNullableValue(int.Parse(WRStationId)));
                    object judgeExist1 = execHelper.ExecuteScalar("SP_Upd_WRHouseCUCC_JudgeExist1", realParams);
                    if(judgeExist1 != DBNull.Value && judgeExist1 != null)
                    {
                        newRow["ReturnValue"] = -2;
                        ReturnTable.Rows.Add(newRow);
                        return ReturnTable;
                    }
                    if(HStatus == 3)
                    {
                        execHelper.ExecuteNonQuery("SP_Upd_WRHouseCUCC_UpdateTBLHouse", realParams);
                        execHelper.ExecuteNonQuery("SP_Upd_WRHouseCUCC_UpdateTBLConfigChangeMicroLog", realParams);
                        execHelper.ExecuteNonQuery("SP_Upd_WRHouseCUCC_UpdateTBLConfigChangeMacroLog", realParams);
                    }

                    object LastStringObj = execHelper.ExecuteScalar("SP_Upd_WRHouseCUCC_GetFieldValue3", realParams);
                    if(LastStringObj != DBNull.Value && LastStringObj != null)
                    {
                        LastString = LastStringObj.ToString();
                    }
                    UpdateString = $"HouseCode:{HouseCode}-HouseName:{HouseName}-Remark:{Remark}-HouseRId:{HouseRId}";

                    object StationNameObj = execHelper.ExecuteScalar("SP_Upd_WRHouseCUCC_GetFieldValue4", realParams);
                    if(StationNameObj != DBNull.Value && StationNameObj != null)
                    {
                        StationName = StationNameObj.ToString();
                    }

                    realParams.Add("HouseRId", string.IsNullOrWhiteSpace(HouseRId) ? DBNull.Value : CommonUtils.GetNullableValue(int.Parse(HouseRId)));
                    //调用其他存储过程
                    Public_StoredService.Instance.SP_WR_OperationRecord(WRStationId, StationName, 6, WRHouseId, HouseName, UpdateString, LastString,"-1");
                    execHelper.ExecuteNonQuery("SP_Upd_WRHouseCUCC_UpdateWRHouseManagementCUCC", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("SP_Upd_WRHouseCUCC:", ex));
                    return null;
                }
                newRow["ReturnValue"] = 1; 
                ReturnTable.Rows.Add(newRow);
                return ReturnTable;
            }
        }

        public int InsertHouseCUCC(string WRStationId, string HouseCode, string HouseRId, string HouseName, string Remark, string LogonId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);

                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("WRStationId", Convert.ToInt32(WRStationId));
                    realParams.Add("HouseName", HouseName);
                    DataTable dt = execHelper.ExecDataTable("SP_Add_WRHouseCUCC_getStationId", realParams);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        object SWStationId = DBNull.Value;
                        if (dt.Rows[0][0] != DBNull.Value && dt.Rows[0][0] != null)
                        {
                            SWStationId = Convert.ToInt32(dt.Rows[0][0]);
                        }
                        string OutputHouseCode = "";
                        string OutHouseCode = Public_StoredService.Instance.SP_GenerateHouseCode(SWStationId, HouseCode, "CUCC", OutputHouseCode);
                        HouseCode = OutHouseCode;
                        realParams.Add("SWStationId", SWStationId);
                        DataTable dt1 = execHelper.ExecDataTable("SP_Add_WRHouseCUCC_selectHouse", realParams);
                        if (dt1 != null && dt1.Rows.Count > 0)
                        {
                            return -2;
                        }
                    }
                    DataTable dt2 = execHelper.ExecDataTable("SP_Add_WRHouseCUCC_selectHouseManagement", realParams);
                    if ((dt2 != null && dt2.Rows.Count > 0))
                    {
                        return -2;
                    }
                    realParams.Add("LogonId", LogonId);
                    object UserId = DBNull.Value;
                    object SWUserName = DBNull.Value;
                    DataTable dt3 = execHelper.ExecDataTable("SP_Add_WRHouseCUCC_selUserInfo", realParams);
                    if (dt3 != null && dt3.Rows.Count > 0)
                    {
                        DataRow dr = dt3.Rows[0];
                        UserId = CommonUtils.GetNullableValue(dr.Field<int?>("UserId"));
                        SWUserName = CommonUtils.GetNullableValue(dr.Field<string>("SWUserName"));
                    }
                    realParams.Add("HouseCode", HouseCode);
                    realParams.Add("UserId", UserId);
                    realParams.Add("SWUserName", SWUserName);
                    realParams.Add("Remark", Remark);
                    realParams.Add("HouseRId", CommonUtils.GetNullableValue(HouseRId));
                    execHelper.ExecuteNonQuery("SP_Add_WRHouseCUCC_insertHouse", realParams);
                    DataTable dt4 = execHelper.ExecDataTable("SP_Add_WRHouseCUCC_getWRHouseId", realParams);
                    string StationName = "";
                    DataTable dt5 = execHelper.ExecDataTable("SP_Add_WRHouseCUCC_getStationName", realParams);
                    if(dt5 != null && dt5.Rows.Count > 0)
                    {
                        StationName = dt5.Rows[0][0].ToString();
                    }
                    if (dt4 != null && dt4.Rows.Count > 0 && dt4.Rows[0][0] != DBNull.Value)
                    {
                        int WRHouseId = Convert.ToInt32(dt4.Rows[0][0]);
                        Public_StoredService.Instance.SP_WR_OperationRecord(WRStationId, StationName, 4, WRHouseId, HouseName, "", "", LogonId);
                    }
                    return 1;
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("SP_Add_WRHouseCUCC:{0}", ex));
                    return 0;
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
        public DataTable SP_ApproveWRHouseCUCC(string WRHouseId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                realParams.Add("WRHouseId", WRHouseId == null ? DBNull.Value : CommonUtils.GetNullableValue(int.Parse(WRHouseId)));
                try
                {
                    object StationStatus = DBNull.Value; object SWStationId = DBNull.Value; object HouseStatus = DBNull.Value;
                    int? maxHouseId = null; int? currHouseId = null;
                    if (WRHouseId == null)
                    {
                        return new DataTable();
                    }
                    DataTable dt = executeHelper.ExecDataTable("SP_ApproveWRHouseCUCC_Select_StationManagementCUCC", realParams);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        DataRow dr = dt.Rows[0];
                        StationStatus = CommonUtils.GetNullableValue(dr.Field<int?>("StationStatus"));
                        SWStationId = CommonUtils.GetNullableValue(dr.Field<int?>("SWStationId"));
                        HouseStatus = CommonUtils.GetNullableValue(dr.Field<int?>("HouseStatus"));

                        //StationStatus = (int)dt.Rows[0]["StationStatus"];
                        //SWStationId = (int)dt.Rows[0]["SWStationId"];
                        //HouseStatus = (int)dt.Rows[0]["HouseStatus"];
                        if ((int)StationStatus !=3)
                        {
                            return new DataTable
                            {
                                Columns = { new DataColumn("ReturnValue", typeof(int)) },
                                Rows = { new object[] { -1 } }
                            };
                        }
                        if ((int)HouseStatus==3)
                        {
                            return new DataTable
                            {
                                Columns = { new DataColumn("ReturnValue", typeof(int)) },
                                Rows = { new object[] { -2 } }
                            };
                        }
                    }
                    realParams.Add("StationStatus", CommonUtils.GetNullableValue( StationStatus));
                    realParams.Add("SWStationId", CommonUtils.GetNullableValue(SWStationId));
                    realParams.Add("HouseStatus", CommonUtils.GetNullableValue(HouseStatus));
                    object objmaxHouseId = executeHelper.ExecuteScalar("SP_ApproveWRHouseCUCC_maxHouseId", realParams);
                    if (objmaxHouseId != null && objmaxHouseId != DBNull.Value)
                    {
                        maxHouseId = Convert.ToInt32(objmaxHouseId);
                        currHouseId = maxHouseId + 1;
                    }
                    realParams.Add("maxHouseId", CommonUtils.GetNullableValue(maxHouseId));
                    realParams.Add("currHouseId", CommonUtils.GetNullableValue(currHouseId));
                    try
                    {
                        string currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                        realParams.Add("currentTime", CommonUtils.GetNullableValue(currentTime));
                        executeHelper.ExecuteNonQuery("SP_ApproveWRHouseCUCC_Insert_House", realParams);
                    }
                    catch (Exception ex)
                    {
                        executeHelper.Rollback();
                        Logger.Log(ex);
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -3 } }
                        };
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRHouseCUCC_Update_HouseManagementCUCC", realParams);
                    }
                    catch (Exception ex)
                    {
                        Logger.Log(ex);
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -5 } }
                        };
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRHouseCUCC_Insert_ConfigChangeMicroLog", realParams);
                    }
                    catch (Exception ex)
                    {
                        executeHelper.Rollback();
                        Logger.Log(ex);
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -6 } }
                        };
                    }
                    executeHelper.ExecuteNonQuery("SP_ApproveWRHouseCUCC_Update_ConfigChangeMacroLog", realParams);
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRHouseCUCC_wr_syncinfo", realParams);
                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -7 } }
                        };
                    }
                    executeHelper.Commit();
                    return new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { 1 } }
                    };

                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    return new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { null } }
                    };
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public string RejectWRHouse(object WRHouseId, string RejectCause)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);

                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                   if(WRHouseId == null)
                    {
                        return "0";
                    }
                    realParams.Add("WRHouseId", Convert.ToInt32(WRHouseId));
                    int HouseStatus = 0;
                    DataTable dt = execHelper.ExecDataTable("SP_RejectWRHouseCUCC_HouseStatus", realParams);
                    if(dt != null && dt.Rows.Count > 0)
                    {
                        HouseStatus = Convert.ToInt32(dt.Rows[0][0]);
                    }
                    if(HouseStatus == 3)
                    {
                        return "-1";
                    }
                    realParams.Add("RejectCause", RejectCause);
                    execHelper.ExecuteNonQuery("SP_RejectWRHouseCUCC_UpdateHouse", realParams);
                    execHelper.ExecuteNonQuery("SP_RejectWRHouseCUCC_UpdateFsu", realParams);
                    return "1";
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("SP_RejectWRHouseCUCC:{0}", ex));
                    return "0";
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
    }
}
