﻿
namespace DM.TestOrder.Entity {
    using System;
    using System.Collections.Generic;

    public partial class WO_TestOrder {
        public List<WO_TestOrderEquipItem> EquipItemList {
            get;
            set;
        }
        public List<WO_TestOrderEquipItemCheckList> TestOrderEquipItemCheckList {
            get;
            set;
        }

        public List<WO_TestOrderArtSelfCheckList> TestOrderArtSelfCheckList {
            get;
            set;
        }

        public string OrderTypeString {
            get {
                if (this.OrderType == 0) {
                    return "新装入网";
                }
                else {
                    return "维护巡检";
                }
            }
        }
        //public string MyOrderId {
        //    get {
        //        return ApplyTime.Value.ToString("yyyyMMdd") + "-" + OrderId.ToString().PadLeft(6, '0');
        //    }
        //}

    }
}
