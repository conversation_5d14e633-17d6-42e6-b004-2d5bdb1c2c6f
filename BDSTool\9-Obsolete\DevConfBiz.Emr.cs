﻿

using BDSTool.BLL.Convert;
using BDSTool.BLL.S2;
using BDSTool.Entity;
using BDSTool.Entity.B;
using BDSTool.Entity.S2;
using BDSTool.Utility;

using Common.Logging.Pro;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;


namespace BDSTool.BLL.B
{
    public partial class DevConfBiz
    {
        #region public
        public static bool ZGetFSUConfigByMonitorUnitId(int MonitorUnitId, ref  List<TDevConf> listDevice) {
            listDevice = new List<TDevConf>();
            try {

                List<TDevConfFromDB> listTDevConfEx = null;
                if (!EquipmentCMCCBiz.GetAllByMuId(MonitorUnitId, ref listTDevConfEx)) {
                    LoggerBDSTool.Info("GetFSUConfigByMonitorUnitId();EquipmentCMCCBiz.GetDevListByMuId Failed");
                    return false;
                }

                //本公司监控单元、FSU，信号编码方式处理不同
                bool IsRealFSU = ZFsuBiz.IsBMonitorUnit(MonitorUnitId);
                LoggerBDSTool.InfoFormat("GetFSUConfigByMonitorUnitId(); IsFsu={0}", IsRealFSU);
                if (IsRealFSU)
                    FillSignalList( listTDevConfEx);
                else
                    ZFillSignalList4Emr(listTDevConfEx);

                foreach (var devEx in listTDevConfEx) {
                    listDevice.Add(devEx.device);
                }
            }
            catch (Exception ex) {
                logger.ErrorFormat("GetFSUConfigByMonitorUnitId();MonitorUnitId={0}; err={1}", MonitorUnitId, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }
        public static bool ZGetFSUConfigByDeviceIds(int MonitorUnitId, List<string> DevIds, ref  List<TDevConf> listDevice) {           
            listDevice = new List<TDevConf>();
            try {
                //本公司监控单元、FSU，处理不同
                bool IsBMu = ZFsuBiz.IsBMonitorUnit(MonitorUnitId);
                LoggerBDSTool.InfoFormat("GetFSUConfigByMonitorUnitId(); IsFsu={0}", IsBMu);


                List<TDevConfFromDB> listTDevConfEx = null;
                if (!EquipmentCMCCBiz.ZGetDevListByDeviceIds(MonitorUnitId, DevIds, ref listTDevConfEx)) {
                    LoggerBDSTool.Info("GetFSUConfigByDeviceIds();EquipmentCMCCBiz.GetDevListByDeviceIds Failed");
                    return false;
                }

                if(IsBMu)
                    FillSignalList(listTDevConfEx);
                else
                    ZFillSignalList4Emr(listTDevConfEx);

                foreach (var devEx in listTDevConfEx) {
                    listDevice.Add(devEx.device);
                }
            }
            catch (Exception ex) {
                logger.ErrorFormat("GetFSUConfigByMonitorUnitId();MonitorUnitId={0}; err={1}", MonitorUnitId, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }
        #endregion 

        #region private
        public static void ZFillSignalList4Emr(List<TDevConfFromDB> listTDevConfEx) {
            foreach (var devEx in listTDevConfEx) {
                int StationId = devEx.StationId;
                int EquipmentId = devEx.EquipmentId;
                var list = TSignalBiz.ZGetTSignalList4Emr( StationId, EquipmentId);
                devEx.device.Signals = list;


                if (list.Count == 0)
                    LoggerBDSTool.WarnFormat("FillSignalList(); device={0}; no tsignal", devEx.device.DeviceName);
                else
                    LoggerBDSTool.InfoFormat("FillSignalList(); device={0}; get Sig-Count={1}", devEx.device.DeviceName, list.Count);
            }
        }
        #endregion

    }
}
