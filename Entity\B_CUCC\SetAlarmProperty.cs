﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class SetAlarmProperty : BMessage
    {
        public List<Device> LDevice { get; set; }

        public SetAlarmProperty(string suid, string surid, List<Device> lDevice)
            : base()
        {
            MessageType = (int)BMessageType.SET_AlarmProperty;
            SUId = suid;
            SURId = surid;
            LDevice = lDevice;
        }

        private XmlElement GetElementInfo(XmlDocument xmlDoc)
        {
            XmlElement xmle = xmlDoc.CreateElement("DeviceList");
            if (LDevice.Count > 0)
            {
                foreach (Device dev in LDevice)
                {
                    XmlElement xmlT = xmlDoc.CreateElement("Device");
                    xmlT.SetAttribute("Id", dev.Id);
                    xmlT.SetAttribute("RId", dev.RId);
                    if (dev.SignalList.Count > 0)
                    {
                        foreach (Signal sdv in dev.SignalList)
                        {
                            XmlElement xmlid = xmlDoc.CreateElement("Signal");
                            xmlid.SetAttribute("Id", sdv.Id);
                            xmlid.SetAttribute("BDelay", sdv.BDelay.ToString());
                            xmlid.SetAttribute("EDelay", sdv.EDelay.ToString());
                            xmlT.AppendChild(xmlid);
                        }
                    }
                    xmle.AppendChild(xmlT);
                }
            }
            else
            {
                XmlElement xmlT = xmlDoc.CreateElement("Device");
                xmlT.SetAttribute("Id", "");
                xmlT.SetAttribute("RId", "");
                XmlElement xml1 = xmlDoc.CreateElement("Signal");
                xml1.InnerText = "";
                xmlT.AppendChild(xml1);
                xmle.AppendChild(xmlT);
            }
            return xmle;
        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmlDoc = CreateXmlDocument();
                AppendPK_Type(xmlDoc, "Request", BMessageType.SET_AlarmProperty.ToString(), MessageType.ToString());
                XmlElement xel = xmlDoc.CreateElement("Info");
                XmlElement xelone = xmlDoc.CreateElement("SUId");
                XmlElement xeltwo = xmlDoc.CreateElement("SURId");
                xelone.InnerText = SUId;
                xeltwo.InnerText = SURId;
                xel.AppendChild(xelone);
                xel.AppendChild(xeltwo);
                xel.AppendChild(GetElementInfo(xmlDoc));
                XmlNode node = xmlDoc.SelectSingleNode("Request");
                node.AppendChild(xel);
                StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("SetAlarmProperty.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmlDoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetAlarmProperty.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetAlarmProperty.Serialize() Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }


        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1},{2},{3}:", MessageId, (BMessageType)MessageType, SUId, SURId);
            sb.Append("\r\n");
            if (LDevice.Count > 0)
            {
                foreach (Device dev in LDevice)
                {
                    sb.Append("[").Append(dev.Id).Append(",").Append(dev.RId);
                    if(dev.SignalList.Count > 0)
                    {
                        foreach (Signal signal in dev.SignalList)
                        {
                            sb.Append("(");
                            sb.Append(signal.Id).Append(",").Append(signal.BDelay.ToString()).Append(",").Append(signal.EDelay.ToString());
                            sb.Append(")");
                        }
                    }
                    sb.Append("]");

                    sb.Append("\r\n");
                }
            }
            return sb.ToString();
        }

    }
}
