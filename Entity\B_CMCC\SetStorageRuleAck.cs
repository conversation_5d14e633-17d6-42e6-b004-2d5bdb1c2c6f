﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;


namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 写监控点存储规则应答
    /// </summary>
    public sealed class SetStorageRuleAck : BMessage
    {
        public EnumResult Result { get; private set; }

        public string FailureCause { get; private set; }

        public List<DeviceSetStorageRuleAck> Devices { get; private set; }

        #region SiteWeb配置

        public int MyStationId { get; set; }

        #endregion

        public SetStorageRuleAck(string fsuId, EnumResult result, string failureCause, List<DeviceSetStorageRuleAck> devices)
            : base()
        {
            MessageType = (int)BMessageType.SET_STORAGERULE_ACK;

            FSUID = fsuId;
            Result = result;
            FailureCause = failureCause;
            Devices = devices;
        }

        public SetStorageRuleAck()
            : base() 
        {
            MessageType = (int)BMessageType.SET_STORAGERULE_ACK;
        }

        public static SetStorageRuleAck Deserialize(XmlDocument xmldoc)
        {
            SetStorageRuleAck setStorageRuleAck = null;
            try
            {
                string fsuId = xmldoc.SelectSingleNode("/Response/Info/FSUID").InnerText.Trim();
                string result = xmldoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim();
                EnumResult enumResult = EnumResult.FAILURE;
                if (!string.IsNullOrEmpty(result))
                {
                    enumResult = (EnumResult)int.Parse(result);
                }
                string failureCause = xmldoc.SelectSingleNode("/Response/Info/FailureCause").InnerText.Trim();

                XmlNodeList nodelist = xmldoc.SelectNodes("/Response/Info/DeviceList/Device");

                List<DeviceSetStorageRuleAck> deviceSetStorageRuleAckList = new List<DeviceSetStorageRuleAck>();

                foreach (XmlNode xn in nodelist)//遍历所有子节点 
                {
                    XmlElement xe = (XmlElement)xn;
                    string deviceId = xe.GetAttribute("ID");

                    List<TSignalMeasurementId> successList = new List<TSignalMeasurementId>();
                    List<TSignalMeasurementId> failList = new List<TSignalMeasurementId>();

                    XmlNode xns = xe.SelectSingleNode("SuccessList");
                    if (xns != null)
                    {
                        foreach (XmlNode xnSub in xns.ChildNodes)
                        {
                            TSignalMeasurementId tsmid = new TSignalMeasurementId();
                            tsmid.ID = xnSub.Attributes["ID"].Value.Trim();
                            string strSignalNumber = xnSub.Attributes["SignalNumber"].Value.Trim();
                            if (string.IsNullOrEmpty(strSignalNumber) || strSignalNumber.ToUpper() == "NULL")
                            {
                                entityLogger.ErrorFormat("SetStorageRuleAck.Deserialize():ID = {0}, SignalNumber is NullOrEmpty", tsmid.ID);
                            }
                            else
                            {
                                tsmid.SignalNumber = strSignalNumber;
                            }
                            successList.Add(tsmid);
                        }
                    }
                    XmlNode xnf = xe.SelectSingleNode("FailList");
                    if (xnf != null)
                    {
                        foreach (XmlNode xnSub in xnf.ChildNodes)
                        {
                            TSignalMeasurementId tsmid = new TSignalMeasurementId();
                            tsmid.ID = xnSub.Attributes["ID"].Value.Trim();
                            string strSignalNumber = xnSub.Attributes["SignalNumber"].Value.Trim();
                            if (string.IsNullOrEmpty(strSignalNumber) || strSignalNumber.ToUpper() == "NULL")
                            {
                                entityLogger.ErrorFormat("SetStorageRuleAck.Deserialize():ID = {0}, SignalNumber is NullOrEmpty", tsmid.ID);
                            }
                            else
                            {
                                tsmid.SignalNumber = strSignalNumber;
                            }
                            failList.Add(tsmid);
                        }
                    }
                    DeviceSetStorageRuleAck deviceSetStorageRuleAck = new DeviceSetStorageRuleAck(deviceId, successList, failList);
                    deviceSetStorageRuleAckList.Add(deviceSetStorageRuleAck);
                }
                setStorageRuleAck = new SetStorageRuleAck(fsuId, enumResult, failureCause, deviceSetStorageRuleAckList);
                entityLogger.DebugFormat("SetStorageRuleAck.Deserialize(),xml:\r\n{0}", FormatXml(xmldoc.InnerXml));
                setStorageRuleAck.StringXML = xmldoc.InnerXml;
                return setStorageRuleAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetStorageRuleAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetStorageRuleAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmldoc.InnerXml));
                setStorageRuleAck = new SetStorageRuleAck();
                setStorageRuleAck.ErrorMsg = ex.Message;
                setStorageRuleAck.StringXML = xmldoc.InnerXml;
                return setStorageRuleAck;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();

            sb.AppendFormat("{0}, {1}, {2}, {3}:",
                MessageId, (BMessageType)MessageType, FSUID, Devices != null ? Devices.Count : 0);
            sb.AppendLine();
            if (Devices != null)
            {
                foreach (DeviceSetStorageRuleAck item in Devices)
                {
                    sb.AppendFormat("{0}", item.ToString());
                    sb.AppendLine();
                }
            }

            return sb.ToString();
        }


    }
}
