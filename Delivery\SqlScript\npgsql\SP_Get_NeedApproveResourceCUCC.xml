﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
		<procedure owner="" name="NeedApproveResourceCUCCS_UserId" grant="">
			<parameters>
				<parameter name="LogonId" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[
            SELECT  TBL_Account.UserId UserId FROM TBL_Account 
	        WHERE TBL_Account.LogonId = @LogonId ; 
         ]]>
			</body>
		</procedure>
		<procedure owner="" name="NeedApproveResourceCUCCS_X" grant="">
			<parameters>
				<parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
            SELECT 'X' FROM TBL_UserRoleMap WHERE TBL_UserRoleMap.UserId = @UserId AND TBL_UserRoleMap.RoleId = -1
                 ]]>
			</body>
		</procedure>
		<procedure owner="" name="NeedApproveResourceCUCCS_Filter_UserId" grant="">
			<parameters>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
             SELECT DISTINCT a.UserId FROM wr_fsumanagementcucc a
             UNION
             SELECT DISTINCT b.UserId FROM wr_housemanagementcucc b
             UNION
             SELECT DISTINCT c.UserId FROM wr_stationmanagementcucc c
                 ]]>
			</body>
		</procedure>
		<procedure owner="" name="NeedApproveResourceCUCCS_StationCount" grant="">
			<parameters>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
            SELECT count(*) p_StationCount FROM wr_stationmanagementcucc a WHERE a.StationStatus = 1 AND a.UserId IN (SELECT t1.UserId FROM ($[filterUser]) t1);
                 ]]>
			</body>
		</procedure>
		<procedure owner="" name="NeedApproveResourceCUCCS_HouseCount" grant="">
			<parameters>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
             SELECT count(*) p_HouseCount FROM wr_housemanagementcucc b WHERE b.HouseStatus = 1 AND b.UserId IN (SELECT t2.UserId FROM ($[filterUser]) t2);
                 ]]>
			</body>
		</procedure>
		<procedure owner="" name="NeedApproveResourceCUCCS_FsuCount" grant="">
			<parameters>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
             SELECT count(*) p_FsuCount FROM wr_fsumanagementcucc c WHERE c.FsuStatus = 1 AND c.UserId IN (SELECT t3.UserId FROM ($[filterUser]) t3);
                 ]]>
			</body>
		</procedure>
		<procedure owner="" name="NeedApproveResourceCUCCS" grant="">
			<parameters>
				<parameter name="p_StationCount" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="p_HouseCount" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="p_FsuCount" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
            SELECT @p_StationCount StationCount, @p_HouseCount HouseCount, @p_FsuCount FsuCount;
                 ]]>
			</body>
		</procedure>
		<procedure owner="" name="NeedApproveResourceCUCCS2" grant="">
			<body>SELECT 0 StationCount, 0 HouseCount, 0 FsuCount</body>
		</procedure>
	</procedures>
</root>
