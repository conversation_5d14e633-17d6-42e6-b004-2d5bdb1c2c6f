﻿using BDSTool.BLL.S2;
using BDSTool.DBUtility;
using BDSTool.DBUtility.Common;
using BDSTool.Entity.S2;
using Common.Logging.Pro;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace BDSTool.BLL.Convert
{
    public class EquipmentCategoryConvert
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static int? TryParse(int? DeviceType) {
            try {
                if (DeviceType!=null){
                    string rtn = null;
                    if (CommonUtils.IsNoProcedure)
                    {
                        rtn = Public_ExecuteSqlService.Instance.NoStore_TBLDataItem_GetByDeviceType(DeviceType);
                    }
                    else
                    {
                        var sql = string.Format(@"SELECT ItemId FROM TBL_DataItem WHERE EntryId=7 AND ExtendField2={0}", SHelper.GetPara(DeviceType));
                        rtn = DBHelper.ExecuteScalar(sql);
                    }
                    return SHelper.ToIntNullable(rtn);
                }
            }
            catch (Exception ex) {
                logger.ErrorFormat("EquipmentCategoryConvert.TryParse();Error={0}", ex.Message);
                logger.Error(ex.StackTrace);               
            }
            return null;
        }
    }
}
