﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 时间同步
    /// </summary>
    public class TimeCheck : BMessage
    {
        [JsonIgnore]
        public TTime Time { get; set; }

        [JsonProperty("Time")]
        [JsonConverter(typeof(CustomDateTimeConverter1))]
        public DateTime? dateTime { get; set; }

        public TimeCheck() : base()
        {
            MessageType = (int)BMessageType.TIME_CHECK;
        }

        public TimeCheck(string fsuId, TTime time)
            : base()
        {
            MessageType = (int)BMessageType.TIME_CHECK;

            FSUID = fsuId;
            Time = time;

            dateTime = ConvertTime(Time);
        }

        public TimeCheck(string fsuId, DateTime time)
            : base()
        {
            MessageType = (int)BMessageType.TIME_CHECK;

            FSUID = fsuId;
            TTime tTime = new TTime(time);
            Time = tTime;

            dateTime = ConvertTime(Time);
        }

        /// <summary>
        /// 将CMCC定义的时间TTime转换为DateTime
        /// </summary>
        /// <param name="time"></param>
        private DateTime ConvertTime(TTime time)
        {
            if (time == null)
                return DateTime.Now;
            string timeStr = time.ToString();
            DateTime dateTime = DateTime.Parse(timeStr);
            return dateTime;
        }

        public override string Serialize()
        {
            string result = string.Empty;
            try
            {
                JsonSerializerSettings settings = new JsonSerializerSettings();
                settings.ContractResolver = new CustomContractResolver();
                TIotEntity iotEntity = new TIotEntity(this);
                result = JsonConvert.SerializeObject(iotEntity, settings);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("TimeCheck.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("TimeCheck.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
            }
            return result;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}, {2}:{3}", MessageId, (BMessageType)MessageType, FSUID, Time.ToString());
            return sb.ToString();
        }
    }
}