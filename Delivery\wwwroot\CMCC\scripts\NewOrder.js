﻿'use strict';
var cookie = getCookie();
var CurrentUserId = cookie['userinfo']['UserId'];
var SelectedStationId = -1;
var SelectedCompanyId = -1;
var SelectedClerkId = -1;
var Equips = [];
var isSubmit = false;
var Stations = [];
var Equipments = [];
var InstallCompanys = [];
var Installers = [];

function checkTextBoxLength(id) {
    if (!$('#' + id).textbox('isValid')) {
        $.messager.alert("提示", "请输入正确长度!", 'error');
        return false;
    }
    return true;
}

//构建割接类型控件
function loadDicDropControl(control, entryId) {
    control.combobox({
        method: 'GET',
        url: '../api/MyDemo?OpFlag=OrderType&id=7',
        valueField: 'ItemId',
        textField: 'ItemName',
        //multiple: true,
        editable: false, //禁止编辑
        onLoadSuccess: function () {
            var data = $(this).combobox("getData");
            $(this).combobox('select', data[0].ItemId);
        }
    });
}
         
    //带查询按钮的文本弹出框
$.extend($.fn.textbox.methods, {
    addClearBtn: function (jq, iconCls) {
        return jq.each(function () {
            var t = $(this);
            var opts = t.textbox('options');
            opts.icons = opts.icons || [];
            opts.icons.unshift({
                iconCls: iconCls,
                handler: function (e) {
                    $(e.data.target).textbox('clear').textbox('textbox').focus();
                    $(this).css('visibility', 'hidden');
                }
            });
            t.textbox();
            if (!t.textbox('getText')) {
                t.textbox('getIcon', 0).css('visibility', 'hidden');
            }
            t.textbox('textbox').bind('keyup', function () {
                var icon = t.textbox('getIcon', 0);
                if ($(this).val()) {
                    icon.css('visibility', 'visible');
                } else {
                    icon.css('visibility', 'hidden');
                }
            });
        });
    }
});
/*
$(function(){
	$('#tt').textbox().textbox('addClearBtn', 'icon-clear');
});
*/
function SearchStationDialog() {
    $('#StationDialog').dialog('open');
    $('#btnResetCondition').click();
	$('#dgStation').datagrid('loadData', []);
    //$('#btnSeach').click();
}
function loadStationGridControl(){
    $('#dgStation').datagrid({
        title: '',
        //url: '../api/StationList',
        singleSelect: true,
        width: function () { return document.body.clientWidth * 0.9 },
        height: 'auto',
        fitColumns: true,
        rownumbers: true,
        checkOnSelect: true,
        columns: [[
            { field: 'ck', title: '', checkbox: true, vawidth: 30 },
            { field: 'StationId', title: '', hidden: true },
            { field: 'StationName', title: '站址名称', width: 100 },
            { field: 'StationCode', title: '站址编码', width: 80 },
            { field: 'StationCategoryName', title: '站址类型', width: 80 },
            { field: 'Address', title: '基站地址', width: 150 },
            { field: 'ProvinceName', title: '省', width: 50 },
            { field: 'CityName', title: '市', width: 50 },
            { field: 'CountyName', title: '区', width: 50 },
        ]],
        loadFilter: pagerFilter,
        pagination: true,//分页设置
        pageSize: 10,
        pageList: [10, 20, 50],
        onDblClickRow: function (index, row) {
            LoadStationInfoById(row);
            $('#StationDialog').dialog('close');
        }
    });
}

//定义站址选择查询按钮单击事件
function InitBtnStationSeach()
{
    $('#btnSeach').click(function () {
        if (checkTextBoxLength('StationName') && checkTextBoxLength('StationCode'))
            LoadStation();
            //$('#dgStation').datagrid({
            //    queryParams: {
            //        StationName: $('#StationName').val(),
            //        StationCode: $('#StationCode').val(),
            //    }
            //});
    });
}


function LoadStation() {
    var stationName = encodeURI($('#StationName').val());
    var stationCode = $('#StationCode').val()
    var queryStr = '?' + (stationName ? 'stationName=' + stationName : '') + (stationCode ? 'stationCode=' + stationCode : '')
    $.ajax({
        type: "GET",
        //contentType: "application/json; charset=UTF-8",
        //contentType: "application/x-www-form-urlencoded",
        url: '../api/StationList' + queryStr,
        //data: paras,
        success: function (data) {
            //var rows = JSON.parse(data);
            $('#dgStation').datagrid('loadData', data);
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
}

//定义候选设备选择查询按钮单击事件
function InitBtnEquipsSeach()
{
    $('#btnEquipsSeach').click(function () {
        if (checkTextBoxLength('txtHouseName') && checkTextBoxLength('txtEquipName') && checkTextBoxLength('txtEquipCode'))
            LoadEquips();
    });
}
function LoadEquips() {
    $.ajax({
        type: "GET",
        contentType: "application/json; charset=UTF-8",
        url: '../api/SiteEquip?userid=' + CurrentUserId + '&stationId=' + SelectedStationId + '&houseName=' + encodeURI($("#txtHouseName").val()) + '&equipmentName=' + encodeURI($("#txtEquipName").val()) + '&equipmentNo=' + $("#txtEquipCode").val() + '&orderType=' + $("#OrderType").combobox('getValue'),
        success: function (data) {
            $('#dgEquipsQuery').datagrid('loadData', data);
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
}
		
//初始化重置按钮
function InitResetCondition()
{
    $('#btnResetCondition').click(function () {
                $('#StationName').textbox('setValue', '');
                $('#StationCode').textbox('setValue', '');
            });
}

//初始化重置按钮
function InitResetEquips()
{
    $('#btnEquipsReset').click(function () {
                $('#txtHouseName').textbox('setValue', '');
                $('#txtEquipName').textbox('setValue', '');
				$('#txtEquipCode').textbox('setValue', '');
            });
}

//初始化选择按钮
function InitChooseControl()
{
    $('#btnChoose').click(function () {
        getSelected();
        Equips = [];
        $('#dgEquips').datagrid('loadData', []);
    });
}
//传回选中的局站
function getSelected(){
	var row = $('#dgStation').datagrid('getSelected');
	if (row){
        LoadStationInfoById(row);
        $('#StationDialog').dialog('close');
	}
    else{
	    $.messager.alert("提示", "请选择基站!", 'error');
    }
}
//这个方法没用到，只是测试DATAGRID批量选中
function getSelections(){
	var ss = [];
	var rows = $('#dgStation').datagrid('getSelections');
	for(var i=0; i<rows.length; i++){
		var row = rows[i];
		ss.push('<span>'+row.itemid+":"+row.productid+":"+row.attr1+'</span>');
	}
	$.messager.alert('Info', ss.join('<br/>'));
}
//根据选择的局站ID，带出局站其他信息
function LoadStationInfoById(station) {
    $('#txtCenterName').textbox('setValue', station.CenterName);
    $('#txtGroupName').textbox('setValue', station.StructureName);
    $('#txtStationName').textbox('setValue', station.StationName);
    $('#txtStationCode').textbox('setValue', station.StationCode);
    $('#txtStationCategory').textbox('setValue', station.StationCategoryName);
    $('#txtLongitude').textbox('setValue', station.Longitude ? station.Longitude : '0.00000000000000000');
    $('#txtLatitude').textbox('setValue', station.Latitude ? station.Latitude : '0.00000000000000000');
    
    SelectedStationId = station.SWStationId;


    //$.ajax({
    //    type: "GET",
    //    contentType: "application/json; charset=UTF-8",
    //    url: '../api/MyDemo?OpFlag=StationInfoById&id='+stationId,
    //    success: function (data) {
    //        $('#txtCenterName').textbox('setValue', data.CenterName);
    //        $('#txtGroupName').textbox('setValue', data.GroupName);
    //        $('#txtStationName').textbox('setValue', data.StationName);
    //        $('#txtStationCode').textbox('setValue', data.StationCode);
    //        $('#txtStationCategory').textbox('setValue', data.StationCategoryName);
    //        $('#txtLongitude').textbox('setValue', data.Longitude);
    //        $('#txtLatitude').textbox('setValue', data.Latitude);
	//		SelectedStationId = stationId;
    //    }
    //});
}

function InitEquipsGrid(){
	$('#dgEquips').datagrid({
			title: '',
			singleSelect:true,
			width: function () { return document.body.clientWidth * 0.9 },
			height: 'auto',
			fitColumns: true,
			rownumbers:true,
			columns: [[
				{ field: 'EquipmentId', title: '',hidden:true },
				{ field: 'EquipmentName', title: '设备名称' ,width:100},
				{ field: 'EquipmentCategoryName', title: '设备类型', width: 50 },
				{ field: 'Vendor', title: '设备厂家' ,width:100},
				{ field: 'EquipmentStyle', title: '设备型号' ,width:50},
				//{ field: 'UriProtocol', title: '协议编码' ,width:50},
				//{ field: 'UriProtocolOK', title: '协议编码' ,hidden:true},
				//{ field: 'UriImage', title: '水印照片' ,width:50},
				//{ field: 'UriImageOK', title: '水印照片' ,hidden:true},
			]],
		});
}

function InitEquipsQueryGrid(){
	$('#dgEquipsQuery').datagrid({
        title: '',
        singleSelect:false,
        width: function () { return document.body.clientWidth * 0.9 },
        height: 'auto',
        fitColumns: true,
        rownumbers: true,
        checkOnSelect: true,
        columns: [[
            { field: 'ck', title: '',checkbox:true,vawidth:30 },
            { field: 'EquipmentId', title: '',hidden:true },
            { field: 'HouseName', title: '机房名称' ,width:100},
            { field: 'RoomID', title: '机房编码', width: 80 },
            { field: 'MonitorUnitName', title: 'FSU名称' ,width:100},
            { field: 'FSUID', title: 'FSU编码', width: 80 },
            { field: 'EquipmentName', title: '设备名称' ,width:100},
            { field: 'EquipmentNo', title: '设备编码' ,width:80},
        ]],
    });
}

function SearchEuipsDialog() {
    $('#btnEquipsReset').click();
    $('#btnEquipsSeach').click();
    $('#EquipsDialog').dialog('open');
}

//初始化设备选择按钮
function InitEquipsChooseControl()
{
    $('#btnEquipsChoose').click(function () {
        getEquipsSelected();
        
    });
}
//传回选中的设备
function getEquipsSelected(){
	var rows = $('#dgEquipsQuery').datagrid('getChecked');
	if (rows && rows.length > 0){
        LoadEquipsById(rows);
        $('#EquipsDialog').dialog('close');
	}
    else{
            $.messager.alert("提示","请选择设备!","error");
    }
}

function LoadEquipsById(rows){
	Equips=[];
	for (var i = 0; i < rows.length; i++){
	    Equips.push(rows[i].EquipmentId);
	}
	$('#dgEquips').datagrid('loadData', rows);
}

//初始化安装公司表格样式
function InitInstallCompanyGrid(){
	$('#dgInstallCompanyGrid').datagrid({
		title: '',
		singleSelect:true,
		width: function () { return document.body.clientWidth * 0.9 },
		height: 'auto',
		fitColumns: true,
		rownumbers: true,
        checkOnSelect: true,
		columns: [[
		    { field: 'ck', title: '',checkbox:true,vawidth:30 },
			{ field: 'CompanyId',width:100,hidden:true },
			{ field: 'CompanyName', title: '公司名称' ,width:100},
		]],
		onDblClickRow: function (index, row) {
		    $('#txtInstallCompany').textbox('setValue', row.CompanyName);//.textbox('textbox').keyup();
		    SelectedCompanyId = row.CompanyId;
		    $('#InstallCompanyDialog').dialog('close');
		}
	});
}

//初始化安装公司查询按钮
function InitInstallCompanyQuery()
{
    $('#btnInstallCompanySeach').click(function () {
        if (checkTextBoxLength('txtCompanyNameC'))
            getInstallCompany();
    });
}
function getInstallCompany(){
	$.ajax({
        type: "GET",
        contentType: "application/json; charset=UTF-8",
        url: '../api/InstallCompany?keyword=' + encodeURI($("#txtCompanyNameC").val()),
        success: function (data) {
		    InstallCompanys = data;
            $('#dgInstallCompanyGrid').datagrid('loadData', InstallCompanys);
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
}

//初始化公司选择按钮
function InitCompanyChooseControl()
{
    $('#btnInstallCompanyChoose').click(function () {
        getCompanySelected();
        SelectedClerkId = -1;
        $('#txtInstaller').textbox('setValue', '');
    });
}
//传回选中的公司
function getCompanySelected(){
	var row = $('#dgInstallCompanyGrid').datagrid('getChecked');
	if (row && row.length > 0){
	    $('#txtInstallCompany').textbox('setValue', row[0].CompanyName);
	    SelectedCompanyId = row[0].CompanyId;
        $('#InstallCompanyDialog').dialog('close');
	}
    else{
	    $.messager.alert("提示", "请选择安装公司!", 'error');
    }
}

//初始化公司选择按钮
function InitCompanyAddControl()
{
    $('#btnInstallCompanyAdd').click(function () {
		if($("#txtCompanyNameC").val()){
			PostNewCompany();
		}
		else{
		    $.messager.alert("提示", "请先录入安装公司名称!", 'error');
			return;
		}
    });
}
//录入一个新公司
function PostNewCompany() {
    if (checkTextBoxLength('txtCompanyNameC')) {
        var paras = {
            "CompanyName": $("#txtCompanyNameC").val()
        };
        $.ajax({
            type: "POST",
            contentType: "application/json; charset=UTF-8",
            url: '../api/NewCompany',
            data: JSON.stringify(paras),

            success: function (data) {
                //alert(JSON.stringify(data));
                if (data.errormsg == "OK") {
                    /*
                        $('#dgInstallCompanyGrid').datagrid("appendRow", {
                                ck:false,
                                CompanyName: $("#txtCompanyNameC").val()
                        });
                    */
                    getInstallCompany();
                }
                else {
                    $.messager.alert("提示", '录入公司失败!', 'error');
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
            }
        });
    }
}
        
//初始化安装人员表格样式
function InitInstallerGrid(){
	$('#dgInstallerGrid').datagrid({
		title: '',
		singleSelect:true,
		width: function () { return document.body.clientWidth * 0.9 },
		height: 'auto',
		fitColumns: true,
		rownumbers: true,
        checkOnSelect: true,
		columns: [[
		    { field: 'ck', title: '',checkbox:true,vawidth:30 },
			{ field: 'CompanyId', hidden:true},
			{ field: 'CompanyName', title: '公司名称' ,width:100},			
			{ field: 'ClerkId', hidden:true},
			{ field: 'ClerkName', title: '人员名称' ,width:100},
		]],
		onDblClickRow: function (index, row) {
		    SelectedClerkId = row.ClerkId;
		    $('#txtInstaller').textbox('setValue', row.ClerkName);
		    $('#InstallerDialog').dialog('close');
		}
	});
}

//初始化安装人员查询按钮
function InitInstallerQuery()
{
    $('#btnInstallerSeach').click(function () {
        if (checkTextBoxLength('txtInstallerName'))
            getInstaller();
    });
}
function getInstaller(){
	$.ajax({
        type: "GET",
        contentType: "application/json; charset=UTF-8",
        url: '../api/InstallClerk?CompanyId=' + SelectedCompanyId + "&kwClerk=" + encodeURI($("#txtInstallerName").val()),
        success: function (data) {
		    Installers = data;
            $('#dgInstallerGrid').datagrid('loadData', Installers);
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
}

//初始化人员选择按钮
function InitInstallerChooseControl()
{
    $('#btnInstallerChoose').click(function () {
        getInstallerSelected();
    });
}
//传回选中的人员
function getInstallerSelected(){
	var row = $('#dgInstallerGrid').datagrid('getChecked');
	if (row && row.length > 0){
	    SelectedClerkId = row[0].ClerkId;
	    $('#txtInstaller').textbox('setValue', row[0].ClerkName);
        $('#InstallerDialog').dialog('close');
	}
    else{
	    $.messager.alert("提示", "请选择安装人员!", 'error');
    }
}

//初始化安装人员新增按钮
function InitInstallerAddControl()
{
    $('#btnInstallerAdd').click(function () {
	    if(SelectedCompanyId==-1){
	        $.messager.alert("提示", "请先选择安装公司!", 'error');
			return;
		}
		if(!$("#txtInstallerName").val()){
		    $.messager.alert("提示","请先录入安装人员名称!", 'error');
			return;
		}
		PostNewInstaller();
    });
}
//录入一个新安装人员
function PostNewInstaller() {
    if (checkTextBoxLength('txtInstallerName')) {
        var paras = {
            "CompanyId": SelectedCompanyId,
            "ClerkName": $("#txtInstallerName").val(),
        };
        $.ajax({
            type: "POST",
            contentType: "application/json; charset=UTF-8",
            url: '../api/NewClerk',
            data: JSON.stringify(paras),

            success: function (data) {
                //alert(JSON.stringify(data));
                if (data.errormsg == "OK") {
                    /*
                    $('#dgInstallerGrid').datagrid("appendRow", {
                            ck:false,
                            CompanyName: $("#txtCompanyNameI").val(),
                            InstallerName: $("#txtInstallerName").val(),
                    });
                    */
                    getInstaller();
                }
                else {
                    $.messager.alert("提示", '录入人员失败!', 'error');
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
            }
        });
    }
}

function UpdateNeedApproveOrder() {
    $.ajax({
        type: "GET",
        contentType: "application/json; charset=UTF-8",
        url: '../api/OrderList/' + cookie['userinfo']['UserId'],
        success: function (data) {
            parent.$('#daibanul').children('li').eq(1).hide();
            parent.$('#applyToDo').parentsUntil('ul').last().hide();
            if (data && data.length > 0) {
                parent.$('#daibanul').children('li').eq(1).show();
                for (var i = 0; i < data.length; i++) {
                    if (data[i].OrderState === 1) {
                        if (data[i].OrderCount) {
                            parent.$('#applyToDo').parentsUntil('ul').last().show();
                            parent.$('#applyToDo').next().html(data[i].OrderCount);
                        }
                    }
                }
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
}

function NewOrder(){
    if(isSubmit){
        $.messager.alert("提示", "割接单已提交申请!", 'error');
		return;
	}
	if(SelectedStationId==-1){
	    $.messager.alert("提示", "您还没选择站址，不能提交申请!", 'error');
		return;
	}
	if (Equips === undefined || Equips.length == 0) {
	    $.messager.alert("提示", "您还没选择设备，不能提交申请!", 'error');
		return;
	}
	
	if (!$("#txtInstallCompany").val()){
	    $.messager.alert("提示", "您还没选择安装公司，不能提交申请!", 'error');
		return;
	}
	if (!$("#txtInstaller").val()){
	    $.messager.alert("提示", "您还没选择安装人员，不能提交申请!", 'error');
		return;
	}

	$.ajax({
	    type: "GET",
	    contentType: "application/json; charset=UTF-8",
	    url: '../api/Geo',
	    data: 'latitude=' + $("#txtLatitude").numberbox("getValue") + '&longitude=' + $("#txtLongitude").numberbox("getValue"),
	    async: false,
	    success: function (data) {
	        if (data.errormsg !== 'OK') {
	            if (data.errormsg === "invalid")
	                $.messager.alert("提示", "经纬度不合理!", 'error');
	            else
	                $.messager.alert("提示", "提交时出现错误!", 'error');
	            return false;
	        }

	        $.messager.confirm("确认", "您确认提交申请吗？",
                function (r) {
                    if (r) {
                        var paras = {
                            "OrderType": $('#OrderType').combobox('getValue'),
                            "StationId": SelectedStationId,
                            "Latitude": $("#txtLatitude").val(),
                            "Longitude": $("#txtLongitude").val(),
                            "InstallCompany": $("#txtInstallCompany").val(),
                            "InstallClerk": $("#txtInstaller").val(),
                            "ApplyUserId": CurrentUserId,

                            "MyEquips": Equips
                        };
                        $.ajax({
                            type: "POST",
                            contentType: "application/json; charset=UTF-8",
                            url: '../api/NewOrder',
                            data: JSON.stringify(paras),
                            success: function (result) {
                                if (result.errormsg != "OK") {
                                    $.messager.alert("提示", "提交申请失败!", 'error');
                                }
                                else {
                                    isSubmit = true;
                                    InitThisPage();
                                    updateNeedApproveOrder();
                                    $.messager.alert({
                                        title:"提示", 
                                        msg:"申请成功!", 
                                        icon:'info', 
                                        onClose: function () {
                                            //if (parent.$('#tabs').tabs('exists', '综合查询'))
                                            //    parent.$('#tabs').tabs('select', '综合查询').tabs('getTab', '综合查询').find('iframe').eq(0).contents().find('#btnSeach').click();
                                            //else
                                            //    parent.$("#ComprehensiveQuery").click();
                                            showOrderApplyTab(result.orderid, '割接单审核'+result.myorderid);
                                        }
                                    });
                                    
                                }
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                            }
                        });
                    }
                });
	    },
	    error: function (XMLHttpRequest, textStatus, errorThrown) {
	        alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
	    }
	});


	
}

function InitThisPage(){
	SelectedStationId=-1;
	SelectedCompanyId=-1;
    SelectedClerkId=-1;
	isSubmit=false;

	Stations = [];
	//Equipments=[];
    Equips = [];	
	InstallCompanys=[];
	Installers=[];
    
	$('#dgStation').datagrid('loadData', Stations);
	//$('#dgEquipsQuery').datagrid('loadData', Equipments);
	$('#dgEquips').datagrid('loadData', Equips);
	$('#dgInstallCompanyGrid').datagrid('loadData', InstallCompanys);
	$('#dgInstallerGrid').datagrid('loadData', Installers);
	$('#StationName').textbox('setValue', '');
	$('#StationCode').textbox('setValue', '');
	$('#txtHouseName').textbox('setValue', '');
	$('#txtEquipName').textbox('setValue', '');
	$('#txtEquipCode').textbox('setValue', '');
	$('#txtCompanyNameC').textbox('setValue', '');
	$('#txtCompanyNameI').textbox('setValue', '');
	$('#txtInstallerName').textbox('setValue', '');
	$('#txtCenterName').textbox('setValue', '');
	$('#txtGroupName').textbox('setValue', '');
	$('#txtStationName').textbox('setValue', '');
	$('#txtStationCode').textbox('setValue', '');
	$('#txtStationCategory').textbox('setValue', '');
	$('#txtLongitude').textbox('setValue', '0.00000000000000000');
	$('#txtLatitude').textbox('setValue', '0.00000000000000000');
	$('#txtInstallCompany').textbox('setValue', '');
	$('#txtInstaller').textbox('setValue', '');
}



function showOrderApplyTab(orderId, subtitle) {
    var url = "CutoverAudit?orderId=" + orderId

    if (!parent.$('#tabs').tabs('exists', subtitle)) {
        var tabs = parent.$('#tabs').tabs('tabs');
        var count = 0;
        for (var i = 0; i < tabs.length; i++) {
            if (/割接单审核\w*/i.test(tabs[i].panel('options').title))
                if (++count >= 5) {
                    alertInfo('最多打开5笔割接单!');
                    return;
                }
        }
        parent.$('#tabs').tabs('add', {
            title: subtitle,
            content: '<iframe id="mytabframe"  frameborder="0"  scrolling="auto"  style="width:100%;height:100%;" src="' + url + '"></iframe>',
            //href: url,
            closable: true,
            width: $('#mainPanle').width() - 10,
            height: $('#mainPanle').height() - 26
        });

        //IE6下Frame Bug真他妹的多
        //http://www.cnblogs.com/xiaochaohuashengmi/archive/2010/08/12/1797797.html
        if (navigator.userAgent.indexOf("MSIE 6.0") > -1)//浏览器判断 如果是IE6,再重新加载一次Iframe
        {
            var ie6reloadTabFrame = parent.$('#tabs').tabs('getTab', subtitle).find('iframe')[0];
            if (ie6reloadTabFrame)
                ie6reloadTabFrame.contentWindow.location.href = url;
        }
    } else
        parent.$('#tabs').tabs('select', subtitle);
    //else {
    //    var tab = parent.$('#tabs').tabs('getTab',subtitle);
    //    parent.$('#tabs').tabs('update', { tab: tab, options: { content: '<iframe id="mytabframe"  frameborder="0"  scrolling="auto"  style="width:100%;height:100%;" src="' + url + '"></iframe>'} }).tabs('select', subtitle);
    //}

}

$(document).ready(function () {
    $('#txtInstallCompany').textbox().textbox('textbox').attr('readonly', true);
    $('#txtInstaller').textbox().textbox('textbox').attr('readonly', true);
    loadStationGridControl();
    InitBtnStationSeach();
    InitResetCondition();
    InitChooseControl();
    InitEquipsGrid();
    InitEquipsQueryGrid();
    InitBtnEquipsSeach();
    InitResetEquips();
    InitEquipsChooseControl();
    InitInstallCompanyGrid();
    InitInstallCompanyQuery();
    InitCompanyChooseControl();
    InitCompanyAddControl();
    InitInstallerGrid();
    InitInstallerQuery();
    InitInstallerChooseControl();
    InitInstallerAddControl();

    $('#OrderType').combobox({
        onChange: function (newValue, oldValue) {
            Equips = [];
            $('#dgEquips').datagrid('loadData', []);
        }
    });

    $('div.mask').remove();
});