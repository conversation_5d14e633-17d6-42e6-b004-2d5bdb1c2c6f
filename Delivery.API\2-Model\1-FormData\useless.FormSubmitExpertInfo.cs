﻿using DM.TestOrder.Model;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace testOrder.info {

    public class FormSubmitExpertInfo {
        public List<PassResultItem> PassResultList = new List<PassResultItem>();
        public int OrderFlowId {
            get;
            set;
        }
        public int OrderId {
            get;
            set;
        }
        public int OldOrderState {
            get;
            set;
        }
        public int NewOrderState {
            get;
            set;
        }
        public int StateSetUserId {
            get;
            set;
        }
        public string StateSetUserName {
            get;
            set;
        }
        public string Decision {
            get;
            set;
        }
        public string Note {
            get;
            set;
        }
        public int IsApprove {
            get;
            set;
        }
        public string FlowText {
            get;
            set;
        }
        public System.DateTime SaveTime {
            get;
            set;
        }
    }
}