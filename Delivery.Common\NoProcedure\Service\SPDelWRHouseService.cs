﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service.CMCC;

namespace Delivery.Common.NoProcedure.Service
{
    public class SPDelWRHouseService
    {
        private static SPDelWRHouseService _instance = null;

        public static SPDelWRHouseService Instance
        {
            get
            {
                if (_instance == null) _instance = new SPDelWRHouseService();
                return _instance;
            }
        }
        public string DeleteHouse(string WRHouseId, string LogonId, int checkRight = 1)
        {
            string res = "0";
            int UserId;
            int SWStationId;
            int SWHouseId;
            int HouseStatus;
            List<int> FilterUser;
            if (WRHouseId == null)
            {
                return res;
            }
            DbHelper dbHelper = new DbHelper();
            ExecuteHelper execHelper = new ExecuteHelper(dbHelper, true);
            Dictionary<string, object> realParams = new Dictionary<string, object>();
            realParams.Add("LogonId", LogonId);
            realParams.Add("WRHouseId", WRHouseId);
            DataTable temp_1 = execHelper.ExecDataTable("SP_Del_WRHouse_1", realParams);
            UserId = Convert.ToInt32(temp_1.Rows[0]["UserId"]);
            realParams.Add("UserId", UserId);
            DataTable temp_2 = execHelper.ExecDataTable("SP_Del_WRHouse_2", realParams);
            if (checkRight == 1 && temp_2.Rows.Count == 0)
            {
                return "-1";
            }

            DataTable temp_3 = execHelper.ExecDataTable("SP_Del_WRHouse_3", realParams);

            if (temp_3.Rows.Count == 0)
            {
                return "-2";
            }

            DataTable temp_4 = execHelper.ExecDataTable("SP_Del_WRHouse_4", realParams);
            if (temp_4.Rows.Count == 0)
            {
                return "-6";
            }

            DataTable temp_5 = execHelper.ExecDataTable("SP_Del_WRHouse_5", realParams);
            SWStationId = Convert.ToInt32(temp_5.Rows[0]["SWStationId"]);
            SWHouseId = Convert.ToInt32(temp_5.Rows[0]["SWHouseId"]);
            HouseStatus = Convert.ToInt32(temp_5.Rows[0]["HouseStatus"]);
            realParams.Clear();
            realParams.Add("WRHouseId", WRHouseId);
            if (HouseStatus != 3)
            {
                try
                {
                    DataTable temp_6 = execHelper.ExecDataTable("SP_Del_WRHouse_6", realParams);
                    realParams.Clear();
                    execHelper.Commit();
                    return "1";
                }
                catch (Exception e)
                {
                    execHelper.Rollback();
                    return "-3";
                }
            }
            realParams.Add("SWHouseId", SWHouseId);
            try
            {
                DataTable temp_7 = execHelper.ExecDataTable("SP_Del_WRHouse_7", realParams);
                realParams.Clear();
            }
            catch (Exception e)
            {
                execHelper.Rollback();
                return "-4";
            }

            string objectId = $"{SWStationId}.{SWHouseId}";
            try
            {
                PblConfigChangeLogService.Instance.DoExecute(objectId, 5, 3);
            }
            catch (System.Exception)
            {

                execHelper.Rollback();
                return "-7";
            }
            realParams.Add("WRHouseId", WRHouseId);
            try
            {
                DataTable temp_8 = execHelper.ExecDataTable("SP_Del_WRHouse_8", realParams);
                realParams.Clear();
            }
            catch (Exception e)
            {
                execHelper.Rollback();
                return "-6";
            }

            realParams.Add("WRHouseId", WRHouseId);
            string WRStationId, HouseName, StationName;
            DataTable temp_9 = execHelper.ExecDataTable("SP_Del_WRHouse_9", realParams);
            realParams.Clear();
            WRStationId = temp_9.Rows[0]["WRStationId"].ToString();
            HouseName = temp_9.Rows[0]["HouseName"].ToString();
            realParams.Add("WRStationId", WRStationId);
            DataTable temp_10 = execHelper.ExecDataTable("SP_Del_WRHouse_10", realParams);
            realParams.Clear();
            StationName = temp_10.Rows[0]["StationName"].ToString();

            int intWRStationId = Convert.ToInt32(WRStationId);
            int intWRHouseId = Convert.ToInt32(WRHouseId);
            string LastString = "SWHouseId:" + SWHouseId;
            Public_StoredService.Instance.SP_WR_OperationRecord(intWRStationId, StationName, 5, intWRHouseId, HouseName, "", LastString, LogonId);

            realParams.Add("WRHouseId", WRHouseId);
            try
            {
                DataTable temp_11 = execHelper.ExecDataTable("SP_Del_WRHouse_11", realParams);
                realParams.Clear();
            }
            catch (Exception e)
            {
                execHelper.Rollback();
                return "-5";
            }

            return "1";

        }

    }
}
