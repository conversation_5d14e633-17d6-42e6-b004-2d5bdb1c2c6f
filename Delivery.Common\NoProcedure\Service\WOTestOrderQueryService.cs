﻿using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace Delivery.Common.NoProcedure.Service
{

    public class WOTestOrderQueryService
    {
        private static WOTestOrderQueryService _instance = null;

        public static WOTestOrderQueryService Instance
        {
            get
            {
                if (_instance == null) _instance = new WOTestOrderQueryService();
                return _instance;
            }
        }

        public DataTable WOTestOrderQuery(QueryParamTestOrderQuery mypara)
        {
            DataTable res = new DataTable();
            try
            {
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                realParams.Add("UserId", mypara.UserId);
                realParams.Add("Cond_StationName", mypara.Cond_StationName);
                realParams.Add("Cond_StationGroup", mypara.Cond_StationGroup);
                realParams.Add("Cond_StationCode", mypara.Cond_StationCode);
                realParams.Add("Cond_FsuVendor", mypara.Cond_FsuVendor);
                realParams.Add("Cond_OrderType", mypara.Cond_OrderType);
                realParams.Add("Cond_ApplyTime1", mypara.Cond_ApplyTime1 == "" ? "1900-01-01 00:00:00": DateTime.Parse(mypara.Cond_ApplyTime1).ToString("yyyy-MM-dd HH:mm:ss"));
                realParams.Add("Cond_ApplyTime2", mypara.Cond_ApplyTime2 == "" ? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") : DateTime.Parse(mypara.Cond_ApplyTime2).ToString("yyyy-MM-dd HH:mm:ss"));
                realParams.Add("Cond_ApproveTime1", mypara.Cond_ApproveTime1 == "" ? "1900-01-01 00:00:00" : DateTime.Parse(mypara.Cond_ApproveTime1).ToString("yyyy-MM-dd HH:mm:ss"));
                realParams.Add("Cond_ApproveTime2", mypara.Cond_ApproveTime2 == "" ? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") : DateTime.Parse(mypara.Cond_ApproveTime2).ToString("yyyy-MM-dd HH:mm:ss"));
                realParams.Add("Cond_OrderState", mypara.Cond_OrderState);

                using (DbHelper dbHelper = new DbHelper())
                {
                    ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                    DataTable dt1 = execHelper.ExecDataTable("WO_TestOrder_Query_getMaxRowCount", null);

                    int returnCount = -1;
                    if (!Convert.IsDBNull(dt1.Rows[0]["MaxRowCount"]))
                    {
                        returnCount = (int)dt1.Rows[0]["MaxRowCount"];
                    }

                    int OrderType = -1;
                    if (mypara.Cond_OrderType.Equals("新装入网"))
                    {
                        OrderType = 0;
                    }
                    else if (mypara.Cond_OrderType.Equals("维护巡检"))
                    {
                        OrderType = 1;
                    }

                    int OrderState = -1;
                    if (mypara.Cond_OrderState.Equals("入网申请"))
                    {
                        OrderState = 1;
                    }
                    else if (mypara.Cond_OrderState.Equals("专家组审核"))
                    {
                        OrderState = 2;
                    }
                    else if (mypara.Cond_OrderState.Equals("入网复审"))
                    {
                        OrderState = 3;
                    }
                    else if (mypara.Cond_OrderState.Equals("归档"))
                    {
                        OrderState = 4;
                    }

                    DataTable dt2 = execHelper.ExecDataTable("WO_TestOrder_Query_getRoleIdAndName", realParams);
                    int roleId = -999;
                    string roleName = "";
                    if (!Convert.IsDBNull(dt2.Rows[0]["RoleId"]))
                    {
                        roleId = (int)dt2.Rows[0]["RoleId"];
                    }
                    if (!Convert.IsDBNull(dt2.Rows[0]["RoleName"]))
                    {
                        roleName = (string)dt2.Rows[0]["RoleName"];
                    }

                    if (returnCount != -1) {
                        realParams.Add("ReturnCount", returnCount);
                        realParams.Add("LimitCount", true);
                    }
                    realParams.Add("OrderState", OrderState);
                    realParams.Add("OrderType", OrderType);
                    realParams.Add("RoleName", roleName);
                    res = execHelper.ExecDataTable("WO_TestOrder_Query_getResult", realParams);
                }
            }
            catch (Exception ex) {
                Logger.Log(ex);
                return res;
            }
            return res;
        }
    }


    public class QueryParamTestOrderQuery
    {
        public int UserId = -1; //	    用户ID
        public string Cond_StationName = ""; //		站址名称	
        public string Cond_StationGroup = ""; //	站址名称	
        public string Cond_StationCode = ""; //    站址编码						
        public string Cond_FsuVendor = ""; //	FSU厂家			
        public string Cond_OrderType = ""; //.	割接类型																		
        public string Cond_ApplyTime1 = ""; //	申请开始日期
        public string Cond_ApplyTime2 = ""; //	申请截止日期					
        public string Cond_ApproveTime1 = ""; //	归档开始日期						
        public string Cond_ApproveTime2 = ""; //	归档截至日期																	
        public string Cond_OrderState = ""; //(255)=''--11.	申请单状态
    }
}
