﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;

using DM.TestOrder.Model;
using DM.TestOrder.Service.Interface;
using Delivery.API;
using Microsoft.AspNetCore.Mvc;

namespace DM.TestOrder.Controllers {

    public class RemoveController : BaseController
    {

        [HttpPost]
        public JsonResult POST([FromBody] JObject value)
        {
            var orderid = int.Parse(value["orderid"].ToString());

            var rtnmsg = TestOrderApi.Instance.RemoveOrder(orderid);

            var rtn = new
            {
                errormsg = rtnmsg
            };
            return new JsonResult(rtn);
            //var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(rtn);
            ////Debug.WriteLine("输入: "+value.ToString()+"\r\n输出: " + jsonRtn.ToString());

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};
        }

    }
}
