﻿@page
@model Delivery.Pages.CMCC.StationCategoryManagementModel
@{
}

@section Scripts {
    <script type="text/javascript" src="~/Scripts/lib/jquery-1.8.0.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="~/Scripts/common/common.js"></script>
    <script type="text/javascript" src="~/Scripts/common/ie8.js"></script>
    <script type="text/javascript" src="~/CMCC/scripts/StationCategoryManagement.js"></script>
}

<div class="mask">
    <div style="position: relative; top: 50%; left: 50%">
        <div class="loading">
            正在加载，请等待...
        </div>
    </div>
</div>
<div class="easyui-accordion" style="width: 100%;" data-options="multiple: true">
    <div title="站址类型管理" style="width: 100%; padding: 5px;">
        <table id="dg_StationCategory"></table>
    </div>
</div>
<div class="easyui-accordion" style="width: 100%; margin-top: 10px;" data-options="multiple: true">
    <div title="站址类型映射管理" style="width: 100%; padding: 5px;">
        <table id="dg_StationCategoryMapping"></table>
    </div>
</div>
<div id="toolbar" data-grid="#dg_StationCategory">
    <a id="btn_Add" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls:'icon-add',plain:true">添加</a>
    <a id="btn_Save" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls:'icon-save',plain:true">保存</a>
    <a id="btn_Delete" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls:'icon-cancel',plain:true">删除</a>
    <a id="btn_Cancel" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls:'icon-undo',plain:true">取消</a>
</div>
<div>
    <div id="toolbarMapping" data-grid="#dg_StationCategoryMapping">
        <a id="btn_EditMapping" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls:'icon-edit',plain:true">编辑</a>
        <a id="btn_SaveMapping" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls:'icon-save',plain:true">保存</a>
        <a id="btn_CancelMapping" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls:'icon-undo',plain:true">取消</a>
    </div>
</div>