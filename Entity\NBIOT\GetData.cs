﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 请求监控点数据
    /// </summary>
    public sealed class GetData : BMessage
    {
        [Newtonsoft.Json.JsonProperty(Order = 1)]
        public List<TDeviceDecription> DeviceList { get; set; }


        [JsonIgnore]
        public string DeviceID { get; set; }

        [JsonIgnore]
        public string[] IDs { get; set; }

        public GetData() : base()
        {
            MessageType = (int)BMessageType.GET_DATA;

            GetJsonDevice(IDs);
        }

        //public GetData(string fsuId, List<TDeviceDecription> deviceList)
        //    : base()
        //{
        //    MessageType = (int)BMessageType.GET_DATA;
        //    DeviceList = deviceList;
        //    FSUID = fsuId;
        //}

        public GetData(string fsuId, string deviceId, string[] ids)
            : base()
        {
            MessageType = (int)BMessageType.GET_DATA;

            FSUID = fsuId;
            DeviceID = deviceId;
            IDs = ids;

            GetJsonDevice(IDs);
        }

        private void GetJsonDevice(string[] ids)
        {
            string[] IDs = new string[ids.Length];
            for (int i = 0; i < ids.Length; i++)
            {
                string strId = ids[i].Insert(ids[i].Length, ":000");
                IDs[i] = strId;
            }
            DeviceList = new List<TDeviceDecription>();
            TDeviceDecription dev = new TDeviceDecription(DeviceID, IDs);
            DeviceList.Add(dev);
        }

        public override string Serialize()
        {
            string result = string.Empty;
            try
            {
                JsonSerializerSettings settings = new JsonSerializerSettings();
                settings.ContractResolver = new CustomContractResolver();
                TIotEntity iotEntity = new TIotEntity(this);
                result = JsonConvert.SerializeObject(iotEntity, settings);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetData.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetData.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
            }
            return result;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            string str = String.Format("{0}, {1}, {2}.{3}: ",
                MessageId, (BMessageType)MessageType, FSUID, DeviceID);
            sb.Append(str);

            foreach (string id in IDs)
            {
                sb.AppendFormat("[{0}]", id);
            }
            return sb.ToString();
        }
    }
}