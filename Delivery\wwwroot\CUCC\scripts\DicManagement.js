﻿'use strict';

var cmdApi = '../api/DicCmdCheckList',
    eventApi = '../api/DicEventCheckList',
    signalApi = '../api/DicSigCheckList';

//获取ajax的url地址
var getUrl = function (type) {
    var url = '';
    switch (type) {
        case 'cmd':
            url = cmdApi;
            break;
        case 'event':
            url = eventApi;
            break;
        case 'signal':
            url = signalApi;
            break;
        default:
            break;
    }
    return url;
};


//编辑事件
var edit = function (selector) {
    var row = $(selector).datagrid('getSelected');
    if (!row) {
        alertInfo('请选择要修改的记录！');
        return false;
    }

    var editIndex = $(selector).datagrid('getRowIndex', row);
    var editingRowIndexArr = $(selector).datagrid('getEditingRowIndexs');
    if (editingRowIndexArr.length > 0 && editingRowIndexArr[0] !== editIndex) {
        $(selector).datagrid('endEdit', editingRowIndexArr[0]);
        if ($(selector).datagrid('getChanges').length > 0) {
            alertInfo('一次仅允许修改一条，请先保存先前修改的记录！');
            $(selector).datagrid('beginEdit', editingRowIndexArr[0]);
            return false;
        }
    }
    $(selector).datagrid('beginEdit', editIndex);
};

//保存事件
var save = function (selector) {
    var arr = $(selector).datagrid('getEditingRowIndexs');
    if (arr.length > 0) {
        $(selector).datagrid('endEdit', arr[0]);
        var changes = $(selector).datagrid('getChanges');
        if (changes.length === 0)
            return;
        else if (selector === '#tbl_signal') {
            if (changes[0].IsMust === '是'
                && !(changes[0].LimitDown && changes[0].LimitUp && /^(\d+\.\d+)$|^\d+$/.test(changes[0].LimitDown) && /^(\d+\.\d+)$|^\d+$/.test(changes[0].LimitUp)
                && parseFloat(changes[0].LimitDown) <= parseFloat(changes[0].LimitUp))) {
                $.messager.alert({
                    title: '提示',
                    msg: '上下限需为数字且同时填写，下限需小于等于上限！',
                    icon: 'info',
                    onClose: function () {
                        $(selector).datagrid('beginEdit', arr[0]);
                    }
                });
                return false;
            } else if (changes[0].IsMust === '否' && (changes[0].LimitDown || changes[0].LimitUp)) {
                $.messager.alert({
                    title: '提示',
                    msg: '上下限需为空！',
                    icon: 'info',
                    onClose: function () {
                        $(selector).datagrid('beginEdit', arr[0]);
                    }
                });
                return false;
            }
        }
        $.messager.confirm("确认", "您确认保存吗？",
                function (r) {
                    if (r) {
                        $.ajax({
                            type: 'POST',
                            contentType: "application/json; charset=UTF-8",
                            url: getUrl($(selector).attr('data-type')),
                            async: false,
                            data: JSON.stringify(changes[0]),
                            success: function (data) {
                                if (data.errormsg === 'OK') {
                                    alertInfo('保存成功！');
                                    $(selector).datagrid('acceptChanges');
                                    $(selector).datagrid('doFilter');
                                } else {
                                    alertInfo('保存失败！', 'error' );
                                    $(selector).datagrid('beginEdit', arr[0]);
                                }
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                                $(selector).datagrid('beginEdit', arr[0]);
                            }
                        });
                    } else
                        $(selector).datagrid('beginEdit', arr[0]);
                });
    }
};

//取消事件
var cancel = function (selector) {
    var arr = $(selector).datagrid('getEditingRowIndexs');
    if (arr.length > 0) {
        //$(selector).datagrid('cancelEdit', arr[0]);
        $(selector).datagrid('rejectChanges');
    }
};

//初始化datagrid,toolbar
var init = function () {
    //控制字典
    $('#tbl_cmd').datagrid({
        toolbar: '#toolbar_cmd',
        showFilterBar: true,
        rownumbers: true,
        width: document.body.clientWidth * 0.98,
        scrollbarSize: 0,
        fitColumns: true,
        singleSelect: true,
        striped: true,
        pagination: true,
        pageSize: 20,
        loadFilter: pagerFilter,
        columns: [[
            { field: 'CheckId', title: 'CheckId', width: 100, hidden: true },
            { field: 'BaseEquipmentId', title: '基类设备ID', width: 60 },
            { field: 'BaseEquipmentName', title: '基类设备名称', width: 100 },
            { field: 'BaseTypeId', title: '基类ID', width: 100 },
            { field: 'BaseTypeName', title: '基类名称', width: 100 },
            { field: 'CheckType', title: '类型', width: 30 },
            {
                field: 'IsMust', title: '是否必须', width: 30,
                editor: {
                    type: 'combobox',
                    options:
                        {
                            editable: false,
                            valueField: 'value',
                            textField: 'text',
                            data: [
                                { text: '是', value: '是' },
                                { text: '否', value: '否' }
                            ]
                        }
                }
            },
            { field: 'Note', title: '备注', width: 100 }
        ]],
        onDblClickRow: function (index, row) {
            edit($('#tbl_cmd').selector);
        }
    });
    $('#tbl_cmd').datagrid('enableFilter', [{
        field: 'IsMust',
        type: 'combobox',
        options: {
            panelHeight: 'auto',
            data: [{ value: '', text: 'All' }, { value: '是', text: '是' }, { value: '否', text: '否' }],
            onChange: function (value) {
                if (!value) {
                    $('#tbl_cmd').datagrid('removeFilterRule', 'IsMust');
                } else {
                    $('#tbl_cmd').datagrid('addFilterRule', {
                        field: 'IsMust',
                        op: 'equal',
                        value: value
                    });
                }
                $('#tbl_cmd').datagrid('doFilter');
            }
        }
    }]);

    //事件字典
    $('#tbl_event').datagrid({
        toolbar: '#toolbar_event',
        rownumbers: true,
        width: document.body.clientWidth * 0.98,
        scrollbarSize: 0,
        fitColumns: true,
        singleSelect: true,
        striped: true,
        pagination: true,
        pageSize: 20,
        loadFilter: pagerFilter,
        columns: [[
            { field: 'CheckId', title: 'CheckId', width: 100, hidden: true },
            { field: 'BaseEquipmentId', title: '基类设备ID', width: 60 },
            { field: 'BaseEquipmentName', title: '基类设备名称', width: 100 },
            { field: 'BaseTypeId', title: '基类ID', width: 100 },
            { field: 'BaseTypeName', title: '基类名称', width: 100 },
            { field: 'CheckType', title: '类型', width: 30 },
            {
                field: 'IsMust', title: '是否必须', width: 30,
                editor: {
                    type: 'combobox',
                    options:
                        {
                            editable: false,
                            valueField: 'value',
                            textField: 'text',
                            data: [
                                { text: '是', value: '是' },
                                { text: '否', value: '否' }
                            ]
                        }
                }
            },
            { field: 'Note', title: '备注', width: 100 }
        ]],
        onDblClickRow: function (index, row) {
            edit($('#tbl_event').selector);
        }
    });
    $('#tbl_event').datagrid('enableFilter', [{
        field: 'IsMust',
        type: 'combobox',
        options: {
            panelHeight: 'auto',
            data: [{ value: '', text: 'All' }, { value: '是', text: '是' }, { value: '否', text: '否' }],
            onChange: function (value) {
                if (!value) {
                    $('#tbl_event').datagrid('removeFilterRule', 'IsMust');
                } else {
                    $('#tbl_event').datagrid('addFilterRule', {
                        field: 'IsMust',
                        op: 'equal',
                        value: value
                    });
                }
                $('#tbl_event').datagrid('doFilter');
            }
        }
    }]);

    //信号字典
    $('#tbl_signal').datagrid({
        toolbar: '#toolbar_signal',
        rownumbers: true,
        width: document.body.clientWidth * 0.98,
        scrollbarSize: 0,
        fitColumns: true,
        singleSelect: true,
        striped: true,
        pagination: true,
        pageSize: 20,
        loadFilter: pagerFilter,
        columns: [[
            { field: 'CheckId', title: 'CheckId', width: 100, hidden: true },
            { field: 'BaseEquipmentId', title: '基类设备ID', width: 60 },
            { field: 'BaseEquipmentName', title: '基类设备名称', width: 100 },
            { field: 'BaseTypeId', title: '基类ID', width: 100 },
            { field: 'BaseTypeName', title: '基类名称', width: 100 },
            { field: 'CheckType', title: '类型', width: 30 },
            {
                field: 'IsMust', title: '是否必须', width: 30,
                editor: {
                    type: 'combobox',
                    options:
                        {
                            editable: false,
                            valueField: 'value',
                            textField: 'text',
                            data: [
                                { text: '是', value: '是' },
                                { text: '否', value: '否' }
                            ],
                            onChange: function (newValue, oldValue) {
                                if (newValue === '否') {
                                    var index = $('#tbl_signal').datagrid('getEditingRowIndexs')[0];
                                    var editors = $(tbl_signal).datagrid('getEditors', index);
                                    editors[1].target.textbox('setValue', '');
                                    editors[2].target.textbox('setValue', '');
                                }
                            }
                        }
                }
            },
            { field: 'LimitDown', title: '下限', width: 50, editor: { type: 'textbox' } },
            { field: 'LimitUp', title: '上限', width: 50, editor: { type: 'textbox' } },
            { field: 'Note', title: '备注', width: 100 }
        ]],
        onDblClickRow: function (index, row) {
            edit($('#tbl_signal').selector);
        }
    });
    $('#tbl_signal').datagrid('enableFilter', [{
        field: 'IsMust',
        type: 'combobox',
        options: {
            panelHeight: 'auto',
            data: [{ value: '', text: 'All' }, { value: '是', text: '是' }, { value: '否', text: '否' }],
            onChange: function (value) {
                if (!value) {
                    $('#tbl_signal').datagrid('removeFilterRule', 'IsMust');
                } else {
                    $('#tbl_signal').datagrid('addFilterRule', {
                        field: 'IsMust',
                        op: 'equal',
                        value: value
                    });
                }
                $('#tbl_signal').datagrid('doFilter');
            }
        }
    }]);

    //编辑按钮
    $('#btn_EditCmd,#btn_EditEvent,#btn_EditSignal').on('click', function () {
        edit($(this).parent().attr('data-grid'));
    });

    //保存按钮
    $('#btn_SaveCmd,#btn_SaveEvent,#btn_SaveSignal').on('click', function () {
        save($(this).parent().attr('data-grid'));
    });

    //取消按钮
    $('#btn_CancelCmd,#btn_CancelEvent,#btn_CancelSignal').on('click', function () {
        cancel($(this).parent().attr('data-grid'));
    });
};

//加载数据
var loadData = function () {
    //控制字典
    $.ajax({
        type: "GET",
        contentType: "application/json; charset=UTF-8",
        url: cmdApi,
        async: true,
        success: function (data) {
            $('#tbl_cmd').datagrid('loadData', data);
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });

    //事件字典
    $.ajax({
        type: "GET",
        contentType: "application/json; charset=UTF-8",
        url: eventApi,
        async: true,
        success: function (data) {
            $('#tbl_event').datagrid('loadData', data);
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });

    //信号字典
    $.ajax({
        type: "GET",
        contentType: "application/json; charset=UTF-8",
        url: signalApi,
        async: true,
        success: function (data) {
            $('#tbl_signal').datagrid('loadData', data);
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
};

$(function () {
    init();
    loadData();
    $('div.mask').remove();
});
