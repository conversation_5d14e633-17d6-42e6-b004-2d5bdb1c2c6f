﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B_CUCC
{

    /// <summary>
    /// GET_SUPORT_ACK协议中的端口信息结构
    /// </summary>
    public sealed class TPortInfo
    {
        /// <summary>
        /// 端口编号，用数字表示是几号端口
        /// </summary>
        public string PortNo { get; set; }

        /// <summary>
        /// 端口名称，无名称则为空””
        /// </summary>
        public string PortName { get; set; }

        /// <summary>
        /// 端口类型，“串口/SNMP/网口/透明口”
        /// </summary>
        public string PortType { get; set; }

        /// <summary>
        /// 端口参数，“波特率/IP地址/IP地址:端口号”
        /// </summary>
        public string Settings { get; set; }

        /// <summary>
        /// Port下的设备
        /// </summary>
        public List<Device> DeviceList { get; set; }

        public TPortInfo(string portNo, string portName, string portType, string settings, List<Device> deviceList)
        {
            PortNo = portNo;
            PortName = portName;
            PortType = portType;
            Settings = settings;
            DeviceList = deviceList;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0},{1},{2},{3}", PortNo, PortName, PortType, Settings);
            if (DeviceList.Count > 0)
            {
                foreach (Device de in DeviceList)
                {
                    sb.AppendFormat("[{0}]", de.PortInfoToString());
                }
            }
            return sb.ToString();
        }

    }
}
