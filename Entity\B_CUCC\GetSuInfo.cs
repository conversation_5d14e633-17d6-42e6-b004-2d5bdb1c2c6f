﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class GetSuInfo : BMessage
    {
        public GetSuInfo(string suids, string surids) : base()
        {
            MessageType = (int)BMessageType.GET_SUINFO;
            SUId = suids;
            SURId = surids;
        }
        public override string Serialize()
        {
            try
            {
                XmlDocument xmldoc = CreateXmlDocument();
                AppendPK_Type(xmldoc, "Request", BMessageType.GET_SUINFO.ToString(), MessageType.ToString());

                XmlElement xe2 = xmldoc.CreateElement("Info");
                XmlElement xe21 = xmldoc.CreateElement("SUId");
                XmlElement xe22 = xmldoc.CreateElement("SURId");
                xe21.InnerText = SUId;
                xe22.InnerText = SURId;
                xe2.AppendChild(xe21);
                xe2.AppendChild(xe22);
                XmlNode root = xmldoc.SelectSingleNode("Request");
                root.AppendChild(xe2);

                StringXML = xmldoc.InnerXml;
                entityLogger.DebugFormat("GetSuInfo.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmldoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetSuInfo.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetSuInfo.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1},{2},{3},{4}", MessageId, (BMessageType)MessageType, MessageType, SUId, SURId);
            return sb.ToString();
        }
    }
}
