﻿ 
 /*链接样式*/
a {
  color: #369bd7;
  text-decoration: none;
}
a:hover {
  color: #2071a1;
  text-decoration: underline;
}

.row-fluid {
  width: 100%;
  *zoom: 1;
}
.row-fluid:before,
.row-fluid:after {
  display: table;
  content: "";
}
.row-fluid:after {
  clear: both;
}
/*----------------------------*/
.row-fluid .span
{
  display: block;
  width: 100%;
  min-height: 28px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
  float: left;
  margin-left: 2.127659574%;
  *margin-left: 2.0744680846382977%;
    }
/*----------------------------*/
.row-fluid [class*="span"] {
  display: block;
  width: 100%;
  min-height: 28px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
  float: left;
  margin-left: 2.127659574%;
  *margin-left: 2.0744680846382977%;
}
/*----------------------------*/
.row-fluid .ie6firstchild{margin-left: 0px}
/*----------------------------*/
.row-fluid [class*="span"]:first-child {
  margin-left: 0;
}
.row-fluid .span12 {
  width: 99.99999998999999%;
  *width: 99.94680850063828%;
}
.row-fluid .span11 {
  width: 91.489361693%;
  *width: 91.4361702036383%;
}
.row-fluid .span10 {
  width: 82.97872339599999%;
  *width: 82.92553190663828%;
}
.row-fluid .span9 {
  width: 74.468085099%;
  *width: 74.4148936096383%;
}
.row-fluid .span8 {
  width: 65.95744680199999%;
  *width: 65.90425531263828%;
}
.row-fluid .span7 {
  width: 57.446808505%;
  *width: 57.3936170156383%;
}
.row-fluid .span6 {
  width: 48.93617020799999%;
  *width: 48.88297871863829%;
}
.row-fluid .span5 {
  width: 40.425531911%;
  *width: 40.3723404216383%;
}
.row-fluid .span4 {
  width: 31.914893614%;
  *width: 31.8617021246383%;
}
.row-fluid .span3 {
  width: 20.404255317%;
  *width: 20.3510638276383%; /*width: 23 修改为20*/
}
.row-fluid .span2 {
  width: 14.89361702%;
  *width: 14.8404255306383%;
}
.row-fluid .span1 {
  width: 6.382978723%;
  *width: 6.329787233638298%;
}

 
.active .icon32.icon-add,.icon32.icon-add,.icon32.icon-add:hover {background-position : -32px -256px ;}
.active .icon32.icon-alert,.icon32.icon-alert,.icon32.icon-alert:hover {background-position : -128px -256px ;}
.active .icon32.icon-archive,.icon32.icon-archive,.icon32.icon-archive:hover {background-position : -64px -288px ;}
.active .icon32.icon-arrow-4diag,.icon32.icon-arrow-4diag,.icon32.icon-arrow-4diag:hover {background-position : -416px -64px ;}
.active .icon32.icon-arrow-e,.icon32.icon-arrow-e,.icon32.icon-arrow-e:hover {background-position : -64px -64px ;}
.active .icon32.icon-arrow-e-w,.icon32.icon-arrow-e-w,.icon32.icon-arrow-e-w:hover {background-position : -320px -64px ;}
.active .icon32.icon-arrow-n,.icon32.icon-arrow-n,.icon32.icon-arrow-n:hover {background-position : 0 -64px ;}
.active .icon32.icon-arrow-n-s,.icon32.icon-arrow-n-s,.icon32.icon-arrow-n-s:hover {background-position : -256px -64px ;}
.active .icon32.icon-arrow-ne,.icon32.icon-arrow-ne,.icon32.icon-arrow-ne:hover {background-position : -32px -64px ;}
.active .icon32.icon-arrow-ne-sw,.icon32.icon-arrow-ne-sw,.icon32.icon-arrow-ne-sw:hover {background-position : -288px -64px ;}
.active .icon32.icon-arrow-nesw,.icon32.icon-arrow-nesw,.icon32.icon-arrow-nesw:hover {background-position : -384px -64px ;}
.active .icon32.icon-arrow-nw,.icon32.icon-arrow-nw,.icon32.icon-arrow-nw:hover {background-position : -224px -64px ;}
.active .icon32.icon-arrow-s,.icon32.icon-arrow-s,.icon32.icon-arrow-s:hover {background-position : -128px -64px ;}
.active .icon32.icon-arrow-se,.icon32.icon-arrow-se,.icon32.icon-arrow-se:hover {background-position : -96px -64px ;}
.active .icon32.icon-arrow-se-nw,.icon32.icon-arrow-se-nw,.icon32.icon-arrow-se-nw:hover {background-position : -352px -64px ;}
.active .icon32.icon-arrow-sw,.icon32.icon-arrow-sw,.icon32.icon-arrow-sw:hover {background-position : -160px -64px ;}
.active .icon32.icon-arrow-w,.icon32.icon-arrow-w,.icon32.icon-arrow-w:hover {background-position : -192px -64px ;}
.active .icon32.icon-arrowrefresh-e,.icon32.icon-arrowrefresh-e,.icon32.icon-arrowrefresh-e:hover {background-position : -320px -128px ;}
.active .icon32.icon-arrowrefresh-n,.icon32.icon-arrowrefresh-n,.icon32.icon-arrowrefresh-n:hover {background-position : -288px -128px ;}
.active .icon32.icon-arrowrefresh-s,.icon32.icon-arrowrefresh-s,.icon32.icon-arrowrefresh-s:hover {background-position : -352px -128px ;}
.active .icon32.icon-arrowrefresh-w,.icon32.icon-arrowrefresh-w,.icon32.icon-arrowrefresh-w:hover {background-position : -256px -128px ;}
.active .icon32.icon-arrowreturn-en,.icon32.icon-arrowreturn-en,.icon32.icon-arrowreturn-en:hover {background-position : -224px -128px ;}
.active .icon32.icon-arrowreturn-es,.icon32.icon-arrowreturn-es,.icon32.icon-arrowreturn-es:hover {background-position : -160px -128px ;}
.active .icon32.icon-arrowreturn-ne,.icon32.icon-arrowreturn-ne,.icon32.icon-arrowreturn-ne:hover {background-position : -64px -128px ;}
.active .icon32.icon-arrowreturn-nw,.icon32.icon-arrowreturn-nw,.icon32.icon-arrowreturn-nw:hover {background-position : -96px -128px ;}
.active .icon32.icon-arrowreturn-se,.icon32.icon-arrowreturn-se,.icon32.icon-arrowreturn-se:hover {background-position : 0 -128px ;}
.active .icon32.icon-arrowreturn-sw,.icon32.icon-arrowreturn-sw,.icon32.icon-arrowreturn-sw:hover {background-position : -32px -128px ;}
.active .icon32.icon-arrowreturn-wn,.icon32.icon-arrowreturn-wn,.icon32.icon-arrowreturn-wn:hover {background-position : -192px -128px ;}
.active .icon32.icon-arrowreturn-ws,.icon32.icon-arrowreturn-ws,.icon32.icon-arrowreturn-ws:hover {background-position : -128px -128px ;}
.active .icon32.icon-arrowstop-e,.icon32.icon-arrowstop-e,.icon32.icon-arrowstop-e:hover {background-position : -352px 0 ;}
.active .icon32.icon-arrowstop-n,.icon32.icon-arrowstop-n,.icon32.icon-arrowstop-n:hover {background-position : -320px 0 ;}
.active .icon32.icon-arrowstop-s,.icon32.icon-arrowstop-s,.icon32.icon-arrowstop-s:hover {background-position : -384px 0 ;}
.active .icon32.icon-arrowstop-w,.icon32.icon-arrowstop-w,.icon32.icon-arrowstop-w:hover {background-position : -416px 0 ;}
.active .icon32.icon-arrowthick-e,.icon32.icon-arrowthick-e,.icon32.icon-arrowthick-e:hover {background-position : -64px -96px ;}
.active .icon32.icon-arrowthick-n,.icon32.icon-arrowthick-n,.icon32.icon-arrowthick-n:hover {background-position : 0 -96px ;}
.active .icon32.icon-arrowthick-ne,.icon32.icon-arrowthick-ne,.icon32.icon-arrowthick-ne:hover {background-position : -32px -96px ;}
.active .icon32.icon-arrowthick-nw,.icon32.icon-arrowthick-nw,.icon32.icon-arrowthick-nw:hover {background-position : -224px -96px ;}
.active .icon32.icon-arrowthick-s,.icon32.icon-arrowthick-s,.icon32.icon-arrowthick-s:hover {background-position : -128px -96px ;}
.active .icon32.icon-arrowthick-se,.icon32.icon-arrowthick-se,.icon32.icon-arrowthick-se:hover {background-position : -96px -96px ;}
.active .icon32.icon-arrowthick-sw,.icon32.icon-arrowthick-sw,.icon32.icon-arrowthick-sw:hover {background-position : -160px -96px ;}
.active .icon32.icon-arrowthick-w,.icon32.icon-arrowthick-w,.icon32.icon-arrowthick-w:hover {background-position : -192px -96px ;}
.active .icon32.icon-attachment,.icon32.icon-attachment,.icon32.icon-attachment:hover {background-position : -160px -288px ;}
.active .icon32.icon-audio,.icon32.icon-audio,.icon32.icon-audio:hover {background-position : -416px -256px ;}
.active .icon32.icon-basket,.icon32.icon-basket,.icon32.icon-basket:hover {background-position : -288px -256px ;}
.active .icon32.icon-book,.icon32.icon-book,.icon32.icon-book:hover {background-position : -128px -160px ;}
.active .icon32.icon-book-empty,.icon32.icon-book-empty,.icon32.icon-book-empty:hover {background-position : -160px -160px ;}
.active .icon32.icon-bookmark,.icon32.icon-bookmark,.icon32.icon-bookmark:hover {background-position : -288px -160px ;}
.active .icon32.icon-briefcase,.icon32.icon-briefcase,.icon32.icon-briefcase:hover {background-position : -480px -224px ;}
.active .icon32.icon-bullet-off,.icon32.icon-bullet-off,.icon32.icon-bullet-off:hover {background-position : -416px -96px ;}
.active .icon32.icon-bullet-on,.icon32.icon-bullet-on,.icon32.icon-bullet-on:hover {background-position : -384px -96px ;}
.active .icon32.icon-calendar,.icon32.icon-calendar,.icon32.icon-calendar:hover {background-position : -32px -224px ;}
.active .icon32.icon-cancel,.icon32.icon-cancel,.icon32.icon-cancel:hover {background-position : -352px -160px ;}
.active .icon32.icon-carat-1-e,.icon32.icon-carat-1-e,.icon32.icon-carat-1-e:hover {background-position : -64px -32px ;}
.active .icon32.icon-carat-1-n,.icon32.icon-carat-1-n,.icon32.icon-carat-1-n:hover {background-position : 0 -32px ;}
.active .icon32.icon-carat-1-ne,.icon32.icon-carat-1-ne,.icon32.icon-carat-1-ne:hover {background-position : -32px -32px ;}
.active .icon32.icon-carat-1-nw,.icon32.icon-carat-1-nw,.icon32.icon-carat-1-nw:hover {background-position : -224px -32px ;}
.active .icon32.icon-carat-1-s,.icon32.icon-carat-1-s,.icon32.icon-carat-1-s:hover {background-position : -128px -32px ;}
.active .icon32.icon-carat-1-se,.icon32.icon-carat-1-se,.icon32.icon-carat-1-se:hover {background-position : -96px -32px ;}
.active .icon32.icon-carat-1-sw,.icon32.icon-carat-1-sw,.icon32.icon-carat-1-sw:hover {background-position : -160px -32px ;}
.active .icon32.icon-carat-1-w,.icon32.icon-carat-1-w,.icon32.icon-carat-1-w:hover {background-position : -192px -32px ;}
.active .icon32.icon-carat-2-ew,.icon32.icon-carat-2-ew,.icon32.icon-carat-2-ew:hover {background-position : -288px -32px ;}
.active .icon32.icon-carat-2-ns,.icon32.icon-carat-2-ns,.icon32.icon-carat-2-ns:hover {background-position : -256px -32px ;}
.active .icon32.icon-cart,.icon32.icon-cart,.icon32.icon-cart:hover {background-position : -156px -128px ;}
.active .icon32.smallstation,.icon32.smallstation,.icon32.smallstation:hover {background-position : 0px 0px ;}
.active .icon32.icon-check,.icon32.icon-check,.icon32.icon-check:hover {background-position : -4-4 -256px -256px ;}
.active .icon32.icon-check,.icon32.icon-cn-check,.icon32.icon-check:hover {background-position : -416px -32px ;}
.active .icon32.icon-clipboard,.icon32.icon-clipboard,.icon32.icon-clipboard:hover {background-position : -160px -192px ;}
.active .icon32.icon-clock,.icon32.icon-clock,.icon32.icon-clock:hover {background-position : -64px -224px ;}
.active .icon32.icon-close,.icon32.icon-close,.icon32.icon-close:hover {background-position : -384px -32px ;}
.active .icon32.icon-comment,.icon32.icon-comment,.icon32.icon-comment:hover {background-position : -224px -256px ;}
.active .icon32.icon-comment-text,.icon32.icon-comment-text,.icon32.icon-comment-text:hover {background-position : -160px -256px ;}
.active .icon32.icon-comment-video,.icon32.icon-comment-video,.icon32.icon-comment-video:hover {background-position : -192px -256px ;}

.active .icon32.icon-contacts,.icon32.icon-contacts,.icon32.icon-contacts:hover {background-position : -352px -224px ;}
.active .icon32.icon-copy,.icon32.icon-copy,.icon32.icon-copy:hover {background-position : -288px -192px ;}
.active .icon32.icon-cross,.icon32.icon-cross,.icon32.icon-cross:hover {background-position : 0 -256px ;}
.active .icon32.icon-date,.icon32.icon-date,.icon32.icon-date:hover {background-position : 0 -224px ;}
.active .icon32.icon-doc,.icon32.icon-doc,.icon32.icon-doc:hover {background-position : -384px -192px ;}
.active .icon32.icon-document,.icon32.icon-document,.icon32.icon-document:hover {background-position : -448px -192px ;}
.active .icon32.icon-edit,.icon32.icon-edit,.icon32.icon-edit:hover {background-position : -224px -192px ;}

.active .icon32.icon-envelope-open,.icon32.icon-envelope-open,.icon32.icon-envelope-open:hover {background-position : -128px -224px ;}
.active .icon32.icon-extlink,.icon32.icon-extlink,.icon32.icon-extlink:hover {background-position : -480px -64px ;}
.active .icon32.icon-flag,.icon32.icon-flag,.icon32.icon-flag:hover {background-position : -256px -160px ;}
.active .icon32.icon-folder-collapsed,.icon32.icon-folder-collapsed,.icon32.icon-folder-collapsed:hover {background-position : -192px -160px ;}
.active .icon32.icon-folder-open,.icon32.icon-folder-open,.icon32.icon-folder-open:hover {background-position : -224px -160px ;}
.active .icon32.icon-gear,.icon32.icon-gear,.icon32.icon-gear:hover {background-position : 0 -192px ;}
.active .icon32.icon-globe,.icon32.icon-globe,.icon32.icon-globe:hover {background-position : -320px -224px ;}
.active .icon32.icon-heart,.icon32.icon-heart,.icon32.icon-heart:hover {background-position : -320px -160px ;}
.active .icon32.icon-help,.icon32.icon-help,.icon32.icon-help:hover {background-position : -448px -32px ;}
.active .icon32.icon-home,.icon32.icon-home,.icon32.icon-home:hover {background-position : 0 -160px ;}
.active .icon32.icon-image,.icon32.icon-image,.icon32.icon-image:hover {background-position : -416px -224px ;}
.active .icon32.icon-inbox,.icon32.icon-inbox,.icon32.icon-inbox:hover {background-position : -32px -288px ;}
.active .icon32.icon-info,.icon32.icon-info,.icon32.icon-info:hover {background-position : -96px -256px ;}
.active .icon32.icon-key,.icon32.icon-key,.icon32.icon-key:hover {background-position : -128px -192px ;}
.active .icon32.icon-lightbulb,.icon32.icon-lightbulb,.icon32.icon-lightbulb:hover {background-position : -480px -160px ;}
.active .icon32.icon-link,.icon32.icon-link,.icon32.icon-link:hover {background-position : -224px -224px ;}
.active .icon32.icon-locked,.icon32.icon-locked,.icon32.icon-locked:hover {background-position : -64px -192px ;}
.active .icon32.icon-mail-closed,.icon32.icon-mail-closed,.icon32.icon-mail-closed:hover {background-position : -160px -224px ;}
.active .icon32.icon-mail-open,.icon32.icon-mail-open,.icon32.icon-mail-open:hover {background-position : -192px -224px ;}
.active .icon32.icon-messages,.icon32.icon-messages,.icon32.icon-messages:hover {background-position : -320px -256px ;}
.active .icon32.icon-minus,.icon32.icon-minus,.icon32.icon-minus:hover {background-position : -352px -32px ;}
.active .icon32.icon-newwin,.icon32.icon-newwin,.icon32.icon-newwin:hover {background-position : -448px -64px ;}
.active .icon32.icon-note,.icon32.icon-note,.icon32.icon-note:hover {background-position : -320px -192px ;}
.active .icon32.icon-notice,.icon32.icon-notice,.icon32.icon-notice:hover {background-position : -480px -32px ;}
.active .icon32.icon-page,.icon32.icon-page,.icon32.icon-page:hover {background-position : -256px -192px ;}
.active .icon32.icon-pdf,.icon32.icon-pdf,.icon32.icon-pdf:hover {background-position : -352px -192px ;}
.active .icon32.icon-pin,.icon32.icon-pin,.icon32.icon-pin:hover {background-position : -416px -160px ;}
.active .icon32.icon-plus,.icon32.icon-plus,.icon32.icon-plus:hover {background-position : -320px -32px ;}
.active .icon32.icon-print,.icon32.icon-print,.icon32.icon-print:hover {background-position : -64px -160px ;}
.active .icon32.icon-profile,.icon32.icon-profile,.icon32.icon-profile:hover {background-position : -384px -224px ;}
.active .icon32.icon-redo,.icon32.icon-redo,.icon32.icon-redo:hover {background-position : -288px -96px ;}
.active .icon32.icon-refresh,.icon32.icon-refresh,.icon32.icon-refresh:hover {background-position : -352px -96px ;}
.active .icon32.icon-remove,.icon32.icon-remove,.icon32.icon-remove:hover {background-position : -64px -256px ;}
.active .icon32.icon-reply,.icon32.icon-reply,.icon32.icon-reply:hover {background-position : -96px -288px ;}
.active .icon32.icon-replyall,.icon32.icon-replyall,.icon32.icon-replyall:hover {background-position : -320px -96px ;}
.active .icon32.icon-rssfeed,.icon32.icon-rssfeed,.icon32.icon-rssfeed:hover {background-position : -480px -128px ;}
.active .icon32.icon-save,.icon32.icon-save,.icon32.icon-save:hover {background-position : -96px -160px ;}
.active .icon32.icon-scissors,.icon32.icon-scissors,.icon32.icon-scissors:hover {background-position : -192px -192px ;}
.active .icon32.icon-script,.icon32.icon-script,.icon32.icon-script:hover {background-position : -480px -192px ;}
.active .icon32.icon-search,.icon32.icon-search,.icon32.icon-search:hover {background-position : -384px -128px ;}
.active .icon32.icon-sent,.icon32.icon-sent,.icon32.icon-sent:hover {background-position : -128px -288px ;}
.active .icon32.icon-shuffle,.icon32.icon-shuffle,.icon32.icon-shuffle:hover {background-position : -480px 0 ;}
.active .icon32.icon-square-minus,.icon32.icon-square-minus,.icon32.icon-square-minus:hover {background-position : -352px -480px ;}
.active .icon32.icon-square-plus,.icon32.icon-square-plus,.icon32.icon-square-plus:hover {background-position : -320px -480px ;}
.active .icon32.icon-star-off,.icon32.icon-star-off,.icon32.icon-star-off:hover {background-position : -480px -96px ;}

.active .icon32.icon-suitcase,.icon32.icon-suitcase,.icon32.icon-suitcase:hover {background-position : -448px -224px ;}
.active .icon32.icon-tag,.icon32.icon-tag,.icon32.icon-tag:hover {background-position : -448px -160px ;}
.active .icon32.icon-transfer-ew,.icon32.icon-transfer-ew,.icon32.icon-transfer-ew:hover {background-position : -448px 0 ;}
.active .icon32.icon-trash,.icon32.icon-trash,.icon32.icon-trash:hover {background-position : -384px -160px ;}
.active .icon32.icon-treeview-corner,.icon32.icon-treeview-corner,.icon32.icon-treeview-corner:hover {background-position : -448px -480px ;}
.active .icon32.icon-treeview-corner-minus,.icon32.icon-treeview-corner-minus,.icon32.icon-treeview-corner-minus:hover {background-position : -416px -480px ;}
.active .icon32.icon-treeview-corner-plus,.icon32.icon-treeview-corner-plus,.icon32.icon-treeview-corner-plus:hover {background-position : -384px -480px ;}
.active .icon32.icon-treeview-vertical-line,.icon32.icon-treeview-vertical-line,.icon32.icon-treeview-vertical-line:hover {background-position : -480px -480px ;}
.active .icon32.icon-triangle-e,.icon32.icon-triangle-e,.icon32.icon-triangle-e:hover {background-position : -64px 0 ;}
.active .icon32.icon-triangle-ew,.icon32.icon-triangle-ew,.icon32.icon-triangle-ew:hover {background-position : -288px 0 ;}
.active .icon32.icon-triangle-n,.icon32.icon-triangle-n,.icon32.icon-triangle-n:hover {background-position : 0 0 ;}
.active .icon32.icon-triangle-ne,.icon32.icon-triangle-ne,.icon32.icon-triangle-ne:hover {background-position : -32px 0 ;}
.active .icon32.icon-triangle-ns,.icon32.icon-triangle-ns,.icon32.icon-triangle-ns:hover {background-position : -256px 0 ;}
.active .icon32.icon-triangle-nw,.icon32.icon-triangle-nw,.icon32.icon-triangle-nw:hover {background-position : -224px 0 ;}
.active .icon32.icon-triangle-s,.icon32.icon-triangle-s,.icon32.icon-triangle-s:hover {background-position : -128px 0 ;}
.active .icon32.icon-triangle-se,.icon32.icon-triangle-se,.icon32.icon-triangle-se:hover {background-position : -96px 0 ;}
.active .icon32.icon-triangle-sw,.icon32.icon-triangle-sw,.icon32.icon-triangle-sw:hover {background-position : -160px 0 ;}
.active .icon32.icon-triangle-w,.icon32.icon-triangle-w,.icon32.icon-triangle-w:hover {background-position : -192px 0 ;}
.active .icon32.icon-undo,.icon32.icon-undo,.icon32.icon-undo:hover {background-position : -256px -96px ;}
.active .icon32.icon-unlink,.icon32.icon-unlink,.icon32.icon-unlink:hover {background-position : -256px -224px ;}
.active .icon32.icon-unlocked,.icon32.icon-unlocked,.icon32.icon-unlocked:hover {background-position : -96px -192px ;}


.active .icon32.icon-users,.icon32.icon-users,.icon32.icon-users:hover {background-position : -352px -256px ;}
.active .icon32.icon-video,.icon32.icon-video,.icon32.icon-video:hover {background-position : -384px -256px ;}
.active .icon32.icon-volume-off,.icon32.icon-volume-off,.icon32.icon-volume-off:hover {background-position : -448px -256px ;}
.active .icon32.icon-volume-on,.icon32.icon-volume-on,.icon32.icon-volume-on:hover {background-position : -480px -256px ;}
.active .icon32.icon-web,.icon32.icon-web,.icon32.icon-web:hover {background-position : -288px -224px ;}
.active .icon32.icon-wrench,.icon32.icon-wrench,.icon32.icon-wrench:hover {background-position : -32px -192px ;}
.active .icon32.icon-xls,.icon32.icon-xls,.icon32.icon-xls:hover {background-position : -416px -192px ;}
.active .icon32.icon-zoomin,.icon32.icon-zoomin,.icon32.icon-zoomin:hover {background-position : -416px -128px ;}
.active .icon32.icon-zoomout,.icon32.icon-zoomout,.icon32.icon-zoomout:hover {background-position : -448px -128px ;}


.icon32.icon-black,.icons-black .icon32,.icon32.icon-darkgray:hover {background-image : url('images/opa-icons-black32.png') ;}
.icon32.icon-blue,.icons-blue .icon32 {background-image : url('images/opa-icons-blue32.png') ;}

.icon32.icon-green,.icons-green .icon32 {background-image : url('images/opa-icons-green32.png') ;}
.icon32.icon-orange,.icons-orange .icon32 {background-image : url('images/opa-icons-orange32.png') ;}

.icon32.icon-white:hover,.icons-white .icon32:hover,.icons-gray .icon32 {background-image : url('images/opa-icons-gray32.png') ;}
.icon32.icon-white,.icons-white .icon32 {background-image : url('images/opa-icons-white32.png') ;}




.icon-user,.icon-user:active{background-image : url('images/opa-icons-red32.png') ;
         background-position : 0px -160px ;
        background-repeat : no-repeat ;
         display : inline-block ;height : 32px ;vertical-align : text-top ;width : 32px ;}
.icon-user:hover{background-image : url('images/opa-icons-gray32.png') ;
         background-position : 0px -160px ;}

.icon-star-on,.icon-star-on:active {background-image : url('images/opa-icons-color32.png') ;
           background-position : -448px -96px ;
           background-repeat : no-repeat ;
         display : inline-block ;height : 32px ;vertical-align : text-top ;width : 32px ;}
.icon-star-on:hover{background-image : url('images/opa-icons-gray32.png') ;
           background-position : -448px -96px ;}
           
.icon-compose,.icon-compose:active{background-image : url('images/opa-icons-color32.png') ;
           background-position : 0 -128px ;
           background-repeat : no-repeat ;
         display : inline-block ;height : 32px ;vertical-align : text-top ;width : 32px ;}
.icon-compose:hover{background-image : url('images/opa-icons-gray32.png') ;
           background-position : 0 -128px ;}
           
.icon-envelope-closed,.icon-envelope-closed:active{background-image : url('images/opa-icons-color32.png') ;
           background-position : 0px -192px ;
           background-repeat : no-repeat ;
         display : inline-block ;height : 32px ;vertical-align : text-top ;width : 32px ;}
.icon-envelope-closed:hover{background-image : url('images/opa-icons-gray32.png') ;
           background-position : 0px -192px ;}
           

.notification 
{
display: block;
position: absolute;
top: -10px;
right: -5px;
line-height: 16px;
height: 16px;
padding: 0 5px;
font-family: Arial, sans-serif;
color: white !important;
text-shadow: 0 1px 
hsla(0, 0%, 0%, 0.25);
border-width: 1px;
border-style: solid;
border-radius: 10px;
-webkit-border-radius: 10px;
-moz-border-radius: 10px;
-webkit-box-shadow: 0 1px 1px 
hsla(0, 0%, 0%, 0.08), inset 0 1px 
hsla(0, 100%, 100%, 0.3);
-moz-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.08), inset 0 1px rgba(255, 255, 255, 0.3);
box-shadow: 0 1px 1px 
hsla(0, 0%, 0%, 0.08), inset 0 1px 
hsla(0, 100%, 100%, 0.3);
}

.notification {
border-color: #2FABE9;
background-color: #67C2EF;
background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%, hsl(200, 80%, 82%)), color-stop(100%, hsl(200, 81%, 67%)));
background-image: -webkit-linear-gradient(top, hsl(200, 80%, 82%), hsl(200, 81%, 67%));
background-image: -moz-linear-gradient(top, hsl(200, 80%, 82%), hsl(200, 81%, 67%));
background-image: -ms-linear-gradient(top, hsl(200, 80%, 82%), hsl(200, 81%, 67%));
background-image: -o-linear-gradient(top, hsl(200, 80%, 82%), hsl(200, 81%, 67%));
background-image: linear-gradient(top, hsl(200, 80%, 82%), hsl(200, 81%, 67%));
}

.notification.green {
border-color: 	#5AAD34;
background-color: 	#78CD51;
background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%, 	hsl(101, 54%, 71%)), color-stop(100%, 	hsl(101, 55%, 56%)));
background-image: -webkit-linear-gradient(top, 	hsl(101, 54%, 71%), 	hsl(101, 55%, 56%));
background-image: -moz-linear-gradient(top, 	hsl(101, 54%, 71%), 	hsl(101, 55%, 56%));
background-image: -ms-linear-gradient(top, 	hsl(101, 54%, 71%), 	hsl(101, 55%, 56%));
background-image: -o-linear-gradient(top, 	hsl(101, 54%, 71%), 	hsl(101, 55%, 56%));
background-image: linear-gradient(top, 	hsl(101, 54%, 71%), 	hsl(101, 55%, 56%));
}

.notification.yellow {
border-color: #F4A506;
background-color: #FABB3D;
background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%, hsl(40, 95%, 76%)), color-stop(100%, hsl(40, 95%, 61%)));
background-image: -webkit-linear-gradient(top, hsl(40, 95%, 76%), hsl(40, 95%, 61%));
background-image: -moz-linear-gradient(top, hsl(40, 95%, 76%), hsl(40, 95%, 61%));
background-image: -ms-linear-gradient(top, hsl(40, 95%, 76%), hsl(40, 95%, 61%));
background-image: -o-linear-gradient(top, hsl(40, 95%, 76%), hsl(40, 95%, 61%));
background-image: linear-gradient(top, hsl(40, 95%, 76%), hsl(40, 95%, 61%));
}

.notification.red {
border-color: #FA5833;
background-color: #FA603D;
background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%, hsl(11, 95%, 76%)), color-stop(100%, hsl(11, 95%, 61%)));
background-image: -webkit-linear-gradient(top, hsl(11, 95%, 76%), hsl(11, 95%, 61%));
background-image: -moz-linear-gradient(top, hsl(11, 95%, 76%), hsl(11, 95%, 61%));
background-image: -ms-linear-gradient(top, hsl(11, 95%, 76%), hsl(11, 95%, 61%));
background-image: -o-linear-gradient(top, hsl(11, 95%, 76%), hsl(11, 95%, 61%));
background-image: linear-gradient(top, hsl(11, 95%, 76%), hsl(11, 95%, 61%));
}


.well 
{
 min-height: 20px;
  padding: 19px;
  margin:0 0 20 0;
  float:left;
  background-color: #f5f5f5;
  border: 1px solid #eee;
  border: 1px solid rgba(0, 0, 0, 0.05);
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
}

.top-block
{

font-size:14px;
text-align: center;
padding: 5px;

margin-bottom:10px;
font-weight:bold;
position: relative;
display: block;
line-height: 22px;
text-shadow: 0 0 1px #DDD;
text-decoration:none;
-webkit-box-shadow: inset 0 0 0 1px #FAFAFA;
-moz-box-shadow: inset 0 0 0 1px #FAFAFA;
box-shadow: inset 0 0 0 1px #FAFAFA;
background: -moz-linear-gradient(top,  rgba(30,87,153,0.2) 0%, rgba(125,185,232,0) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(30,87,153,0.2)), color-stop(100%,rgba(125,185,232,0)));
background: -webkit-linear-gradient(top,  rgba(30,87,153,0.2) 0%,rgba(125,185,232,0) 100%);
background: -o-linear-gradient(top,  rgba(30,87,153,0.2) 0%,rgba(125,185,232,0) 100%);
background: -ms-linear-gradient(top,  rgba(30,87,153,0.2) 0%,rgba(125,185,232,0) 100%);
background: linear-gradient(to bottom,  rgba(30,87,153,0.2) 0%,rgba(125,185,232,0) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#331e5799', endColorstr='#007db9e8',GradientType=0 );
}

.top-block span{
clear:both;
}


.top-block:hover{
text-decoration:none;
}


.box-header
{
     min-height: 20px;
  padding: 19px;
  margin:0 0 20 0;
  float:left;
  background-color: #f5f5f5;
  border: 1px solid #eee;
  border: 1px solid rgba(0, 0, 0, 0.05);
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  
border:none;
padding-top:5px;
border-bottom: 1px solid #DEDEDE;
border-radius:3px 3px 0 0;
-webkit-border-radius:3px 3px 0 0;
-moz-border-radius:3px 3px 0 0;
height:12px;
min-height:12px;
margin-bottom: 0;
cursor:move;
font-weight:bold;
font-size:16px;
background: -moz-linear-gradient(top,  rgba(255,255,255,0) 0%, rgba(0,0,0,0.1) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,0)), color-stop(100%,rgba(0,0,0,0.1)));
background: -webkit-linear-gradient(top,  rgba(255,255,255,0) 0%,rgba(0,0,0,0.1) 100%);
background: -o-linear-gradient(top,  rgba(255,255,255,0) 0%,rgba(0,0,0,0.1) 100%);
background: -ms-linear-gradient(top,  rgba(255,255,255,0) 0%,rgba(0,0,0,0.1) 100%);
background: linear-gradient(to bottom,  rgba(255,255,255,0) 0%,rgba(0,0,0,0.1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00ffffff', endColorstr='#1a000000',GradientType=0 );

}



.tbArchiveInfoContainer
{
border: 1px solid #DEDEDE;
border-radius:3px;
-webkit-border-radius:3px;
-moz-border-radius:3px;
float:left;   
display:inline;
width:30%;
height:220px;
margin-bottom: 10px;
margin-left:20px;
box-shadow: 0 0 10px rgba(189, 189, 189, 0.4);
-webkit-box-shadow: 0 0 10px rgba(189, 189, 189, 0.4);
-moz-box-shadow: 0 0 10px rgba(189, 189, 189, 0.4);
}

.box-content{
padding:10px;
margin-right:-10px;
margin-left:-10px;
}

ul.dashboard-list li{
padding: 7px 0;
list-style:none;
border-bottom: 1px solid #EBEBEB;
border-top: 1px solid white;
}

ul.dashboard-list a:hover{
text-decoration:none;
}

ul.dashboard-list{
margin:0;
}

ul.dashboard-list li a span {
display: inline-block;
font-size: 18px;
font-weight: bold;
margin-right: 10px;
text-align: right;
width: 150px;
zoom: 1;
}

.alpha {
  margin-left: 0;
}

.omega {
  margin-right: 0;
}
.margin_top_0{margin-top:0!important}
.margin_top_1
{
    margin-top:0!important;  
    vertical-align:middle;  
    display:table-cell;
}
.grid_DingZhi 
{
  margin-top:5px
}
.grid_DingZhi
{
  display: inline;
  float: left;
  margin-left: 10px;
  margin-right: 10px;
}
.grid_DingzhiLogo 
{
  display: none;
  float: left;
  margin-left: 10px;
  margin-right: 10px;
}
 
.textcenter{text-align:center}

.customerMainClear {
  clear: both;
  display: block;
  overflow: hidden;
  visibility: hidden;
  width: 0;
  height: 0;
}