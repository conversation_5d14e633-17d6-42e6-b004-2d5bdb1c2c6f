﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_GetSomeFiledName1" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT a.WRStationId,b.SWStationId,a.HouseCode,b.StationCode ,a.HouseName ,b.StationName
					FROM WR_HouseManagement a 
					INNER JOIN WR_StationManagement b ON a.WRStationId = b.WRStationId
					WHERE a.WRHouseId = CAST(@WRHouseId  AS INTEGER);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_GetSomeFiledName2" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT  WR_FsuManagement.FsuCode AS OriFsuCode 
					FROM WR_FsuManagement WHERE WR_FsuManagement.WRFsuId = CAST(@WRFsuId  AS INTEGER);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_JudgeExist1" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM WR_FsuManagement 
					WHERE WR_FsuManagement.WRFsuId != CAST(@WRFsuId  AS INTEGER) AND WR_FsuManagement.FsuCode = @FsuCode limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_JudgeExist2" grant="">
			<parameters>
				<parameter name="WRStationId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM WR_FsuManagement a 
					INNER JOIN WR_HouseManagement b ON a.WRHouseId = b.WRHouseId 
					WHERE b.WRStationId = CAST(@WRStationId  AS INTEGER) AND a.WRFsuId != CAST(@WRFsuId  AS INTEGER) AND a.FsuName = @FsuName limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_JudgeExist3" grant="">
			<parameters>
				<parameter name="SWStationId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="OriFsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM TSL_MonitorUnitCMCC WHERE TSL_MonitorUnitCMCC.StationId = CAST(@SWStationId  AS INTEGER)
					AND TSL_MonitorUnitCMCC.FSUID != @OriFsuCode AND TSL_MonitorUnitCMCC.FSUName = @FsuName limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_JudgeExist4" grant="">
			<parameters>
				<parameter name="ManufacturerId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM WR_DataItem 
					WHERE WR_DataItem.EntryId = 4 AND WR_DataItem.ItemId = CAST(@ManufacturerId  AS INTEGER) limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_GetSomeFiledName3" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT a.SWMonitorUnitId  FROM WR_FsuManagement a WHERE a.WRFsuId =CAST(@WRFsuId  AS INTEGER);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_JudgeExist5" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM WR_FsuManagement WHERE WR_FsuManagement.WRFsuId != CAST(@WRFsuId  AS INTEGER) AND WR_FsuManagement.IPAddress = @IPAddress 
					AND WR_FsuManagement.IPAddress != '' AND WR_FsuManagement.IPAddress IS NOT NULL limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_JudgeExist6" grant="">
			<parameters>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM TSL_MonitorUnitCMCC WHERE TSL_MonitorUnitCMCC.FSUIP = @IPAddress AND TSL_MonitorUnitCMCC.MonitorUnitId != CAST(@SWMonitorUnitId  AS INTEGER)  
					AND TSL_MonitorUnitCMCC.FSUIP != '' AND TSL_MonitorUnitCMCC.FSUIP IS NOT NULL limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_GetSomeFiledName4" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT  WR_FsuManagement.FsuStatus 
					FROM WR_FsuManagement 
					WHERE WR_FsuManagement.WRFsuId = CAST(@WRFsuId  AS INTEGER);	
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_GetSomeFiledName5" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
				SELECT 
					'FsuCode:' || CAST(FsuCode AS TEXT) ||
					'-FsuName:' || CAST(FsuName AS TEXT) ||
					'-IPAddress:' || CAST(IPAddress AS TEXT) ||
					'-UserName:' || CAST(UserName AS TEXT) ||
					'-IPAddress:' || CAST(IPAddress AS TEXT) ||
					'-Password:' || CAST(Password AS TEXT) ||
					'-FtpUserName:' || CAST(FtpUserName AS TEXT) ||
					'-FtpPassword:' || CAST(FtpPassword AS TEXT) ||
					'-Remark:' || CAST(Remark AS TEXT)
				FROM WR_FsuManagement 
				WHERE WRFsuId = CAST(@WRFsuId  AS INTEGER)
				LIMIT 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_UpdateStatusNotThree" grant="">
			<parameters>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRHouseId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ManufacturerId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="UserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Password" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpUserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpPassword" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Remark" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ContractNo" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ProjectName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FTPType" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="PortNumber" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE WR_FsuManagement
					SET WRHouseId = CAST(@WRHouseId  AS INTEGER)
						,FsuCode = @FsuCode
						,FsuName = @FsuName
						,IPAddress = @IPAddress
						,ManufacturerId = CAST(@ManufacturerId  AS INTEGER)
						,FsuStatus = 1 -- 设为申请入网
						,UserName = @UserName 
						,Password = @Password
						,FtpUserName = @FtpUserName
						,FtpPassword = @FtpPassword
						,Remark = @Remark 
						,ContractNo = @ContractNo
						,ProjectName = @ProjectName
						,PortNumber = CAST(@PortNumber  AS INTEGER)
                        ,FTPType = CAST(@FTPType AS INTEGER)
					WHERE WR_FsuManagement.WRFsuId = CAST(@WRFsuId  AS INTEGER) ;	
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_UpdateStatusIsThree" grant="">
			<parameters>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRHouseId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ManufacturerId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="UserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Password" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpUserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpPassword" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Remark" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ContractNo" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ProjectName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FTPType" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="PortNumber" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE WR_FsuManagement
					SET WRHouseId =  CAST(@WRHouseId  AS INTEGER)
						,FsuCode = @FsuCode
						,FsuName = @FsuName
						,IPAddress = @IPAddress
						,ManufacturerId = CAST(@ManufacturerId  AS INTEGER)
						,UserName = @UserName 
						,Password = @Password
						,FtpUserName = @FtpUserName
						,FtpPassword = @FtpPassword
						,ContractNo = @ContractNo
						,ProjectName = @ProjectName
						,Remark = @Remark 
						,PortNumber = CAST(@PortNumber  AS INTEGER)
                        ,FTPType = CAST(@FTPType AS INTEGER)
					WHERE WR_FsuManagement.WRFsuId = CAST(@WRFsuId  AS INTEGER);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_UpdateTSL_MonitorUnit" grant="">
			<parameters>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWStationId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TSL_MonitorUnit
					SET MonitorUnitName = @FsuName
						,IpAddress = @IPAddress
					WHERE TSL_MonitorUnit.MonitorUnitId = CAST(@SWMonitorUnitId  AS INTEGER) 
						AND TSL_MonitorUnit.StationId = CAST(@SWStationId  AS INTEGER) ;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_UpdateTSL_MonitorUnitCMCC" grant="">
			<parameters>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="StationName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="StationCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="HouseCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="HouseName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="UserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Password" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpUserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpPassword" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWStationId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FTPType" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TSL_MonitorUnitCMCC
					SET FSUID = @FsuCode
						,FSUName = @FsuName
						,SiteID = @StationCode
						,SiteName = @StationName
						,RoomID = @HouseCode
						,RoomName = @HouseName
						,FSUIP = @IPAddress
						,UserName = @UserName 
						,Password = @Password
						,FtpUserName = @FtpUserName
						,ExtendField1 = @FTPType
						,FtpPassword = @FtpPassword
					WHERE TSL_MonitorUnitCMCC.StationId = CAST(@SWStationId  AS INTEGER) 
						AND TSL_MonitorUnitCMCC.MonitorUnitId = CAST(@SWMonitorUnitId  AS INTEGER) ;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_GetBEquipmentInfo" grant="">
			<body>
				<![CDATA[ 
					SELECT  EquipmentTemplateId  FROM TBL_EquipmentTemplate 
					WHERE TBL_EquipmentTemplate.EquipmentTemplateName = 'BInterface-HOST设备' limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_UpdateTBL_Equipment" grant="">
			<parameters>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="BInterfaceHostT" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TBL_Equipment SET EquipmentName = concat('BInterface自诊断设备-' , @FsuName)
					WHERE EquipmentCategory = 99 AND TBL_Equipment.EquipmentTemplateId = CAST(@BInterfaceHostT  AS INTEGER)
						AND TBL_Equipment.MonitorUnitId =  CAST(@SWMonitorUnitId  AS INTEGER);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_UpdateTBL_MonitorUnitProjectInfo" grant="">
			<parameters>
				<parameter name="ContractNo" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ProjectName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TBL_MonitorUnitProjectInfo SET ContractNo = @ContractNo, ProjectName = @ProjectName
					WHERE TBL_MonitorUnitProjectInfo.MonitorUnitId = CAST(@SWMonitorUnitId  AS INTEGER) ;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_UpdateTBL_ConfigChangeMicroLog" grant="">
			<parameters>
				<parameter name="SWStationId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TBL_ConfigChangeMicroLog
					SET EditType = 2, UpdateTime = NOW()
					WHERE TBL_ConfigChangeMicroLog.ObjectId = concat(CAST(@SWStationId  AS INTEGER),'.',CAST(@SWMonitorUnitId  AS INTEGER))  AND 
					TBL_ConfigChangeMicroLog.ConfigId = 15;
				]]>
			</body>
		</procedure>





		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_GetSomeFiledName1" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT a.FsuStatus, a.FsuName,c.SWStationId, c.StationStatus,b.HouseStatus 
					FROM WR_FsuManagement a 
					INNER JOIN WR_HouseManagement b ON a.WRHouseId = b.WRHouseId
					INNER JOIN WR_StationManagement c ON b.WRStationId = c.WRStationId
					WHERE a.WRFsuId = CAST(@WRFsuId  AS INTEGER);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTSLMonitorUnit" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TSL_MonitorUnit
					(
						MonitorUnitId,MonitorUnitName,MonitorUnitCategory,MonitorUnitCode,
						StationId,IpAddress,RunMode,ConnectState,UpdateTime,
						IsSync,IsConfigOK,AppCongfigId,CanDistribute,ENABLE,InstallTime
					)
					SELECT  @SWMonitorUnitId,a.FsuName ,10 MonitorUnitCategory, CAST(@SWMonitorUnitId  AS text) MonitorUnitCode,
						c.SWStationId,a.IPAddress, 1 RunMode, 2 ConnectState, NOW() UpdateTime,
						0 IsSync, 0 IsConfigOK, 1 AppCongfigId, 1 CanDistribute,  1 ENABLE ,a.ApplyTime
					FROM WR_FsuManagement a
					INNER JOIN WR_HouseManagement b ON a.WRHouseId = b.WRHouseId 
					INNER JOIN WR_StationManagement c ON b.WRStationId = c.WRStationId
					WHERE a.WRFsuId = CAST(@WRFsuId  AS INTEGER)
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTSL_MonitorUnitCMCCExt" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
                    INSERT INTO TSL_MonitorUnitCMCCExt
                (
                MonitorUnitId,FSUID,UserName,PassWordMD5,PasswordRaw,PassWordSHA256,PassWordSM3,SFTP
                )
                SELECT SWMonitorUnitId, a.FsuCode, a.UserName, a.Password, a.OriginalPassword, a.ShaPassword, a.SmPassword, a.FTPType
                FROM WR_FsuManagement a
                INNER JOIN WR_HouseManagement b ON a.WRHouseId = b.WRHouseId
                INNER JOIN WR_StationManagement c ON b.WRStationId = c.WRStationId
                WHERE a.WRFsuId = CAST(@WRFsuId AS INTEGER);
                ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTSLMonitorUnitCMCC" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TSL_MonitorUnitCMCC
					( StationId,MonitorUnitId,FSUID,FSUName,SiteID,SiteName,RoomID,RoomName,
					  UserName,PassWord,FSUIP,FTPUserName,FTPPassWord,GetConfigFlag,ExtendField1 
					)
					SELECT c.SWStationId, CAST(@SWMonitorUnitId  AS INTEGER), a.FsuCode, a.FsuName,c.StationCode,c.StationName, b.HouseCode,b.HouseName, 
						a.UserName, a.Password, a.IPAddress, a.FtpUserName, a.FtpPassword, 0 GetConfigFlag, a.FTPType    
					FROM WR_FsuManagement a
					INNER JOIN WR_HouseManagement b ON a.WRHouseId = b.WRHouseId
					INNER JOIN WR_StationManagement c ON b.WRStationId = c.WRStationId
					WHERE a.WRFsuId = CAST(@WRFsuId  AS INTEGER);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTSLPort" grant="">
			<parameters>
				<parameter name="SWPortId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TSL_Port(PortId,MonitorUnitId,PortNo,PortName,PortType,Setting,PhoneNumber,LinkSamplerUnitId,Description)
					VALUES (CAST(@SWPortId  AS INTEGER), CAST(@SWMonitorUnitId  AS INTEGER),5,'COM5',5,'comm_host_dev.so','',0,'');
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_GetSomeFiledName2" grant="">
			<body>
				<![CDATA[ 
					SELECT a.SamplerId as SWSamplerId, a.SamplerName as SamplerUnitName, a.DllPath as swDllPath   
					FROM TSL_Sampler a
					WHERE a.SamplerType = 18 AND a.SamplerName LIKE '%BInterface-HOST%' limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTSLSamplerUnit" grant="">
			<parameters>
				<parameter name="SWSamplerUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWPortId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWSamplerId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SamplerUnitName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="swDllPath" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TSL_SamplerUnit
					(SamplerUnitId,PortId,MonitorUnitId,SamplerId,ParentSamplerUnitId,SamplerType,SamplerUnitName,Address,SpUnitInterval,DllPath,ConnectState,UpdateTime)
					VALUES 
					(CAST(@SWSamplerUnitId  AS INTEGER), CAST(@SWPortId  AS INTEGER), CAST(@SWMonitorUnitId  AS INTEGER),
					CAST(@SWSamplerId  AS INTEGER),0,18, @SamplerUnitName,1,2::float8, @swDllPath,0,NOW());	 
				]]>
			</body>
		</procedure>

		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_GetEquTemplateId" grant="">
			<body>
				<![CDATA[ 
					SELECT  EquipmentTemplateId as swEquipmentTemplateId  FROM TBL_EquipmentTemplate 
					WHERE TBL_EquipmentTemplate.EquipmentCategory = 99 AND TBL_EquipmentTemplate.EquipmentTemplateName = 'BInterface-HOST设备' limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_GetSomeFiledName11" grant="">
			<parameters>
				<parameter name="SWStationId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
                    SELECT Min(a.HouseId) AS v_DefaultHouseId from TBL_House a where a.StationId = CAST(@SWStationId AS INTEGER);
                ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTBLEquipment" grant="">
			<parameters>
				<parameter name="SWStationId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="swEquipmentId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="swEquipmentName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="swEquipmentTemplateId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWSamplerUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="v_DefaultHouseId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TBL_Equipment (StationId, EquipmentId, EquipmentName,EquipmentNo, EquipmentCategory, EquipmentType, EquipmentClass, 
							EquipmentState, Property, EquipmentTemplateId, HouseId, MonitorUnitId, SamplerUnitId, DisplayIndex, ConnectState, UpdateTime,InstalledModule)
					VALUES (@SWStationId, @swEquipmentId, @swEquipmentName,'', 99, 2, -1, 1, '',CAST(@swEquipmentTemplateId AS INTEGER), @v_DefaultHouseId, @SWMonitorUnitId, CAST(@SWSamplerUnitId AS INTEGER), 0, 2, now(),'');	
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_UpdateWRFsuManagement" grant="">
			<parameters>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
   					UPDATE WR_FsuManagement
   					SET FsuStatus = 3
   					,ApproveTime = now()
   					,SWMonitorUnitId =  CAST(@SWMonitorUnitId  AS INTEGER)
   					,RejectCause = '' -- 同时清除退回原因
   					WHERE WR_FsuManagement.WRFsuId = CAST(@WRFsuId  AS INTEGER);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTBLConfigChangeMicroLog1" grant="">
			<parameters>
				<parameter name="SWStationId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TBL_ConfigChangeMicroLog(ObjectId,ConfigId,EditType,UpdateTime)
					SELECT concat(CAST(@SWStationId  AS TEXT),'.',CAST(@SWMonitorUnitId  AS TEXT))  ObjectId, 15 ConfigId, 1 EditType, NOW() UpdateTime;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTBLConfigChangeMicroLog2" grant="">
			<parameters>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWPortId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TBL_ConfigChangeMicroLog(ObjectId,ConfigId,EditType,UpdateTime)
					SELECT concat(CAST(@SWMonitorUnitId  AS TEXT),'.',CAST(@SWPortId  AS TEXT))  ObjectId, 16 ConfigId, 1 EditType, NOW() UpdateTime;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTBLConfigChangeMicroLog3" grant="">
			<parameters>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWPortId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWSamplerUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TBL_ConfigChangeMicroLog(ObjectId,ConfigId,EditType,UpdateTime)
					SELECT concat(CAST(@SWMonitorUnitId  AS TEXT),'.',CAST(@SWPortId  AS TEXT),'.',CAST(@SWSamplerUnitId  AS TEXT)) ObjectId, 17 ConfigId, 1 EditType, NOW() UpdateTime;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTBLConfigChangeMicroLog4" grant="">
			<parameters>
				<parameter name="SWStationId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="swEquipmentId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TBL_ConfigChangeMicroLog(ObjectId,ConfigId,EditType,UpdateTime)
					SELECT concat(CAST(@SWStationId  AS TEXT),'.',CAST(@swEquipmentId  AS TEXT))  ObjectId, 3 ConfigId, 1 EditType, NOW() UpdateTime;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTBLConfigChangeMacroLog" grant="">
			<parameters>
				<parameter name="SWMonitorUnitId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TBL_ConfigChangeMacroLog(ObjectId,ConfigId,EditType,UpdateTime)
					SELECT CAST(@SWMonitorUnitId  AS TEXT)  ObjectId, 15 ConfigId, 2 EditType, NOW() UpdateTime;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_UpdateTBLConfigChangeMacroLog" grant="">
			<parameters>
				<parameter name="SWStationId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TBL_ConfigChangeMacroLog
					SET EditType = 2, UpdateTime = NOW()
					WHERE TBL_ConfigChangeMacroLog.ObjectId = CAST(@SWStationId  AS TEXT) AND TBL_ConfigChangeMacroLog.ConfigId = 1 ; 
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTBLMonitorUnitProjectInfo" grant="">
			<parameters>
				<parameter name="SWStationId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TBL_MonitorUnitProjectInfo(StationId,MonitorUnitId,ProjectName,ContractNo,InstallTime) 
					SELECT @SWStationId , SWMonitorUnitId, ProjectName, ContractNo, ApplyTime 
					FROM WR_FsuManagement 
					WHERE WR_FsuManagement.WRFsuId =CAST(@WRFsuId  AS INTEGER); 
				]]>
			</body>
		</procedure>





		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_GetSomeFiledName1" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT a.WRStationId, b.SWStationId, a.HouseCode, b.StationCode 
						FROM WR_HouseManagementCUCC a 
						INNER JOIN WR_StationManagementCUCC b ON a.WRStationId = b.WRStationId
						WHERE a.WRHouseId = CAST(@WRHouseId  AS INTEGER);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_JudgeExist1" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM WR_FsuManagementCUCC WHERE WR_FsuManagementCUCC.WRFsuId != CAST(@WRFsuId  AS INTEGER) AND WR_FsuManagementCUCC.FsuCode = @FsuCode limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_JudgeExist2" grant="">
			<parameters>
				<parameter name="WRStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM WR_FsuManagementCUCC a 
					INNER JOIN WR_HouseManagementCUCC b ON a.WRHouseId = b.WRHouseId 
					WHERE b.WRStationId = CAST(@WRStationId  AS INTEGER) AND a.FsuCode != @FsuCode AND a.FsuName = @FsuName limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_JudgeExist3" grant="">
			<parameters>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM TSL_MonitorUnitCUCC 
					WHERE TSL_MonitorUnitCUCC.StationId = CAST(@SWStationId  AS INTEGER) AND TSL_MonitorUnitCUCC.SUID != @FsuCode AND TSL_MonitorUnitCUCC.SUName = @FsuName limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_JudgeExist4" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM WR_FsuManagementCUCC 
					WHERE WR_FsuManagementCUCC.WRFsuId != CAST(@WRFsuId  AS INTEGER) AND WR_FsuManagementCUCC.IPAddress = @IPAddress limit 1;
					]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_JudgeExist5" grant="">
			<parameters>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM TSL_MonitorUnitCUCC WHERE TSL_MonitorUnitCUCC.SUIP = @IPAddress AND TSL_MonitorUnitCUCC.SUID != @FsuCode limit 1;
					]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_GetSomeFiledName2" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT  a.FsuStatus, a.SWMonitorUnitId 
					FROM WR_FsuManagementCUCC a
					WHERE a.WRFsuId = CAST(@WRFsuId  AS INTEGER);	
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_GetSomeFiledName3" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 
						'FsuCode:' || CAST(FsuCode AS TEXT) ||
						'-FsuName:' || CAST(FsuName AS TEXT) ||
						'-IPAddress:' || CAST(IPAddress AS TEXT) ||
						'-UserName:' || CAST(UserName AS TEXT) ||
						'-IPAddress:' || CAST(IPAddress AS TEXT) ||
						'-Password:' || CAST(Password AS TEXT) ||
						'-FtpUserName:' || CAST(FtpUserName AS TEXT) ||
						'-FtpPassword:' || CAST(FtpPassword AS TEXT) ||
						'-Remark:' || CAST(Remark AS TEXT) ||
						'-FsuRId:' || COALESCE(CAST(FsuRId AS TEXT), '')
					FROM WR_FsuManagementCUCC
					WHERE WRFsuId = CAST(@WRFsuId  AS INTEGER);

				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_GetSomeFiledName4" grant="">
			<parameters>
				<parameter name="WRStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT COALESCE(StationName, '') FROM WR_StationManagementCUCC WHERE WRStationId = CAST(@WRStationId  AS INTEGER) LIMIT 1;
					]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_UpdateWRFsuManagementCUCC_FsuStatusNotThree" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="UserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Password" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpUserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpPassword" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Remark" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuRId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ManufacturerId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ContractNo" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ProjectName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE WR_FsuManagementCUCC
					SET WRHouseId = CAST(@WRHouseId  AS INTEGER)
						,FsuCode = @FsuCode
						,FsuName = @FsuName
						,IPAddress = @IPAddress
						,ManufacturerId = CAST(@ManufacturerId  AS INTEGER)
						,FsuStatus = 1 
						,UserName = @UserName 
						,Password = @Password
						,FtpUserName = @FtpUserName
						,FtpPassword = @FtpPassword
						,Remark = @Remark 
						,ContractNo = @ContractNo
						,ProjectName = @ProjectName	
						,FsuRId = @FsuRId
					WHERE WR_FsuManagementCUCC.WRFsuId = CAST(@WRFsuId  AS INTEGER);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_UpdateWRFsuManagementCUCC_FsuStatusIsThree" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="UserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Password" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpUserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpPassword" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Remark" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuRId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ManufacturerId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ContractNo" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ProjectName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>

			</parameters>
			<body>
				<![CDATA[ 
					UPDATE WR_FsuManagementCUCC
					SET WRHouseId = CAST(@WRHouseId  AS INTEGER)
						,FsuCode = @FsuCode
						,FsuName = @FsuName
						,IPAddress = @IPAddress
						,ManufacturerId = CAST(@ManufacturerId  AS INTEGER)
						,UserName = @UserName 
						,Password = @Password
						,FtpUserName = @FtpUserName
						,FtpPassword = @FtpPassword
						,Remark = @Remark 
						,ContractNo = @ContractNo
						,ProjectName = @ProjectName
						,FsuRId = @FsuRId
					WHERE WR_FsuManagementCUCC.WRFsuId = CAST(@WRFsuId  AS INTEGER);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_UpdateTSLMonitorUnit_FsuStatusIsThree" grant="">
			<parameters>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>

			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TSL_MonitorUnit
					SET MonitorUnitName = @FsuName
						,IpAddress = @IPAddress
					WHERE TSL_MonitorUnit.MonitorUnitId = CAST(@SWMonitorUnitId  AS INTEGER) 
						AND TSL_MonitorUnit.StationId = CAST(@SWStationId  AS INTEGER);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_UpdateTSLMonitorUnitCUCC_FsuStatusIsThree" grant="">
			<parameters>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="UserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Password" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpUserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpPassword" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TSL_MonitorUnitCUCC
					SET SUID = @FsuCode
						,SUName = @FsuName
						,SUIP = @IPAddress
						,UserName = @UserName 
						,Password = @Password
						,FtpUserName = @FtpUserName
						,FtpPassword = @FtpPassword
					WHERE TSL_MonitorUnitCUCC.StationId = CAST(@SWStationId  AS INTEGER) 
						AND TSL_MonitorUnitCUCC.MonitorUnitId = CAST(@SWMonitorUnitId  AS INTEGER);   
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_UpdateTBLMonitorUnitProjectInfo_FsuStatusIsThree" grant="">
			<parameters>
				<parameter name="ProjectName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ContractNo" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TBL_MonitorUnitProjectInfo 
					SET  ProjectName = @ProjectName
						, ContractNo = @ContractNo
					WHERE  TBL_MonitorUnitProjectInfo.StationId = CAST(@SWStationId  AS INTEGER) AND TBL_MonitorUnitProjectInfo.MonitorUnitId = CAST(@SWMonitorUnitId  AS INTEGER);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_UpdateTBLConfigChangeMicroLog_FsuStatusIsThree" grant="">
			<parameters>
				<parameter name="ObjectId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TBL_ConfigChangeMicroLog
					SET EditType = 2, UpdateTime = NOW()
					WHERE TBL_ConfigChangeMicroLog.ObjectId = @ObjectId 
					AND TBL_ConfigChangeMicroLog.ConfigId = 15;	
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_UpdateTBLConfigChangeMacroLog_FsuStatusIsThree" grant="">
			<parameters>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TBL_ConfigChangeMacroLog
					SET EditType = 2, UpdateTime = NOW()
					WHERE TBL_ConfigChangeMacroLog.ObjectId = CAST(@SWMonitorUnitId  AS TEXT) AND TBL_ConfigChangeMacroLog.ConfigId = 15;
				]]>
			</body>
		</procedure>
	</procedures>
</root>
