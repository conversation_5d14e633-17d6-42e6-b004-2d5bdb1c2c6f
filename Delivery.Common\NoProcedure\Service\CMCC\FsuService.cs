﻿using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace Delivery.Common.NoProcedure.Service.CMCC
{
    public class FsuService
    {

        private static FsuService _instance = null;

        public static FsuService Instance
        {
            get
            {
                if (_instance == null) _instance = new FsuService();
                return _instance;
            }
        }


        public int SP_Add_WRFsu(string WRHouseId, string FsuCode, string FsuName, string IPAddress, string UserName, string Password, string FtpUserName, string FtpPassword, string Remark, string ContractNo, string ProjectName, string LogonId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);

                Dictionary<string, object> realParams = new Dictionary<string, object>();
                realParams.Add("FsuCode", FsuCode);

                try
                {
                    if (WRHouseId == null || WRHouseId == "" || FsuCode == null || FsuCode == "" || FsuName == null || FsuName == "")                    
                        return 0;                  

                    DataSet resultSql = execHelper.ExecDataSet("SP_Add_WRFsu_FindByFsuCode", realParams);
                    if (resultSql!= null && (resultSql.Tables[0].Rows.Count > 0 || resultSql.Tables[1].Rows.Count > 0))                                       
                        return -1; 

                    realParams.Add("WRHouseId", WRHouseId);
                    DataTable dt = execHelper.ExecDataTable("SP_Add_WRFsu_GetStationId", realParams);
                    if (dt.Rows.Count>0)
                    {
                        int WRStationId = (int)dt.Rows[0][0];
                        int SWStationId = (int)dt.Rows[0][1];

                        realParams.Add("WRStationId", WRStationId);
                        realParams.Add("SWStationId", SWStationId);
                        realParams.Add("FsuName", FsuName);

                        DataSet resultSql1 = execHelper.ExecDataSet("SP_Add_WRFsu_FindByStationIdAndFsuName", realParams);
                        if (resultSql1 != null && (resultSql1.Tables[0].Rows.Count > 0 || resultSql1.Tables[1].Rows.Count > 0))                       
                            return -2;                      

                        int ManufacturerId = int.Parse(FsuCode.Substring(0, 2));
                        realParams.Add("ManufacturerId", ManufacturerId);

                        DataTable dtt = execHelper.ExecDataTable("SP_Add_WRFsu_ManufacturerId", realParams);
                        if (dtt.Rows.Count == 0)
                            return -3;


                        if (IPAddress != null && IPAddress !="")
                        {
                            realParams.Clear();
                            realParams.Add("IPAddress", IPAddress);
                            realParams.Add("FsuCode", FsuCode);
                            
                            DataSet resultSql2 = execHelper.ExecDataSet("SP_Add_WRFsu_IPAddress", realParams);
                            if (resultSql2 != null && (resultSql2.Tables[0].Rows.Count > 0 || resultSql2.Tables[1].Rows.Count > 0))                          
                                return -4;

                            realParams.Clear();
                            realParams.Add("LogonId", LogonId);
                            DataTable dtUser = execHelper.ExecDataTable("SP_Add_WRFsu_GetUser", realParams);
                            if (dtUser.Rows.Count > 0) 
                            {
                                int iUserId = (int)dt.Rows[0][0];
                                string SWUserName = dt.Rows[0][1].ToString();

                                realParams.Clear();
                                realParams.Add("WRHouseId", WRHouseId);
                                realParams.Add("FsuCode", FsuCode);
                                realParams.Add("FsuName", FsuName);
                                realParams.Add("IPAddress", IPAddress);
                                realParams.Add("ManufacturerId", ManufacturerId);
                                realParams.Add("iUserId", iUserId);
                                realParams.Add("SWUserName", SWUserName);
                                realParams.Add("UserName", UserName);
                                realParams.Add("Password", Password);
                                realParams.Add("FtpUserName", FtpUserName);
                                realParams.Add("FtpPassword", FtpPassword);
                                realParams.Add("Remark", Remark);
                                realParams.Add("ContractNo", ContractNo);
                                realParams.Add("ProjectName", ProjectName);
                                execHelper.ExecuteNonQuery("SP_Add_WRFsu_Insert", realParams);

                                realParams.Clear();
                                realParams.Add("WRHouseId", WRHouseId);
                                realParams.Add("FsuCode", FsuCode);
                                realParams.Add("FsuName", FsuName);
                                int WRFsuId  = (int)execHelper.ExecuteScalar("SP_Add_WRFsu_GetWRFsuId", realParams);

                                realParams.Clear();
                                realParams.Add("WRStationId", WRStationId);
                                string StationName = execHelper.ExecuteScalar("SP_Add_WRFsu_GetStationName", realParams).ToString();

                                //此处预留，等负责这个存储过程的同事修改后调用
                                Public_StoredService.Instance.SP_WR_OperationRecord(WRStationId, StationName,7,WRFsuId,FsuName,"","",LogonId);
                                return 1;
                            }
                        }
                    }                    
                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    return 0;
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }

            return 0;        
        }
        public DataTable SP_Get_WRFsuByStationOrHouse(int? WRStationId,int? WRHouseId,string LogonId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper,false);
                Dictionary<string,object> realParams = new Dictionary<string,object>();
                realParams.Add("LogonId", LogonId);
                realParams.Add("WRStationId", WRStationId);
                realParams.Add("WRHouseId", WRHouseId);
                try
                {
                    int? UserId=null; int? iWRSatationId = null;
                    if(WRStationId == null) WRStationId = -1;
                    if(WRHouseId==null) WRHouseId = -1;

                    DataTable dt = executeHelper.ExecDataTable("SP_Get_WRFsuByStationOrHouse_UserId", realParams);
                    if(dt!=null && dt.Rows.Count > 0)
                    {
                        UserId = (int)dt.Rows[0]["UserId"];
                    }
                    realParams.Add("UserId", UserId);
                    DataTable dt2 = executeHelper.ExecDataTable("SP_Get_WRFsuByStationOrHouse_Select_UserRoleMap", realParams);
                    string filterUser;
                    if(dt2!=null && dt2.Rows.Count > 0)
                    {
                        filterUser = XmlScriptProvider.Current.GetCommandSql("SP_Get_WRFsuByStationOrHouse_Select_FsuManagement", realParams);
                    } else
                    {
                        filterUser = XmlScriptProvider.Current.GetCommandSql("SP_Get_WRFsuByStationOrHouse_Filter_UserRoleMap", realParams);
                    }
                    realParams.Add("filterUser", filterUser);
                    if(WRStationId == 0 && WRHouseId == -1)
                    {
                       return   executeHelper.ExecDataTable("SP_Get_WRFsuByStationOrHouse_Connect_FsuManagement", realParams);
                    } else if(WRStationId!=-1 && WRHouseId == -1)
                    {
                       return executeHelper.ExecDataTable("SP_Get_WRFsuByStationOrHouse_Connect_FsuManagement2", realParams);
                    } else if(WRStationId == -1 && WRHouseId !=-1)
                    {
                        DataTable dt3 = executeHelper.ExecDataTable("SP_Get_WRFsuByStationOrHouse_iWRSatationId", realParams);
                        if(dt3!=null && dt3.Rows.Count > 0)
                        {
                            iWRSatationId = (int)dt3.Rows[0]["iWRSatationId"];
                        }
                        realParams.Add("iWRSatationId", iWRSatationId);

                       return executeHelper.ExecDataTable("SP_Get_WRFsuByStationOrHouse", realParams);
                    } else if(WRStationId == -1 && WRHouseId == -1)
                    {
                        return executeHelper.ExecDataTable("SP_Get_WRFsuByStationOrHouse2", realParams);
                    } else
                    {
                        return executeHelper.ExecDataTable("SP_Get_WRFsuByStationOrHouse3", realParams);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    return new DataTable();
                }finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public DataTable GetWRFsu(string startTimeStr, string endTimeStr, string structureId, string fsuName, string fsuCode, string manufactureId, string status, string LogonId)
        {
            DateTime EndTime = DateTime.TryParse(endTimeStr, out DateTime time2) ? time2 : DateTime.Now;
            DateTime StartTime = DateTime.TryParse(startTimeStr, out DateTime time1) ? time1 : EndTime.AddMonths(-1);
            Dictionary<string, object> realParams = new Dictionary<string, object>();
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper);
                try
                {
                    if (StartTime > EndTime)
                    {
                        return null;
                    }
                    realParams.Add("WhereTime", true);
                    realParams.Add("StartTime", StartTime);
                    realParams.Add("EndTime", EndTime);
                    if(structureId != null && structureId != "-1")
                    {
                        realParams.Add("WhereStructure", true);
                        realParams.Add("StructureId", "%" + structureId + "%");
                    }
                    if(fsuName != null && fsuName.Trim() != "")
                    {
                        realParams.Add("WhereFsuName", true);
                        realParams.Add("WhereSWFsuName", true);
                        realParams.Add("FsuName", "%" + fsuName + "%");
                    }
                    if(fsuCode != null && fsuCode.Trim() != "")
                    {
                        realParams.Add("WhereFsuCode", true);
                        realParams.Add("WhereSWFsuCode", true);
                        realParams.Add("FsuCode", "%" + fsuCode + "%");
                    }
                    if(manufactureId != null && manufactureId != "-1")
                    {
                        realParams.Add("WhereManufactureId", true);
                        realParams.Add("ManufacturerId", manufactureId);
                    }
                    if(status != null && status != "-1")
                    {
                        realParams.Add("WhereStatus", true);
                        realParams.Add("Status", status);
                    }
                    realParams.Add("LogonId", LogonId);
                    //-- 用户权限
                    object userIdObj = executeHelper.ExecuteScalar("SP_Get_WRFsu_getUserId", realParams);
                    object userId = CommonUtils.GetNullableValue(userIdObj);
                    realParams.Add("iUserId", userId);
                    object roleMapCountObj = executeHelper.ExecuteScalar("SP_Get_WRFsu_getRoleMapCount", realParams);
                    int roleMapCount = roleMapCountObj == null || roleMapCountObj == DBNull.Value ? 0 : int.Parse(roleMapCountObj.ToString());
                    string filterUserInnerSql;
                    if (roleMapCount > 0)
                    {//-- 管理员可以查看全部,也包括已删除用户创建的FSU
                        filterUserInnerSql = XmlScriptProvider.Current.GetCommandSql("SP_Get_WRFsu_getSqlAdmin", realParams);
                    }
                    else
                    {//-- 非管理员
                        filterUserInnerSql = XmlScriptProvider.Current.GetCommandSql("SP_Get_WRFsu_getSqlNotAdmin", realParams);
                    }
                    realParams.Add("filterUserInnerSql", filterUserInnerSql);

                    DataTable resultDt1 = executeHelper.ExecDataTable("SP_Get_WRFsu1", realParams);

                    DataTable resultDt2 = executeHelper.ExecDataTable("SP_Get_WRFsu2", realParams);

                    resultDt1.Merge(resultDt2);//全部合并进入Dt1中
                    if (resultDt1.Rows.Count > 0)
                    {
                        // 对DataTable进行排序
                        resultDt1.DefaultView.Sort = "ApplyTime DESC";
                        resultDt1 = resultDt1.DefaultView.ToTable();
                        int rowNumber = 1;
                        foreach (DataRow row in resultDt1.Rows)
                        {
                            row["RowNumber"] = rowNumber++;
                        }

                    }
                    return resultDt1;
                }
                catch (Exception ex)
                {
                    Logger.Log("SP_Get_WRFsu throw Exception: ", LogType.Error);
                    Logger.Log(ex);
                    return null;
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
    }
}
