﻿
using Delivery.API;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using Delivery.DAL;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace DM.TestOrder.Controllers {

    public class CheckItemSignalController : BaseController {

        [HttpGet]
        public JsonResult Get(int id) {
            //var sql = string.Format("WO_Debug_CheckItemSignal {0}", id);
            //var dt = DBHelper.GetData(sql);

            //var sReturn = JsonConvert.SerializeObject(dt);
            //HttpResponseMessage result = new HttpResponseMessage {
            //    Content = new StringContent(sReturn, Encoding.GetEncoding("UTF-8"), "application/json")
            //};
            //return result; 
            var tb = new DataTable();
            if (CommonUtils.IsNoProcedure)
            {
                tb = WoTestOrderEquipItemCheckListService.Instance.WODebugCheckItemSignal(id);
            }
            else
            {
                tb = new ExecuteSql().ExecuteStoredProcedure("WO_Debug_CheckItemSignal", new QueryParameter[] {
                 new QueryParameter("OrderCheckId", DataType.Int, id.ToString()) });
            }
            return new JsonResult(tb);

        }
    }
}
