﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.6.8.批量设置FSU注册信息
    /// </summary>
    public sealed class SetLoginInfo:BMessage
    {
        public string UserName { get; private set; }

        public string PassWord { get; private set; }

        public SetLoginInfo(string userName, string password):base()
        {
            MessageType = (int)BMessageType.SET_LOGININFO;
            //协议中无FSUID
            //FSUID = fsuId;
            UserName = userName;
            //2016-08-25 此处密码是明文
            PassWord = password;
        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmldoc = CreateXmlDocument();
                AppendPK_Type(xmldoc, "Request", BMessageType.SET_LOGININFO.ToString());

                XmlElement xe2 = xmldoc.CreateElement("Info");
                //协议中无FSUID
                //XmlElement xe21 = xmldoc.CreateElement("FSUID");
                //xe21.InnerText = FSUID;
                //xe2.AppendChild(xe21);

                XmlElement xe22 = xmldoc.CreateElement("UserName");
                xe22.InnerText = UserName;
                xe2.AppendChild(xe22);

                XmlElement xe23 = xmldoc.CreateElement("PassWord");
                xe23.InnerText = PassWord;
                xe2.AppendChild(xe23);

                XmlNode root = xmldoc.SelectSingleNode("Request");
                root.AppendChild(xe2);

                StringXML = xmldoc.InnerXml;
                entityLogger.DebugFormat("SetLoginInfo.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmldoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetLoginInfo.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetLoginInfo.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}: {2}, {3}", MessageId, (BMessageType)MessageType, UserName, PassWord);
            return sb.ToString();
        }

    }
}
