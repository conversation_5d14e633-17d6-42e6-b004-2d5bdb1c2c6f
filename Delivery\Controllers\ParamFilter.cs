﻿using Microsoft.AspNetCore.Mvc.Filters;

using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Delivery.Controllers
{
    public class ParamFilter : IActionFilter
    {
        public void OnActionExecuted(ActionExecutedContext context)
        {
            
        }

        public void OnActionExecuting(ActionExecutingContext context)
        {
            if (context.HttpContext.Request.Method.CompareTo("GET") == 0)
            {
                IDictionary<string, object> argDic = context.ActionArguments;

            }
           

            //throw new NotImplementedException();
        }
    }
}
