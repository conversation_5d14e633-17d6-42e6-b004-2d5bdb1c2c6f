﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Delivery.Entity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Delivery.Controllers
{
    //[ApiController]
    //[Route("api/[controller]")]
    public class WeatherForecastController : BaseController
    {
        private static readonly string[] Summaries = new[]
        {
            "Freezing", "Bracing", "Chi<PERSON>", "Cool", "Mild", "Warm", "<PERSON><PERSON>y", "Hot", "Sweltering", "Scorching"
        };

        private readonly ILogger<WeatherForecastController> _logger;

        public WeatherForecastController(ILogger<WeatherForecastController> logger)
        {
            _logger = logger;
        }

        [HttpGet]
        public IEnumerable<WeatherForecast> Get()
        {
            var rng = new Random();
            return Enumerable.Range(1, 5).Select(index => new WeatherForecast
            {
                Date = DateTime.Now.AddDays(index),
                TemperatureC = rng.Next(-20, 55),
                Summary = Summaries[rng.Next(Summaries.Length)]
            })
            .ToArray();
        }
        

        [HttpGet("[action]/{id}")]
        public IActionResult Test(int id)
        {
            //return $"test:{id}";
            return Ok(new { test = $"test:{id}"});
        }


        [HttpGet("[action]")]
        public IActionResult Test1(int id)
        {
            //return $"test:{id}";
            return Ok(new { test = $"test:{id}" });
        }

        [HttpPost("[action]")]
        public IActionResult Test2([FromBody] string str)
        {
            //return $"test:{id}";

            //string str = HttpContext.Request.Form["a"].ToString();
            return Ok(new { test = $"test{str}" });
        }
    }
}
