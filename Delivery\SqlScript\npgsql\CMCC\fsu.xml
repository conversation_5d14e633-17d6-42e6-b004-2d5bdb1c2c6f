﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <procedures>

	  <procedure owner="" name="SP_Add_WRFsu_FindByFsuCode" grant="">
		  <parameters>
			  <parameter name="FsuCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		    
		    SELECT * FROM WR_FsuManagement WHERE WR_FsuManagement.FsuCode = @FsuCode;
			SELECT * FROM TSL_MonitorUnitCMCC T WHERE T.FSUID = @FsuCode;
        
      ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Add_WRFsu_GetStationId" grant="">
		  <parameters>
			  <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		    
				SELECT a.WRStationId,b.SWStationId 
				FROM WR_HouseManagement a 
				INNER JOIN WR_StationManagement b ON a.WRStationId = b.WRStationId
				WHERE a.WRHouseId = @WRHouseId;
        
			]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Add_WRFsu_FindByStationIdAndFsuName" grant="">
		  <parameters>
			  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="FsuName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="FsuCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		    
					SELECT 'X' FROM WR_FsuManagement a 
					INNER JOIN WR_HouseManagement b ON a.WRHouseId = b.WRHouseId 
					WHERE b.WRStationId = @WRStationId AND a.FsuCode != @FsuCode AND a.FsuName = @FsuName;
					
					SELECT 'X' FROM TSL_MonitorUnitCMCC 
					WHERE TSL_MonitorUnitCMCC.StationId = @SWStationId AND TSL_MonitorUnitCMCC.FSUID != @FsuCode AND TSL_MonitorUnitCMCC.FSUName = @FsuName;
        
      ]]>
		  </body>
	  </procedure>

	  <procedure owner="" name="SP_Add_WRFsu_ManufacturerId" grant="">
		  <parameters>
			  <parameter name="ManufacturerId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>			 
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		    
					SELECT 'X' FROM WR_DataItem 
					WHERE WR_DataItem.EntryId=4 AND WR_DataItem.ItemId = @ManufacturerId;
        
      ]]>
		  </body>
	  </procedure>
	  
	  <procedure owner="" name="SP_Add_WRFsu_IPAddress" grant="">
		  <parameters>
			  <parameter name="IPAddress" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="FsuCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		    
		    SELECT 'X' FROM WR_FsuManagement WHERE WR_FsuManagement.IPAddress = @IPAddress;
			SELECT 'X' FROM TSL_MonitorUnitCMCC T WHERE T.FSUIP = @IPAddress AND T.FSUID != @FsuCode
        
      ]]>
		  </body>
	  </procedure>


	  <procedure owner="" name="SP_Add_WRFsu_GetUser" grant="">
		  <parameters>
			  <parameter name="LogonId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>			 
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		    
		    SELECT  T.UserId , T.UserName FROM TBL_Account T  WHERE T.LogonId = @LogonId ;
        
      ]]>
		  </body>
	  </procedure>
	  
	  <procedure owner="" name="SP_Add_WRFsu_Insert" grant="">
		  <parameters>
			  <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="FsuCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="FsuName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="IPAddress" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>			  
			  <parameter name="ManufacturerId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="iUserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWUserName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="UserName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="Password" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="FtpUserName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="FtpPassword" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="Remark" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="ContractNo" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="ProjectName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="OriginalPassword" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="ShaPassword" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="Sm3Password" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="FTPType" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="PortNumber" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		    
		    INSERT INTO WR_FsuManagement
              (WRHouseId,FsuCode,FsuName,IPAddress,ManufacturerId,FsuStatus,UserId, SWUserName,UserName,Password,FtpUserName,FtpPassword,ApplyTime,Remark,ContractNo,ProjectName,OriginalPassword,ShaPassword,SmPassword,FTPType,PortNumber)
			VALUES
              (@WRHouseId,@FsuCode,@FsuName,@IPAddress,@ManufacturerId, 1,@iUserId, @SWUserName,@UserName,@Password,@FtpUserName,@FtpPassword,now(),@Remark,@ContractNo,@ProjectName,@OriginalPassword,@ShaPassword,@SmPassword,CAST(@FTPType AS INTEGER),CAST(@PortNumber AS INTEGER));
        
      ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Add_WRFsu_GetWRFsuId" grant="">
		  <parameters>
			  <parameter name="WRHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="FsuCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="FsuName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		    
		     SELECT WRFsuId  FROM WR_FsuManagement WHERE WR_FsuManagement.WRHouseId = @WRHouseId AND WR_FsuManagement.FsuCode = @FsuCode AND  WR_FsuManagement.FsuName = @FsuName;
        
      ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Add_WRFsu_GetStationName" grant="">
		  <parameters>
			  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		    
		     SELECT COALESCE(StationName,'')  FROM WR_StationManagement WHERE WR_StationManagement.WRStationId = @WRStationId;
        
      ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Get_WRFsuByStationOrHouse_UserId" grant="">
		  <parameters>
			  <parameter name="LogonId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
			SELECT  TBL_Account.UserId UserId FROM TBL_Account 
	        WHERE TBL_Account.LogonId = @LogonId; 
			 ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Get_WRFsuByStationOrHouse_Select_UserRoleMap" grant="">
		  <parameters>
			  <parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
			SELECT 'X' FROM TBL_UserRoleMap a WHERE a.UserId = @UserId AND a.RoleId = -1
			 ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Get_WRFsuByStationOrHouse_Select_FsuManagement" grant="">
		  <parameters>
			  <parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
			SELECT DISTINCT UserId FROM WR_FsuManagement
			 ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Get_WRFsuByStationOrHouse_Filter_UserRoleMap" grant="">
		  <parameters>
			  <parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
			SELECT DISTINCT a.UserId FROM TBL_UserRoleMap a 
		    WHERE a.RoleId IN (SELECT RoleId FROM TBL_UserRoleMap 
			WHERE TBL_UserRoleMap.UserId = @UserId)
			 ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Get_WRFsuByStationOrHouse_Connect_FsuManagement" grant="">
		  <parameters>
			  <parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
			SELECT a.WRFsuId, a.FsuName 
		    FROM WR_FsuManagement a
		    INNER JOIN ($[filterUser]) d ON a.UserId = d.UserId
		    WHERE a.FsuStatus = 3;		
			 ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Get_WRFsuByStationOrHouse_Connect_FsuManagement2" grant="">
		  <parameters>
			  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
			SELECT a.WRFsuId, a.FsuName 
		    FROM WR_FsuManagement a 
		    INNER JOIN WR_HouseManagement b ON a.WRHouseId = b.WRHouseId
		    INNER JOIN WR_StationManagement c ON b.WRStationId = c.WRStationId 
		    INNER JOIN ($[filterUser]) d ON a.UserId = d.UserId
		    WHERE c.WRStationId = @WRStationId AND a.FsuStatus = 3;	
			 ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Get_WRFsuByStationOrHouse_iWRSatationId" grant="">
		  <parameters>
			  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
			SELECT a.WRStationId iWRSatationId
		    FROM WR_HouseManagement a
		    WHERE a.WRHouseId = @WRHouseId LIMIT 1;	
			 ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Get_WRFsuByStationOrHouse" grant="">
		  <parameters>
			  <parameter name="iWRSatationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
			SELECT a.WRFsuId, a.FsuName 
		    FROM WR_FsuManagement a 
		    INNER JOIN WR_HouseManagement b ON a.WRHouseId = b.WRHouseId
		    INNER JOIN WR_StationManagement c ON b.WRStationId = c.WRStationId 
		    INNER JOIN ($[filterUser]) d ON a.UserId = d.UserId
		    WHERE c.WRStationId = @iWRSatationId AND a.FsuStatus = 3;	
			 ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Get_WRFsuByStationOrHouse2" grant="">
		  <parameters>
			  <parameter name="iWRSatationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
			SELECT a.WRFsuId, a.FsuName 
		    FROM WR_FsuManagement a
		    INNER JOIN ($[filterUser]) d ON a.UserId = d.UserId
		    WHERE 1=2 and a.FsuStatus = 3;		
			 ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Get_WRFsuByStationOrHouse3" grant="">
		  <parameters>
			  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
			SELECT a.WRFsuId, a.FsuName 
		    FROM WR_FsuManagement a 
		    INNER JOIN WR_HouseManagement b ON a.WRHouseId = b.WRHouseId
		    INNER JOIN WR_StationManagement c ON b.WRStationId = c.WRStationId 
		    INNER JOIN ($[filterUser]) d ON a.UserId = d.UserId
		    WHERE c.WRStationId = @WRStationId AND b.WRHouseId = @WRHouseId 
		    AND a.FsuStatus = 3;
			 ]]>
		  </body>
	  </procedure>
    <procedure owner="" name="SP_Get_WRFsu_getUserId" grant="">
      <parameters>
        <parameter name="LogonId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
			  SELECT TBL_Account.UserId iUserId FROM TBL_Account WHERE TBL_Account.LogonId = @LogonId;
			 ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_WRFsu_getRoleMapCount" grant="">
      <parameters>
        <parameter name="iUserId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
			  SELECT count(*) FROM TBL_UserRoleMap WHERE UserId = CAST(@iUserId AS INTEGER) AND RoleId = -1;
			 ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_WRFsu_getSqlAdmin" grant="">
      <parameters>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
			  SELECT DISTINCT UserId FROM WR_FsuManagement
			 ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_WRFsu_getSqlNotAdmin" grant="">
      <parameters>
        <parameter name="iUserId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
			  SELECT DISTINCT UserId FROM TBL_UserRoleMap 
		    WHERE TBL_UserRoleMap.RoleId IN (SELECT RoleId FROM TBL_UserRoleMap 
			  WHERE TBL_UserRoleMap.UserId =  CAST(@iUserId AS INTEGER))
			 ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_WRFsu1" grant="">
      <parameters>
        <parameter name="StructureId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FsuName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FsuCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ManufacturerId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Status" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StartTime" type="datetime" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EndTime" type="datetime" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="iUserId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
        <replace keyName ="WhereStructure"> and d.LevelPath LIKE @StructureId</replace>
        <replace keyName ="WhereSWFsuName"> and j.FsuName like @FsuName</replace>
        <replace keyName ="WhereSWFsuCode"> and j.FSUID like @FsuCode</replace>
        <replace keyName ="WhereManufactureId"> and a.ManufacturerId = CAST(@ManufacturerId AS INTEGER)</replace>
        <replace keyName ="WhereStatus"> and a.FsuStatus = CAST(@Status AS INTEGER)</replace>
        <replace keyName="WhereTime">
          <![CDATA[ AND a.ApplyTime >= @StartTime AND a.ApplyTime <= @EndTime ]]>
        </replace>
      </replaces>
      <body>
        <![CDATA[
       SELECT 1 RowNumber,
		   a.WRFsuId,
		   d.StructureName,
		   c.StationCode,h.StationName,
		   a.WRHouseId,i.HouseName,
		   j.FSUID FsuCode,j.FSUName FsuName,j.FSUIP IPAddress,
		   a.ManufacturerId,e.ItemValue ManufactureName,
		   a.FsuStatus,f.ItemValue StatusName,
		   a.UserId, a.SWUserName UserName,
		   j.UserName LoginName, j.PassWord Password,
		   j.FTPUserName FtpUserName,j.FTPPassWord FtpPassword,
		   a.ApplyTime,
		   a.ApproveTime,
		   a.RejectCause,
		   a.Remark,
		   a.ContractNo,
		   a.ProjectName,
           a.FTPType,
           a.PortNumber		
	     FROM WR_FsuManagement a
	     INNER JOIN WR_HouseManagement b ON a.WRHouseId = b.WRHouseId
	     INNER JOIN WR_StationManagement c ON b.WRStationId = c.WRStationId
	     INNER JOIN TBL_StationStructure d ON c.StructureId = d.StructureId
	     INNER JOIN WR_DataItem e ON a.ManufacturerId = e.ItemId AND e.EntryId=4
	     INNER JOIN WR_DataItem f ON a.FsuStatus = f.ItemId AND f.EntryId = 5
	     INNER JOIN TBL_Station h ON c.SWStationId = h.StationId
	     INNER JOIN TBL_House i ON c.SWStationId = i.StationId AND b.SWHouseId = i.HouseId
	     INNER JOIN TSL_MonitorUnitCMCC j ON a.SWMonitorUnitId = j.MonitorUnitId AND h.StationId = j.StationId
	     INNER JOIN ($[filterUserInnerSql]) k ON a.UserId = k.UserId 
	     WHERE a.FsuStatus = 3 
       $[WhereTime] $[WhereSWFsuName] $[WhereSWFsuCode]  $[WhereManufactureId] $[WhereStructure] $[WhereStatus]
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Get_WRFsu2" grant="">
      <parameters>
        <parameter name="StructureId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FsuName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FsuCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ManufacturerId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Status" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StartTime" type="datetime" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EndTime" type="datetime" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="iUserId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
        <replace keyName ="WhereStructure"> and d.LevelPath LIKE @StructureId</replace>
        <replace keyName ="WhereFsuName"> and a.FsuName like @FsuName</replace>
        <replace keyName ="WhereFsuCode"> and a.FsuCode like @FsuCode</replace>
        <replace keyName ="WhereManufactureId"> and a.ManufacturerId =  CAST(@ManufacturerId AS INTEGER)</replace>
        <replace keyName ="WhereStatus"> and a.FsuStatus = CAST(@Status AS INTEGER)</replace>
        <replace keyName="WhereTime">
          <![CDATA[ AND a.ApplyTime >= @StartTime AND a.ApplyTime <= @EndTime ]]>
        </replace>
      </replaces>
      <body>
        <![CDATA[
       SELECT 1 RowNumber,
		   a.WRFsuId,
		   d.StructureName,
		   c.StationCode,c.StationName,
		   a.WRHouseId,b.HouseName,
		   a.FsuCode,a.FsuName,a.IPAddress,
		   a.ManufacturerId,e.ItemValue ManufactureName,
		   a.FsuStatus,f.ItemValue StatusName,
		   a.UserId, a.SWUserName UserName,
		   a.UserName LoginName,a.Password,
		   a.FtpUserName,a.FtpPassword,
		   a.ApplyTime,
		   a.ApproveTime,
		   a.RejectCause,
		   a.Remark,
		   a.ContractNo,
		   a.ProjectName,
           a.FTPType,
           a.PortNumber		
	     FROM WR_FsuManagement a
	     INNER JOIN WR_HouseManagement b ON a.WRHouseId = b.WRHouseId
	     INNER JOIN WR_StationManagement c ON b.WRStationId = c.WRStationId
	     INNER JOIN TBL_StationStructure d ON c.StructureId = d.StructureId
	     INNER JOIN WR_DataItem e ON a.ManufacturerId = e.ItemId AND e.EntryId=4
	     INNER JOIN WR_DataItem f ON a.FsuStatus = f.ItemId AND f.EntryId = 5 
       INNER JOIN ($[filterUserInnerSql]) k ON a.UserId = k.UserId 	
	     WHERE a.FsuStatus != 3 
       $[WhereTime] $[WhereFsuName] $[WhereFsuCode]  $[WhereManufactureId] $[WhereStructure] $[WhereStatus]
      ]]>
      </body>
    </procedure>
  </procedures>
</root>
