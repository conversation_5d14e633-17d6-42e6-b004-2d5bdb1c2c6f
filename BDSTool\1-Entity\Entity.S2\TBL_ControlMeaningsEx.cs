
namespace BDSTool.Entity.S2
{


    using BDSTool.DBUtility;
    using BDSTool.DBUtility.Common;
    using Delivery.Common.NoProcedure.Service;
    using Delivery.Common.NoProcedure.Utils;
    using System;
    using System.Collections.Generic;

    public partial class TBL_ControlMeanings : IBatchInsertRow
    {
        #region for batch insert
        private static string _InsertHeader = @"INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings)";

        public string GetInsertHeader() {
            if (CommonUtils.IsNoProcedure)
            {
                return Public_ExecuteSqlService.Instance.GetInsertHeader_TBL_ControlMeanings();
            }
            return _InsertHeader;
        }

        public string GetDataRowValueString() {
            return string.Format(@"({0}, {1}, {2}, {3})",
                EquipmentTemplateId, ControlId, ParameterValue, SHelper.GetPara(Meanings));
        }

        #endregion
    }
}
