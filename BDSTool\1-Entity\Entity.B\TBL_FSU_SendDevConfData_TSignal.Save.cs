﻿

using BDSTool.DBUtility;
using BDSTool.DBUtility.Common;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace BDSTool.Entity.B
{
    public partial class TBL_FSU_SendDevConfData_TSignal : IBatchInsertRow
    {
        public TBL_FSU_SendDevConfData_TSignal(){

        }
    
        #region for batch insert
        private static string _InsertHeader = "INSERT INTO TBL_FSU_SendDevConfData_TSignal (LogId, FSUID, DeviceID, Type, ID,SignalName, AlarmLevel, Threshold, AbsoluteVal, RelativeVal, Describe,NMAlarmID,SignalNumber) ";
        
        public string GetInsertHeader() {
            if (CommonUtils.IsNoProcedure)
            {
                return Public_ExecuteSqlService.Instance.GetInsertHeader_TBL_FSU_SendDevConfData_TSignal();
            }
            return _InsertHeader;
        }

        public string GetDataRowValueString() {
            return String.Format(@"({0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12})",
            LogId, SHelper.GetPara(FSUID), SHelper.GetPara(DeviceID), (int)Type, SHelper.GetPara(ID),
            SHelper.GetPara(SignalName), SHelper.GetPara((int)AlarmLevel), SHelper.GetPara(Threshold), SHelper.GetPara(AbsoluteVal), SHelper.GetPara(RelativeVal),
            SHelper.GetPara(Describe), SHelper.GetPara(NMAlarmID), SHelper.GetPara(SignalNumber));
        }

        #endregion

        public TBL_FSU_SendDevConfData_TSignal(int logId, string fsuID, string deviceID, TSignal sig) {
            LogId = logId;
            FSUID = fsuID;
            DeviceID = deviceID;

            Type          =  (int)sig.Type;
            ID            =  sig.ID;
            AlarmLevel = (int)sig.AlarmLevel;
            SignalName    =  sig.SignalName;
            Threshold     =  sig.Threshold;
            AbsoluteVal   =  sig.AbsoluteVal;
            RelativeVal   =  sig.RelativeVal;
            Describe      =  sig.Describe;
            NMAlarmID     =  sig.NMAlarmID;
            SignalNumber = sig.SignalNumber;
        }

        public static TBL_FSU_SendDevConfData_TSignal ConvertFromTSignal(int logId, string fsuID, string deviceID, TSignal sig) {
            var sigData=new  TBL_FSU_SendDevConfData_TSignal(){
            LogId = logId,
            FSUID = fsuID,
            DeviceID = deviceID,

            Type = (int)sig.Type,
            ID = sig.ID,
            AlarmLevel = (int)sig.AlarmLevel,
            SignalName = sig.SignalName,
            Threshold = sig.Threshold,
            AbsoluteVal = sig.AbsoluteVal,
            RelativeVal = sig.RelativeVal,
            Describe = sig.Describe,
            NMAlarmID = sig.NMAlarmID,
            SignalNumber = sig.SignalNumber,
            };

            return sigData;
        }
    }
}
