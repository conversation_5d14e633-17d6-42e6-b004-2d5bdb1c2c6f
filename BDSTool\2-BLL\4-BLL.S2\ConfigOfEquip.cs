﻿


using BDSTool.DBUtility.Common;
using Common.Logging.Pro;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace BDSTool.BLL.S2 {
    public partial class ConfigHelper {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");


        //UPDATE TBL_Signal 
        //SET BaseTypeId=m.BaseTypeId 
        //FROM TBL_Signal s, TBL_SignalBaseMap m 
        //WHERE s.EquipmentTemplateId={0} AND s.BaseTypeId IS NULL 
        //AND m.StandardType=1 AND m.StationBaseType=0 
        //AND m.StandardDicId/1000=s.SignalId/1000
        public static void UpdateBaseTypeIdOfSignal(int EquipmentTemplateId) {
            //20170519 
            // 1-总是更新所有的基类信息。
            // 2- 映射不区分局站类型（不可能存在 基类+多个局站类型 对应 不同 字典的情况 ）。适应江苏移动的字典
            // 3- 20170824 fix bug -000基类不存在
            //var sql2 = string.Format("UPDATE TBL_Signal SET BaseTypeId=floor(m.BaseTypeId/1000)*1000+RIGHT(SignalId,3) FROM TBL_Signal s, TBL_SignalBaseMap m WHERE s.EquipmentTemplateId={0} AND s.BaseTypeId IS NULL AND m.StandardType=1 AND m.StationBaseType=0 AND m.StandardDicId/1000=s.SignalId/1000", EquipmentTemplateId);
            //var sql2 = string.Format("UPDATE TBL_Signal SET BaseTypeId=floor(m.BaseTypeId/1000)*1000+RIGHT(SignalId,3) FROM TBL_Signal s, TBL_SignalBaseMap m WHERE s.EquipmentTemplateId={0} AND m.StandardType=1 AND m.StandardDicId/1000=s.SignalId/1000", EquipmentTemplateId);

            string sql2;
            if (CommonUtils.IsNoProcedure) 
            {
                string temp = Public_ExecuteSqlService.Instance.NoStore_TBLSignal_UpdateSql();
                sql2 = sql2 = string.Format(temp, EquipmentTemplateId);
            }
            else
            {
                sql2 = string.Format(
                @"UPDATE TBL_Signal SET BaseTypeId=
                CASE WHEN RIGHT(SignalId,3)='000' THEN floor(m.BaseTypeId/1000)*1000+'001' ELSE floor(m.BaseTypeId/1000)*1000+RIGHT(SignalId,3) END 
                FROM TBL_Signal s, TBL_SignalBaseMap m WHERE s.EquipmentTemplateId={0} AND m.StandardType=1 AND m.StandardDicId/1000=s.SignalId/1000", EquipmentTemplateId);
            }
            DbConfigPara para =  DBHelper.GetDbConfig();
            if (para.ProviderName.ToLower() == "mysql")
            {
                if (CommonUtils.IsNoProcedure)
                {
                    string temp = Public_ExecuteSqlService.Instance.NoStore_TBLSignalBaseMap_UpdateSql();
                    sql2 = sql2 = string.Format(temp, EquipmentTemplateId);
                }
                else
                {
                    sql2 = string.Format(
                    @"UPDATE TBL_Signal s 
                    INNER JOIN TBL_SignalBaseMap m  on  s.EquipmentTemplateId={0} AND m.StandardType=1 AND m.StandardDicId/1000=s.SignalId/1000 
                    SET s.BaseTypeId=CASE WHEN RIGHT(s.SignalId,3)='000' THEN floor(m.BaseTypeId/1000)*1000+'001' 
                    ELSE floor(m.BaseTypeId/1000)*1000+RIGHT(s.SignalId,3) 
                    END ", EquipmentTemplateId);
                }
            }
            DBHelper.ExecuteNonQuery(sql2);
        }

        //UPDATE TBL_Control 
        //SET BaseTypeId=m.BaseTypeId 
        //FROM TBL_Control s, TBL_CommandBaseMap m 
        //WHERE s.EquipmentTemplateId={0} AND s.BaseTypeId IS NULL 
        //AND m.StandardType=1 AND m.StationBaseType=0 
        //AND m.StandardDicId/1000=s.ControlId/1000
        public static void UpdateBaseTypeIdOfControl(int EquipmentTemplateId) {
            //20170519 
            // 1-总是更新所有的基类信息。
            // 2- 映射不区分局站类型（不可能存在 基类+多个局站类型 对应 不同 字典的情况 ）。适应江苏移动的字典
            // 3- 20170824 fix bug -000基类不存在
            //var sql2 = string.Format("UPDATE TBL_Control SET  BaseTypeId=floor(m.BaseTypeId/1000)*1000+RIGHT(ControlId,3)  FROM TBL_Control s, TBL_CommandBaseMap m WHERE s.EquipmentTemplateId={0} AND s.BaseTypeId IS NULL AND m.StandardType=1 AND m.StationBaseType=0 AND m.StandardDicId/1000=s.ControlId/1000", EquipmentTemplateId);
            //var sql2 = string.Format("UPDATE TBL_Control SET  BaseTypeId=floor(m.BaseTypeId/1000)*1000+RIGHT(ControlId,3)  FROM TBL_Control s, TBL_CommandBaseMap m WHERE s.EquipmentTemplateId={0} AND m.StandardType=1 AND m.StandardDicId/1000=s.ControlId/1000", EquipmentTemplateId);

            //floor(m.BaseTypeId/1000)*1000+RIGHT(ControlId,3)  
            string sql2;
            if (CommonUtils.IsNoProcedure)
            {
                string temp = Public_ExecuteSqlService.Instance.NoStore_TBLControl_UpdateSql();
                sql2 = sql2 = string.Format(temp, EquipmentTemplateId);
            }
            else
            {
                sql2 = string.Format(
                @"UPDATE TBL_Control SET  BaseTypeId=
                CASE WHEN RIGHT(ControlId,3)='000' THEN floor(m.BaseTypeId/1000)*1000+'001' ELSE floor(m.BaseTypeId/1000)*1000+RIGHT(ControlId,3) END 
                FROM TBL_Control s, TBL_CommandBaseMap m WHERE s.EquipmentTemplateId={0} AND m.StandardType=1 AND m.StandardDicId/1000=s.ControlId/1000", EquipmentTemplateId);
             }
            DbConfigPara para = DBHelper.GetDbConfig();
            if (para.ProviderName.ToLower() == "mysql")
            {
                if (CommonUtils.IsNoProcedure)
                {
                    string temp = Public_ExecuteSqlService.Instance.NoStore_TBLCommandBaseMap_UpdateSql();
                    sql2 = sql2 = string.Format(temp, EquipmentTemplateId);
                }
                else
                {
                    sql2 = string.Format(
                    @"UPDATE TBL_Control s
                    INNER JOIN TBL_CommandBaseMap m on s.EquipmentTemplateId={0} AND m.StandardType=1 AND m.StandardDicId/1000=s.ControlId/1000
                    SET  s.BaseTypeId=
                    CASE WHEN RIGHT(s.ControlId,3)='000' THEN floor(m.BaseTypeId/1000)*1000+'001' 
                    ELSE floor(m.BaseTypeId/1000)*1000+RIGHT(s.ControlId,3) END ", EquipmentTemplateId);
                }

            }
            DBHelper.ExecuteNonQuery(sql2);
        }
        //UPDATE TBL_EventCondition
        //SET BaseTypeId=m.BaseTypeId
        //FROM TBL_EventCondition ec, TBL_EventBaseMap m
        //WHERE 
        //ec.EquipmentTemplateId=11000342 AND ec.BaseTypeId IS NULL
        //AND m.StandardType=1 AND m.StationBaseType=0 
        //AND m.StandardDicId=ec.EventId/1000
        public static void UpdateBaseTypeIdOfEventCondition(int EquipmentTemplateId) {
            //20170519 
            // 1-总是更新所有的基类信息。
            // 2- 映射不区分局站类型（不可能存在 基类+多个局站类型 对应 不同 字典的情况 ）。适应江苏移动的字典
            // 3- 20170824 fix bug -000基类不存在

            //var sql2 = string.Format("UPDATE TBL_EventCondition SET BaseTypeId=floor(m.BaseTypeId/1000)*1000+RIGHT(EventId,3) FROM TBL_EventCondition ec, TBL_EventBaseMap m WHERE ec.EquipmentTemplateId={0} AND ec.BaseTypeId IS NULL AND m.StandardType=1 AND m.StationBaseType=0 AND m.StandardDicId=ec.EventId/1000", EquipmentTemplateId);
            //var sql2 = string.Format("UPDATE TBL_EventCondition SET BaseTypeId=floor(m.BaseTypeId/1000)*1000+RIGHT(EventId,3) FROM TBL_EventCondition ec, TBL_EventBaseMap m WHERE ec.EquipmentTemplateId={0} AND m.StandardType=1 AND m.StandardDicId=ec.EventId/1000", EquipmentTemplateId);

            //floor(m.BaseTypeId/1000)*1000+RIGHT(EventId,3) 

            string sql2;
            if (CommonUtils.IsNoProcedure)
            {
                string temp = Public_ExecuteSqlService.Instance.NoStore_TBLEventCondition_UpdateSql();
                sql2 = sql2 = string.Format(temp, EquipmentTemplateId);
            }
            else
            {
                sql2 = string.Format(
                @"UPDATE TBL_EventCondition SET BaseTypeId=
                CASE WHEN RIGHT(EventId,3)='000' THEN floor(m.BaseTypeId/1000)*1000+'001' ELSE floor(m.BaseTypeId/1000)*1000+RIGHT(EventId,3) END 
                FROM TBL_EventCondition ec, TBL_EventBaseMap m WHERE ec.EquipmentTemplateId={0} AND m.StandardType=1 AND m.StandardDicId=ec.EventId/1000", EquipmentTemplateId);
            }
            DbConfigPara para = DBHelper.GetDbConfig();
            if (para.ProviderName.ToLower() == "mysql")
            {
                if (CommonUtils.IsNoProcedure)
                {
                    string temp = Public_ExecuteSqlService.Instance.NoStore_TBLEventBaseMap_UpdateSql();
                    sql2 = sql2 = string.Format(temp, EquipmentTemplateId);
                }
                else
                {
                    sql2 = string.Format(
                    @"UPDATE TBL_EventCondition ec
                    INNER JOIN TBL_EventBaseMap m on ec.EquipmentTemplateId={0} AND m.StandardType=1 AND m.StandardDicId=ec.EventId/1000
                    SET ec.BaseTypeId=CASE WHEN RIGHT(ec.EventId,3)='000' THEN floor(m.BaseTypeId/1000)*1000+'001' 
                    ELSE floor(m.BaseTypeId/1000)*1000+RIGHT(ec.EventId,3) END  ", EquipmentTemplateId);
                }
            }
            DBHelper.ExecuteNonQuery(sql2);
        }
    }
}
