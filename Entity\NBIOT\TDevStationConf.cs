﻿namespace ENPC.Kolo.Entity.NBIOT
{
    public class TDevStationConf
    {
        public TDevStationConf() { }

        public TDevStationConf(string devId, string siteId, string roomId, uint? signalNum)
        {
            DevID = devId;
            SiteID = siteId;
            RoomID = roomId;
            SignalNum = signalNum;
        }

        public string DevID { get; set; }

        public string SiteID { get; set; }

        public string RoomID { get; set; }

        public uint? SignalNum { get; set; }

        public override string ToString()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
    }
}