﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class GetAlarmProperty : BMessage
    {
        public List<Device> LDevice { get; set; }

        public GetAlarmProperty(string suId, string suRId, List<Device> list)
            : base()
        {
            MessageType = (int)BMessageType.GET_AlarmProperty;
            SUId = suId;
            SURId = suRId;
            LDevice = list;
        }

        public GetAlarmProperty(string suId, string suRId, List<Device> list, string strDeviceList)
            : base()
        {
            MessageType = (int)BMessageType.GET_AlarmProperty;
            SUId = suId;
            SURId = suRId;
            LDevice = list;
            if ((list == null || list.Count == 0) && !string.IsNullOrEmpty(strDeviceList))
            {
                string[] strDeviceIds = strDeviceList.Split('|');
                if (strDeviceIds != null && strDeviceIds.Length > 0)
                {
                    List<Device> deviceList = new List<Device>();
                    foreach (string deviceId in strDeviceIds)
                    {
                        string Id = deviceId.Substring(0, deviceId.IndexOf(','));
                        string RId = deviceId.Substring(deviceId.IndexOf(',') + 1);
                        Device device = new Device(Id, RId);
                        deviceList.Add(device);
                    }
                    LDevice = deviceList;
                }
            }

        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmlDoc = CreateXmlDocument();
                AppendPK_Type(xmlDoc, "Request", BMessageType.GET_AlarmProperty.ToString(), MessageType.ToString());
                XmlElement xel = xmlDoc.CreateElement("Info");
                XmlElement xel1 = xmlDoc.CreateElement("SUId");
                XmlElement xel2 = xmlDoc.CreateElement("SURId");
                XmlElement xel3 = xmlDoc.CreateElement("DeviceList");
                XmlElement xel4 = null;
                xel1.InnerText = SUId;
                xel2.InnerText = SURId;
                if (LDevice.Count > 0)
                {
                    foreach (Device dev in LDevice)
                    {
                        xel4 = xmlDoc.CreateElement("Device");
                        xel4.SetAttribute("Id", dev.Id);
                        xel4.SetAttribute("RId", dev.RId);
                        xel3.AppendChild(xel4);
                    }
                }
                //else
                //{
                //    xel4 = xmlDoc.CreateElement("Device");
                //    xel4.SetAttribute("Id", "");
                //    xel4.SetAttribute("RId", "");
                //    xel3.AppendChild(xel4);
                //}
                xel.AppendChild(xel1);
                xel.AppendChild(xel2);
                xel.AppendChild(xel3);
                XmlNode node = xmlDoc.SelectSingleNode("Request");
                node.AppendChild(xel);
                StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("GetAlarmProperty.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmlDoc.InnerXml;

            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetAlarmProperty.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetAlarmProperty.Serialize() Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            if (LDevice.Count > 0)
            {
                foreach (Device dev in LDevice)
                {
                    sb.AppendFormat("{0}, {1},{2},{3},Device:{4},{5}", MessageId, (BMessageType)MessageType, SUId, SURId, dev.Id, dev.RId);
                }
            }
            else
            {
                sb.AppendFormat("{0}, {1},{2},{3},Device:is null", MessageId, (BMessageType)MessageType, SUId, SURId);
            }
            return sb.ToString();
        }


    }
}
