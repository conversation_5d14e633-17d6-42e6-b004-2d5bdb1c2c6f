﻿
using DM.TestOrder.Common.DBA;


using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using DM.TestOrder.Common;
using DM.TestOrder.Entity;


namespace DM.TestOrder.DAL.Helper
{
    public class WO_TestOrderHelper
    {
        public static WO_TestOrder FromDataRow(DataRow row) {
            var e = new WO_TestOrder();
            try {
                e.OrderId=   int.Parse(row["OrderId"].ToString());
                e.MyOrderId = row["MyOrderId"].ToString();
                e.OrderType = int.Parse(row["OrderType"].ToString());
                e.StationId = int.Parse(row["StationId"].ToString());
                e.Latitude = SHelper.ToDecimal(row["Latitude"].ToString());
                e.Longitude = SHelper.ToDecimal(row["Longitude"].ToString());
                e.EquipItems = row["EquipItems"].ToString();
                e.InstallCompany = row["InstallCompany"].ToString();
                e.InstallClerk = row["InstallClerk"].ToString();

                e.InstallCompanyId =SHelper.ToInt( row["InstallCompanyId"]);
                e.InstallClerkId = SHelper.ToInt( row["InstallClerkId"].ToString());


                e.ApplyUserId = SHelper.ToInt(row["ApplyUserId"].ToString());
                e.ApplyUserName = row["ApplyUserName"].ToString();
                e.ApplyUserFsuVendor = row["ApplyUserFsuVendor"].ToString();
                

                e.ApplyTime = SHelper.ToDateTime(row["ApplyTime"]);
                e.StateChangeTime = SHelper.ToDateTime(row["StateChangeTime"]);
                e.OrderState = SHelper.ToInt(row["OrderState"].ToString());
                e.StateSetUserId = SHelper.ToInt(row["StateSetUserId"].ToString());
                e.StateSetUserName = row["StateSetUserName"].ToString();
                e.SubmitTime = SHelper.ToDateTimeNullable(row["SubmitTime"]);

                

                e.ExpertUserId = SHelper.ToInt(row["ExpertUserId"]);
                e.ExpertDecision = row["ExpertDecision"].ToString();
                e.ExpertNote = row["ExpertNote"].ToString();
                e.ExpertIsApprove = SHelper.ToInt(row["ExpertIsApprove"]);

                e.FinalUserId =  SHelper.ToInt(row["FinalUserId"]); 
                e.FinalGeneralReuslt = row["FinalGeneralReuslt"].ToString();
                e.FinalDecision = row["FinalDecision"].ToString();
                e.FinalNote = row["FinalNote"].ToString();
                e.FinalIsApprove = SHelper.ToInt(row["FinalIsApprove"]);

                e.ApproveTime = SHelper.ToDateTimeNullable(row["ApproveTime"]);
                e.NeedUpload = SHelper.ToInt(row["NeedUpload"]);
            }
            catch (Exception ex) {
                MyLogger.ErrorFormat("WO_TestOrderHelper.FromDataRow();error={0}", ex.Message);
                MyLogger.Error(ex.StackTrace);
                return null;
            }
            return e;
        }
    }
}
