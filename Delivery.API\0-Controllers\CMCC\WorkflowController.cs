using Delivery.BSL;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using Delivery.Common;
using System.Linq;
using BDSTool.DBUtility.Common;
using Newtonsoft.Json.Linq;
using System.Threading.Tasks;
using System.Net.Http;
using System.Text.Json;
using System.IO;

namespace Delivery.API.CMCC
{
    [Route("CMCC/api/[controller]")] 
    public class WorkflowController : BaseController
    {
     
        public class ServiceResponse
        {
            public string Msg { get; set; }
            public int code { get; set; }
            public object data { get; set; }
        }



        [HttpGet("[action]")]
        public string GetWorkflowInfo(string WorkflowType, int page, int rows)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            DataTable dt = ccbsl.GetWorkflowInfo(WorkflowType);
            return Json4EasyUI.onDataGrid(dt, page, rows);
        }


        [HttpPost("[action]")]
        public bool saveWorkflowID([FromForm] string WorkflowID, [FromForm] string AutoId)
        {
            try
            {
                if (WorkflowID != null && AutoId != null)
                {
                    var sql = string.Format("Update wr_workflowcmcc set WorkflowId='{0}' where AutoId={1}", WorkflowID, AutoId);
                    DBHelper.ExecuteScalar(sql);
                }
                else
                {
                    var sql = string.Format(" DELETE FROM wr_workflowcmcc where AutoId={0}",  AutoId);
                    DBHelper.ExecuteScalar(sql);

                }
               
            }
            catch (Exception ex)
            {
                return false;
            }
            return true;
        }

        [HttpPost("[action]")]
        public IActionResult NewWorkflow([FromBody] JObject value)
        {
            var WorkflowName = value["WorkflowName"].ToString();
            var Remark = value["Remark"].ToString();
            var Station = value["Station"].ToObject<JArray>();
            var House = value["House"].ToObject<JArray>();
            var Fsu = value["Fsu"].ToObject<JArray>();
            var UserId = value["UserId"].ToString();
            string WorkflowType = value["WorkflowType"].ToString();
            var OrderId = value["OrderId"].ToString();
            string endstring = DateTime.Now.ToString("yyyy-MM-dd hh:mm:ss");
            try
            {
                string autoId2;
                if (WorkflowType == "1")
                {
                    var sql = string.Format("INSERT INTO wr_workflowcmcc(WorkflowName, Remark, UserId, ApplyTime,WorkflowStatus,WorkflowType) VALUES ('{0}', '{1}', '{2}', '{3}',{4},'{5}')", WorkflowName, Remark, UserId, endstring, 1, WorkflowType);
                    var sql2 = string.Format("SELECT LAST_INSERT_ID()");
                    DBHelper.ExecuteScalar(sql);
                    autoId2 = DBHelper.ExecuteScalar(sql2);
                    if (Station != null && Station.Count > 0)
                    {
                        foreach (JObject station in Station)
                        {
                            var stationId = station["WRStationId"].ToString();
                            var stringsql = string.Format("INSERT INTO WR_WorkflowDetailCMCC(AutoId, AssetId, AssetTypeId) VALUES ({0}, {1}, {2})",
                                autoId2, stationId, 1
                            );
                            DBHelper.ExecuteScalar(stringsql);
                        }
                    }
                    if (House != null && House.Count > 0)
                    {
                        foreach (JObject house in House)
                        {
                            var houseId = house["WRHouseId"].ToString();
                            var stringsql = string.Format("INSERT INTO WR_WorkflowDetailCMCC(AutoId, AssetId, AssetTypeId) VALUES ({0}, {1}, {2})",
                                autoId2, houseId, 2);
                            DBHelper.ExecuteScalar(stringsql);
                        }
                    }
                    if (Fsu != null && Fsu.Count > 0)
                    {
                        foreach (JObject fsu in Fsu)
                        {
                            var fsuId = fsu["WRFsuId"].ToString();
                            var stringsql = string.Format("INSERT INTO WR_WorkflowDetailCMCC(AutoId, AssetId, AssetTypeId) VALUES ({0}, {1}, {2})",
                                autoId2, fsuId, 3);
                            DBHelper.ExecuteScalar(stringsql);
                        }
                    }
                  
                }
                else
                {
                    var sql = string.Format("INSERT INTO wr_workflowcmcc(WorkflowName, Remark, UserId, ApplyTime,WorkflowStatus,WorkflowType,ReviewerL1) VALUES ('{0}', '{1}', '{2}', '{3}',{4},'{5}', '{6}')", WorkflowName, Remark, UserId, endstring, 1, WorkflowType, OrderId);
                    var sql2 = string.Format("SELECT LAST_INSERT_ID()");
                    DBHelper.ExecuteScalar(sql);
                    autoId2 = DBHelper.ExecuteScalar(sql2);
                }

                return Ok(new { Success = true, AutoId = autoId2 });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = ex.Message });
            }
        }



        [HttpGet("[action]")]
        public string GetWorkflowDetailInfo(string AutoId, int page, int rows)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            DataTable dt = ccbsl.GetWorkflowDetailInfo(AutoId);
            return Json4EasyUI.onDataGrid(dt, page, rows);
        }


        [HttpPost("[action]")]
        public IActionResult ApproveWorkflow([FromBody] JObject value)
        {
            var AutoId2 = value["AutoId"].ToString();
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            bool dt = ccbsl.ApproveWorkflow(AutoId2);
            return Ok(new { Success = true, msg = dt });

        }


        [HttpPost("[action]")]
        public async Task<IActionResult> ProcessPendingWorkflows()
        {
            try
            {
                // 查询待处理的工作流并拼接ID
                var sql = "SELECT WorkflowId FROM wr_workflowcmcc WHERE WorkflowStatus = 1";
                DataTable dt = DBHelper.GetTable(sql);

                if (dt == null || dt.Rows.Count == 0)
                {
                    return Ok(new { Success = true, Message = "没有待处理的工作流" });
                }

                string workflowIds = string.Join(",", dt.AsEnumerable()
                    .Select(row => row["WorkflowId"].ToString()));

                // 准备请求数据
                var requestData = new { ids = workflowIds };
                var jsonContent = new StringContent(
                    JsonSerializer.Serialize(requestData),
                    Encoding.UTF8,
                    "application/json");

                // 发送请求
                using (var client = new HttpClient())
                {
                    string url = ConfigHelper.BaseUrl + "/siteweb-flow/api/processInstance/getProcessInstanceStatus";
                    var response = await client.PostAsync(url, jsonContent);

                    if (response.IsSuccessStatusCode)
                    {
                        var responseContent = await response.Content.ReadAsStringAsync();
                        var responseObj = JsonSerializer.Deserialize<ServiceResponse>(responseContent);
                        Console.WriteLine(responseObj);
                        if (responseObj.code == 200 && responseObj.data != null)
                        {
                            // 解析返回的数据并更新数据库
                            var workflowResults = JsonSerializer.Deserialize<Dictionary<string, int>>(responseObj.data.ToString());
                            foreach (var item in workflowResults)
                            {
                                var updateSql = string.Format(
                                    "UPDATE wr_workflowcmcc SET WorkflowStatus = {0} WHERE WorkflowId = '{1}'",
                                    item.Value, item.Key);
                                DBHelper.ExecuteScalar(updateSql);
                            }

                            return Ok(new { Success = true, Message = "工作流状态更新成功" });
                        }
                    }

                    return BadRequest(new { Success = false, Message = "外部服务处理失败" });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Success = false, Message = ex.Message });
            }
        }


        [HttpPost("[action]")]
        public IActionResult DeleteWorkflowDetail([FromForm] string AutoId, [FromForm] string AssetId, [FromForm] string AssetTypeId)
        { 
            try
            {
                var sql = string.Format(" DELETE FROM WR_WorkflowDetailCMCC WHERE AutoId = {0} and AssetTypeId = {1} and AssetId = {2}", AutoId, AssetTypeId, AssetId);
                DBHelper.ExecuteScalar(sql);
              
                return Ok(new { Success = true });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = ex.Message });
            }
        }


        [HttpGet("[action]")]
        public IActionResult GetAppSettings()
        {
            try
            {
                string strValue = ConfigHelper.WorkFlow;
                string strBaseUrl = ConfigHelper.BaseUrl;
                string hostUrl = ConfigHelper.Urls;

                return Ok(new { Success = true, Data = strValue ,url= strBaseUrl, hostUrl = hostUrl });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Success = false, Message = ex.Message });
            }
        }

        

    }
}
