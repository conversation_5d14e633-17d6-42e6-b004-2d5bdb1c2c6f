﻿using Common.Logging.Pro;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;


using BDSTool.Entity.S2;
using BDSTool.DBUtility;
using BDSTool.DBUtility.Common;
using BDSTool.Entity.B;
using ENPC.Kolo.Entity.B_CMCC;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace BDSTool.BLL.S2
{
    public class SignalBizZ
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static bool XAddOne(TBL_Signal signal) {
            if (XSaveEntity(signal) == false)
                return false;


            //处理信号属性（27)
            if (!SetSignalPropertyOfVisible(signal))
                return false;

            //Describe	120字节	描述信息 =>条件
            //  状态信号为状态描述,，格式举例：0&正常;1&告警 ; 需要设置TBL_SignalMeanings
            //  模拟信号为单位。
            if (signal.SignalCategory != 1)//开关量
                if (SetSignalMeaningsZ(signal) == false)
                    return false;

            return true;
            
        }

        public static bool SetSignalPropertyOfVisible(TBL_Signal signal) {

            try {
                string sql;
                if (CommonUtils.IsNoProcedure) 
                {
                    string Tempsql = Public_ExecuteSqlService.Instance.NoStore_TBLSignalProperty_InsertSql();
                    sql = string.Format(Tempsql,  signal.EquipmentTemplateId, signal.SignalId, "'27'");
                }
                else
                {
                    sql = string.Format(@"INSERT INTO TBL_SignalProperty
                                (EquipmentTemplateId, SignalId, SignalPropertyId)
                                VALUES ({0}, {1}, {2})",
                            signal.EquipmentTemplateId, signal.SignalId, "'27'");
                }
                 

                DBHelper.ExecuteNonQuery(sql);
            }
            catch (Exception ex) {
                logger.ErrorFormat("SetSignalPropertyOfVisible();EquipmentTemplateId={0},SignalName={1};Error={2}",
                    signal.EquipmentTemplateId, signal.SignalName, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;

        }
        public static bool SetSignalMeaningsZ(TBL_Signal signal) {
            try {
                foreach (var state in signal.StateValue2Meanings) {
                    int StateValue = state.Key;
                    string Meanings = state.Value;
                    if (CommonUtils.IsNoProcedure)
                    {
                        Public_ExecuteSqlService.Instance.NoStore_TBLSignalMeanings_InsertSql(signal.EquipmentTemplateId, signal.SignalId, StateValue, Meanings);
                    }
                    else {
                        string sql = string.Format(@"INSERT INTO TBL_SignalMeanings
                        (EquipmentTemplateId, SignalId, StateValue, Meanings)
                        VALUES ({0}, {1}, {2}, '{3}')",
                        signal.EquipmentTemplateId, signal.SignalId, StateValue, Meanings);

                        DBHelper.ExecuteNonQuery(sql);
                    }
                    
                }
            }
            catch (Exception ex) {
                logger.ErrorFormat("SetSignalMeaningsZ();EquipmentTemplateId={0},SignalName={1};Error={2}",
                    signal.EquipmentTemplateId, signal.SignalName, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }
        public static bool XSaveEntity(TBL_Signal signal) {
            try {
                if (CommonUtils.IsNoProcedure)
                {
                    Public_ExecuteSqlService.Instance.NoStore_TBLSignal_InsertSql(signal.EquipmentTemplateId, signal.SignalId, SHelper.GetPara(signal.Enable), SHelper.GetPara(signal.Visible), SHelper.GetPara(signal.Description),
                    SHelper.GetPara(signal.SignalName), signal.SignalCategory, signal.SignalType, signal.ChannelNo, signal.ChannelType,
                    SHelper.GetPara(signal.Expression), SHelper.GetPara(signal.DataType), SHelper.GetPara(signal.ShowPrecision), SHelper.GetPara(signal.Unit), signal.DisplayIndex,
                    signal.ModuleNo);
                }
                else {
                    var sql = string.Format(@"INSERT TBL_Signal 
                    ( EquipmentTemplateId , SignalId , Enable , Visible , Description , 
                    SignalName , SignalCategory , SignalType , ChannelNo , ChannelType , 
                    Expression , DataType , ShowPrecision , Unit, DisplayIndex , 
                    ModuleNo ) 
                    values(
                        {0},{1},{2},{3},{4},
                        {5},{6},{7},{8},{9},
                        {10},{11},{12},{13},{14},
                        {15}
                    )",
                    signal.EquipmentTemplateId, signal.SignalId, SHelper.GetPara(signal.Enable), SHelper.GetPara(signal.Visible), SHelper.GetPara(signal.Description),
                    SHelper.GetPara(signal.SignalName), signal.SignalCategory, signal.SignalType, signal.ChannelNo, signal.ChannelType,
                    SHelper.GetPara(signal.Expression), SHelper.GetPara(signal.DataType), SHelper.GetPara(signal.ShowPrecision), SHelper.GetPara(signal.Unit), signal.DisplayIndex,
                    signal.ModuleNo);

                    DBHelper.ExecuteNonQuery(sql);
                }

            }
            catch (Exception ex) {
                logger.ErrorFormat("XSaveEntity();EquipmentTemplateId={0},SignalName={1};Error={2}", 
                    signal.EquipmentTemplateId, signal.SignalName, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }

        public static bool DeleteList(int EquipmentTemplateId, List<TSignalEx> signals) {
            try {
                if (signals.Count == 0)
                    return true;

                StringBuilder sb = new StringBuilder();
                DbConfigPara para = DBHelper.GetDbConfig();
                foreach (var sig in signals) {
                    string sql;
                    if (CommonUtils.IsNoProcedure)
                    {
                        string tempSql = Public_ExecuteSqlService.Instance.NoStore_TBLSignal_GetSql();
                        sql = string.Format(tempSql, EquipmentTemplateId, sig.TSignalId);
                    }
                    else
                    {
                        sql = string.Format("delete from TBL_Signal where EquipmentTemplateId={0} and SignalId={1}", EquipmentTemplateId, sig.TSignalId);
                    }
                    sb.AppendLine(DBHelper.GetExecSql(sql));
                }

                foreach (var sig in signals)
                {
                    string sql;
                    if (CommonUtils.IsNoProcedure)
                    {
                        string tempSql = Public_ExecuteSqlService.Instance.NoStore_TBLSignalProperty_GetSql();
                        sql = string.Format(tempSql, EquipmentTemplateId, sig.TSignalId);
                    }
                    else
                    {
                        sql = string.Format("delete from TBL_SignalProperty where EquipmentTemplateId={0} and SignalId={1}", EquipmentTemplateId, sig.TSignalId);
                    }
                    sb.AppendLine(DBHelper.GetExecSql(sql));
                }

                //开关量需要
                foreach (var sig in signals.Where(o=>o.tsig.Type==EnumType.DI)) {
                    string sql;
                    if (CommonUtils.IsNoProcedure)
                    {
                        string tempSql = Public_ExecuteSqlService.Instance.NoStore_TBLSignalMeanings_GetSql();
                        sql = string.Format(tempSql, EquipmentTemplateId, sig.TSignalId);
                    }
                    else
                    {
                        sql = string.Format("delete from TBL_SignalMeanings where EquipmentTemplateId={0} and SignalId={1}", EquipmentTemplateId, sig.TSignalId);
                    }
                    sb.AppendLine(DBHelper.GetExecSql(sql));
                }

                var sqlAll = sb.ToString();
                DBHelper.ExecuteNonQuery(sqlAll);

            }
            catch (Exception ex) {
                logger.ErrorFormat("SignalBiz.DeleteList();EquipmentTemplateId={0},TSignalCount={1};Error={2}",
                    EquipmentTemplateId, signals.Count, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }

       
        public static bool XDeleteList(int EquipmentTemplateId, List<int> signalIds) {
            try {
                if (signalIds.Count == 0)
                    return true;

                StringBuilder sb = new StringBuilder();
                foreach (var sigId in signalIds) {
                    string sql;
                    if (CommonUtils.IsNoProcedure)
                    {
                        string tempSql = Public_ExecuteSqlService.Instance.NoStore_TBLSignal_GetSql();
                        sql = string.Format(tempSql, EquipmentTemplateId, sigId);
                    }
                    else
                    {
                        sql = string.Format("delete from TBL_Signal where EquipmentTemplateId={0} and SignalId={1}", EquipmentTemplateId, sigId);
                    }
                    sb.AppendLine(DBHelper.GetExecSql(sql));
                }

                foreach (var sigId in signalIds) {
                    string sql;
                    if (CommonUtils.IsNoProcedure)
                    {
                        string tempSql = Public_ExecuteSqlService.Instance.NoStore_TBLSignalProperty_GetSql();
                        sql = string.Format(tempSql, EquipmentTemplateId, sigId);
                    }
                    else
                    {
                        sql = string.Format("delete from TBL_SignalProperty where EquipmentTemplateId={0} and SignalId={1}", EquipmentTemplateId, sigId);
                    }
                    sb.AppendLine(DBHelper.GetExecSql(sql));
                }

                //可以优化，只有开关量需要
                foreach (var sigId in signalIds)
                {
                    string sql;
                    if (CommonUtils.IsNoProcedure)
                    {
                        string tempSql = Public_ExecuteSqlService.Instance.NoStore_TBLSignalMeanings_GetSql();
                        sql = string.Format(tempSql, EquipmentTemplateId, sigId);
                    }
                    else
                    {
                        sql = string.Format("delete from TBL_SignalMeanings where EquipmentTemplateId={0} and SignalId={1}", EquipmentTemplateId, sigId);
                    }
                    sb.AppendLine(DBHelper.GetExecSql(sql));
                }

                var sqlAll = sb.ToString();
                DBHelper.ExecuteNonQuery(sqlAll);

            }
            catch (Exception ex) {
                logger.ErrorFormat("SignalBiz.DeleteSignals();EquipmentTemplateId={0},TSignalCount={1};Error={2}",
                    EquipmentTemplateId, signalIds.Count, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }

        //        public static bool SaveSignalMeanings(TBL_Signal signal) {
        //            try {

        //                if (signal.StateValue2Meanings.Count==0)
        //                    return false;

        //                foreach (var state in signal.StateValue2Meanings) {


        //                    int StateValue =  state.Key;
        //                    string Meanings = state.Value;

        //                    string sql = string.Format(@"INSERT INTO dbo.TBL_SignalMeanings
        //                    (EquipmentTemplateId, SignalId, StateValue, Meanings)
        //                    VALUES ({0}, {1}, {2}, '{3}')",
        //                    signal.EquipmentTemplateId, signal.SignalId, StateValue, Meanings);

        //                    DBHelper.ExecuteNonQuery(sql);                    
        //                }
        //            }
        //            catch (Exception ex) {
        //                logger.ErrorFormat("SetSignalMeanings();EquipmentTemplateId={0},SignalName={1};Error={2}",
        //                    signal.EquipmentTemplateId, signal.SignalName,  ex.Message);
        //                logger.Error(ex.StackTrace);
        //                return false;
        //            }
        //            return true;
        //        }
    }
}
