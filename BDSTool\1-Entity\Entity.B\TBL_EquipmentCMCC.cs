namespace BDSTool.Entity.B
{
    using System;
    using System.Collections.Generic;
    
    public partial class TBL_EquipmentCMCC
    {
        public string DeviceID { get; set; }
        public string DeviceName { get; set; }
        public string FSUID { get; set; }
        public Nullable<int> StationId { get; set; }
        public Nullable<int> MonitorUnitId { get; set; }
        public Nullable<int> EquipmentId { get; set; }
        public string RoomName { get; set; }
        public Nullable<int> DeviceType { get; set; }
        public Nullable<int> DeviceSubType { get; set; }
        public string Model { get; set; }
        public string Brand { get; set; }
        public Nullable<double> RatedCapacity { get; set; }
        public string Version { get; set; }
        public Nullable<System.DateTime> BeginRunTime { get; set; }
        public string DevDescribe { get; set; }
        public string ExtendField1 { get; set; }
        public string ExtendField2 { get; set; }
    }
}
