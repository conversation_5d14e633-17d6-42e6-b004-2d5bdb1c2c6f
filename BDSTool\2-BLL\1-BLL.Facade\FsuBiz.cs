﻿using BDSTool.BLL.S2;
using BDSTool.DBUtility;
using BDSTool.DBUtility.Common;
using BDSTool.Entity.B;
using BDSTool.Entity.DRHelper;
using BDSTool.Utility;
using Common.Logging.Pro;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Xml;
using System.Xml.Linq;

namespace BDSTool.BLL.B
{
    public partial class FsuBiz
    {
        private readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");
        //----------------------------------------------------------
        public XmlDocument ContentXML;
        public string FSUID;
        public List<TDevConf> ListDevNewRaw;
        public string ErrorMsg = string.Empty;
        //----------------------------------------------------------
        List<TBL_EquipmentCMCCEx> ListOfEquipmentCMCCEx = new List<TBL_EquipmentCMCCEx>();
        TSL_MonitorUnitCMCCEx FsuConfig = null;
        List<DevConfExBiz> ListDevNewEx = new List<DevConfExBiz>();
        //----------------------------------------------------------
        public FsuBiz() {
        }

        public FsuBiz(XmlDocument contentXML, string fsuID, List<TDevConf> _listDevNew) {
            ContentXML = contentXML;
            FSUID = fsuID;
            ListDevNewRaw = _listDevNew;
        }

        #region 单个设备配置导入
        string InnerXML;
        public FsuBiz(string innerXML, string fsuID, List<TDevConf> _listDevNew) {
            InnerXML = innerXML;
            FSUID = fsuID;
            ListDevNewRaw = _listDevNew;
        }
        public bool OnImportConfigOnlyOneDevice() {
            //1-check management config ready
            if (!GetFsuManConfig()) {
                logger.Warn("OnImportConfigOnlyOneDevice();GetFsuManConfig Failed");
                return false;
            }
            //-------------------------------------------------------------------------------------------------
            //2-ValidInputPara
            if (!ValidateInputConfigData()) {
                logger.Warn("OnImportConfigOnlyOneDevice();ValidateListDevice Failed");
                return false;
            }
            //-------------------------------------------------------------------------------------------------                
            if (FsuConfig.IsFirstConfig) {
                //创建虚拟采集配置
                if (CreateConfigOfSampler() == false) {
                    logger.WarnFormat("OnImportConfigOnlyOneDevice();CreateConfigOfSampler Failed;FSUID={0}", FSUID);
                    return false;
                }
                else
                    logger.InfoFormat("OnImportConfigOnlyOneDevice();CreateConfigOfSampler OK;FSUID={0}", FSUID);
            }

            //-------------------------------------------------------------------------------------------------                
            if (!ImportFsuConfigOnlyOneDevice()) {
                logger.Warn("OnImportConfigOnlyOneDevice(); 自动更新配置失败");
                return false;
            }


            logger.Info("OnImportConfigOnlyOneDevice(); END OF IMPORT MU CONFIG");

            return true;
        }

        private bool ImportFsuConfigOnlyOneDevice() {
            //---------------------------------------------------------------------------------------------------
            //找出当前配置
            if (!EquipmentCMCCExBiz.GetAllByFsuId(FSUID, ListOfEquipmentCMCCEx)) {
                logger.Info("ImportFsuConfigOnlyOneDevice(); EquipmentCMCCExBiz.GetAllByFsuId Failed");
                return false;
            }

            //---------------------------------------------------------------------------------------------------
            if (!InitDeviceExList()) {
                logger.InfoFormat("ImportFsuConfigOnlyOneDevice();InitDeviceExList Failed; FSUID={0}", FsuConfig.FSUID);
                return false;
            }
            //---------------------------------------------------------------------------------------------------
            //不需要删除设备
            //if (!DelDeviceNoExist()) {
            //    logger.InfoFormat("ImportFsuConfig();DelDeviceNoExist Failed;FSUID={0}", FsuConfig.FSUID);
            //    return false;
            //}
            //logger.InfoFormat("ImportFsuConfig();DelDeviceNoExist OK;FSUID={0}", FsuConfig.FSUID);
            //---------------------------------------------------------------------------------------------------
            //新增或更新设备
            foreach (var devEx in ListDevNewEx) {
                //-------------------------------------------------------------------------------
                if (!devEx.SaveConfig()) {
                    ErrorMsg = string.Format("导入设备[{0}]配置失败,DeviceID={1}", devEx.TDevice.DeviceName, devEx.TDevice.DeviceID);

                    if (!string.IsNullOrEmpty(devEx.ErrorMsg))
                        ErrorMsg = devEx.ErrorMsg;

                    logger.Info(ErrorMsg);
                    return false;
                }
            }

            //---------------------------------------------------------------------------------------------------
            if (!AddConfigImportLogOnlyOneDevice()) {
                logger.Warn("保存导入的单设备配置XML失败");
            }
            //---------------------------------------------------------------------------------------------------
            ////save raw tsignal data  
            //foreach (var devEx in ListDevNewEx) {
            //    if (!devEx.SaveRawTSignalConfigOfOneTDevice()) {
            //        ErrorMsg = string.Format("保存设备[{0}]的TSignal原始数据失败,DeviceID={1}", devEx.TDevice.DeviceName, devEx.TDevice.DeviceID);
            //        logger.Info(ErrorMsg);
            //        return false;
            //    }
            //}
            //logger.InfoFormat("ImportFsuConfigOnlyOneDevice();SaveRawTSignalConfig OK;FSUID={0}", FsuConfig.FSUID);

            return true;
        }

        private bool AddConfigImportLogOnlyOneDevice() {
            try {
                var CfgType = "device";
                DBHelper.SaveXmlData("BM_AddConfigImportLog", new string[] { "FSUID", "CfgType" }, new string[] { FSUID, CfgType }, "CfgContent", InnerXML);
            }
            catch (Exception ex) {
                logger.ErrorFormat("AddConfigImportLogOnlyOneDevice();Error={0}", ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }

        #endregion

        #region public

     

        public bool OnImportConfig() {
            //1-check management config ready
            if (!GetFsuManConfig()) {
                logger.Warn("OnImportConfig();GetFsuManConfig Failed");
                return false;
            }
            //-------------------------------------------------------------------------------------------------
            //2-ValidInputPara
            if (!ValidateInputConfigData()) {
                logger.Warn("OnImportConfig();ValidateListDevice Failed");
                return false;
            }
            //-------------------------------------------------------------------------------------------------                
            if (FsuConfig.IsFirstConfig) {
                //创建虚拟采集配置
                if (CreateConfigOfSampler() == false) {
                    logger.WarnFormat("OnImportConfig();CreateConfigOfSampler Failed;FSUID={0}", FSUID);
                    return false;
                }
                else
                    logger.InfoFormat("OnImportConfig();CreateConfigOfSampler OK;FSUID={0}", FSUID);
            }

            //-------------------------------------------------------------------------------------------------                
            if (!ImportFsuConfig()) {
                logger.Warn("OnImportConfig(); 自动更新配置失败");
                    return false;
            }

            //-------------------------------------------------------------------------------------------------                
            //记录本次成功导入的XML配置文件
            if (!UpdateSCSyncCfgData()) {
                ErrorMsg = "存储本次导入日志失败";
                logger.Warn("OnImportConfig(); UpdateSCSyncCfgData()失败");
                return false;
            }

            //20190116 导入完成后，修改设备名称
            UpdateEquipmentAndHouseId(FSUID);

            logger.Info("OnImportConfig(); END OF IMPORT MU CONFIG");

            return true;
        }

        public bool OnReceiveConfigData(SendDevConfData data) {
            try {
                if (data == null || data.Values == null)
                    return false;

                if (!MonitorUnitCMCCBiz.Exist(data.FSUID)) {
                    LoggerBDSTool.WarnFormat("FsuBiz.OnReceiveConfigData(); failed to FindByFSUID;FSU={0}", data.FSUID);
                    return false;
                }

                //var rawText = data.ContentXML.InnerXml;
                //InnerXml=InnerXml.Replace("<?xml version=\"1.0\"?>", "");

                // create a reader and move to the content
                XDocument doc = null;
                using (XmlNodeReader nodeReader = new XmlNodeReader(data.ContentXML)) {
                    nodeReader.MoveToContent();
                    doc = XDocument.Load(nodeReader);
                }

                var InnerXml = doc.ToString();

                var CfgCode = FileMD5Helper.MD5String(InnerXml);
                var rtn = DBHelper.SaveXmlData("BM_SaveFSUSendConfig", new string[] { "FSUID", "FSUSendCfgCode" }, new string[] { data.FSUID, CfgCode }, "FSUSendCfgContent", InnerXml);

                var isUpdate = SHelper.ToBool(rtn);

                LoggerBDSTool.InfoFormat("FsuBiz.OnReceiveConfigData(), configdata is {0}", isUpdate ? "updated" : "nochange");

            }
            catch (Exception ex) {
                logger.ErrorFormat("FsuBiz.OnReceiveConfigData();FSU={0};Error={1}", data.FSUID, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }
        #endregion

        #region private
        private bool GetFsuManConfig() {
            try {
                //validate fsu 注册信息
                FsuConfig = MonitorUnitCMCCBiz.GetMainConfigByFSUID(FSUID);
                
                //20170802 s2v5.4错误问题修改
                if (FsuConfig == null) {
                    ErrorMsg = "FSU注册信息检查失败。原因：FSU基础数据配置表中无此FSUID的记录";
                    logger.WarnFormat("GetFsuManConfig();errmsg={0}", ErrorMsg);
                    return false;
                }

                //如果端口在，采集器为空，应该是在配置工具中手动删除了所有设备。容错处理，删除端口后作为新增FSU处理
                if(FsuConfig.PortId != null && FsuConfig.SamplerUnitId == null){
                    logger.WarnFormat("GetFsuManConfig();容错删除无采集单元的虚拟端口");
                    if (CommonUtils.IsNoProcedure)
                    {
                        Public_ExecuteSqlService.Instance.NoStore_TSLPort_DeleteByPortId(FsuConfig.PortId);
                    }
                    else 
                    {
                        DBHelper.ExecuteNonQuery("DELETE FROM TSL_Port WHERE PortId=" + FsuConfig.PortId);
                    }
                    FsuConfig = MonitorUnitCMCCBiz.GetMainConfigByFSUID(FSUID);
                }


                if (FsuConfig == null) {
                    ErrorMsg = "FSU注册信息检查失败。原因：FSU基础数据配置表中无此FSUID的记录";
                    logger.WarnFormat("GetFsuManConfig();errmsg={0}", ErrorMsg);
                    return false;
                }
                if ((FsuConfig.PortId == null & FsuConfig.SamplerUnitId != null) ||
                    (FsuConfig.PortId != null & FsuConfig.SamplerUnitId == null)) {
                    ErrorMsg = "FSU注册信息检查失败。原因：默认虚拟端口和采集器配置错误,请手工删除错误配置";
                    logger.WarnFormat("GetFsuManConfig();errmsg={0}", ErrorMsg);
                    return false;
                }
                //4-通过检查最新配置表中的记录，判断是否为新建配置               
                if (FsuConfig.PortId == null & FsuConfig.SamplerUnitId == null) {
                    FsuConfig.IsFirstConfig = true;
                    logger.Info("新建FSU配置");
                }
                else {
                    FsuConfig.IsFirstConfig = false;
                    logger.Info("更新FSU配置");
                }
            }
            catch (Exception ex) {
                logger.ErrorFormat("GetFsuManConfig();FSUID={0};Error={1}", FsuConfig.FSUID, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }

            return true;
        }
        private bool ValidateInputConfigData() {
            try {
                //1. DeviceId 必须唯一
                //动环设备ID：即监控对象的编码，该编码在站点内唯一，可配置，后面简称“设备ID”具体定义详见《中国移动动环命名及编码指导意见》
                var idList = ListDevNewRaw.ConvertAll(o => o.DeviceID);
                var idListDis = idList.Distinct().ToList();
                if (idList.Count != idListDis.Count) {
                    string s = String.Join(",", idList);
                    ErrorMsg = "FSU配置中存在重复的DeviceID. 待导入的DeviceID：" + s;
                    logger.WarnFormat("FsuBiz.ValidateInputConfigData();Failed; DeviceID存在重复;" + s);
                    return false;
                }

                //2 局房必须存在
                var allRoomName = HouseBiz.GetAllHouseName(FsuConfig.StationId);
                if (allRoomName == null || allRoomName.Count == 0) {
                    ErrorMsg = "局站中未配置任何配置";
                    logger.WarnFormat("FsuBiz.ValidateInputConfigData();Failed; " + ErrorMsg);
                    return false;
                }

                foreach (var dev in ListDevNewRaw) {
                    if (!allRoomName.Exists(o => o == dev.RoomName)) {
                        ErrorMsg = "局站中未配置局房：" + dev.RoomName;
                        logger.WarnFormat("FsuBiz.ValidateInputConfigData();Failed; " + ErrorMsg);
                        return false;
                    }
                }

                //信号ID有效性检查
                foreach (var device in ListDevNewRaw){
                    if(!TDevConfBiz.ValidateTSigId(device,ref ErrorMsg)){
                        logger.WarnFormat("FsuBiz.ValidateInputConfigData();Failed; errmsg={0}", ErrorMsg);
                        return false;
                    }
                }

                //20170925 req:mail from liang; 导入局站配置时，从字典表取含义再导入数据库
                //信号描述字段设置-->
                //1) get dic
                Dictionary<int, string> dicStandard = StandardDicBiz.LoadStandardDic();
                //2) fill describe
                foreach (var device in ListDevNewRaw)
                    foreach (var tsig in device.Signals) {
                        if (tsig.Type == EnumType.DI || tsig.Type == EnumType.DO) {
                            var desc = TSignalBiz.GetDescribe(tsig, dicStandard);
                            if (!string.IsNullOrWhiteSpace(desc))
                                tsig.Describe = desc;
                            else{
                                if (tsig.Type == EnumType.DI)
                                    logger.WarnFormat("GetDescribeFromDB();DI;Failed; use default value={0}", tsig.Describe);        
                                else if(tsig.Type == EnumType.DO)
                                    logger.InfoFormat("GetDescribeFromDB();DO;Failed; use default value={0}", tsig.Describe);        
                            }

                            logger.DebugFormat("GetDescribeFromDB();Type={0}; Describe={1}", tsig.Type, tsig.Describe);                                   
                        }
                    }
                //<--

                //信号描述字段检查
                foreach (var device in ListDevNewRaw)
                    foreach (var tsig in device.Signals) {
                        if (!TSignalBiz.ValidateDescribe(tsig)) {
                            ErrorMsg = string.Format("待导入监控点信号TSignal的Describe字段无效.设备:{0},监控点ID:{1},Describe:{2}",
                                device.DeviceName, tsig.ID, SHelper.GetPara(tsig.Describe));
                            logger.WarnFormat("FsuBiz.ValidateInputConfigData();Failed; errmsg={0}", ErrorMsg);
                            return false;
                        }
                    }
            }
            catch (Exception ex) {
                logger.ErrorFormat("FsuBiz.ValidateInputConfigData();Error={0}", ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }



        private bool ImportFsuConfig() {
            //---------------------------------------------------------------------------------------------------
            //找出当前配置
            if (!EquipmentCMCCExBiz.GetAllByFsuId(FSUID, ListOfEquipmentCMCCEx)) {
                logger.Info("FacadeConfigImport.ImportFsuConfig(); EquipmentCMCCExBiz.GetAllByMuId Failed");
                return false;
            }

            //---------------------------------------------------------------------------------------------------
            if (!InitDeviceExList()) {
                logger.InfoFormat("ImportFsuConfig();CreateDeviceExList Failed; FSUID={0}", FsuConfig.FSUID);
                return false;
            }
            //---------------------------------------------------------------------------------------------------
            //删除设备
            if (!DelDeviceNoExist()) {
                logger.InfoFormat("ImportFsuConfig();DelDeviceNoExist Failed;FSUID={0}", FsuConfig.FSUID);
                return false;
            }
            logger.InfoFormat("ImportFsuConfig();DelDeviceNoExist OK;FSUID={0}", FsuConfig.FSUID);
            //---------------------------------------------------------------------------------------------------
            //新增或更新设备
            foreach (var devEx in ListDevNewEx) {
                //-------------------------------------------------------------------------------
                if (!devEx.SaveConfig()){
                    ErrorMsg = string.Format("导入设备[{0}]配置失败,DeviceID={1}", devEx.TDevice.DeviceName, devEx.TDevice.DeviceID);
                 
                    if(!string.IsNullOrEmpty(devEx.ErrorMsg))
                        ErrorMsg=devEx.ErrorMsg;

                    logger.Info(ErrorMsg);
                    return false;
                }
            }

            //---------------------------------------------------------------------------------------------------
            //save raw tsignal data  
            if (CommonUtils.IsNoProcedure) 
            {
                Public_ExecuteSqlService.Instance.NoStore_TBLFsuTSignalCMCC_DeleteByFSUID(SHelper.GetPara(FSUID));
            }
            else
            {
                DBHelper.ExecuteNonQuery("DELETE FROM TBL_FsuTSignalCMCC WHERE FSUID=" + SHelper.GetPara(FSUID));
            }
            foreach (var devEx in ListDevNewEx) {
                if (!devEx.SaveRawTSignalConfigOfOneTDevice()) {
                    ErrorMsg = string.Format("保存设备[{0}]的TSignal原始数据失败,DeviceID={1}", devEx.TDevice.DeviceName, devEx.TDevice.DeviceID);           
                    logger.Info(ErrorMsg);
                    return false;
                }
            }
            logger.InfoFormat("ImportFsuConfig();SaveRawTSignalConfig OK;FSUID={0}", FsuConfig.FSUID);

            return true;
        }

        private bool DelDeviceNoExist() {
            //---------------------------------------------------------------------------------------------------
            //找出待删除和待更新的设备
            var listDevOld2Update = (from devOld in ListOfEquipmentCMCCEx
                                     join devNew in ListDevNewEx on devOld.DeviceID equals devNew.TDevice.DeviceID
                                     select devOld).ToList();

            var ListDevOld2Del = ListOfEquipmentCMCCEx.Except(listDevOld2Update).ToList();


            var listNewDeviceUpdate = (from devNew in ListDevNewEx
                                       join devOld in ListOfEquipmentCMCCEx on devNew.TDevice.DeviceID equals devOld.DeviceID
                                       select devNew).ToList();
            var listDevNew = ListDevNewEx.Except(listNewDeviceUpdate).ToList();

            //logger.InfoFormat("FSUID={0}; AddCount={1},DelCount={2},MayUpdaeCount={3}",
            //FsuConfig.FSUID, listDevNew.Count, ListDevOld2Del.Count, listDevOld2Update.Count);

            if (ListDevOld2Del.Count > 0) {
                logger.Info("DelDeviceNoExist();更新配置之前删除已有设备级配置------------->");
                int DelDeviceCnt = 0;
                foreach (var equipmentCMCCEx in ListDevOld2Del) {
                    DevConfExBiz.DeleteDeviceConfig(equipmentCMCCEx.FSUID,equipmentCMCCEx.DeviceID, equipmentCMCCEx.StationId.Value, equipmentCMCCEx.EquipmentId.Value, equipmentCMCCEx.EquipmentTemplateId);
                    DelDeviceCnt++;
                }

                if (DelDeviceCnt > 0)
                    if (FsuConfig.IsFirstConfig)
                        logger.WarnFormat("DelDeviceNoExist();新建FSU配置时发现已有设备配置{0}个, 容错处理已自动删除; FSUID={1}", DelDeviceCnt, FsuConfig.FSUID);
                    else
                        logger.InfoFormat("DelDeviceNoExist();FSUID={0};更新FSU配置之前，删除设备配置{0}个", DelDeviceCnt);

                logger.Info("DelDeviceNoExist();更新配置之前删除已有设备级配置<-------------");
            }
            return true;
        }
     
        private  bool InitDeviceExList() {
            try {
                foreach (var dev in ListDevNewRaw) {
                    var devEx = new DevConfExBiz();

                    TBL_EquipmentCMCCEx existDevCfg = ListOfEquipmentCMCCEx.FirstOrDefault(o => o.DeviceID == dev.DeviceID);
                    if (!devEx.Init(FsuConfig, dev, existDevCfg, ref ErrorMsg)) {
                        logger.WarnFormat("IntDeviceExList(); devEx.Init Failed; FSUID={0}", FsuConfig.FSUID);
                        return false;
                    }

                    ListDevNewEx.Add(devEx);
                }
            }
            catch (Exception ex) {
                logger.ErrorFormat("InitDeviceExList();Error={0}", ex.Message);
                logger.Error(ex.StackTrace);

                if (string.IsNullOrEmpty(ErrorMsg))
                    ErrorMsg = "转换设备{0}的配置出错.错误信息:"+ ex.Message;
                return false;
            }
            return true;
        }


        private bool DelDevicesBaseOnImportConfig(List<TBL_EquipmentCMCCEx> listDevExOld2Del, ref int DelDeviceCnt) {

            return true;
        }

        private bool UpdateSCSyncCfgData() {
            try {
                XDocument doc = null;
                using (XmlNodeReader nodeReader = new XmlNodeReader(ContentXML)) {
                    nodeReader.MoveToContent();
                    doc = XDocument.Load(nodeReader);
                }

                var InnerXml = doc.ToString();

                var CfgCode = FileMD5Helper.MD5String(InnerXml);
                if (CommonUtils.IsNoProcedure)
                {
                    Public_StoredService.Instance.BM_SaveImportConfig(FSUID,CfgCode, InnerXml);
                }
                else {
                    var rtn = DBHelper.SaveXmlData("BM_SaveImportConfig", new string[] { "FSUID", "SCSyncCfgCode" }, new string[] { FSUID, CfgCode }, "SCSyncCfgContent", InnerXml);
                }

            }
            catch (Exception ex) {
                logger.ErrorFormat("UpdateSCSyncCfgData();Error={0}", ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }

        //20190116 zdk 配置导入成功后，修改设备名称（名称从交维的设备资源表获得）
        private void UpdateEquipmentAndHouseId(string FSUID)
        {
            try
            {
                string sql = string.Format("BU_V3_UpdateEquipNameAndHouseIdCMCC {0}", SHelper.GetPara(FSUID));
                //DBHelper.ExecuteNonQuery(sql);
                try
                {
                    if(CommonUtils.IsNoProcedure)
                    {
                        WO_TestOrderDalService.Instance.UpdateEquipmentAndHouseId(FSUID);
                    }
                    else
                    {
                        using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                        {
                            dbHelper.CreateProcedureCommand("BU_V3_UpdateEquipNameAndHouseIdCMCC");
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("FSUID", DbType.AnsiString, SHelper.GetProcPara(FSUID)));
                            dbHelper.ExecuteNoQuery();
                        }
                    }
                }
                catch (Exception ex)
                {
                    DBHelper.LogError(sql, ex);
                }
            }
            catch (Exception ex)
            {
                logger.ErrorFormat("UpdateEquipmentAndHouseId(); exec BU_V3_UpdateEquipNameAndHouseIdCMCC failed; SUID={0};Error={1}", FSUID, ex.Message);
            }
        }

        private bool CreateConfigOfSampler() {
            int SamplerId = 0;
            if (!SamplerUnitBiz.GetSamplerIdOfSelfDiagnosis(ref SamplerId))
                return false;

            int newPortId = 0;
            if (!GlobalIdentityBiz.GetNewId("TSL_Port", ref newPortId))
                return false;
            int newSamplerUnitId = 0;
            if (!GlobalIdentityBiz.GetNewId("TSL_SamplerUnit", ref newSamplerUnitId))
                return false;


            var sql = string.Format("BM_CreateConfigOfSample {0},{1},{2},{3},{4}",
            SHelper.GetPara(FsuConfig.FSUID), FsuConfig.MonitorUnitId, newPortId, newSamplerUnitId, SamplerId);

            //DBHelper.ExecuteNonQuery(sql);
            try
            {
                if(CommonUtils.IsNoProcedure)
                {
                    BM_GetFSUMainConfigService.Instance.BM_CreateConfigOfSample(SHelper.GetProcPara(FsuConfig.FSUID), FsuConfig.MonitorUnitId, newPortId, newSamplerUnitId, SamplerId);
                }
                using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                {
                    dbHelper.CreateProcedureCommand("BM_CreateConfigOfSample");
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("FSUID", DbType.AnsiString, SHelper.GetProcPara(FsuConfig.FSUID)));
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("MonitorUnitId", DbType.Int32, FsuConfig.MonitorUnitId));
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("PortId", DbType.Int32, newPortId));
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("SamplerUnitId", DbType.Int32, newSamplerUnitId));
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("SamplerId", DbType.Int32, SamplerId));
                    dbHelper.AddDbParameter(dbHelper.GetReturnDbParameter());
                    dbHelper.ExecuteNoQuery();
                }
            }
            catch (Exception ex)
            {
                DBHelper.LogError(sql, ex);
            }

            FsuConfig.PortId = newPortId;
            FsuConfig.SamplerUnitId = newSamplerUnitId;
            return true;
        }

        private static bool DelAllDeviceCfgDataOfFSU(string FSUID, ref int DelDeviceCnt) {
            var equipIdList = new List<EquipmentKey>();
            if (!EquipmentCMCCBiz.GetEquipmentIdListByFsuId(FSUID, ref equipIdList)) {
                return false;
            }

            DelDeviceCnt = 0;
            foreach (var eid in equipIdList) {
                var sql = string.Format("BM_DelAllDeviceCfgDataOfFSU {0},{1},{2},{3}"
                    , SHelper.GetPara(eid.FSUID), SHelper.GetPara(eid.DeviceID), SHelper.GetPara(eid.StationId), SHelper.GetPara(eid.EquipmentId));
                //DBHelper.ExecuteNonQuery(sql);
                try
                {
                    using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                    {
                        dbHelper.CreateProcedureCommand("BM_DelAllDeviceCfgDataOfFSU");
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("FSUID", DbType.AnsiString, SHelper.GetProcPara(eid.FSUID)));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("DeviceID", DbType.Int32, SHelper.GetProcPara(eid.DeviceID)));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("StationId", DbType.Int32, SHelper.GetProcPara(eid.StationId)));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("EquipmentId", DbType.Int32, SHelper.GetProcPara(eid.EquipmentId)));
                        dbHelper.AddDbParameter(dbHelper.GetReturnDbParameter());
                        dbHelper.ExecuteNoQuery();
                    }
                }
                catch (Exception ex)
                {
                    DBHelper.LogError(sql, ex);
                }
                CfgEquipmentOP.LogConfigChange(EditType.Delete, eid.StationId, eid.EquipmentId);

                DelDeviceCnt++;
            }

            return true;
        }
        #endregion

        #region bak
        //public static bool CreateConfigOfSampler(ref TSL_MonitorUnitCMCCEx fsuConfig) {
        //    //创建虚拟端口
        //    int newPortId = 0;
        //    if (FsuPortBiz.AddOne(fsuConfig, ref newPortId) == false) {
        //        logger.InfoFormat("CreateConfigOfSampler();FSUID={0};PortBiz.AddOne Failed ", fsuConfig.FSUID);
        //        return false;
        //    }

        //    //创建虚拟采集单元
        //    int newSamplerUnitId = 0;
        //    if (FsuSamplerUnitBiz.AddOne(fsuConfig, newPortId, ref newSamplerUnitId) == false) {
        //        logger.InfoFormat("CreateConfigOfSampler();FSUID={0};SamplerUnitBiz.AddOne Failed ", fsuConfig.FSUID);
        //        return false;
        //    }

        //    fsuConfig.PortId = newPortId;
        //    fsuConfig.SamplerUnitId = newSamplerUnitId;
        //    return true;
        //}

        //private static bool DelAllDeviceCfgDataOfFSU(string FSUID, ref int DelDeviceCnt) {
        //    var sql = string.Format("BM_DelAllDeviceCfgDataOfFSU {0}", SHelper.GetPara(FSUID));
        //    var rtn = DBHelper.ExecuteScalar(sql);
        //    if (string.IsNullOrEmpty(rtn))
        //        DelDeviceCnt = 0;
        //    else
        //        DelDeviceCnt = int.Parse(rtn);
        //    return true;
        //}

        #endregion
    }
}
