﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class GetAoDataAck : BMessage
    {
        public List<Device> LDevice { get; set; }

        #region SiteWeb配置

        public int MyStationId { get; set; }

        #endregion
        public GetAoDataAck() : base()
        {
            MessageType = (int)BMessageType.GET_AODATA_ACK;

        }
        public GetAoDataAck(string suids, string surids, List<Device> ldevice) : base()
        {
            MessageType = (int)BMessageType.GET_AODATA_ACK;
            SUId = suids;
            SURId = surids;
            LDevice = ldevice;
        }

        public static GetAoDataAck Deserialize(XmlDocument xmlDoc)
        {
            GetAoDataAck getAoDataAck = null;
            try
            {
                string suid = xmlDoc.SelectSingleNode("/Response/Info/SUId").InnerText.Trim();
                string surid = xmlDoc.SelectSingleNode("/Response/Info/SURId").InnerText.Trim();
                XmlNodeList nodes = xmlDoc.SelectNodes("/Response/Info/Values/DeviceList/Device");
                List<Device> list = new List<Device>();
                foreach (XmlElement node in nodes)
                {
                    string id = node.GetAttribute("Id");
                    string rId = node.GetAttribute("RId");
                    List<Signal> lst = new List<Signal>();
                    XmlNodeList lists = node.SelectNodes("Signal");
                    foreach (XmlElement n in lists)
                    {
                        Signal sdv = new Signal();
                        sdv.Id = n.GetAttribute("Id");
                        if(string.IsNullOrEmpty(n.GetAttribute("SetValue")))
                        {
                            sdv.SetValue = null; 
                        }
                        else
                        {
                            sdv.SetValue = float.Parse(n.GetAttribute("SetValue"));
                        }
                        if (string.IsNullOrEmpty(n.GetAttribute("HLimit")))
                        {
                            sdv.HLimit = null;
                        }
                        else
                        {
                            sdv.HLimit = float.Parse(n.GetAttribute("HLimit"));
                        }
                        if (string.IsNullOrEmpty(n.GetAttribute("SHLimit")))
                        {
                            sdv.SHLimit = null;
                        }
                        else
                        {
                            sdv.SHLimit = float.Parse(n.GetAttribute("SHLimit"));
                        }
                        if (string.IsNullOrEmpty(n.GetAttribute("LLimit")))
                        {
                            sdv.LLimit = null;
                        }
                        else
                        {
                            sdv.LLimit = float.Parse(n.GetAttribute("LLimit"));
                        }
                        if (string.IsNullOrEmpty(n.GetAttribute("SLLimit")))
                        {
                            sdv.SLLimit = null;
                        }
                        else
                        {
                            sdv.SLLimit = float.Parse(n.GetAttribute("SLLimit"));
                        }
                        if (string.IsNullOrEmpty(n.GetAttribute("Threshold")))
                        {
                            sdv.Threshold = null;
                        }
                        else
                        {
                            sdv.Threshold = float.Parse(n.GetAttribute("Threshold"));
                        }
                        if (string.IsNullOrEmpty(n.GetAttribute("RelativeVal")))
                        {
                            sdv.RelativeVal = null;
                        }
                        else
                        {
                            sdv.RelativeVal  = float.Parse(n.GetAttribute("RelativeVal"));
                        }
                        if (string.IsNullOrEmpty(n.GetAttribute("IntervalTime")))
                        {
                            sdv.IntervalTime = null;
                        }
                        else
                        {
                            sdv.IntervalTime = float.Parse(n.GetAttribute("IntervalTime"));
                        }
                        lst.Add(sdv);
                    }
                    Device device = new Device(id, rId, lst);
                    list.Add(device);
                }
                getAoDataAck = new GetAoDataAck(suid, surid, list);
                entityLogger.DebugFormat("GetAoDataAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                getAoDataAck.StringXML = xmlDoc.InnerXml;
                return getAoDataAck;

            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetDoAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetDoAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                getAoDataAck = new GetAoDataAck();
                getAoDataAck.ErrorMsg = ex.Message;
                getAoDataAck.StringXML = xmlDoc.InnerXml;
                return getAoDataAck;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            if (LDevice.Count > 0)
            {
                foreach (Device de in LDevice)
                {
                    sb.AppendFormat("{0},{1},{2},{3},{4},{5}\n", MessageId, (BMessageType)MessageType,MessageType, SUId, SURId, de.ConvertToString());
                }
            }
            else
            {
                sb.AppendFormat("{0},{1},{2},{3},{4}", MessageId, (BMessageType)MessageType,MessageType, SUId, SURId);
            }
            return sb.ToString();
        }
    }
}
