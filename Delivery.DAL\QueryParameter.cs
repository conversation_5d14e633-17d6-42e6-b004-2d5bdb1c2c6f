﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Runtime.Serialization;

namespace Delivery.DAL
{

    public class QueryParameter
    {
        public QueryParameter(string name, DataType dataType, string value)
        {
            Name = name;
            DataType = dataType;
            Value = value;
        }

        public String Name
        {
            get;
            set;
        }

        public DataType DataType
        {
            get;
            set;
        }

        public String Value
        {
            get;
            set;
        }
    }
}