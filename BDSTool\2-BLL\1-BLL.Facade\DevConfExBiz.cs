﻿
using BDSTool.BLL.B;
using BDSTool.BLL.Convert;
using BDSTool.BLL.S2;
using BDSTool.DBUtility;
using BDSTool.DBUtility.Common;
using BDSTool.Entity.B;
using BDSTool.Entity.S2;
using BDSTool.Utility;
using Common.Logging.Pro;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.BLL.B
{
    public partial class DevConfExBiz 
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");
        #region prop
        public string ErrorMsg;
        private bool IsNew = true;
        private bool IsDevicePropChange = true;
        
        #region from db
        public TBL_EquipmentCMCCEx EquipmentCMCCEx;

        #endregion

        #region from fsu
        public TDevConf TDevice;
        public List<TSignalEx> ListOfTSignalEx;
        #endregion

        public string FSUID {            get;            set;        }
        public int StationId {            get;            set;        }
        public int HouseId {            get;            set;        }
        public int EquipmentCategory {            get;            set;        }
        public int? MonitorUnitId {            get;            set;        }
        public int? SamplerUnitId {            get;            set;        }

        public TBL_EquipmentTemplate EquipmentTemplate;
        public TBL_Equipment Equipment;
        #endregion

        #region public
        public DevConfExBiz() {


            TDevice = new TDevConf();
            ListOfTSignalEx = new List<TSignalEx>();
            
            Equipment = new TBL_Equipment();
            EquipmentTemplate = new TBL_EquipmentTemplate(){

                Signals = new List<TBL_Signal>(),
                Events = new List<TBL_Event>(),
                Controls = new List<TBL_Control>()
            };


        }


        public bool Init(TSL_MonitorUnitCMCCEx fsuConfigRow, TDevConf dev, TBL_EquipmentCMCCEx equipmentCMCCEx, ref string errmsg) {
            TDevice = dev;

            if (equipmentCMCCEx != null) {
                
                IsNew = false;

                EquipmentCMCCEx = equipmentCMCCEx;

                if (EquipmentCMCCEx.DeviceName == dev.DeviceName && EquipmentCMCCEx.RoomName == dev.RoomName && EquipmentCMCCEx.DeviceType == dev.DeviceType &&
                    EquipmentCMCCEx.DeviceSubType == dev.DeviceSubType && EquipmentCMCCEx.Model == dev.Model && EquipmentCMCCEx.Brand == dev.Brand &&
                    EquipmentCMCCEx.RatedCapacity == dev.RatedCapacity && EquipmentCMCCEx.Version == dev.Version && EquipmentCMCCEx.BeginRunTime == dev.BeginRunTime
                    && EquipmentCMCCEx.DevDescribe == dev.DevDescribe)
                    IsDevicePropChange = false;
            }


            if (fsuConfigRow.IsFirstConfig && EquipmentCMCCEx != null) {
                DevConfExBiz.DeleteDeviceConfig(EquipmentCMCCEx.FSUID, EquipmentCMCCEx.DeviceID, EquipmentCMCCEx.StationId.Value, EquipmentCMCCEx.EquipmentId.Value, EquipmentCMCCEx.EquipmentTemplateId);

                logger.WarnFormat("DevConfExBiz.Init();新建配置之前发现已有设备配置,容错删除; dev={0}", dev.DeviceName);
            }

            //----------------------------------------------------------------------------------
             if(!TSignalEx.Convert2TSignalExList(dev.Signals, ref ListOfTSignalEx, ref errmsg)) {
                 logger.WarnFormat("DevConfExBiz.Init();监控对象转换失败.设备名称：{0}", dev.DeviceName);
                 return false;
             }

            //----------------------------------------------------------------------------------
             if (EquipmentCMCCEx != null) {
                 Equipment.EquipmentId = EquipmentCMCCEx.EquipmentId.Value;
                 EquipmentTemplate.EquipmentTemplateId = EquipmentCMCCEx.EquipmentTemplateId;
            }
            else{
                if (!CreateEquipIdAndEquipmentTemplateId(ref errmsg)) {
                    return false;
                }
            }
            //----------------------------------------------------------------------------------------------------------
            if (!SetManConfig(fsuConfigRow, dev, ref errmsg)) {
                logger.InfoFormat("DevConfExBiz.Init();管理配置设置失败;FSUID={0}, DevId={1}", fsuConfigRow.FSUID, dev.DeviceID);
                return false;
            }

            if (!SetSiteWebConfigData(ref errmsg)) {
                logger.InfoFormat("DevConfExBiz.Init();配置转换失败;FSUID={0}, DevId={1}", fsuConfigRow.FSUID, dev.DeviceID);
                return false;
            }
            return true;
        }

        //public bool Obsolete_SaveEquipmentConfig(DevConfExBiz dev, ref string errmsg) {
        //    //-------------------------------------------------------------------------------
        //    if (!EquipmentCMCCBiz.Obsolete_SaveEntity(dev.EquipmentCMCCEx))
        //        return false;
        //    if (!EquipmentTemplateBiz.SaveEntity(dev.EquipmentTemplate)) {
        //        logger.ErrorFormat("SaveEquipmentConfig(); failed to EquipmentTemplateBiz.SaveEntity; EquipmentTemplateName={0}", dev.EquipmentTemplate.EquipmentTemplateName);
        //        return false;
        //    }
        //    if (!EquipmentBiz.SaveEntity(dev.Equipment)) {
        //        logger.InfoFormat("SaveEquipmentConfig(); failed to EquipmentBiz.SaveEntity; FSUID={0}, DeviceName={1}", dev.FSUID, dev.TDevice.DeviceName);
        //        DeleteDeviceConfig(dev.FSUID, dev.TDevice.DeviceID, dev.StationId, dev.Equipment.EquipmentId, dev.EquipmentTemplate.EquipmentTemplateId);
        //        errmsg = string.Format("设备配置存库失败.待导入设备名:{0}", dev.TDevice.DeviceName);
        //        return false;
        //    }
        //    //-------------------------------------------------------------------------------
        //    if (!TSignalsBiz.InsertConfigOfTSEC(dev.EquipmentTemplate)) {
        //        logger.InfoFormat("SaveEquipmentConfig(); failed to EquipmentTemplateBiz.DeepSaveEntity; FSUID={0}, DeviceName={1}", dev.FSUID, dev.TDevice.DeviceName);
        //        DeleteDeviceConfig(dev.FSUID, dev.TDevice.DeviceID, StationId, Equipment.EquipmentId, EquipmentTemplate.EquipmentTemplateId);
        //        errmsg = string.Format("设备模板配置存库失败.待导入设备名:{0}", dev.TDevice.DeviceName);
        //        return false;
        //    }
        //    //-------------------------------------------------------------------------------
        //    CfgEquipmentOP.LogConfigChange(EditType.Add, dev.Equipment.StationId, dev.Equipment.EquipmentId);
        //    //-------------------------------------------------------------------------------

        //    return true;
        //}


        public bool SaveConfig() {
            try {
                logger.InfoFormat("SAVE DEV-CFG------------------------------------------------------------------------------>");
                logger.InfoFormat("DeviceName={0}", TDevice.DeviceName);
                if (IsNew) {
                    //add
                    if (!InsertConfigAll()) {
                        logger.InfoFormat("SaveConfig();InsertConfigAll Failed;dev={0}", TDevice.DeviceName);
                        ErrorMsg = string.Format("新建FSU配置时配置存库失败.待导入设备名:{0}", TDevice.DeviceName);
                        return false;
                    }
                }
                else {
                    if (!UpdateSiteWebConfigV2()) {
                        return false;
                    }
                }
            }
            catch (Exception ex) {
                logger.ErrorFormat("SaveConfig();;dev={0}; EquipId={1},EquipTempId={2}, error={3}", 
                    TDevice.DeviceName, Equipment.EquipmentId, EquipmentTemplate.EquipmentTemplateId, ex.Message);

                logger.Error(ex.StackTrace);
                return false;
            }
            logger.InfoFormat("<------------------------------------------------------------------------------SAVE DEV-CFG");
            return true;
        }

        //20170518 del obsolete code
        //private bool UpdateSiteWebConfigV1() {
        //    if (IsDevicePropChange) {//设备级信号级全部重置
        //        if (!UpdateConfigOfDeviceLevel()) {
        //            logger.InfoFormat("UpdateSiteWebConfigV1();UpdateConfigOfDeviceLevel Failed;dev={0}", TDevice.DeviceName);
        //            ErrorMsg = string.Format("设备信息更新失败.待导入设备名:{0}", TDevice.DeviceName);
        //            return false;
        //        }
        //    }
        //    //信号级重置
        //    DevConfExBiz.DeleteDeviceConfigOnlyTSignal(EquipmentCMCCEx);
        //    //-------------------------------------------------------------------------------
        //    if (!TSignalsBiz.InsertConfigOfTSEC(EquipmentTemplate)) {
        //        logger.InfoFormat("SaveConfigUpdateSiteWebConfigV1 failed to InsertConfigOfSEC; FSUID={0}, DeviceName={1}", FSUID, TDevice.DeviceName);
        //        DeleteDeviceConfig(FSUID, TDevice.DeviceID, StationId, Equipment.EquipmentId, EquipmentTemplate.EquipmentTemplateId);
        //        ErrorMsg = string.Format("设备模板配置存库失败.待导入设备名:{0}", TDevice.DeviceName);
        //        return false;
        //    }

        //    CfgEquipmentOP.LogConfigChange(EditType.Modified, Equipment.StationId, Equipment.EquipmentId);//kkk
        //    return true;
        //}

        private bool UpdateSiteWebConfigV2() {
            logger.InfoFormat("更新设备配置;dev={0}; EquipId={1},EquipTempId={2}", TDevice.DeviceName, Equipment.EquipmentId, EquipmentTemplate.EquipmentTemplateId);

            if (IsDevicePropChange) {
                if (!UpdateConfigOfDeviceLevel()) {
                    logger.InfoFormat("UpdateSiteWebConfigV2();UpdateConfigOfDeviceLevel Failed;dev={0}", TDevice.DeviceName);
                    ErrorMsg = string.Format("设备信息更新失败.待导入设备名:{0}", TDevice.DeviceName);
                    return false;
                }
                //-------------------------------------------------------------------------------
                //0825 fix bug,house change no update as
                CfgEquipmentOP.LogConfigChange(EditType.Modified, Equipment.StationId, Equipment.EquipmentId);
                //-------------------------------------------------------------------------------
            }
            else
                logger.InfoFormat("UpdateSiteWebConfigV2(); 设备属性不变");
            //------------------------------------------------------------------------------------------------------------------------------------------
            var signalsBiz = new TSignalsBiz(Equipment, EquipmentTemplate, ListOfTSignalEx);

            if (!signalsBiz.Save()) {
                logger.InfoFormat("UpdateSiteWebConfigV2();UpdateConfigOfSec Failed;dev={0}", TDevice.DeviceName);
                ErrorMsg = signalsBiz.ErrorMsg;
                return false;
            }
  
            return true;
        }

            

        public static void DeleteDeviceConfig(string FSUID, string DeviceID, int StationId, int EquipmentId, int EquipmentTemplateId) {
            logger.InfoFormat("DelOneDevice();删除已有设备级配置; EquipmentId={0},EquipmentTemplateId={1}",
            EquipmentId, EquipmentTemplateId);

            var sql = string.Format("BM_DeviceConfigDel {0},{1},{2},{3},{4}"                
                ,SHelper.GetPara(FSUID), SHelper.GetPara(DeviceID),SHelper.GetPara(StationId), SHelper.GetPara(EquipmentId), SHelper.GetPara(EquipmentTemplateId));
            //DBHelper.ExecuteNonQuery(sql);
            try
            {
                using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                {
                    if(CommonUtils.IsNoProcedure)
                    {
                        BM_DeviceConfigService.Instance.BM_DeviceConfigDel(FSUID, DeviceID, StationId, EquipmentId, EquipmentTemplateId);
                        return;
                    }
                    dbHelper.CreateProcedureCommand("BM_DeviceConfigDel");
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("FSUID", DbType.AnsiString, SHelper.GetProcPara(FSUID)));
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("DeviceID", DbType.AnsiString, SHelper.GetProcPara(DeviceID)));
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("StationId", DbType.Int32, StationId));
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("EquipmentId", DbType.Int32, EquipmentId));
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("EquipmentTemplateId", DbType.Int32, EquipmentTemplateId));
                    dbHelper.AddDbParameter(dbHelper.GetReturnDbParameter());
                    dbHelper.ExecuteNoQuery();
                }
            }
            catch (Exception ex)
            {
                DBHelper.LogError(sql, ex);
            }
            CfgEquipmentOP.LogConfigChange(EditType.Delete, StationId, EquipmentId);
        }
        public bool SaveRawTSignalConfigOfOneTDevice() {
            try {

                //2-save TSignal
                var dataList = new List<IBatchInsertRow>();
                foreach (var tSignal in ListOfTSignalEx) {
                    var sigData = TBL_FsuTSignalCMCC.ConvertFromTSignal(FSUID, this, tSignal);
                    dataList.Add(sigData);
                }

                if (!BatchInsertHelper.Execute(dataList)) {
                    logger.InfoFormat("SaveRawTSignalConfigOfOneTDevice();批量插入失败; FSUID={0}", FSUID);
                    return false;
                }

            }
            catch (Exception ex) {
                logger.ErrorFormat("SaveRawConfigOfOneTDevice();FSUID={0};Error={1}", FSUID, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }

            return true;
        }

        public bool InsertConfigAll() {
            //-------------------------------------------------------------------------------
            logger.InfoFormat("新增设备配置; dev={0}; EquipId={1},EquipTempId={2}", TDevice.DeviceName, Equipment.EquipmentId, EquipmentTemplate.EquipmentTemplateId);

            if (!SaveConfigOfDeviceLevel())
                return false;

            //-------------------------------------------------------------------------------
            if (!TSignalsBiz.InsertConfigOfTSEC(EquipmentTemplate)) {
                logger.InfoFormat("InsertConfigAll(); failed to EquipmentTemplateBiz.DeepSaveEntity; FSUID={0}, DeviceName={1}", FSUID, TDevice.DeviceName);
                DeleteDeviceConfig(FSUID, TDevice.DeviceID, StationId, Equipment.EquipmentId, EquipmentTemplate.EquipmentTemplateId);
                ErrorMsg = string.Format("设备模板配置存库失败.待导入设备名:{0}", TDevice.DeviceName);
                return false;
            }
            //-------------------------------------------------------------------------------
            CfgEquipmentOP.LogConfigChange(EditType.Add, Equipment.StationId, Equipment.EquipmentId);
            //-------------------------------------------------------------------------------
            return true;
        }

        public bool InsertConfigTSigOnly() {
            //-------------------------------------------------------------------------------
            logger.InfoFormat("InsertConfigTSigOnly();dev={0}; EquipId={1},EquipTempId={2}", TDevice.DeviceName, Equipment.EquipmentId, EquipmentTemplate.EquipmentTemplateId);
            //-------------------------------------------------------------------------------
            if (!TSignalsBiz.InsertConfigOfTSEC(EquipmentTemplate)) {
                logger.InfoFormat("InsertConfigAll(); failed to EquipmentTemplateBiz.DeepSaveEntity; FSUID={0}, DeviceName={1}", FSUID, TDevice.DeviceName);
                DeleteDeviceConfig(FSUID, TDevice.DeviceID, StationId, Equipment.EquipmentId, EquipmentTemplate.EquipmentTemplateId);
                ErrorMsg = string.Format("设备模板配置存库失败.待导入设备名:{0}", TDevice.DeviceName);
                return false;
            }
            //-------------------------------------------------------------------------------
            CfgEquipmentOP.LogConfigChange(EditType.Add, Equipment.StationId, Equipment.EquipmentId);
            //-------------------------------------------------------------------------------
            return true;
        }
        #endregion

        #region private



        private bool SaveConfigOfDeviceLevel() {
            try {
                //保存之前，容错
                if (EquipmentCMCCBiz.Exist(FSUID, TDevice.DeviceID)) {
                    if (CommonUtils.IsNoProcedure)
                    {
                        Public_ExecuteSqlService.Instance.NoStore_TBLEquipmentCMCC_DeleteByFSUIDAndDeviceID(FSUID, TDevice.DeviceID);
                    }
                    else {
                        var sqlDel = string.Format("DELETE FROM TBL_EquipmentCMCC WHERE FSUID={0} AND DeviceID={1}", SHelper.GetPara(FSUID), SHelper.GetPara(TDevice.DeviceID));
                        DBHelper.ExecuteNonQuery(sqlDel);
                    }
                    logger.WarnFormat("SaveConfigOfDeviceLevel();容错删除B接口设备级配置; deviceName={0}",TDevice.DeviceName);
                }
                

                var sql = string.Format("BM_DeviceConfigAdd	{0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13},{14},{15},{16},{17},{18}",
                        SHelper.GetPara(FSUID), SHelper.GetPara(TDevice.DeviceID), SHelper.GetPara(TDevice.DeviceName), SHelper.GetPara(TDevice.RoomName), SHelper.GetPara(TDevice.DeviceType),
                        SHelper.GetPara(TDevice.DeviceSubType), SHelper.GetPara(TDevice.Model), SHelper.GetPara(TDevice.Brand), SHelper.GetPara(TDevice.RatedCapacity), SHelper.GetPara(TDevice.Version),
                        SHelper.GetPara(TDevice.BeginRunTime), SHelper.GetPara(TDevice.DevDescribe), StationId, HouseId, MonitorUnitId,
                        SamplerUnitId, Equipment.EquipmentId, EquipmentTemplate.EquipmentTemplateId, EquipmentCategory);
                //DBHelper.ExecuteNonQuery(sql);
                try
                {
                    if (CommonUtils.IsNoProcedure)
                    {
                        Public_StoredService.Instance.BM_DeviceConfigAdd(SHelper.GetPara(FSUID), SHelper.GetPara(TDevice.DeviceID), SHelper.GetPara(TDevice.DeviceName), SHelper.GetPara(TDevice.RoomName), SHelper.GetPara(TDevice.DeviceType),
                        SHelper.GetPara(TDevice.DeviceSubType), SHelper.GetPara(TDevice.Model), SHelper.GetPara(TDevice.Brand), SHelper.GetPara(TDevice.RatedCapacity), SHelper.GetPara(TDevice.Version),
                        SHelper.GetPara(TDevice.BeginRunTime), SHelper.GetPara(TDevice.DevDescribe), StationId, HouseId, MonitorUnitId,
                        SamplerUnitId, Equipment.EquipmentId, EquipmentTemplate.EquipmentTemplateId, EquipmentCategory);
                    }
                    else
                    {
                        using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                        {
                            dbHelper.CreateProcedureCommand("BM_DeviceConfigAdd");
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("FSUID", DbType.AnsiString, SHelper.GetProcPara(FSUID)));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("DeviceID", DbType.AnsiString, SHelper.GetProcPara(TDevice.DeviceID)));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("DeviceName", DbType.AnsiString, SHelper.GetProcPara(TDevice.DeviceName)));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("RoomName", DbType.AnsiString, SHelper.GetProcPara(TDevice.RoomName)));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("DeviceType", DbType.Int32, SHelper.GetProcPara(TDevice.DeviceType)));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("DeviceSubType", DbType.Int32, SHelper.GetProcPara(TDevice.DeviceSubType)));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("Model", DbType.AnsiString, SHelper.GetProcPara(TDevice.Model)));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("Brand", DbType.AnsiString, SHelper.GetProcPara(TDevice.Brand)));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("RatedCapacity", DbType.Single, SHelper.GetProcPara(TDevice.RatedCapacity)));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("Version", DbType.AnsiString, SHelper.GetProcPara(TDevice.Version)));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("BeginRunTime", DbType.DateTime, SHelper.GetProcPara(TDevice.BeginRunTime)));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("DevDescribe", DbType.AnsiString, SHelper.GetProcPara(TDevice.DevDescribe)));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("StationId", DbType.Int32, StationId));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("HouseId", DbType.Int32, HouseId));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("MonitorUnitId", DbType.Int32, MonitorUnitId));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("SamplerUnitId", DbType.Int32, SamplerUnitId));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("EquipmentId", DbType.Int32, Equipment.EquipmentId));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("EquipmentTemplateId", DbType.Int32, EquipmentTemplate.EquipmentTemplateId));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("EquipmentCategory", DbType.Int32, EquipmentCategory));
                            dbHelper.AddDbParameter(dbHelper.GetReturnDbParameter());
                            dbHelper.ExecuteNoQuery();
                        }
                    }
                }
                catch (Exception ex)
                {
                    DBHelper.LogError(sql, ex);
                }
            }
            catch (Exception ex) {
                logger.ErrorFormat("SaveConfigOfDeviceLevel.SaveEntity();(deviceName={0}, EquipmentTemplateId={1},EquipmentId={2};Error={3}",
                   TDevice.DeviceName, EquipmentTemplate.EquipmentTemplateId, Equipment.EquipmentId, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }

        private bool UpdateConfigOfDeviceLevel() {
            try {
                logger.InfoFormat("UpdateConfigOfDeviceLevel();dev={0}; EquipId={1},EquipTempId={2}", TDevice.DeviceName, Equipment.EquipmentId, EquipmentTemplate.EquipmentTemplateId);

                var sql = string.Format("BM_DeviceConfigUpdate	{0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13},{14},{15},{16},{17},{18}",
                        SHelper.GetPara(FSUID), SHelper.GetPara(TDevice.DeviceID), SHelper.GetPara(TDevice.DeviceName), SHelper.GetPara(TDevice.RoomName), SHelper.GetPara(TDevice.DeviceType),
                        SHelper.GetPara(TDevice.DeviceSubType), SHelper.GetPara(TDevice.Model), SHelper.GetPara(TDevice.Brand), SHelper.GetPara(TDevice.RatedCapacity), SHelper.GetPara(TDevice.Version),
                        SHelper.GetPara(TDevice.BeginRunTime), SHelper.GetPara(TDevice.DevDescribe), StationId, HouseId, MonitorUnitId,
                        SamplerUnitId, Equipment.EquipmentId, EquipmentTemplate.EquipmentTemplateId, EquipmentTemplate.EquipmentTemplateId);
                //DBHelper.ExecuteNonQuery(sql);

                try
                {
                    using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                    {
                        if(CommonUtils.IsNoProcedure)
                        {
                          var result = BM_DeviceConfigService.Instance.BM_DeviceConfigUpdate(SHelper.GetProcPara(FSUID), SHelper.GetProcPara(TDevice.DeviceID), SHelper.GetProcPara(TDevice.DeviceName), SHelper.GetProcPara(TDevice.RoomName),
                              SHelper.GetProcPara(TDevice.DeviceType), SHelper.GetProcPara(TDevice.DeviceSubType), SHelper.GetProcPara(TDevice.Model), SHelper.GetProcPara(TDevice.Brand),
                              SHelper.GetProcPara(TDevice.RatedCapacity), SHelper.GetProcPara(TDevice.Version), SHelper.GetProcPara(TDevice.BeginRunTime).ToString(), SHelper.GetProcPara(TDevice.DevDescribe),
                              StationId, HouseId, MonitorUnitId, SamplerUnitId, Equipment.EquipmentId, EquipmentTemplate.EquipmentTemplateId, EquipmentCategory);
                          return  result == -1 ? false : true;
                        }
                        dbHelper.CreateProcedureCommand("BM_DeviceConfigUpdate");
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("FSUID", DbType.AnsiString, SHelper.GetProcPara(FSUID)));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("DeviceID", DbType.AnsiString, SHelper.GetProcPara(TDevice.DeviceID)));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("DeviceName", DbType.AnsiString, SHelper.GetProcPara(TDevice.DeviceName)));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("RoomName", DbType.AnsiString, SHelper.GetProcPara(TDevice.RoomName)));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("DeviceType", DbType.Int32, SHelper.GetProcPara(TDevice.DeviceType)));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("DeviceSubType", DbType.Int32, SHelper.GetProcPara(TDevice.DeviceSubType)));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("Model", DbType.AnsiString, SHelper.GetProcPara(TDevice.Model)));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("Brand", DbType.AnsiString, SHelper.GetProcPara(TDevice.Brand)));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("RatedCapacity", DbType.Single, SHelper.GetProcPara(TDevice.RatedCapacity)));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("Version", DbType.AnsiString, SHelper.GetProcPara(TDevice.Version)));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("BeginRunTime", DbType.DateTime, SHelper.GetProcPara(TDevice.BeginRunTime)));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("DevDescribe", DbType.AnsiString, SHelper.GetProcPara(TDevice.DevDescribe)));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("StationId", DbType.Int32, StationId));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("HouseId", DbType.Int32, HouseId));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("MonitorUnitId", DbType.Int32, MonitorUnitId));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("SamplerUnitId", DbType.Int32, SamplerUnitId));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("EquipmentId", DbType.Int32, Equipment.EquipmentId));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("EquipmentTemplateId", DbType.Int32, EquipmentTemplate.EquipmentTemplateId));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("EquipmentCategory", DbType.Int32, EquipmentCategory));
                        dbHelper.AddDbParameter(dbHelper.GetReturnDbParameter());
                        dbHelper.ExecuteNoQuery();
                    }
                }
                catch (Exception ex)
                {
                    DBHelper.LogError(sql, ex);
                }
            }
            catch (Exception ex) {
                logger.ErrorFormat("UpdateConfigOfDeviceLevel.SaveEntity();(deviceName={0}, EquipmentTemplateId={1},EquipmentId={2};Error={3}",
                   TDevice.DeviceName, EquipmentTemplate.EquipmentTemplateId, Equipment.EquipmentId, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }



        private static void DeleteDeviceConfigOnlyTSignal(TBL_EquipmentCMCCEx devSimple) {
            logger.InfoFormat("DelOneDeviceTSignalDataOnly();删除已有信号级配置; DeviceName={0},EquipmentId={1},EquipmentTemplateId={2}",
            devSimple.DeviceName, devSimple.EquipmentId, devSimple.EquipmentTemplateId);

            var sql = string.Format("BM_DeviceConfigDelOnlyTSig {0},{1},{2},{3}"
                , SHelper.GetPara(devSimple.FSUID), SHelper.GetPara(devSimple.DeviceID), SHelper.GetPara(devSimple.StationId), SHelper.GetPara(devSimple.EquipmentId));
            //DBHelper.ExecuteNonQuery(sql);
            try
            {
                using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                {
                    dbHelper.CreateProcedureCommand("BM_DeviceConfigDelOnlyTSig");
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("FSUID", DbType.AnsiString, SHelper.GetProcPara(devSimple.FSUID)));
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("DeviceID", DbType.AnsiString, SHelper.GetProcPara(devSimple.DeviceID)));
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("StationId", DbType.Int32, SHelper.GetProcPara(devSimple.StationId)));
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("EquipmentId", DbType.Int32, SHelper.GetProcPara(devSimple.EquipmentId)));
                    dbHelper.AddDbParameter(dbHelper.GetReturnDbParameter());
                    dbHelper.ExecuteNoQuery();
                }
            }
            catch (Exception ex)
            {
                DBHelper.LogError(sql, ex);
            }
            CfgEquipmentOP.LogConfigChange(EditType.Delete, devSimple.StationId.Value, devSimple.EquipmentId.Value);
        }



        
        
        private bool CreateEquipIdAndEquipmentTemplateId(ref string errmsg) {


            int eqId = 0;
            if (!GlobalIdentityBiz.GetNewId("TBL_Equipment", ref eqId)) {
                errmsg = string.Format("FSU配置导入失败. 原因: 系统未能自动生成EquipmentId");
                logger.ErrorFormat("DevConfExBiz.CreateEquipIdAndEquipmentTemplateId(); failed to GetNewId for TBL_Equipment");
                return false;
            }
            Equipment.EquipmentId = eqId;



            int equipmentTemplateId = 0;
            if (!GlobalIdentityBiz.GetNewId("TBL_EquipmentTemplate", ref equipmentTemplateId)) {
                errmsg = string.Format("FSU配置导入失败. 原因: 系统未能自动生成EquipmentTemplateId");
                logger.ErrorFormat("DevConfExBiz.CreateEquipIdAndEquipmentTemplateId(); failed to GetNewId for TBL_EquipmentTemplate");
                return false;
            }
            EquipmentTemplate.EquipmentTemplateId = equipmentTemplateId;

            return true;
        }
        private bool SetManConfig(TSL_MonitorUnitCMCCEx fsuConfig, TDevConf dev, ref string errmsg) {
            TDevice = dev;

            //1-/////////////////////////////////////////////////////////////////////////////////////////////////
            FSUID = fsuConfig.FSUID;
            StationId = fsuConfig.StationId;
            MonitorUnitId = fsuConfig.MonitorUnitId;
            SamplerUnitId = fsuConfig.SamplerUnitId.Value;

            //------------------------------------------------------------------------------------------------------------------
            int houseId=0;
            if (!HouseBiz.GetHouseIdByRoomName(fsuConfig.StationId, TDevice.RoomName, ref  houseId)) {
                errmsg = string.Format("FSU配置导入失败. 原因: 设备[{0}]所属局房[{1}]未配置", TDevice.DeviceName, TDevice.RoomName);
                logger.InfoFormat("DevConfExBiz.SetManConfig();" + errmsg);
                return false;          
            }
            HouseId = houseId;

            //------------------------------------------------------------------------------------------------------------------
            var equipmentCategory = EquipmentCategoryConvert.TryParse(dev.DeviceType);
            if (equipmentCategory == null) {
                errmsg = string.Format("FSU配置导入失败. 原因: 转换设备类型失败; DeviceName={0},DeviceType={1}", dev.DeviceName, dev.DeviceType);
                logger.InfoFormat("DevConfExBiz.SetManConfig();" + errmsg);
                return false;
            }
            EquipmentCategory = equipmentCategory.Value;

            return true;
        }
        private bool SetSiteWebConfigData(ref string errmsg) {
            //设备转换==============================================================================================================================
            var et = EquipmentTemplateConvert.TryParse(this, ref errmsg);
            if (et==null) {
                logger.InfoFormat("DevConfExBiz.SetSiteWebConfigData(); failed to AddEquipmentTemplate; FSUID={0}, DeviceName={1}", FSUID, TDevice.DeviceName);               
                return false;
            }

            if (!EquipmentConvert.TryParse(this, ref Equipment)) {
                logger.InfoFormat("DevConfExBiz.SetSiteWebConfigData(); failed to AddEquipment; FSUID={0}, DeviceName={1}", FSUID, TDevice.DeviceName);
                errmsg = string.Format("FSU配置导入失败. 原因: 设备[{0}]导入设备信息错误", TDevice.DeviceName);
                return false;
            }


            //信号转换==============================================================================================================================
            if (!SiteWebSECConvert.ListOfTSignals2NewSiteWebSECs(ref errmsg, EquipmentTemplate.EquipmentTemplateId, ListOfTSignalEx,
               ref EquipmentTemplate.Signals, ref EquipmentTemplate.Events, ref EquipmentTemplate.Controls)) {
                   logger.InfoFormat("DevConfExBiz.SetSiteWebConfigData(); failed to AddEquipmentTemplate; FSUID={0}, DeviceName={1}", FSUID, TDevice.DeviceName);
                   if (string.IsNullOrEmpty(errmsg))
                        errmsg = string.Format("FSU配置导入失败. 原因: 设备[{0}]导入TSignal信息错误", TDevice.DeviceName);
                return false;
            }
            return true;
         }
        #endregion

    }
}
