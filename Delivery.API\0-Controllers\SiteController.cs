﻿using BLL;
using Delivery.API;
using DM.TestOrder.DAL;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;





namespace DM.TestOrder.Controllers {

    public class SiteController : BaseController {

        //http://localhost/orderapi/Site
        [HttpGet]
        public JsonResult Get() {

            var dt = Station.GetAllInfo();

            //var sReturn = JsonConvert.SerializeObject(dt);
            //HttpResponseMessage result = new HttpResponseMessage {
            //    Content = new StringContent(sReturn, Encoding.GetEncoding("UTF-8"), "application/json")
            //};
            //return result; 
            return new JsonResult(dt);
        }

        [HttpGet("{id}")]
        public JsonResult Get(int id) {

            var dt = Station.GetOne(id);
            return new JsonResult(dt);
            //var sReturn = JsonConvert.SerializeObject(dt);
            //HttpResponseMessage result = new HttpResponseMessage {
            //    Content = new StringContent(sReturn, Encoding.GetEncoding("UTF-8"), "application/json")
            //};
            //return result;
        }
    }
}
