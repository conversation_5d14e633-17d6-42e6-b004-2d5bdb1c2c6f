﻿using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace Delivery.Common.NoProcedure.Service
{
    public class WO_TestOrderDalService
    {
        private static WO_TestOrderDalService _instance = null;

        public static WO_TestOrderDalService Instance
        {
            get
            {
                if (_instance == null) _instance = new WO_TestOrderDalService();
                return _instance;
            }
        }
        public void TestOrderLock(int orderid, int userid, int islock)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("OrderId", orderid);
                    realParams.Add("UserId", userid);
                    realParams.Add("IsLock", islock);
                    if(islock == 0)
                    {
                        execHelper.ExecuteNonQuery("WO_TestOrder_NotLock", realParams);
                    }
                    else
                    {
                        string LockTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                        realParams.Add("LockTime", LockTime);
                        execHelper.ExecuteNonQuery("WO_TestOrder_Lock", realParams);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("WO_TestOrder_Lock:{0}", ex));
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public string TestOrderQueryLock(int orderId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    string result = "";
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("OrderId", orderId);
                    string lockUserName = "";
                    string lockTime = "";
                    DataTable dt = execHelper.ExecDataTable("WO_TestOrder_LockInfo", realParams);
                    if(dt != null && dt.Rows.Count > 0)
                    {
                        lockUserName = dt.Rows[0]["LockUserName"].ToString();
                        lockTime = dt.Rows[0]["LockTime"].ToString();
                    }
                    if(!string.IsNullOrEmpty(lockUserName) && !string.IsNullOrEmpty(lockTime))
                    {
                        DateTime t1 = Convert.ToDateTime(lockTime);
                        DateTime t2 = DateTime.Now;
                        TimeSpan span = new TimeSpan(t2.Ticks - t1.Ticks);
                        double diffTime = span.TotalSeconds;
                        if(diffTime < 120)
                        {
                            result = lockUserName + "已锁定此工单,请稍后重试.";
                            return result;
                        }
                    }
                    return "OK";
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("WO_TestOrder_QueryLock:{0}", ex));
                    return "";
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public string TestOrderValidateConfig(int orderId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("OrderId", orderId);
                    DataTable dt = execHelper.ExecDataTable("WO_TestOrder_ValidateConfig", realParams);
                    if (dt == null || dt.Rows.Count == 0)
                    {
                        return "工单已失效。原因：局站配置已删除";
                    }
                    DataTable dt1 = execHelper.ExecDataTable("WO_TestOrder_ValidateEquipment", realParams);
                    if(dt1 != null && dt1.Rows.Count > 0)
                    {
                        return "工单已失效。原因：设备配置已删除";
                    }
                    return "OK";
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("WO_TestOrder_ValidateConfig:{0}", ex));
                    return "";
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public DataTable WO_CreateTestOrder(int OrderType, int StationId, object Latitude, object Longitude, string EquipItems, string InstallCompany, string InstallClerk, int ApplyUserId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    int NeedUpload = 0;
                    DataTable dt = execHelper.ExecDataTable("WO_CreateTestOrder_getNeedUpload", realParams);
                    if(dt != null && dt.Rows.Count > 0)
                    {
                        NeedUpload = Convert.ToInt32(dt.Rows[0][0]);
                    }
                    realParams.Add("ApplyUserId", ApplyUserId);
                    string ApplyUserName = "";
                    DataTable dt1 = execHelper.ExecDataTable("WO_CreateTestOrder_getApplyUserName", realParams);
                    if(dt1 != null && dt1.Rows.Count > 0)
                    {
                        ApplyUserName = dt1.Rows[0][0].ToString();
                    }
                    string StateSetUserName = ApplyUserName;
                    realParams.Add("InstallCompany", InstallCompany);
                    object InstallCompanyId = DBNull.Value; object InstallClerkId = DBNull.Value;
                    DataTable dt2 = execHelper.ExecDataTable("WO_CreateTestOrder_getInstallCompanyId", realParams);
                    if(dt2 != null && dt2.Rows.Count > 0)
                    {
                        InstallCompanyId = CommonUtils.GetNullableValue(dt2.Rows[0].Field<int?>("InstallCompanyId"));
                    }
                    realParams.Add("InstallCompanyId", InstallCompanyId);
                    realParams.Add("InstallClerk", InstallClerk);
                    DataTable dt3 = execHelper.ExecDataTable("WO_CreateTestOrder_getInstallClerkId", realParams);
                    if(dt3 != null && dt3.Rows.Count > 0)
                    {
                        InstallClerkId = CommonUtils.GetNullableValue(dt3.Rows[0].Field<int?>("InstallClerkId"));
                    }
                    string ApplyUserFsuVendor = "";
                    DataTable dt4 = execHelper.ExecDataTable("WO_CreateTestOrder_getApplyUserFsuVendor", realParams);
                    if(dt4 != null && dt4.Rows.Count > 0)
                    {
                        ApplyUserFsuVendor = dt4.Rows[0][0].ToString();
                    }
                    realParams.Add("OrderType", OrderType);
                    realParams.Add("StationId", StationId);
                    realParams.Add("Latitude", Latitude);
                    realParams.Add("Longitude", Longitude);
                    realParams.Add("EquipItems", EquipItems);
                    realParams.Add("ApplyUserName", ApplyUserName);
                    realParams.Add("ApplyUserFsuVendor", ApplyUserFsuVendor);
                    DateTime time = DateTime.Now;
                    int OrderState = 1;
                    int StateSetUserId = ApplyUserId;
                    realParams.Add("ApplyTime", time);
                    realParams.Add("StateChangeTime", time);
                    realParams.Add("OrderState", OrderState);
                    realParams.Add("StateSetUserId", StateSetUserId);
                    realParams.Add("StateSetUserName", StateSetUserName);
                    realParams.Add("InstallClerkId", InstallClerkId);
                    realParams.Add("NeedUpload", NeedUpload);
                    execHelper.ExecuteNonQuery("WO_CreateTestOrder_insertTestOrder", realParams);
                    int OrderId = -1;
                    DataTable dt5 = execHelper.ExecDataTable("WO_CreateTestOrder_getOrderId", realParams);
                    if(dt5 != null && dt5.Rows.Count > 0)
                    {
                        OrderId = Convert.ToInt32(dt5.Rows[0][0]);
                    }
                    string MyOrderId = "";
                    if (OrderId.ToString().Length < 6)
                    {
                        int count = 6 - OrderId.ToString().Length;
                        string repeatedString = String.Concat(Enumerable.Repeat("0", count));
                        MyOrderId = time.ToString("yyyyMMdd") + "-" + repeatedString + OrderId.ToString();
                    }
                    else
                    {
                        MyOrderId = time.ToString("yyyyMMdd") + "-" + OrderId.ToString();
                    }
                    realParams.Add("OrderId", OrderId);
                    realParams.Add("MyOrderId", MyOrderId);
                    execHelper.ExecuteNonQuery("WO_CreateTestOrder_updateTestOrder", realParams);
                    execHelper.ExecuteNonQuery("WO_CreateTestOrder_insertTestOrderArtSelfCheckList", realParams);
                    execHelper.ExecuteNonQuery("WO_CreateTestOrder_insertTestOrderExpertCheckList", realParams);

                    string flowTime = time.ToString("yyyy-MM-dd HH:mm:ss");
                    string FlowText = flowTime + "_创建工单_提交人:" + StateSetUserName;
                    realParams.Add("FlowText", FlowText);
                    execHelper.ExecuteNonQuery("WO_CreateTestOrder_insertTestOrderFlow", realParams);
                    DataTable rt = execHelper.ExecDataTable("WO_CreateTestOrder_selectTestOrder", realParams);
                    return rt;
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("WO_CreateTestOrder:{0}", ex));
                    return new DataTable();
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public static string GetProcPara(string para)
        {
            if (string.IsNullOrEmpty(para))
                return "";
            else
            {
                if (para.IndexOf('\'') != -1)
                {
                    para = para.Replace("'", "");
                }
                return para;
            }
        }

        public void UpdateEquipmentAndHouseId(string fSUID)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    string FSUID = GetProcPara(fSUID);
                    realParams.Add("FSUID", FSUID);
                    DataTable dt = execHelper.ExecDataTable("BU_V3_UpdateEquipNameAndHouseIdCMCC_getEqs", realParams);
                    if (dt == null || dt.Rows.Count == 0)
                        return;
                    foreach(DataRow dr in dt.Rows)
                    {
                        int TmpStationId = Convert.ToInt32(dr["StationId"]);
                        int TmpEquipmentId = Convert.ToInt32(dr["EquipmentId"]);
                        string TmpDeviceName = dr["DeviceName"].ToString();
                        int TmpSWHouseId = Convert.ToInt32(dr["SWHouseId"]);

                        string msg = "modify eq config, eq =" + TmpEquipmentId.ToString();
                        realParams.Add("TmpStationId", TmpStationId);
                        realParams.Add("TmpDeviceName", TmpDeviceName);
                        realParams.Add("TmpSWHouseId", TmpSWHouseId);
                        realParams.Add("TmpEquipmentId", TmpEquipmentId);
                        execHelper.ExecuteNonQuery("BU_V3_UpdateEquipNameAndHouseIdCMCC_updateEquipment", realParams);
                        execHelper.ExecuteNonQuery("BU_V3_UpdateEquipNameAndHouseIdCMCC_updateEquipmentCMCC", realParams);
                        DataTable dt1 = execHelper.ExecDataTable("BU_V3_UpdateEquipNameAndHouseIdCMCC_isExistEquipItem", realParams);
                        if(dt1 == null || dt1.Rows.Count == 0)
                        {
                            DateTime MaitainSD = DateTime.Now;
                            DateTime MaintainED = MaitainSD.AddYears(1);
                            string OperationContent = String.Concat("Add Equipment Project Status Equipment id:", TmpStationId.ToString(), '.',
                            TmpEquipmentId.ToString(), " Date:", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                            CallableSPService.Instance.PblSaveEquipmentMaitain(TmpStationId, TmpEquipmentId, 3, MaitainSD, MaintainED, -1, "接入管理导入设备，未割接入网，打工程标识", null);
                            CallableSPService.Instance.PamSaveOperationRecord(-1, TmpStationId, 2, 73, OperationContent);
                        }
                        string objectId = TmpStationId.ToString() + "." + TmpEquipmentId.ToString();
                        PblConfigChangeLogService.Instance.DoExecute(objectId, 3, 2);
                    }
                    execHelper.ExecuteNonQuery("BU_V3_UpdateEquipNameAndHouseIdCMCC_updateEquipmentTemplate", realParams);

                    DataTable dt2 = execHelper.ExecDataTable("BU_V3_UpdateEquipNameAndHouseIdCMCC_gettsl_monitorunitcmcc", realParams);
                    foreach (DataRow dr in dt2.Rows)
                    {
                        int MonitorUnitId = Convert.ToInt32(dr["MonitorUnitId"]);
                        realParams.Add("MonitorUnitId", MonitorUnitId);
                    }

                    DataTable dt4 = execHelper.ExecDataTable("BU_V3_UpdateEquipNameAndHouseIdCMCC_getwr_syncinfo", realParams);
                    if (dt4 == null || dt4.Rows.Count == 0)
                    {
                        execHelper.ExecuteNonQuery("BU_V3_UpdateEquipNameAndHouseIdCMCC_insertwr_syncinfo", realParams);
                    }
                    else
                    {
                        execHelper.ExecuteNonQuery("BU_V3_UpdateEquipNameAndHouseIdCMCC_updatewr_syncinfo", realParams);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("BU_V3_UpdateEquipNameAndHouseIdCMCC:{0}", ex));
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
    }
}
