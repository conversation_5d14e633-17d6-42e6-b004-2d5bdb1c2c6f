﻿

using BLL;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using testorder.dal;
using DM.TestOrder.formData;
using DM.TestOrder.Model;

using DM.TestOrder.Entity;
using DM.TestOrder.Service.Convert.FormData;
using DM.TestOrder.Service.Convert;
using DM.TestOrder.DAL;

namespace DM.TestOrder.Service.Interface {
    public partial class TestOrderApi {

        #region pubic-method
        public string SubmitNewOrder(FormNewOrder orderInfo, out int newOrderId, out string newMyOrderId) {
            newOrderId = 0;
            newMyOrderId = "";

            WO_TestOrder MyOrderPara = FormDataMapper.ToEntity4CreateOrder(orderInfo);
            
            var errmsg = "OK";
            try {
                WoTestOrderDal.AddOne(MyOrderPara, out newOrderId, out newMyOrderId);

            }
            catch (Exception ex) {
                errmsg = ex.Message +"; "+ ex.InnerException;
                return errmsg;                
            }
            return errmsg;
        }

        public string SubmitModifyOrder(SectionOrder SectionOrder) {
            var errmsg = "OK";
            try {
                WO_TestOrder order = Mapper.ToEntity4ModifyOrder(SectionOrder);

                //validate
                if (WoGeoDal.Validate(order.Latitude, order.Longitude)!="OK") {
                    errmsg = "经纬度非法,数据验证失败";
                    return errmsg;
                }

                WoTestOrderDal.Update(order);
            }
                catch (Exception ex) {
                    errmsg = ex.Message;
                return errmsg;
            }
            return errmsg;
        }


        #endregion
 
    }
}
