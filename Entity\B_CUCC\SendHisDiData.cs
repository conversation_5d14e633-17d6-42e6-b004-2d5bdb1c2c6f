﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class SendHisDiData : BMessage
    {
        public List<Device> LDevice { get; set; }
        public SendHisDiData(string suids, string surids, List<Device> ldevice)
            : base()
        {
            MessageType = (int)BMessageType.SEND_HISDIDATA;
            SUId = suids;
            SURId = surids;
            LDevice = ldevice;
        }
        public SendHisDiData()
            : base()
        {
            MessageType = (int)BMessageType.SEND_HISDIDATA;
        }
        public static SendHisDiData Deserialize(XmlDocument xmlDoc)
        {
            SendHisDiData sendHisDiData = null;
            string errMsg = string.Empty;
            string suid = string.Empty;
            string surid = string.Empty;

            try
            {
                suid = xmlDoc.SelectSingleNode("/Request/Info/SUId").InnerText.Trim();
                surid = xmlDoc.SelectSingleNode("/Request/Info/SURId").InnerText.Trim();
                XmlNodeList nodes = xmlDoc.SelectNodes("/Request/Info/Values/DeviceList/Device");
                List<Device> ldevice = new List<Device>();
                foreach (XmlElement node in nodes)
                {
                    Device dev = new Device();
                    dev.Id = node.GetAttribute("Id");
                    dev.RId = node.GetAttribute("RId");
                    XmlNodeList ndlists = node.SelectNodes("Signal");
                    dev.SignalList = new List<Signal>();
                    foreach (XmlElement nd in ndlists)
                    {
                        Signal sdv = new Signal();
                        string strSignalId = nd.GetAttribute("Id").Trim();
                        if (string.IsNullOrEmpty(strSignalId))
                        {
                            errMsg = "SEND_HISDIDATA Signal ID is nullOrEmpty";
                            break;
                        }
                        else
                        {
                            sdv.Id = strSignalId;
                        }
                        string strTime = nd.GetAttribute("RecordTime").Trim();
                        if (string.IsNullOrEmpty(strTime))
                        {
                            errMsg = "SEND_HISDIDATA RecordTime is nullOrEmpty";
                            break;
                        }
                        else
                        {
                            sdv.RecordTime = strTime;
                        }
                        dev.SignalList.Add(sdv);
                    }
                    ldevice.Add(dev);
                }
                if(errMsg != string.Empty)
                {
                    entityLogger.ErrorFormat("SendHisDiData.Deserialize():{0}", errMsg);
                    sendHisDiData = GetErrorEntity(suid, surid, errMsg);
                }
                else
                {
                    sendHisDiData = new SendHisDiData(suid, surid, ldevice);
                }
                entityLogger.DebugFormat("SendHisDiData.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SendHisDiData.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SendHisDiData.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                sendHisDiData = GetErrorEntity(suid, surid, ex.Message);
            }
            sendHisDiData.StringXML = xmlDoc.InnerXml;
            return sendHisDiData;
        }

        /// <summary>
        /// 反序列化异常时，构造带错误信息的实体
        /// </summary>
        /// <param name="errorMsg">解析错误信息</param>
        /// <returns></returns>
        private static SendHisDiData GetErrorEntity(string suId, string surId, string errorMsg)
        {
            SendHisDiData errorEntity = new SendHisDiData();
            SendHisDiDataAck ackEntity = new SendHisDiDataAck(suId, surId, EnumResult.FAILURE);
            ackEntity.ErrorMsg = errorMsg;
            errorEntity.ErrorMsg = ackEntity.Serialize();
            return errorEntity;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            if (LDevice.Count > 0)
            {
                foreach (Device de in LDevice)
                {
                    sb.AppendFormat("{0},{1},{2},{3},{4},{5}\n", MessageId, (BMessageType)MessageType, MessageType, SUId, SURId, de.PartToString());
                }
            }
            else
            {
                sb.AppendFormat("{0},{1},{2},{3},{4},Device is null", MessageId, (BMessageType)MessageType, MessageType, SUId, SURId);
            }
            return sb.ToString();
        }
    }
}
