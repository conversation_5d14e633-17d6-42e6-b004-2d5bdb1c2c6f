﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.NBIOT
{
    public sealed class Device
    {
        [JsonProperty("DevID")]
        public string DeviceId { get; set; }

        //获取实时数据
        [JsonIgnore]
        public List<TSemaphore> Semaphores { get; set; }

        #region SiteWeb配置

        [JsonIgnore]
        public int MyDeviceId { get; set; }

        #endregion

        //[JsonIgnore]//报文请求类型
        //public string PK_Type = string.Empty;

        //private dynamic _data;
        //public dynamic Data
        //{
        //    get { return _data; }
        //    set
        //    {
        //        if (_data == null)
        //        {
        //            _data = GetSemaphoreByPkType(value.ToString());
        //        }
        //        else
        //        {
        //            _data = value;
        //        }
        //    }
        //} 

        /// <summary>
        /// 根据请求类型处理相应的实体
        /// </summary>
        /// <param name="pkType"></param>
        /// <param name="valueStr"></param>
        /// <returns></returns>
        private object GetSemaphoreByPkType(string valueStr)
        {
            object item = null;
            //if (string.Compare(pkType, "SEND_CHANGED_DATA", true) == 0 || string.Compare(pkType, "SEND_DATA", true) == 0 || string.Compare(pkType, "GET_DATA_ACK", true) == 0 || string.Compare(pkType, "GET_ASYN_DATA_PUSH", true) == 0)
            //{
            List<TGetSemaphore> getSemaphoreList = JsonConvert.DeserializeObject<List<TGetSemaphore>>(valueStr);
            item = getSemaphoreList;
            GetSemaphore(getSemaphoreList);
            //}
            return item;
        }

        //把请求监控点获得的数据赋值给需要用的数据结构
        public void GetSemaphore(List<TGetSemaphore> getSemaphoreList)
        {
            if (getSemaphoreList != null && getSemaphoreList.Count > 0)
            {
                Semaphores = Semaphores ?? new List<TSemaphore>();
                foreach (TGetSemaphore data in getSemaphoreList)
                {
                    TSemaphore semaphore = new TSemaphore();

                    string[] sId = data.SID.Split(new char[] { ':' });
                    semaphore.ID =sId[0];
                    semaphore.SignalNumber = sId[1];
                    semaphore.MeasuredVal = data.Value;
                    semaphore.Status = data.Status;
                    semaphore.Time = data.Time;
                    Semaphores.Add(semaphore);
                }
            }
        }

        public Device() 
        {
        }

        /// <summary>
        /// 监控点数据请求和应答报文中的设备数据结构
        /// </summary>
        /// <param name="deviceId"></param>
        /// <param name="semaphores"></param>
        public Device(string deviceId, List<TSemaphore> semaphores):base()
        {
            DeviceId = deviceId;
            Semaphores = semaphores;

            //_data = SetSemaphore(Semaphores);
        }

        /// <summary>
        /// 写监控点设置值时，将定义的类变量值赋给按JS定义的类变量
        /// </summary>
        /// <param name="semaphores"></param>
        private List<TSetSemaphore> SetSemaphore(List<TSemaphore> semaphores)
        {
            List<TSetSemaphore> setSemaphores = new List<TSetSemaphore>();
            try
            {
                if (semaphores != null && semaphores.Count > 0)
                {
                    foreach (TSemaphore item in semaphores)
                    {
                        TSetSemaphore setSemaphore = new TSetSemaphore();

                        string sId = item.ID;
                        string signalNumber = item.SignalNumber;
                        setSemaphore.SID = string.Format("{0}:{1}",sId,signalNumber);
                        setSemaphore.SetupVal = item.SetupVal;

                        setSemaphores.Add(setSemaphore);
                    }
                }
            }
            catch (Exception)
            { 
            }
            return setSemaphores;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append(DeviceId).Append(":\r\n");
            if (Semaphores != null && Semaphores.Count > 0)
            {
                foreach (TSemaphore data in Semaphores)
                {
                    sb.Append(data.ToString()).Append("\r\n");
                }
            }
            return sb.ToString();
        }
    }
}
