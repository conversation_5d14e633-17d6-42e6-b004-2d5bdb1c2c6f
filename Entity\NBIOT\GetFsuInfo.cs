﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 获取FSU状态信息
    /// </summary>
    public class GetFsuInfo : BMessage
    {
        public GetFsuInfo() : base()
        {
            MessageType = (int)BMessageType.GET_FSUINFO;
        }

        public GetFsuInfo(string fsuId) : base()
        {
            MessageType = (int)BMessageType.GET_FSUINFO;
            FSUID = fsuId;
        }

        public override string Serialize()
        {
            string result = string.Empty;
            try
            {
                JsonSerializerSettings settings = new JsonSerializerSettings();
                settings.ContractResolver = new CustomContractResolver();
                TIotEntity iotEntity = new TIotEntity(this);
                result = JsonConvert.SerializeObject(iotEntity, settings);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetFsuInfo.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetFsuInfo.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
            }
            return result;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0},{1}:{2}", MessageId, (BMessageType)MessageType, FSUID);
            return sb.ToString();
        }
    }
}