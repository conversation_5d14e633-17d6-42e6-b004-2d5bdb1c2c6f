﻿
using System;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Delivery.Common;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Net.Http.Headers;
using SampleApp.Utilities;

namespace Delivery.Controllers.Cutover 
    {
    public class EquipFileUploadController : BaseController
    {
        private const int MAX_FILE_SIZE = 5 * 1024 * 1024;

        [HttpPost]
        //public async Task<IActionResult> Post() {

        //    if (!MultipartRequestHelper.IsMultipartContentType(Request.ContentType))
        //    {
        //        ModelState.AddModelError("File",
        //            $"The request couldn't be processed (Error 1).");
        //        // Log error

        //        return BadRequest(ModelState);
        //    }

        //    // var formAccumulator = new KeyValueAccumulator();
        //    var trustedFileNameForDisplay = string.Empty;
        //    // var untrustedFileNameForStorage = string.Empty;
        //    var streamedFileContent = new byte[0];

        //    var boundary = MultipartRequestHelper.GetBoundary(
        //            MediaTypeHeaderValue.Parse(Request.ContentType),
        //            MAX_FILE_SIZE);
        //    var reader = new MultipartReader(boundary, HttpContext.Request.Body);
        //    var section = await reader.ReadNextSectionAsync();

        //    while (section != null)
        //    {
        //        var hasContentDispositionHeader =
        //            ContentDispositionHeaderValue.TryParse(
        //                section.ContentDisposition, out var contentDisposition);

        //        if (hasContentDispositionHeader)
        //        {
        //            if (MultipartRequestHelper
        //                .HasFileContentDisposition(contentDisposition))
        //            {
        //                //untrustedFileNameForStorage = contentDisposition.FileName.Value;
        //                // Don't trust the file name sent by the client. To display
        //                // the file name, HTML-encode the value.
        //                trustedFileNameForDisplay = WebUtility.HtmlEncode(
        //                        contentDisposition.FileName.Value);

        //                streamedFileContent =
        //                    await FileHelpers.ProcessStreamedFile(section, contentDisposition,
        //                        ModelState, new string[] { ".zip"}, MAX_FILE_SIZE);

        //                if (!ModelState.IsValid)
        //                {
        //                    return BadRequest(ModelState);
        //                }
        //            }
        //            //else if (MultipartRequestHelper
        //            //    .HasFormDataContentDisposition(contentDisposition))
        //            //{
        //            //    // Don't limit the key name length because the 
        //            //    // multipart headers length limit is already in effect.
        //            //    var key = HeaderUtilities
        //            //        .RemoveQuotes(contentDisposition.Name).Value;
        //            //    var encoding = GetEncoding(section);

        //            //    if (encoding == null)
        //            //    {
        //            //        ModelState.AddModelError("File",
        //            //            $"The request couldn't be processed (Error 2).");
        //            //        // Log error

        //            //        return BadRequest(ModelState);
        //            //    }

        //            //    using (var streamReader = new StreamReader(
        //            //        section.Body,
        //            //        encoding,
        //            //        detectEncodingFromByteOrderMarks: true,
        //            //        bufferSize: 1024,
        //            //        leaveOpen: true))
        //            //    {
        //            //        // The value length limit is enforced by 
        //            //        // MultipartBodyLengthLimit
        //            //        var value = await streamReader.ReadToEndAsync();

        //            //        if (string.Equals(value, "undefined",
        //            //            StringComparison.OrdinalIgnoreCase))
        //            //        {
        //            //            value = string.Empty;
        //            //        }

        //            //        formAccumulator.Append(key, value);

        //            //        if (formAccumulator.ValueCount >
        //            //            _defaultFormOptions.ValueCountLimit)
        //            //        {
        //            //            // Form key count limit of 
        //            //            // _defaultFormOptions.ValueCountLimit 
        //            //            // is exceeded.
        //            //            ModelState.AddModelError("File",
        //            //                $"The request couldn't be processed (Error 3).");
        //            //            // Log error

        //            //            return BadRequest(ModelState);
        //            //        }
        //            //    }
        //            }
        //        }

        //        // Drain any remaining section body that hasn't been consumed and
        //        // read the headers for the next section.
        //        section = await reader.ReadNextSectionAsync();
        //    }


            //if (!Request.Content.IsMimeMultipartContent()) {
            //    throw new HttpResponseException(HttpStatusCode.UnsupportedMediaType);
            //}

            ////文件最大尺寸已在web.config中配置设置为50M
            //var fSize = HttpContext.Current.Request.ContentLength; 
            //if (fSize > MAX_FILE_SIZE+1024) {
            //    logger.WarnFormat("EquipFileUploadController.Post();Request bigger than 5M; size ={0:N1}M", fSize * 1.0 / (1024 * 1024));
            //    var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(new {
            //        errormsg = "上传文件最大不得超过5M"
            //    });

            //    return new HttpResponseMessage() {
            //        Content = new StringContent(jsonRtn, Encoding.UTF8, "text/html"),
            //    };
            //}

            //var orderId = int.Parse(HttpContext.Current.Request.Form["orderid"]);
            //var equipmentId = int.Parse(HttpContext.Current.Request.Form["equipmentId"]);
            //var filetype = HttpContext.Current.Request.Form["filetype"];//PIC or DOC          
            //var userid = HttpContext.Current.Request.Form["userid"]; //父目录


            ////目录结构设计: [userid]/[orderid]/
            ////文件名设计  :  类型+设备ID,示例: PIC-755000015.ZIP	
            //var myPath = "upload/" + userid.ToString() + "/" + orderId;

            //WO_FileUploadRec woFileUploadRec=null;

            //string uploadFolderPath = HostingEnvironment.MapPath("~" + "/" + myPath);

            ////如果路径不存在，创建路径  
            //if (!Directory.Exists(uploadFolderPath))
            //    Directory.CreateDirectory(uploadFolderPath);

            //var toSaveFileName = string.Format("{0}-{1}", filetype, equipmentId);
            //var provider = new WithExtensionMultipartFormDataStreamProvider(uploadFolderPath, toSaveFileName);

            //// Read the form data.  
            //await Request.Content.ReadAsMultipartAsync(provider);

            //var file = provider.FileData.FirstOrDefault();
            //if(file==null ){
            //    var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(new {
            //        errormsg = "upload-fail"
            //    });

            //    return new HttpResponseMessage() {
            //        Content = new StringContent(jsonRtn, Encoding.UTF8, "text/html"),
            //    };
            //}

            ////接收文件  
            //var fileSaveName=Path.GetFileName(file.LocalFileName);
            //woFileUploadRec = new WO_FileUploadRec() {
            //    OrderId = orderId,
            //    EquipmentId =equipmentId,
            //    FType = filetype,
            //    FSaveName = fileSaveName.ToLower(),
            //    Uri = myPath + "/" + fileSaveName.ToLower(),
            //    FOriginalName = provider.FOriginalName,
            //    FSize = fSize,
            //    UploadTime=DateTime.Now,
            //    UserId = int.Parse(userid)
            //};
            //WoFileUploadRecDal.AddOrUpdateOne(woFileUploadRec);


            //var jsonRtnOK = Newtonsoft.Json.JsonConvert.SerializeObject( new {
            //    uri = woFileUploadRec.Uri,      
            //    errormsg = "OK"
            //});

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtnOK, Encoding.UTF8, "text/html"),
            //};

        }

        //public static string GetCommonString( string input) {
        //    var regex = new Regex(@"([^\u4e00-\u9fa5a-zA-z0-9\s].*?)");
        //    var inputReplaced = regex.Replace(input, "_");
        //    return inputReplaced;
        //}

        //[AcceptVerbs("POST")]
        //[ActionName("postNewFile")]
        //public string PostFilePic(HttpPostedFileBase fileData)
        //{
        //    //HttpPostedFile file = HttpContext.Current.Request.Files[0];
        //    //string strPath = "D:\\MyProjects\\StudySolution\\RestDemo\\Upload\\test2.rar";
        //    //file.SaveAs(strPath);
        //    string result = "0";
        //    return result;

        //}

        //[AcceptVerbs("Get")]
        //[ActionName("getNewFile")]
        //public string GetFilePic(HttpPostedFileBase fileData) {
        //    HttpPostedFile file = HttpContext.Current.Request.Files[0];
        //    string strPath = "D:\\MyProjects\\StudySolution\\RestDemo\\Upload\\test2.rar";
        //    file.SaveAs(strPath);
        //    string result = "0";
        //    return result;

        //}
        private static Encoding GetEncoding(MultipartSection section)
        {
            var hasMediaTypeHeader =
                MediaTypeHeaderValue.TryParse(section.ContentType, out var mediaType);

            // UTF-7 is insecure and shouldn't be honored. UTF-8 succeeds in 
            // most cases.
            if (!hasMediaTypeHeader || Encoding.UTF7.Equals(mediaType.Encoding))
            {
                return Encoding.UTF8;
            }

            return mediaType.Encoding;
        }
    }
}
