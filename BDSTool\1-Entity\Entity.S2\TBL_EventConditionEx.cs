
namespace BDSTool.Entity.S2
{


    using BDSTool.DBUtility;
    using BDSTool.DBUtility.Common;
    using Delivery.Common.NoProcedure.Service;
    using Delivery.Common.NoProcedure.Utils;
    using System;
    using System.Collections.Generic;

    public partial class TBL_EventCondition : IBatchInsertRow
    {
        #region for batch insert
        private static string _InsertHeader = @"INSERT INTO TBL_EventCondition (EventConditionId, EquipmentTemplateId, EventId, StartOperation, StartCompareValue, StartDelay, EndOperation, EndCompareValue, EndDelay, Frequency, FrequencyThreshold, Meanings, EquipmentState, BaseTypeId, EventSeverity, StandardName)";

        public string GetInsertHeader() {
            if (CommonUtils.IsNoProcedure)
            {
                return Public_ExecuteSqlService.Instance.GetInsertHeader_TBL_EventCondition();
            }
            return _InsertHeader;
        }

        public string GetDataRowValueString() {
            return string.Format(@"({0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13},{14},{15})",
                EventConditionId, EquipmentTemplateId, EventId, SHelper.GetPara(StartOperation), SHelper.GetPara(StartCompareValue),
                StartDelay, SHelper.GetPara(EndOperation), SHelper.GetPara(EndCompareValue), SHelper.GetPara(EndDelay), SHelper.GetPara(Frequency),
                SHelper.GetPara(FrequencyThreshold), SHelper.GetPara(Meanings), SHelper.GetPara(EquipmentState), SHelper.GetPara(BaseTypeId), EventSeverity,
                SHelper.GetPara(StandardName));
        }

        #endregion
    }
}
