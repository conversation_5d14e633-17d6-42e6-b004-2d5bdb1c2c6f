﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    ///请求动环设备配置数据应答
    /// </summary>
    public sealed class GetDevConfAck: BMessage
    {

        public EnumResult Result { get; private set; }

        //public List<TDevConf> Values { get; private set; }
        public TDevConf Values { get; private set; }

        public string FailureCause { get; private set; }

        public XmlDocument ContentXML;

        #region SiteWeb配置

        public int MyStationId { get; set; }

        #endregion

        //public GetDevConfAck(string fsuId, EnumResult result, List<TDevConf> values, string failureCause)
        public GetDevConfAck(EnumResult result, TDevConf values, string failureCause)
            : base()
        {
            MessageType = (int)BMessageType.GET_DEV_CONF_ACK;

            //FSUID = fsuId;
            Result = result;
            Values = values;
            FailureCause = failureCause;
        }

        public GetDevConfAck() : base() 
        {
            MessageType = (int)BMessageType.GET_DEV_CONF_ACK;
        }

        public static GetDevConfAck Deserialize(XmlDocument xmlDoc)
        {
            GetDevConfAck getDevConfAck = null;

            string errorMsg = string.Empty;
            try
            {
                //string fsuId = xmlDoc.SelectSingleNode("/Response/Info/FSUID").InnerText;
                string strResult = xmlDoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim().ToUpper();
                EnumResult result = CheckEnumResult(strResult, ref errorMsg);
                string failureCause = xmlDoc.SelectSingleNode("/Response/Info/FailureCause").InnerText.Trim();
                //此处原来返回的是个列表，新文档中是单个设备
                //XmlNodeList nodelist = xmlDoc.SelectNodes("/Response/Info/Values/DeviceList/Device");
                //List<TDevConf> devList = new List<TDevConf>();
                XmlNodeList nodelist = xmlDoc.SelectNodes("/Response/Info/Values/Device");
                //foreach (XmlNode device in nodelist)
                TDevConf devConf = new TDevConf();
                if (nodelist.Count > 0)
                {
                    XmlNode device = nodelist[0];
                    //TDevConf devConf = new TDevConf();
                    devConf.DeviceID = device.Attributes["DeviceID"].Value.Trim();
                    devConf.DeviceName = device.Attributes["DeviceName"].Value.Trim();
                    devConf.SiteID = device.Attributes["SiteID"].Value.Trim();
                    devConf.RoomID = device.Attributes["RoomID"].Value.Trim();
                    devConf.SiteName = device.Attributes["SiteName"].Value.Trim();
                    devConf.RoomName = device.Attributes["RoomName"].Value.Trim();
                    string strDeviceType = device.Attributes["DeviceType"].Value.Trim();
                    if(string.IsNullOrEmpty(strDeviceType))
                    {
                        devConf.DeviceType = null;
                    }
                    else
                    {
                        devConf.DeviceType = int.Parse(strDeviceType);
                    }
                    string strDeviceSubType = device.Attributes["DeviceSubType"].Value.Trim();
                    if (string.IsNullOrEmpty(strDeviceSubType))
                    {
                        devConf.DeviceSubType = null;
                    }
                    else
                    {
                        devConf.DeviceSubType = int.Parse(strDeviceSubType);
                    }
                    devConf.Model = device.Attributes["Model"].Value.Trim();
                    devConf.Brand = device.Attributes["Brand"].Value.Trim();
                    string strRatedCapacity = device.Attributes["RatedCapacity"].Value.Trim();
                    if (string.IsNullOrEmpty(strRatedCapacity))
                    {
                        devConf.RatedCapacity = null;
                    }
                    else
                    {
                        devConf.RatedCapacity = float.Parse(strRatedCapacity);
                    }
                    devConf.Version = device.Attributes["Version"].Value.Trim();
                    string strBeginTime = device.Attributes["BeginRunTime"].Value.Trim();
                    if (string.IsNullOrEmpty(strBeginTime))
                    {
                        devConf.BeginRunTime = null;
                    }
                    else
                    {
                        devConf.BeginRunTime = Convert.ToDateTime(strBeginTime);
                    }
                    devConf.DevDescribe = device.Attributes["DevDescribe"].Value.Trim();
                    devConf.ConfRemark = device.Attributes["ConfRemark"].Value.Trim();
                    if (device.ChildNodes.Count > 0)
                    {
                        List<TSignal> signalList = new List<TSignal>();
                        foreach (XmlNode signal in device.ChildNodes[0].ChildNodes)
                        {
                            TSignal tSignal = new TSignal();
                            tSignal.Type = (EnumType)int.Parse(signal.Attributes["Type"].Value.Trim());
                            tSignal.ID = signal.Attributes["ID"].Value.Trim();
                            if (!CheckNumberValue(tSignal.ID))
                            {
                                errorMsg = "TSignal.ID is invalid";
                                break;
                            }
                            tSignal.SignalName = signal.Attributes["SignalName"].Value.Trim();
                            tSignal.AlarmLevel = (EnumState)int.Parse(signal.Attributes["AlarmLevel"].Value.Trim());
                            ////获得的配置中的float类型值可以为空值
                            string strThreshold = signal.Attributes["Threshold"].Value.Trim();
                            if (string.IsNullOrEmpty(strThreshold) || strThreshold.ToUpper() == "NULL")
                            {
                                tSignal.Threshold = null;
                            }
                            else
                            {
                                tSignal.Threshold = float.Parse(strThreshold);
                            }
                            //string strAbsoluteVal = signal.Attributes["AbsoluteVal"].Value.Trim();
                            //if (string.IsNullOrEmpty(strAbsoluteVal))
                            //{
                            //    tSignal.AbsoluteVal = null;
                            //}
                            //else
                            //{
                            //    tSignal.AbsoluteVal = float.Parse(strAbsoluteVal);
                            //}
                            //string strRelativeVal = signal.Attributes["RelativeVal"].Value.Trim();
                            //if (string.IsNullOrEmpty(strRelativeVal))
                            //{
                            //    tSignal.RelativeVal = null;
                            //}
                            //else
                            //{
                            //    tSignal.RelativeVal = float.Parse(strRelativeVal);
                            //}
                            
                            if (signal.Attributes["Describe"] == null)
                            {
                                if (tSignal.Type == EnumType.DO)//遥控
                                {
                                    tSignal.Describe = "1&待命";
                                }
                                else if (tSignal.Type == EnumType.DI)//遥信
                                {
                                    tSignal.Describe = "0&0;1&1";
                                }
                                else
                                {
                                    tSignal.Describe = "";
                                }
                            }
                            else
                            {
                                tSignal.Describe = signal.Attributes["Describe"].Value.Trim();
                            }

                            tSignal.NMAlarmID = signal.Attributes["NMAlarmID"].Value.Trim();
                            tSignal.SignalNumber = signal.Attributes["SignalNumber"].Value.Trim();
                            if (!string.IsNullOrEmpty(tSignal.SignalNumber) && !CheckNumberValue(tSignal.SignalNumber))
                            {
                                errorMsg = "TSignal.SignalNumber is invalid";
                                break;
                            }
                            //告警等级转换
                            if (tSignal.AlarmLevel == EnumState.CRITICAL)
                            {
                                tSignal.MyAlarmLevel = EnumLevel.CRITICAL;
                            }
                            else if (tSignal.AlarmLevel == EnumState.MAJOR)
                            {
                                tSignal.MyAlarmLevel = EnumLevel.MAJOR;
                            }
                            else if (tSignal.AlarmLevel == EnumState.MINOR)
                            {
                                tSignal.MyAlarmLevel = EnumLevel.MINOR;
                            }
                            else if (tSignal.AlarmLevel == EnumState.HINT)
                            {
                                tSignal.MyAlarmLevel = EnumLevel.HINT;
                            }

                            signalList.Add(tSignal);
                        }
                        devConf.Signals = signalList;
                    }
                    //devList.Add(devConf);
                }
                if(errorMsg == string.Empty)
                {
                    getDevConfAck = new GetDevConfAck(result, devConf, failureCause);
                    getDevConfAck.StringXML = xmlDoc.InnerXml;
                    getDevConfAck.ContentXML = xmlDoc;
                }
                else
                {
                    entityLogger.ErrorFormat("GetDevConfAck.Deserialize():{0}", errorMsg);
                    getDevConfAck = new GetDevConfAck();
                    getDevConfAck.ErrorMsg = errorMsg;
                    getDevConfAck.StringXML = xmlDoc.InnerXml;
                    getDevConfAck.ContentXML = xmlDoc;
                }
                entityLogger.DebugFormat("GetDevConfAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                return getDevConfAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetDevConfAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetDevConfAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("GetDevConfAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                getDevConfAck = new GetDevConfAck();
                getDevConfAck.ErrorMsg = ex.Message;
                getDevConfAck.StringXML = xmlDoc.InnerXml;
                getDevConfAck.ContentXML = xmlDoc;
                return getDevConfAck;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}:{2},{3}", MessageId, MessageType, Result, FailureCause);
            sb.AppendLine();
            if (Values != null)
            {
                //foreach (TDevConf device in Values)
                //{
                //    sb.AppendFormat("{0}\r\n", device.ToString());
                //}
                sb.AppendFormat("{0}\r\n", Values.ToString());
            }
            return sb.ToString();
        }


    }
}
