﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class TSemaphoreDel<T> where T : new()
    {
        public string DevID { get; set; }

        public List<T> Data { get; set; }

        #region SiteWeb配置
        [Newtonsoft.Json.JsonIgnore]
        public int MyDeviceId { get; set; }
        #endregion

        public TSemaphoreDel() { }

        public TSemaphoreDel(string devId, List<T> data)
        {
            DevID = devId;
            Data = data;
        }      

        public override string ToString()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
    }
}