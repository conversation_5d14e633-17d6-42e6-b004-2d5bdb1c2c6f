﻿



using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Text;
using testorder.dal;
using DM.TestOrder.DAL.Helper;

using DM.TestOrder.Model;
using Delivery.DAL;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;

namespace DM.TestOrder.DAL
{
    public partial class WoFlowDal
    {

        public static void RemoveOrder(int orderId)
        {
            //var sql = string.Format("WO_CR1_RemoveOrder {0}",orderId);
            //var rtn = DBHelper.ExecuteScalar(sql);
            if(CommonUtils.IsNoProcedure)
            {
                 var rtn1 = WoPartSPService.Instance.WoCR1RemoveOrder(orderId);
                if (rtn1 != "OK")
                {
                    Debug.WriteLine(rtn1);
                    throw new Exception(rtn1);
                }
                return;
            }
            var rtnVal = new ExecuteSql().ExecuteStoredProcedureScalar("WO_CR1_RemoveOrder", new QueryParameter[] {
                new QueryParameter("OrderId",DataType.Int,orderId.ToString())
            });
            var rtn = rtnVal == null ? null : rtnVal.ToString();
            if (rtn != "OK")
            {
                Debug.WriteLine(rtn);
                throw new Exception(rtn);
            }

        }
    }
}



