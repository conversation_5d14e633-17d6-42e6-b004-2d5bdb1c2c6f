﻿
using BDSTool.DBUtility;
using BDSTool.Entity.S2;
using Common.Logging.Pro;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.Entity.DRHelper
{
    public class TSL_PortHelper
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static TSL_Port FromDataRow(DataRow row) {

            var entity = new TSL_Port();
            entity.PortId = int.Parse(row["PortId"].ToString());
            entity.MonitorUnitId = int.Parse(row["MonitorUnitId"].ToString());
            entity.PortNo  = int.Parse(row["PortNo"].ToString());
            entity.PortName =row["PortName"].ToString();
            entity.PortType  = int.Parse(row["PortType"].ToString());
            entity.Setting  =row["Setting"].ToString();
            entity.PhoneNumber = row["PhoneNumber"].ToString();
            entity.LinkSamplerUnitId = SHelper.ToIntNullable(row["LinkSamplerUnitId"]);
            entity.Description =row["Description"].ToString();

            return entity;
        }
    }
}
