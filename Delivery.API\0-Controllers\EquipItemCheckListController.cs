﻿
using Delivery.API;
using DM.TestOrder.DAL;
using DM.TestOrder.Service.Interface;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;
using System.Net.Http;
using System.Text;





namespace DM.TestOrder.Controllers {

    public class EquipItemCheckListController : BaseController {

        [HttpGet]
        public JsonResult Get(int id) {

            WoTestOrderEquipItemCheckListDal.Refresh(id);

            var partTest = TestOrderApi.Instance.GetFormTest(id);
            return new JsonResult(partTest);


        }
    }
}
