﻿
using BDSTool.Entity.S2;

using Common.Logging.Pro;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.Entity.DRHelper
{
    public class TSL_SamplerUnitMiniHelper
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static TSL_SamplerUnitMini FromDataRow(DataRow row) {
            var entity = new TSL_SamplerUnitMini();
            entity.SamplerUnitId = int.Parse(row["SamplerUnitId"].ToString());
            entity.PortId = int.Parse(row["PortId"].ToString());
            entity.MonitorUnitId = int.Parse(row["MonitorUnitId"].ToString());
            entity.SamplerId = int.Parse(row["SamplerId"].ToString());
            entity.SamplerType = int.Parse(row["SamplerType"].ToString());

            entity.Description =row["SamplerUnitName"].ToString();
            entity.Description =row["DllPath"].ToString();
            entity.Description =row["Description"].ToString();
            return entity;
        }
    }
}
