﻿using DM.TestOrder.Entity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using DM.TestOrder.DAL.Helper;
using Delivery.DAL;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace DM.TestOrder.DAL {

    public partial class WoTestOrderDal {
        public static string ValidateConfig(int orderId) {

            //var sql = string.Format("WO_TestOrder_ValidateConfig {0}", orderId);
            //return DBHelper.ExecuteScalar(sql);
            if(CommonUtils.IsNoProcedure)
            {
                return WO_TestOrderDalService.Instance.TestOrderValidateConfig(orderId);
            }
            else
            {
                var rtn = new ExecuteSql().ExecuteStoredProcedureScalar("WO_TestOrder_ValidateConfig", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, orderId.ToString())
            });
                return rtn == null ? null : rtn.ToString();
            }
        }

        public static string QueryLock(int orderId) {

            //var sql = string.Format("WO_TestOrder_QueryLock {0} ", orderId);
            //return DBHelper.ExecuteScalar(sql);    
            if (CommonUtils.IsNoProcedure)
            {
                return WO_TestOrderDalService.Instance.TestOrderQueryLock(orderId);
            }
            else
            {
                var rtn = new ExecuteSql().ExecuteStoredProcedureScalar("WO_TestOrder_QueryLock", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, orderId.ToString())
                });
                return rtn == null ? null : rtn.ToString();
            }
            
        }

         public static WO_TestOrder GetOne(int orderId) {
            //cr1 修改，过滤掉已删除的工单        
            //var sql=string.Format("select * from WO_TestOrder where OrderId ={0}", orderId);
            //var sql = string.Format("select * from WO_TestOrder where OrderId ={0} and OrderState<=4", orderId);
            //var tb = DBHelper.GetTable(sql);
            DataTable tb = new DataTable();
            if (CommonUtils.IsNoProcedure)
            {
                tb = Public_ExecuteSqlService.Instance.NoStore_WOTestOrder_GetOneByOrderId(orderId);
            }
            else {
                var sql = "select * from WO_TestOrder where OrderId =@orderId and OrderState<=4";
                tb = new ExecuteSql().ExecuteSQL(sql, new QueryParameter[] {
                new QueryParameter("orderId", DataType.Int, orderId.ToString())
                });
            }
            
            if (tb.Rows.Count == 0)
                return null;
            var ord = WO_TestOrderHelper.FromDataRow(tb.Rows[0]);
            return ord;

        }
         public static bool IsExist(int orderId) {

            //var sql = string.Format("select 1 from WO_TestOrder where OrderId ={0}", orderId);
            //var rtn = DBHelper.ExecuteScalar(sql);
            object rtn = null;
            if (CommonUtils.IsNoProcedure)
            {
                rtn = Public_ExecuteSqlService.Instance.NoStore_WOTestOrder_IsExistByOrderId(orderId);
            }
            else
            {
                var sql = "select 1 from WO_TestOrder where OrderId =@orderId";
                rtn = new ExecuteSql().ExecuteSQLScalar(sql, new QueryParameter[] {
                new QueryParameter("orderId", DataType.Int, orderId.ToString())
                });
            }
            return rtn == null;

         }
         public static List<WO_TestOrderArtSelfCheckList> GetTestOrderArtSelfCheckList(int orderId) {
             var items = new List<WO_TestOrderArtSelfCheckList>();
            //var sql = string.Format("select * from WO_TestOrderArtSelfCheckList where OrderId ={0}", orderId);
            //var tb = DBHelper.GetTable(sql);
            DataTable tb = new DataTable();
            if (CommonUtils.IsNoProcedure)
            {
                tb = Public_ExecuteSqlService.Instance.NoStore_GetTestOrderArtSelfCheckList(orderId);
            }
            else
            {
                var sql = "select * from WO_TestOrderArtSelfCheckList where OrderId =@orderId";
                tb = new ExecuteSql().ExecuteSQL(sql, new QueryParameter[] {
                new QueryParameter("orderId", DataType.Int, orderId.ToString())
                });
            }
            if (tb.Rows.Count == 0)
                 return items;
             foreach (DataRow row in tb.Rows) {
                 var item = WO_TestOrderArtSelfCheckListHelper.FromDataRow(row);
                 items.Add(item);
             }

             return items;

         }

         public static List<WO_TestOrderExpertCheckList> GetTestOrderExpertCheckList(int orderId) {
             var items = new List<WO_TestOrderExpertCheckList>();
            //var sql = string.Format("select * from WO_TestOrderExpertCheckList where OrderId ={0}", orderId);
            //var tb = DBHelper.GetTable(sql);
            DataTable tb = new DataTable();
            if (CommonUtils.IsNoProcedure)
            {
                tb = Public_ExecuteSqlService.Instance.NoStore_GetTestOrderExpertCheckList(orderId);
            }
            else
            {
                var sql = "select * from WO_TestOrderExpertCheckList where OrderId =@orderId";
                tb = new ExecuteSql().ExecuteSQL(sql, new QueryParameter[] {
                new QueryParameter("orderId", DataType.Int, orderId.ToString())
                });
            }
            if (tb.Rows.Count == 0)
                 return items;
             foreach (DataRow row in tb.Rows) {
                 var item = WO_ExpertCheckListHelper.FromDataRow(row);
                 items.Add(item);
             }

             return items;

         }

         public static List<WO_TestOrder> GetAll() {


            var items = new List<WO_TestOrder>();
            //var sql = string.Format("select * from WO_TestOrder");
            //var tb = DBHelper.GetTable(sql);
            DataTable tb = new DataTable();
            if (CommonUtils.IsNoProcedure)
            {
                tb = Public_ExecuteSqlService.Instance.NoStore_WOTestOrder_GetAll();
            }
            else
            {
                var sql = "select * from WO_TestOrder";
                tb = new ExecuteSql().ExecuteSQL(sql);
            }
            if (tb.Rows.Count == 0)
                return items;
            foreach (DataRow row in tb.Rows) {
                var item = WO_TestOrderHelper.FromDataRow(row);
                items.Add(item);
            }

            return items;

         }
    }
}