﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 写监控点门限数据
    /// </summary>
    public class SetThreshold : BMessage
    {
        [Newtonsoft.Json.JsonProperty("Value")]
        public List<DeviceThreshold> Values { get; private set; }

        public SetThreshold() : base()
        {
            MessageType = (int)BMessageType.SET_THRESHOLD;
        }

        public SetThreshold(string fsuId, List<DeviceThreshold> value) : base()
        {
            MessageType = (int)BMessageType.SET_THRESHOLD;

            FSUID = fsuId;
            Values = value;
        }

        public override string Serialize()
        {
            string result = string.Empty;
            try
            {
                JsonSerializerSettings settings = new JsonSerializerSettings();
                settings.ContractResolver = new CustomContractResolver();
                TIotEntity iotEntity = new TIotEntity(this);
                result = JsonConvert.SerializeObject(iotEntity, settings);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetThreshold.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetThreshold.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
            }
            return result;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0},{1},{2}:", MessageId, (BMessageType)MessageType, FSUID);
            foreach (DeviceThreshold value in Values)
            {
                sb.AppendFormat("{0}", value.ToString());
            }
            return sb.ToString();
        }
    }
}