﻿using BDSTool.DBUtility;
using BDSTool.DBUtility.Common;
using BDSTool.Entity.B;
using BDSTool.Entity.DRHelper;
using BDSTool.Entity.S2;
using Common.Logging.Pro;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.BLL.B
{
    public partial class EquipmentCMCCBiz
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static bool GetAllByFsuId(string FSUID, List<TBL_EquipmentCMCC> listOfEquipmentCMCC) {
            try {
                DataTable table;
                if (CommonUtils.IsNoProcedure)
                {
                    table = Public_ExecuteSqlService.Instance.NoStore_TBLEquipmentCMCC_GetByFsuId(FSUID);
                }
                else
                {
                    var sql = string.Format(@"SELECT DeviceID, DeviceName, FSUID, StationId, MonitorUnitId, EquipmentId, RoomName, DeviceType,  DeviceSubType, Model, Brand, RatedCapacity, Version, BeginRunTime, DevDescribe FROM TBL_EquipmentCMCC  WHERE FSUID ={0}", SHelper.GetPara(FSUID));

                    table = DBHelper.GetTable(sql);
                }
                if (table != null) {
                    foreach (DataRow row in table.Rows) {
                        var devEx = TBL_EquipmentCMCCHelper.FromDataRow(row);
                        listOfEquipmentCMCC.Add(devEx);
                    }
                }
            }
            catch (Exception ex) {
                logger.ErrorFormat("GetAllByFsuId();FSUID={0};Error={1}", FSUID, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }

        public static   bool GetEquipmentIdListByFsuId(string FSUID, ref List<BDSTool.Entity.B.EquipmentKey> equipIdList) {
            try {

                DataTable table = null;
                if (CommonUtils.IsNoProcedure)
                {
                    table = Public_ExecuteSqlService.Instance.NoStore_TBLEquipmentCMCC_GetTableByFsuId(FSUID);
                }
                else
                {
                    var sql = string.Format("select FSUID, DeviceID, StationId, EquipmentId FROM TBL_EquipmentCMCC WHERE FSUID={0} and StationId is not null and EquipmentId is not null",
                    SHelper.GetPara(FSUID));
                    table = DBHelper.GetTable(sql);
                }

                if (table != null) {
                    foreach (DataRow row in table.Rows) {
                        BDSTool.Entity.B.EquipmentKey e=new BDSTool.Entity.B.EquipmentKey();
                        e.FSUID = (row["FSUID"].ToString());
                        e.DeviceID = (row["DeviceID"].ToString());
                        e.StationId =   int.Parse(row["StationId"].ToString());
                        e.EquipmentId = int.Parse(row["EquipmentId"].ToString());
                        equipIdList.Add(e);
                    }
                }
            }
            catch (Exception ex) {
                logger.ErrorFormat("GetEquipmentIdListByFsuId();FSUID={0};Error={1}", FSUID, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }

    }
}
