using Delivery.API;
using DM.TestOrder.DAL;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Text;

namespace DM.TestOrder.Controllers
{
    public class StandardDicSigCheckListController : BaseController{
        [HttpGet]
        public JsonResult Get()
        {
            var dt = WoStandardDicSigCheckListDal.GetAll();
            return new JsonResult(dt);
        }

        [HttpPost]
        public JsonResult POST([FromBody] JObject value)
        {
            var IsMust = value["CheckIsMust"].ToString(); // 是 或 否

            var rtnMsg = "";
            if (IsMust == "1")
            {
                if (string.IsNullOrEmpty(value["LimitDown"].ToString()) || string.IsNullOrEmpty(value["LimitUp"].ToString()))
                    rtnMsg = "设置为必测信号, 上限和下限不能为空";
                else
                {
                    var StandardDicId = int.Parse(value["StandardDicId"].ToString());
                    var LimitDown = float.Parse(value["LimitDown"].ToString());
                    var LimitUp = float.Parse(value["LimitUp"].ToString());

                    rtnMsg = WoStandardDicSigCheckListDal.UpdateOne(StandardDicId, IsMust, LimitDown, LimitUp);
                }
            }
            else
            {
                if (!string.IsNullOrEmpty(value["LimitDown"].ToString()) || !string.IsNullOrEmpty(value["LimitUp"].ToString()))
                    rtnMsg = "设置为非必测信号, 上限和下限必须为空";
                else
                {
                    var StandardDicId = int.Parse(value["StandardDicId"].ToString());

                    rtnMsg = WoStandardDicSigCheckListDal.UpdateOne(StandardDicId, IsMust, null, null);

                }
            }
            return new JsonResult(new
            {
                errormsg = rtnMsg
            });

           
        }

    }
}
