﻿using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace Delivery.Common.NoProcedure.Service
{
    public class WoTestOrderEquipItemCheckListService
    {
        private static WoTestOrderEquipItemCheckListService _instance = null;

        public static WoTestOrderEquipItemCheckListService Instance
        {
            get
            {
                if (_instance == null) _instance = new WoTestOrderEquipItemCheckListService();
                return _instance;
            }
        }

        public void RefreshCmd(int orderId)
        {
            try
            {
                using (DbHelper dbHelper = new DbHelper())
                {
                    ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);

                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("OrderId", orderId);
                    //执行update语句
                    execHelper.ExecuteNonQuery("WO_EquipItemCheckList_RefreshCmd", realParams);
                }
            }
            catch (Exception ex)
            {
                Logger.Log(ex);
                return;
            }
            
        }

        public DataTable WODebugCheckItemSignal(int orderId)
        {
            DataTable res = new DataTable();
            try
            {
                using (DbHelper dbHelper = new DbHelper())
                {
                    ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);

                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("OrderId", orderId);

                    res =  execHelper.ExecDataTable("WO_Debug_CheckItemSignal_select1", realParams);
                    return res;
                }
            }
            catch (Exception ex) {
                Logger.Log(ex);
                return new DataTable();
            }
        }

        public void RefreshSignal(int orderId)
        {
            try
            {
                using (DbHelper dbHelper = new DbHelper())
                {
                    ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);

                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("OrderId", orderId);                   
                        
                    //执行update语句
                    int UpdateCount = execHelper.ExecuteNonQuery("WO_EquipItemCheckList_RefreshSignalPrep_insert", realParams);
                    execHelper.ExecuteNonQuery("WO_EquipItemCheckList_RefreshSignal_update1", realParams);

                    if (UpdateCount > 0)
                    {
                        execHelper.ExecuteNonQuery("WO_EquipItemCheckList_RefreshSignal_updateSignal", realParams);
                        execHelper.ExecuteNonQuery("WO_EquipItemCheckList_RefreshSignal_updateEquip", realParams);
                    }

                }
            }
            catch (Exception ex)
            {
                Logger.Log(ex);
                return;
            }

        }

        public void RefreshEvent(int orderId)
        {
            try
            {
                using (DbHelper dbHelper = new DbHelper())
                {
                    ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);

                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("OrderId", orderId);
                    DateTime eventStartTime1 = DateTime.Now.AddDays(-7);
                    realParams.Add("EventStartTime1", eventStartTime1);
                    int valueInt = (int)execHelper.ExecuteScalar("RefreshEvent_ValueInt", realParams);
                    realParams.Add("ValueInt", valueInt);
                    //执行update语句
                    execHelper.ExecuteNonQuery("WO_EquipItemCheckList_RefreshActiveEvent", realParams);
                    execHelper.ExecuteNonQuery("WO_EquipItemCheckList_RefreshHistoryEvent", realParams);
                }
            }
            catch (Exception ex)
            {
                Logger.Log(string.Format("WO_EquipItemCheckList_RefreshEvent:{0}", ex));
                return;
            }
        }

    }
}
