﻿
using DM.TestOrder.Service.Interface;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web.Http;


namespace DM.TestOrder.Controllers {
        
    public class SubmitOnlineApplyController : BaseApiController{
        public class FormSubmitOnlineApply {
            //public string ApplyOrderId { get; set; }
            public int UserId {get;set;}
            public int OrderId {get;set;}
        }

        //var paras = {
        //        UserId: PartOrder.ApplyUserId,
        //        OrderId: OrderModel.OrderId                
        //};
        public HttpResponseMessage POST([FromBody]JObject value) {
            var formData = Newtonsoft.Json.JsonConvert.DeserializeObject<FormSubmitOnlineApply>(value.ToString());


            var errmsg = TestOrderApi.Instance.Section1_SubmitOnlineApply(formData.UserId, formData.OrderId);
            var rtn = new {
                errormsg = errmsg
            };

            var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(rtn);
            Debug.WriteLine("输入: "+value.ToString());
            Debug.WriteLine("输出: " + jsonRtn.ToString());

            return new HttpResponseMessage() {
                Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            };
        }

    }
}
