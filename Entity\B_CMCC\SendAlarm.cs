﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.6.2.	上报告警信息
    /// </summary>
    public sealed class SendAlarm : BMessage
    {
        public List<TAlarm> Values { get; private set; }

        public SendAlarm(string fsuId , List<TAlarm> values): base()
        {
            MessageType = (int)BMessageType.SEND_ALARM;

            FSUID = fsuId;
            Values = values;
        }

        public static SendAlarm Deserialize(XmlDocument xmlDoc)
        {
            string errorMsg = string.Empty;
            try
            {
                string fsuId = xmlDoc.SelectSingleNode("/Request/Info/FSUID").InnerText.Trim();
                XmlNodeList alarmNodeList = xmlDoc.SelectNodes("/Request/Info/Values/TAlarmList/TAlarm");

                List<TAlarm> alarmList = new List<TAlarm>();
                foreach (XmlNode alarmNode in alarmNodeList)
                {
                    TAlarm alarm = new TAlarm();
                    foreach (XmlNode node in alarmNode.ChildNodes)
                    {
                        switch (node.Name.Trim())
                        {
                            case "SerialNo": alarm.SerialNo = node.InnerText.Trim(); break;
                            case "ID": 
                                alarm.ID = node.InnerText.Trim();
                                if (!CheckNumberValue(alarm.ID))
                                {
                                    errorMsg = "TAlarm.ID is invalid";
                                    break;
                                }
                                break;
                            case "DeviceID": alarm.DeviceID = node.InnerText.Trim(); break;
                            case "NMAlarmID": alarm.NMAlarmID = node.InnerText.Trim(); break;
                            case "AlarmTime":
                                string strAlarmTime = node.InnerText.Trim();
                                if (string.IsNullOrEmpty(strAlarmTime))
                                {
                                    errorMsg = "TAlarm.AlarmTime is NullOrEmtpy";
                                }
                                else
                                {
                                    alarm.AlarmTime = Convert.ToDateTime(strAlarmTime);
                                }
                                break;
                            case "AlarmLevel": 
                                alarm.AlarmLevel = (EnumState)int.Parse(node.InnerText.Trim());
                                //告警等级转换
                                if (alarm.AlarmLevel == EnumState.CRITICAL)
                                {
                                    alarm.MyAlarmLevel = EnumLevel.CRITICAL;
                                }
                                else if (alarm.AlarmLevel == EnumState.MAJOR)
                                {
                                    alarm.MyAlarmLevel = EnumLevel.MAJOR;
                                }
                                else if (alarm.AlarmLevel == EnumState.MINOR)
                                {
                                    alarm.MyAlarmLevel = EnumLevel.MINOR;
                                }
                                else if (alarm.AlarmLevel == EnumState.HINT)
                                {
                                    alarm.MyAlarmLevel = EnumLevel.HINT;
                                }
                                break;
                            case "AlarmFlag":
                                string strAlarmFlag = node.InnerText.Trim().ToUpper();
                                if (strAlarmFlag == "BEGIN")
                                {
                                    alarm.AlarmFlag = EnumFlag.BEGIN;
                                }
                                else if (strAlarmFlag == "END")
                                {
                                    alarm.AlarmFlag = EnumFlag.END;
                                }
                                else
                                {
                                    //alarm.AlarmFlag = (EnumFlag)int.Parse(strAlarmFlag);
                                    throw new Exception("SendAlarm.Deserialize()方法中XML元素AlarmFlag的值不合法.");
                                    //目前文档可能有问题，没有明确给出 BEGIN = 0, END = 1,暂时按字符串处理，跟文档保持一致
                                }
                                break;
                            case "AlarmDesc": alarm.AlarmDesc = node.InnerText; break;
                            ////注意：上报的告警触发值不允许为空，否则直接回复上报失败，不存库
                            case "EventValue":
                                string strValue = node.InnerText.Trim();
                                if (string.IsNullOrEmpty(strValue))
                                {
                                    errorMsg = "TAlarm.EventValue is NullOrEmtpy";
                                }
                                else
                                {
                                    alarm.EventValue = float.Parse(strValue);
                                }
                                break;
                            case "SignalNumber": 
                                alarm.SignalNumber = node.InnerText.Trim();
                                if (!string.IsNullOrEmpty(alarm.SignalNumber) && !CheckNumberValue(alarm.SignalNumber))
                                {
                                    errorMsg = "TAlarm.SignalNumber is invalid";
                                    break;
                                }
                                break;
                            case "AlarmRemarK": alarm.AlarmRemarK = node.InnerText.Trim(); break;
                            default: break;
                        }
                    }
                    if (errorMsg != string.Empty)
                    {
                        break;
                    }
                    alarmList.Add(alarm);
                }
                //一旦上报告警触发值为空，则直接构建一个带错误信息的空实体
                SendAlarm sendAlarm = null;
                if (errorMsg != string.Empty)
                {
                    sendAlarm = GetErrorEntity(errorMsg);
                    entityLogger.ErrorFormat("SendAlarm.Deserialize():{0}", errorMsg);
                }
                else
                {
                    sendAlarm = new SendAlarm(fsuId, alarmList);
                }
                entityLogger.DebugFormat("SendAlarm.Deserialize(),xml:\r\n" + FormatXml(xmlDoc.InnerXml));
                sendAlarm.StringXML = xmlDoc.InnerXml;
                return sendAlarm;
            }
            catch(Exception ex)
            {
                entityLogger.ErrorFormat("SendAlarm.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SendAlarm.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                //反序列化异常时，返回带错误信息的实体
                SendAlarm errorEntity = GetErrorEntity(string.Format("SEND_ALARM Deserialize failure:{0}", ex.Message));
                errorEntity.StringXML = xmlDoc.InnerXml;
                return errorEntity;
            }
        }

        /// <summary>
        /// 反序列化异常时，构造带错误信息的实体
        /// </summary>
        /// <param name="errorMsg"></param>
        /// <returns></returns>
        private static SendAlarm GetErrorEntity(string errorMsg)
        {
            SendAlarm errorEntity = new SendAlarm(string.Empty, null);
            SendAlarmAck ackEntity = new SendAlarmAck(EnumResult.FAILURE, errorMsg);
            errorEntity.ErrorMsg = ackEntity.Serialize();
            return errorEntity;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}, {2}:", MessageId, MessageType, FSUID);
            sb.AppendLine();
            if (Values != null)
            {
                foreach (TAlarm alarm in Values)
                {
                    sb.AppendFormat("{0}\r\n", alarm.ToString());
                }
            }
            return sb.ToString();
        }

    }
}
