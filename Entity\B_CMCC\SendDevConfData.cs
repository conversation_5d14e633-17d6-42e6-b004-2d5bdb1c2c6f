﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.2.6.17　上报动环设备的配置数据
    /// </summary>
    public sealed class SendDevConfData:BMessage
    {
        public List<TDevConf> Values { get; private set; }

        public SendDevConfData(string fsuId, List<TDevConf> values) : base()
        {
            MessageType = (int)BMessageType.SEND_DEV_CONF_DATA;

            FSUID = fsuId;
            Values = values;
        }

        public SendDevConfData() : base() 
        {
            MessageType = (int)BMessageType.SEND_DEV_CONF_DATA;
        }

        public XmlDocument ContentXML;

        public static SendDevConfData Deserialize(XmlDocument xmlDoc)
        {
            SendDevConfData sendDevConfData = null;
            string errorMsg = string.Empty;
            try
            {
                string fsuId = xmlDoc.SelectSingleNode("/Request/Info/FSUID").InnerText;
                XmlNodeList devNodeList = xmlDoc.SelectNodes("/Request/Info/Values/Device");

                List<TDevConf> devList = new List<TDevConf>();
                foreach (XmlNode device in devNodeList)
                {
                    TDevConf devConf = new TDevConf();
                    devConf.DeviceID = device.Attributes["DeviceID"].Value.Trim();
                    devConf.DeviceName = device.Attributes["DeviceName"].Value.Trim();

                    devConf.SiteID = device.Attributes["SiteID"].Value.Trim();
                    devConf.RoomID = device.Attributes["RoomID"].Value.Trim();

                    devConf.SiteName = device.Attributes["SiteName"].Value.Trim();
                    devConf.RoomName = device.Attributes["RoomName"].Value.Trim();
                    string strDeviceType = device.Attributes["DeviceType"].Value.Trim();
                    if(string.IsNullOrEmpty(strDeviceType))
                    {
                        devConf.DeviceType = null;
                    }
                    else
                    {
                        devConf.DeviceType = int.Parse(strDeviceType);
                    }
                    string strDeviceSubType = device.Attributes["DeviceSubType"].Value.Trim();
                    if (string.IsNullOrEmpty(strDeviceSubType))
                    {
                        devConf.DeviceSubType = null;
                    }
                    else
                    {
                        devConf.DeviceSubType = int.Parse(strDeviceSubType);
                    }
                    devConf.Model = device.Attributes["Model"].Value.Trim();
                    devConf.Brand = device.Attributes["Brand"].Value.Trim();
                    //额定容量可能为空
                    string strRatedCapacity = device.Attributes["RatedCapacity"].Value.Trim();
                    if (string.IsNullOrEmpty(strRatedCapacity))
                    {
                        devConf.RatedCapacity = null;
                    }
                    else
                    {
                        devConf.RatedCapacity = float.Parse(strRatedCapacity);
                    }
                    devConf.Version = device.Attributes["Version"].Value.Trim();
                    //设备启用时间可能为空
                    string strBeginTime = device.Attributes["BeginRunTime"].Value.Trim();
                    if (string.IsNullOrEmpty(strBeginTime))
                    {
                        devConf.BeginRunTime = null;
                    }
                    else
                    {
                        devConf.BeginRunTime = Convert.ToDateTime(strBeginTime);
                    }
                    devConf.DevDescribe = device.Attributes["DevDescribe"].Value;
                    devConf.ConfRemark = device.Attributes["ConfRemark"].Value.Trim();
                    if (device.ChildNodes.Count > 0)
                    {
                        if (device.ChildNodes[0].ChildNodes.Count > 0)
                        {
                            List<TSignal> signalList = new List<TSignal>();
                            foreach (XmlNode signal in device.ChildNodes[0].ChildNodes)
                            {
                                TSignal tSignal = new TSignal();
                                tSignal.Type = (EnumType)int.Parse(signal.Attributes["Type"].Value.Trim());
                                tSignal.ID = signal.Attributes["ID"].Value.Trim();
                                if (!CheckNumberValue(tSignal.ID))
                                {
                                    errorMsg = "TSignal.ID is invalid";
                                    break;
                                }
                                tSignal.SignalName = signal.Attributes["SignalName"].Value.Trim();
                                tSignal.AlarmLevel = (EnumState)int.Parse(signal.Attributes["AlarmLevel"].Value.Trim());
                                //上报配置中的float类型值可以为空值
                                string strThreshold = signal.Attributes["Threshold"].Value.Trim();
                                if (string.IsNullOrEmpty(strThreshold) || strThreshold.ToUpper() == "NULL")
                                {
                                    tSignal.Threshold = null;
                                }
                                else
                                {
                                    tSignal.Threshold = float.Parse(strThreshold);
                                }
                                //string strAbsoluteVal = signal.Attributes["AbsoluteVal"].Value.Trim();
                                //if (string.IsNullOrEmpty(strAbsoluteVal))
                                //{
                                //    tSignal.AbsoluteVal = null;
                                //}
                                //else
                                //{
                                //    tSignal.AbsoluteVal = float.Parse(strAbsoluteVal);
                                //}
                                //string strRelativeVal = signal.Attributes["RelativeVal"].Value.Trim();
                                //if (string.IsNullOrEmpty(strRelativeVal))
                                //{
                                //    tSignal.RelativeVal = null;
                                //}
                                //else
                                //{
                                //    tSignal.RelativeVal = float.Parse(strRelativeVal);
                                //}
                                //底端上报的Describe包含&时，发转现会被转义多次，增加兼容&amp;amp;
                                if (signal.Attributes["Describe"] == null)
                                {
                                    if (tSignal.Type == EnumType.DO)//遥控
                                    {
                                        tSignal.Describe = "1&待命";
                                    }
                                    else if (tSignal.Type == EnumType.DI)//遥信
                                    {
                                        tSignal.Describe = "0&0;1&1";
                                    }
                                    else
                                    {
                                        tSignal.Describe = "";
                                    }
                                }
                                else
                                {
                                    string strDescribe = signal.Attributes["Describe"].Value.Trim();
                                    if (strDescribe.Contains("&amp;"))
                                    {
                                        strDescribe.Replace("&amp;", "&");
                                    }
                                    tSignal.Describe = strDescribe;
                                }
                                tSignal.NMAlarmID = signal.Attributes["NMAlarmID"].Value.Trim();
                                tSignal.SignalNumber = signal.Attributes["SignalNumber"].Value.Trim();
                                if (!string.IsNullOrEmpty(tSignal.SignalNumber) && !CheckNumberValue(tSignal.SignalNumber))
                                {
                                    errorMsg = "TSignal.SignalNumber is invalid";
                                    break;
                                }
                                //告警等级转换
                                if (tSignal.AlarmLevel == EnumState.CRITICAL)
                                {
                                    tSignal.MyAlarmLevel = EnumLevel.CRITICAL;
                                }
                                else if (tSignal.AlarmLevel == EnumState.MAJOR)
                                {
                                    tSignal.MyAlarmLevel = EnumLevel.MAJOR;
                                }
                                else if (tSignal.AlarmLevel == EnumState.MINOR)
                                {
                                    tSignal.MyAlarmLevel = EnumLevel.MINOR;
                                }
                                else if (tSignal.AlarmLevel == EnumState.HINT)
                                {
                                    tSignal.MyAlarmLevel = EnumLevel.HINT;
                                }

                                signalList.Add(tSignal);
                            }
                            if (errorMsg != string.Empty)
                            {
                                break;
                            }
                            devConf.Signals = signalList;
                        }
                    }
                    devList.Add(devConf);
                }
                if (errorMsg == string.Empty)
                {
                    sendDevConfData = new SendDevConfData(fsuId, devList);
                    sendDevConfData.ContentXML = xmlDoc;
                }
                else
                {
                    entityLogger.ErrorFormat("SendDevConfData.Deserialize():{0}", errorMsg);                    
                    sendDevConfData = new SendDevConfData();
                    sendDevConfData.ErrorMsg = errorMsg;
                    sendDevConfData.ContentXML = xmlDoc;
                }
                sendDevConfData.StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("SendDevConfData.Deserialize(),xml:\r\n{0}", xmlDoc.InnerXml);
                entityLogger.DebugFormat("SendDevConfData.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                return sendDevConfData;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SendDevConfData.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SendDevConfData.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                sendDevConfData = new SendDevConfData();
                sendDevConfData.StringXML = xmlDoc.InnerXml;
                sendDevConfData.ErrorMsg = ex.Message;
                sendDevConfData.ContentXML = xmlDoc;
                return sendDevConfData;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}, {2}:", MessageId, MessageType, FSUID);
            sb.AppendLine();
            if (Values != null)
            {
                foreach(TDevConf decConf in Values)
                {
                    sb.AppendFormat("{0}\r\n", decConf.ToString());
                }
            }
            return sb.ToString();
        }
    }
}
