﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Delivery.Common
{
    public class Validation
    {
        /// <summary>
        /// 是否数字
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static bool IsNum(string input)
        {
            if (input == "")
            {
                return false;
            }
            else
            {
                Regex m_regex = new System.Text.RegularExpressions.Regex("^(-?[0-9]*[.]*[0-9]{0,3})$");
                return m_regex.IsMatch(input);
            }
        }

        /// <summary>
        /// 过滤非法符串
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static string GetSafeSQL(string value)
        {
            if (string.IsNullOrEmpty(value))
                return string.Empty;
            value = Regex.Replace(value, @";", string.Empty);
            value = Regex.Replace(value, @"'", string.Empty);
            value = Regex.Replace(value, @"&", string.Empty);
            value = Regex.Replace(value, @"%20", string.Empty);
            value = Regex.Replace(value, @"--", string.Empty);
            value = Regex.Replace(value, @"==", string.Empty);
            value = Regex.Replace(value, @"<", string.Empty);
            value = Regex.Replace(value, @">", string.Empty);
            value = Regex.Replace(value, @"%", string.Empty);
            value = Regex.Replace(value, @"delete", string.Empty);
            value = Regex.Replace(value, @"update", string.Empty);
            value = Regex.Replace(value, @"insert", string.Empty); 
            value = value.Trim();
            return value;
        }

        /// <summary>
        /// 校验手机号码是否符合标准。
        /// </summary>
        /// <param name="mobile"></param>
        /// <returns></returns>
        public static bool ValidateMobile(string mobile)
        {
            if (string.IsNullOrEmpty(mobile))
                return false;

            Regex regex = new Regex("^1\\d{10}$");

            return regex.IsMatch(mobile);

        }

        /// </summary>  
        /// <param name="source"></param> 
        /// <returns></returns>  
        public static bool IsEmail(string source)
        {
            return
            Regex.IsMatch(source,
            @"^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$",
            RegexOptions.IgnoreCase);
        }
        public static bool HasEmail(string source)
        {
            return
            Regex.IsMatch(source,
            @"[A-Za-z0-9](([_\.\-]?[a-zA-Z0-9]+)*)@([A-Za-z0-9]+)(([\.\-]?[a-zA-Z0-9]+)*)\.([A-Za-z]{2,})",
            RegexOptions.IgnoreCase);
        }
    }
}
