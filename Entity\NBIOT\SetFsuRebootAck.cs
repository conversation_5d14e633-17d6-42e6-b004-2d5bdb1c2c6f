﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 重启FSU应答
    /// </summary>
    public class SetFsuRebootAck : BMessage
    {
        //返回结果
        public EnumResult Result { get; set; }

        // 失败的原因
        public string FailureCause { get; set; }

        public SetFsuRebootAck() : base()
        {
            MessageType = (int)BMessageType.SET_FSUREBOOT_ACK;
        }

        public SetFsuRebootAck(string fsuId, EnumResult result, string failureCause):base()
        {
            MessageType = (int)BMessageType.SET_FSUREBOOT_ACK;

            FSUID = fsuId;
            Result = result;
            FailureCause = failureCause;
        }

        public static SetFsuRebootAck Deserialize(string json)
        {
            SetFsuRebootAck info = null;
            try
            {
                info = JsonConvert.DeserializeObject<SetFsuRebootAck>(json);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetFsuRebootAck.Deserialize:{0}" , ex.Message);
                entityLogger.ErrorFormat("SetFsuRebootAck.Deserialize:{0}" , ex.StackTrace);
                info = new SetFsuRebootAck();
                info.ErrorMsg = ex.Message;
                info.StringXML = json;
            }
            return info;
        }

        public override string ToString()
        {
            return String.Format("{0},{1},{2}:{3},{4}",
                MessageId, (BMessageType)MessageType, FSUID, Result, FailureCause);
        }
    }
}