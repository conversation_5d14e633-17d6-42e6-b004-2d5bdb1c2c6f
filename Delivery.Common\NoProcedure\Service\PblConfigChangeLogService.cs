﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;

namespace Delivery.Common.NoProcedure.Service
{
    public class PblConfigChangeLogService
    {
        private static PblConfigChangeLogService _instance = null;

        public static PblConfigChangeLogService Instance
        {
            get
            {
                if (_instance == null) _instance = new PblConfigChangeLogService();
                return _instance;
            }
        }
        /// <summary> 存储过程 PBL_ConfigChangeLog 的实现 </summary>
        public void DoExecute(string objectId, int configId, int editType, ExecuteHelper helper = null)
        {
            if(helper == null)
            {
                using (DbHelper dbHelper = new DbHelper())
                {
                    ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                    try
                    {
                        CommonExecute(objectId, configId, editType, executeHelper);
                        executeHelper.Commit();
                    }
                    catch (Exception ex)
                    {
                        executeHelper.Rollback();
                        Logger.Log("PblConfigChangeLogService.doExecute throw Exception: ", LogType.Error);
                        Logger.Log(ex);
                    }
                    finally
                    {
                        dbHelper.Connection.Close();
                    }
                }
            } else
            {
                CommonExecute(objectId, configId, editType, helper);
            }
        }

        private void CommonExecute(string objectId, int configId, int editType, ExecuteHelper executeHelper)
        {
            objectId = objectId == null ? "" : objectId;
            Dictionary<string, object> realParams = new Dictionary<string, object>();
            realParams.Add("ObjectId", objectId);
            realParams.Add("ConfigId", configId);
            realParams.Add("EditType", editType);
            //【原注释】：记录本记录到[TBL_ConfigChangeMicroLog]（如果该Config有其他EditType记录则删除其他EditType记录）
            object countObj = executeHelper.ExecuteScalar("ConfigChangeLog_GetOldCount", realParams);
            int count = countObj == null || countObj == DBNull.Value ? 0 : int.Parse(countObj.ToString());
            if (count > 0)
            {
                executeHelper.ExecuteNonQuery("ConfigChangeLog_UpdateChangeLog", realParams);
            }
            else
            {
                executeHelper.ExecuteNonQuery("ConfigChangeLog_DelChangeLog", realParams);
                executeHelper.ExecuteNonQuery("ConfigChangeLog_InsertChangeLog", realParams);
            }

            /*【原注释】：杭州电信C接口修改,增加写C_ConfigChangeMicroLog,供C接口增量配置使用,20141204,BEGIN*/
            object countObjC = executeHelper.ExecuteScalar("ConfigChangeLog_GetOldCountC", realParams);
            int countC = countObjC == null || countObjC == DBNull.Value ? 0 : int.Parse(countObjC.ToString());
            if (countC > 0)
            {
                executeHelper.ExecuteNonQuery("ConfigChangeLog_UpdateChangeLogC", realParams);
            }
            else
            {
                executeHelper.ExecuteNonQuery("ConfigChangeLog_InsertChangeLogC", realParams);
            }
            /*【原注释】：杭州电信C接口修改,增加写C_ConfigChangeMicroLog,供C接口增量配置使用,20141204,END*/

            //【原注释】：查询[TBL_ConfigChangeMap]
            int? v_MacroConfigId = null;
            int? v_MacroEditType = null;
            string v_IdConvertRule = null;
            DataTable mapInfo = executeHelper.ExecDataTable("ConfigChangeLog_GetMapInfo", realParams);
            if (mapInfo == null || mapInfo.Rows.Count <= 0)
            {
                return;
            }
            else
            {
                DataRow row = mapInfo.Rows[0];
                v_MacroConfigId = row.Field<int?>("MacroConfigId");
                v_MacroEditType = row.Field<int?>("MacroEditType");
                v_IdConvertRule = row.Field<string>("IdConvertRule");
                //【原注释】：如果不需要生成宏变更记录，则退出
                if (!v_MacroConfigId.HasValue || !v_MacroEditType.HasValue)
                {
                    return;
                }
            }
            v_IdConvertRule = v_IdConvertRule == null ? "" : v_IdConvertRule;
            //【原注释】：生成细记录ID临时表
            string[] tmpStr1Array = objectId.Split('.');
            //【原注释】：生成宏记录ID转换规则表
            string[] tmpStr2Array = v_IdConvertRule.Split('.');

            //【原注释】：生成宏记录ID
            string v_MacroObjectId = "";
            if (tmpStr2Array.Length > 0)
            {
                for (int i = 0, len = tmpStr2Array.Length; i < len; i++)
                {
                    string tmpPosStr = tmpStr2Array[i];
                    if(int.TryParse(tmpPosStr, out int tmpPos))
                    {
                        string tmpStr2 = tmpStr1Array[tmpPos - 1];
                        v_MacroObjectId = tmpStr2 + ".";
                    }
                }
            }
            v_MacroObjectId = v_MacroObjectId.Trim('.'); //Trim('.')可以去掉前后的连续的.。".a.b.a.d...".Trim('.');//"a.b.a.d"
            //【原注释】：插入宏记录
            realParams.Clear();
            realParams.Add("MacroObjectId", v_MacroObjectId);
            realParams.Add("MacroConfigId", v_MacroConfigId.Value);
            realParams.Add("MacroEditType", v_MacroEditType.Value);
            object countObjMacroLog = executeHelper.ExecuteScalar("ConfigChangeLog_GetOldCount_MacroLog", realParams);
            int countMacroLog = countObjMacroLog == null || countObjMacroLog == DBNull.Value ? 0 : int.Parse(countObjMacroLog.ToString());
            if (countMacroLog > 0)
            {
                executeHelper.ExecuteNonQuery("ConfigChangeLog_UpdateChangeLog_MacroLog", realParams);
            }
            else
            {
                executeHelper.ExecuteNonQuery("ConfigChangeLog_DelChangeLog_MacroLog", realParams);
                executeHelper.ExecuteNonQuery("ConfigChangeLog_InsertChangeLog_MacroLog", realParams);
            }
        }
    }
}
