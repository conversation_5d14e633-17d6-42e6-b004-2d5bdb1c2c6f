﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Delivery.API._5_Common
{
    public class LogonIdRequireAttribute : ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext context)
        {
            //添加此校验是给割接单提交照片后，工作流系统能够下载上传的照片
            string requestPath = context.HttpContext.Request.Path.Value?.ToLower();
            if (requestPath != null && (requestPath.Contains("/api/equipfiledownload") || requestPath.Contains("/api/equipfiledownload/")))
            {
                base.OnActionExecuting(context);
                return;
            }

            string userInfo;
            context.HttpContext.Request.Cookies.TryGetValue("userinfo", out userInfo);
            if (userInfo != null) {
                Match match = new Regex("(?<=LogonId=)(.*)(?=&)").Match(userInfo);
                if (string.IsNullOrEmpty(match.Value))
                {
                    context.Result = new UnauthorizedObjectResult("logonId not found.");
                }
                else
                {
                    base.OnActionExecuting(context);
                }
            }
            else
            {
                context.Result = new UnauthorizedObjectResult("logonId not found.");
            }
        }
    }
}
