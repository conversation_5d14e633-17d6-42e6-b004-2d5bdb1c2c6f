﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace Delivery.API
{
    public class DataTableColumnMapper
    {
        public static void RenameColumns(DataTable table, Dictionary<string, string> fieldMap)
        {
            if (table == null || fieldMap == null)
                return;

            // 避免运行时修改正在遍历的列集合，先记录需要替换的列
            var renamePlan = new Dictionary<string, string>();
            foreach (DataColumn col in table.Columns)
            {
                var original = col.ColumnName;
                var lookupKey = original.ToLower();
                if (fieldMap.TryGetValue(lookupKey, out string newName) && original != newName)
                {
                    renamePlan[original] = newName;
                }
            }

            // 执行重命名
            foreach (var kv in renamePlan)
            {
                table.Columns[kv.Key].ColumnName = kv.Value;
            }
        }
    }
}
