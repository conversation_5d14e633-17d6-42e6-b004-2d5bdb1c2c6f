﻿
using DM.TestOrder.Common.DBA;
using DM.TestOrder.Entity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Text;
using testorder.dal;
using DM.TestOrder.DAL.Helper;
using DM.TestOrder.Common.DBA;



namespace DM.TestOrder.DAL {


    public class WoTestOrderFlowDal
    {

        public static void AddOneAndUpdateOrder(WO_TestOrderFlow newOrder) {
            var old = WoTestOrderDal.GetOne(newOrder.OrderId);
            if (old==null)
                throw new Exception("工单不存在");

            if (old.OrderState!= newOrder.OldOrderState) {
                var s = string.Format("工单状态已更新,审批失败。当前状态为{0},提交原状态为{1}", old.OrderState, newOrder.OldOrderState);
                Debug.WriteLine(s);
                throw new Exception(s);
            }

            var sql = string.Format("WO_ApproveExpert {0},{1},{2},{3},{4},{5},{6},{7}",
                newOrder.OrderId, 
                newOrder.StateSetUserId, newOrder.OldOrderState,SHelper.GetPara(newOrder.IsApprove),                
                newOrder.NewOrderState, SHelper.GetPara(newOrder.Decision), SHelper.GetPara(newOrder.Note),  SHelper.GetPara(newOrder.FlowText));

            DBHelper.ExecuteNonQuery(sql);

        }

        public static List<WO_TestOrderFlow> GetByOrderId(int orderId) {

            var items = new List<WO_TestOrderFlow>();
            var sql = string.Format("select * from WO_TestOrderFlow where OrderId ={0}", orderId);
            var tb = DBHelper.GetTable(sql);
            if (tb.Rows.Count == 0)
                return items;
            foreach (DataRow row in tb.Rows) {
                var item = WO_TestOrderFlowHelper.FromDataRow(row);
                items.Add(item);
            }

            return items;

        }
           
       }


}

