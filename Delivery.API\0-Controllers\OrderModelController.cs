﻿
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using DM.TestOrder.Service.Interface;
using Delivery.API;
using Microsoft.AspNetCore.Mvc;

namespace DM.TestOrder.Controllers {
        
    public class OrderModelController : BaseController{
        //api/OrderModel/1
        [HttpGet]
        public JsonResult Get(int id) {
            string errmsg;
            var ord = TestOrderApi.Instance.GetOrderModel(id,  out errmsg);

            var json = "";
            if (ord != null)
                // json = Newtonsoft.Json.JsonConvert.SerializeObject(ord);
                return new JsonResult(ord);
            else
            {
                // json = Newtonsoft.Json.JsonConvert.SerializeObject( new {errormsg = errmsg});
                return new JsonResult(new { errormsg = errmsg });
            }

            //return new HttpResponseMessage() {
            //    Content = new StringContent(json, Encoding.UTF8, "application/json"),
            //};
        }
    }
}
