﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B
{
    /// <summary>
    /// 5.2.11.13　用户获取FSU的状态信息
    /// </summary>
    public sealed class GetFsuInfo : BMessage
    {
        public GetFsuInfo(string fsuId, string fsuCode)
            : base()
        {
            MessageType = (int)BMessageType.GetFsuInfo;
            FsuId = fsuId;
            FsuCode = fsuCode;
        }

        public override string Serialize()
        {
            XmlDocument xmldoc = CreateXmlDocument();
            AppendPK_Type(xmldoc, "Request","GET_FSUINFO", "1701");

            XmlElement xe2 = xmldoc.CreateElement("Info");
            XmlElement xe21 = xmldoc.CreateElement("FsuId");
            xe21.InnerText = FsuId;
            xe2.AppendChild(xe21);
            XmlElement xe22 = xmldoc.CreateElement("FsuCode");
            xe22.InnerText = FsuCode;
            xe2.AppendChild(xe22);

            XmlNode root = xmldoc.SelectSingleNode("Request");
            root.AppendChild(xe2);

            return xmldoc.InnerXml;
        }

        public static GetFsuInfo Deserialize(string raw)
        {
            throw new NotImplementedException();
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}: {2}, {3}", MessageId, (BMessageType)MessageType, FsuId, FsuCode);
        }
    }
}
