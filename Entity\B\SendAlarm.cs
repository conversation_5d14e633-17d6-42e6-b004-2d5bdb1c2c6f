﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Diagnostics;
using System.Xml;

namespace ENPC.Kolo.Entity.B
{
    /// <summary>
    /// 5.2.11.2　上报告警信息
    /// </summary>
    public class SendAlarm : BMessage
    {
        public TAlarm[] Alarms { get; private set; }

        public SendAlarm(params TAlarm[] alarms)
            : base()
        {
            Debug.Assert(alarms != null);

            MessageType = (int)BMessageType.SendAlarm ;
            Alarms = alarms;
        }

        public override string Serialize()
        {
            throw new NotImplementedException();
        }

        public static SendAlarm Deserialize(XmlDocument xmldoc)
        {
            XmlNodeList nodelist = xmldoc.SelectNodes("/Request/Info/Values/TAlarmList/TAlarm");

            List<TAlarm> tAlarms = new List<TAlarm>();
           
            foreach (XmlNode xn in nodelist)
            {
                TAlarm alarm = new TAlarm();

                foreach (XmlNode xnSub in xn.ChildNodes)
                { 
                    if(xnSub.Name == "SerialNo")
                    {
                        alarm.SerialNo = UInt32.Parse(xnSub.InnerText);
                    }
                    else if (xnSub.Name == "Id")
                    {
                        alarm.Id = xnSub.InnerText;
                    }
                    else if (xnSub.Name == "FsuId")
                    {
                        alarm.FsuId = xnSub.InnerText;
                    }
                    else if (xnSub.Name == "FsuCode")
                    {
                        alarm.FsuCode = xnSub.InnerText;
                    }
                    else if (xnSub.Name == "DeviceId")
                    {
                        alarm.DeviceId = xnSub.InnerText;
                    }
                    else if (xnSub.Name == "DeviceCode")
                    {
                        alarm.DeviceCode = xnSub.InnerText;
                    }
                    else if (xnSub.Name == "AlarmTime")
                    {
                        alarm.AlarmTime = Convert.ToDateTime(xnSub.InnerText);
                    }
                     else if (xnSub.Name == "AlarmLevel")
                    {
                        int level = Convert.ToInt32(xnSub.InnerText);
                        switch (level)
                        {
                            case 1: alarm.AlarmLevel = (EnumState)3; break;
                            case 2: alarm.AlarmLevel = (EnumState)2; break;
                            case 3: alarm.AlarmLevel = (EnumState)1; break;
                            case 4: alarm.AlarmLevel = (EnumState)0; break;
                            default: alarm.AlarmLevel = (EnumState)0; break;
                        }
                    }
                    else if (xnSub.Name == "AlarmFlag")
                    {
                        if (xnSub.InnerText == "BEGIN")
                        {
                            alarm.AlarmFlag = EnumFlag.Begin;
                        }
                        else
                        {
                            alarm.AlarmFlag = EnumFlag.End;
                        }
                    }
                    else if (xnSub.Name == "AlarmDesc")
                    {
                        alarm.Meanings = xnSub.InnerText;
                    }
                }

                tAlarms.Add(alarm);
            }

            SendAlarm sendAlarm = new SendAlarm();

            sendAlarm.Alarms = tAlarms.ToArray();

            return sendAlarm;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}:", MessageId, MessageType);
            sb.AppendLine();
            if (Alarms != null)
            {
                foreach (TAlarm item in Alarms)
                {
                    sb.AppendFormat("{0}\r\n", item);
                }
            }

            return sb.ToString();
        }
    }
}
