namespace BDSTool.Entity.S2
{
    using System;
    using System.Collections.Generic;
    
    public partial class TBL_Signal
    {
        public int EquipmentTemplateId { get; set; }
        public int SignalId { get; set; }
        public bool Enable { get; set; }
        public bool Visible { get; set; }
        public string Description { get; set; }
        public string SignalName { get; set; }
        public int SignalCategory { get; set; }
        public int SignalType { get; set; }
        public int ChannelNo { get; set; }
        public int ChannelType { get; set; }
        public string Expression { get; set; }
        public Nullable<int> DataType { get; set; }
        public string ShowPrecision { get; set; }
        public string Unit { get; set; }
        public Nullable<double> StoreInterval { get; set; }
        public Nullable<double> AbsValueThreshold { get; set; }
        public Nullable<double> PercentThreshold { get; set; }
        public Nullable<int> StaticsPeriod { get; set; }
        public Nullable<decimal> BaseTypeId { get; set; }
        public Nullable<double> ChargeStoreInterVal { get; set; }
        public Nullable<double> ChargeAbsValue { get; set; }
        public int DisplayIndex { get; set; }
        public Nullable<int> MDBSignalId { get; set; }
        public int ModuleNo { get; set; }
    }
}
