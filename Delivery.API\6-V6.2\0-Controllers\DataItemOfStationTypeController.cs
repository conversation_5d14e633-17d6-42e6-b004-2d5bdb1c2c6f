﻿using Delivery.API;
using DM.TestOrder.DAL;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using testorder.dal;



namespace DM.TestOrder.Controllers {

    public class DataItemOfStationTypeController : BaseController {

        [HttpGet]
        public JsonResult Get() {
            var dt = WrDataItemOfStationTypeDal.GetAll();
            //var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(dt);

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};
            return new JsonResult(dt);

        }
    }
}
