﻿using Delivery.API;
using DM.TestOrder.DAL;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using testorder.dal;



namespace DM.TestOrder.Controllers {

    public class DataItemOfStationTypeController : BaseController {

        [HttpGet]
        public JsonResult Get() {
            var dt = WrDataItemOfStationTypeDal.GetAll();
            DataTableColumnMapper.RenameColumns(dt, StationTypeFieldMap);
            //var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(dt);

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};
            return new JsonResult(dt);

        }

        private static readonly Dictionary<string, string> StationTypeFieldMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "entryitemid", "EntryItemId" },
            { "entryid", "EntryId" },
            { "itemid", "ItemId" },
            { "parententryid", "ParentEntryId" },
            { "parentitemid", "ParentItemId" },
            { "itemvalue", "ItemValue" },
            { "lastupdatedate", "LastUpdateDate" },
            { "extendfield1", "ExtendField1" },
            { "extendfield2", "ExtendField2" },
            { "extendfield3", "ExtendField3" }
        };
    }
}
