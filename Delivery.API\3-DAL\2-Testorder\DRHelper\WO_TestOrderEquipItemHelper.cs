﻿
using DM.TestOrder.Common.DBA;


using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using DM.TestOrder.Common;
using DM.TestOrder.Entity;


namespace DM.TestOrder.DAL.Helper
{
    public class WO_TestOrderEquipItemHelper
    {
        public static WO_TestOrderEquipItem FromDataRow(DataRow row) {
            var e = new WO_TestOrderEquipItem();
            try {

                e.OrderEquipId = SHelper.ToInt(row["OrderEquipId"]);
                e.OrderId = SHelper.ToInt(row["OrderId"]);
                e.EquipmentId = SHelper.ToInt(row["EquipmentId"].ToString());
                e.BaseEquipmentID = SHelper.ToInt(row["BaseEquipmentID"].ToString());
                e.BaseEquipmentName = row["BaseEquipmentName"].ToString();
                e.UriProtocol = row["UriProtocol"].ToString();
                e.UriImage = row["UriImage"].ToString();
                e.SaveTime = SHelper.ToDateTime(row["SaveTime"]);
            }
            catch (Exception ex) {
                MyLogger.ErrorFormat("WO_TestOrderEquipItem.FromDataRow();error={0}", ex.Message);
                MyLogger.Error(ex.StackTrace);
                return null;
            }
            return e;
        }
    }
}
