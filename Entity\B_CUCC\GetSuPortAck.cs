﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class GetSuPortAck : BMessage
    {

        public List<TPortInfo> TPortInfoList { get; set; }

        public EnumResult Result { get; set; }

        #region SiteWeb配置
        public int MyStationId { get; set; }

        #endregion

        public GetSuPortAck():base()
        {
            MessageType = (int)BMessageType.GET_SUPORT_ACK;
        }

        public GetSuPortAck(string suids, string surids, List<TPortInfo> tportInfoList, EnumResult result) : base()
        {
            MessageType = (int)BMessageType.GET_SUPORT_ACK;
            SUId = suids;
            SURId = surids;
            TPortInfoList = tportInfoList;
            Result = result;
        }

        public static GetSuPortAck Deserialize(XmlDocument xmlDoc)
        {
            GetSuPortAck getSuPortAck = null;
            string suid = string.Empty;
            string surid = string.Empty;
            string strResult = string.Empty;
            try
            {
                suid = xmlDoc.SelectSingleNode("/Response/Info/SUId").InnerText.Trim();
                surid = xmlDoc.SelectSingleNode("/Response/Info/SURId").InnerText.Trim();
                strResult = xmlDoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim();
                XmlNodeList nodes = xmlDoc.SelectNodes("/Response/Info/PortList/TPortInfo");
                List<TPortInfo> tpiList = new List<TPortInfo>();
                foreach(XmlElement node in nodes)
                {
                    string portNo = node.GetAttribute("PortNo");
                    string portName = node.GetAttribute("PortName");
                    string portType = node.GetAttribute("PortType");
                    string settings = node.GetAttribute("Settings");
                    XmlNodeList lists = node.SelectNodes("DeviceList/Device");
                    List<Device> deviceList = new List<Device>();
                    foreach(XmlElement dev in lists)
                    {
                        string id = dev.GetAttribute("Id");
                        string rId = dev.GetAttribute("RId");
                        string address = dev.GetAttribute("Address");
                        string protocol = dev.GetAttribute("Protocol");
                        string version = dev.GetAttribute("Version");
                        string updTime = dev.GetAttribute("UpdateTime");
                        Device device = new Device(id, rId, address, protocol, version, updTime);
                        deviceList.Add(device);
                    }
                    TPortInfo tpi = new TPortInfo(portNo, portName, portType, settings, deviceList);
                    tpiList.Add(tpi);
                }
                EnumResult result = EnumResult.FAILURE;
                if(strResult == "1" || strResult.ToUpper() == "SUCCESS")
                {
                    result = EnumResult.SUCCESS;
                }
                getSuPortAck = new GetSuPortAck(suid, surid, tpiList, result);
                entityLogger.DebugFormat("getSuPortAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                getSuPortAck.StringXML = xmlDoc.InnerXml;
                return getSuPortAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("getSuPortAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("getSuPortAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                getSuPortAck = new GetSuPortAck();
                getSuPortAck.ErrorMsg = ex.Message;
                getSuPortAck.StringXML = xmlDoc.InnerXml;
                return getSuPortAck;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            if (TPortInfoList.Count > 0)
            {
                foreach (TPortInfo tpi in TPortInfoList)
                {
                    sb.AppendFormat("{0},{1},{2},{3},{4},{5},{6}", MessageId, (BMessageType)MessageType, MessageType, SUId, SURId, Result.ToString(), tpi.ToString());
                }
            }
            else
            {
                sb.AppendFormat("{0},{1},{2},{3},{4},{5}, TPortInfoList is null", MessageId, (BMessageType)MessageType, MessageType, SUId, SURId, Result.ToString());
            }
            return sb.ToString();
        }


    }


}
