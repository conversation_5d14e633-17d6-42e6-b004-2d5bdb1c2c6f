﻿using DM.TestOrder.DAL;
using Newtonsoft.Json;
using System.IO;
using Newtonsoft.Json.Linq;
using System.Text;
using System.Data;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System.Web;
using Delivery.API;
using Microsoft.AspNetCore.Mvc;
using Delivery.Common.NoProcedure.Service;

namespace DM.TestOrder.Controllers
{
    public class ExportToExcelController : BaseController
    {
        [HttpPost]

        public FileStreamResult POST([FromBody]JObject value)
        {

            var s = JsonConvert.SerializeObject(value);
            var queryParam = JsonConvert.DeserializeObject<QueryParamTestOrderQuery>(s);

            var dt = WoOrderListDal.Query(queryParam);
            //dt.Columns.Remove("OrderId");
            //dt.Columns.Remove("OrderState");
            //dt.Columns["MyOrderId"].SetOrdinal(0);
            //dt.Columns["MyOrderId"].ColumnName = "割接单号";
            //dt.Columns["OrderTypeString"].SetOrdinal(1);
            //dt.Columns["OrderTypeString"].ColumnName = "割接类型";
            //dt.Columns["FinalGeneralReuslt"].SetOrdinal(2);
            //dt.Columns["FinalGeneralReuslt"].ColumnName = "总体测试结果";
            //dt.Columns["StationGroup"].SetOrdinal(3);
            //dt.Columns["StationGroup"].ColumnName = "分组";
            //dt.Columns["StationName"].SetOrdinal(4);
            //dt.Columns["StationName"].ColumnName = "站址名称";
            //dt.Columns["StationCode"].SetOrdinal(5);
            //dt.Columns["StationCode"].ColumnName = "站址编码";
            //dt.Columns["ApplyUserFsuVendor"].SetOrdinal(6);
            //dt.Columns["ApplyUserFsuVendor"].ColumnName = "FSU厂家";
            //dt.Columns["ApplyTime"].SetOrdinal(7);
            //dt.Columns["ApplyTime"].ColumnName = "申请时间";
            //dt.Columns["SubmitTime"].SetOrdinal(8);
            //dt.Columns["SubmitTime"].ColumnName = "受理时间";
            //dt.Columns["ApproveTime"].SetOrdinal(9);
            //dt.Columns["ApproveTime"].ColumnName = "归档时间";
            //dt.Columns["OrderStateString"].SetOrdinal(10);
            //dt.Columns["OrderStateString"].ColumnName = "申请单状态";
            //dt.Columns["Dealer"].SetOrdinal(11);
            //dt.Columns["Dealer"].ColumnName = "当前处理人";

            string[] cols = new string[]
            {
                "MyOrderId","OrderTypeString","FinalGeneralReuslt","StationGroup","StationName","StationCode",
                "ApplyUserFsuVendor","ApplyTime","SubmitTime","ApproveTime","OrderStateString","Dealer"
            };
            DataTable myDt = dt.DefaultView.ToTable(true, cols);
            myDt.Columns["MyOrderId"].ColumnName = "割接单号";
            myDt.Columns["OrderTypeString"].ColumnName = "割接类型";
            myDt.Columns["FinalGeneralReuslt"].ColumnName = "总体测试结果";
            myDt.Columns["StationGroup"].ColumnName = "分组";
            myDt.Columns["StationName"].ColumnName = "站址名称";
            myDt.Columns["StationCode"].ColumnName = "站址编码";
            myDt.Columns["ApplyUserFsuVendor"].ColumnName = "FSU厂家";
            myDt.Columns["ApplyTime"].ColumnName = "申请时间";
            myDt.Columns["SubmitTime"].ColumnName = "受理时间";
            myDt.Columns["ApproveTime"].ColumnName = "归档时间";
            myDt.Columns["OrderStateString"].ColumnName = "申请单状态";
            myDt.Columns["Dealer"].ColumnName = "当前处理人";

            MemoryStream stream = RenderToExcel(myDt);
            return File(stream, "application/vnd.ms-excel", HttpUtility.UrlEncode("割接单综合查询导出.xls"));
            //HttpResponseMessage result = new HttpResponseMessage(HttpStatusCode.OK);
            //string fileName = "割接单综合查询导出.xls";
            //fileName = HttpUtility.UrlEncode(fileName);
            //result.Content = new StreamContent(stream);
            //result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //result.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment")
            //{
            //    FileName = fileName
            //};
            //return result;
        }

        private static MemoryStream RenderToExcel(DataTable table)
        {
            MemoryStream ms = new MemoryStream();

            using (table)
            {
                IWorkbook workbook = new HSSFWorkbook();
                ISheet sheet = workbook.CreateSheet();
                IRow headerRow = sheet.CreateRow(0);

                // handling header.
                foreach (DataColumn column in table.Columns)
                    headerRow.CreateCell(column.Ordinal).SetCellValue(column.Caption);//If Caption not set, returns the ColumnName value

                // handling value.
                int rowIndex = 1;

                foreach (DataRow row in table.Rows)
                {
                    IRow dataRow = sheet.CreateRow(rowIndex);

                    foreach (DataColumn column in table.Columns)
                    {
                        dataRow.CreateCell(column.Ordinal).SetCellValue(row[column].ToString());
                    }
                    rowIndex++;
                }
                AutoSizeColumns(sheet);

                workbook.Write(ms);
                ms.Flush();
                ms.Position = 0;
            }

            return ms;
        }
        private static void AutoSizeColumns(ISheet sheet)
        {
            if (sheet.PhysicalNumberOfRows > 0)
            {
                IRow headerRow = sheet.GetRow(0);

                for (int i = 0, l = headerRow.LastCellNum; i < l; i++)
                {
                    sheet.AutoSizeColumn(i);
                }
            }
        }
    }
}
