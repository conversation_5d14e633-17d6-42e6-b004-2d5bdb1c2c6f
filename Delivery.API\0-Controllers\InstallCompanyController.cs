﻿using Delivery.API;
using DM.TestOrder.Common.DBA;
using DM.TestOrder.DAL;
using Microsoft.AspNetCore.Mvc;

using System.Diagnostics;


namespace DM.TestOrder.Controllers {
    //[MyRequestAuthorizeAttribute]
    public class InstallCompanyController : BaseController{

        //http://localhost:56324/api/InstallCompany?keyword=1
        [HttpGet]
        public JsonResult Get(string keyword="") {
            keyword = SHelper.RegulateParam(keyword);
                

            Debug.WriteLine(keyword);
            var dt = WoInstallCompanyDal.GetByKeyword(keyword);
            //var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(dt);

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};
            return new JsonResult(dt);

        }
        //public HttpResponseMessage Get() {
        //    var dt = WoInstallCompanyDal.GetAll();
        //    var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(dt);

        //    return new HttpResponseMessage() {
        //        Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
        //    };

        //}

    }
}
