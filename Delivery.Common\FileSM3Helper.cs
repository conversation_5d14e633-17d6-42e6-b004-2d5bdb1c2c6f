﻿using Org.BouncyCastle.Crypto.Digests;
using Org.BouncyCastle.Utilities.Encoders;
using System;
using System.Collections.Generic;
using System.Text;

namespace Delivery.Common
{
    public class FileSM3Helper
    {

        public static string Hash(string dataHex)
        {
            byte[] msg1 = Encoding.Default.GetBytes(dataHex);
            var sm3 = new SM3Digest();
            //HMac mac = new HMac(sm3); // 带密钥的杂凑算法
            //mac.Init(keyParameter);
            sm3.BlockUpdate(msg1, 0, msg1.Length);
            // byte[] result = new byte[sm3.GetMacSize()];
            byte[] result = new byte[sm3.GetDigestSize()];
            sm3.DoFinal(result, 0);
            return Encoding.ASCII.GetString(Hex.Encode(result)).ToUpper();


        }

    }
}
