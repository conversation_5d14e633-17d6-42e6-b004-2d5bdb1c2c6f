﻿
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Odbc;
using System.Linq;
using System.Text;
using Carrier.BDSTool;
using Common.Logging.Pro;


using ENPC.Kolo.Entity.B_CMCC;
using BDSTool.BLL.B;
using BDSTool.Utility;
using BDSTool.DBUtility.Common;
//using Carrier.BDSTool.Entity;
//using Carrier.BDSTool.BLL;


namespace Carrier.BDSTool
{
    //ID转换_历史版本
    public partial class V1Bak_BDSHelperCMCC
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");
        private static void FormatSigNumber(ref string SignalNumber) {
            if (string.IsNullOrEmpty(SignalNumber))
                SignalNumber = "001";
            else
                SignalNumber = int.Parse(SignalNumber).ToString("000");
        }

         #region SiteWebID转换成B接口ID
        /// <summary>
        /// 6-ID转换: SiteWebID -> B接口ID(信号)
        /// </summary>
        /// <param name="stationId"></param>
        /// <param name="equipmentId"></param>
        /// <param name="signalId"></param>
        /// <param name="FSUID">输出:</param>
        /// <param name="DeviceID">输出:</param>
        /// <param name="ID">输出:</param>
        /// <param name="SignalNumber">输出:</param>
        /// <returns></returns>
        public static bool ConvertSignal2BID(int stationId, int equipmentId, int signalId,
                    ref string FSUID, ref string DeviceID, ref string ID, ref string SignalNumber) {

            try {
                if (BDSHelperCMCC.GetFSUDeviceIDByEquipmentId(stationId, equipmentId, ref  FSUID, ref  DeviceID) == false)
                    return false;

                string sql = string.Format("BM_ConvertSignal2BID {0},{1},{2}",
                stationId, equipmentId, signalId);

                //var table = DBHelper.GetTable(sql);
                DataTable table = null;
                try
                {
                    using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                    {
                        dbHelper.CreateProcedureCommand("BM_ConvertSignal2BID");
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("StationId", DbType.Int32, stationId));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("EquipmentId", DbType.Int32, equipmentId));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("SignalId", DbType.Int32, signalId));
                        table = dbHelper.ExecuteDataTable();
                    }
                }
                catch (Exception ex)
                {
                    DBHelper.LogError(sql, ex);
                }

                if (table.Rows.Count == 0) {
                    logger.WarnFormat("无配置;SigId={0},{1}", equipmentId, signalId);
                    return false;
                }

                var row = table.Rows[0];

                if (row["BaseTypeId"] == DBNull.Value) {
                    logger.WarnFormat("无基类;SigId={0},{1}", equipmentId, signalId);
                    return false;
                }
                if (row["StandardDicId"] == DBNull.Value) {
                    var BaseTypeId = row["BaseTypeId"].ToString();
                    logger.WarnFormat("有基类无映射;SigId={0},{1}; BaseTypeId={2}", equipmentId, signalId, BaseTypeId);
                    return false;
                }

                ID = row["ID"].ToString();
                SignalNumber = row["SignalNumber"].ToString();
            }
            catch (Exception ex) {
                logger.ErrorFormat("ConvertSignal2BID();stationId={0};equipmentId={1};SignalId={2}; Error={3}"
                , stationId, equipmentId, signalId, ex.Message);
                logger.ErrorFormat("ConvertSignal2BID();Stack={0}", ex.StackTrace);
                return false;
            }
            return true;
        }


        /// <summary>
        /// 7-ID转换: SiteWebID -> B接口ID(事件)
        /// </summary>
        /// <param name="stationId"></param>
        /// <param name="equipmentId"></param>
        /// <param name="EventId"></param>
        /// <param name="EventConditionId"></param>
        /// <param name="FSUID">输出</param>
        /// <param name="DeviceID">输出</param>
        /// <param name="ID">输出</param>
        /// <param name="SignalNumber">输出:</param>
        /// <returns></returns>
        public static bool ConvertEvent2BID(int stationId, int equipmentId, int EventId, int EventConditionId,
            ref string FSUID, ref string DeviceID, ref string ID, ref string SignalNumber) {
            try {
                if (BDSHelperCMCC.GetFSUDeviceIDByEquipmentId(stationId, equipmentId, ref  FSUID, ref  DeviceID) == false)
                    return false;

                string sql = string.Format("BM_ConvertEvent2BID {0},{1},{2},{3}",
                           stationId, equipmentId, EventId, EventConditionId);

                //var table = DBHelper.GetTable(sql);

                DataTable table = null;
                try
                {
                    using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                    {
                        dbHelper.CreateProcedureCommand("BM_ConvertEvent2BID");
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("StationId", DbType.Int32, stationId));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("EquipmentId", DbType.Int32, equipmentId));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("EventId", DbType.Int32, EventId));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("EventConditionId", DbType.Int32, EventConditionId));
                        table = dbHelper.ExecuteDataTable();
                    }
                }
                catch (Exception ex)
                {
                    DBHelper.LogError(sql, ex);
                }
                if (table.Rows.Count == 0) {
                    logger.WarnFormat("无配置;EventId={0},{1},{2}", equipmentId, EventId, EventConditionId);
                    return false;
                }

                var row = table.Rows[0];

                if (row["BaseTypeId"] == DBNull.Value) {
                    logger.WarnFormat("无基类;EventId={0},{1},{2}", equipmentId, EventId, EventConditionId);
                    return false;
                }
                if (row["StandardDicId"] == DBNull.Value) {
                    var BaseTypeId = row["BaseTypeId"].ToString();
                    logger.WarnFormat("有基类无映射;SigId={0},{1},{2}; BaseTypeId={2}", equipmentId, EventId, EventConditionId, BaseTypeId);
                    return false;
                }

                ID = row["ID"].ToString();
                SignalNumber = row["SignalNumber"].ToString();
            }
            catch (Exception ex) {
                logger.ErrorFormat("ConvertEvent2BID();EventId={0},{1},{2},{3};Error={4}", stationId, equipmentId, EventId, EventConditionId, ex.Message);
                logger.ErrorFormat("ConvertEvent2BID();Stack={0}", ex.StackTrace);
                return false;
            }
            return true;
        }


        /// <summary>
        /// 8-ID转换: SiteWebID -> B接口ID(控制)
        /// </summary>
        /// <param name="stationId"></param>
        /// <param name="equipmentId"></param>
        /// <param name="controlId"></param>
        /// <param name="FSUID">输出</param>
        /// <param name="DeviceID">输出</param>
        /// <param name="ID">输出</param>
        /// <param name="SignalNumber">输出:</param>
        /// <returns></returns>
        public static bool CovnertControl2BID(int stationId, int equipmentId, int controlId,
                    ref string FSUID, ref string DeviceID, ref string ID, ref string SignalNumber) {
            try {
                if (BDSHelperCMCC.GetFSUDeviceIDByEquipmentId(stationId, equipmentId, ref  FSUID, ref  DeviceID) == false)
                    return false;

                string sql = string.Format("BM_CovnertControl2BID {0},{1},{2}",
                stationId, equipmentId, controlId);

                // var table = DBHelper.GetTable(sql);
                DataTable table = null;
                try
                {
                    using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                    {
                        dbHelper.CreateProcedureCommand("BM_CovnertControl2BID");
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("StationId", DbType.Int32, stationId));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("EquipmentId", DbType.Int32, equipmentId));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("ControlId", DbType.Int32, controlId));
                        table = dbHelper.ExecuteDataTable();
                    }
                }
                catch (Exception ex)
                {
                    DBHelper.LogError(sql, ex);
                }
                if (table.Rows.Count == 0) {
                    logger.WarnFormat("无配置;controlId={0},{1}", equipmentId, controlId);
                    return false;
                }

                var row = table.Rows[0];

                if (row["BaseTypeId"] == DBNull.Value) {
                    logger.WarnFormat("无基类;controlId={0},{1}", equipmentId, controlId);
                    return false;
                }
                if (row["StandardDicId"] == DBNull.Value) {
                    var BaseTypeId = row["BaseTypeId"].ToString();
                    logger.WarnFormat("有基类无映射;controlId={0},{1}; BaseTypeId={2}", equipmentId, controlId, BaseTypeId);
                    return false;
                }

                ID = row["ID"].ToString();
                SignalNumber = row["SignalNumber"].ToString();
            }
            catch (Exception ex) {
                logger.ErrorFormat("CovnertControl2BID();stationId={0};equipmentId={1};controlId={2};Error={3}", stationId, equipmentId, controlId, ex.Message);
                logger.ErrorFormat("CovnertControl2BID();Stack={0}", ex.StackTrace);
                return false;
            }
            return true;
        }
        #endregion

        #region B接口ID转换成SiteWebID
        /// <summary>
        /// 9-B接口ID转换成SiteWebID(信号)
        /// </summary>
        /// <param name="FSUID"></param>
        /// <param name="DeviceID"></param>
        /// <param name="ID">信号</param>
        /// <param name="SignalNumber"></param>
        /// <param name="StationId">输出</param>
        /// <param name="EquipmentId">输出</param>
        /// <param name="SignalId">输出</param>
        /// <returns></returns>
        public static bool ConvertBID2Signal(string FSUID, string DeviceID, string ID, string SignalNumber
            , ref int StationId, ref  int EquipmentId, ref  int SignalId) {
            try {
                FormatSigNumber(ref SignalNumber);

                string sql = string.Format("BM_ConvertBID2Signal '{0}','{1}','{2}','{3}'", FSUID, DeviceID, ID, SignalNumber);
                //var table = DBHelper.GetTable(sql);
                DataTable table = null;
                try
                {
                    using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                    {
                        dbHelper.CreateProcedureCommand("BM_ConvertBID2Signal");
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("FSUID", DbType.AnsiString, FSUID));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("DeviceID", DbType.AnsiString, DeviceID));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("ID", DbType.AnsiString, ID));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("SignalNumber", DbType.AnsiString, SignalNumber));
                        table = dbHelper.ExecuteDataTable();
                    }
                }
                catch (Exception ex)
                {
                    DBHelper.LogError(sql, ex);
                }
                if (table.Rows.Count == 0)
                    return false;

                var row = table.Rows[0];
                StationId = int.Parse(row["StationId"].ToString());
                EquipmentId = int.Parse(row["EquipmentId"].ToString());
                SignalId = int.Parse(row["SignalId"].ToString());

            }
            catch (Exception ex) {
                logger.ErrorFormat("ConvertBID2Signal();FSU.DeviceID.ID={0}.{1}.{2};Error={3}", FSUID, DeviceID, ID, ex.Message);
                logger.ErrorFormat("ConvertBID2Signal();Stack={0}", ex.StackTrace);
                return false;
            }

            return true;
        }


        /// <summary>
        ///  10-B接口ID转换成SiteWebID(事件)
        /// </summary>
        /// <param name="FSUID"></param>
        /// <param name="DeviceID"></param>
        /// <param name="ID"></param>
        /// <param name="SignalNumber"></param>
        /// <param name="StationId">输出</param>
        /// <param name="EquipmentId">输出</param>
        /// <param name="EventId">输出</param>
        /// <param name="EventConditionId">输出</param>
        /// <returns></returns>
        public static bool ConvertBID2Event(string FSUID, string DeviceID, string ID, string SignalNumber
            , ref int StationId, ref int EquipmentId, ref int EventId, ref int EventConditionId) {

            try {
                FormatSigNumber(ref SignalNumber);

                string sql = string.Format("BM_ConvertBID2Event '{0}','{1}','{2}','{3}'",
                       FSUID, DeviceID, ID, SignalNumber);

                //var table = DBHelper.GetTable(sql);
                DataTable table = null;
                try
                {
                    using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                    {
                        dbHelper.CreateProcedureCommand("BM_ConvertBID2Event");
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("FSUID", DbType.AnsiString, FSUID));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("DeviceID", DbType.AnsiString, DeviceID));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("ID", DbType.AnsiString, ID));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("SignalNumber", DbType.AnsiString, SignalNumber));
                        table = dbHelper.ExecuteDataTable();
                    }
                }
                catch (Exception ex)
                {
                    DBHelper.LogError(sql, ex);
                }
                if (table.Rows.Count == 0)
                    return false;

                var row = table.Rows[0];
                StationId = int.Parse(row["StationId"].ToString());
                EquipmentId = int.Parse(row["EquipmentId"].ToString());
                EventId = int.Parse(row["EventId"].ToString());
                EventConditionId = int.Parse(row["EventConditionId"].ToString());
            }
            catch (Exception ex) {
                logger.ErrorFormat("ConvertBID2Event();FSU.DeviceID.ID={0}.{1}.{2};Error={3}", FSUID, DeviceID, ID, ex.Message);
                logger.ErrorFormat("ConvertBID2Event();Stack={0}", ex.StackTrace);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 11-B接口ID转换成SiteWebID(控制)
        /// </summary>
        /// <param name="FSUID"></param>
        /// <param name="DeviceID"></param>
        /// <param name="ID"></param>
        /// <param name="SignalNumber"></param>
        /// <param name="StationId">输出</param>
        /// <param name="EquipmentId">输出</param>
        /// <param name="ControlId">输出</param>
        /// <returns></returns>
        public static bool CovnertBID2Control(string FSUID, string DeviceID, string ID, string SignalNumber
            , ref int StationId, ref int EquipmentId, ref  int ControlId) {
            try {
                FormatSigNumber(ref SignalNumber);

                string sql = string.Format("BM_ConvertBID2Control '{0}','{1}','{2}','{3}'",
                FSUID, DeviceID, ID, SignalNumber);

                //var table = DBHelper.GetTable(sql);
                DataTable table = null;
                try
                {
                    using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                    {
                        dbHelper.CreateProcedureCommand("BM_ConvertBID2Control");
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("FSUID", DbType.AnsiString, FSUID));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("DeviceID", DbType.AnsiString, DeviceID));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("ID", DbType.AnsiString, ID));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("SignalNumber", DbType.AnsiString, SignalNumber));
                        table = dbHelper.ExecuteDataTable();
                    }
                }
                catch (Exception ex)
                {
                    DBHelper.LogError(sql, ex);
                }
                if (table.Rows.Count == 0)
                    return false;

                var row = table.Rows[0];
                StationId = int.Parse(row["StationId"].ToString());
                EquipmentId = int.Parse(row["EquipmentId"].ToString());
                ControlId = int.Parse(row["ControlId"].ToString());
            }
            catch (Exception ex) {
                logger.ErrorFormat("CovnertBID2Control();FSU.DeviceID.ID={0}.{1}.{2};Error={3}", FSUID, DeviceID, ID, ex.Message);
                logger.ErrorFormat("CovnertBID2Control();Stack={0}", ex.StackTrace);
                return false;
            }

            return true;
        }
        #endregion


    }
}
