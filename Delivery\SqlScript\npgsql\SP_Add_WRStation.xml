﻿<?xml version="1.0" encoding="utf-8"?>
<?xml-stylesheet type="text/xsl" href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
		<procedure owner="" name="SP_Add_WRStation_1" grant="">
			<parameters>
				<parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
        SELECT EXISTS (
        SELECT 1 FROM WR_StationManagement WHERE StationName = @StationName) 
        OR EXISTS (
        SELECT 1 FROM TBL_Station WHERE StationName = @StationName
        ) AS StationExists
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Add_WRStation_2" grant="">
			<parameters>
				<parameter name="LogonId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT UserId,UserName FROM TBL_Account
				WHERE LogonId = @LogonId
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Add_WRStation_3" grant="">
			<parameters>
				<parameter name="myCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StationCategory" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="myStatus" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="Address" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="SWUserName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="Province" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="City" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="County" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="Remark" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="ContractNo" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="ProjectName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StructureId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				INSERT INTO WR_StationManagement
				(StructureId,StationCode,StationName,StationCategory,StationStatus,Address,UserId, SWUserName,ApplyTime,Province,City,County,Remark,ContractNo,ProjectName)
				VALUES
				(CAST(@StructureId AS INTEGER),@myCode,@StationName,CAST(@StationCategory AS INTEGER),CAST(@myStatus AS INTEGER), @Address,CAST(@UserId AS INTEGER), @SWUserName,now(),CAST(@Province AS INTEGER),CAST(@City AS INTEGER),CAST(@County AS INTEGER),@Remark,@ContractNo,@ProjectName);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Add_WRStation_4" grant="">
			<parameters>
				<parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT s.WRStationId FROM WR_StationManagement s WHERE s.StationName = @StationName;
				]]>
			</body>
		</procedure>
	</procedures>
</root>
