﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;

namespace Delivery.Common.NoProcedure.Service
{
    public class SPGetWRDeviceService
    {
        private static SPGetWRDeviceService _instance = null;

        public static SPGetWRDeviceService Instance
        {
            get
            {
                if (_instance == null) _instance = new SPGetWRDeviceService();
                return _instance;
            }
        }
        public DataTable GetData(string startTime, string endTime, string WRStationId, string DeviceCategory,
                                        string DeviceSubCategory, string DeviceName, string DeviceCode, string WRFsuId, string LogonId)
        {
            DataTable res;

            int iUserId;

            List<int> FilterUser;

            DbHelper dbHelper = new DbHelper();
            ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);

            Dictionary<string, object> realParams = new Dictionary<string, object>();
            realParams.Add("LogonId", LogonId);
            DataTable temp_1 = execHelper.ExecDataTable("SP_Get_WRDevice_2", realParams);
            realParams.Clear();
            iUserId = Convert.ToInt32(temp_1.Rows[0]["UserId"]);
            realParams.Add("iUserId", iUserId);
            DataTable temp_2 = execHelper.ExecDataTable("SP_Get_WRDevice_3", realParams);
            realParams.Clear();
            if (temp_2.Rows.Count != 0)
            {
                DataTable temp_3 = execHelper.ExecDataTable("SP_Get_WRDevice_4", null);
                realParams.Clear();
                FilterUser = ConvertDataTableToList(temp_3, "UserId");
            }
            else
            {
                realParams.Add("iUserId", iUserId);
                DataTable temp_4 = execHelper.ExecDataTable("SP_Get_WRDevice_5", realParams);
                realParams.Clear();
                FilterUser = ConvertDataTableToList(temp_4, "UserId");
            }
            realParams.Add("StartTime", startTime);
            realParams.Add("EndTime", endTime);
            realParams.Add("WhereTime", true);
            if (!string.IsNullOrWhiteSpace(WRStationId) && WRStationId != "-1")
            {
                realParams.Add("WhereWRSationId", true);
                realParams.Add("WRStationId", int.Parse(WRStationId));
            }

            if (!string.IsNullOrWhiteSpace(DeviceCategory) && DeviceCategory != "-1")
            {
                realParams.Add("WhereDeviceCategory", true);
                realParams.Add("DeviceCategory", DeviceCategory);
            }

            // 检查并构建设备子类别条件
            if (!string.IsNullOrWhiteSpace(DeviceSubCategory) && DeviceSubCategory != "-1")
            {
                realParams.Add("WhereDeviceSubCategory", true);
                realParams.Add("DeviceSubCategory", DeviceSubCategory);
            }

            // 检查并构建设备名称条件
            if (!string.IsNullOrWhiteSpace(DeviceName))
            {
                realParams.Add("WhereDeviceName", true);
                realParams.Add("DeviceName", DeviceName);
            }

            // 检查并构建设备编码条件
            if (!string.IsNullOrWhiteSpace(DeviceCode))
            {
                realParams.Add("WhereDeviceCode", true);
                realParams.Add("DeviceCode", DeviceCode);
            }

            // 检查并构建FSU ID条件
            if (!string.IsNullOrWhiteSpace(WRFsuId) && WRFsuId != "-1")
            {
                realParams.Add("WhereFsuId", true);
                realParams.Add("WRFsuId", WRFsuId);
            }


            res = execHelper.ExecDataTable("SP_Get_WRDevice_1", realParams);
            // 创建一个新的DataTable副本，以避免在遍历时修改集合
            DataTable filteredDataTable = res.Clone();

            foreach (DataRow row in res.Rows)
            {
                int userId = Convert.ToInt32(row["UserId"]); // 确保列名和数据类型匹配
                if (FilterUser.Contains(userId))
                {
                    // 如果UserId在FilterUser列表中，添加该行到新的DataTable
                    filteredDataTable.ImportRow(row);
                }
            }

            return filteredDataTable;



        }


        public static List<int> ConvertDataTableToList(DataTable dataTable, string columnName)
        {
            List<int> list = new List<int>();

            if (dataTable != null && dataTable.Rows.Count > 0)
            {
                foreach (DataRow row in dataTable.Rows)
                {
                    if (row[columnName] != DBNull.Value)
                    {
                        list.Add(Convert.ToInt32(row[columnName]));
                    }
                }
            }

            return list;
        }


    }
}
