﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace testOrder.entity {
    public class Equip{
        public int EquipmentId{get;set;}
        //设备名称
        public string EquipmentName{get;set;}
        //设备类型			
        public string EquipmentCategory{get;set;}
        //设备厂家		
        public string Vendor{get;set;}
        //设备型号			
        public string EquipmentStyle{get;set;}

        public Equip() {

        }

        #region MyRegion
          //设备协议
        public string UriProtocol{get;set;}
        public string UriProtocolOK {
            get {
                if (string.IsNullOrWhiteSpace(UriProtocol))
                    return "";
                else
                    return "已上传";

            }

        }

        //水印照片
        public string UriPhoto{get;set;}
        public string UriPhotoOK {
            get {
                if (string.IsNullOrWhiteSpace(UriPhoto))
                    return "";
                else
                    return "已上传";

            }

        }
      
        #endregion
    }
}
