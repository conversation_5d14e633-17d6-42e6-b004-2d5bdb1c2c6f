﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B_CMCC
{
    public sealed class DeviceSetStorageRuleAck
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string ID { get;  set; }

        public List<string> SuccessList { get; set; }
        public List<string> FailList { get; set; }

        /// <summary>
        /// 写成功列表
        /// </summary>
        public List<TSignalMeasurementId> SuccessList2 { get; set; }

        /// <summary>
        /// 写失败列表
        /// </summary>
        public List<TSignalMeasurementId> FailList2 { get; set; }

        #region SiteWeb配置
        public int MyDeviceId { get; set; }

        #endregion

        public DeviceSetStorageRuleAck(string deviceId, List<TSignalMeasurementId> successList, List<TSignalMeasurementId> failList)
        {
            ID = deviceId;

            SuccessList2 = successList;
            FailList2 = failList;
            SuccessList = new List<string>();
            FailList = new List<string>();
            if (SuccessList2 != null && SuccessList2.Count > 0)
            {               
                foreach (TSignalMeasurementId tsmid in SuccessList2)
                {
                    SuccessList.Add(tsmid.ID + tsmid.SignalNumber);
                }
            }
            if (FailList2 != null && FailList2.Count > 0)
            {
                foreach (TSignalMeasurementId tsmid in FailList2)
                {
                    FailList.Add(tsmid.ID + tsmid.SignalNumber);
                }
            }

        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}", ID);
            sb.AppendLine();

            sb.Append("SuccessList:");
            sb.AppendLine();
            foreach (TSignalMeasurementId item in SuccessList2)
            {
                sb.AppendFormat("{0}", item.ToString());
                sb.AppendLine();
            }

            sb.Append("FailList:");
            sb.AppendLine();
            foreach (TSignalMeasurementId item in FailList2)
            {
                sb.AppendFormat("{0}", item.ToString());
                sb.AppendLine();
            }

            return sb.ToString();
        }

    }
}
