﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 监控点数据应答报文(异步)
    /// </summary>
    public class GetAsynDataPush : BMessage
    {
        public string Sequence { get; set; }

        public List<Device> Values { get; set; }

        #region SiteWeb配置

        [Newtonsoft.Json.JsonIgnore]
        public int MyStationId { get; set; }

        #endregion  

        public GetAsynDataPush() : base()
        {
            MessageType = (int)BMessageType.GET_ASYN_DATA_PUSH;
        }

        public GetAsynDataPush(string fsuId, string sequence, List<Device> values)
            : base()
        {
            MessageType = (int)BMessageType.GET_DATA_ACK;

            FSUID = fsuId;
            Sequence = sequence;
            Values = values;
        }

        public static GetAsynDataPush Deserialize(string json)
        {
            GetAsynDataPush info = null;
            try
            {
                info = JsonConvert.DeserializeObject<GetAsynDataPush>(json);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetAsynDataPush.Deserialize:{0}", ex.Message);
                entityLogger.ErrorFormat("GetAsynDataPush.Deserialize:{0}", ex.StackTrace);
                info = new GetAsynDataPush();
                info.ErrorMsg = ex.Message;
                info.StringXML = json;
            }
            return info;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}, {2}, {3}", MessageId, MessageType, FSUID, Sequence);
            sb.AppendLine();

            if (Values != null)
            {
                foreach (Device device in Values)
                {
                    sb.AppendFormat("{0}\r\n", device.ToString());
                }
            }
            return sb.ToString();
        }
    }
}