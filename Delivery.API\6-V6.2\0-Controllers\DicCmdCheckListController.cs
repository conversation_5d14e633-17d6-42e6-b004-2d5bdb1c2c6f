﻿using BLL;
using Delivery.API;
using DM.TestOrder.DAL;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;

namespace DM.TestOrder.Controllers {

    public class DicCmdCheckListController : BaseController {
        [HttpGet]
        public JsonResult Get() {
            var dt = WoDicCmdCheckListDal.GetAll();
            DataTableColumnMapper.RenameColumns(dt, DicCmdFieldMap);
            return new JsonResult(dt);
            //var sReturn = JsonConvert.SerializeObject(dt);
            //HttpResponseMessage result = new HttpResponseMessage {
            //    Content = new StringContent(sReturn, Encoding.GetEncoding("UTF-8"), "application/json")
            //};
            //return result;
        }

        private static readonly Dictionary<string, string> DicCmdFieldMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "baseequipmentid", "BaseEquipmentId" },
            { "baseequipmentname", "BaseEquipmentName" },
            { "basetypeid", "BaseTypeId" },
            { "basetypename", "BaseTypeName" },
            { "checktype", "CheckType" },
            { "note", "Note" },
            { "checkid", "CheckId" },
            { "ismust", "IsMust" }
        };

        [HttpPost]
        public JsonResult POST([FromBody]JObject value) {
            var CheckId = int.Parse(value["CheckId"].ToString());
            var IsMust = value["IsMust"].ToString(); // 是 或 否

            var rtnMsg = WoDicCmdCheckListDal.UpdateOne(CheckId, IsMust);

            //var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(new {
            //    errormsg = rtnMsg
            //});

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};
            return new JsonResult(new
            {
                errormsg = rtnMsg
            });
        }
    }
}
