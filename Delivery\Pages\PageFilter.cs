﻿using Delivery.Common;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Authentication;
using System.Threading.Tasks;

namespace Delivery.Pages
{
    public class PageFilter : IAsyncPageFilter
    {
        public async Task OnPageHandlerExecutionAsync(PageHandlerExecutingContext context, PageHandlerExecutionDelegate next)
        {
            await next.Invoke();
        }

        public Task OnPageHandlerSelectionAsync(PageHandlerSelectedContext context)
        {
            string path = context.HttpContext.Request.Path;
            try
            {
                byte[] bLogonId;
                byte[] bUserId;
                byte[] bType;
                if (context.HttpContext.Session == null || 
                    !context.HttpContext.Session.TryGetValue("LogonId", out bLogonId) || 
                    !context.HttpContext.Session.TryGetValue("UserId", out bUserId) ||
                    !context.HttpContext.Session.TryGetValue("BInterfaceType", out bType))
                {
                    throw new Exception("Session失效或丢失！");
                }
                string logonId = System.Text.Encoding.UTF8.GetString(bLogonId);
                string userId = System.Text.Encoding.UTF8.GetString(bUserId);
                string type = System.Text.Encoding.UTF8.GetString(bType);
                string userInfo;
                if (context.HttpContext.Request.Cookies == null || !context.HttpContext.Request.Cookies.TryGetValue("userinfo", out userInfo))
                {
                    throw new Exception("Cookie失效或丢失！");
                }
                if (!userInfo.Contains($"LogonId={logonId}"))
                {
                    throw new Exception(string.Format("Session信息与Cookie信息不一致[LogonId]：{0},{1}", logonId, userInfo));
                }
                if (!userInfo.Contains($"UserId={userId}"))
                {
                    throw new Exception(string.Format("Session信息与Cookie信息不一致[UserId]：{0},{1}", userId, userInfo));
                }
                
                if (path.Contains("/CMCC") && type != "1" || path.Contains("/CUCC") && type != "3")
                {
                    throw new Exception("访问路径有误！");
                }
            }
            catch (Exception ex)
            {
                Logger.Log(ex.Message, LogType.Error);
                context.HttpContext.Response.Redirect($"/Index?path={path}");  
            }

            //context.HttpContext.Response.Redirect("/");
            return Task.CompletedTask;
        }
    }
}
