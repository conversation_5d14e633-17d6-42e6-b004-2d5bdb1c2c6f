﻿
using BDSTool.DBUtility;
using BDSTool.Entity.B;
using BDSTool.Entity.S2;
using Common.Logging.Pro;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.Entity.DRHelper
{
    public class TSL_MonitorUnitCMCCExHelper
    {
        public static TSL_MonitorUnitCMCCEx FromDataRow(DataRow row) {
            var fsu = new TSL_MonitorUnitCMCCEx();
            fsu.FSUID = row["FSUID"].ToString();
            //-----------------------------------------------------------------
            fsu.PortId = SHelper.ToIntNullable(row["PortId"]);
            fsu.SamplerUnitId = SHelper.ToIntNullable(row["SamplerUnitId"]);
            //-----------------------------------------------------------------

            fsu.StationId = SHelper.ToInt(row["StationId"]);
            fsu.FSUName = row["FSUName"].ToString();
            fsu.SiteID = row["SiteID"].ToString();
            fsu.RoomID = row["RoomID"].ToString();

            fsu.MonitorUnitId = SHelper.ToInt(row["MonitorUnitId"]);
            fsu.UserName = row["UserName"].ToString();
            fsu.PassWord = row["PassWord"].ToString();
            fsu.FSUIP = row["FSUIP"].ToString();
            fsu.FSUMAC = row["FSUMAC"].ToString();
            fsu.FSUVER = row["FSUVER"].ToString();
            fsu.Result = SHelper.ToIntNullable(row["Result"]);
            fsu.FailureCause = row["FailureCause"].ToString();
            fsu.CPUUsage = SHelper.ToDoubleNullable(row["CPUUsage"]);
            fsu.MEMUsage = SHelper.ToDoubleNullable(row["MEMUsage"]);
            fsu.GetFSUInfoResult = SHelper.ToIntNullable(row["GetFSUInfoResult"]);
            fsu.GetFSUFaliureCause = row["GetFSUFaliureCause"].ToString();
            fsu.GetFSUTime = SHelper.ToDateTimeNullable(row["GetFSUTime"]);
            fsu.FTPUserName = row["FTPUserName"].ToString();
            fsu.FTPPassWord = row["FTPPassWord"].ToString();
            fsu.ExtendField1 = row["ExtendField1"].ToString();
            fsu.ExtendField2 = row["ExtendField2"].ToString();
            return fsu;
        }
    }
}
