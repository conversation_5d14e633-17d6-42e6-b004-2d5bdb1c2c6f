﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class DeviceThreshold
    {
        [Newtonsoft.Json.JsonProperty("DevID")]
        public string DeviceID { get; set; }

        [Newtonsoft.Json.JsonProperty("Threshold")]
        public List<TThreshold> Thresholds { get; set; }

        public DeviceThreshold() { }

        public DeviceThreshold(string devId, List<TThreshold> thresholds)
        {
            DeviceID = devId;
            Thresholds = thresholds;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}:", DeviceID);
            foreach (TThreshold threshold in Thresholds)
            {
                sb.Append("[").Append(threshold.ToString()).Append("]");
            }
            return sb.ToString();
        }

        //public override string ToString()
        //{
        //    return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        //}
    }
}