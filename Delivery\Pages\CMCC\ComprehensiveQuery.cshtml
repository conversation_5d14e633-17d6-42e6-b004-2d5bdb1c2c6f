﻿@page
@model Delivery.Pages.CMCC.ComprehensiveQueryModel
@{
}
@section Scripts {
    <script type="text/javascript" src="~/Scripts/lib/jquery-1.8.0.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="~/Scripts/common/ie8.js"></script>
    <script type="text/javascript" src="~/Scripts/common/common.js"></script>
    <script type="text/javascript" src="~/CMCC/scripts/ComprehensiveQuery.js"></script>
}

@section Styles {
    <style type="text/css">
        * {
            font-size: 12px;
        }

        .labTdStyle {
            text-align: left;
            white-space: nowrap;
            width: 50px;
        }

        .contorlTdStyle {
            text-align: left;
            white-space: nowrap;
            width: 200px;
        }

        #myAccordion1 > div.panel {
            margin-bottom: 16px;
        }

            #myAccordion1 > div.panel.panel-last {
                margin-bottom: 0;
            }

            #myAccordion1 > div.panel > div.panel-header.accordion-header {
                border-top-width: 1px;
            }

            #myAccordion1 > div.panel:first-child > div.panel-header.accordion-header {
                border-top-width: 0;
            }

        .txtCenter {
            text-align: center;
        }

        .textbox.textbox-readonly > input, .textbox.textbox-readonly > textarea {
            background-color: #eaefed;
        }
    </style>
}


<div class="mask">
    <div style="position: relative; top: 50%; left: 50%">
        <div class="loading">
            正在加载，请等待...
        </div>
    </div>
</div>
<div style="min-width: 1000px; width: 100%; overflow: auto;">
    <table style="width: 100%;">
        <tr>
            <td class="labTdStyle">分组</td>
            <td class="contorlTdStyle">
                <input id="cbxStructureId" class="easyui-textbox" style="width: 150px;" />
            </td>
            <td class="labTdStyle">站址名称</td>
            <td class="contorlTdStyle">
                <input id="txtStationNameQuery" class="easyui-textbox" style="width: 150px;" data-options="validType:'maxLength[255]'" />
            </td>
            <td class="labTdStyle">站址编码</td>
            <td class="contorlTdStyle">
                <input id="txtStationCodeQuery" class="easyui-textbox" style="width: 150px;" data-options="validType:'maxLength[255]'" />
            </td>
            <td class="labTdStyle">FSU厂家</td>
            <td class="contorlTdStyle">
                <input id="cbxFsuVendor" class="easyui-combobox" style="width: 150px;" editable="false" />
            </td>

        </tr>
        <tr>
            <td class="labTdStyle">割接类型</td>
            <td class="contorlTdStyle">
                <select id="cbxOrderType" class="easyui-combobox" style="width: 150px;" editable="false">
                    <option value="-1">请选择</option>
                    <option value="1">新装入网</option>
                    <option value="2">维护巡检</option>
                </select>
            </td>
            <td class="labTdStyle">申请开始日期</td>
            <td class="contorlTdStyle">
                <input id="dtbApplyStartTime" class="easyui-datetimebox" data-options="validType:['date[\'yyyy-MM-dd hh:mm:ss\']', 'less[\'#dtbApplyEndTime\']'],onChange:function(){$('#dtbApplyEndTime').datebox('validate')}" style="width: 150px;" /><!---->
            </td>
            <td class="labTdStyle">申请截止日期</td>
            <td class="contorlTdStyle">
                <input id="dtbApplyEndTime" class="easyui-datetimebox" data-options="validType:['date[\'yyyy-MM-dd hh:mm:ss\']','greater[\'#dtbApplyStartTime\']'],onChange:function(){$('#dtbApplyStartTime').datebox('validate')}" style="width: 150px;" />
            </td>
            <td class="labTdStyle">归档开始日期</td>
            <td class="contorlTdStyle">
                <input id="dtbArchivesStartTime" class="easyui-datetimebox" data-options="validType:['date[\'yyyy-MM-dd hh:mm:ss\']', 'less[\'#dtbArchivesEndTime\']'],onChange:function(){$('#dtbArchivesEndTime').datebox('validate')}" style="width: 150px;" />
            </td>

        </tr>

        <tr>
            <td class="labTdStyle">归档截止日期</td>
            <td class="contorlTdStyle">
                <input id="dtbArchivesEndTime" class="easyui-datetimebox" data-options="validType:['date[\'yyyy-MM-dd hh:mm:ss\']','greater[\'#dtbArchivesStartTime\']'],onChange:function(){$('#dtbArchivesStartTime').datebox('validate')}" style="width: 150px;" />
            </td>
            <td>申请单状态</td>
            <td>
                <select id="cbxOrderState" class="easyui-combobox" style="width: 150px;" editable="false">
                    <option value="-1">请选择</option>
                    <option value="1">入网申请</option>
                    <option value="2">专家组审核</option>
                    <option value="3">入网复审</option>
                    <option value="4">归档</option>
                </select>
            </td>
            <td colspan="2"></td>
            <td colspan="4">
                <input type="button" id="btnSeach" value="查询" class="commonButton" style="width: 60px;" />
                <input type="button" id="btnReset" value="重置" class="commonButton" style="width: 60px;" />
                <input type="button" id="btnDelete" value="删除" class="commonButton" style="width: 60px;" />
                <input type="button" id="btnToExcel" value="导出" class="commonButton" style="width: 60px;" />
            </td>
        </tr>
    </table>
    <div>
        <table id="dgComprehensiveQuery"></table>
    </div>
</div>


<div style="display: none;">
    <input type="hidden" id="hid_Cond_StationName" />
    <input type="hidden" id="hid_Cond_StationCode" />
    <input type="hidden" id="hid_Cond_StationGroup" />
    <input type="hidden" id="hid_Cond_FsuVendor" />
    <input type="hidden" id="hid_Cond_OrderType" />
    <input type="hidden" id="hid_Cond_ApplyTime1" />
    <input type="hidden" id="hid_Cond_ApplyTime2" />
    <input type="hidden" id="hid_Cond_ApproveTime1" />
    <input type="hidden" id="hid_Cond_ApproveTime2" />
    <input type="hidden" id="hid_Cond_OrderState" />
</div>
