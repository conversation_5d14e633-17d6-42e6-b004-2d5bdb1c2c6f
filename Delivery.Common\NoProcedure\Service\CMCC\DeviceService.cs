﻿using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace Delivery.Common.NoProcedure.Service.CMCC
{
    public class DeviceService
    {
        private static DeviceService _instance = null;

        public static DeviceService Instance
        {
            get
            {
                if (_instance == null) _instance = new DeviceService();
                return _instance;
            }
        }


        public int SP_Add_WRDevice(string houseId, string fsuId, string deviceCategory, string deviceSubCategory, string sysSerialNo, string deviceName, string remark, string logonId, string portUse, string enableTime, string lifeTime)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);

                Dictionary<string, object> realParams = new Dictionary<string, object>();

                try
                {
                    if (houseId == null || houseId == "" || deviceCategory == null || deviceCategory == ""
                        || deviceSubCategory == null || deviceSubCategory == "" || deviceName == null || deviceName == "")
                        return 0;

                    realParams.Add("WRHouseId", houseId);
                    DataSet resultSql = execHelper.ExecDataSet("SP_Add_WRDevice_FindByHouseId", realParams);
                    if (resultSql != null && resultSql.Tables[0].Rows.Count > 0)
                        return -1;

                    DataTable dt = execHelper.ExecDataTable("SP_Add_WRDevice_GetStationId", realParams);
                    if (dt.Rows.Count > 0)
                    {
                        int WRStationId = (int)dt.Rows[0][0];
                        int SWStationId = (int)dt.Rows[0][1];
                        int SWHouseId = (int)dt.Rows[0][2];

                        realParams.Add("SWStationId", SWStationId);
                        realParams.Add("DeviceName", deviceName);

                        DataSet resultSql1 = execHelper.ExecDataSet("SP_Add_WRDevice_FindByStationIdAndDeviceName", realParams);
                        if (resultSql1 != null && (resultSql1.Tables[0].Rows.Count > 0 || resultSql1.Tables[1].Rows.Count > 0))
                            return -2;

                        if (sysSerialNo == null || sysSerialNo == "")
                            sysSerialNo = "001";


                        string DeviceType = deviceCategory + deviceSubCategory;
                        string DeviceCode = "123";
                        //此处预留，等负责这个存储过程的同事修改后调用
                        DeviceCode = Public_StoredService.Instance.SP_GenerateDeviceCode(WRStationId, DeviceType, sysSerialNo, DeviceCode);
                        if (DeviceCode == null || DeviceCode == "") return -3;

                        realParams.Clear();
                        realParams.Add("LogonId", logonId);
                        DataTable dtUser = execHelper.ExecDataTable("SP_Add_WRDevice_GetUser", realParams);

                        int iUserId = (int)dtUser.Rows[0][0];
                        string SWUserName = dtUser.Rows[0][1].ToString();

                        realParams.Clear();
                        realParams.Add("WRStationId", WRStationId);
                        realParams.Add("WRHouseId", SWHouseId);
                        realParams.Add("WRFsuId", fsuId);
                        realParams.Add("DeviceType", DeviceType);
                        realParams.Add("DeviceCode", DeviceCode);
                        realParams.Add("DeviceName", deviceName);
                        realParams.Add("UserId", iUserId);
                        realParams.Add("SWUserName", SWUserName);
                        realParams.Add("SWStationId", SWStationId);
                        realParams.Add("SWHouseId", SWHouseId);
                        realParams.Add("Remark", remark);
                        realParams.Add("PortUse", portUse);
                        realParams.Add("EnableTime", enableTime);
                        realParams.Add("LifeTime", lifeTime);
                        execHelper.ExecuteNonQuery("SP_Add_WRDevice_Insert", realParams);

                        realParams.Clear();
                        realParams.Add("WRStationId", WRStationId);
                        realParams.Add("DeviceName", deviceName);
                        int WRDeviceId = (int)execHelper.ExecuteScalar("SP_Add_WRDevice_WRDeviceId", realParams);

                        realParams.Clear();
                        realParams.Add("WRStationId", WRStationId);
                        string StationName = execHelper.ExecuteScalar("SP_Add_WRDevice_GetStationName", realParams).ToString();

                        //此处预留，等负责这个存储过程的同事修改后调用
                        Public_StoredService.Instance.SP_WR_OperationRecord(WRStationId, StationName, 10, WRDeviceId, deviceName, "", "", logonId);
                        return 1;
                    }
                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    return 0;
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }

            return 0;
        }
        public DataTable SP_GetDeviceSubCategory(string DeviceCategory)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper,false);
                Dictionary<string,object> realParams = new Dictionary<string,object>();
                try
                {
                    int?iDeviceCategory;
                    if(DeviceCategory ==null )
                    {
                        DeviceCategory = "-1";   
                    }
                    DataTable dt;
                    if(DeviceCategory.Equals("-1"))
                    {
                        dt = executeHelper.ExecDataTable("SP_GetDeviceSubCategory_DataItem",realParams);
                    } else
                    {
                        iDeviceCategory = Convert.ToInt32(DeviceCategory);
                        realParams.Add("iDeviceCategory", iDeviceCategory);
                        dt = executeHelper.ExecDataTable("SP_GetDeviceSubCategory_DataItem2", realParams);
                    }  
                    return dt;
                }
                catch (Exception ex) {
                    Logger.Log(ex);
                    return new DataTable();
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public DataTable SP_GetDeviceCategory()
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    DataTable dt = executeHelper.ExecDataTable("SP_GetDeviceCategory", realParams);
                    return dt;
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("SP_GetDeviceCategory:{0}", ex));
                    return new DataTable();
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

    }
}
