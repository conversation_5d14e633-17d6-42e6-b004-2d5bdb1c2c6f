﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Text;
using OpenGauss;
using OpenGauss.NET;

namespace Delivery.Common.NoProcedure.Utils.DB
{
    public class OpenGuassDatabase : DataAccessObject
    {
        public const string POSTGRESRETURNPARAMETER = "ret";
        public OpenGuassDatabase(string connectString)
        {
            this.ConnectionString = connectString;
        }

        /// <summary>
        /// 新建数据Provider工厂
        /// </summary>
        /// <returns></returns>
        public override DbProviderFactory GetClientFactory()
        {
            return OpenGaussFactory.Instance;
        }

        public override DbParameter GetReturnDbParameter()
        {
            DbParameter dbParameter = null;

            dbParameter = GetDbParameter(POSTGRESRETURNPARAMETER, DbType.Int32, 0, ParameterDirection.ReturnValue);

            return dbParameter;
        }

        public override object GetReturnDbParameterValue()
        {
            var parameterName = string.Empty;
            parameterName = BuildParameterName(POSTGRESRETURNPARAMETER);
            return Command.Parameters[parameterName].Value;
        }

        private static string BuildParameterName(string parameterName)
        {
            // PostgreSQL 参数通常使用 @ 前缀
            return "@" + parameterName;
        }
    }
}
