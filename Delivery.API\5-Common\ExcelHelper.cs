﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using NPOI.XSSF.UserModel;
using NPOI.SS.UserModel;
using System.IO;
using Delivery.Common;


namespace Delivery.API
{
    public class ExcelHelper
    {
        public static byte[] DataTableToExcel(DataTable table, string title = null, string sheetName = "Sheet")
        {
            try
            {
                IWorkbook workbook = new XSSFWorkbook();
                ISheet sheet = workbook.CreateSheet(sheetName);
                int cellsIndex = 0;
                // 标题
                if (!string.IsNullOrEmpty(title))
                {
                    // 填充数据
                    IRow cellsTitle = sheet.CreateRow(0);
                    cellsTitle.CreateCell(0).SetCellValue(title);
                    // 合并单元格
                    sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(0, 1, 0, table.Columns.Count - 1));
                    cellsIndex = 2;
                }
                // 填充表头
                IRow cellsHeader = sheet.CreateRow(cellsIndex);
                for (int i = 0; i < table.Columns.Count; i++)
                {
                    cellsHeader.CreateCell(i).SetCellValue(table.Columns[i].ColumnName);
                }
                // 填充数据
                cellsIndex += 1;
                foreach (DataRow dr in table.Rows)
                {
                    IRow row = sheet.CreateRow(cellsIndex);
                    for (int i = 0; i < table.Columns.Count; i++)
                    {
                        //row.CreateCell(i).SetCellValue(StrHelper.ToString(dr[i]));
                        row.CreateCell(i).SetCellValue(dr[i].ToString());
                    }
                    cellsIndex++;
                }
                byte[] buffer = null;
                using (MemoryStream ms = new MemoryStream())
                {
                    workbook.Write(ms);
                    buffer = ms.GetBuffer();
                    ms.Close();
                }
                return buffer;
            }
            catch (Exception ex)
            {
                Logger.Log(ex);
                return null;
            }
        }
    }
}
