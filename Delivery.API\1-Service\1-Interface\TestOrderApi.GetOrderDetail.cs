﻿

using BLL;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using testorder.dal;
using DM.TestOrder.Model;
using DM.TestOrder.Entity;
using DM.TestOrder.Service.Convert;
using DM.TestOrder.DAL;
using DM.TestOrder.Common;



namespace DM.TestOrder.Service.Interface {
    public partial class TestOrderApi {
        public static TestOrderApi Instance = new TestOrderApi();
        public OrderModel GetOrderModel(int orderId, out string errmsg) {
            errmsg = "OK";

            WO_TestOrder entity = WoTestOrderDal.GetOne(orderId);
            if (entity == null) {
                errmsg = (string.Format("无此ID的工单(id={0})", orderId));
                return null;
            }

            var rtn = WoTestOrderDal.QueryLock(orderId);
            if (rtn != "OK") {
                errmsg = rtn;
                return null;
            }

            rtn = WoTestOrderDal.ValidateConfig(orderId);
            if (rtn != "OK") {
                errmsg = rtn;
                return null;
            }
            //=====================================================================================
            //1-提交
            try {

                var model = new OrderModel();
                model.OrderId = entity.OrderId;
                //model.ApplyOrderId = entity.ApplyOrderId;
                model.OrderState = entity.OrderState;

                //=====================================================================================
                //1-提交-填单
                //model.SectionOrder.InitData(entity);
                model.SectionOrder.PartOrder = Mapper.ToPartOrder(entity);



                //----------------------------------------------------------------------
                //设备检查清单
                model.SectionOrder.PartTest.ItemCheckList = Mapper.ToListOfItemCheck(WoTestOrderEquipItemCheckListDal.GetByOrderId(entity.OrderId));

                //工艺自查清单
                model.SectionOrder.PartArtTest.ItemCheckList = Mapper.ToListOfArtItemCheck(WoTestOrderDal.GetTestOrderArtSelfCheckList(entity.OrderId));

                //----------------------------------------------------------------------
                var secOrder = model.SectionOrder.PartOrder;


                //secOrder.ApplyOrderId = entity.ApplyOrderId;
                var eqCount = entity.EquipItemList.Count;
                var docDevice = entity.EquipItemList.Where(o => !string.IsNullOrWhiteSpace(o.UriProtocol)).Count();
                var docPhoto = entity.EquipItemList.Where(o => !string.IsNullOrWhiteSpace(o.UriImage)).Count();

                var passTestCount = model.SectionOrder.PartTest.ItemCheckList.Where(o => o.IsPass == 1).Count();
                var testCount = model.SectionOrder.PartTest.ItemCheckList.Count();

                secOrder.ScoreDocDevice = string.Format("{0}/{1}", docDevice, eqCount);
                secOrder.ScoreDocPhoto = string.Format("{0}/{1}", docPhoto, eqCount);

                secOrder.ScoreDocTest = string.Format("{0}/{1}", passTestCount, testCount);

                //art-test
                var artPassTestCount = model.SectionOrder.PartArtTest.ItemCheckList.Where(o => o.IsPass == 1).Count();
                var artTestCount = model.SectionOrder.PartArtTest.ItemCheckList.Count();
                secOrder.ScoreDocArt = string.Format("{0}/{1}", artPassTestCount, artTestCount);

                //if (docDevice == eqCount && docPhoto == eqCount && passTestCount == testCount)
                //    secOrder.SubmitPassReuslt = "全部通过";
                //else if (docDevice + docPhoto + passTestCount > 0)
                //    secOrder.SubmitPassReuslt = "部分通过";
                //else
                //    secOrder.SubmitPassReuslt = "未通过";
                //----------------------------------------------------------------------

                //=====================================================================================
                //2-专家组审批

                SubLoadDataOfExpertApprove(model, entity);
                //=====================================================================================
                //3-终审
                model.SectionFinalApprove.OrderId = entity.OrderId;

                model.SectionFinalApprove.StateSetUserId = entity.FinalUserId;
                if (entity.FinalUserId != 0) {
                    model.SectionFinalApprove.StateSetUserName = Account.GetUserNameByUserId(entity.FinalUserId);
                }
                model.SectionFinalApprove.FinalGeneralReuslt = entity.FinalGeneralReuslt;
                model.SectionFinalApprove.FinalDecision = entity.FinalDecision;
                model.SectionFinalApprove.FinalNote = entity.FinalNote;
                model.SectionFinalApprove.FinalIsApprove = entity.FinalIsApprove;



                model.SectionFlow.FlowList = Mapper.ToListOfTestOrderFlowInfo(WoTestOrderFlowDal.GetByOrderId(entity.OrderId));
                return model;
            }
            catch (Exception ex) {
                MyLogger.ErrorFormat("TestOrderApi.GetOrderModel();error={0}", ex.Message);
                MyLogger.Error(ex.StackTrace);
                throw ex;
            }

        }

        private void SubLoadDataOfExpertApprove(OrderModel model, WO_TestOrder entityOrder) {
            var secExpert = model.SectionExpertApprove;

            secExpert.OrderId = model.OrderId;

            secExpert.StateSetUserId = entityOrder.ExpertUserId;
            if (entityOrder.ExpertUserId != 0) {
                secExpert.StateSetUserName = Account.GetUserNameByUserId(entityOrder.ExpertUserId);
            }
            secExpert.ExpertDecision = entityOrder.ExpertDecision;
            secExpert.ExpertNote = entityOrder.ExpertNote;
            secExpert.ExpertIsApprove = entityOrder.ExpertIsApprove;

            List<WO_TestOrderExpertCheckList> list = WoTestOrderDal.GetTestOrderExpertCheckList(model.OrderId);
            foreach (var item in list) {
                var newRow = new PassResultItem() {
                    OrderCheckId = item.OrderCheckId,
                    OrderId = item.OrderId,

                    CheckDicId = item.CheckDicId,
                    CheckDicNote = item.CheckDicNote,
                    IsPass = item.IsPass,
                    PassNote = item.PassNote
                };
                secExpert.PassResultList.Add(newRow);
            }
        }

    }
}
