﻿


using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using DM.TestOrder.DAL.Helper;
using Delivery.DAL;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace DM.TestOrder.DAL {
    public class DataItem
    {
        public static string GetEquipmentCategoryName(int CatId) {
            //string sql = string.Format("SELECT ItemValue FROM TBL_DataItem WHERE EntryId=7 and ItemId={0}", CatId);
            //var rtn = DBHelper.ExecuteScalar(sql);

            object obj = null;
            if (CommonUtils.IsNoProcedure)
            {
                obj = Public_ExecuteSqlService.Instance.NoStore_GetEquipmentCategoryName(CatId);
            }
            else
            {
                string sql = "SELECT ItemValue FROM TBL_DataItem WHERE EntryId=7 and ItemId=@CatId";
                obj = new ExecuteSql().ExecuteSQLScalar(sql, new QueryParameter[] {
                new QueryParameter("CatId", DataType.Int, CatId.ToString()),
                });
            }

            return obj.ToString();
        }

        //public static List<TBL_DataItem> GetStationCategoryDataSource() {

        //        retur null;//n s2.TBL_DataItem.Where(o => o.EntryId == 71).ToList();
        //}

        public static string GetStationCategoryName(int CatId) {
            //string sql = string.Format("SELECT ItemValue FROM TBL_DataItem WHERE EntryId=71 and ItemId={0}", CatId);
            //var rtn = DBHelper.ExecuteScalar(sql);
            //return rtn;
            object obj = null;
            if (CommonUtils.IsNoProcedure)
            {
                obj = Public_ExecuteSqlService.Instance.NoStore_GetStationCategoryName(CatId);
            }
            else
            {
                string sql = "SELECT ItemValue FROM TBL_DataItem WHERE EntryId=71 and ItemId=@CatId";
                obj = new ExecuteSql().ExecuteSQLScalar(sql, new QueryParameter[] {
                new QueryParameter("CatId", DataType.Int, CatId.ToString()),
                });
            }
            return obj.ToString();
        }

        public static string GetWRStationCategoryName(int stationId) {
            //string sql = string.Format("WO_GetWRStationCategoryName {0}", stationId);
            //var rtn = DBHelper.ExecuteScalar(sql);
            //return rtn;
            if (CommonUtils.IsNoProcedure)
            {
                var obj1 = WoCommonService.Instance.WO_GetWRStationCategoryName(stationId);
                return obj1.ToString();
            }
            var obj = new ExecuteSql().ExecuteStoredProcedureScalar("WO_GetWRStationCategoryName", new QueryParameter[] {
                new QueryParameter("stationId", DataType.Int, stationId.ToString()),
            });
            return obj.ToString();
        }
    }
}
