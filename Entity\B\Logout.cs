﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B
{
    /// <summary>
    /// FSU向SC登出请求报文
    /// </summary>
    public sealed class Logout : BMessage
    {
        public Logout(string fsuId)
            : base()
        {
            MessageType = (int)BMessageType.Logout;
            FsuId = fsuId;
        }

        public override string Serialize()
        {
            throw new NotImplementedException();
        }

        public static Login Deserialize(string raw)
        {
            throw new NotImplementedException();
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}: {2}", MessageId, (BMessageType)MessageType, FsuId);
        }
    }
}
