﻿
using Delivery.API;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using Delivery.DAL;
using Microsoft.AspNetCore.Mvc;





namespace DM.TestOrder.Controllers {

    public class CheckItemEventController : BaseController {

        [HttpGet]
        public JsonResult Get(int id) {
            //var sql = string.Format("WO_Debug_CheckItemEvent {0}", id);
            //var dt = DBHelper.GetData(sql);

            //var sReturn = JsonConvert.SerializeObject(dt);
            //HttpResponseMessage result = new HttpResponseMessage {
            //    Content = new StringContent(sReturn, Encoding.GetEncoding("UTF-8"), "application/json")
            //};
            //return result; 
            if (CommonUtils.IsNoProcedure)
            {
                var tb1 = WoCommonService.Instance.GetCheckItemEvent(id);
                return new JsonResult(tb1);
            }
            var tb = new ExecuteSql().ExecuteStoredProcedure("WO_Debug_CheckItemEvent", new QueryParameter[] {
                 new QueryParameter("OrderCheckId", DataType.Int, id.ToString()) });
            return new JsonResult(tb);

        }
    }
}
