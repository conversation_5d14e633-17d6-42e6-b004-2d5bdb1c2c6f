﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.6.11.	时间同步
    /// </summary>
    public sealed class TimeCheck: BMessage
    {

        public TTime Time { get; private set; }

        public TimeCheck(string fsuId, TTime time):base()
        {
            MessageType = (int)BMessageType.TIME_CHECK;

            FSUID = fsuId;
            Time = time;
        }

        public TimeCheck(string fsuId, DateTime time):base()
        {
            MessageType = (int)BMessageType.TIME_CHECK;

            FSUID = fsuId;
            TTime tTime = new TTime(time);
            Time = tTime;
        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmldoc = CreateXmlDocument();
                AppendPK_Type(xmldoc, "Request", BMessageType.TIME_CHECK.ToString());

                XmlElement xe2 = xmldoc.CreateElement("Info");
                XmlElement xe20 = xmldoc.CreateElement("FSUID");
                xe20.InnerText = FSUID;
                xe2.AppendChild(xe20);
                XmlElement xe21 = xmldoc.CreateElement("Time");

                XmlElement xe211 = xmldoc.CreateElement("Year");
                xe211.InnerText = Time.Year.ToString();
                xe21.AppendChild(xe211);

                XmlElement xe212 = xmldoc.CreateElement("Month");
                xe212.InnerText = Time.Month;
                xe21.AppendChild(xe212);

                XmlElement xe213 = xmldoc.CreateElement("Day");
                xe213.InnerText = Time.Day;
                xe21.AppendChild(xe213);

                XmlElement xe214 = xmldoc.CreateElement("Hour");
                xe214.InnerText = Time.Hour;
                xe21.AppendChild(xe214);

                XmlElement xe215 = xmldoc.CreateElement("Minute");
                xe215.InnerText = Time.Minute;
                xe21.AppendChild(xe215);

                XmlElement xe216 = xmldoc.CreateElement("Second");
                xe216.InnerText = Time.Second;
                xe21.AppendChild(xe216);

                xe2.AppendChild(xe21);
                XmlNode root = xmldoc.SelectSingleNode("Request");
                root.AppendChild(xe2);

                StringXML = xmldoc.InnerXml;
                if (StringXML != string.Empty)
                {
                    entityLogger.DebugFormat("TimeCheck.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                }
                else 
                {
                    entityLogger.Error("TimeCheck.Serialize(),xml is empty ");
                }
                return xmldoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("TimeCheck.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("TimeCheck.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}, {2}:{3} ",
                MessageId, (BMessageType)MessageType, FSUID, Time.ToString());
            return sb.ToString();
        }

    }
}
