﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using DM.TestOrder.Model;
using DM.TestOrder.Service.Interface;
using Delivery.API;
using Microsoft.AspNetCore.Mvc;

namespace DM.TestOrder.Controllers {
        
    public class SectionOrderController : BaseController{
        //http://localhost:34380/api/SectionOrder
        [HttpPost]
        public JsonResult Post([FromBody]JObject value) {
            //return Debug.WriteLine(value.ToString());


            var SectionOrder = Newtonsoft.Json.JsonConvert.DeserializeObject<SectionOrder>(value.ToString());

            var errmsg= TestOrderApi.Instance.SubmitModifyOrder(SectionOrder);

            var rtn = new {
                errormsg = errmsg
            };
            //var json = Newtonsoft.Json.JsonConvert.SerializeObject(rtn);

            //var response = Request.CreateResponse(HttpStatusCode.OK);
            //response.StatusCode = HttpStatusCode.OK;
            //response.Content = new StringContent(json);    // 响应内容
            //return response;
            return new JsonResult(rtn);
        }
    }
}
