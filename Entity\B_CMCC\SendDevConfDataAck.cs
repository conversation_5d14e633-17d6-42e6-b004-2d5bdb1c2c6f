﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 上报监控对象配置数据应答报文
    /// </summary>
    public sealed class SendDevConfDataAck : BMessage
    {
        public EnumResult Result { get; private set; }

        public string FailureCause { get; private set; }

        public SendDevConfDataAck(EnumResult result, string failureCause):base()
        {
            MessageType = (int)BMessageType.SEND_DEV_CONF_DATA_ACK;
            Result = result;
            FailureCause = failureCause;
        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmlDoc = CreateXmlDocument();
                AppendPK_Type(xmlDoc, "Response", BMessageType.SEND_DEV_CONF_DATA_ACK.ToString());

                XmlElement xe2 = xmlDoc.CreateElement("Info");
                XmlElement xe21 = xmlDoc.CreateElement("Result");
                xe21.InnerText = ((int)Result).ToString();
                XmlElement xe22 = xmlDoc.CreateElement("FailureCause");
                xe22.InnerText = FailureCause;

                xe2.AppendChild(xe21);
                xe2.AppendChild(xe22);
                XmlNode root = xmlDoc.SelectSingleNode("Response");
                root.AppendChild(xe2);

                StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("SendDevConfDataAck.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmlDoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SendDevConfDataAck.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SendDevConfDataAck.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}: {2}, {3}", MessageId, (BMessageType)MessageType, Result, FailureCause);
        }
    }
}
