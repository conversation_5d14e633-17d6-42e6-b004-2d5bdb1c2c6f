﻿<?xml version="1.0" encoding="utf-8"?>
<ClassDiagram MajorVersion="1" MinorVersion="1">
  <Class Name="ENPC.Kolo.Entity.B.BMessage" Collapsed="true">
    <Position X="12.75" Y="1.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>B\BMessage.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="ENPC.Kolo.Entity.B.GetData" Collapsed="true">
    <Position X="0.5" Y="3.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAACAAAAAAAAAAAEAAAEABAEACBEAAAAEAAAAAAAAAA=</HashCode>
      <FileName>B\GetData.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="ENPC.Kolo.Entity.B.GetDataAck" Collapsed="true">
    <Position X="2.75" Y="3.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAEAAAEABQAACBEAAAAAAAoAAAAAAA=</HashCode>
      <FileName>B\GetDataAck.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="ENPC.Kolo.Entity.B.GetHisData" Collapsed="true">
    <Position X="9.5" Y="3.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAACAAAAAAAAAAAEAAAEABAEICBkAAAAEAAAAAAAAAA=</HashCode>
      <FileName>B\GetHisData.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="ENPC.Kolo.Entity.B.GetHisDataAck" Collapsed="true">
    <Position X="16.25" Y="3.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAEAAAEABQAACBEAAAAAAAoAAAAAAA=</HashCode>
      <FileName>B\GetHisDataAck.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="ENPC.Kolo.Entity.B.Login" Collapsed="true">
    <Position X="23" Y="3.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAABAAAAAAAAAAAEAAAAABAAACBEAAACEAEAAAAAAAA=</HashCode>
      <FileName>B\Login.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="ENPC.Kolo.Entity.B.LoginAck" Collapsed="true">
    <Position X="5" Y="3.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAEAAAAAAAAACBAAAAAAAAAABAAAAA=</HashCode>
      <FileName>B\LoginAck.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="ENPC.Kolo.Entity.B.Logout" Collapsed="true">
    <Position X="11.75" Y="3.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAEAAAAABAAACBAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>B\Logout.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="ENPC.Kolo.Entity.B.LogoutAck" Collapsed="true">
    <Position X="18.5" Y="3.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAEAAAAAAAAACBAAAAAAAAAABAAAAA=</HashCode>
      <FileName>B\LogoutAck.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="ENPC.Kolo.Entity.B.SendAlarm" Collapsed="true">
    <Position X="25.25" Y="3.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAEAAAAAAAAACBAAAAAAABAAAAAAAA=</HashCode>
      <FileName>B\SendAlarm.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="ENPC.Kolo.Entity.B.SendAlarmAck" Collapsed="true">
    <Position X="7.25" Y="3.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAEAAAAAAQAACBAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>B\SendAlarmAck.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="ENPC.Kolo.Entity.B.SetPoint" Collapsed="true">
    <Position X="14" Y="3.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAEAAAAABAAQCBEAAAAAAAAAAAAAAA=</HashCode>
      <FileName>B\SetPoint.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="ENPC.Kolo.Entity.B.SetPointAck" Collapsed="true">
    <Position X="20.75" Y="3.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAEAAAAABQAQCBEAAAAAAAAAAAAAAA=</HashCode>
      <FileName>B\SetPointAck.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="ENPC.Kolo.Entity.Message" Collapsed="true">
    <Position X="12.75" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAEAAAAAAAAAAAAASAAAAAGAAAIAAAAAAA=</HashCode>
      <FileName>Message.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Enum Name="ENPC.Kolo.Entity.B.BMessageType" Collapsed="true">
    <Position X="27.75" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AACAAAQAEgAAQAAAIAAAAAIABAAAAAIAABAQAAAgACA=</HashCode>
      <FileName>B\BMessageType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="ENPC.Kolo.Entity.MessageFamily" Collapsed="true">
    <Position X="29.5" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAACA=</HashCode>
      <FileName>Message.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Font Name="Microsoft YaHei" Size="9" />
</ClassDiagram>