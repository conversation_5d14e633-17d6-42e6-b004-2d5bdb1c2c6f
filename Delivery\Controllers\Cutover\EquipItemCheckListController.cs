﻿
using DM.TestOrder.DAL;
using DM.TestOrder.Service.Interface;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web.Http;
using testorder.dal;




namespace DM.TestOrder.Controllers {

    public class EquipItemCheckListController : BaseApiController {

        //http://localhost/orderapi/EquipItemCheckList?id=1
        public HttpResponseMessage Get(int id) {
            Debug.WriteLine(id);

            Debug.WriteLine("WO_TestOrderEquipItemCheckListDal refresh -->");
            WoTestOrderEquipItemCheckListDal.Refresh(id);
            Debug.WriteLine("WO_TestOrderEquipItemCheckListDal refresh <--");

            var partTest = TestOrderApi.Instance.GetFormTest(id);
            var json = Newtonsoft.Json.JsonConvert.SerializeObject(partTest);

            return new HttpResponseMessage() {
                Content = new StringContent(json, Encoding.UTF8, "application/json"),
            };


        }
    }
}
