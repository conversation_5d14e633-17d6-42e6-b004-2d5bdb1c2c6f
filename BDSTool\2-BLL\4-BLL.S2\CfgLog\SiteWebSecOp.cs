﻿
using Common.Logging.Pro;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;




using BDSTool.Entity.S2;
using System.Data;
using BDSTool.Entity.DRHelper;
using BDSTool.Entity.B;
using BDSTool.BLL.Convert;
using ENPC.Kolo.Entity.B_CMCC;
using BDSTool.Utility;
using BDSTool.DBUtility.Common;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace BDSTool.BLL.S2
{


    public partial class SiteWebSecOP 
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

       public TypeOfSEC SECType;

        public int StationId {
            get;
            set;
        }
        public int EquipmentTemplateId {
            get;
            set;
        }
        public int SecId {
            get;
            set;
        }

        public EditType EditType {
            get;
            set;
        }

        public object Execute() {
            return true;
        }

        //public static void LogConfigChange(EditType EditType, int StationId, int EquipmentId) {
        //    var op = new EquipmentSecOP();
        //    op.EditType = EditType;
        //    op.StationId = StationId;
        //    op.EquipmentId = EquipmentId;
        //    CfgChangeLog.LogConfigChange(op);
        //}


        //EnumOfOpConfigId.EquipmentSignalOP
        public static void LogConfigChange(EnumOfOpConfigId cfgType, EditType EditType, int EquipmentTemplateId, int Id) {
            string objectId = string.Format("{0}.{1}", EquipmentTemplateId, Id);
            int configId = (int)cfgType;
            int editType = (int)EditType;
            var sql = string.Format("PBL_ConfigChangeLog '{0}',{1},{2}", objectId, configId, editType);
            //DBHelper.ExecuteNonQuery(sql);
            try
            {
                using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                {
                    if (CommonUtils.IsNoProcedure)
                    {
                        PblConfigChangeLogService.Instance.DoExecute(objectId, configId, editType);
                        return;
                    }
                    dbHelper.CreateProcedureCommand("PBL_ConfigChangeLog");
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("ObjectId", DbType.AnsiString, objectId));
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("ConfigId", DbType.Int32, configId));
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("EditType", DbType.Int32, editType));
                    dbHelper.AddDbParameter(dbHelper.GetReturnDbParameter());
                    dbHelper.ExecuteNoQuery();
                }
            }
            catch (Exception ex)
            {
                DBHelper.LogError(sql, ex);
            }
            //objectId = string.Format("{0}", EquipmentTemplateId);
            //configId = (int)cfgType;
            //editType = (int)EditType.Modified;
            //var sql = string.Format("EXEC PBL_ConfigChangeLog '{0}',{1},{2}", objectId, configId, editType);
            //DBHelper.ExecuteNonQuery(sql);
        }
    }
}
