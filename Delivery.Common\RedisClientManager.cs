﻿using ServiceStack.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Delivery.Common
{
    public class RedisClientManager
    {
        private static PooledRedisClientManager _defaultManager;
        private static readonly Dictionary<string, PooledRedisClientManager> ManagerDictionary;

        static RedisClientManager()
        {
            if (IsNoSSRedis && IsNoSCRedis)
                return;
            _defaultManager = CreateDefaultManager();
            ManagerDictionary = new Dictionary<string, PooledRedisClientManager>();
        }
        private static string[] SplitString(string strSource, string split)
        {
            return strSource.Split(split.ToArray(), StringSplitOptions.RemoveEmptyEntries);
        }

        static public Boolean IsNoSSRedis
        {
            get
            {
                return String.IsNullOrEmpty(ConfigHelper.GetSection("DefaultRedisServer").Value) &&
                    String.IsNullOrEmpty(ConfigHelper.GetSection("DefaultReadRedisServer").Value) &&
                    String.IsNullOrEmpty(ConfigHelper.GetSection("DefaultWriteRedisServer").Value);
            }
        }

        static public Boolean IsNoSCRedis
        {
            get
            {
                return String.IsNullOrEmpty(ConfigHelper.GetSection("EccRedisUrl").Value);
            }
        }

        private static PooledRedisClientManager CreateDefaultManager()
        {
            string defaultRedisServer = ConfigHelper.GetSection("DefaultRedisServer").Value;
            string defaultReadRedisServer = ConfigHelper.GetSection("DefaultReadRedisServer").Value;
            string defaultWriteRedisServer = ConfigHelper.GetSection("DefaultWriteRedisServer").Value;
            if (string.IsNullOrEmpty(defaultReadRedisServer))
            {
                defaultReadRedisServer = defaultRedisServer;
            }
            if (string.IsNullOrEmpty(defaultWriteRedisServer))
            {
                defaultWriteRedisServer = defaultRedisServer;
            }
            string[] writeServerList = SplitString(defaultWriteRedisServer, ",");
            string[] readServerList = SplitString(defaultReadRedisServer, ",");
            return new PooledRedisClientManager(readServerList, writeServerList,
                new RedisClientManagerConfig()
                {
                    AutoStart = true,
                    MaxWritePoolSize = 100,
                    MaxReadPoolSize = 100
                });
        }

        /// <summary>
        /// 创建redis应用程序池
        /// </summary>
        /// <param name="connectStr"></param>
        /// <returns></returns>
        private static PooledRedisClientManager CreateManager(string connectStr)
        {
            string[] arrStr = connectStr.Split(':');
            string host = arrStr.Length > 0 ? arrStr[0] : "";
            int port = 6379;
            if (arrStr.Length > 1)
            {
                int.TryParse(arrStr[1], out port);
            }

            long db = 0;
            if (arrStr.Length > 2)
            {
                long.TryParse(arrStr[2], out db);
            }

            RedisClientManagerConfig config = new RedisClientManagerConfig { MaxReadPoolSize = 100, MaxWritePoolSize = 100, AutoStart = true };
            return new PooledRedisClientManager(new[] { host + ":" + port }, new[] { host + ":" + port },
                config, db, null, null);
        }

        public static IRedisClient GetClient(string connectStr = null)
        {
            if (string.IsNullOrEmpty(connectStr))
            {
                if (_defaultManager == null)
                {
                    _defaultManager = CreateDefaultManager();
                }
                return _defaultManager.GetClient();
            }

            connectStr = connectStr.Trim();
            PooledRedisClientManager manager = null;
            if (ManagerDictionary.ContainsKey(connectStr))
            {
                manager = ManagerDictionary[connectStr];
            }
            if (manager == null)
            {
                manager = CreateManager(connectStr);
                ManagerDictionary[connectStr] = manager;
            }
            return manager.GetClient();
        }
        public static void Reset()
        {
            foreach (KeyValuePair<string, PooledRedisClientManager> item in ManagerDictionary)
            {
                item.Value.Dispose();
            }
            ManagerDictionary.Clear();
            if (_defaultManager != null)
            {
                _defaultManager.Dispose();
                _defaultManager = null;
            }
        }
    }
}
