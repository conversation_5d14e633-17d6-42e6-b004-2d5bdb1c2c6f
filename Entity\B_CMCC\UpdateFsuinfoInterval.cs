﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.6.13.	更新FSU状态信息获取周期
    /// </summary>
    public sealed class UpdateFsuinfoInterval:BMessage
    {
        public Int16 Interval { get; private set; }

        public UpdateFsuinfoInterval(string fsuId, Int16 interval):base()
        {
            MessageType = (int)BMessageType.UPDATE_FSUINFO_INTERVAL;

            FSUID = fsuId;
            Interval = interval;
        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmldoc = CreateXmlDocument();
                AppendPK_Type(xmldoc, "Request", BMessageType.UPDATE_FSUINFO_INTERVAL.ToString());

                XmlElement xe2 = xmldoc.CreateElement("Info");
                XmlElement xe21 = xmldoc.CreateElement("FSUID");
                xe21.InnerText = FSUID;
                xe2.AppendChild(xe21);

                XmlElement xe22 = xmldoc.CreateElement("Interval");
                xe22.InnerText = Interval.ToString();
                xe2.AppendChild(xe22);

                XmlNode root = xmldoc.SelectSingleNode("Request");
                root.AppendChild(xe2);

                StringXML = xmldoc.InnerXml;
                entityLogger.DebugFormat("UpdateFsuinfoInterval.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmldoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("UpdateFsuinfoInterval.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("UpdateFsuinfoInterval.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            string str = String.Format("{0}, {1}, {2}:{3} ",
                MessageId, (BMessageType)MessageType, FSUID, Interval);
            sb.Append(str);

            return sb.ToString();
        }
    }
}
