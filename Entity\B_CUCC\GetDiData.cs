﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class GetDiData : BMessage
    {
        public List<Device> LDevice { get; set; }
        public GetDiData(string suids, string surids, List<Device> lDeviceInfo) : base()
        {
            MessageType = (int)BMessageType.GET_DIDATA;
            SUId = suids;
            SURId = surids;
            LDevice = lDeviceInfo;
        }

        public GetDiData(string suids, string surids, List<Device> lDeviceInfo, string strDeviceList)
            : base()
        {
            MessageType = (int)BMessageType.GET_DIDATA;
            SUId = suids;
            SURId = surids;
            LDevice = lDeviceInfo;
            if ((lDeviceInfo == null || lDeviceInfo.Count == 0) && !string.IsNullOrEmpty(strDeviceList))
            {
                string[] strDeviceIds = strDeviceList.Split('|');
                if (strDeviceIds != null && strDeviceIds.Length > 0)
                {
                    List<Device> deviceList = new List<Device>();
                    foreach (string deviceId in strDeviceIds)
                    {
                        string Id = deviceId.Substring(0, deviceId.IndexOf(','));
                        string RId = deviceId.Substring(deviceId.IndexOf(',') + 1);
                        Device device = new Device(Id, RId);
                        deviceList.Add(device);
                    }
                    LDevice = deviceList;
                }
            }
        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmlDoc = CreateXmlDocument();
                AppendPK_Type(xmlDoc, "Request", BMessageType.GET_DIDATA.ToString(), MessageType.ToString());
                XmlElement xel = xmlDoc.CreateElement("Info");
                XmlElement xelone = xmlDoc.CreateElement("SUId");
                XmlElement xeltwo = xmlDoc.CreateElement("SURId");
                XmlElement xelthree = xmlDoc.CreateElement("DeviceList");
                if (LDevice.Count > 0)
                {
                    foreach (Device di in LDevice)
                    {
                        XmlElement xelfour = xmlDoc.CreateElement("Device");
                        xelfour.SetAttribute("Id", di.Id);
                        xelfour.SetAttribute("RId", di.RId);
                        if (di.SignalList != null && di.SignalList.Count > 0)
                        {
                            foreach(Signal signal in di.SignalList)
                            {
                                //XmlElement xelfive = xmlDoc.CreateElement("Id");
                                //xelfive.InnerText = signal.Id;
                                XmlElement xelfive = xmlDoc.CreateElement("Signal");
                                xelfive.SetAttribute("Id", signal.Id);
                                xelfour.AppendChild(xelfive);
                            }
                        }
                        //else
                        //{
                        //    XmlElement xelfive = xmlDoc.CreateElement("Id");
                        //    xelfive.InnerText = "";
                        //    xelfour.AppendChild(xelfive);
                        //}
                        xelthree.AppendChild(xelfour);
                    }
                }
                //else
                //{
                //    XmlElement xelfour = xmlDoc.CreateElement("Device");
                //    xelfour.SetAttribute("Id", "");
                //    xelfour.SetAttribute("RId", "");
                //    xelthree.AppendChild(xelfour);
                //}
                xelone.InnerText = SUId;
                xeltwo.InnerText = SURId;
                xel.AppendChild(xelone);
                xel.AppendChild(xeltwo);
                xel.AppendChild(xelthree);
                XmlNode node = xmlDoc.SelectSingleNode("Request");
                node.AppendChild(xel);
                StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("GetDiData.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmlDoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetDiData.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetDiData.Serialize() Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            if (LDevice.Count > 0)
            {
                foreach (Device de in LDevice)
                {
                    sb.AppendFormat("{0},{1},{2},{3},{4}}\n", MessageId, (BMessageType)MessageType, SUId, SURId, de.ToString());
                }
            }
            else
            {
                sb.AppendFormat("{0},{1},{2},{3},Device is null", MessageId, (BMessageType)MessageType, SUId, SURId);
            }
            return sb.ToString();
        }
    }
}
