﻿
 

using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace DM.TestOrder.Common.DBA
{
    public partial class DBHelper
    {
        public static void ExecuteStoredProcedure(string stroreProcedure, QueryParameter[] queryParameters) {
            try {
                using (var dbHelper = new DatabaseHelper(GetDbConfig())) {
                    dbHelper.CreateProcCommand(stroreProcedure);

                    dbHelper.ExecuteStoredProcedureNoQuery(queryParameters);
                }
            }
            catch (Exception e) {

                throw e;
            }

            loggerSql.Debug("[sql] " + stroreProcedure);
            //logger.Debug("[sql] " + sql);
        }

        public static string ExecuteStoredProcedureForScalar(string stroreProcedure, QueryParameter[] queryParameters) {
            try {
                using (var dbHelper = new DatabaseHelper(GetDbConfig())) {
                    dbHelper.CreateProcCommand(stroreProcedure);

                    return dbHelper.ExecuteStoredProcedureForScalar(queryParameters);
                }
            }
            catch (Exception e) {

                throw e;
            }

            loggerSql.Debug("[sql] " + stroreProcedure);
            //logger.Debug("[sql] " + sql);
        }

    }
}
