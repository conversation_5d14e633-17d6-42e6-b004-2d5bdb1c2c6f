﻿using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace Delivery.Common.NoProcedure.Service
{
    public class SP_GetStructureInfoService
    {
        private static SP_GetStructureInfoService _instance = null;

        public static SP_GetStructureInfoService Instance
        {
            get
            {
                if (_instance == null) _instance = new SP_GetStructureInfoService();
                return _instance;
            }
        }

        public DataTable GetStructureInfo()
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    DataTable dt = execHelper.ExecDataTable("SP_Get_StructureInfo", realParams);
                    return dt;
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("SP_Get_StructureInfo:{0}", ex));
                    return null;
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public DataTable GetNeedApproveResource(string LoginId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    DataTable resultDt;
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("LogonId", LoginId);
                    object userId = DBNull.Value;
                    DataTable rt = execHelper.ExecDataTable("SP_Get_StructureUserId", realParams);
                    if(rt != null && rt.Rows.Count > 0)
                    {
                        userId = CommonUtils.GetNullableValue(rt.Rows[0].Field<int?>("myStatus"));
                    }
                    realParams.Add("UserId", userId);
                    DataTable dt = execHelper.ExecDataTable("SP_Get_UserRoleMap", realParams);
                    if(dt != null && dt.Rows.Count > 0)
                    {
                        int stationCount = (int)execHelper.ExecuteScalar("SP_Get_StationCount", realParams);
                        int houseCount = (int)execHelper.ExecuteScalar("SP_Get_HouseCount", realParams);
                        int fsuCount = (int)execHelper.ExecuteScalar("SP_Get_FsuCount", realParams);
                        realParams.Add("StationCount", stationCount);
                        realParams.Add("HouseCount", houseCount);
                        realParams.Add("FsuCount", fsuCount);
                        resultDt = execHelper.ExecDataTable("SP_Get_NeedApproveResourceCount", realParams);
                    }
                    else
                    {
                        resultDt = execHelper.ExecDataTable("SP_Get_NeedApproveResourceCount1", realParams);
                    }
                    return resultDt;
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("SP_Get_NeedApproveResource:{0}", ex));
                    return null;
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
        public DataTable GetUserInfo(string logonid)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    DataTable resultDt;
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("LogonId", logonid);
                    DataTable dt = execHelper.ExecDataTable("SP_Get_GetUserInfoAccount", realParams);
                    if(dt != null && dt.Rows.Count > 0)
                    {
                        resultDt = execHelper.ExecDataTable("SP_GetUserInfo", realParams);
                    }
                    else
                    {
                        resultDt = execHelper.ExecDataTable("SP_GetUserInfo1", realParams);
                    }
                    return resultDt;
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("SP_GetUserInfo:{0}", ex));
                    return null;
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public DataSet GetFsuAssociatedDevice(string fsuCode)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    DataSet ds = new DataSet();
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("FsuCode", fsuCode);
                    DataTable dt = execHelper.ExecDataTable("SP_Get_WRFsu_AssociatedDevice", realParams);
                    DataTable dt1 = execHelper.ExecDataTable("SP_Get_WRFsu_AssociatedDevice1", realParams);
                    ds.Tables.Add(dt);
                    ds.Tables.Add(dt1);
                    return ds;
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("SP_Get_WRFsu_AssociatedDevice:{0}", ex));
                    return null;
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public DataTable GetAllFsuFtpInfo(string logonId, string stationName)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("LogonId", logonId);
                    realParams.Add("StationName", stationName);
                    DataTable dt = execHelper.ExecDataTable("SP_Get_AllFsuFtpInfo", realParams);
                    return dt;
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("SP_Get_AllFsuFtpInfo:{0}", ex));
                    return null;
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
    }
}
