//------------------------------------------------------------------------------
// <auto-generated>
//     此代码已从模板生成。
//
//     手动更改此文件可能导致应用程序出现意外的行为。
//     如果重新生成代码，将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------

namespace DM.TestOrder.Entity
{
    using System;
    using System.Collections.Generic;
    
    public partial class WO_TestOrder
    {
        public int OrderId { get; set; }
        public string MyOrderId { get; set; }
        public int OrderType { get; set; }
        public int StationId { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public string EquipItems { get; set; }
        public string InstallCompany { get; set; }
        public string InstallClerk { get; set; }
      	public int InstallCompanyId { get; set; }
	    public int InstallClerkId    { get; set; }

        public int ApplyUserId { get; set; }
        public string ApplyUserName { get; set; }
        public string ApplyUserFsuVendor { get; set; }
        
        public System.DateTime ApplyTime { get; set; }
        public System.DateTime StateChangeTime { get; set; }
        public int OrderState { get; set; }
        public int StateSetUserId { get; set; }
        public string StateSetUserName { get; set; }
        public Nullable<System.DateTime> SubmitTime { get; set; }

        public Nullable<System.DateTime> ApproveTime { get; set; }
        public int NeedUpload { get; set; }

        public int    ExpertUserId { get; set; }
        public int    ExpertIsApprove { get; set; }
        public string ExpertDecision { get; set; }
        public string ExpertNote { get; set; }

        public int FinalUserId { get; set; }
        public string FinalGeneralReuslt { get; set; }
        public string FinalDecision { get; set; }
        public string FinalNote { get; set; }
        public int    FinalIsApprove { get; set; }
    }
}
