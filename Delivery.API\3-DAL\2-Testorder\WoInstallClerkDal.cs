﻿
using System.Data;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using Delivery.DAL;

namespace DM.TestOrder.DAL {
    public class WoInstallClerkDal
    {
        public static string AddOneToCompany(int CompanyId, string ClerkName) {
            //var sql = string.Format("WO_InstallClerk_AddToCompany {0},{1}", SHelper.GetPara(CompanyId), SHelper.GetPara(ClerkName));
            //var rtn = DBHelper.ExecuteScalar(sql);
            if (CommonUtils.IsNoProcedure)
            {
                var rtn1 = WoCommonService.Instance.WO_InstallClerk_AddToCompany(CompanyId, ClerkName);
                return rtn1 == null ? null : rtn1.ToString();
            }
            var rtn = new ExecuteSql().ExecuteStoredProcedureScalar("WO_InstallClerk_AddToCompany", new QueryParameter[] {
                 new QueryParameter("CompanyId", DataType.Number, CompanyId.ToString()),
                 new QueryParameter("ClerkName", DataType.String, ClerkName)
            });
            return rtn == null ? null : rtn.ToString();

        } 

        //public static string AddOne(string CompanyName, string ClerkName) {
        //    var sql = string.Format("WO_InstallClerk_Add{0},{1}", SHelper.GetPara(CompanyName), SHelper.GetPara(ClerkName));
        //    var rtn = DBHelper.ExecuteScalar(sql);
        //    return rtn;

        //} 

        public static DataTable  GetAll() {
            DataTable tb;
            if (CommonUtils.IsNoProcedure)
            {
                tb = Public_ExecuteSqlService.Instance.NoStore_WOInstallClerk_GetAll();
            }
            else
            {
                var sql = "select * from WO_InstallClerk";
                //var tb = DBHelper.GetTable(sql);
                tb = new ExecuteSql().ExecuteSQL(sql);
            }
            return tb;
        }



        public static DataTable GetByKeywordForOneCompany(int companyId, string kwClerk) {

            if (CommonUtils.IsNoProcedure)
            {
                var tb1 = WoCommonService.Instance.WO_InstallClerk_QueryForOneCompany(companyId,kwClerk);
                return tb1;
            }
            var sql = "";


            if (string.IsNullOrWhiteSpace(kwClerk))
                kwClerk = "";

            //sql = string.Format("WO_InstallClerk_QueryForOneCompany {0},{1}", SHelper.GetPara(companyId), SHelper.GetPara(kwClerk));

            //var tb = DBHelper.GetTable(sql);
            var tb = new ExecuteSql().ExecuteStoredProcedure("WO_InstallClerk_QueryForOneCompany", new QueryParameter[] {
                 new QueryParameter("CompanyId", DataType.Number, companyId.ToString()),
                 new QueryParameter("kwClerk", DataType.String, kwClerk)
            });
            return tb;
        }

        //暂时保留
        public static DataTable GetByKeyword(string kwCompany, string kwClerk) {
            if (CommonUtils.IsNoProcedure)
            {
                var tb1 = WoCommonService.Instance.WO_InstallClerk_QueryEx(kwCompany,kwClerk);
                return tb1;
            }
            var sql = "";
            if (string.IsNullOrWhiteSpace(kwCompany))
                kwCompany = "";

            if (string.IsNullOrWhiteSpace(kwClerk))
                kwClerk = "";

            //sql = string.Format("WO_InstallClerk_QueryEx {0},{1}", SHelper.GetPara(kwCompany), SHelper.GetPara(kwClerk));

            //var tb = DBHelper.GetTable(sql);
            var tb = new ExecuteSql().ExecuteStoredProcedure("WO_InstallClerk_QueryEx", new QueryParameter[] {
                 new QueryParameter("kwCompany", DataType.String, kwCompany),
                 new QueryParameter("kwClerk", DataType.String, kwClerk)
            });
            return tb;
        }

        
    }
}

