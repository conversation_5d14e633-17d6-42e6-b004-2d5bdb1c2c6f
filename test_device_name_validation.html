<!DOCTYPE html>
<html>
<head>
    <title>设备名称校验测试</title>
    <script src="https://code.jquery.com/jquery-1.8.0.min.js"></script>
    <script>
        // 模拟设备厂商列表（使用API返回的格式）
        var deviceVendorList = [
            { ItemValue: '动力源', ItemId: '1' },
            { ItemValue: '华为', ItemId: '2' },
            { ItemValue: '中兴', ItemId: '3' },
            { ItemValue: '艾默生', ItemId: '4' },
            { ItemValue: '江苏亚奥', ItemId: '5' }
        ];

        // 设备名称格式校验函数
        function validateDeviceName(value) {
            if (!value) return { valid: true, message: '' };
            
            // 1. 检查下划线数量（应该有6个下划线）
            var underscoreCount = (value.match(/_/g) || []).length;
            if (underscoreCount !== 6) {
                return {
                    valid: false,
                    message: '设备名称格式错误，必须包含6个下划线，当前包含' + underscoreCount + '个下划线'
                };
            }
            
            // 2. 按下划线分割，应该有7个部分
            var parts = value.split('_');
            if (parts.length !== 7) {
                return {
                    valid: false,
                    message: '设备名称格式错误，应该有7个部分，当前有' + parts.length + '个部分'
                };
            }
            
            // 3. 检查每个部分是否为空
            for (var i = 0; i < parts.length; i++) {
                if (!parts[i] || parts[i].trim() === '') {
                    return {
                        valid: false,
                        message: '设备名称格式错误，第' + (i + 1) + '个部分不能为空'
                    };
                }
            }
            
            // 4. 检查设备厂商（第4个部分，索引为3）是否在预存列表中
            var vendor = parts[3];
            var vendorExists = false;
            for (var j = 0; j < deviceVendorList.length; j++) {
                if (deviceVendorList[j].ItemValue === vendor) {
                    vendorExists = true;
                    break;
                }
            }
            
            if (!vendorExists) {
                return {
                    valid: false,
                    message: '设备厂商输入错误，"' + vendor + '"不在预存的厂商列表中'
                };
            }
            
            return { valid: true, message: '校验通过' };
        }

        // 测试用例
        var testCases = [
            {
                name: '正确格式',
                value: '3F_1#开关电源_1#整流屏_动力源_DKD42_1000A_2011',
                expected: true
            },
            {
                name: '下划线数量不足',
                value: '3F_1#开关电源_1#整流屏_动力源_DKD42_1000A',
                expected: false
            },
            {
                name: '下划线数量过多',
                value: '3F_1#开关电源_1#整流屏_动力源_DKD42_1000A_2011_extra',
                expected: false
            },
            {
                name: '包含空部分',
                value: '3F__1#整流屏_动力源_DKD42_1000A_2011',
                expected: false
            },
            {
                name: '设备厂商不存在',
                value: '3F_1#开关电源_1#整流屏_未知厂商_DKD42_1000A_2011',
                expected: false
            },
            {
                name: '另一个正确格式',
                value: '2F_UPS系统_1#UPS_华为_UPS5000_10KVA_2020',
                expected: true
            }
        ];

        function runTests() {
            var resultsDiv = document.getElementById('results');
            var html = '<h3>测试结果：</h3>';
            
            testCases.forEach(function(testCase, index) {
                var result = validateDeviceName(testCase.value);
                var passed = result.valid === testCase.expected;
                
                html += '<div style="margin: 10px 0; padding: 10px; border: 1px solid ' + (passed ? 'green' : 'red') + ';">';
                html += '<h4>测试 ' + (index + 1) + ': ' + testCase.name + ' ' + (passed ? '✓' : '✗') + '</h4>';
                html += '<p><strong>输入值:</strong> ' + testCase.value + '</p>';
                html += '<p><strong>期望结果:</strong> ' + (testCase.expected ? '通过' : '失败') + '</p>';
                html += '<p><strong>实际结果:</strong> ' + (result.valid ? '通过' : '失败') + '</p>';
                html += '<p><strong>校验信息:</strong> ' + result.message + '</p>';
                html += '</div>';
            });
            
            resultsDiv.innerHTML = html;
        }

        function testUserInput() {
            var userInput = document.getElementById('userInput').value;
            var result = validateDeviceName(userInput);
            var resultDiv = document.getElementById('userResult');
            
            resultDiv.innerHTML = '<h4>用户输入测试结果:</h4>' +
                '<p><strong>输入值:</strong> ' + userInput + '</p>' +
                '<p><strong>校验结果:</strong> ' + (result.valid ? '通过' : '失败') + '</p>' +
                '<p><strong>校验信息:</strong> ' + result.message + '</p>';
            
            resultDiv.style.color = result.valid ? 'green' : 'red';
        }

        window.onload = function() {
            runTests();
            
            // 显示设备厂商列表
            var vendorDiv = document.getElementById('vendorList');
            var vendorHtml = '<h3>预存的设备厂商列表：</h3><ul>';
            deviceVendorList.forEach(function(vendor) {
                vendorHtml += '<li>' + vendor.ItemValue + '</li>';
            });
            vendorHtml += '</ul>';
            vendorDiv.innerHTML = vendorHtml;
        };
    </script>
</head>
<body>
    <h1>设备名称校验测试</h1>
    <p>设备名称格式要求：楼层_系统名称及编号_设备子类及编号_设备厂商_型号_实配容量_投产年限</p>
    
    <div id="vendorList"></div>
    
    <h2>自定义测试</h2>
    <div>
        <input type="text" id="userInput" placeholder="请输入设备名称进行测试" style="width: 400px; padding: 5px;">
        <button onclick="testUserInput()">测试</button>
    </div>
    <div id="userResult" style="margin: 10px 0;"></div>
    
    <div id="results"></div>
</body>
</html>
