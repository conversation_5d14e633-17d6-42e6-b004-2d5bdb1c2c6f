﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.NBIOT
{ 
    public class BConstant
    {
        public const string All = "9999999999";
        /// <summary>
        /// 用户名长度
        /// </summary>
        public const int USER_LENGTH    = 20;
        /// <summary>
        /// 口令长度
        /// </summary>
        public const int PASSWORD_LEN   = 20;
        /// <summary>
        /// 描述信息长度
        /// </summary>
        public const int DES_LENGTH     = 120;
        /// <summary>
        /// 版本描述的长度
        /// </summary>
        public const int VER_LENGTH     = 20;
        /// <summary>
        /// FSU ID字符串长度
        /// </summary>
        public const int FSUID_LEN      = 20;
        /// <summary>
        /// 网管告警编号
        /// </summary>
        public const int NMALARMID_LEN  = 40;
        /// <summary>
        /// IP串长度
        /// </summary>
        public const int IP_LENGTH      = 15;
        /// <summary>
        /// 设备ID长度
        /// </summary>
        public const int DEVICEID_LEN   = 26;
        /// <summary>
        /// 监控点/站点/机房ID长度
        /// </summary>
        public const int ID_LENGTH      = 12;
        /// <summary>
        /// 告警序号长度
        /// </summary>
        public const int SERIALNO_LEN   = 10;
        /// <summary>
        /// 时间串长度
        /// </summary>
        public const int TIME_LEN       = 19;
        /// <summary>
        /// 设备配置信息长度
        /// </summary>
        public const int DEV_CONF_LEN   = 1000;
        /// <summary>
        /// 告警预留字段
        /// </summary>
        public const int ALARMREMARK_LEN= 60;
        /// <summary>
        /// 名字命名长度
        /// </summary>
        public const int NAME_LENGTH    = 80;
        /// <summary>
        /// 失败原因描述信息长度
        /// </summary>
        public const int FAILURE_CAUSE_LEN = 40;

    }

    /// <summary>
    /// 报文返回结果
    /// </summary>
    public enum EnumResult
    {
        /// <summary>
        /// 失败
        /// </summary>
        FAILURE = 0,        

        /// <summary>
        /// 成功
        /// </summary>
        SUCCESS = 1        
    }

    /// <summary>
    /// 监控系统数据的种类
    /// </summary>
    public enum EnumType
    {
        /// <summary>
        /// 无效类型
        /// </summary>
        INVALID = -1,

        /// <summary>
        /// 局站
        /// </summary>
        //STATION=0,

        /// <summary>
        /// 告警
        /// </summary>
        ALARM = 0,

        /// <summary>
        /// 设备
        /// </summary>
        //DEVICE = 5,

        /// <summary>
        /// 数字输入量（包含多态数字输入量），遥信
        /// </summary>
        DI = 4,

        /// <summary>
        /// 模拟输入量，遥测
        /// </summary>
        AI = 3,

        /// <summary>
        /// 数字输出量，遥控
        /// </summary>
        DO = 1,

        /// <summary>
        /// 模拟输出量，遥调
        /// </summary>
        AO = 2
    }


    /// <summary>
    /// 数据值的状态
    /// </summary>
    public enum EnumState
    {
        /// <summary>
        /// 正常数据
        /// </summary>
        NOALARM = 0,

        /// <summary>
        /// 一级告警
        /// </summary>
        CRITICAL = 1,

        /// <summary>
        /// 二级告警
        /// </summary>
        MAJOR = 2,

        /// <summary>
        /// 三级告警
        /// </summary>
        MINOR = 3,

        /// <summary>
        /// 四级告警
        /// </summary>
        HINT = 4,
           
        /// <summary>
        /// 操作事件
        /// </summary>
        OPEVENT = 5,

        /// <summary>
        /// 无效数据
        /// </summary>
        INVALID = 6
    }

    //20170331版本的协议中，EnumState == 1表示无效数据
    //public enum EnumState
    //{
    //    /// <summary>
    //    /// 正常数据
    //    /// </summary>
    //    NOALARM = 0,

    //    /// <summary>
    //    /// 无效数据
    //    /// </summary>
    //    INVALID = 1
    //}

    /// <summary>
    /// 告警标志
    /// </summary>
    public enum EnumFlag
    {
        /// <summary>
        /// 开始
        /// </summary>
        BEGIN = 0,

        /// <summary>
        /// 结束
        /// </summary>
        END = 1
    }

    public enum EnumLevel
    {
        /// <summary>
        /// 一级告警
        /// </summary>
        CRITICAL = 3,

        /// <summary>
        /// 二级告警
        /// </summary>
        MAJOR = 2,

        /// <summary>
        /// 三级告警
        /// </summary>
        MINOR = 1,

        /// <summary>
        /// 四级告警
        /// </summary>
        HINT = 0,

        /// <summary>
        /// 无效值
        /// </summary>
        INVALID = -1,

    }

}
