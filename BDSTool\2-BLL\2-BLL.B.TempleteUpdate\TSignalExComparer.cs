﻿using BDSTool.Entity.B;
using Common.Logging.Pro;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace BDSTool.Entity.S2
{
    public class TSignalExComparer : IEqualityComparer<TSignalEx>  
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");
         public bool Equals( TSignalEx x,  TSignalEx y)  
        {
            if (Object.ReferenceEquals(x, y))
                return true;
            
             if(x == null || y == null)
                return false;

             if (x.tsig.ID != y.tsig.ID) {
                 if (int.Parse(x.tsig.ID) != int.Parse(y.tsig.ID)) {
                     logger.WarnFormat("不可能.信号级属性变化. ID:{0}!={1};ID={2}", x.tsig.ID, y.tsig.ID, x.tsig.ID);
                     return false;
                 }                    
             }
                 


             if (x.tsig.Type != y.tsig.Type)
                 return false;

             if (x.tsig.SignalName != y.tsig.SignalName) {
                 logger.InfoFormat("信号级属性变化.SignalName:{0}!={1};ID={2}", x.tsig.SignalName, y.tsig.SignalName, x.tsig.ID);
                 return false;
             }


             if (x.tsig.AlarmLevel != y.tsig.AlarmLevel) {
                 //仅对告警有意义
                 if (x.tsig.Type == EnumType.ALARM) {
                     logger.InfoFormat("信号级属性变化.AlarmLevel:{0}!={1};ID={2}", x.tsig.AlarmLevel, y.tsig.AlarmLevel, x.tsig.ID);
                     return false;
                 }
             }
                 

             if (x.tsig.Threshold != y.tsig.Threshold) {
                  if(x.tsig.Type == EnumType.ALARM) {
                     if (x.tsig.Threshold.HasValue && y.tsig.Threshold.HasValue){
                         logger.InfoFormat("信号级属性变化.Threshold:{0}!={1};ID={2}", x.tsig.Threshold, y.tsig.Threshold, x.tsig.ID);
                         return false;
                     }                  
                     else {
                         if (x.tsig.Threshold.HasValue && x.tsig.Threshold != 0) {
                             logger.InfoFormat("信号级属性变化.Threshold:{0}!={1};ID={2}", x.tsig.Threshold, y.tsig.Threshold, x.tsig.ID);
                             return false;
                         }
                         if (y.tsig.Threshold.HasValue && y.tsig.Threshold != 0) {
                             logger.InfoFormat("信号级属性变化.Threshold:{0}!={1};ID={2}", x.tsig.Threshold, y.tsig.Threshold, x.tsig.ID);
                             return false;
                         }
                     }
                  }
             }

             if (x.tsig.AbsoluteVal != y.tsig.AbsoluteVal) {
                 if (x.tsig.AbsoluteVal.HasValue && y.tsig.AbsoluteVal.HasValue){
                     logger.InfoFormat("信号级属性变化.AbsoluteVal:{0}!={1};ID={2}", x.tsig.AbsoluteVal, y.tsig.AbsoluteVal, x.tsig.ID);
                     return false;
                 }                     
                 else {
                     if (x.tsig.AbsoluteVal.HasValue && x.tsig.AbsoluteVal != 0) {
                         logger.InfoFormat("信号级属性变化.AbsoluteVal:{0}!={1};ID={2}", x.tsig.AbsoluteVal, y.tsig.AbsoluteVal, x.tsig.ID);
                         return false;
                     }
                     if (y.tsig.AbsoluteVal.HasValue && y.tsig.AbsoluteVal != 0) {
                         logger.InfoFormat("信号级属性变化.AbsoluteVal:{0}!={1};ID={2}", x.tsig.AbsoluteVal, y.tsig.AbsoluteVal, x.tsig.ID);
                         return false;
                     }
                 }

             }
             if (x.tsig.RelativeVal != y.tsig.RelativeVal) {
                 if (x.tsig.RelativeVal.HasValue && y.tsig.RelativeVal.HasValue){
                     logger.InfoFormat("信号级属性变化.RelativeVal:{0}!={1};ID={2}", x.tsig.RelativeVal, y.tsig.RelativeVal, x.tsig.ID);
                     return false;
                 }                     
                 else {
                     if (x.tsig.RelativeVal.HasValue && x.tsig.RelativeVal != 0) {
                         logger.InfoFormat("信号级属性变化.RelativeVal:{0}!={1};ID={2}", x.tsig.RelativeVal, y.tsig.RelativeVal, x.tsig.ID);
                         return false;
                     }
                     if (y.tsig.RelativeVal.HasValue && y.tsig.RelativeVal != 0) {
                         logger.InfoFormat("信号级属性变化.RelativeVal:{0}!={1};ID={2}", x.tsig.RelativeVal, y.tsig.RelativeVal, x.tsig.ID);
                         return false;
                     }
                 }
             }

             if (x.tsig.Describe != y.tsig.Describe) {
                //告警此字段无意义
                 if (x.tsig.Type == EnumType.DO) {
                     if (!string.IsNullOrEmpty(x.tsig.Describe) && !string.IsNullOrEmpty(y.tsig.Describe)) {
                         logger.InfoFormat("信号级属性变化.Describe:{0}!={1};ID={2}", x.tsig.Describe, y.tsig.Describe, x.tsig.ID);
                         return false;
                     }
                     else {
                         if (!string.IsNullOrEmpty(x.tsig.Describe) && x.tsig.Describe != "1&待命") {
                             logger.InfoFormat("信号级属性变化.Describe:{0}!={1};ID={2}", x.tsig.Describe, y.tsig.Describe, x.tsig.ID);
                             return false;
                         }

                         if (!string.IsNullOrEmpty(y.tsig.Describe) && y.tsig.Describe != "1&待命") {
                             logger.InfoFormat("信号级属性变化.Describe:{0}!={1};ID={2}", x.tsig.Describe, y.tsig.Describe, x.tsig.ID);
                             return false;
                         }
                     }
                 }
                 else if (x.tsig.Type != EnumType.ALARM) {
                    logger.InfoFormat("信号级属性变化.Describe:{0}!={1};ID={2}", x.tsig.Describe, y.tsig.Describe, x.tsig.ID);
                    return false;
                 }

             }

             if (x.tsig.NMAlarmID != y.tsig.NMAlarmID) {
                 logger.InfoFormat("信号级属性变化.NMAlarmID:{0}!={1};ID={2}", x.tsig.NMAlarmID, y.tsig.NMAlarmID, x.tsig.ID);
                 return false;
             }
                 
             if (x.tsig.SignalNumber != y.tsig.SignalNumber) {
                 if (x.tsig.SignalNumber != "NULL" &&  y.tsig.SignalNumber != "NULL") {
                     if (int.Parse(x.tsig.SignalNumber) != int.Parse(y.tsig.SignalNumber)) {
                         logger.InfoFormat("信号级属性变化.SignalNumber:{0}!={1};ID={2}", x.tsig.SignalNumber, y.tsig.SignalNumber, x.tsig.ID);
                         return false;
                     }
                         
                 }
                 else {
                     if (x.tsig.SignalNumber != "NULL" && int.Parse(x.tsig.SignalNumber) != 0) {
                         logger.InfoFormat("信号级属性变化.SignalNumber:{0}!={1};ID={2}", x.tsig.SignalNumber, y.tsig.SignalNumber, x.tsig.ID);
                         return false;
                     }

                     if (y.tsig.SignalNumber != "NULL" && int.Parse(y.tsig.SignalNumber) != 0) {
                         logger.InfoFormat("信号级属性变化.SignalNumber:{0}!={1};ID={2}", x.tsig.SignalNumber, y.tsig.SignalNumber, x.tsig.ID);
                         return false;
                     }
                 }
             }
                 

             return true;

             //return    (x.tsig.Type == y.tsig.Type &&
             //x.tsig.ID == y.tsig.ID &&
             //x.tsig.SignalName == y.tsig.SignalName &&
             //x.tsig.AlarmLevel == y.tsig.AlarmLevel &&
             //x.tsig.Threshold == y.tsig.Threshold &&
             //x.tsig.AbsoluteVal == y.tsig.AbsoluteVal &&
             //x.tsig.RelativeVal == y.tsig.RelativeVal &&
             //x.tsig.Describe == y.tsig.Describe &&
             //x.tsig.NMAlarmID == y.tsig.NMAlarmID &&
             //x.tsig.SignalNumber == y.tsig.SignalNumber);

         }
         public int GetHashCode(TSignalEx s)  
        {
             return s.TSignalId;//还需要逐个比较
            //int rtn = 0;
            ////if (s.Type != null)
            ////    rtn ^= s.Type.GetHashCode();

            //if (s.ID != null)
            //    rtn ^= s.ID.GetHashCode();

            ////if (s.AlarmLevel != null)
            ////    rtn ^= s.AlarmLevel.GetHashCode();

            //if (s.SignalName != null)
            //    rtn ^= s.SignalName.GetHashCode();


            //if (s.Threshold.HasValue)
            //    rtn ^= s.Threshold.GetHashCode();
            //if (s.Threshold.HasValue)
            //    rtn ^= s.AbsoluteVal.GetHashCode();
            //if (s.Threshold.HasValue)
            //    rtn ^= s.RelativeVal.GetHashCode();

            //if (s.Describe != null)
            //    rtn ^= s.Describe.GetHashCode();

            //if (s.NMAlarmID != null)
            //    rtn ^= s.NMAlarmID.GetHashCode();

            //if (s.SignalNumber != null)
            //    rtn ^= s.SignalNumber.GetHashCode();

            //return rtn;

        }  
    }
}
