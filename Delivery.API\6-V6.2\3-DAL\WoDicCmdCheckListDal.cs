﻿
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Text;
using testorder.dal;
using DM.TestOrder.DAL.Helper;
using Delivery.DAL;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace DM.TestOrder.DAL {
    public class WoDicCmdCheckListDal
    {
        public static DataTable GetAll() {
            DataTable tb;
            if (CommonUtils.IsNoProcedure)
            {
                tb = Public_ExecuteSqlService.Instance.NoStore_WODicCmdCheckList_GetAll();
            }
            else
            {
                var sql = "SELECT CheckId, BaseEquipmentId, BaseEquipmentName, BaseTypeId, BaseTypeName, CheckType, ifnull(IsMust,'否') IsMust, Note FROM WO_DicCmdCheckList";
                //var tb = DBHelper.GetTable(sql);
                tb = new ExecuteSql().ExecuteSQL(sql);
            }
            return tb;
        }


        public static string UpdateOne(int CheckId, string IsMust) {
            if (CommonUtils.IsNoProcedure)
            {
                var rtn1 = WoCommonService.Instance.WO_DicCmdCheckList_Update(CheckId,IsMust);
                return rtn1 == null ? null : rtn1.ToString();
            }
            //var sql = string.Format("WO_DicCmdCheckList_Update {0},{1}",
            //    CheckId,
            //    SHelper.GetPara(IsMust));

            //var rtn = DBHelper.ExecuteScalar(sql);
            var rtn = new ExecuteSql().ExecuteStoredProcedureScalar("WO_DicCmdCheckList_Update", new QueryParameter[] {
                new QueryParameter("CheckId", DataType.Int, CheckId.ToString()),
                new QueryParameter("IsMust", DataType.String, IsMust)
            });
            return rtn == null ? null : rtn.ToString();
        }          
      }


}

