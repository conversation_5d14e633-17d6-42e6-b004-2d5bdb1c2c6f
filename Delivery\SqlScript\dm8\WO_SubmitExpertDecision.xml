﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <procedures>
    <procedure owner="" name="WO_TestOrderExpertCheckList_Update" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="CheckDicId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="IsPass" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="PassNote" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        update WO_TestOrderExpertCheckList
	      set WO_TestOrderExpertCheckList.IsPass = :IsPass, WO_TestOrderExpertCheckList.PassNote = :PassNote
	      where WO_TestOrderExpertCheckList.OrderId = :OrderId and WO_TestOrderExpertCheckList.CheckDicId = :CheckDicId
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="SubmitFinal_OrderState" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        select OrderState OldOrderState from WO_TestOrder where WO_TestOrder.OrderId = :OrderId
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="SubmitFinal_StateSetUserName" grant="">
      <parameters>
        <parameter name="StateSetUserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        select UserName StateSetUserName from TBL_Account where TBL_Account.UserId = :StateSetUserId
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="SubmitFinal_InsertTestOrderFlow" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="OldOrderState" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="NewOrderState" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StateSetUserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StateSetUserName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FinalDecision" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FinalNote" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FinalIsApprove" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FlowText" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SaveTime" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        INSERT INTO WO_TestOrderFlow (
		    OrderId, OldOrderState, NewOrderState, StateSetUserId, StateSetUserName, Decision, Note, IsApprove, FlowText, SaveTime)
        VALUES (
		    :OrderId, :OldOrderState, :NewOrderState, :StateSetUserId, :StateSetUserName, :FinalDecision, :FinalNote, :FinalIsApprove, :FlowText, :SaveTime);
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="SubmitFinal_UpdateTestOrder" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="NewOrderState" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StateSetUserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StateSetUserName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FinalDecision" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FinalNote" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FinalIsApprove" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FinalGeneralReuslt" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SaveTime" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        update WO_TestOrder set 
	      WO_TestOrder.OrderState = :NewOrderState,
	      WO_TestOrder.StateSetUserId = :StateSetUserId,
	      WO_TestOrder.StateSetUserName = :StateSetUserName,
	      WO_TestOrder.StateChangeTime = :SaveTime,
	      WO_TestOrder.FinalUserId = :StateSetUserId,
	      WO_TestOrder.FinalGeneralReuslt = :FinalGeneralReuslt,		
	      WO_TestOrder.FinalDecision = :FinalDecision,		
	      WO_TestOrder.FinalNote = :FinalNote,			
	      WO_TestOrder.FinalIsApprove = :FinalIsApprove
	      where WO_TestOrder.OrderId = :OrderId
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="SubmitFinal_UpdateOrderApproveTime" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SaveTime" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        update WO_TestOrder set WO_TestOrder.ApproveTime = :SaveTime where WO_TestOrder.OrderId = :OrderId
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="SubmitFinal_InsertTestOrderEquipItem" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SaveTime" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        insert into WO_TestOrderEquipItemCheckListHis 
			  (OrderCheckId, OrderId, CheckType, CheckTypeId, HasConfig, IsPass, PassNote, PassFailReason, EquipmentId, EquipmentName, EquipmentCategoryName, BaseEquipmentId, BaseEquipmentName, BaseTypeId, BaseTypeName, Unit, LimitDown, LimitUp, Note, CheckId, EquipmentLogicClass, LogicClass, StandardName, SignalType, SamplerValue, SamplerTime)
		    select OrderCheckId, OrderId, CheckType, CheckTypeId, HasConfig, IsPass, PassNote, PassFailReason, EquipmentId, EquipmentName, EquipmentCategoryName, BaseEquipmentId, BaseEquipmentName, BaseTypeId, BaseTypeName, Unit, LimitDown, LimitUp, Note, CheckId, EquipmentLogicClass, LogicClass, StandardName, SignalType, SamplerValue, SamplerTime 		
		    from WO_TestOrderEquipItemCheckList where WO_TestOrderEquipItemCheckList.OrderId = :OrderId
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="SubmitFinal_DeleteTestOrderEquipItemt" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        delete from WO_TestOrderEquipItemCheckList where WO_TestOrderEquipItemCheckList.OrderId = :OrderId
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="SubmitFinal_DeleteTestOrderEquipItemtSignal" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        delete from WO_TestOrderEquipItemCheckListSignal where WO_TestOrderEquipItemCheckListSignal.OrderId = :OrderId
      ]]>
      </body>
    </procedure>
  </procedures>
</root>
