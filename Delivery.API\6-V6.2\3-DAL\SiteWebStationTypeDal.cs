﻿

using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using Delivery.DAL;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;


namespace DM.TestOrder.DAL {
    public class SiteWebStationTypeDal
    {
        public static DataTable GetAll() {
            DataTable tb;
            if (CommonUtils.IsNoProcedure) {
                tb = Public_ExecuteSqlService.Instance.NoStore_TBLDataItem_GetAll();
            }
            else
            {
                var sql = "select * from  TBL_DataItem where  EntryId=71";
                //var tb = DBHelper.GetTable(sql);
                tb = new ExecuteSql().ExecuteSQL(sql);
            }
            return tb;
        }
    }
}

