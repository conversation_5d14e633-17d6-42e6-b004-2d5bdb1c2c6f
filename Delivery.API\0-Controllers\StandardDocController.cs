﻿using Delivery.Common;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace Delivery.API._0_Controllers
{
    public class StandardDocController: BaseController
    {
        [HttpGet]
        public string Get(string type)
        {
            try
            {
                string fileName = string.Empty;
                switch (type)
                {
                    case "1":
                        fileName = "StationStandard.txt";
                        break;
                    case "2":
                        fileName = "HouseStandard.txt";
                        break;
                    case "3":
                        fileName = "FsuStandard.txt";
                        break;
                    case "4":
                        fileName = "DeviceStandard.txt";
                        break;
                    default:
                        break;
                }
                if (string.IsNullOrEmpty(fileName))
                {
                    return string.Empty;
                }
                else
                {
                    string content = string.Empty;
                    string filePath = Path.Combine(AppContext.BaseDirectory, "StandardDoc", fileName);
                    if (!System.IO.File.Exists(filePath))
                    {
                        return string.Empty;
                    }
                    using (FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                    {
                        StreamReader sr = new StreamReader(fs);
                        content = sr.ReadToEnd();
                        sr.Close();
                        sr.Dispose();
                    }
                    return content;
                }
            }
            catch (Exception ex)
            {
                Logger.Log(ex);
                return string.Empty;
            }
        }

        [HttpPost]
        public string Post([FromBody] FileContent file)
        {
            try
            {
                if (UserId != "-1")
                    throw new System.Security.Authentication.AuthenticationException("无权限！");
                //Stream inputStream = HttpContext.Request.InputStream;
                //StringBuilder sb = new StringBuilder();
                //int bufferLength = 1024;
                //byte[] buffer = new byte[bufferLength];

                //int count = 0;
                //while ((count = inputStream.Read(buffer, 0, bufferLength)) > 0)
                //{
                //    sb.Append(Encoding.UTF8.GetString(buffer, 0, count));
                //}
                //inputStream.Flush();
                //inputStream.Close();
                //inputStream.Dispose();

                //FileContent fileContent = JsonConvert.DeserializeObject<FileContent>(sb.ToString());


                string directoryPath = Path.Combine(AppContext.BaseDirectory, "StandardDoc");
                if (!Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                }

                string filePath = Path.Combine(directoryPath, file.FileName + ".txt");
                using (FileStream fs = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                {
                    StreamWriter sw = new StreamWriter(fs);
                    sw.Write(file.Content);
                    sw.Flush();
                    sw.Close();
                    sw.Dispose();
                }

                return "保存成功！";
            }
            catch (Exception ex)
            {
                Logger.Log(ex);
                return "保存失败！";
            }

        }

        public class FileContent
        {
            public string FileName { get; set; }
            public string Content { get; set; }
        }
    }
}
