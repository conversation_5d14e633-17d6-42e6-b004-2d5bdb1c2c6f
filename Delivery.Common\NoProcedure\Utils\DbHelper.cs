﻿using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils.DB;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;

namespace Delivery.Common.NoProcedure.Utils
{
    public class DbHelper:IDisposable
    {
        /// <summary>
        /// 数据库连接字符串
        /// </summary>

        private  DataAccessObject dataAccessObject = null;
        public DbHelper()
        {
            LoadDataBase();
        }
        private  void LoadDataBase()
        {
            if (dataAccessObject == null)
            {
                dataAccessObject = CreateDataBase(CommonUtils.DbConnectionString, CommonUtils.DbProviderName);
            }
        }

        public void AddDbParameter(string parameterName, DbType dbType, object parameterValue)
        {
            dataAccessObject.AddDbParameter(parameterName, dbType, parameterValue);
        }

        public void CreateNewCommand()
        {
            dataAccessObject.CreateNewCommand();
        }

        public DbConnection Connection
        {
            get
            {
                return dataAccessObject.Connection;
            }
        }
        public DbCommand Command
        {
            get
            {
                return dataAccessObject.Command;
            }
        }

        public DataSet ExecuteDataSet()
        {
            return dataAccessObject.ExecuteDataSet();
        }

        public IDataReader ExecuteDataReader()
        {
            return dataAccessObject.ExecuteReader();
        }
        public object ExecuteScalar()
        {
            return dataAccessObject.ExecuteScalar();
        }
        public int ExecuteNonQuery()
        {
            return dataAccessObject.ExecuteNoQuery();
        }

        public DataTable ExecuteDataTable()
        {
            return dataAccessObject.ExecuteDataTable();
        }

        public DbCommand CreateTextCommand(string sql)
        {
            return dataAccessObject.CreateTextCommand(sql);
        }

        /// <summary>
        /// 预定义的数据访问对象
        /// </summary>
        /// <param name="connectionString">连接字符串</param>
        /// <param name="providerInvariantName">数据访问组件名称</param>
        private DataAccessObject CreateDataBase(string connectionString, string providerInvariantName)
        {
            if (string.IsNullOrEmpty(connectionString))
                throw new ArgumentNullException("connectionString");

            if (string.IsNullOrEmpty(providerInvariantName))
                throw new ArgumentNullException("providerInvariantName");

            switch (providerInvariantName.ToLower().Trim())
            {
                //case "mssql":
                //   return  new SqlDatabase(connectionString);
                //case "npgsql":
                //    return new PgSqlDatabase(connectionString);
                case "mysql":
                    return new MysqlDatabase(connectionString);
                case "dm8":
                    return new DM8Database(connectionString);
                default:
                    throw new ArgumentOutOfRangeException("providerInvariantName");
            }
        }

        internal void BindingParameters(ArrayList needParams, Dictionary<string, object> realParams)
        {
            foreach (Parameter item in needParams)
            {
                DbType? dbtype = CommonUtils.ConverDbType(item.SqlType);
                if (dbtype.HasValue)
                {
                    object vle;
                    if (realParams.TryGetValue(item.Name, out vle))
                    {//未传入说明此参数在此次运行时不需要
                        AddDbParameter(item.Name, dbtype.Value, vle);
                    }
                } 
                else
                {
                    throw new ArgumentException("unknow DbType:" + item.SqlType);
                }
            }
        }

        public void Dispose()
        {
            if(dataAccessObject != null)
                dataAccessObject.Dispose();
        }
    }
}
