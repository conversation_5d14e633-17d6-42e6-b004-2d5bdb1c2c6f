<?xml version="1.0" encoding="utf-8" ?>
<configuration>
 <configSections>
  <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net"/>
 </configSections>
 
 <log4net>
  <appender name="RollingLogFileAppender" type="log4net.Appender.RollingFileAppender">
   <!--日志路径-->
   <param name= "File" value= "log/"/>
   <!--是否是向文件中追加日志-->
   <param name= "AppendToFile" value= "true"/>
   <!--log保留天数-->
   <param name= "MaxSizeRollBackups" value= "10"/>
   <!--日志文件名是否是固定不变的-->
   <param name= "StaticLogFileName" value= "false"/>
   <!--日志文件名格式为:2008-08-31.log-->
   <param name= "DatePattern" value= "yyyy-MM-dd&quot;.log&quot;"/>
   <!--日志根据日期滚动-->
   <param name= "RollingStyle" value= "Date"/>
   <layout type="log4net.Layout.PatternLayout">
    <param name="ConversionPattern" value="%d [%t] %-5p %c - %m%n %loggername" />
   </layout>
  </appender>
 
  <root>
   <!--(高) OFF > FATAL > ERROR > WARN > INFO > DEBUG > ALL (低) -->
   <level value="ERROR" />
   <appender-ref ref="RollingLogFileAppender"/>
  </root>
 </log4net>
</configuration>