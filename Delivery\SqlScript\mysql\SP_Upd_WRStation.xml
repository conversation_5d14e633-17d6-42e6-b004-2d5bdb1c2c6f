﻿<?xml version="1.0" encoding="utf-8"?>
<?xml-stylesheet type="text/xsl" href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
		<procedure owner="" name="SP_Upd_WRStation_1" grant="">
			<parameters>
				<parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="WRStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT 'X' FROM WR_StationManagement w
				WHERE w.WRStationId != @WRStationId AND w.StationName = @StationName
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Upd_WRStation_2" grant="">
			<parameters>
				<parameter name="WRStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT  s.StationStatus,s.StationCode,s.SWStationId,s.County,s.StationCategory
				FROM WR_StationManagement s WHERE s.WRStationId = @WRStationId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Upd_WRStation_3" grant="">
			<parameters>
				<parameter name="WRStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT concat('StructureId:' , convert(StructureId,CHAR) ,
				'-StationName:' , convert(StationName,CHAR) ,
				'-StationCategory:' , convert(StationCategory,CHAR) ,
				'-Address:' , convert(Address,CHAR) ,
				'-Remark:' , convert(Remark,CHAR) ,
				'-ContractNo:' , convert(ContractNo,CHAR) ,
				'-ProjectName:' , convert(ProjectName,CHAR))
				FROM WR_StationManagement WHERE WR_StationManagement.WRStationId = @WRStationId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Upd_WRStation_4" grant="">
			<parameters>
				<parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="WRStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StructureId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StationCategory" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="Province" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="City" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="County" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="Address" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="Remark" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="ContractNo" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="ProjectName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				UPDATE WR_StationManagement
				SET StationName = @StationName,StructureId = @StructureId,StationCategory = @StationCategory,Province = @Province,City = @City,County = @County,Address = @Address,Remark = @Remark,ContractNo = @ContractNo,ProjectName = @ProjectName
				WHERE WR_StationManagement.WRStationId = @WRStationId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Upd_WRStation_5" grant="">
			<parameters>
				<parameter name="StationCategory" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT d.SiteWeb_ItemId FROM WO_DicStationTypeMap d
				WHERE d.WR_ItemId = @StationCategory;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Upd_WRStation_6" grant="">
			<parameters>
				<parameter name="SWStationCategory" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="SWStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				UPDATE TBL_Station
				SET TBL_Station.StationName = @StationName,TBL_Station.StationCategory = @SWStationCategory
				,TBL_Station.UpdateTime = now()
				WHERE TBL_Station.StationId = @SWStationId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Upd_WRStation_7" grant="">
			<parameters>
				<parameter name="ContractNo" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="SWStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="ProjectName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				UPDATE TBL_StationProjectInfo
				SET TBL_StationProjectInfo.ContractNo = @ContractNo, TBL_StationProjectInfo.ProjectName = @ProjectName
				WHERE TBL_StationProjectInfo.StationId = @SWStationId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Upd_WRStation_8" grant="">
			<parameters>
				<parameter name="WRStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				UPDATE TBL_StationCMCC,WR_StationManagement
				SET TBL_StationCMCC.SiteID = WR_StationManagement.StationCode
				,TBL_StationCMCC.SiteName = WR_StationManagement.StationName
				,TBL_StationCMCC.Description = WR_StationManagement.Remark
				WHERE TBL_StationCMCC.StationId = WR_StationManagement.SWStationId
				AND WR_StationManagement.WRStationId = @WRStationId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Upd_WRStation_9" grant="">
			<parameters>
				<parameter name="SWStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				UPDATE TBL_ConfigChangeMicroLog
				SET TBL_ConfigChangeMicroLog.EditType = 2, TBL_ConfigChangeMicroLog.UpdateTime = now()
				WHERE TBL_ConfigChangeMicroLog.ObjectId = @SWStationId AND TBL_ConfigChangeMicroLog.ConfigId = 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Upd_WRStation_10" grant="">
			<parameters>
				<parameter name="LogonId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT  a.UserId,a.UserName
				FROM TBL_Account a WHERE a.LogonId = @LogonId ;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Upd_WRStation_11" grant="">
			<parameters>
				<parameter name="StructureId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StationCategory" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="Address" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="Remark" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="ContractNo" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="ProjectName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StationCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StationStatus" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="SWStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="County" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="myCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="Province" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="City" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="WRStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				UPDATE WR_StationManagement
				SET WR_StationManagement.StructureId = @StructureId,WR_StationManagement.StationCode = @myCode
				,WR_StationManagement.StationName = @StationName,WR_StationManagement.StationCategory = @StationCategory
				,WR_StationManagement.StationStatus = 1
				,WR_StationManagement.Province = @Province
				,WR_StationManagement.ApplyTime = now(),WR_StationManagement.City = @City
				,WR_StationManagement.County = @County,WR_StationManagement.Address = @Address
				,WR_StationManagement.Remark = @Remark,WR_StationManagement.ContractNo = @ContractNo
				,WR_StationManagement.ProjectName = @ProjectName
				WHERE WR_StationManagement.WRStationId = @WRStationId;
				]]>
			</body>
		</procedure>
	</procedures>
</root>
