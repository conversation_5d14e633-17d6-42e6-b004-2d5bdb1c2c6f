﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    public sealed class TimeCheckAck:BMessage
    {
        public EnumResult Result { get; private set; }

        public string FailureCause { get; private set; }

        public TimeCheckAck(string fsuId, EnumResult result, string failureCause):base()
        {
            MessageType = (int)BMessageType.TIME_CHECK_ACK;

            FSUID = fsuId;
            Result = result;
            FailureCause = failureCause;
        }

        public TimeCheckAck() : base() 
        {
            MessageType = (int)BMessageType.TIME_CHECK_ACK;
        }

        public static TimeCheckAck Deserialize(XmlDocument xmlDoc)
        {
            TimeCheckAck timeChechAck = null;
            try
            {
                string fsuId = xmlDoc.SelectSingleNode("/Response/Info/FSUID").InnerText;
                string strResult = xmlDoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim();
                string failureCause = xmlDoc.SelectSingleNode("/Response/Info/FailureCause").InnerText;
                EnumResult result;
                if (string.IsNullOrEmpty(strResult) || strResult.ToUpper()=="NULL")
                {
                    result = EnumResult.FAILURE;
                }
                else
                {
                    result = (EnumResult)int.Parse(strResult);
                }
                timeChechAck = new TimeCheckAck(fsuId, result, failureCause);
                //timeChechAck = new TimeCheckAck(result, failureCause);
                if (xmlDoc.InnerXml != string.Empty)
                {
                    entityLogger.DebugFormat("TimeCheckAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                }
                else
                {
                    entityLogger.DebugFormat("TimeCheckAck.Deserialize(),xml is empty");
                }
                timeChechAck.StringXML = xmlDoc.InnerXml;
                return timeChechAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("TimeCheckAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("TimeCheckAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                timeChechAck = new TimeCheckAck();
                timeChechAck.ErrorMsg = ex.Message;
                timeChechAck.StringXML = xmlDoc.InnerXml;
                return timeChechAck;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}, {2}, {3}", MessageId, MessageType, Result, FailureCause);
            return sb.ToString();
        }
    }
}
