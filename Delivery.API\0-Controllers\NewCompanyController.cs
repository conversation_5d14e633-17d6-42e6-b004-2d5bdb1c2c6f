﻿using Delivery.API;
using DM.TestOrder.DAL;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;



namespace DM.TestOrder.Controllers {
        
    public class NewCompanyController : BaseController{

        /*
        以Post方式提交 
        url : http://localhost/orderapi/NewCompany
        数据:
        {
            "CompanyName": "新增公司"
        }
        */
        [HttpPost]
        public JsonResult POST([FromBody]JObject value) {
            var CompanyName = value["CompanyName"].ToString();

            var rtnMsg = WoInstallCompanyDal.AddOne(CompanyName);

            var rtn = new {
                errormsg = rtnMsg
            };


            var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(rtn);


            //Debug.WriteLine(value.ToString());
            //Debug.WriteLine(jsonRtn.ToString());

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};
            return new JsonResult(rtn);
        }
    }
}
