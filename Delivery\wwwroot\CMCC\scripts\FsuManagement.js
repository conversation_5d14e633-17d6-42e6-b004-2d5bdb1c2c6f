'use strict';

//global
var cookie = getCookie();
var userRole = getUserRole();

//机房查询
var loadHouseManagementData = function () {
    $('#lookHouseTable').datagrid({
        method: 'GET',
        url: `${HOUSE_HANDLER}/GetHouseInfoCheck`,
        title: '机房信息列表',  //rownumbers: true, //行号  striped: true, //隔行变色                
        idField: 'HouseCode', //主键
        singleSelect: true,
        selectOnCheck: true,
        pagination: true,
        pageSize: 30,
        pageList: [30],
        queryParams: {
            //action: 'GetHouseInfoCheck',
            StationName: $('#looktxtStationNameq').val(),
            HouseName: $('#looktxtHouseNameq').val(),
            StationCode: $('#looktxtStationCodeq').val(),
            HouseCode: '',
            strBegin: '',
            strEnd: '',
            Status: '3'
        },
        width: function () { return document.body.clientWidth * 0.9; }, //设置表格宽度为客户端页面的0.9
        height: 'auto',
        fitColumns: true,
        columns: [[
            {
                field: 'ck', align: 'center', title: '选择 ', width: 9,
                formatter: function (value, row, index) {
                    return '<input type="radio" id="radio' + index + '" radiogroup="lookinfoCK" />';
                }
            },
            { field: 'RowNumber', title: '序号', width: 10, align: 'center' },
            { field: 'StationName', title: '站址名称', width: 15, align: 'left' },
            { field: 'StationCode', title: '站址编码', width: 20, align: 'left' },
            { field: 'HouseName', title: '机房名称', width: 12, align: 'left' },
            { field: 'HouseCode', title: '机房编码', width: 15, align: 'left' }

        ]],
        checkOnSelect: true,
        onClickRow: function (index, row) {
            var currentRadio = 'radio' + index;
            $('input:radio').each(function () {
                $(this).attr('checked', false);
            });
            $('#' + currentRadio).attr('checked', 'checked');
        },
        onDblClickRow: function (index, row) {
            $('#divLookforHouse').dialog('close');
            $('#txtWRHouseId').val(row.WRHouseId);
            $('#txtHouseName').textbox('setValue', row.HouseName);
            $('#FsuDialog').dialog('open');
        },
        onLoadError: function () {
            alertInfo('查询出错！', 'error');
        }
    });
};

//导出关联配置
var exportConfig = function (fsuCode, fsuName) {
    var f = $('<form action="FsuAssociatedDeviceExcel.aspx" method="post" id="fm1" accept-charset="UTF-8"></form>');
    $('<input type="hidden"  name="fsuCode"/>').val(fsuCode).appendTo(f);
    $('<input type="hidden"  name="fsuName"/>').val(fsuName).appendTo(f);
    f.appendTo(document.body).submit().remove();
};

//FSU信息列表查询
var loadFsuManagement = function () {
    $('#FsuManagementTable').datagrid({
        method: 'GET',
        url: `${FSU_HANDLER}/GetFsuInfo`,
        title: 'FSU信息列表',  //rownumbers: true, //行号  striped: true, //隔行变色                
        idField: 'FsuCode', //主键
        singleSelect: true,
        pagination: true,
        pageSize: 30,
        pageList: [30],
        queryParams: {
            //action: 'GetFsuInfo',
            startTime: $('#txtApplyTimeq').val(),
            endTime: $('#txtApplyTimeq2').val(),
            structureId: $('#StructureIdLook').combotree('getValue'),
            fsuName: $('#txtFsuNameq').val(),
            fsuCode: $('#txtFsuCodeq').val(),
            manufactureId: $('#ManufacturerIdq').combobox('getValue'),
            fsuStatus: $('#FsuStatusq').combobox('getValue')
        },
        width: function () { return document.body.clientWidth * 0.9; }, //设置表格宽度为客户端页面的0.9
        height: 'auto',
        columns: [[
            {
                field: 'btn', title: '', width: 100, align: 'center',
                formatter: function (value, row, index) {
                    if (row.FsuStatus === '3') {
                        return '<button type="button" onclick="exportConfig(\'' + row.FsuCode + '\',\'' +  row.FsuName  + '\')">导出关联配置</button>';
                    }
                }
            },
            { field: 'RowNumber', title: '序号', width: 40, align: 'center' },
            { field: 'StructureName', title: '分组', width: 80, align: 'left' },
            { field: 'StationName', title: '站址名称', width: 100, align: 'left' },
            { field: 'StationCode', title: '站址编码', width: 100, align: 'left' },
            { field: 'FsuName', title: 'FSU名称', width: 100, align: 'left' },
            { field: 'FsuCode', title: 'FSU编码', width: 100, align: 'left' },
            { field: 'ManufactureName', title: 'FSU厂家', width: 120, align: 'left' },
            { field: 'ContractNo', title: '合同号', width: 120, align: 'left' },
            { field: 'PortNumber', title: '端口数', width: 60, align: 'left' },
            { field: 'ProjectName', title: '工程名称', width: 120, align: 'left' },
            {
                title: '状态', field: 'StatusName', width: 60, align: 'center', halign: 'center',
                formatter: function (value, row, index) {
                    if (value === '申请退回')
                        return '<font color="red">' + value + '</font>';
                    else if (value === '申请通过')
                        return '<font color="green">' + value + '</font>';
                    else
                        return value;
                }
            },
            { field: 'LoginName', title: '注册用户名', width: 80, align: 'left' },
            { field: 'Password', title: '注册用口令', width: 80, align: 'left' },
            { field: 'FtpUserName', title: 'FTP用户名', width: 80, align: 'left' },
            { field: 'FtpPassword', title: 'FTP用口令', width: 80, align: 'left' },
            { field: 'UserName', title: '申请人', width: 60, align: 'left' },
            { field: 'ApplyTime', title: '申请日期', width: 120, align: 'left' },
            { field: 'ApproveTime', title: '批准日期', width: 120, align: 'left' },
            { field: 'RejectCause', title: '退回原因', width: 150, align: 'left' },
            {
                field: 'Remark', title: '备注', width: 150, align: 'left',
                formatter: function (value, row, index) {
                    return (value || '').toString().replace(/\s*/g, "");
                }
            }
        ]],
        onClickRow: function (index, row) {
            if (row.FsuStatus === '1'  ) {
                $('#btn_Pass').removeAttr('disabled');
                $('#btn_Pass').css({ 'background-color': 'rgb(232, 244, 255)' });
                $('#btn_back').removeAttr('disabled');
                $('#btn_back').css({ 'background-color': 'rgb(232, 244, 255)' });
            }
            else if (row.FsuStatus === '2' || row.FsuStatus === '3') {
                $('#btn_Pass').attr('disabled', 'disabled');
                $('#btn_Pass').css({ 'background-color': 'Silver' });
                $('#btn_back').attr('disabled', 'disabled');
                $('#btn_back').css({ 'background-color': 'Silver' });
            }
        },
        onLoadError: function () {
            alertInfo('查询出错！', 'error');
        }
    });
};

//检查新增/编辑输入项
var checkinfo = function () {
    if (!$('#txtHouseName').val()) {
        alertInfo('所属机房没有选择！');
        return false;
    }
    if (!$('#txtFsuName').val()) {
        alertInfo('FSU名称没有输入！');
        return false;
    }
    if (!$('#txtFsuCode').val()) {
        alertInfo('FSU编码没有输入！');
        $('#txtFsuCode').next('span').find('input').focus();
        return false;
    }
    if ($('#txtFsuCode').val().length !== 14) {
        alertInfo('输入有误，FSU编码字符长度需为14！');
        $('#txtFsuCode').next('span').find('input').focus();
        return false;
    }
    if (!$('#txtRemark').val()) {
        alertInfo('Fsu备注没有输入！');
        return false;
    }
    if (!$('#txtRegName').val()) {
        alertInfo('注册用户名没有输入！');
        return false;
    }
    if ($('#ManufacturerId').combobox('getValue') === '-1') {
        alertInfo('Fsu编码没有相应FSU厂家！');
        return false;
    }
    if (!$('#txtRegPass').val()) {
        alertInfo('注册口令没有输入！');
        return false;
    }
    if (!$('#txtFtpUser').val()) {
        alertInfo('FTP用户名没有输入！');
        return false;
    }
    if ($('#txtIPAddress').val()) {
        var pattern_ipv4 = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/i;
        var pattern_ipv6 = /^((([0-9A-F]{1,4}:){7}([0-9A-F]{1,4}|:))|(([0-9A-F]{1,4}:){6}(:[0-9A-F]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-F]{1,4}:){5}(((:[0-9A-F]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-F]{1,4}:){4}(((:[0-9A-F]{1,4}){1,3})|((:[0-9A-F]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-F]{1,4}:){3}(((:[0-9A-F]{1,4}){1,4})|((:[0-9A-F]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-F]{1,4}:){2}(((:[0-9A-F]{1,4}){1,5})|((:[0-9A-F]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-F]{1,4}:){1}(((:[0-9A-F]{1,4}){1,6})|((:[0-9A-F]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-F]{1,4}){1,7})|((:[0-9A-F]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))$/i;
        if (!pattern_ipv4.test($('#txtIPAddress').val()) && !pattern_ipv6.test($('#txtIPAddress').val())) {
            alertInfo('输入的IP地址不合法！');
            return false;
        }
        //if (!reg) {
        //    alertInfo('输入的IP地址不合法！');
        //    return false;
        //}
    }
    if (!$('#txtFsuRId').val) {
        alertInfo('SU资源ID没有输入！');
        return false;
    }

    if (!$('#txtFtpPass').val()) {
        alertInfo('FTP口令没有输入！');
        return false;
    }
    if (!$('#txtContractNo').val()) {
        alertInfo('合同号没有输入！');
        return false;
    }
    if (!$('#txtProjectName').val()) {
        alertInfo('工程名称没有输入！');
        return false;
    }
    return true;
};

//时间校验
var checkDateValid = function () {
    if (!$('#txtApplyTimeq').val() && $('#txtApplyTimeq2').val()
        || $('#txtApplyTimeq').val() && !$('#txtApplyTimeq2').val()) {
        alertInfo('申请开始日期和申请截止日期必须同时存在！');
        return false;
    }
    if ($('#txtApplyTimeq').datetimebox('isValid') && $('#txtApplyTimeq2').datetimebox('isValid'))
        return true;
    alertInfo('请输入正确的查询条件！');
    return false;
};

//回车事件
document.onkeydown = function (event) {
    var e = event || window.event;
    if (e.keyCode === 13 && document.activeElement.tagName !== 'TEXTAREA') {
        e.returnValue = false;
        e.cancel = true;
        if (!$('#divLookforHouse').dialog('options')['closed']) {
            $('#lookbtn_seach').focus();
            $('#lookbtn_seach').click();
        } else {
            $('#btn_seach').focus();
            $('#btn_seach').click();
        }
    }
};

//初始化控件
var init = function () {
    $('#txtApplyTimeq').datetimebox('setValue', new Date(new Date().setDate(1)).format('yyyy-MM-dd') + ' 00:00:00');
    $('#txtApplyTimeq2').datetimebox('setValue', new Date(new Date().getTime() + 1000 * 60 * 60).format('yyyy-MM-dd hh:mm:ss'));

    var groupInfo = getGroupInfo();
    if (groupInfo) {
        var obj = JSON.parse(groupInfo);
        $('#StructureIdLook').combotree('loadData', obj);
        $('#StructureIdLook').combotree('setValues', [obj[0].id]);
    }

    loadDicUiDropControl($('#ManufacturerId'), 4, 4);
    $('#ManufacturerId').combobox({ disabled: true});
    loadDicUiDropControl($('#ManufacturerIdq'), 4, 4);
    loadDicUiDropControl($('#FsuStatusq'), 5, 5);

    //Fsu编码输入联动Fsu厂家下拉框
    $('#txtFsuCode').textbox({
        onChange: function (newValue, oldValue) {
            if (newValue && newValue.length >= 2) {
                var value = newValue.substr(0, 2);
                $('#ManufacturerId').combobox('select', (~~value).toString());
                if ($('#ManufacturerId').combobox('getText') === value)
                    $('#ManufacturerId').combobox('select', '-1');
            }
        }
    });

    //新增
    $('#btn_opendialog').click(function () {
        $('#txtWRFsuId').val('');
        $('#txtWRHouseId').val('');
        $('#txtHouseName').textbox('setValue', '');
        $('#txtFsuName').textbox('setValue', '');
        $('#txtFsuCode').textbox('setValue', '');
        $('#txtIPAddress').textbox('setValue', '');
        $('#ManufacturerId').combobox('setValue', '-1');
        $('#txtRemark').textbox('setValue', '');
        $('#txtRegName').textbox('setValue', '');
        $('#txtRegPass').textbox('setValue', '');
        $('#txtRegPassHide').val('');
        $('#txtFtpUser').textbox('setValue', '');
        $('#txtFtpPass').textbox('setValue', '');
        $('#txtContractNo').textbox('setValue', '');
        $('#txtProjectName').textbox('setValue', '');
        $('#FTPTypeId').combobox('setValue', '0');
        $('#txtPortNumber').textbox('setValue', '');

        $('#btn_new').show();
        $('#btn_modify').hide();
        $('#FsuDialog').dialog('setTitle', '新增FSU');
        $('#FsuDialog').dialog('open');
    });

    //编辑
    $('#btn_Edit').click(function () {
        var row = $('#FsuManagementTable').datagrid('getSelected');
        if (row) {
            $('#txtWRFsuId').val(row.WRFsuId );
            $('#txtWRHouseId').val(row.WRHouseId );
            $('#txtHouseName').textbox('setValue', row.HouseName );
            $('#txtFsuName').textbox('setValue', row.FsuName );
            $('#txtIPAddress').textbox('setValue', row.IPAddress.trim(/^\[+|\]+$/g));
            $('#txtFsuCode').textbox('setValue', row.FsuCode  );
            $('#ManufacturerId').combobox('setValue', row.ManufacturerId );
            $('#txtRemark').textbox('setValue', row.Remark );
            $('#txtRegName').textbox('setValue', row.LoginName );
            $('#txtRegPass').textbox('setValue', row.Password );
            $('#txtRegPassHide').val(row.Password );
            $('#txtFtpUser').textbox('setValue', row.FtpUserName );
            $('#txtFtpPass').textbox('setValue', row.FtpPassword );
            $('#txtContractNo').textbox('setValue', row.ContractNo );
            $('#txtProjectName').textbox('setValue', row.ProjectName );
            $('#txtPortNumber').textbox('setValue', row.PortNumber );
            $('#FTPTypeId').combobox('setValue', row.FTPType);

            $('#btn_new').hide();
            $('#btn_modify').show();
            $('#FsuDialog').dialog('setTitle', '编辑FSU');
            $('#FsuDialog').dialog('open');
        }
        else
            alertInfo('请选择要修改的行！');
    });

    //关闭
    $('#btn_close').click(function () {
        $('#FsuDialog').dialog('close');
    });

    //打开机房查询对话窗
    $('#btn_LookHouse,#divHouseName,#txtHouseName').click(function () {
        $('#looktxtStationNameq').textbox('setValue', '');
        $('#looktxtStationCodeq').textbox('setValue', '');
        $('#looktxtHouseNameq').textbox('setValue', '');
        $('#FsuDialog').dialog('close');
        $('#divLookforHouse').dialog('open');
        loadHouseManagementData();
        $('#looktxtStationNameq').focus();
    });

    //查询
    $('#btn_seach').click(function () {
        if (!checkDateValid())
            return false;
        loadFsuManagement();
    });

    //重置
    $('#btn_Reset').click(function () {
        $('#txtFsuNameq').textbox('setValue', '');
        $('#txtFsuCodeq').textbox('setValue', '');
        $('#txtApplyTimeq').datetimebox('setValue', '');
        $('#txtApplyTimeq2').datetimebox('setValue', '');
        $('#FsuStatusq').combobox('setValue', '-1');
        $('#ManufacturerIdq').combobox('setValue', '-1');
        $('#StructureIdLook').combotree('setValue', '-1');
    });

    //通过
    $('#btn_Pass').click(function () {
        var row = $('#FsuManagementTable').datagrid('getSelected');
        if (row) {
            $.messager.confirm('提示', '你确定要通过审批吗？',
                function (r) {
                    if (r) {
                        $.ajax({
                            type: 'POST',
                            url: `${FSU_HANDLER}/ApproveWRFsu`,
                            contentType: 'application/x-www-form-urlencoded',
                            data: {
                                //action: 'ApproveWRFsu',
                                WRFsuId: row.WRFsuId.toString()
                            },
                            success: function (data) {
                                if (data === '1') {
                                    refreshNeedApproveResource();
                                    loadFsuManagement();
                                    $('#btn_Pass').attr('disabled', 'disabled');
                                    $('#btn_Pass').css({ 'background-color': 'Silver' });
                                    $('#btn_back').attr('disabled', 'disabled');
                                    $('#btn_back').css({ 'background-color': 'Silver' });
                                    alertInfo('审批成功！');
                                } else if (data === '-1')
                                    alertInfo('-1: FSU所属机房申请尚未通过审核,不允许审核FSU！', 'error');
                                else if (data === '-2')
                                    alertInfo('-2: FSU所属站址申请尚未通过审核,不允许审核FSU！', 'error');
                                else if (data === '-3')
                                    alertInfo('-3: 已审核过的FSU不能重复审核！', 'error');
                                else
                                    alertInfo(data + ': 出现错误', 'error');
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                            }
                        });
                    }
                });
        }
        else
            alertInfo('请选择要通过的行！');
    });

    //删除
    $('#btn_Delete').on('click', function (e) {
        var row = $('#FsuManagementTable').datagrid('getSelected');
        if (row) {
            $.messager.confirm('确认', '您确认删除吗？',
                function (r) {
                    if (r)
                        $.ajax({
                            type: 'POST',
                            url: `${FSU_HANDLER}/DeleteWRFsu`,
                            contentType: 'application/x-www-form-urlencoded',
                            data: {
                                //action: 'DeleteWRFsu',
                                WRFsuId: row.WRFsuId
                            },
                            async: false,
                            success: function (data) {
                                if (data === '1') {
                                    refreshNeedApproveResource();
                                    loadFsuManagement();
                                    alertInfo('删除成功！');
                                } else if (data === '-1')
                                    alertInfo('-1: 当前用户无权限删除此FSU！', 'error');
                                else
                                    alertInfo(data + ': 删除出现错误！', 'error');
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                            }
                        });
                });
        } else
            alertInfo('请选择要删除的行！');
    });

    //提交修改
    $('#btn_modify').click(function () {
        if (checkinfo()) {
            $.messager.confirm('提示框', '你确认要修改吗?',
                function (r) {
                    if (r) {
                        var ipAddress = $('#txtIPAddress').val();
                        $.ajax({
                            type: 'POST',
                            url: `${FSU_HANDLER}/ModifyWRFsu`,
                            contentType: 'application/x-www-form-urlencoded',
                            data: {
                                //action: 'ModifyWRFsu',
                                WRFsuId: $('#txtWRFsuId').val(),
                                WRHouseId: $('#txtWRHouseId').val(),
                                FsuName: $('#txtFsuName').val(),
                                FsuCode: $('#txtFsuCode').val(),
                                IPAddress: ipAddress,
                                Remark: $('#txtRemark').val(),
                                RegName: $('#txtRegName').val(),
                                ManufactureId: $('#ManufacturerId').combobox('getValue'),
                                RegPass: $('#txtRegPass').val(),
                                FtpUser: $('#txtFtpUser').val(),
                                FtpPass: $('#txtFtpPass').val(),
                                RegPassHide: $('#txtRegPassHide').val(),
                                ContractNo: $('#txtContractNo').textbox('getValue'),
                                ProjectName: $('#txtProjectName').textbox('getValue'),
                                FTPType: $('#FTPTypeId').combobox('getValue'),
                                PortNumber: $('#txtPortNumber').val()
                            },
                            success: function (data) {
                                if (data === '1') {
                                    $('#btn_Pass').removeAttr('disabled');
                                    $('#btn_Pass').css({ 'background-color': '' });
                                    $('#btn_back').removeAttr('disabled');
                                    $('#btn_back').css({ 'background-color': '' });
                                    $('#FsuDialog').dialog('close');
                                    alertInfo('修改成功！');
                                    loadFsuManagement();
                                }
                                else if (data === '-1')
                                    alertInfo('-1: 保存失败，FSU编码重复！', 'error');
                                else if (data === '-2')
                                    alertInfo('-2: 保存失败，FSU名称重复！', 'error');
                                else if (data === '-3')
                                    alertInfo('-3: 保存失败，FSU编码错误或厂家字典未更新！');
                                else if (data === '-4')
                                    alertInfo('-4: 保存失败，FSU IP 地址重复！');
                                else if (data === '-5')
                                    alertInfo('-5: 保存失败，修改之前状态已变为[审核通过]！', 'error');
                                else
                                    alertInfo(data + ': 保存失败，请查看日志文件！', 'error');
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                            }
                        });
                    }
                });
        }
    });

    //退回
    $('#btn_back').click(function () {
        var row = $('#FsuManagementTable').datagrid('getSelected');
        if (row) {
            $('#TxtRejectReason').textbox('setValue', '');
            $('#BackDialog').dialog('open');
        }
        else
            alertInfo('请选择要退回的行！');
    });

    //提交退回
    $('#btn_AppBackRe').click(function () {
        if (!$('#TxtRejectReason').val()) {
            alertInfo('请录入退回原因！');
            return false;
        }
        var row = $('#FsuManagementTable').datagrid('getSelected');
        $.messager.confirm('提示框', '你确认要退回吗?', function (r) {
            if (r) {
                $.ajax({
                    type: 'POST',
                    url: `${FSU_HANDLER}/RejectWRFsu`,
                    contentType: 'application/x-www-form-urlencoded',
                    data: {
                        //action: 'RejectWRFsu',
                        WRFsuId: row.WRFsuId.toString(),
                        RejectReason: $('#TxtRejectReason').val()
                    },
                    success: function (data) {
                        if (data === '1') {
                            refreshNeedApproveResource();
                            $('#btn_Pass').attr('disabled', 'disabled');
                            $('#btn_Pass').css({ 'background-color': 'Silver' });
                            $('#btn_back').attr('disabled', 'disabled');
                            $('#btn_back').css({ 'background-color': 'Silver' });
                            $('#BackDialog').dialog('close');
                            alertInfo('退回成功！');
                            loadFsuManagement();
                        } else if (data === '-1')
                            alertInfo('-1: 已审核通过的FSU不能退回！', 'error');
                        else
                            alertInfo(data + ': 出现错误！', 'error');
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                    }
                });
            }
        });
    });

    //提交新增
    $('#btn_new').click(function () {
        if (checkinfo()) {
            $.messager.confirm('提示框', '你确认要提交吗?',
                function (r) {
                    if (r) {
                        var ipAddress = $('#txtIPAddress').val();
                        $.ajax({
                            type: 'POST',
                            url: `${FSU_HANDLER}/newone`,
                            contentType: 'application/x-www-form-urlencoded',
                            data: {
                                //action: 'newone',
                                WRHouseId: $('#txtWRHouseId').val(),
                                FsuCode: $('#txtFsuCode').val(),
                                FsuName: $('#txtFsuName').val(),
                                IPAddress: ipAddress,
                                Remark: $('#txtRemark').val(),
                                UserName: $('#txtRegName').val(),
                                RegPass: $('#txtRegPass').val(),
                                ManufactureId: $('#ManufacturerId').combobox('getValue'),
                                FtpUser: $('#txtFtpUser').val(),
                                FtpPass: $('#txtFtpPass').val(),
                                ContractNo: $('#txtContractNo').textbox('getValue'),
                                ProjectName: $('#txtProjectName').textbox('getValue'),
                                FTPType: $('#FTPTypeId').combobox('getValue'),
                                PortNumber: $('#txtPortNumber').val()
                            },
                            success: function (data) {
                                if (data === '1') {
                                    refreshNeedApproveResource();
                                    $('#FsuDialog').dialog('close');
                                    alertInfo('添加成功！');
                                    loadFsuManagement();
                                }
                                else if (data === '-1') {
                                    alertInfo('-1: 保存失败，FSU编码重复！');
                                    return;
                                }
                                else if (data === '-2') {
                                    alertInfo('-2: 保存失败，FSU名称重复！');
                                    return;
                                }
                                else if (data === '-3') {
                                    alertInfo('-3: 保存失败，FSU编码错误或厂家字典未更新！');
                                    return;
                                }
                                else if (data === '-4') {
                                    alertInfo('-4: 保存失败，FSU IP 地址重复！');
                                    return;
                                }
                                else if (data === '-5') {
                                    alertInfo('-5: 授权FSU接入数已用完，请联系厂家！');
                                    return;
                                }
                                else {
                                    alertInfo(data + ': 保存失败，请查看日志文件！');
                                    return;
                                }
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                            }
                        });
                    }
                });
        }
    });


    //导出Excel
    $('#btn_Excel').click(function () {
        var f = $('<form action="api/fsu/exportexcel" method="get" id="fm1" accept-charset="UTF-8"></form>');
        $('<input type="hidden"  name="fsuName"/>').val($('#txtFsuNameq').val()).appendTo(f);
        $('<input type="hidden"  name="fsuCode"/>').val($('#txtFsuCodeq').val()).appendTo(f);
        $('<input type="hidden"  name="startTime"/>').val($('#txtApplyTimeq').val()).appendTo(f);
        $('<input type="hidden"  name="endTime"/>').val($('#txtApplyTimeq2').val()).appendTo(f);
        $('<input type="hidden"  name="Status"/>').val($('#FsuStatusq').combobox('getValue')).appendTo(f);
        $('<input type="hidden"  name="structureId"/>').val($('#StructureIdLook').combotree('getValue')).appendTo(f);
        $('<input type="hidden"  name="manufactureId"/>').val($('#ManufacturerIdq').combobox('getValue')).appendTo(f);
        f.appendTo(document.body).submit().remove();
    });

    //机房查询-关闭
    $('#lookbtn_close').click(function () {
        $('#divLookforHouse').dialog('close');
        $('#FsuDialog').dialog('open');
    });

    //机房查询-查询
    $('#lookbtn_seach').click(function () {
        loadHouseManagementData();
    });

    //机房查询-重置
    $('#lookbtn_Reset').click(function () {
        $('#looktxtStationNameq').textbox('setValue', '');
        $('#looktxtStationCodeq').textbox('setValue', '');
        $('#looktxtHouseNameq').textbox('setValue', '');
        $('#looktxtStationNameq').focus();
    });

    //机房查询-选择
    $('#lookbtn_Select').click(function () {
        var row = $('#lookHouseTable').datagrid('getSelected');
        if (row) {
            $('#divLookforHouse').dialog('close');
            $('#txtWRHouseId').val(row.WRHouseId );
            $('#txtHouseName').textbox('setValue', row.HouseName );
            $('#FsuDialog').dialog('open');
        }
        else
            alertInfo('请选择要选择行！');
    });

    //规范浏览
    $('#btn_help').click(function () {
        $.ajax({
            type: 'GET',
            url: GET_STANDARDDOC_HANDLER,
            contentType: 'application/x-www-form-urlencoded',
            data: { type: 3 },
            async: false,
            success: function (data) {
                $('#standardContent').val(data);
                $('#standardContent').attr('disabled', 'disbaled');
                $('#standardDoc').dialog('open');
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
            }
        });
    });

    //规范浏览编辑
    $('#btn_docEdit').click(function () {
        $('#standardContent').removeAttr('disabled');
        $('#standardContent').focus();
    });

    //规范浏览保存
    $('#btn_docSave').click(function () {
        $('#standardContent').attr('disabled', 'disabled');
        $.messager.confirm('确认', '您确认保存吗？',
            function (r) {
                if (r) {
                    $.ajax({
                        type: 'POST',
                        contentType: 'application/json; charset=UTF-8',
                        url: SAVE_STANDARDDOC_HANDLER,
                        data: JSON.stringify({ FileName: 'FsuStandard', Content: $('#standardContent').val().replace(/(?!\r)\n/g, '\r\n') }),
                        success: function (data) {
                            alertInfo(data);
                        },
                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                        }
                    });
                }
            });
    });
};

//onload
$(function () {
    init();

    if (cookie['userinfo']['UserId'] !== '-1' || userRole !== '系统管理员')
        $('#toolbar').remove();
    if (userRole !== '系统管理员') {
        $('#btn_Pass').remove();
        $('#btn_back').remove();
    }
    else {
        $('#btn_Delete').remove();
        $('#btn_opendialog').remove();
    }

    loadFsuManagement();

    $('div.mask').remove();
});