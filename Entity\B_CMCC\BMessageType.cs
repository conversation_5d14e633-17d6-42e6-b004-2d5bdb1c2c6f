﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B_CMCC
{
    public enum BMessageType
    {
        /// <summary>
        /// 未定义
        /// </summary>
        UNDEFINED = 0,

        /// <summary>
        /// 注册/响应，FSU向LSC注册
        /// </summary>
        LOGIN = 101,
        LOGIN_ACK = 102,

        /// <summary>
        /// 实时告警发送/响应，FSU上报告警信息
        /// </summary>
        SEND_ALARM = 201,
        SEND_ALARM_ACK = 202,

        /// <summary>
        /// 请求监控点数据/响应
        /// </summary>
        GET_DATA = 301,
        GET_DATA_ACK = 302,

        /// <summary>
        /// 写监控点设置值请求/响应
        /// </summary>
        SET_POINT = 401,
        SET_POINT_ACK = 402,

        /// <summary>
        /// 请求监控点门限数据/响应
        /// </summary>
        GET_THRESHOLD = 501,
        GET_THRESHOLD_ACK = 502,

        /// <summary>
        /// 写监控点门限数据/响应
        /// </summary>
        SET_THRESHOLD = 601,
        SET_THRESHOLD_ACK = 602,

        /// <summary>
        /// 获取FSU注册信息/响应
        /// </summary>
        GET_LOGININFO = 701,
        GET_LOGININFO_ACK = 702,

        /// <summary>
        /// 设置FSU注册信息/响应
        /// </summary>
        SET_LOGININFO = 801,
        SET_LOGININFO_ACK = 802,

        /// <summary>
        /// 获取FSU的FTP信息/响应
        /// </summary>
        GET_FTP = 901,
        GET_FTP_ACK = 902,

        /// <summary>
        /// 设置FSU的FTP信息/响应
        /// </summary>
        SET_FTP = 1001,
        SET_FTP_ACK = 1002,

        /// <summary>
        /// LSC向FSU请求时间同步/响应
        /// </summary>
        TIME_CHECK = 1101,
        TIME_CHECK_ACK = 1102,

        /// <summary>
        /// 获取FSU的状态参数/响应
        /// </summary>
        GET_FSUINFO = 1201,
        GET_FSUINFO_ACK = 1202,

        /// <summary>
        /// 重启FSU请求/响应
        /// </summary>
        SET_FSUREBOOT = 1301,
        SET_FSUREBOOT_ACK = 1302,

        /// <summary>
        /// FSU上报监控点数据请求/响应
        /// </summary>
        SEND_DATA = 1401,
        SEND_DATA_ACK = 1402,

        /// <summary>
        /// LSC动环配置数据请求/响应
        /// </summary>
        GET_DEV_CONF = 1501,
        GET_DEV_CONF_ACK = 1502,

        /// <summary>
        /// FSU上报动环设备配置变更数据请求/响应
        /// </summary>
        SEND_DEV_CONF_DATA = 1601,
        SEND_DEV_CONF_DATA_ACK = 1602,

        /// <summary>
        /// LSC写动环设备配置数据请求/响应
        /// </summary>
        SET_DEV_CONF_DATA = 1701,
        SET_DEV_CONF_DATA_ACK = 1702,

        /// <summary>
        /// 更新FSU状态信息获取周期
        /// </summary>
        UPDATE_FSUINFO_INTERVAL = 1801,
        UPDATE_FSUINFO_INTERVAL_ACK = 1802,

        /// <summary>
        /// 查询监控点存储规则
        /// </summary>
        GET_STORAGERULE = 1901,
        GET_STORAGERULE_ACK = 1902,

        /// <summary>
        /// 写监控点存储规则
        /// </summary>
        SET_STORAGERULE = 2001,
        SET_STORAGERULE_ACK = 2002,

    }
}
