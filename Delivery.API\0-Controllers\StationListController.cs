﻿using BLL;
using Delivery.API;
using Delivery.DAL;
using Microsoft.AspNetCore.Mvc;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using System.Collections.Generic;
using System;

namespace DM.TestOrder.Controllers
{

    public class StationListController : BaseController
    {

        [HttpGet]
        public JsonResult Get(string stationName = "", string stationCode = "")
        {
            if (CommonUtils.IsNoProcedure)
            {
                WOGetStationListService wOGetStationListService = new WOGetStationListService();
                var res = wOGetStationListService.GetStationList(stationName, stationCode);
                DataTableColumnMapper.RenameColumns(res, StationFieldMap);
                return new JsonResult(res);
            }
            else
            {
                var tb = new ExecuteSql().ExecuteStoredProcedure("WO_GetStationList", new QueryParameter[] {
                new QueryParameter("StationName", DataType.String, stationName),
                new QueryParameter("StationCode", DataType.String, stationCode)
                    
            });
                return new JsonResult(tb);
            }


        }

        private static readonly Dictionary<string, string> StationFieldMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            {"address","Address"},
            {"centerid","CenterId"},
            {"centername","CenterName"},
            {"city","City"},
            {"cityname","CityName"},
            {"county","County"},
            {"countyname","CountyName"},
            {"latitude","Latitude"},
            {"longitude","Longitude"},
            {"province","Province"},
            {"provincename","ProvinceName"},
            {"remark","Remark"},
            {"stationcategory","StationCategory"},
            {"stationcategoryname","StationCategoryName"},
            {"stationcode","StationCode"},
            {"stationid","StationId"},
            {"stationname","StationName"},
            {"stationstatus","StationStatus"},
            {"stationstatus1","StationStatus1"},
            {"structureid","StructureId"},
            {"structurename","StructureName"},
            {"swstationid","SWStationId"}
        };

    }
}
