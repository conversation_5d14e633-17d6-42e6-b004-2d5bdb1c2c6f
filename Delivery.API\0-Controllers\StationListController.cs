﻿using BLL;
using Delivery.API;
using Delivery.DAL;
using Microsoft.AspNetCore.Mvc;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;




namespace DM.TestOrder.Controllers
{

    public class StationListController : BaseController
    {

        [HttpGet]
        public JsonResult Get(string stationName = "", string stationCode = "")
        {
            if (CommonUtils.IsNoProcedure)
            {
                WOGetStationListService wOGetStationListService = new WOGetStationListService();
                var res = wOGetStationListService.GetStationList(stationName, stationCode);
                return new JsonResult(res);
            }
            else
            {
                var tb = new ExecuteSql().ExecuteStoredProcedure("WO_GetStationList", new QueryParameter[] {
                new QueryParameter("StationName", DataType.String, stationName),
                new QueryParameter("StationCode", DataType.String, stationCode)
                    
            });
                return new JsonResult(tb);
            }


        }

    }
}
