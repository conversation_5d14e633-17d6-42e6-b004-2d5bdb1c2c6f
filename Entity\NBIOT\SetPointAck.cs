﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class SetPointAck : BMessage
    {
        public EnumResult Result { get; set; }
        
        public string FailureCause { get; set; }

        [JsonProperty("DeviceList")]
        public List<DeviceSetPointAck> Devices { get; set; }

        #region SiteWeb配置

        [JsonIgnore]
        public int MyStationId { get; set; }

        [JsonIgnore]
        public int UserId { get; set; }

        [JsonIgnore]
        public DateTime? StartTime { get; set; }

        [JsonIgnore]
        public int ControlId { get; set; }

        #endregion

        public SetPointAck() : base()
        {
            MessageType = (int)BMessageType.SET_POINT_ACK;
        }

        public SetPointAck(string fsuId, EnumResult result, string failureCause, List<DeviceSetPointAck> devices)
            : base()
        {
            MessageType = (int)BMessageType.SET_POINT_ACK;

            FSUID = fsuId;
            Result = result;
            FailureCause = failureCause;
            Devices = devices;
        }

        public static SetPointAck Deserialize(string json)
        {
            SetPointAck info = null;
            try
            {
                info = JsonConvert.DeserializeObject<SetPointAck>(json);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetPointAck.Deserialize:{0}" , ex.Message);
                entityLogger.ErrorFormat("SetPointAck.Deserialize:{0}" , ex.StackTrace);
                info = new SetPointAck();
                info.ErrorMsg = ex.Message;
                info.StringXML = json;
            }
            return info;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();

            sb.AppendFormat("{0}, {1}, {2}, {3}:",
                MessageId, (BMessageType)MessageType, FSUID, Devices != null ? Devices.Count : 0);
            sb.AppendLine();
            if (Devices != null)
            {
                foreach (DeviceSetPointAck item in Devices)
                {
                    sb.AppendFormat("{0}", item.ToString());
                    sb.AppendLine();
                }
            }
            return sb.ToString();
        }
    }
}