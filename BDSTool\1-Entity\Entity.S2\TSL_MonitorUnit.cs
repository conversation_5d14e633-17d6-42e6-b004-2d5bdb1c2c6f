﻿namespace BDSTool.Entity.S2
{
    using System;
    using System.Collections.Generic;

    public partial class TSL_MonitorUnit
    {
        public int MonitorUnitId {
            get;
            set;
        }
        public string MonitorUnitName {
            get;
            set;
        }
        public int MonitorUnitCategory {
            get;
            set;
        }
        public string MonitorUnitCode {
            get;
            set;
        }
        public Nullable<int> WorkStationId {
            get;
            set;
        }
        public Nullable<int> StationId {
            get;
            set;
        }
        public string IpAddress {
            get;
            set;
        }
        public Nullable<int> RunMode {
            get;
            set;
        }
        public string ConfigFileCode {
            get;
            set;
        }
        public Nullable<System.DateTime> ConfigUpdateTime {
            get;
            set;
        }
        public string SampleConfigCode {
            get;
            set;
        }
        public string SoftwareVersion {
            get;
            set;
        }
        public string Description {
            get;
            set;
        }
        public Nullable<System.DateTime> StartTime {
            get;
            set;
        }
        public Nullable<System.DateTime> HeartbeatTime {
            get;
            set;
        }
        public int ConnectState {
            get;
            set;
        }
        public System.DateTime UpdateTime {
            get;
            set;
        }
        public bool IsSync {
            get;
            set;
        }
        public Nullable<System.DateTime> SyncTime {
            get;
            set;
        }
        public bool IsConfigOK {
            get;
            set;
        }
        public string ConfigFileCode_Old {
            get;
            set;
        }
        public string SampleConfigCode_Old {
            get;
            set;
        }
        public Nullable<int> AppCongfigId {
            get;
            set;
        }
        public bool CanDistribute {
            get;
            set;
        }
        public bool Enable {
            get;
            set;
        }
        public string ProjectName {
            get;
            set;
        }
        public string ContractNo {
            get;
            set;
        }
        public Nullable<System.DateTime> InstallTime {
            get;
            set;
        }
    }
}
