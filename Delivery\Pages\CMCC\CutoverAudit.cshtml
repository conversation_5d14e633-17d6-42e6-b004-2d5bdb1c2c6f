﻿@page
@model Delivery.Pages.CMCC.CutoverAuditModel
@{
}

@section Scripts {
    <script type="text/javascript" src="~/Scripts/lib/jquery-1.8.0.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/ajaxfileUpload.js"></script>
    <script type="text/javascript" src="~/Scripts/common/ie8.js"></script>
    <script type="text/javascript" src="/Scripts/common/common.js"></script>
    <script type="text/javascript" src="~/CMCC/scripts/CutoverAudit.js"></script>
}

@section Styles {
    <style>
        * {
            font-size: 12px;
        }

        .txtCenter {
            text-align: center;
        }

        .textbox.textbox-readonly > input, .textbox.textbox-readonly > textarea {
            background-color: #eaefed;
        }

        .datagrid-cell {
            word-wrap: break-word;
            white-space: normal;
        }

        .upload {
            display: none;
        }
    </style>
    <!--[if IE]>
    <style>
        .upload {
            display: inline;
            position: absolute;
            filter:alpha(opacity:0);
            opacity: 0;
            width: 46px;
            margin-left: -46px;
            height: 23px;
        }
    </style>
    <![endif]-->
}

<div class="mask">
    <div style="position: relative; top: 50%; left: 50%">
        <div class="loading">
            正在加载，请等待...
        </div>
    </div>
</div>
<div style="width: 100%; padding: 0px;" class="easyui-panel">
    <table style="width: 100%;">
        <tr>
            <td class="labTdStyle">
                割接类型
            </td>
            <td class="contorlTdStyle">
                <input id="txtOrderType" class="easyui-textbox" style="width: 200px;" readonly="true" />
            </td>
            <td class="labTdStyle">
                割接单号
            </td>
            <td class="contorlTdStyle">
                <input id="txtMyOrderId" class="easyui-textbox" style="width: 200px;" readonly="true" />
            </td>
        </tr>
        <tr>
            <td class="labTdStyle">
                创建人
            </td>
            <td class="contorlTdStyle">
                <input id="txtApplyUserName" class="easyui-textbox" style="width: 200px;" readonly="true" />
            </td>
            <td class="labTdStyle">
                FSU厂家
            </td>
            <td class="contorlTdStyle">
                <input id="txtApplyUserFsuVendor" class="easyui-textbox" style="width: 200px;" readonly="true" />
            </td>
        </tr>
    </table>
</div>
<h1></h1>
<div class="easyui-accordion" style="width: 100%;"> 
    <div title="入网申请" style="width: 100%; padding: 5px;">
        <div id="applyAccordion" class="easyui-accordion" data-options="multiple:true,onSelect:function(title,index){if (title==='测试项')$('#dgTestItems').datagrid('resize');}" style="width: 100%; padding: 0px;">
            <div title="站址信息" style="overflow: auto; padding: 5px; display: none;" data-options="selected:false">
                <table style="width: 100%;">
                    <tr>
                        <td class="labTdStyle">
                            监控中心
                        </td>
                        <td class="contorlTdStyle">
                            <input id="txtCenterName" class="easyui-textbox" style="width: 200px;" readonly="true" />
                        </td>
                        <td class="labTdStyle">
                            分 组
                        </td>
                        <td class="contorlTdStyle">
                            <input id="txtGroupName" class="easyui-textbox" style="width: 200px;" readonly="true" />
                        </td>
                    </tr>
                    <tr>
                        <td class="labTdStyle">
                            站址名称
                        </td>
                        <td class="contorlTdStyle">
                            <input id="txtStationName" class="easyui-textbox" style="width: 200px;" readonly="true" />
                        </td>
                        <td class="labTdStyle">
                            站址编码
                        </td>
                        <td class="contorlTdStyle">
                            <input id="txtStationCode" class="easyui-textbox" style="width: 200px;" readonly="true" />
                        </td>
                    </tr>
                    <tr>
                        <td class="labTdStyle">
                            站址类型
                        </td>
                        <td class="contorlTdStyle">
                            <input id="txtStationCategory" class="easyui-textbox" style="width: 200px;" readonly="true" />
                        </td>
                        <td class="labTdStyle">
                            经 度
                        </td>
                        <td class="contorlTdStyle">
                            <input id="txtLongitude" type="text" class="easyui-numberbox" value="100"
                                   style="width: 200px;" data-options="min:0,max:180,precision:17" />
                            <span style="color: red">范围:(0,180]</span>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2" class="labTdStyle"></td>
                        <td class="labTdStyle">
                            纬 度
                        </td>
                        <td class="contorlTdStyle">
                            <input id="txtLatitude" type="text" class="easyui-numberbox" value="100"
                                   style="width: 200px;" data-options="min:0,max:180,precision:17" />
                            <span style="color: red">范围:(0,90]</span>
                        </td>
                    </tr>
                </table>
            </div>
            <div id="pageEquips" title="设备信息" style="padding: 5px; display: none;" data-options="selected:false">
                <table id="dgEquips"></table>
            </div>
            <div title="工程信息" style="overflow: auto; padding: 5px;" data-options="selected:false">
                <table style="width: 100%;">
                    <tr>
                        <td class="labTdStyle">
                            安装公司
                        </td>
                        <td class="contorlTdStyle">
                            <input id="txtInstallCompany" class="easyui-textbox" style="width: 200px" data-options="
                            prompt: '请选择安装公司',
			                icons:[{
				                iconCls:'icon-search',
				                handler: function(e){
					                var v = $(e.data.target).textbox('getValue');
                                    $('#installCompanyDialog').dialog('open');
                                    $('#btnInstallCompanyReset').click();
                                    $('#btnInstallCompanySeach').click();
				                }
			                }]" />
                        </td>
                        <td class="labTdStyle">
                            安装人员
                        </td>
                        <td class="contorlTdStyle">
                            <input id="txtInstaller" class="easyui-textbox" style="width: 200px" data-options="
                            prompt: '请选择安装人员',
			                icons:[{
				                iconCls:'icon-search',
				                handler: function(e){
                                    if(!CompanyId){
                                        $.messager.alert('提示','请先选择安装公司！');
                                        return;
                                    }
					                var v = $(e.data.target).textbox('getValue');
                                    $('#installerDialog').dialog('open');
                                    $('#btnInstallerReset').click();
                                    $('#btnInstallerSeach').click();
				                }
			                }]" />
                        </td>
                    </tr>
                </table>
            </div>
            <div title="测试项" style="padding: 5px; display: none;" data-options="">
                <table id="dgTestItems" title="测试项表" style="width: 100%; height: auto;"></table>
                <h1></h1>
                <div style="width: 100%; text-align: center;">
                    <input type="button" id="btnRefresh" value="刷新测试项" class="commonButton" style="width: 100px;" />
                </div>
                <h1></h1>
                <table id="dgArtTestItems" title="工艺自查表"></table>
                <h1></h1>
            </div>
        </div>
        <div style="margin-top: 15px;">
            <input id="txtScoreDocDevice" data-options="label:'协议归档',labelAlign:'right',labelWidth:60,readonly:true"
                   style="width: 150px;" class="easyui-textbox" />
            <span style="width: 50px" />
            <input id="txtScoreDocPhoto" data-options="label:'照片归档',labelAlign:'right',labelWidth:60,readonly:true"
                   style="width: 150px;" class="easyui-textbox" />
            <input id="txtScoreDocTest" data-options="label:'通过项',labelAlign:'right',labelWidth:60,readonly:true"
                   style="width: 150px;" class="easyui-textbox" />
            <input id="txtScoreDocArt" data-options="label:'工艺自查',labelAlign:'right',labelWidth:60,readonly:true"
                   style="width: 150px;" class="easyui-textbox" />
        </div>
        <div style="width: 100%; text-align: center;">
            <input type="button" id="btnSubmit" value="保存" class="commonButton" style="display: none; width: 100px; margin-top: 8px;" />
        </div>
    </div>
</div>

<h1></h1>
<div>
    <div id="auditAccordion" class="easyui-accordion" data-options="selected:false" style="width: 100%; padding: 0px;">
        <div title="专家组审核" style="padding: 5px; display: none; width: 100%" data-options="selected:false">
            <table id="dgExpertApprove" style="width: 100%; height: auto;"></table>
            <h1></h1>
            <table style="width: 100%; table-layout: fixed;">
                <tr>
                    <td style="width: 15%;">
                        <label class="labTdStyle">审核意见</label><span style="color: #f00; font-weight: bold;"> *</span>
                    </td>
                    <td>
                        <input id="txtExpertDecision" class="easyui-textbox" data-options="multiline:true,validType:'maxLength[255]'" style="width: 99%; height: 40px" />
                    </td>
                </tr>
                <tr>
                    <td>
                        <label class="labTdStyle">备注</label>
                    </td>
                    <td>
                        <input id="txtExpertNote" class="easyui-textbox" data-options="multiline:true,validType:'maxLength[255]'" style="width: 99%; height: 40px" />
                    </td>
                </tr>
            </table>
            <h1></h1>
            <div style="width: 100%; text-align: center;">
                <input type="button" id="btnExpertPass" value="通过" class="commonButton" style="display: none; width: 100px;" />
                <input type="button" id="btnExpertReject" value="退回" class="commonButton" style="display: none; width: 100px;" />
            </div>
            <h1></h1>
        </div>
    </div>
</div>
<h1></h1>
<div>
    <div id="reviewAccordion" class="easyui-accordion" data-options="selected:false" style="width: 100%; padding: 0px;">
        <div title="入网复审" style="overflow: auto; padding: 5px; display: none;" data-options="selected:false">
            <table style="width: 100%; table-layout: fixed;">
                <tr>
                    <td style="width: 15%;">
                        <label class="labTdStyle">总体测试结果</label>
                    </td>
                    <td>
                        <input id="rdoAllPass" type="radio" checked="checked" value="全部通过" onchange="document.getElementById('rdoPartPass').checked = !this.checked" />全部通过
                        <input id="rdoPartPass" type="radio" style="margin-left: 20%" value="部分通过" onchange="document.getElementById('rdoAllPass').checked = !this.checked" />部分通过
                    </td>
                </tr>
                <tr>
                    <td>
                        <label class="labTdStyle">审核意见</label><span style="color: #f00; font-weight: bold;"> *</span>
                    </td>
                    <td>
                        <input id="txtFinalDecision" class="easyui-textbox" data-options="multiline:true,validType:'maxLength[255]'" style="width: 99%; height: 40px" />
                    </td>
                </tr>
                <tr>
                    <td>
                        <label class="labTdStyle">备注</label>
                    </td>
                    <td>
                        <input id="txtFinalNote" class="easyui-textbox" data-options="multiline:true,validType:'maxLength[255]'" style="width: 99%; height: 40px" />
                    </td>
                </tr>
            </table>
            <h1></h1>
            <div style="width: 100%; text-align: center;">
                <input type="button" id="btnFinalPass" value="通过" class="commonButton" style="display: none; width: 100px;" />
                <input type="button" id="btnFinalReject" value="退回" class="commonButton" style="display: none; width: 100px;" />
            </div>
            <h1></h1>
        </div>
    </div>
</div>
<h1></h1>
<div>
    <div id="flowAccordion" class="easyui-accordion" data-options="selected:false" style="width: 100%; padding: 0px;">
        <div title="流程" style="overflow: auto; padding: 5px;" data-options="selected:false">
            <table id="dgFlow" class="easyui-datagrid"></table>
        </div>
    </div>


    <div style="min-width: 1000px; width: 100%; text-align: center; display: none;" id="subButton">
        <table style="width: 100%;">
            <tr style="width: 100%;">
                <td style="width: 100%; text-align: center">
                    <input type="button" id="btnSubmit" value="确    认" onclick="flowSubmit();" class="commonButton" style="display: block; width: 100px; " />
                </td>
            </tr>
        </table>
    </div>

    <div id="installCompanyDialog" title="安装公司选择" style="width: 700px; height: 550px; padding: 5px; display: none;" class="easyui-dialog" data-options="modal:true,closed:true,closable:true">
        <table style="width: 100%;">
            <tr>
                <td style="text-align: left; white-space: nowrap; width: 50px;">
                    公司名称
                </td>
                <td style="text-align: left; white-space: nowrap; width: 200px;">
                    <input id="txtCompanyNameC" class="easyui-textbox" style="width: 100%;" data-options="validType:'maxLength[255]'" />
                </td>
                <td style="text-align: right; white-space: nowrap;">
                    <input type="button" id="btnInstallCompanySeach" value="查询" class="commonButton" style="width: 40pt;" />
                    <input type="button" id="btnInstallCompanyReset" value="重置" class="commonButton" style="width: 40pt;" />
                    <input type="button" id="btnInstallCompanyChoose" value="选择" class="commonButton" style="width: 40pt;" />
                    <input type="button" id="btnInstallCompanyAdd" value="录入" class="commonButton" style="width: 40pt; display: none;" />
                </td>
                <td style="width: 50px" />
            </tr>
            <tr style="display: none;">
                <td colspan="4" style="color: #f00;">*初始无安装公司数据时,请填写公司名称点击录入安装公司</td>
            </tr>
        </table>
        <div>
            <table id="dgInstallCompanyGrid"></table>
        </div>
    </div>
</div>
<div id="installerDialog" title="安装人员选择" style="width: 700px; height: 550px; padding: 5px; display: none;" class="easyui-dialog" data-options="modal:true,closed:true,closable:true">
    <table style="width: 100%;">
        <tr>
            <td style="text-align: right; white-space: nowrap; width: 50px;">
                人员名称
            </td>
            <td style="text-align: left; white-space: nowrap; width: 200px;">
                <input id="txtInstallerName" class="easyui-textbox" style="width: 100%;" data-options="validType:'maxLength[255]'" />
            </td>
            <td style="width: 300px;"></td>
            <td style="text-align: right; white-space: nowrap;" colspan="4">
                <input type="button" id="btnInstallerSeach" value="查询" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btnInstallerReset" value="重置" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btnInstallerChoose" value="选择" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btnInstallerAdd" value="录入" class="commonButton" style="width: 40pt;" />
            </td>
        </tr>
        <tr>
            <td colspan="4" style="color: #f00;">*初始无安装人员数据时,请填写人员名称点击录入安装人员</td>
        </tr>

    </table>
    <div>
        <table id="dgInstallerGrid"></table>
    </div>
</div>

<div id="BackDialog" title="新增工作流" style="width: 550px; height: 250px;" class="easyui-dialog"
     data-options="modal:true,closed:true,closable:true">
    <table id="Table1">
        <tr>

            <td style="width: 150px; height: 45px; text-align: right;">工作流名称:</td>
            <td>
                <input type='text' placeholder="请输入工作流名称" data-options="prompt:'请输入工作流名称'" class="easyui-textbox" id="txtWorkflowNameq" style="width: 140px;" />
            </td>
        </tr>
        <tr>
            <td style="width: 150px; height: 45px; text-align: right;">备注:</td>
            <td>
                <textarea name="TxtRejectReason" id="TxtRejectReason" class="easyui-textbox" data-options="prompt:'备注'" style="width: 300px;"></textarea>
            </td>
            <td></td>
        </tr>
        <tr>
            <td style="width: 50px;"></td>
            <td>
                <input type="button" id="btn_AppBackRe" value="提交" class="commonButton" style="width: 40pt;" />
            </td>
            <td></td>
        </tr>
    </table>
</div>