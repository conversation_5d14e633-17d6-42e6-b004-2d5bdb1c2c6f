﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 监控点数据应答报文
    /// </summary>
    public sealed class GetDataAck : BMessage
    {
        /// <summary>
        /// 返回结果
        /// </summary>
        [Newtonsoft.Json.JsonProperty(Order = 1)]
        public EnumResult Result { get; set; }

        /// <summary>
        /// 失败的原因
        /// </summary>
        [Newtonsoft.Json.JsonProperty(Order = 2)]
        public string FailureCause { get; set; }

        //public List<TSemaphore<TRealTimeData>> Values { get; set; }
        public List<Device> Values { get; set; }

        #region SiteWeb配置
        [Newtonsoft.Json.JsonIgnore]
        public int MyStationId { get; set; }

        #endregion    

        public GetDataAck(string fsuId, EnumResult result, List<Device> values, string failureCause):base()
        {
            MessageType = (int)BMessageType.GET_DATA_ACK;

            FSUID = fsuId;
            Result = result;
            Values = values;
            FailureCause = failureCause;
        }

        public GetDataAck()
            : base()
        {
            MessageType = (int)BMessageType.GET_DATA_ACK;
        }

        public static GetDataAck Deserialize(string json)
        {
            GetDataAck info = null;
            try
            {
                info = JsonConvert.DeserializeObject<GetDataAck>(json);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetDataAck.Deserialize:{0}",ex.Message);
                entityLogger.ErrorFormat("GetDataAck.Deserialize:{0}",ex.StackTrace);
                info = new GetDataAck();
                info.ErrorMsg = ex.Message;
                info.StringXML = json;
            }
            return info;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}, {2}:{3},{4}", MessageId, MessageType, FSUID, Result, FailureCause);
            sb.AppendLine();

            if (Values != null)
            {
                foreach (Device device in Values)
                {
                    sb.AppendFormat("{0}\r\n", device.ToString());
                }
            }
            return sb.ToString();
        }
    }
}