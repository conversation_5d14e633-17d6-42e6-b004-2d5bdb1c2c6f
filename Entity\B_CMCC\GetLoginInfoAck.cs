﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.6.7.获取FSU注册信息应答
    /// </summary>
    public sealed class GetLoginInfoAck:BMessage
    {
        public string UserName { get; private set; }
        public string PassWord { get; private set; }
        public string FSUIP { get; private set; }
        public string FSUVER { get; private set; }
        public string FSUMAC { get; private set; }
        public string SiteID { get; private set; }
        public string RoomID { get; private set; }

        public string SiteName { get; private set; }
        public string RoomName { get; private set; }

        public EnumResult Result { get; private set; }
        public string FailureCause { get; private set; }

        public GetLoginInfoAck(string userName, string password, string fsuId, string fsuIp, string fsuVer, string fsuMac, string siteId, 
            string roomId, string siteName, string roomName, EnumResult result, string failureCause):base()
        {
            MessageType = (int)BMessageType.GET_LOGININFO_ACK;

            UserName = userName;
            PassWord = password;
            FSUID = fsuId;
            FSUIP = fsuIp;
            FSUVER = fsuVer;
            FSUMAC = fsuMac;
            SiteID = siteId;
            RoomID = roomId;
            SiteName = siteName;
            RoomName = roomName;
            Result = result;
            FailureCause = failureCause;
        }

        public GetLoginInfoAck() : base() 
        {
            MessageType = (int)BMessageType.GET_LOGININFO_ACK;
        }

        public static GetLoginInfoAck Deserialize(XmlDocument xmlDoc)
        {
            GetLoginInfoAck getLoginInfoAck = null;
            try
            {
                string userName = xmlDoc.SelectSingleNode("/Response/Info/UserName").InnerText.Trim();
                ////2016-08-22 按阳老师和教授要求，注册用户密码（MD5加密），在解析后，转成大写
                //string password = xmlDoc.SelectSingleNode("/Response/Info/PassWord").InnerText.Trim().ToUpper();
                //2016-09-03 获取FSU的注册密码时，不转大写，保持原文（未加密）
                string password = xmlDoc.SelectSingleNode("/Response/Info/PassWord").InnerText.Trim();
                string fsuId = xmlDoc.SelectSingleNode("/Response/Info/FSUID").InnerText.Trim();
                string fsuIp = xmlDoc.SelectSingleNode("/Response/Info/FSUIP").InnerText.Trim();
                string fsuVer = xmlDoc.SelectSingleNode("/Response/Info/FSUVER").InnerText.Trim();
                string fsuMac = xmlDoc.SelectSingleNode("/Response/Info/FSUMAC").InnerText.Trim();
                string siteId = xmlDoc.SelectSingleNode("/Response/Info/SiteID").InnerText.Trim();
                string roomId = xmlDoc.SelectSingleNode("/Response/Info/RoomID").InnerText.Trim();

                string siteName = xmlDoc.SelectSingleNode("/Response/Info/SiteName").InnerText.Trim();
                string roomName = xmlDoc.SelectSingleNode("/Response/Info/RoomName").InnerText.Trim();

                string resultString = xmlDoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim();
                string failureCause = xmlDoc.SelectSingleNode("/Response/Info/FailureCause").InnerText.Trim();
                EnumResult result;
                if (string.IsNullOrEmpty(resultString))
                {
                    result = EnumResult.FAILURE;
                }
                else
                {
                    result = (EnumResult)int.Parse(resultString);
                }

                getLoginInfoAck = new GetLoginInfoAck(userName, password, fsuId, fsuIp, fsuVer, fsuMac, siteId, roomId, siteName, roomName, result, failureCause);
                getLoginInfoAck.StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("GetLoginInfo.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                return getLoginInfoAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetLoginInfoAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetLoginInfoAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                getLoginInfoAck = new GetLoginInfoAck();
                getLoginInfoAck.StringXML = xmlDoc.InnerXml;
                getLoginInfoAck.ErrorMsg = ex.Message;
                return getLoginInfoAck;
            } 
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}, {2}:{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13}",
                MessageId, (BMessageType)MessageType, FSUID, UserName, PassWord, FSUIP, FSUVER, FSUMAC, SiteID, RoomID, SiteName, RoomName, Result, FailureCause);
        }

    }
}
