﻿using Delivery.Common;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Text;

namespace Delivery.Common.NoProcedure.Utils
{
    public class CommonUtils
	{
		public const string PARAM_StartTime = "StartTime";
		public const string PARAM_EndTime = "EndTime";


		private static bool? noProcedure = null;
		private static string dbConnectionString = null;
		private static string dbProviderName = null;
		private static string sqlpath = null;

		public static string DbProviderName
		{
			get
			{
				if (dbProviderName == null)
				{
					dbProviderName = ConfigHelper.DefaultNpProviderName;
				}
				return dbProviderName;
			}
		}
		public static string DbConnectionString
		{
			get
			{
				if (dbConnectionString == null)
				{
					dbConnectionString = ConfigHelper.DefaultNpConnectionString;
				}
				return dbConnectionString;
			}
		}
		public static string SqlPath
		{
			get
			{
				if (sqlpath == null)
				{
					sqlpath = ConfigHelper.Sqlpath;
				}
				return sqlpath;
			}
		}

		/// <summary> 是否禁用存储过程 </summary>
		public static bool IsNoProcedure
		{
			get
			{
				if (noProcedure == null || !noProcedure.HasValue)
				{
					string strValue = ConfigHelper.NoProcedure;
					strValue = strValue == null ? "false" : strValue.Trim();
					noProcedure = "true".Equals(strValue.ToLower());
				}
				return noProcedure.Value;
			}
		}

		public static DbType? ConverDbType(string sqlType)
        {
            switch (sqlType)
			{
				case "int": return DbType.Int32;
				case "long": return DbType.Int64;
				case "string": return DbType.String;
				case "datetime": return DbType.DateTime;
				case "float": return DbType.Double;
				case "decimal": return DbType.Decimal;//TODO: 测试经纬度保存是否成功
				default: return null;
            }
        }

		public static List<string> getInStrArray(List<DataRow> rowList, string fieldName, bool isStr, int maxCount = 500)
        {
			if (maxCount < 500) maxCount = 500;
			List<string> result = new List<string>();
			StringBuilder currentStr = new StringBuilder("");
			int currentCount = 0;

			foreach (DataRow row in rowList)
			{
				string fvalue = row[fieldName].ToString();
				if (isStr)
				{
					fvalue = $"'{fvalue}'";
				}

				if (currentCount + 1 > maxCount)
				{
					// 超过长度，将当前字符串加入结果数组，重置当前字符串和长度
					result.Add(currentStr.ToString().TrimEnd(','));
					currentStr = new StringBuilder("");
					currentCount = 0;
				}

				currentStr.Append(fvalue).Append(",");
				currentCount++; //个数累加1
			}

			// 将最后一个字符串加入结果数组
			if(currentStr.Length > 0)
			{
				result.Add(currentStr.ToString().TrimEnd(','));
			} 

			return result;
		}
		// 将 LINQ 查询的结果转换为 DataTable
		public static DataTable ConvertToDataTable<T>(IEnumerable<T> data)
		{
			if (data == null ) return null;
			PropertyInfo[] pInfos = typeof(T).GetProperties();
			int pLen = pInfos.Length;//属性的数量
			Type[] pTypes = new Type[pLen];//用于保存各属性的类型

			DataTable table = new DataTable();
			for(int i = 0; i < pLen; i++)
            {
				PropertyInfo prop = pInfos[i];
				// 检查属性类型是否为可空整数
				Type columnType = Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType;

				// 将列添加到 DataTable
				table.Columns.Add(prop.Name, columnType);

				// 保存各字段数据类型
				pTypes[i] = columnType;
			}

			foreach (var item in data)
			{
				DataRow row = table.NewRow();
				for (int i = 0; i < pLen; i++)
                {
					PropertyInfo prop = pInfos[i];
					// 获取属性类型
					Type columnType = pTypes[i];
					// 获取属性的值
					object value = prop.GetValue(item);
					// 处理可空类型的情况
					if (value == null)
					{
						row[prop.Name] = DBNull.Value; // 将 DBNull.Value 赋给可空整数的列
					}
					else
					{
						// 使用 Convert.ChangeType 处理类型转换
						row[prop.Name] = Convert.ChangeType(value, columnType);
					}
				}
				table.Rows.Add(row);
			}

			return table;
		}
		/// <summary> 在用作数据库参数时，对可为空类型（T?）作统一处理，如果为null则返回 DBNull.Value </summary>
		public static object GetNullableValue<T>(T? source) where T : struct
		{
			if (source.HasValue) 
				return source.Value;
			else
				return DBNull.Value;
		}
		/// <summary> 在用作数据库参数时，对非?类型作统一处理，如果为null则返回 DBNull.Value </summary>
		public static object GetNullableValue<T>(T source)
		{
			if (source == null)
				return DBNull.Value;
			else
				return source;
        }
	}
}
