﻿using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace Delivery.Common.NoProcedure.Service
{
    public class SP_WRStationService
    {
        private static SP_WRStationService _instance = null;

        public static SP_WRStationService Instance
        {
            get
            {
                if (_instance == null) _instance = new SP_WRStationService();
                return _instance;
            }
        }

        public DataTable GetWRStation(string structureId, string StationName, string StationCode, string strStartTime, string strEndTime, string stationCategory, string stationStatus, string LogonId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    strEndTime = strEndTime ?? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    DateTime endDateTime = DateTime.Parse(strEndTime);
                    strStartTime = strStartTime ?? endDateTime.AddMonths(-1).ToString("yyyy-MM-dd HH:mm:ss");
                    DateTime startDateTime = DateTime.Parse(strStartTime);

                    if (endDateTime < startDateTime)
                        return null;
                    string WhereTime = string.Format(" and a.ApplyTime >= '{0}' and a.ApplyTime <= '{1}'", strStartTime, strEndTime);
                    realParams.Add("WhereTime", WhereTime);
                    if (structureId != null && !structureId.Equals("-1")) {
                        string whereStructureId = string.Format(" and b.LevelPath LIKE '%{0}%'", structureId);
                        realParams.Add("WhereStructureId", whereStructureId);
                    }
                    if (stationCategory != null && !stationCategory.Equals("-1")) {
                        string WhereStationCategory = string.Format("and a.StationCategory ='{0}'", stationCategory);
                        realParams.Add("WhereStationCategory", WhereStationCategory);
                    }

                    if (!string.IsNullOrEmpty(StationName))
                    {
                        StationName = StationName.Trim();
                        string WhereStationName = string.Format("and a.StationName LIKE '%{0}%'", StationName);
                        string WhereSWStationName = string.Format("and i.StationName LIKE '%{0}%'", StationName);

                        realParams.Add("WhereStationName", WhereStationName);
                        realParams.Add("WhereSWStationName", WhereSWStationName);
                    }

                    
                    if (!string.IsNullOrEmpty(StationCode))
                    {
                        StationCode = StationCode.Trim();
                        string WhereStationCode = string.Format("and a.StationCode LIKE '%{0}%'", StationCode);
                        realParams.Add("WhereStationCode", WhereStationCode);
                    }

                    if (stationStatus != null && !stationStatus.Equals("-1"))
                    {
                        string WhereStatus = string.Format("and a.StationStatus ='{0}'", stationStatus);
                        realParams.Add("WhereStatus", WhereStatus);
                    }

                    realParams.Add("LogonId", LogonId);
                    //获取用户权限
                    object userIdobj =  executeHelper.ExecuteScalar("SP_WRStation_CUCCGetStation_GetUserId", realParams);
                    object obj = CommonUtils.GetNullableValue(userIdobj);
                    string userIdStr = obj == DBNull.Value ? null : obj.ToString();

                    realParams.Add("userId", userIdStr);
                    //是否管理员
                    object judgeRoleIdExist = executeHelper.ExecuteScalar("SP_WRStation_CUCCGetStation_JudgeExist", realParams);
                    bool isAdmin = false;
                    if (judgeRoleIdExist != DBNull.Value && judgeRoleIdExist != null)
                    {
                        isAdmin = true;
                    }

                    //获取sCenterId,CenterName 值
                    DataTable filedValueDt = executeHelper.ExecDataTable("SP_WRStation_CUCCGetStation_GetFiledValue", null);
                    string sCenterId, CenterName;
                    if (filedValueDt != null && filedValueDt.Rows.Count > 0) { 
                        sCenterId = filedValueDt.Rows[0].Field<string>("sCenterId");
                        CenterName = filedValueDt.Rows[0].Field<string>("CenterName");
                        realParams.Add("sCenterId", sCenterId);
                        realParams.Add("CenterName", CenterName);
                    }

                    DataTable resultTable1, resultTable2;
                    if (isAdmin) {
                        resultTable1 = executeHelper.ExecDataTable("SP_WRStation_CUCCGetStation_GetResultTable1_IsAdmin", realParams);
                        resultTable2 = executeHelper.ExecDataTable("SP_WRStation_CUCCGetStation_GetResultTable2_IsAdmin", realParams);
                    }
                    else {
                        resultTable1 = executeHelper.ExecDataTable("SP_WRStation_CUCCGetStation_GetResultTable1_NotAdmin", realParams);
                        resultTable2 = executeHelper.ExecDataTable("SP_WRStation_CUCCGetStation_GetResultTable2_NotAdmin", realParams);
                    }

                    List<DataRow> rows = resultTable2.AsEnumerable().ToList();
                    rows.AddRange(resultTable1.AsEnumerable());
                    if (rows.Count <= 0)
                        return new DataTable();
                    resultTable1 = rows.CopyToDataTable();
                    int index = 0;
                    // 使用 LINQ 查询实现类似 ROW_NUMBER() over(order by ApplyTime DESC) 的功能
                    var query = resultTable1.AsEnumerable()
                              .OrderByDescending(row => row.Field<DateTime?>("ApplyTime"))
                              .Select(row => new
                              {
                                  RowNumber = ++index,
                                  CenterId = row.Field<string>("CenterId"),
                                  CenterName = row.Field<string>("CenterName"),
                                  WRStationId = row.Field<int?>("WRStationId"),
                                  StructureId = row.Field<int?>("StructureId"),
                                  StructureName = row.Field<string>("StructureName"),
                                  StationCode = row.Field<string>("StationCode"),
                                  StationName = row.Field<string>("StationName"),
                                  StationCategory = row.Field<int?>("StationCategory"),
                                  StationCategoryName = row.Field<string>("StationCategoryName"),
                                  StationStatus = row.Field<int?>("StationStatus"),
                                  StatusName = row.Field<string>("StatusName"),
                                  Address = row.Field<string>("Address"),
                                  UserId = row.Field<int?>("UserId"),
                                  UserName = row.Field<string>("UserName"),
                                  ApplyTime = row.Field<DateTime?>("ApplyTime"),
                                  ApproveTime = row.Field<DateTime?>("ApproveTime"),
                                  Province = row.Field<int?>("Province"),
                                  ProvinceName = row.Field<string>("ProvinceName"),
                                  City = row.Field<int?>("City"),
                                  CityName = row.Field<string>("CityName"),
                                  County = row.Field<int?>("County"),
                                  CountyName = row.Field<string>("CountyName"),
                                  SWStationId = row.Field<int?>("SWStationId"),
                                  RejectCause = row.Field<string>("RejectCause"),
                                  Remark = row.Field<string>("Remark"),
                                  Latitude = row.Field<string>("Latitude"),
                                  Longitude = row.Field<string>("Longitude"),
                                  ContractNo = row.Field<string>("ContractNo"),
                                  ProjectName = row.Field<string>("ProjectName"),
                                  StationRId = row.Field<string>("StationRId"),

                              });
                    DataTable res = CommonUtils.ConvertToDataTable(query);
                    return res;
                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    return null;
                }
                
            }
        }

        public DataTable GetWRStationConditionCUCC(string structureId, string StationName, string StationCode, string strStartTime, string strEndTime, string stationCategory)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    strEndTime = strEndTime ?? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    DateTime endDateTime = DateTime.Parse(strEndTime);
                    strStartTime = strStartTime ?? endDateTime.AddMonths(-1).ToString("yyyy-MM-dd HH:mm:ss");
                    DateTime startDateTime = DateTime.Parse(strStartTime);
                    string WhereTime = string.Format(" and a.ApplyTime >= '{0}' and a.ApplyTime <= '{1}'", strStartTime, strEndTime);
                    realParams.Add("WhereTime", WhereTime);

                    if (structureId != null && !structureId.Equals("-1"))
                    {
                        string whereStructureId = string.Format(" and b.LevelPath LIKE '%{0}%'", structureId);
                        realParams.Add("WhereStructureId", whereStructureId);
                    }
                    if (stationCategory != null && !stationCategory.Equals("-1"))
                    {
                        string WhereStationCategory = string.Format("and a.StationCategory ='{0}'", stationCategory);
                        realParams.Add("WhereStationCategory", WhereStationCategory);
                    }

                    if (!string.IsNullOrEmpty(StationName))
                    {
                        StationName = StationName.Trim();
                        string WhereStationName = string.Format("and a.StationName LIKE '%{0}%'", StationName);
                        string WhereSWStationName = string.Format("and i.StationName LIKE '%{0}%'", StationName);

                        realParams.Add("WhereStationName", WhereStationName);
                        realParams.Add("WhereSWStationName", WhereSWStationName);
                    }

                    if (!string.IsNullOrEmpty(StationCode))
                    {
                        StationCode = StationCode.Trim();
                        string WhereStationCode = string.Format("and a.StationCode LIKE '%{0}%'", StationCode);
                        realParams.Add("WhereStationCode", WhereStationCode);
                    }

                    //获取sCenterId,CenterName 值
                    DataTable filedValueDt = executeHelper.ExecDataTable("SP_WRStation_CUCCGetStation_GetFiledValue", null);
                    string sCenterId, CenterName;
                    if (filedValueDt != null && filedValueDt.Rows.Count > 0)
                    {
                        sCenterId = filedValueDt.Rows[0].Field<string>("sCenterId");
                        CenterName = filedValueDt.Rows[0].Field<string>("CenterName");
                        realParams.Add("sCenterId", sCenterId);
                        realParams.Add("CenterName", CenterName);
                    }
                    DataTable resultTable1 = executeHelper.ExecDataTable("SP_WRStation_CUCCGetStationCondition_GetResultTable_Audited", realParams);
                    DataTable resultTable2 = executeHelper.ExecDataTable("SP_WRStation_CUCCGetStationCondition_GetResultTable_NotAudited", realParams);

                    List<DataRow> rows = resultTable2.AsEnumerable().ToList();
                    rows.AddRange(resultTable1.AsEnumerable());
                    if (rows.Count <= 0)
                        return new DataTable();
                    resultTable1 = rows.CopyToDataTable();
                    int index = 0;
                    // 使用 LINQ 查询实现类似 ROW_NUMBER() over(order by ApplyTime DESC) 的功能
                    var query = resultTable1.AsEnumerable()
                              .OrderByDescending(row => row.Field<DateTime?>("ApplyTime"))
                              .Select(row => new
                              {
                                  RowNumber = ++index,
                                  CenterId = row.Field<string>("CenterId"),
                                  CenterName = row.Field<string>("CenterName"),
                                  WRStationId = row.Field<int?>("WRStationId"),
                                  StructureId = row.Field<int?>("StructureId"),
                                  StructureName = row.Field<string>("StructureName"),
                                  StationCode = row.Field<string>("StationCode"),
                                  StationName = row.Field<string>("StationName"),
                                  StationCategory = row.Field<int?>("StationCategory"),
                                  StationCategoryName = row.Field<string>("StationCategoryName"),
                                  StationStatus = row.Field<int?>("StationStatus"),
                                  StatusName = row.Field<string>("StatusName"),
                                  Address = row.Field<string>("Address"),
                                  UserId = row.Field<int?>("UserId"),
                                  UserName = row.Field<string>("UserName"),
                                  ApplyTime = row.Field<DateTime?>("ApplyTime"),
                                  ApproveTime = row.Field<DateTime?>("ApproveTime"),
                                  Province = row.Field<int?>("Province"),
                                  ProvinceName = row.Field<string>("ProvinceName"),
                                  City = row.Field<int?>("City"),
                                  CityName = row.Field<string>("CityName"),
                                  County = row.Field<int?>("County"),
                                  CountyName = row.Field<string>("CountyName"),
                                  SWStationId = row.Field<int?>("SWStationId"),
                                  RejectCause = row.Field<string>("RejectCause"),
                                  Remark = row.Field<string>("Remark"),
                                  ContractNo = row.Field<string>("ContractNo"),
                                  ProjectName = row.Field<string>("ProjectName"),

                              });
                    DataTable res = CommonUtils.ConvertToDataTable(query);
                    return res;
                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    return null;
                }
            }
        }

        public DataTable UpdWRStationCUCC(string WRStationId, string StructureId, string Province, string City, string County, string StationCategory, string StationRId, string StationName, string Address, string Remark, string ContractNo, string ProjectName, string LoginId)
        {
            if (WRStationId == null)
                return null;

            using (DbHelper dbHelper = new DbHelper())
            {
                //返回字段表
                DataTable ReturnTable = new DataTable();
                DataColumn newColumn = new DataColumn("ReturnValue", typeof(int));
                ReturnTable.Columns.Add(newColumn);
                DataRow newRow = ReturnTable.NewRow();

                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    //定义字段
                    int? iStatus = null, SWStationId = null, OriCounty = null, OriStationCategory = null, SWStationCategory = null, OriStationStructureId = null ;
                    string StnCode = null,UpdateString = null,LastString = null;

                    realParams.Add("WRStationId", CommonUtils.GetNullableValue(WRStationId));
                    realParams.Add("StationName", CommonUtils.GetNullableValue(StationName));
                    object judgeExist1 = executeHelper.ExecuteScalar("SP_WRStation_UpdWRStationCUCC_JudgeExist1",realParams);
                    if(judgeExist1 != null && judgeExist1 != DBNull.Value)
                    {
                        newRow["ReturnValue"] = -1;
                        ReturnTable.Rows.Add(newRow);
                        return ReturnTable;
                    }

                    //获取部分字段值
                    DataTable filedValueDt = executeHelper.ExecDataTable("SP_WRStation_UpdWRStationCUCC_GetFiledValue1",realParams);
                    if(filedValueDt != null && filedValueDt.Rows.Count > 0)
                    {
                        iStatus = filedValueDt.Rows[0].Field<int?>("iStatus");
                        StnCode = filedValueDt.Rows[0].Field<string>("StnCode");
                        SWStationId = filedValueDt.Rows[0].Field<int?>("SWStationId");
                        OriCounty = filedValueDt.Rows[0].Field<int?>("OriCounty");
                        OriStationCategory = filedValueDt.Rows[0].Field<int?>("OriStationCategory");
                    }
                    realParams.Add("SWStationId", CommonUtils.GetNullableValue(SWStationId));
                    realParams.Add("StnCode", CommonUtils.GetNullableValue(StnCode));

                    object LastStringObj = executeHelper.ExecuteScalar("SP_WRStation_UpdWRStationCUCC_GetFiledValue2", realParams);
                    if (LastStringObj != null && LastStringObj != DBNull.Value) 
                    {
                        LastString = LastStringObj.ToString();
                    }
                    UpdateString = $"StructureId:{StructureId}-StationName:{StationName}-StationCategory:{StationCategory}-Address:{Address}-Remark:{Remark}-ContractNo:{ContractNo}-ProjectName:{ProjectName}-StationRId:{StationRId}";
                    //调用CALL SP_WR_OperationRecord (WRStationId,StationName,3,WRStationId,StationName,UpdateString,LastString,LogonId);


                    realParams.Add("StructureId", CommonUtils.GetNullableValue(StructureId));
                    realParams.Add("StationCategory", CommonUtils.GetNullableValue(StationCategory));
                    realParams.Add("Province", CommonUtils.GetNullableValue(Province));
                    realParams.Add("City", CommonUtils.GetNullableValue(City));
                    realParams.Add("County", CommonUtils.GetNullableValue(County));
                    realParams.Add("Address", CommonUtils.GetNullableValue(Address));
                    realParams.Add("Remark", CommonUtils.GetNullableValue(Remark));
                    realParams.Add("ContractNo", CommonUtils.GetNullableValue(ContractNo));
                    realParams.Add("ProjectName", CommonUtils.GetNullableValue(ProjectName));
                    realParams.Add("StationRId", CommonUtils.GetNullableValue(StationRId));
                    //根据StationStatus分叉
                    if (iStatus != null && iStatus == 3)
                    {
                        //更新WR_StationManagementCUCC
                        executeHelper.ExecuteNonQuery("SP_WRStation_UpdWRStationCUCC_UpdateWRStationManagementCUCC_IStatusIsThree",realParams);

                        //获取SWStationCategory字段
                        Object SWStationCategoryobj = executeHelper.ExecuteScalar("SP_WRStation_UpdWRStationCUCC_GetFiledValue3",realParams); 
                        if(SWStationCategoryobj!=DBNull.Value && SWStationCategoryobj != null)
                        {
                            SWStationCategory = int.Parse(SWStationCategoryobj.ToString());
                        }
                        if (SWStationCategory == null) 
                        {
                            executeHelper.Rollback();
                            newRow["ReturnValue"] = -4;
                            ReturnTable.Rows.Add(newRow);
                            return ReturnTable;
                        }
                        realParams.Add("SWStationCategory", SWStationCategory);

                        //更新TBL_Station
                        executeHelper.ExecuteNonQuery("SP_WRStation_UpdWRStationCUCC_UpdateTBLStation_IStatusIsThree", realParams);

                        //同步更新SiteWeb站址所属片区
                        //获取OriStationStructureId
                        Object OriStationStructureIdObj = executeHelper.ExecuteScalar("SP_WRStation_UpdWRStationCUCC_GetFiledValue4", realParams);
                        if (OriStationStructureIdObj != DBNull.Value && OriStationStructureIdObj != null)
                        {
                            OriStationStructureId = int.Parse(OriStationStructureIdObj.ToString());
                        }
                        if (OriStationStructureId != int.Parse(StructureId)) {
                            realParams.Add("OriStationStructureId", OriStationStructureId);
                            //TBL_StationStructureMap
                            executeHelper.ExecuteNonQuery("SP_WRStation_UpdWRStationCUCC_UpdateTBLStationStructureMap_IStatusIsThree", realParams);
                        }
                        //同步更新SiteWeb站址工程名和合同号
                        executeHelper.ExecuteNonQuery("SP_WRStation_UpdWRStationCUCC_UpdateTBLStationProjectInfo_IStatusIsThree", realParams);
                        executeHelper.ExecuteNonQuery("SP_WRStation_UpdWRStationCUCC_UpdateTBLConfigChangeMicroLog_IStatusIsThree", realParams);
                        executeHelper.ExecuteNonQuery("SP_WRStation_UpdWRStationCUCC_UpdateTBLConfigChangeMacroLog_IStatusIsThree", realParams);
                    }
                    else
                    {
                        if (OriCounty != int.Parse(County) || OriStationCategory != int.Parse(StationCategory)) 
                        {
                            StnCode = null;
                            //调用CALL SP_GenerateStationCode(County, StationCategory, StnCode );
                            Public_StoredService.Instance.SP_GenerateStationCode(int.Parse(County), int.Parse(StationCategory), out StnCode);
                            if (StnCode == null ) {
                                executeHelper.Rollback();
                                newRow["ReturnValue"] = -2;
                                ReturnTable.Rows.Add(newRow);
                                return ReturnTable;
                            }
                        }
                        //更新WR_StationManagementCUCC
                        executeHelper.ExecuteNonQuery("SP_WRStation_UpdWRStationCUCC_UpdateWRStationManagementCUCC_IStatusNotThree", realParams);
                    }
                    executeHelper.Commit();
                }
                catch (Exception ex)
                {
                    executeHelper.Rollback();
                    Logger.Log(ex);
                    newRow["ReturnValue"] = -1;
                    ReturnTable.Rows.Add(newRow);
                    return ReturnTable;
                }
                newRow["ReturnValue"] = 1;
                ReturnTable.Rows.Add(newRow);
                return ReturnTable;                 
            }
        }
    }
}
