﻿
using DM.TestOrder.Common.DBA;


using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using DM.TestOrder.Common;
using DM.TestOrder.Entity;

namespace DM.TestOrder.DAL.Helper
{
    public class TBL_StationHelper
    {
        public static TBL_Station FromDataRow(DataRow row) {
            var e = new TBL_Station();
            try {
                e.StationId = SHelper.ToInt(row["StationId"]);
                e.StationName = row["StationName"].ToString();
                e.Latitude = SHelper.ToDecimal(row["Latitude"]);
                e.Longitude = SHelper.ToDecimal(row["Longitude"]);
                e.StationCategory = SHelper.ToInt(row["StationCategory"]);
                e.StationState = SHelper.ToInt(row["StationState"]);
                e.CenterId = SHelper.ToInt(row["CenterId"]);
            }
            catch (Exception ex) {
                MyLogger.ErrorFormat("TBL_StationHelper.FromDataRow();error={0}", ex.Message);
                MyLogger.Error(ex.StackTrace);
                return null;
            }
            return e;
        }
    }
}
