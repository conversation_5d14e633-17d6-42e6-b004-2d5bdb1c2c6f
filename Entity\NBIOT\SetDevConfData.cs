﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class SetDevConfData : BMessage
    {
        public SetDevConfData() : base()
        {
            MessageType = (int)BMessageType.SET_DEV_CONF_DATA;
        }

        public SetDevConfData(string fsuId, List<TThreshold> values) : base()
        {
            MessageType = (int)BMessageType.SET_DEV_CONF_DATA;
            FSUID = fsuId;
            Values = values;
        }

        [Newtonsoft.Json.JsonProperty(Order = 1)]
        public List<TThreshold> Values { get; set; }
    }
}