﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <procedures>
    <procedure owner="" name="PCT_DeleteMonitorUnit_ProjectInfo" grant="">
      <parameters>
        <parameter name="MonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
          DELETE FROM TBL_MonitorUnitProjectInfo WHERE MonitorUnitId=@MonitorUnitId;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="PCT_DeleteMonitorUnit_EquipmentProjectInfo" grant="">
      <parameters>
        <parameter name="MonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
          DELETE FROM TBL_EquipmentProjectInfo WHERE MonitorUnitId=@MonitorUnitId;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="PCT_DeleteMonitorUnit_Equipment" grant="">
      <parameters>
        <parameter name="MonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
          DELETE FROM TBL_Equipment WHERE MonitorUnitId=@MonitorUnitId;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="PCT_DeleteMonitorUnit_Signal" grant="">
      <parameters>
        <parameter name="MonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
          DELETE FROM TSL_MonitorUnitSignal WHERE MonitorUnitId=@MonitorUnitId;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="PCT_DeleteMonitorUnit_Event" grant="">
      <parameters>
        <parameter name="MonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
          DELETE FROM TSL_MonitorUnitEvent WHERE MonitorUnitId=@MonitorUnitId;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="PCT_DeleteMonitorUnit_Control" grant="">
      <parameters>
        <parameter name="MonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
          DELETE FROM TSL_MonitorUnitControl WHERE MonitorUnitId=@MonitorUnitId;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="PCT_DeleteMonitorUnit_SamplerUnit" grant="">
      <parameters>
        <parameter name="MonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
          DELETE FROM TSL_SamplerUnit WHERE MonitorUnitId=@MonitorUnitId;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="PCT_DeleteMonitorUnit_Port" grant="">
      <parameters>
        <parameter name="MonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
          DELETE FROM TSL_Port WHERE MonitorUnitId=@MonitorUnitId;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="PCT_DeleteMonitorUnit_getStationId" grant="">
      <parameters>
        <parameter name="MonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
          SELECT t.StationId vStationId FROM TSL_MonitorUnit t WHERE t.MonitorUnitId = @MonitorUnitId;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="PCT_DeleteMonitorUnit_MonitorUnit" grant="">
      <parameters>
        <parameter name="MonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
          DELETE FROM TSL_MonitorUnit WHERE MonitorUnitId=@MonitorUnitId;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="PCT_DeleteMonitorUnit_getCount" grant="">
      <parameters>
        <parameter name="vStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
          SELECT COUNT(t.ConnectState) FROM TSL_MonitorUnit t
	        WHERE t.StationId = @vStationId AND t.ConnectState <> 1;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="PCT_DeleteMonitorUnit_updateStation" grant="">
      <parameters>
        <parameter name="vStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
          UPDATE TBL_Station SET ConnectState = 1 WHERE StationId = @vStationId;
			]]>
      </body>
    </procedure>
  </procedures>
</root>
