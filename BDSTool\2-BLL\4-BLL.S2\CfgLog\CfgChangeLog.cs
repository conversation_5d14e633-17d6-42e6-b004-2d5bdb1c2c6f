﻿
using BDSTool.DBUtility.Common;
using Common.Logging.Pro;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.BLL.S2
{
    public class CfgChangeLog
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");
        /// <summary>
        /// 记录配置变化日志
        /// </summary>
        /// <param name="op"></param>
        public static void LogConfigChange(IDataOperation op) {
            string entityName = op.GetType().Name;
            //List<KoloData.TBL_ConfigChangeDefineRow> rows = new List<KoloData.TBL_ConfigChangeDefineRow>();
            //rows.AddRange(PublicVar.DefaultData.TBL_ConfigChangeDefine.Where(s => s.EntityName == entityName));
            ////查找该对象是否需要记录日志，如果不需要或记录有错误则返回
            //if (rows.Count != 1) return;
            var tbcfgChangeDefine = GetConfigChangeDefine(entityName);
            if (tbcfgChangeDefine==null || tbcfgChangeDefine.Rows.Count == 0)
                return;

            //获取存储数据所使用的各种信息
            var r = tbcfgChangeDefine.Rows[0];
            int configId = int.Parse(r["ConfigId"].ToString());
            string idDefine = r["IdDefine"].ToString();



            int editType = (int)op.EditType;
            string objectId = GetObjectId(op, idDefine);

            var sql = string.Format("PBL_ConfigChangeLog '{0}',{1},{2}", objectId, configId, editType);
            //DBHelper.ExecuteNonQuery(sql);
            try
            {
                using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                {
                    if(CommonUtils.IsNoProcedure)
                    {
                        PblConfigChangeLogService.Instance.DoExecute(objectId, configId, editType);
                        return;
                    }
                    dbHelper.CreateProcedureCommand("PBL_ConfigChangeLog");
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("ObjectId", DbType.AnsiString, objectId));
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("ConfigId", DbType.Int32, configId));
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("EditType", DbType.Int32, editType));
                    dbHelper.AddDbParameter(dbHelper.GetReturnDbParameter());
                    dbHelper.ExecuteNoQuery();
                }
            }
            catch (Exception ex)
            {
                DBHelper.LogError(sql, ex);
            }
        }



        //查询TBL_ConfigChangeDefine表
        private static DataTable GetConfigChangeDefine(string entityName) {
            try {
                DataTable rtn = null;
                if (CommonUtils.IsNoProcedure)
                {
                    rtn = Public_ExecuteSqlService.Instance.NoStore_TBLConfigChangeDefine_GetByEntityName(entityName);
                }
                else
                {
                    string sql = string.Format(@"SELECT * FROM TBL_ConfigChangeDefine WHERE EntityName='{0}'", entityName);
                    rtn = DBHelper.GetTable(sql);
                }
                return rtn;
            }
            catch (Exception ex) {
                logger.ErrorFormat("ExistAnyHouse();Error={0}", ex.Message);
                logger.Error(ex.StackTrace);
                return null;
            }
        }

        /// <summary>
        /// 根据ID定义反射获取对应的ID String
        /// </summary>
        /// <param name="op"></param>
        /// <param name="idDefine"></param>
        /// <returns></returns>
        private static string GetObjectId(IDataOperation op, string idDefine) {
            string[] ids = idDefine.Split('.');
            if (ids.Length == 0)
                return null;

            StringBuilder sb = new StringBuilder();
            foreach (string s in ids) {
                if (string.IsNullOrEmpty(s))
                    continue;
                sb.Append(op.GetType().GetProperty(s.Trim()).GetValue(op, null).ToString());
                sb.Append(".");
            }

            return sb.ToString().Substring(0, sb.Length - 1);
        }
    }
}
