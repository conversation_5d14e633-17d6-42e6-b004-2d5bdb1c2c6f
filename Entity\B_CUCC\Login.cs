﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{

    /// <summary>
    /// ********　SU向SC注册
    /// </summary>
    public sealed class Login : BMessage
    {
        public string UserName { get; private set; }

        public string PassWord { get; private set; }

        public string SUIP { get; private set; }

        public List<Device> DeviceList { get; set; }

        /// <summary>
        /// device 的 Id 和 RId 组合字符串,格式: Id1,RId1|Id2,RId2|Id3,RId3|...
        /// </summary>
        public string StrDeviceList { get; set; }

        public string SUVer { get; private set; }

        public string SUPort { get; private set; }

        public string SUVendor { get; private set; }

        public string SUModel { get; private set; }

        public string SUHardVer { get; private set; }

        public string SUConfigTime { get; private set; }

        //public int MyMonitoringUnitId { get; set; }

        public Login() : base() 
        {
            MessageType = (int)BMessageType.LOGIN;
        }

        public Login(string userName, string password, string suId, string surId, string suIp, List<Device> deviceList, string suVer
            , string suPort, string suVendor, string suModel, string suHardVer)
            : base()
        {
            MessageType = (int)BMessageType.LOGIN;
            UserName = userName;
            PassWord = password;
            SUId = suId;
            SURId = surId;
            SUIP = suIp;
            DeviceList = deviceList;
            SUVer = suVer;
            SUPort = suPort;
            SUVendor = suVendor;
            SUModel = suModel;
            SUHardVer = suHardVer;
        }

        public Login(string userName, string password, string suId, string surId, string suIp, List<Device> deviceList, string suVer
            , string suPort, string suVendor, string suModel, string suHardVer, string suConfigTime, string strDeviceList)
            : base()
        {
            MessageType = (int)BMessageType.LOGIN;
            UserName = userName;
            PassWord = password;
            SUId = suId;
            SURId = surId;
            SUIP = suIp;
            DeviceList = deviceList;
            SUVer = suVer;
            SUPort = suPort;
            SUVendor = suVendor;
            SUModel = suModel;
            SUHardVer = suHardVer;
            SUConfigTime = suConfigTime;
            StrDeviceList = strDeviceList;
        }

        public static Login Deserialize(XmlDocument xmlDoc)
        {
            Login login = null;
            string errMsg = string.Empty;
            try
            {
                string userName = xmlDoc.SelectSingleNode("/Request/Info/UserName").InnerText.Trim();
                string password = xmlDoc.SelectSingleNode("/Request/Info/PassWord").InnerText.Trim();
                string suId = xmlDoc.SelectSingleNode("/Request/Info/SUId").InnerText.Trim();
                string surId = xmlDoc.SelectSingleNode("/Request/Info/SURId").InnerText.Trim();
                string suIp = xmlDoc.SelectSingleNode("/Request/Info/SUIP").InnerText.Trim();
                string suVer = xmlDoc.SelectSingleNode("/Request/Info/SUVer").InnerText.Trim();

                string suPort = xmlDoc.SelectSingleNode("/Request/Info/SUPort").InnerText.Trim();
                string suVendor = xmlDoc.SelectSingleNode("/Request/Info/SUVendor").InnerText.Trim();
                string suModel = xmlDoc.SelectSingleNode("/Request/Info/SUModel").InnerText.Trim();
                string suHardVer = xmlDoc.SelectSingleNode("/Request/Info/SUHardVer").InnerText.Trim();
                string suConfigTime = xmlDoc.SelectSingleNode("/Request/Info/SUConfigTime").InnerText.Trim();

                List<Device> deviceList = null;
                string strDeviceIds = null;
                if (string.IsNullOrEmpty(suIp))
                {
                    errMsg = "LOGIN SUIP Value is nullOrEmpty";
                }
                else
                {
                    XmlNodeList nodelist = xmlDoc.SelectNodes("/Request/Info/DeviceList/Device");
                    deviceList = new List<Device>();
                    foreach (XmlNode nodeDevice in nodelist)
                    {
                        string deviceId = nodeDevice.Attributes["Id"].Value.Trim();
                        string deviceRId = nodeDevice.Attributes["RId"].Value.Trim();
                        string deviceIds = deviceId + "," + deviceRId;
                        Device device = new Device(deviceId, deviceRId);
                        deviceList.Add(device);
                        if (string.IsNullOrEmpty(strDeviceIds))
                        {
                            strDeviceIds = deviceIds;
                        }
                        else
                        {
                            strDeviceIds = strDeviceIds + "|" + deviceIds;
                        }
                    }
                }

                if (string.IsNullOrEmpty(errMsg))
                {
                    login = new Login(userName, password, suId, surId, suIp, deviceList, suVer, suPort, suVendor, suModel, suHardVer, suConfigTime, strDeviceIds);
                }
                else
                {
                    entityLogger.ErrorFormat("Login.Deserialize():{0}", errMsg);
                    login = GetErrorEntity(errMsg);
                }
                entityLogger.DebugFormat("Login.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                login.StringXML = xmlDoc.InnerXml;
                return login;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("Login.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("Login.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                login = GetErrorEntity(ex.Message);
                login.StringXML = xmlDoc.InnerXml;
                return login;
            }
        }
        private static Login GetErrorEntity(string errorMsg)
        {
            Login errlogin = new Login();
            LoginAck ackEntiy = new LoginAck(EnumRightMode.INVALID);
            ackEntiy.ErrorMsg = errorMsg;
            errlogin.ErrorMsg = ackEntiy.Serialize();
            return errlogin;
        }
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append(String.Format("{0}, {1}: {2}, {3}, {4}, {5}, {6}, {7}, {8}, {9}, {10},{11},{12}",
                MessageId, (BMessageType)MessageType, UserName.Trim(), PassWord.Trim(), SUId, SURId
                , SUIP, SUVer, SUPort, SUVendor, SUModel, SUHardVer, SUConfigTime));
            sb.Append("{");
            foreach(Device device in DeviceList)
            {
                sb.Append("[").Append(device.ToString()).Append("]");
            }
            sb.Append("}");
            return sb.ToString();
        }

    }
}
