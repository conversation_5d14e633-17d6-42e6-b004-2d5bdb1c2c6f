﻿
using Common.Logging.Pro;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;



using BDSTool.Entity.S2;
using BDSTool.DBUtility.Common;
using Carrier.BDSTool;
using System.Data;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace BDSTool.BLL.S2 {
    public class EventCategoryMapBiz {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static bool IsNeedSetEventKind() {
            try {
                string sql = null;
                if (CommonUtils.IsNoProcedure)
                {
                    sql = Public_ExecuteSqlService.Instance.NoStore_sysobjects_GetSql();
                }
                else 
                {
                    sql= "select 1 from sysobjects where name ='WO_BSetEventCategory'";
                }
                DbConfigPara para = DBHelper.GetDbConfig();
                    if (para.ProviderName.ToLower() == "mysql")
                    {
                        if (CommonUtils.IsNoProcedure)
                        {
                            sql = Public_ExecuteSqlService.Instance.NoStore_TABLES_GetSql();
                    }
                        else 
                        {
                            sql = "select  TABLE_NAME  from  INFORMATION_SCHEMA.TABLES  where TABLE_NAME ='WO_BSetEventCategory'";
                        }
                    }

                    var s = DBHelper.ExecuteScalar(sql);
                    if (string.IsNullOrEmpty(s))
                        return false;
                    else
                        return true;
            }
            catch (Exception ex) {
                logger.ErrorFormat("IsNeedSetEventKind();Error={0}", ex.Message);
                return false;
            }
        }

        public static void UpdateSiteWebEventCategoryId(int EquipmentTemplateId) {
            try {
                if (CommonUtils.IsNoProcedure)
                {
                    int resultCount = EventCategoryService.Instance.UpdateEventCateory(EquipmentTemplateId);
                        if (resultCount > 0)
                            logger.InfoFormat("UpdateSiteWebEventCategoryId(); ###; Gao预制SiteWeb事件类别; updatCount={0}", resultCount); 
                    return;
                }
                var sql = string.Format("WO_BSetEventCategory {0}", EquipmentTemplateId);
                //var rtn = DBHelper.ExecuteScalar(sql);
                object rtn = null;
                try
                {
                    using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                    {
                        dbHelper.CreateProcedureCommand("WO_BSetEventCategory");
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("EquipmentTemplateId", DbType.Int32, EquipmentTemplateId));
                        rtn = dbHelper.ExecuteScalar();
                    }
                }
                catch (Exception ex)
                {
                    DBHelper.LogError(sql, ex);
                }
               
                if (!(rtn==null)){
                    var updatCount = int.Parse(rtn.ToString());
                    if (updatCount>0)
                        logger.InfoFormat("UpdateSiteWebEventCategoryId(); ###; Gao预制SiteWeb事件类别; updatCount={0}", updatCount);
                }
                    
            }
            catch (Exception ex) {
                logger.ErrorFormat("UpdateSiteWebEventCategoryId();EquipmentTemplateId={0};Error={1}", EquipmentTemplateId, ex.Message);
            }
        }
    }
}
