﻿
using Common.Logging.Pro;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.BLL.S2
{
    public enum EnumOfOpConfigId
    {
        CfgStationStructureOP = 0,
        CfgStationOP = 1,
        CfgEquipmentOP = 3,
        CfgHouseOP = 5,

        EquipmentTemplateOP = 6,
        EquipmentSignalOP = 7,
        SignalMeaningsOP = 8,
        EquipmentSignalPropertyOP = 9,

        EquipmentEventOP = 10,
        EventConditionOP = 11,

        EquipmentCommandOP = 13,
        CommandMeaningsOP = 14
    }

    public enum EditType
    {
        UnChanged = 0,
        Add = 1,
        Modified = 2,
        Delete = 3,
    }

    public interface IDataOperation
    {
        bool LogCfgChangeEnable {
            get;
            set;
        }

        EditType EditType {
            get;
            set;
        }

        object Execute();
    }

    //class TBL_ConfigChangeDefine
    //{
    //    public int ConfigId;    
    //    public string EntityName;
    //    public string TableName;
    //    public string IdDefine;
    //}



}
