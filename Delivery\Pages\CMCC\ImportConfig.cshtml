﻿@page
@model Delivery.Pages.CMCC.ImportConfigModel
@{
}

@section Scripts {
    <script type="text/javascript" src="~/Scripts/lib/jquery-1.8.0.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="~/Scripts/common/ie8.js"></script>
    <script type="text/javascript" src="~/Scripts/common/common.js"></script>
<script type="text/javascript">
    var CONFIG_HANDLER = 'api/file';
    $(function () {
        //$('#submit').click(upload);
        init();
    });

    var localImport = function (fileElement, fsuid) {
        var confirmMsg = '确认导入吗？'
        if (!fsuid) {
            alertInfo('请确认配置工具中已预先配置FSUID！', 'warning');
            return;
        } else if (!/^devices_\d{14}\.xml$/.test(fileElement.files[0].name)) {
            alertInfo('配置文件名有误！');
            return;
        } else if (fileElement.files[0].size > 2 * 1024 * 1024) {
            alertInfo('文件大小不能超过2M！');
            return;
        } else if ('devices_' + fsuid + '.xml' !== fileElement.files[0].name) {
            confirmMsg = "配置文件名FSUID不匹配，是否继续导入？";
        }

        $.messager.confirm("确认", confirmMsg, function (r) {
            if (r) {
                $('#waiting').show();
                if (!window.FormData) {
                    $.ajaxFileUpload({
                        url: `${CONFIG_HANDLER}/localImport`, //用于文件上传的服务器端请求地址
                        secureuri: false, //是否需要安全协议，一般设置为false
                        fileElementId: fileElement.id, //文件上传域的ID
                        dataType: 'json', //返回值类型 一般设置为json
                        data: {
                            //action: "localImport",
                            fsuCode: fsuid
                        },
                        success: function (text, status)  //服务器成功响应处理函数
                        {
                            if (text === 'OK') {
                                alertInfo("配置数据导入成功！");
                                init();
                            }
                            else
                                alertInfo(text, 'error');
                            fileElement.value = '';
                            $('#waiting').hide();
                        },
                        error: function (data, status, errorThrown) {
                            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                            fileElement.value = '';
                            $('#waiting').hide();
                        }
                    });

                } else {
                    var fd = new FormData();
                    fd.append('fsuCode', fsuid);
                    //fd.append('action', 'localImport');
                    fd.append('upfile', fileElement.files[0]);
                    $.ajax({
                        type: "POST",
                        url: `${CONFIG_HANDLER}/localImport`,
                        data: fd,
                        cache: false,
                        processData: false,
                        contentType: false,
                        success: function (text) {
                            if (text === 'OK')
                                alertInfo("配置数据导入成功!");
                            else
                                alertInfo(text, 'error');
                            fileElement.value = '';
                            $('#waiting').hide();
                        },
                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                            fileElement.value = '';
                            $('#waiting').hide();
                        }
                    });
                }
            } else {
                fileElement.value = '';
            }
        });

    };

    var ftpImport = function (fsuCode) {
        $.messager.confirm("确认", "确认通过ftp下载并导入吗？", function (r) {
            if (r) {
                $('#waiting').show();
                $.ajax({
                    type: "POST",
                    contentType: 'application/x-www-form-urlencoded',
                    url: `${CONFIG_HANDLER}/ftpImport`,
                    data: { fsuCode: fsuCode },
                    async: false,
                    success: function (data) {
                        if (data === 'OK') {
                            alertInfo("配置数据导入成功!");
                            init();
                        }
                        else
                            alertInfo(data, 'error');
                        $('#waiting').hide();
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                        $('#waiting').hide();
                    }
                })
            };
        })
    };

    var loadFsuInfo = function () {
        $('#dg_fsu').datagrid({
            method: 'GET',
            title: 'Fsu配置数据导入',
            singleSelect: true,
            url: `${CONFIG_HANDLER}/GetAllFsuFtpFile`,
            width: function () { return document.body.clientWidth * 0.9; },
            rownumbers: true,
            remoteSort: false,
            pagination: true,
            pageSize: 50,
            pageList: [50, 100, 150, 200],
            queryParams: {
                //action: "GetAllFsuFtpFile",
                stationName: $('#txtStationNameq').val()
            },
            //loadFilter: pagerFilter,
            columns: [[
                {
                    field: 'LocalImport', title: '本地配置文件导入', width: 120, align: 'left',
                    formatter: function (value, row, index) {
                        return '<button type="button">本地配置文件导入</button><input type="file" id="local_'
                            + row.FSUID + '" class="fileInput" accept=".xml" onchange="localImport(this,\'' + row.FSUID + '\')">';
                    }
                },
                {
                    field: 'FtpImport', title: 'FTP配置文件导入', width: 120, align: 'left',
                    formatter: function (value, row, index) {
                        return '<button type="button" onclick="ftpImport(\'' + row.FSUID + '\')">FTP配置文件导入</button>';
                    }
                },
                {
                    field: 'IsSynced', title: '配置是否已更新', width: 100, align: 'left',
                    formatter: function (value, row, index) {
                        return value === '1' ? '是' : '否';
                    }
                },
                { field: 'StationName', title: '局站名称', width: 200, align: 'left' },
                { field: 'FSUID', title: 'FSUID号', width: 150, align: 'left' },
                { field: 'FSUName', title: 'FSU名称', width: 200, align: 'left' },
                { field: 'FSUIP', title: 'FSUIP地址', width: 150, align: 'left' },
                { field: 'FTPUserName', title: 'FTP用户名', width: 150, align: 'left' },
                { field: 'FTPPassWord', title: 'FTP密码', width: 150, align: 'left' },
                { field: 'GetConfigFlag', title: '获取配置标志', width: 200, align: 'left', hidden: true },
            ]]
        });
    }

    var init = function () {
        loadFsuInfo();

        $('#btn_search').click(function () {
            loadFsuInfo();
        });
    };


    </script>
}

@section Styles {
    <style>
        .fileInput {
            display: inline;
            position: relative;
            filter: alpha(opacity:0);
            opacity: 0;
            width: 108px;
            margin-left: -108px;
            height: 23px;
        }
    </style>
}


<div class="mask" style="opacity:.5; display:none;">
    <div style="position: relative; top: 50%; left: 50%">
        <div style="position: absolute; top: -17px; left: -70px; height: 16px; padding: 12px 5px 10px 30px; opacity:1; background: #fff url(../Content/themes/default/images/pagination_loading.gif) no-repeat scroll 5px 10px; border: 2px solid #ccc; color: #000;">
            正在加载，请等待...
        </div>
    </div>
</div>
<div id="waiting" class="mask" style="background-color: rgba(255,255,255,.5); display:none;">
    <div style="position: relative; top: 50%; left: 50%">
        <div style="position: absolute; top: -17px; left: -70px; height: 16px; padding: 12px 5px 10px 30px; background: #fff url(../Content/themes/default/images/pagination_loading.gif) no-repeat scroll 5px 10px; border: 2px solid #ccc; color: #000;">
            正在处理，请等待...
        </div>
    </div>
</div>

<table style="margin: 0; padding: 0;">
    <tr>
        <td style="text-align: left; white-space: nowrap">站址名称:</td>
        <td>
            <input type='text' placeholder="请输入站址名称" data-options="prompt:'请输入站址名称'" class="easyui-textbox" id="txtStationNameq" style="width: 140px;" />
        </td>
        <td><input type="button" id="btn_search" value="查询" class="commonButton" style="width: 40pt;" /></td>
    </tr>
</table>
<table id="dg_fsu"></table>

