﻿using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace Delivery.Common.NoProcedure.Service.CUCC
{
    public class DeviceService
    {
        private static DeviceService _instance = null;

        public static DeviceService Instance
        {
            get
            {
                if (_instance == null) _instance = new DeviceService();
                return _instance;
            }
        }

        public DataTable GetDeviceInfo(string startTime, string endTime, string stationId, string fsuId, string deviceType, string deviceName, string deviceCode, string logonId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    DateTime sTime = Convert.ToDateTime(startTime);
                    DateTime eTime = Convert.ToDateTime(endTime);
                    if (sTime > eTime)
                    {
                        return new DataTable();
                    }
                    eTime = eTime == null ? DateTime.Now : eTime;
                    sTime = sTime == null ? eTime.AddMonths(-1) : sTime;
                    realParams.Add("WhereTime", true);
                    realParams.Add("StartTime", sTime);
                    realParams.Add("EndTime", eTime);
                    if(stationId != null && stationId != "-1")
                    {
                        realParams.Add("WhereWRSationId", true);
                        realParams.Add("WRStationId", stationId);
                    }
                    if(deviceType != null && deviceType.Trim() != "" && deviceType != "-1")
                    {
                        realParams.Add("WhereDeviceTypeCode", true);
                        realParams.Add("DeviceType", deviceType);
                    }
                    if(deviceName != null && deviceName.Trim() != "")
                    {
                        realParams.Add("WhereDeviceName", true);
                        realParams.Add("DeviceName", "%" + deviceName + "%");
                    }
                    if(deviceCode != null && deviceCode.Trim() != "")
                    {
                        realParams.Add("WhereDeviceCode", true);
                        realParams.Add("DeviceCode", "%" + deviceCode + "%");
                    }
                    if(fsuId != null && fsuId != "-1")
                    {
                        realParams.Add("WhereFsuId", true);
                        realParams.Add("WRFsuId", fsuId);
                    }
                    realParams.Add("LogonId", logonId);
                    DataTable dt = execHelper.ExecDataTable("StationInfo_userId", realParams);
                    object iUserId = DBNull.Value;
                    if(dt != null && dt.Rows.Count > 0)
                    {
                        iUserId = CommonUtils.GetNullableValue(dt.Rows[0].Field<int?>("iUserId"));
                    }
                    realParams.Add("iUserId", iUserId);
                    DataTable dt1 = execHelper.ExecDataTable("StationInfo_userRole", realParams);
                    if (dt1 != null && dt1.Rows.Count > 0)
                    {
                        realParams.Add("RoleMapNotNull", true);
                    }
                    else
                    {
                        realParams.Add("RoleMapNull", true);
                    }
                    realParams.Add("SqlStr", true);
                    return execHelper.ExecDataTable("SP_Get_WRDeviceCUCC", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("SP_Get_WRDeviceCUCC:{0}", ex));
                    return new DataTable();
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
        public int SP_Add_WRDeviceCUCC(string WRHouseId, string WRFsuId, string DeviceType, string DeviceName, string DeviceRId, string Remark, string LogonId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                realParams.Add("WRHouseId", WRHouseId);
                realParams.Add("WRFsuId", WRFsuId);
                realParams.Add("DeviceType", DeviceType);
                realParams.Add("DeviceName", DeviceName);
                realParams.Add("DeviceRId", DeviceRId);
                realParams.Add("Remark", Remark);
                realParams.Add("LogonId", LogonId);
                try
                {
                    object WRStationId = DBNull.Value;object SWStationId = DBNull.Value;object SWHouseId = DBNull.Value;
                    string DeviceCode = null;object UserId = DBNull.Value;string SWUserName = null;
                    object WRDeviceId = DBNull.Value;string StationName = null;
                    if (WRHouseId == null || WRHouseId == "" || DeviceType == null || DeviceType == "" || DeviceName == null || DeviceName == "")
                        return 0;
                    DataTable dt = executeHelper.ExecDataTable("SP_Add_WRDeviceCUCC_FindByHouse", realParams);
                    if (dt != null && dt.Rows.Count > 0)
                        return -1;
                    DataTable dt2 = executeHelper.ExecDataTable("SP_Add_WRDeviceCUCC_StationInfo", realParams);
                    if(dt2!=null && dt2.Rows.Count > 0)
                    {
                        WRStationId = CommonUtils.GetNullableValue(dt2.Rows[0].Field<int?>("WRStationId"));
                        SWStationId = CommonUtils.GetNullableValue(dt2.Rows[0].Field<int?>("SWStationId"));
                        SWHouseId = CommonUtils.GetNullableValue(dt2.Rows[0].Field<int?>("SWHouseId"));
                    }
                    realParams.Add("WRStationId", WRStationId);
                    realParams.Add("SWStationId", SWStationId);
                    realParams.Add("SWHouseId", SWHouseId);
                    DataSet resultSql1 = executeHelper.ExecDataSet("SP_Add_WRDeviceCUCC_FindByStationIdAndDeviceName", realParams);
                    if (resultSql1 != null && (resultSql1.Tables[0].Rows.Count > 0 || resultSql1.Tables[1].Rows.Count > 0))
                        return -2;
                    SP_GenerateCUCCDeviceCode(WRStationId, DeviceType, int.Parse(WRFsuId),out DeviceCode);
                    realParams.Add("DeviceCode", DeviceCode);
                    if (DeviceCode == null)
                        return -3;
                    DataTable dt3 = executeHelper.ExecDataTable("SP_Add_WRDeviceCUCC_UserInfo", realParams);
                    if(dt3!=null && dt3.Rows.Count > 0)
                    {
                        UserId = CommonUtils.GetNullableValue(dt3.Rows[0].Field<int?>("UserId"));
                        SWUserName = dt3.Rows[0]["SWUserName"].ToString();
                    }
                    realParams.Add("UserId", UserId);
                    realParams.Add("SWUserName", SWUserName);
                    executeHelper.ExecuteNonQuery("SP_Add_WRDeviceCUCC_Insert_WR_DeviceManagementCUCC", realParams);
                    DataTable dt4 = executeHelper.ExecDataTable("SP_Add_WRDeviceCUCC_WRDeviceId", realParams);
                    if(dt4!=null && dt4.Rows.Count > 0)
                    {
                        WRDeviceId = CommonUtils.GetNullableValue(dt4.Rows[0].Field<int?>("WRDeviceId"));
                    }
                    realParams.Add("WRDeviceId", WRDeviceId);
                    DataTable dt5 = executeHelper.ExecDataTable("SP_Add_WRDeviceCUCC_StationName", realParams);
                    if (dt5 != null && dt5.Rows.Count > 0)
                    {
                        StationName = dt5.Rows[0]["StationName"].ToString();
                    }
                    realParams.Add("StationName", StationName);
                    Public_StoredService.Instance.SP_WR_OperationRecord(WRStationId, StationName,10,WRDeviceId,DeviceName,"","",LogonId);
                    return 1;
                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    return 0;
                }finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
        public void SP_GenerateCUCCDeviceCode(object WRStationId, string DeviceType, object WRFsuId, out string OutputCode)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                OutputCode = "";
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    int maxNo = 0;
                    realParams.Add("WRStationId", WRStationId);
                    realParams.Add("WRFsuId", WRFsuId);
                    realParams.Add("DeviceType", DeviceType);
                    DataTable dt = execHelper.ExecDataTable("SP_GenerateCUCCDeviceCode_getMaxNo", realParams);
                    if(dt != null && dt.Rows.Count > 0 && dt.Rows[0][0] != DBNull.Value)
                    {
                        maxNo = Convert.ToInt32(dt.Rows[0][0]);
                    }
                    object DeviceCount = null;
                    DataTable dt1 = execHelper.ExecDataTable("SP_GenerateCUCCDeviceCode_getDeviceCount", realParams);
                    if(dt1 != null && dt1.Rows.Count > 0 && dt1.Rows[0][0] != DBNull.Value)
                    {
                        DeviceCount = Convert.ToInt32(dt1.Rows[0][0]);
                    }
                    object iCurrentNo = null;
                    if(DeviceCount == null || (int)DeviceCount == 0)
                    {
                        iCurrentNo = 1;
                    }
                    else if((int)DeviceCount < 100)
                    {
                        int startIndex = 1;
                        while(startIndex < maxNo)
                        {
                            string tmpCode = null;
                            if(startIndex <= 9)
                            {
                                tmpCode = DeviceType + "0" + startIndex.ToString();
                            }
                            else
                            {
                                tmpCode = DeviceType + startIndex.ToString();
                            }
                            if(realParams.ContainsKey("tmpCode"))
                            {
                                realParams.Remove("tmpCode");
                            }
                            realParams.Add("tmpCode", tmpCode);
                            DataTable dt2 = execHelper.ExecDataTable("SP_GenerateCUCCDeviceCode_ifDeviceExist", realParams);
                            if(dt2 == null || dt2.Rows.Count == 0)
                            {
                                iCurrentNo = startIndex;
                                break;
                            }
                            startIndex++;
                        }
                        if(startIndex == maxNo && iCurrentNo == null)
                        {
                            iCurrentNo = maxNo + 1;
                        }
                    }
                    else
                    {
                        iCurrentNo = maxNo + 1;
                    }
                    string sCurrentNo = "";
                    if((int)iCurrentNo < 10)
                    {
                        sCurrentNo = "0" + iCurrentNo.ToString();
                    }
                    else
                    {
                        sCurrentNo = iCurrentNo.ToString();
                    }
                    OutputCode = DeviceType + sCurrentNo;
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("SP_GenerateCUCCDeviceCode:{0}", ex));
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public DataTable SP_Upd_WRDeviceCUCC(string WRDeviceId, string WRHouseId, string WRFsuId, string DeviceType, string DeviceName, string DeviceRId, string Remark, string LogonId)
        {
            if (WRDeviceId == null || DeviceType == null || DeviceName == null)
                return null;

            using (DbHelper dbHelper = new DbHelper())
            {
                //返回字段表
                DataTable ReturnTable = new DataTable();
                DataColumn newColumn = new DataColumn("ReturnValue", typeof(int));
                ReturnTable.Columns.Add(newColumn);
                DataRow newRow = ReturnTable.NewRow();

                //定义字段
                int? WRStationId = null, SWStationId = null, SWHouseId = null, SWEquipmentId = null, UserId = null;
                string ObjectId = null, OriDeviceType = null, DeviceCode = null, SWUserName = null, UpdateString = null, LastString = null, StationName = null;

                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("WRHouseId",CommonUtils.GetNullableValue(WRHouseId));
                    object judgeExist1 = execHelper.ExecuteScalar("SP_Upd_WRDeviceCUCC_JuedgeExist1", realParams);
                    if (judgeExist1 != DBNull.Value && judgeExist1 != null) {
                        newRow["ReturnValue"] = -1;
                        ReturnTable.Rows.Add(newRow);
                        return ReturnTable;
                    }

                    //获取站点信息
                    DataTable fieldValueDt1 = execHelper.ExecDataTable("SP_Upd_WRDeviceCUCC_GetFieldValue1", realParams);
                    if(fieldValueDt1 != null && fieldValueDt1.Rows.Count > 0)
                    {
                        WRStationId = fieldValueDt1.Rows[0].Field<int?>("WRStationId");
                        SWStationId = fieldValueDt1.Rows[0].Field<int?>("SWStationId");
                        SWHouseId = fieldValueDt1.Rows[0].Field<int?>("SWHouseId");
                    }
                    realParams.Add("WRStationId", CommonUtils.GetNullableValue(WRStationId));
                    realParams.Add("SWStationId", CommonUtils.GetNullableValue(SWStationId));
                    realParams.Add("WRDeviceId", CommonUtils.GetNullableValue(WRDeviceId));
                    realParams.Add("DeviceName", CommonUtils.GetNullableValue(DeviceName));


                    object judgeExist2 = execHelper.ExecuteScalar("SP_Upd_WRDeviceCUCC_JuedgeExist2", realParams);
                    if(judgeExist2 != DBNull.Value && judgeExist2 != null)
                    {
                        newRow["ReturnValue"] = -2;
                        ReturnTable.Rows.Add(newRow);
                        return ReturnTable;
                    }

                    //获取设备信息
                    DataTable fieldValueDt2 = execHelper.ExecDataTable("SP_Upd_WRDeviceCUCC_GetFieldValue2", realParams);
                    if (fieldValueDt2 != null && fieldValueDt2.Rows.Count > 0)
                    {
                        OriDeviceType = fieldValueDt2.Rows[0].Field<string>("OriDeviceType");
                        DeviceCode = fieldValueDt2.Rows[0].Field<string>("DeviceCode");
                    }
                    if (!OriDeviceType.Equals(DeviceType))
                    {
                        DeviceCode = null;
                        SP_GenerateCUCCDeviceCode(WRStationId, DeviceType, WRFsuId, out DeviceCode);
                        if(DeviceCode == null)
                        {
                            newRow["ReturnValue"] = -3;
                            ReturnTable.Rows.Add(newRow);
                            return ReturnTable;
                        }
                    }

                    //获取用户信息
                    realParams.Add("LogonId", CommonUtils.GetNullableValue(LogonId));
                    DataTable fieldValueDt3 = execHelper.ExecDataTable("SP_Upd_WRDeviceCUCC_GetFieldValue3", realParams);
                    if (fieldValueDt3 != null && fieldValueDt3.Rows.Count > 0)
                    {
                        UserId = fieldValueDt3.Rows[0].Field<int?>("UserId");
                        SWUserName = fieldValueDt3.Rows[0].Field<string>("SWUserName");
                    }

                    //获取LastString值
                    object LastStringObj = execHelper.ExecuteScalar("SP_Upd_WRDeviceCUCC_GetFieldValue4", realParams);
                    if(LastStringObj != null && LastStringObj != DBNull.Value)
                    {
                        LastString = LastStringObj.ToString();
                    }
                    UpdateString = $"WRHouseId:{WRHouseId}-DeviceType:{DeviceType}-DeviceRId:{DeviceRId}-DeviceCode:{DeviceCode}-DeviceName:{DeviceName}-UserId:{UserId}-Remark:{Remark}-WRFsuId:{WRFsuId}";

                    //获取StationName
                    object StationNameObj = execHelper.ExecuteScalar("SP_Upd_WRDeviceCUCC_GetFieldValue5", realParams);
                    if(StationNameObj != DBNull.Value && StationNameObj != null)
                    {
                        StationName = StationNameObj.ToString();
                    }

                    Public_StoredService.Instance.SP_WR_OperationRecord(WRStationId, StationName, 12, WRDeviceId, DeviceName, UpdateString, LastString, LogonId);

                    realParams.Add("DeviceType", CommonUtils.GetNullableValue(DeviceType));
                    realParams.Add("DeviceRId", CommonUtils.GetNullableValue(DeviceRId));
                    realParams.Add("DeviceCode", CommonUtils.GetNullableValue(DeviceCode));
                    realParams.Add("UserId", CommonUtils.GetNullableValue(UserId));
                    realParams.Add("SWUserName", CommonUtils.GetNullableValue(SWUserName));
                    realParams.Add("SWHouseId", CommonUtils.GetNullableValue(SWHouseId));
                    realParams.Add("Remark", CommonUtils.GetNullableValue(Remark));
                    realParams.Add("WRFsuId", CommonUtils.GetNullableValue(WRFsuId));

                    //更新WR_DeviceManagementCUCC
                    execHelper.ExecuteNonQuery("SP_Upd_WRDeviceCUCC_UpdateWRDeviceManagementCUCC", realParams);

                    //如果TBL_Equipment表中已经存在当前修改的设备,则同步修改TBL_Equipment设备名称和机房
                    object SWEquipmentIdObj = execHelper.ExecuteScalar("SP_Upd_WRDeviceCUCC_GetFieldValue6", realParams);
                    if (SWEquipmentIdObj != DBNull.Value && SWEquipmentIdObj != null) {
                        SWEquipmentId = int.Parse(SWEquipmentIdObj.ToString());
                    }
                    if (SWEquipmentId != null) {
                        realParams.Add("SWEquipmentId", SWEquipmentId);
                        execHelper.ExecuteNonQuery("SP_Upd_WRDeviceCUCC_UpdateTBLEquipment", realParams);
                        ObjectId = SWStationId + "." + SWEquipmentId;
                        //调用 CALL PBL_ConfigChangeLog (ObjectId, 3, 2);
                        PblConfigChangeLogService.Instance.DoExecute(ObjectId, 3, 2);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("SP_Upd_WRDeviceCUCC:{0}", ex));
                    return null;
                }
                newRow["ReturnValue"] = 1;
                ReturnTable.Rows.Add(newRow);
                return ReturnTable; ;
            }
        }
    }
}
