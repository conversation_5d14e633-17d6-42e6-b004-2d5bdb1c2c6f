﻿
using BDSTool.DBUtility.Common;
using BDSTool.Entity;
using BDSTool.Entity.DRHelper;
using BDSTool.Entity.S2;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;


namespace BDSTool.BLL.B
{
    public partial class TSignalBiz
    {
        public static List<TSignal> GetTSignalList(int StationId, int EquipmentId) {
            var list = new List<TSignal>();

            var proc ="BM_GetTSignalList" ;

            var sql = string.Format("{0} {1},{2}", proc, StationId, EquipmentId);
            //var ds = DBHelper.GetData(sql);
            DataSet ds = null;
            try
            {
                if(CommonUtils.IsNoProcedure)
                {
                    ds = BM_GetFSUMainConfigService.Instance.BM_GetTSignalList(StationId, EquipmentId);
                }
                else
                {
                    using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                    {
                        dbHelper.CreateProcedureCommand("BM_GetTSignalList");
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("StationId", DbType.Int32, StationId));
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter("EquipmentId", DbType.Int32, EquipmentId));

                        ds = dbHelper.ExecuteDataSet();
                    }
                }
            }
            catch (Exception ex)
            {
                DBHelper.LogError(sql, ex);
            }
            if(ds.Tables.Count>0){
                foreach (DataRow r in ds.Tables[0].Rows) {
                    var t = TSignalHeper.FromDataRow(r);
                    if (t!=null)
                        list.Add(t);
                }

            }          
            return list;
        }

        public static List<TSignal> ZGetTSignalList4Emr(int StationId, int EquipmentId) {
            var list = new List<TSignal>();

            var proc ="BM_GetTSignalList4Emr";

            var sql = string.Format("{0} {1},{2}", proc, StationId, EquipmentId);
            //var ds = DBHelper.GetData(sql);

            DataSet ds = null;
            try
            {
                using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                {
                    dbHelper.CreateProcedureCommand("BM_GetTSignalList4Emr");
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("StationId", DbType.Int32, StationId));
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("EquipmentId", DbType.Int32, EquipmentId));
                    ds = dbHelper.ExecuteDataSet();
                }
            }
            catch (Exception ex)
            {
                DBHelper.LogError(sql, ex);
            }
            if (ds.Tables.Count > 0) {
                foreach (DataRow r in ds.Tables[0].Rows) {
                    var t = TSignalHeper.FromDataRow(r);
                    if (t != null)
                        list.Add(t);
                }

            }
            return list;
        }

        public static TSignal GetTSignalBySiteWebSECID(bool IsFsu, SiteWebSECType secType, int StationId, int EquipmentId, int SiteWebSECId, int? EventConditionId = null) {
            var sql = "";
            bool IsEmr = !IsFsu;
            DataTable table = null;
            switch (secType) {
                case SiteWebSECType.Signal:
                    sql = string.Format("{0} {1},{2},{3}",
                        "BM_GetTSignalBySignal", IsEmr, StationId, EquipmentId, SiteWebSECId);
                    try
                    {
                        using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                        {
                            dbHelper.CreateProcedureCommand("BM_GetTSignalBySignal");
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("IsEmr", DbType.Boolean, IsEmr));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("StationId", DbType.Int32, StationId));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("EquipmentId", DbType.Int32, EquipmentId));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("SiteWebSECId", DbType.Int32, SiteWebSECId));
                            table = dbHelper.ExecuteDataTable();
                        }
                    }
                    catch (Exception ex)
                    {
                        DBHelper.LogError(sql, ex);
                    }
                    break;
                case SiteWebSECType.Event:
                    sql = string.Format("{0} {1},{2},{3},{4}",
                        "BM_GetTSignalByEvent", IsEmr, StationId, EquipmentId, SiteWebSECId, EventConditionId);
                    try
                    {
                        using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                        {
                            dbHelper.CreateProcedureCommand("BM_GetTSignalByEvent");
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("IsEmr", DbType.Boolean, IsEmr));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("StationId", DbType.Int32, StationId));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("EquipmentId", DbType.Int32, EquipmentId));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("SiteWebSECId", DbType.Int32, SiteWebSECId));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("EventConditionId", DbType.Int32, EventConditionId));
                            table = dbHelper.ExecuteDataTable();
                        }
                    }
                    catch (Exception ex)
                    {
                        DBHelper.LogError(sql, ex);
                    }
                    break;
                case SiteWebSECType.Control:
                    sql = string.Format("{0} {1},{2},{3}", 
                        "BM_GetTSignalByControl", IsEmr, StationId, EquipmentId, SiteWebSECId);
                    try
                    {
                        using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                        {
                            dbHelper.CreateProcedureCommand("BM_GetTSignalByControl");
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("IsEmr", DbType.Boolean, IsEmr));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("StationId", DbType.Int32, StationId));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("EquipmentId", DbType.Int32, EquipmentId));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("SiteWebSECId", DbType.Int32, SiteWebSECId));
                            table = dbHelper.ExecuteDataTable();
                        }
                    }
                    catch (Exception ex)
                    {
                        DBHelper.LogError(sql, ex);
                    }
                    break;
            }

            //var table = DBHelper.GetTable(sql);
            if (table.Rows.Count == 0)
                return null;

            var t = TSignalHeper.FromDataRow(table.Rows[0]);
            return t;
        }

    }
}
