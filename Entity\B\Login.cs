﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B
{
    /// <summary>
    /// ********　FSU向SC注册
    /// </summary>
    public sealed class Login : BMessage
    {
        public string User { get; private set; }
        public string Password { get; private set; }

        public string FsuIp { get; private set; }

        public string[] DeviceIds { get; private set; }
        public string[] DeviceCodes { get; set; }

        public int MyMonitoringUnitId { get; set; }

        public Login(string user, string password, string fsuId, string fsuCode, string fsuIp, params string[] ids)
            : base()
        {
            MessageType = (int)BMessageType.Login;
            User = user;
            Password = password;

            FsuId = fsuId;
            FsuCode = fsuCode;
            FsuIp = fsuIp;
            DeviceIds = ids;
        }

        public override string Serialize()
        {
            throw new NotImplementedException();
        }

        public static Login Deserialize(XmlDocument xmldoc)
        {
            string userName = xmldoc.SelectSingleNode("/Request/Info/UserName").InnerText;
            string password = xmldoc.SelectSingleNode("/Request/Info/PaSCword").InnerText;
            string fsuId = xmldoc.SelectSingleNode("/Request/Info/FsuId").InnerText;
            string fsuCode = xmldoc.SelectSingleNode("/Request/Info/FsuCode").InnerText;
            string fsuIp = xmldoc.SelectSingleNode("/Request/Info/FsuIP").InnerText;

            XmlNodeList nodelist = xmldoc.SelectNodes("/Request/Info/DeviceList/Device");

            List<string> deviceIds = new List<string>();
            List<string> deviceCodes = new List<string>();

            foreach (XmlNode xn in nodelist)
            {
                XmlElement xe = (XmlElement)xn;  
                string deviceId = xe.GetAttribute("Id");
                string deviceCode = xe.GetAttribute("Code");

                deviceIds.Add(deviceId);
                deviceCodes.Add(deviceCode);
            }


            Login login = new Login(userName, password, fsuId, fsuCode, fsuIp, deviceIds.ToArray());
            login.DeviceCodes = deviceCodes.ToArray();

            return login;
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}: {2}, {3}, {4}, {5}, {6}", 
                MessageId, (BMessageType)MessageType, User.Trim(), Password.Trim(), FsuId, FsuCode, FsuIp);
        }
    }
}
