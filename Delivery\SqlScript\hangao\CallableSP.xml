﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
    <procedure owner="" name="SaveEquipmentMaitain_GetCount" grant="">
      <parameters>
        <parameter name="StationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EquipmentId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT count(*) FROM TBL_EquipmentMaintain T WHERE T.StationId = @StationId AND T.EquipmentId = @EquipmentId </body>
    </procedure>
    <procedure owner="" name="SaveEquipmentMaitain_InsertMaintain" grant="">
      <parameters>
        <parameter name="StationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EquipmentId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EquipmentState" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StartTime" type="datetime" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EndTime" type="datetime" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="UserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Description" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ExtendFiled1" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body> INSERT INTO TBL_EquipmentMaintain(StationId,EquipmentId,EquipmentState,StartTime,EndTime,UserId,Description,ExtendFiled1)
        VALUES (@StationId,@EquipmentId,@EquipmentState,@StartTime,@EndTime,@UserId,@Description,@ExtendFiled1) 
      </body>
    </procedure>
    <procedure owner="" name="SaveEquipmentMaitain_InsertStateOperation1" grant="">
      <parameters>
        <parameter name="StationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EquipmentId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EquipmentState" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StartTime" type="datetime" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EndTime" type="datetime" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="UserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Description" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ExtendFiled1" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        INSERT INTO TBL_ProjectStateOperation (OperationType,Operation, StationId, EquipmentId, Reason, StartTime, EndTime, UserId, OperationDate)
        VALUES (301,'新增设备预约', @StationId, @EquipmentId, @Description, @StartTime, @EndTime, @UserId, now())
      </body>
    </procedure>
    <procedure owner="" name="SaveEquipmentMaitain_InsertStateOperation2" grant="">
      <parameters>
        <parameter name="StationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EquipmentId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="UserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        INSERT INTO TBL_ProjectStateOperation (OperationType,Operation, StationId, EquipmentId, Reason, StartTime, EndTime, UserId, OperationDate)
        SELECT 303,'删除设备预约', T.StationId, T.EquipmentId, T.Description, T.StartTime, T.EndTime, @UserId, now()
        FROM TBL_EquipmentMaintain T WHERE T.StationId = @StationId AND T.EquipmentId = @EquipmentId
      </body>
    </procedure>
    <procedure owner="" name="SaveEquipmentMaitain_InsertStateOperation3" grant="">
      <parameters>
        <parameter name="StationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EquipmentId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StartTime" type="datetime" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EndTime" type="datetime" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="UserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Description" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>INSERT INTO TBL_ProjectStateOperation (OperationType,Operation, StationId, EquipmentId, Reason, StartTime, EndTime, UserId, OperationDate)
			   VALUES (302,'修改设备预约', @StationId, @EquipmentId, @Description, @StartTime, @EndTime, @UserId, now())
      </body>
    </procedure>
    <procedure owner="" name="SaveEquipmentMaitain_UpdateMaintain" grant="">
      <parameters>
        <parameter name="StationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EquipmentId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EquipmentState" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StartTime" type="datetime" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EndTime" type="datetime" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="UserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Description" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ExtendFiled1" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        UPDATE TBL_EquipmentMaintain 
        SET EquipmentState = @EquipmentState,
            StartTime = @StartTime,
            EndTime = @EndTime,
            UserId = @UserId,
            Description = @Description,
            ExtendFiled1 = @ExtendFiled1
        WHERE StationId = @StationId AND EquipmentId = @EquipmentId
      </body>
    </procedure>
    <procedure owner="" name="SaveOperationRecord_GetStationName" grant="">
      <parameters>
        <parameter name="StationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body> SELECT a.StationName FROM TBL_Station a WHERE a.StationId = @StationId </body>
    </procedure>
    <procedure owner="" name="SaveOperationRecord_InsertRecord" grant="">
      <parameters>
        <parameter name="UserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Operation" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="OperationType" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="OperationContent" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        INSERT INTO TBL_OperationRecord(
                    UserId,
                    StationId,
                    StationName,
                    Operation,
                    OperationTime,
                    OperationType,
                    OperationContent)
        VALUES(
                    @UserId           ,
                    @StationId        ,
                    @StationName	  ,
                    @Operation        ,
                    now()		  ,
                    @OperationType    ,
                    @OperationContent )
      </body>
    </procedure>
    
	</procedures>
</root>
