﻿using DM.TestOrder.Entity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using DM.TestOrder.DAL.Helper;
using Delivery.DAL;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace DM.TestOrder.DAL {

    public class WoTestOrderArtSelfCheckListDal
    {
        public static List<WO_TestOrderArtSelfCheckList> GetByOrderId(int orderId) {
            var items = new List<WO_TestOrderArtSelfCheckList>();
            //var sql = string.Format("select * from WO_TestOrderArtSelfCheckList where OrderId ={0}", orderId);
            //var tb = DBHelper.GetTable(sql);
            DataTable tb;
            if (CommonUtils.IsNoProcedure)
            {
                tb = Public_ExecuteSqlService.Instance.NoStore_WOTestOrderArtSelfCheckList_GetByOrderId(orderId);
            }
            else
            {
                var sql = "select * from WO_TestOrderArtSelfCheckList where OrderId = @orderId";
                tb = new ExecuteSql().ExecuteSQL(sql, new QueryParameter[]
                {
                new QueryParameter("orderId", DataType.Int, orderId.ToString())
                });
            }
            if (tb.Rows.Count == 0)
                return items;
            foreach (DataRow row in tb.Rows) {
                var item = WO_TestOrderArtSelfCheckListHelper.FromDataRow(row);
                items.Add(item);
            }

            return items;
        }

        public static void DBUpdate(WO_TestOrderArtSelfCheckList c) {
            if (CommonUtils.IsNoProcedure)
            {
                 WoCommonService.Instance.WO_TestOrderArtSelfCheckList_Update(c.OrderId,c.CheckDicId,c.IsPass);
                 return ;
            }

            //var sqlCheckItem = string.Format("WO_TestOrderArtSelfCheckList_Update {0},{1},{2}",
            //        c.OrderId,c.CheckDicId, c.IsPass);
            //    DBHelper.ExecuteNonQuery(sqlCheckItem);
            new ExecuteSql().ExecuteStoredProcedureNoQuery("WO_TestOrderArtSelfCheckList_Update", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, c.OrderId.ToString()),
                new QueryParameter("CheckDicId", DataType.Int, c.CheckDicId.ToString()),
                new QueryParameter("IsPass", DataType.Int, c.IsPass.ToString())
            });

        }
    }
}
