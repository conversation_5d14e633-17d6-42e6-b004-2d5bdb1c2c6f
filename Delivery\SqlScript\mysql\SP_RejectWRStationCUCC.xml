﻿<?xml version="1.0" encoding="utf-8"?>
<?xml-stylesheet type="text/xsl" href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
		<procedure owner="" name="SP_RejectWRStationCUCC_1" grant="">
			<parameters>
				<parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[ SELECT a.StationStatus FROM WR_StationManagementCUCC a WHERE a.WRStationId = @WRStationId; ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_RejectWRStationCUCC_2" grant="">
			<parameters>
				<parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StationCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="RejectCause" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
				set sql_safe_updates = 0;
				
				UPDATE WR_StationManagementCUCC
				SET WR_StationManagementCUCC.StationStatus = 2, WR_StationManagementCUCC.RejectCause = @RejectCause
				WHERE WR_StationManagementCUCC.WRStationId = @WRStationId;
				
				UPDATE WR_HouseManagementCUCC
				SET WR_HouseManagementCUCC.HouseStatus = 2,WR_HouseManagementCUCC.RejectCause = concat('级联退回:' , @RejectCause)
				WHERE WR_HouseManagementCUCC.WRStationId = @WRStationId;
				
				UPDATE WR_FsuManagementCUCC,WR_HouseManagementCUCC
				SET WR_FsuManagementCUCC.FsuStatus = 2,WR_FsuManagementCUCC.RejectCause = concat('级联退回:' , @RejectCause)
				WHERE WR_FsuManagementCUCC.WRHouseId = WR_HouseManagementCUCC.WRHouseId
				AND WR_HouseManagementCUCC.WRStationId = @WRStationId;
				
				set sql_safe_updates = 1;
				]]>
			</body>
		</procedure>
	</procedures>
</root>
