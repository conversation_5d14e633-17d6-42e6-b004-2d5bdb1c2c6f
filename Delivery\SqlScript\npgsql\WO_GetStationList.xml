﻿<?xml version="1.0" encoding="utf-8"?>
<?xml-stylesheet type="text/xsl" href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
		<procedure owner="" name="WO_GetStationList_1" grant="">
			<body>
				<![CDATA[
				SELECT StructureId,StructureName
				FROM TBL_StationStructure
				WHERE TBL_StationStructure.StructureType = 2 AND TBL_StationStructure.ParentStructureId = 0;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="WO_GetStationList_2" grant="">
			<parameters>
				<parameter name="SCenterId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="CenterName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StationCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT @SCenterId CenterId, @CenterName  CenterName,
				s.StationId,
				s.StationName,
				s.Latitude,
				s.Longitude,
				w.StructureId, b.StructureName,w.StationCode,
				w.StationCategory , c.ItemValue StationCategoryName,
				w.StationStatus,
				w.Address,
				w.Province , f.ItemValue ProvinceName,
				w.City , g.ItemValue CityName,
				w.County , h.ItemValue CountyName,
				w.SWStationId,
				w.Remark,
				w.StationStatus
				FROM WR_StationManagement w
				INNER JOIN TBL_Station s ON w.SWStationId = s.StationId
				INNER JOIN TBL_StationStructure b ON w.StructureId = b.StructureId
				INNER JOIN WR_DataItem c ON w.StationCategory = c.ItemId AND c.EntryId = 6
				
				LEFT JOIN WR_DataItem f ON w.Province = f.ItemId AND f.EntryId = 1
				LEFT JOIN WR_DataItem g ON w.City = g.ItemId AND g.EntryId = 2
				LEFT JOIN WR_DataItem h ON w.County = h.ItemId AND h.EntryId = 3
				WHERE
				(@StationName = '' or s.StationName like concat('%',@StationName,'%'))
				and
				(@StationCode = '' OR w.StationCode like concat('%',@StationCode,'%') )
				UNION ALL
				SELECT @SCenterId CenterId, @CenterName  CenterName,
				s.StationId,
				s.StationName,
				s.Latitude,
				s.Longitude,
				A.StructureId,
				A.StructureName,
				NULL StationCode,
				NULL StationCategory ,
				NULL StationCategoryName,
				NULL StationStatus,
				NULL Address,
				NULL Province ,
				NULL ProvinceName,
				NULL City ,
				NULL CityName,
				NULL County ,
				NULL CountyName,
				s.StationId SWStationId,
				NULL Remark,
				NULL StationStatus
				FROM TBL_Station s
				INNER JOIN TBL_StationStructure A ON  A.StructureGroupId=1
				INNER JOIN TBL_StationStructureMap B ON A.StructureId = B.StructureId AND B.StationId =s.StationId
				WHERE
				s.StationId NOT IN (SELECT SWStationId FROM WR_StationManagement WHERE SWStationId IS NOT NULL)
				AND s.StationId>0
				AND	(@StationName = '' or s.StationName like concat('%',@StationName,'%'))
				AND @StationCode = '';
				]]>
			</body>
		</procedure>
		<procedure owner="" name="WO_GetStationList_3" grant="">
			<parameters>
				<parameter name="SCenterId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="CenterName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StationCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT @SCenterId CenterId, @CenterName  CenterName,
				s.StationId,
				s.StationName,
				s.Latitude,
				s.Longitude,
				w.StructureId, b.StructureName,w.StationCode,
				w.StationCategory , c.ItemValue StationCategoryName,
				w.StationStatus,
				w.Address,
				w.Province , f.ItemValue ProvinceName,
				w.City , g.ItemValue CityName,
				w.County , h.ItemValue CountyName,
				w.SWStationId,
				w.Remark,
				w.StationStatus
				FROM WR_StationManagementCUCC w
				INNER JOIN TBL_Station s ON w.SWStationId = s.StationId
				INNER JOIN TBL_StationStructure b ON w.StructureId = b.StructureId
				INNER JOIN WR_DataItem c ON w.StationCategory = c.ItemId AND c.EntryId = 6
				
				LEFT JOIN WR_DataItem f ON w.Province = f.ItemId AND f.EntryId = 1
				LEFT JOIN WR_DataItem g ON w.City = g.ItemId AND g.EntryId = 2
				LEFT JOIN WR_DataItem h ON w.County = h.ItemId AND h.EntryId = 3
				WHERE
				(@StationName = '' or s.StationName like concat('%',@StationName,'%'))
				and
				(@StationCode = '' OR w.StationCode like concat('%',@StationCode,'%'))
				UNION ALL
				SELECT @SCenterId CenterId, @CenterName  CenterName,
				s.StationId,
				s.StationName,
				s.Latitude,
				s.Longitude,
				A.StructureId,
				A.StructureName,
				NULL StationCode,
				NULL StationCategory ,
				NULL StationCategoryName,
				NULL StationStatus,
				NULL Address,
				NULL Province ,
				NULL ProvinceName,
				NULL City ,
				NULL CityName,
				NULL County ,
				NULL CountyName,
				s.StationId SWStationId,
				NULL Remark,
				NULL StationStatus
				FROM TBL_Station s
				INNER JOIN TBL_StationStructure A ON  A.StructureGroupId=1
				INNER JOIN TBL_StationStructureMap B ON A.StructureId = B.StructureId AND B.StationId =s.StationId
				WHERE
				s.StationId NOT IN (SELECT SWStationId FROM WR_StationManagementCUCC WHERE SWStationId IS NOT NULL)
				AND s.StationId>0
				AND	(@StationName = '' or s.StationName like concat('%',@StationName,'%'))
				AND @StationCode = '';
				]]>
			</body>
		</procedure>
	</procedures>
</root>
