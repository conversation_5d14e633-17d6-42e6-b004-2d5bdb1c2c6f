﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using Delivery.Entity;
using System.Web;
using Microsoft.AspNetCore.Http;

namespace Delivery.Common
{
    public static class ExcelReader
    {
        private static string GetCellValue(ICell cell)
        {
            if (cell == null)
                return string.Empty;
            switch (cell.CellType)
            {
                case CellType.Blank:
                    return string.Empty;
                case CellType.Boolean:
                    return cell.BooleanCellValue.ToString();
                case CellType.Error:
                    return cell.ErrorCellValue.ToString();
                case CellType.Numeric:
                    if (NPOI.SS.UserModel.DateUtil.IsCellDateFormatted(cell))
                        return cell.DateCellValue.ToString("yyyy-MM-dd");
                    else
                        return cell.ToString();
                case CellType.Unknown:
                default:
                    return cell.ToString();//This is a trick to get the correct value of the cell. NumericCellValue will return a numeric value no matter the cell value is a date or a number
                case CellType.String:
                    return cell.StringCellValue;
                case CellType.Formula:
                    try
                    {
                        XSSFFormulaEvaluator e = new XSSFFormulaEvaluator(cell.Sheet.Workbook);
                        e.EvaluateInCell(cell);
                        return cell.ToString();
                    }
                    catch
                    {
                        return cell.NumericCellValue.ToString();
                    }
            }
        }

        private static void AutoSizeColumns(ISheet sheet)
        {
            if (sheet.PhysicalNumberOfRows > 0)
            {
                IRow headerRow = sheet.GetRow(0);

                for (int i = 0, l = headerRow.LastCellNum; i < l; i++)
                {
                    sheet.AutoSizeColumn(i);
                }
            }
        }

        public static void WriteContractExcel(string fileLocation, FileStream excelFile3, string sheetName, int headerRowIndex, DataTable dt)
        {
            IWorkbook workbook;
            using (excelFile3)
            {
                workbook = new XSSFWorkbook(excelFile3);

                ISheet sheet = workbook.GetSheet(sheetName);
                if (sheet != null)
                {
                    ICell cellTmp = null;
                    IRow RowTmp = sheet.GetRow(headerRowIndex);
                    int cellCount = RowTmp.LastCellNum;//LastCellNum = PhysicalNumberOfCells
                    int rowCount = sheet.LastRowNum;
                    cellTmp = RowTmp.CreateCell(cellCount);
                    cellTmp.SetCellValue("导入结果");
                    ICellStyle style3 = workbook.CreateCellStyle();
                    style3.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
                    style3.VerticalAlignment = VerticalAlignment.Center;
                    cellTmp.CellStyle = style3;
                    for (int x = 1; x <= rowCount; x++)
                    {
                        RowTmp = sheet.GetRow(x);
                        string pid = GetCellValue(RowTmp.GetCell(1)).Split(',')[0];
                        string cno = GetCellValue(RowTmp.GetCell(5));
                        cellTmp = RowTmp.CreateCell(cellCount);
                        foreach (DataRow dr in dt.Rows)
                        {
                            string dp = Convert.ToString(dr["province"]).Split(',')[0];
                            string dc = Convert.ToString(dr["contractno"]);
                            if (pid == dp && cno == dc)
                            {

                                cellTmp.CellStyle = style3;
                                cellTmp.SetCellValue(Convert.ToString(dr["result"]));
                                break;
                            }
                        }
                    }
                }
            }
            FileStream files = new FileStream(fileLocation, FileMode.Create);
            workbook.Write(files);
            files.Close();
        }

        public static string ReturnZero(string e) {
            try {
            if (e == "") return "0"; else return Convert.ToString(Convert.ToDouble(e));
            }catch(Exception ex)
            {
                Logger.Log(ex);
                return "0";
            }
        }

        public static string ReturnItemId(string c)
        {
            string r = "";
            switch (c)
            { 
                case "发货":
                        r = "21";
                break;
                case "完工":
                            r = "5";
                break;
                case "初验":
                            r = "7";
                break;
                case "终验":
                            r = "9";
                break;
                default:break;
            }
            return r;
        }
        public static DataTable DataTableFromContractRelation(Stream excelFileStream, string sheetName,int headerRowIndex,int datarowindex)
        {
            DataTable table;
            using (excelFileStream)
            {
                IWorkbook workbook;
                workbook = new XSSFWorkbook(excelFileStream);
                ISheet sheet = workbook.GetSheet(sheetName);
               
                if (sheet != null)
                {
                    table = RenderFromExcel(sheet, headerRowIndex, datarowindex);
                }
                else
                {
                    table = new DataTable("错误的Sheet名称或总表Sheet不存在");
                }
            }
            return table;
        }

        /// <summary>
        /// Excel表格转换成DataTable
        /// </summary>
        /// <param name="sheet">表格</param>
        /// <param name="headerRowIndex">标题行索引号，如第一行为0</param>
        /// <returns></returns>
        private static DataTable RenderFromExcel(ISheet sheet, int headerRowIndex,int datarowIndex)
        {
            DataTable table = new DataTable();
            ICell cellTmp = null;
            string strTmp;

            IRow headerRow = sheet.GetRow(headerRowIndex);
            int cellCount = headerRow.LastCellNum;//LastCellNum = PhysicalNumberOfCells
            int rowCount = sheet.LastRowNum;//LastRowNum = PhysicalNumberOfRows - 1

            //handling header.
            for (int i = headerRow.FirstCellNum; i < cellCount; i++)
            {
                DataColumn column = new DataColumn(headerRow.GetCell(i).StringCellValue);
                table.Columns.Add(column);
            }

            for (int i = datarowIndex; i <= rowCount; i++)
            {
                IRow row = sheet.GetRow(i);
                DataRow dataRow = table.NewRow();
                if (row != null)
                {
                    for (int j = row.FirstCellNum; j < cellCount; j++)
                    {
                        if (row.GetCell(j) != null)
                        {
                            cellTmp = row.GetCell(j);
                            strTmp = GetCellValue(cellTmp);
                            strTmp = Validation.GetSafeSQL(strTmp);

                            //if string has two '-',try it
                            if (strTmp.Length <= 10 && strTmp.Length - strTmp.Replace("-", "").Length == 2)
                            {
                                try
                                {
                                    dataRow[j] = cellTmp.DateCellValue;
                                }
                                catch
                                {
                                    dataRow[j] = strTmp;
                                }
                            }
                            else
                                dataRow[j] = strTmp;

                        }
                    }
                }
                table.Rows.Add(dataRow);
            }

            return table;
        }
        public static void RenderToExcel(DataTable table, HttpContext context, string fileName)
        {
            using (MemoryStream ms = RenderToExcel(table))
            {
                RenderToBrowser(ms, context, fileName);
            }
        }

        public static void RenderToBrowser(MemoryStream ms, HttpContext context, string fileName)
        {
            fileName = HttpUtility.UrlEncode(fileName);
            context.Response.Headers.Add("Content-Disposition", "attachment;fileName=" + fileName);
            byte[] content = ms.ToArray();
            context.Response.Body.Write(content, 0, content.Length);
        }

        public static MemoryStream RenderToExcel(DataTable table)
        {
            MemoryStream ms = new MemoryStream();

            using (table)
            {
                IWorkbook workbook = new HSSFWorkbook();
                ISheet sheet = workbook.CreateSheet();
                IRow headerRow = sheet.CreateRow(0);

                // handling header.
                foreach (DataColumn column in table.Columns)
                    headerRow.CreateCell(column.Ordinal).SetCellValue(column.Caption);//If Caption not set, returns the ColumnName value

                // handling value.
                int rowIndex = 1;

                foreach (DataRow row in table.Rows)
                {
                    IRow dataRow = sheet.CreateRow(rowIndex);

                    foreach (DataColumn column in table.Columns)
                    {
                        dataRow.CreateCell(column.Ordinal).SetCellValue(row[column].ToString());
                    }
                    rowIndex++;
                }
                AutoSizeColumns(sheet);

                workbook.Write(ms);
                ms.Flush();
                ms.Position = 0;
            }

            return ms;
        }
        public static void UpdateContractExcel(string fileLocation,Stream excelFileStream, string sheetName, int headerRowIndex, int datarowIndex,DataTable dt)
        {
            IWorkbook workbook;
            using (excelFileStream)
            {
                workbook = new XSSFWorkbook(excelFileStream);
                ISheet sheet = workbook.GetSheet(sheetName);
                //int cellCount = sheet.GetRow(headerRowIndex).LastCellNum;//LastCellNum = PhysicalNumberOfCells
                int rowCount = sheet.LastRowNum;//LastRowNum = PhysicalNumberOfRows - 1
                for (int i = datarowIndex; i <= rowCount; i++)
                {
                    IRow RowTmp = sheet.GetRow(i);
                    ICell cellTmp = null;
                    int cellCount = RowTmp.LastCellNum;
                    cellTmp = RowTmp.CreateCell(cellCount);

                    for (int j = 0; j < dt.Rows.Count; j++) { 
                        if(GetCellValue(RowTmp.GetCell(2))==Convert.ToString(dt.Rows[j]["合同编号"])&& GetCellValue(RowTmp.GetCell(5)) == Convert.ToString(dt.Rows[j]["核销合同"]))
                        {
                            cellTmp.SetCellValue(Convert.ToString(dt.Rows[j]["Result"]));
                            break;
                        }
                    }
                }
            }
            FileStream files = new FileStream(fileLocation, FileMode.Create);
            workbook.Write(files);
            files.Close();
        }

        public static DataTable RenderFromInstallEquipmentExcel(FileStream filestream, string sheetName, int headerRowIndex,int datarowindex)
        {
            DataTable table;
            using (filestream)
            {
                IWorkbook workbook;
                workbook = new XSSFWorkbook(filestream);
                ISheet sheet = workbook.GetSheet(sheetName);

                if (sheet != null)
                {
                    table = RenderFromExcel(sheet, headerRowIndex, datarowindex);
                }
                else
                {
                    table = new DataTable("错误的Sheet名称或总表Sheet不存在");
                }
            }
            return table ;
        }

        public static void WriteInstallEquipmentExcel(string fileLocation, FileStream excelFile3, string sheetName, int headerRowIndex,DataTable dt)
        {
            IWorkbook workbook;
            using (excelFile3)
            {
                workbook = new XSSFWorkbook(excelFile3);

                ISheet sheet = workbook.GetSheet(sheetName);
                if (sheet != null)
                {
                    ICell cellTmp = null;
                    IRow RowTmp = sheet.GetRow(headerRowIndex);
                    int cellCount = RowTmp.LastCellNum;//LastCellNum = PhysicalNumberOfCells
                    int rowCount = sheet.LastRowNum;
                    cellTmp = RowTmp.CreateCell(cellCount);
                    cellTmp.SetCellValue("导入结果");
                    ICellStyle style3 = workbook.CreateCellStyle();
                    style3.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
                    style3.VerticalAlignment = VerticalAlignment.Center;
                    cellTmp.CellStyle = style3;
                    for (int x = 1; x <= rowCount; x++)
                    {
                        RowTmp = sheet.GetRow(x);
                        string pid =GetCellValue(RowTmp.GetCell(3));
                        string cid = GetCellValue(RowTmp.GetCell(4));
                        string sn = GetCellValue(RowTmp.GetCell(5));
                        cellTmp = RowTmp.CreateCell(cellCount);
                        foreach (DataRow dr in dt.Rows)
                        {
                            if (pid == Convert.ToString(dr["Production"]) && cid == Convert.ToString(dr["Equipment"]) && sn == Convert.ToString(dr["SN"]))
                            {

                                cellTmp.CellStyle = style3;
                                cellTmp.SetCellValue(Convert.ToString(dr["result"]));
                                break;
                            }
                        }
                    }
                }
            }
            FileStream files = new FileStream(fileLocation, FileMode.Create);
            workbook.Write(files);
            files.Close();
        }

        public static void WriteWeiBaoExcel(string fileLocation, FileStream excelFile3, string sheetName, int headerRowIndex, DataTable dt)
        {
            IWorkbook workbook;
            using (excelFile3)
            {
                workbook = new XSSFWorkbook(excelFile3);

                ISheet sheet = workbook.GetSheet(sheetName);
                if (sheet != null)
                {
                    ICell cellTmp = null;
                    IRow RowTmp = sheet.GetRow(headerRowIndex);
                    int cellCount = RowTmp.LastCellNum;//LastCellNum = PhysicalNumberOfCells
                    int rowCount = sheet.LastRowNum;
                    cellTmp = RowTmp.CreateCell(cellCount);
                    cellTmp.SetCellValue("导入结果");
                    ICellStyle style3 = workbook.CreateCellStyle();
                    style3.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
                    style3.VerticalAlignment = VerticalAlignment.Center;
                    cellTmp.CellStyle = style3;
                    for (int x = 2; x <= rowCount; x++)
                    {
                        RowTmp = sheet.GetRow(x);
                        string sid = GetCellValue(RowTmp.GetCell(0));
                        cellTmp = RowTmp.CreateCell(cellCount);
                        foreach (DataRow dr in dt.Rows)
                        {
                            if (sid == Convert.ToString(dr["StationId"]))
                            {
                                cellTmp.CellStyle = style3;
                                cellTmp.SetCellValue(Convert.ToString(dr["result"]));
                                break;
                            }
                        }
                    }
                }
            }
            FileStream files = new FileStream(fileLocation, FileMode.Create);
            workbook.Write(files);
            files.Close();
        }


        public static void SetCellValue(ISheet sheet, int rowIndex, int colIndex, string value, CellType cellType = CellType.String)
        {
            IRow row = sheet.GetRow(rowIndex);
            row.GetCell(colIndex).SetCellType(cellType);
            row.GetCell(colIndex).SetCellValue(value);

        }
    }


}
