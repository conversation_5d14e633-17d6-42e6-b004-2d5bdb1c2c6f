﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{

    /// <summary>
    /// 写监控点存储规则
    /// </summary>
    public sealed class SetStorageRule : BMessage
    {

        public List<Device> Devices { get; private set; }

        public SetStorageRule(string fsuId, List<Device> devices)
            : base()
        {
            MessageType = (int)BMessageType.SET_STORAGERULE;

            FSUID = fsuId;
            Devices = devices;

        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmlDoc = CreateXmlDocument();
                AppendPK_Type(xmlDoc, "Request", BMessageType.SET_STORAGERULE.ToString());

                XmlElement xe2 = xmlDoc.CreateElement("Info");
                XmlElement xe21 = xmlDoc.CreateElement("FSUID");
                xe21.InnerText = FSUID;
                xe2.AppendChild(xe21);

                XmlElement xe22 = xmlDoc.CreateElement("Value");
                XmlElement xe221 = xmlDoc.CreateElement("DeviceList");
                foreach (Device device in Devices)
                {
                    XmlElement xeDevice = xmlDoc.CreateElement("Device");
                    xeDevice.SetAttribute("ID", device.DeviceId);
                    foreach (TStorageRule storeageRule in device.TStorageRules)
                    {
                        XmlElement xeSemaphore = xmlDoc.CreateElement("TStorageRule");
                        xeSemaphore.SetAttribute("Type", ((int)storeageRule.Type).ToString());
                        xeSemaphore.SetAttribute("ID", storeageRule.ID);
                        string strSignalNumber = storeageRule.SignalNumber.ToString().Trim();
                        if (strSignalNumber.Length == 1)
                        {
                            strSignalNumber = "00" + strSignalNumber;
                        }
                        else if (strSignalNumber.Length == 2)
                        {
                            strSignalNumber = "0" + strSignalNumber;
                        }
                        xeSemaphore.SetAttribute("SignalNumber", strSignalNumber);
                        xeSemaphore.SetAttribute("AbsoluteVal", storeageRule.AbsoluteVal.ToString());
                        xeSemaphore.SetAttribute("RelativeVal", storeageRule.RelativeVal.ToString());
                        xeSemaphore.SetAttribute("StorageInterval", storeageRule.StorageInterval.ToString());
                        xeSemaphore.SetAttribute("StorageRefTime", storeageRule.StorageRefTime);
                        xeDevice.AppendChild(xeSemaphore);
                    }
                    xe221.AppendChild(xeDevice);
                }
                xe22.AppendChild(xe221);
                xe2.AppendChild(xe22);

                XmlNode root = xmlDoc.SelectSingleNode("Request");
                root.AppendChild(xe2);
                StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("SetStorageRule.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmlDoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetStorageRule.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetStorageRule.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            string str = String.Format("{0}, {1}, {2}:",
                MessageId, (BMessageType)MessageType, FSUID);
            sb.Append(str);

            foreach (Device device in Devices)
            {
                sb.AppendFormat("[{0}]", device.ToString());
            }
            return sb.ToString();
        }


    }
}
