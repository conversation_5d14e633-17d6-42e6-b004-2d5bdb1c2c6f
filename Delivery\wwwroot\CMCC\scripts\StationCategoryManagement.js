﻿'use strict';

var getUrl = function (selector, type) {
    var url;
    switch (selector) {
        case '#dg_StationCategory':
            switch (type) {
                case 'query':
                    url = '../api/DataItemOfStationType';
                    break;
                case 'add':
                    url = '../api/DataItemOfStationTypeNew';
                    break;
                case 'delete':
                    url = '../api/DataItemOfStationTypeDelete';
                    break;
                default:
                    break;
            }
            break;
        case '#dg_StationCategoryMapping':
            switch (type) {
                case 'query':
                case 'edit':
                    url = "../api/StationTypeMap";
                    break;
                default:
                    break;
            }
            break;
        default:
            break;
    }
    return url;
};

$(function () {
    // 编辑项下拉框数据源
    var sitewebCategoryData, sitewebCategoryDic = {};

    var getComboData = function () {
        $.ajax({
            type: "GET",
            contentType: "application/json; charset=UTF-8",
            url: '../api/SiteWebStationType',
            async: false,
            success: function (data) {
                for (var i = 0; i < data.length; i++) {
                    sitewebCategoryDic[data[i].ItemId] = data[i].ItemValue;
                }
                sitewebCategoryData = data;
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
            }
        });
    };
    getComboData();

    // datagrid加载初始化
    var load = function () {
        $('#dg_StationCategory').datagrid({
            //title: '站址类型管理',
            toolbar: '#toolbar',
            method: 'GET',
            url: getUrl('#dg_StationCategory', 'query'),
            rownumbers: true,
            width: document.body.clientWidth * 0.98,
            scrollbarSize: 0,
            fitColumns: true,
            singleSelect: true,
            striped: true,
            columns: [[
                { field: 'ItemId', title: '站址类型ID', width: 100, editor: { type: 'numberbox', options: { required: true, validType: 'maxLength[255]', tipPosition: 'top', validateOnBlur: true } } },
                { field: 'ItemValue', title: '站址类型', width: 100, editor: { type: 'textbox', options: { required: true, validType: 'maxLength[255]', tipPosition: 'top', validateOnBlur: true } } }
                //{ field: 'ItemValue', title: '站址类型', width: 100, editor: { type: 'combobox', options: { required: true, editable: false, valueField: 'ItemId', textField: 'ItemValue', data: categoryData } } }
            ]]
        });
    };
    load();
    var mappingLoad = function () {
        $('#dg_StationCategoryMapping').datagrid({
            //title: '站址类型映射管理',
            toolbar: '#toolbarMapping',
            method: 'GET',
            url: getUrl('#dg_StationCategoryMapping', 'query'),
            rownumbers: true,
            width: document.body.clientWidth * 0.98,
            scrollbarSize: 0,
            fitColumns: true,
            singleSelect: true,
            striped: true,
            columns: [[
                { field: 'WR_ItemValue', title: '站址类型', width: 100 },
                {
                    field: 'SiteWeb_ItemId', title: 'SiteWeb站址类型', width: 100,
                    editor: {
                        type: 'combobox', options: {
                            required: true,
                            editable: false,
                            valueField: 'ItemId',
                            textField: 'ItemValue',
                            data: sitewebCategoryData
                        }
                    },
                    formatter: function (value, row, index) {
                        return value ? sitewebCategoryDic[value] : value;
                    }
                }
            ]],
            onDblClickRow: function (index, row) {
                edit($('#dg_StationCategoryMapping').selector);
            }
        });
    };
    mappingLoad();

    //添加按钮事件
    var add = function (selector) {
        var dg = $(selector);
        if (dg.datagrid('getEditingRowIndexs').length > 0 || dg.datagrid('getChanges').length > 0)
            alertInfo('一次仅允许修改一条记录,请先保存上一次修改！');
        else {
            dg.datagrid('appendRow', { isNewRow: true });
            var editIndex = dg.datagrid('getRows').length - 1;
            dg.datagrid('selectRow', editIndex);
            dg.datagrid('beginEdit', editIndex);
            var editor = dg.datagrid('getEditors', editIndex)[0];
            if (editor.type === 'numberbox')
                editor.target.next().find('input').eq(0).focus();
        }
    };

    // 保存事件
    var save = function (selector) {
        var saveData, operationType;
        var dg = $(selector);
        var editingIndexArr = dg.datagrid('getEditingRowIndexs');
        if (editingIndexArr.length > 0) { // 新增或修改
            var isValid = true;
            var editors = dg.datagrid('getEditors', editingIndexArr[0]);
            for (var i = 0; i < editors.length; i++) {
                if ((editors[i].type === 'textbox' || editors[i].type === 'numberbox') && !editors[i].target.textbox('isValid')) {
                    isValid = false;
                    break;
                }
            }
            dg.datagrid('endEdit', editingIndexArr[0]);
            var row = dg.datagrid('getChanges')[0];
            if (isValid && row && !Object.values(row).includes('')) {
                saveData = row;
                if (row.isNewRow)
                    operationType = 'add';
                else
                    operationType = 'edit';
            } else if (!isValid) {
                alertInfo('请正确输入编辑项！');
                dg.datagrid('beginEdit', editingIndexArr[0]);
            }
        } else { // 删除或无修改
            var changes = dg.datagrid('getChanges');
            if (changes.length > 0) {
                saveData = changes[0];
                operationType = 'delete';
            }
        }
        if (operationType && saveData)
            $.messager.confirm('确认', '您确认保存吗？',
                function (r) {
                    if (r) {
                        $.ajax({
                            type: "POST",
                            contentType: "application/json; charset=UTF-8",
                            url: getUrl(selector, operationType),
                            data: JSON.stringify(saveData),
                            success: function (data) {
                                if (data.errormsg === "OK") {
                                    $(selector).datagrid('acceptChanges');
                                    alertInfo('保存成功！');
                                    if (selector === '#dg_StationCategory') {
                                        mappingLoad();
                                    }
                                } else {
                                    alertInfo(data.errormsg, 'error');
                                    $(selector).datagrid('selectRow', editingIndexArr[0]);
                                    $(selector).datagrid('beginEdit', editingIndexArr[0]);
                                }
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                                $(selector).datagrid('selectRow', editingIndexArr[0]);
                                $(selector).datagrid('beginEdit', editingIndexArr[0]);
                            }
                        });
                    } else
                        $(selector).datagrid('selectRow', editingIndexArr[0]);
                    $(selector).datagrid('beginEdit', editingIndexArr[0]);
                });

    };

    // 删除事件
    var del = function (selector) {
        var dg = $(selector);
        var row = dg.datagrid('getSelected');
        if (!row)
            alertInfo('请选择要修改的记录！');
        else {
            var changes = dg.datagrid('getChanges');
            var editingRowIndexArr = dg.datagrid('getEditingRowIndexs');
            if (editingRowIndexArr.length > 0 && editingRowIndexArr[0] !== dg.datagrid('getRowIndex', row) || changes.length > 0 && dg.datagrid('getRowIndex', changes[0]) !== editingRowIndexArr[0])
                alertInfo('一次仅允许修改一条记录,请先保存上一次修改！');
            else
                dg.datagrid('deleteRow', dg.datagrid('getRowIndex', row));
        }
    };

    // 取消事件
    var cancel = function (selector) {
        var dg = $(selector);
        var editingIndexArr = dg.datagrid('getEditingRowIndexs');
        if (editingIndexArr.length > 0)
            dg.datagrid('endEdit', editingIndexArr[0]);
        dg.datagrid('rejectChanges');
    };

    //编辑事件
    var edit = function (selector) {
        var dg = $(selector);
        var row = dg.datagrid('getSelected');
        var selectIndex = dg.datagrid('getRowIndex', row);
        var editingIndexArr = dg.datagrid('getEditingRowIndexs');
        if (!row)
            alertInfo('请选择要修改的记录！');
        else if ((editingIndexArr.length > 0 || dg.datagrid('getChanges').length > 0) && dg.datagrid('getRowIndex', row) !== editingIndexArr[0])
            alertInfo('一次仅允许修改一条记录,请先保存上一次修改！');
        else
            dg.datagrid('beginEdit', selectIndex);
    };

    //新增按钮
    $('#btn_Add').on('click', function () {
        add($(this).parent().attr('data-grid'));
    });

    //删除按钮
    $('#btn_Delete,#btn_DeleteMapping').on('click', function () {
        del($(this).parent().attr('data-grid'));
    });

    //保存按钮
    $('#btn_Save,#btn_SaveMapping').on('click', function () {
        save($(this).parent().attr('data-grid'));
    });

    //取消按钮
    $('#btn_Cancel,#btn_CancelMapping').on('click', function () {
        cancel($(this).parent().attr('data-grid'));
    });

    //编辑按钮
    $('#btn_EditMapping').on('click', function () {
        edit($(this).parent().attr('data-grid'));
    });

    $('div.mask').remove();
});
