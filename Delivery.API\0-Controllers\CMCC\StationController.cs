﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using Delivery.BSL;
using Delivery.Common;

namespace Delivery.API.CMCC
{
    [Route("CMCC/api/[controller]")]
    public class StationController : BaseController
    {
        [HttpGet("[action]")]
        public string GetNeedApproveResource()
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            DataTable dt = ccbsl.GetNeedApproveResource(LogonId);
            StringBuilder sb = new StringBuilder();
            sb.Append("{");
            for (int i = 0; i < dt.Columns.Count; i++)
            {
                sb.AppendFormat("\"{0}\":\"{1}\"", dt.Columns[i].ColumnName, dt.Rows[0][i].ToString());
                if (i < dt.Columns.Count - 1)
                    sb.Append(",");
            }
            sb.Append("}");
            return sb.ToString();
        }

        [HttpGet("[action]")]
        public string GetStationInfo(string StructureId, string StationName, string StationCode, string strBegin, string strEnd, string StationType, string StationStatus, int page, int rows)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            DataTable dt = ccbsl.GetStationInfo(StructureId, StationName, StationCode, strBegin, strEnd, StationType, StationStatus, LogonId);
            return Json4EasyUI.onDataGrid(dt, page, rows);
        }

        [HttpGet("[action]")]
        public string GetStationInfoCheck(string StructureId, string StationName, string StationCode, string strBegin, string strEnd, string StationType, string StationStatus, int page, int rows)
        {
            if (string.IsNullOrEmpty(StructureId))
                StructureId = "-1";
            if (string.IsNullOrEmpty(StationType))
                StationType = "-1";
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            DataTable dt = ccbsl.GetStationInfoCheck(StructureId, StationName, StationCode, strBegin, strEnd, StationType, StationStatus, LogonId);
            return Json4EasyUI.onDataGrid(dt, page, rows);
        }

        [HttpPost("[action]")]
        public string ApproveStationInfo([FromForm] string WRStationId)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            return ccbsl.ApproveStation(WRStationId);
        }

        [HttpPost("[action]")]
        public string RejectWRStationInfo([FromForm] string WRStationId, [FromForm] string StationCode, [FromForm] string RejectCause)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            return ccbsl.RejectWRStation(WRStationId, StationCode, RejectCause);

        }

        [HttpPost("[action]")]
        public string DeleteStation([FromForm] string WRStationId)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            return ccbsl.DeleteStation(WRStationId, LogonId);
        }

        [HttpPost("[action]")]
        public string NewStation([FromForm] string StructureId, [FromForm] string Province, [FromForm] string City, [FromForm] string County, [FromForm] string StationCategory, [FromForm] string StationName, [FromForm] string Address, [FromForm] string Remark, [FromForm] string ContractNo, [FromForm] string ProjectName)
        {
            //string structureId = HttpContext.Request.Form["StructureId"].ToString();
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            string result = string.Empty;
            result = ccbsl.InsertStation(StructureId, Province, City, County, StationCategory, StationName, Address, Remark, ContractNo, ProjectName, LogonId).ToString();
            return result;
        }

        [HttpPost("[action]")]
        public string ModifyStation([FromForm] string WRStationId, [FromForm] string StructureId, [FromForm] string Province, [FromForm] string City, [FromForm] string County, [FromForm] string StationCategory, [FromForm] string StationName, [FromForm] string Address, [FromForm] string Remark, [FromForm] string ContractNo, [FromForm] string ProjectName)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            string result = string.Empty;
            result = ccbsl.UpdateStation(WRStationId, StructureId, Province, City, County, StationCategory, StationName, Address, Remark, ContractNo, ProjectName, LogonId).ToString();
            return result;
        }

        [HttpGet("[action]")]
        public string GetGroupInfo()
        {
            DataTable dt = new DeliveryDataBsl().GetGroupInfo();
            List<Entity.EasyUITree> list = new List<Entity.EasyUITree>();
            Entity.EasyUITree tree = new Entity.EasyUITree();
            tree.id = -1;
            tree.text = "请选择";
            list.Add(tree);
            //list.AddRange(GetAllChildren(dt, dt.Select("parentId=0").First().Field<int>("id")));
            DataRow[] drs = dt.Select("parentId=0");
            //int tempValue = (int)drs[0].ItemArray[0];
            list.AddRange(GetAllChildren(dt, (int)drs[0].ItemArray[0]));
            return JsonHelper.SerializeData(list);
        }

        [HttpGet("[action]")]
        public string GetAssetStation()
        {
            try
            {
                DeliveryDataBsl ccbsl = new DeliveryDataBsl();
                DataTable dt = ccbsl.GetAssetStation();

                // 将DataTable转换为List<object>格式，确保JSON序列化正确
                var result = new List<object>();
                if (dt != null && dt.Rows.Count > 0)
                {
                    foreach (DataRow row in dt.Rows)
                    {
                        result.Add(new
                        {
                            ItemId = row["ItemId"]?.ToString() ?? "",
                            ItemValue = row["ItemValue"]?.ToString() ?? ""
                        });
                    }
                }

                return JsonHelper.SerializeData(result);
            }
            catch (Exception ex)
            {
                // 返回空数组，避免前端解析错误
                return "[]";
            }
        }

        private List<Entity.EasyUITree> GetAllChildren(DataTable source, int parentId)
        {
            List<Entity.EasyUITree> list = new List<Entity.EasyUITree>();
            string filter = String.Format("parentId = {0}", parentId);
            DataRow[] rows = source.Select(filter);

            int rowCount = rows.Count();
            for (int i = 0; i < rowCount; i++)
            {
                Entity.EasyUITree tree = new Entity.EasyUITree();
                tree.id = int.Parse(rows[i].ItemArray[0].ToString());
                tree.text = rows[i].ItemArray[1].ToString();
                tree.children = GetAllChildren(source, tree.id);
                list.Add(tree);
            }
            return list;

        }

        /// <summary>
        /// 导出 Excel
        /// </summary>
        /// <returns></returns>              
        [HttpGet("[action]")]
        public IActionResult ExportExcel(string StructureId, string StationName, string StationCode, string strBegin, string strEnd, string StationType, string StationStatus)
        {
            DataTable dataTable = GetExportData( StructureId, StationName, StationCode, strBegin, strEnd, StationType, StationStatus, LogonId);

            string name = "站址管理";
            byte[] bytes = ExcelHelper.DataTableToExcel(dataTable, null, name);
            return File(bytes, "application/ms-excel", $"{name}_{DateTime.Now:yyyyMMddHHmmssfff}.xlsx");
        }

        private DataTable GetExportData(string StructureId, string StationName, string StationCode, string strBegin, string strEnd, string StationType, string StationStatus, string LogonId)
        {
            try
            {
                DeliveryDataBsl ccbsl = new DeliveryDataBsl();
                DataTable dt = ccbsl.GetStationInfo(StructureId, StationName, StationCode, strBegin, strEnd, StationType, StationStatus, LogonId);
                string[] cols = new string[]
                {
                        "RowNumber","StructureName","StationName","StationCode","StationCategoryName", "ContractNo", "ProjectName",
                        "StatusName","UserName","ApplyTime","ApproveTime","CountyName","RejectCause"
                };
                DataTable myDt = dt.DefaultView.ToTable(true, cols);
                myDt.Columns["RowNumber"].ColumnName = "序号";
                myDt.Columns["StructureName"].ColumnName = "分组";
                myDt.Columns["StationName"].ColumnName = "站址名称";
                myDt.Columns["StationCode"].ColumnName = "站址编码";
                myDt.Columns["StationCategoryName"].ColumnName = "站址类型";
                myDt.Columns["ContractNo"].ColumnName = "合同号";
                myDt.Columns["ProjectName"].ColumnName = "工程名称";
                myDt.Columns["StatusName"].ColumnName = "申请单状态";
                myDt.Columns["UserName"].ColumnName = "申请人";
                myDt.Columns["ApplyTime"].ColumnName = "申请日期";
                myDt.Columns["ApproveTime"].ColumnName = "批准日期";
                myDt.Columns["CountyName"].ColumnName = "行政区";
                myDt.Columns["RejectCause"].ColumnName = "退回原因";

                return myDt;
            }
            catch(Exception ex)
            {
                Logger.Log(ex);
            }
            return null;
        }
    }
}
