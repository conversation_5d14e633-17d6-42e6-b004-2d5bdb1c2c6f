﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <procedures>  
		  
			  
	<procedure owner="" name="SP_Get_WRHouseCondition" grant="">
		<parameters>			
		</parameters>
		<replaces>					
		</replaces>
		<body>
			<![CDATA[ 
		
			SELECT ROW_NUMBER() over(order by ApplyTime DESC) as Row<PERSON><PERSON><PERSON>,
			WRHouseId,WRStationId,
						StructureId, StructureName,
						StationCode,StationName,HouseCode,HouseName,
						HouseStatus,StatusName ,
						UserId, UserName,
						ApplyTime,ApproveTime,
						RejectCause,
						Remark  
			 FROM (
					SELECT 
							a.WRHouseId,a.WRStationId,
							b.StructureId, e.<PERSON>,
							b.<PERSON><PERSON>,f.<PERSON>,a.<PERSON>, g.<PERSON>,
							a.<PERSON>tat<PERSON>, c.ItemValue StatusName ,
							a.User<PERSON>d, a.SWUserName UserName,
							a.ApplyTime,a.ApproveTime,
							a.RejectCause,
							a.Remark
						FROM WR_HouseManagement a
						INNER JOIN WR_StationManagement b ON a.WRStationId = b.WRStationId
						INNER JOIN WR_DataItem c ON a.HouseStatus = c.ItemId AND c.EntryId = 5 
						INNER JOIN TBL_StationStructure e ON b.StructureId = e.StructureId
						INNER JOIN TBL_Station f ON b.SWStationId = f.StationId
						INNER JOIN TBL_House g ON b.SWStationId = g.StationId AND a.SWHouseId = g.HouseId
						WHERE a.HouseStatus = 3 
						$[WhereTime] $[WhereStructure] $[WhereSWHouseName] $[WhereHouseCode] $[WhereSWStationName] $[WhereStationCode]    
						union all     
						SELECT 
								a.WRHouseId,a.WRStationId,
								b.StructureId, e.StructureName,
								b.StationCode,b.StationName,a.HouseCode, a.HouseName,
								a.HouseStatus, c.ItemValue StatusName ,
								a.UserId, a.SWUserName UserName,
								a.ApplyTime,a.ApproveTime,
								a.RejectCause,
								a.Remark
							FROM WR_HouseManagement a
							INNER JOIN WR_StationManagement b ON a.WRStationId = b.WRStationId
							INNER JOIN WR_DataItem c ON a.HouseStatus = c.ItemId AND c.EntryId = 5 
							INNER JOIN TBL_StationStructure e ON b.StructureId = e.StructureId
							WHERE a.HouseStatus = 1
						$[WhereTime] $[WhereStructure] $[WhereHouseName] $[WhereHouseCode] $[WhereStationName] $[WhereStationCode]    
			  ) AS combined_result
			ORDER BY combined_result.ApplyTime DESC;			
        
			]]>
		</body>
	</procedure>

    <procedure owner="" name="SP_Get_WRHouse" grant="">
      <parameters>
        <parameter name="StructureId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="HouseName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="HouseCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Status" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StartTime" type="datetime" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EndTime" type="datetime" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="iUserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
        <replace keyName ="WhereStructure"> and e.LevelPath LIKE @StructureId</replace>
        <replace keyName ="WhereStationName"> and b.StationName like @StationName</replace>
        <replace keyName ="WhereSWStationName"> and f.StationName like @StationName</replace>
        <replace keyName ="WhereStationCode"> and b.StationCode like @StationCode</replace>
        <replace keyName ="WhereHouseName"> and a.HouseName like @HouseName</replace>
        <replace keyName ="WhereSWHouseName"> and g.HouseName like @HouseName</replace>
        <replace keyName ="WhereHouseCode"> and a.HouseCode like @HouseCode</replace>
        <replace keyName ="WhereStatus"> and a.HouseStatus = CAST(@Status AS INTEGER)</replace>
        <replace keyName="WhereTime">
          <![CDATA[ AND a.ApplyTime >= @StartTime AND a.ApplyTime <= @EndTime ]]>
        </replace>
        <replace keyName ="RoleMapNotNull">
          SELECT DISTINCT UserId FROM WR_HouseManagement
        </replace>
        <replace keyName ="RoleMapNull">
          SELECT DISTINCT UserId FROM TBL_UserRoleMap
          WHERE RoleId IN (SELECT RoleId FROM TBL_UserRoleMap WHERE UserId = @iUserId)
        </replace>
        <replace keyName ="SqlStr1">
          <![CDATA[ SELECT a.WRHouseId,a.WRStationId,
		      b.StructureId, e.StructureName,
		      b.StationCode,f.StationName,a.HouseCode, g.HouseName,
		      a.HouseStatus, c.ItemValue StatusName ,
		      a.UserId, a.SWUserName UserName, a.ApplyTime,a.ApproveTime, a.RejectCause,a.Remark
	        FROM WR_HouseManagement a]]>
        </replace>
        <replace keyName ="SqlStr2">
          <![CDATA[  SELECT a.WRHouseId,a.WRStationId,
			    b.StructureId, e.StructureName,
			    b.StationCode,b.StationName,a.HouseCode, a.HouseName,
			    a.HouseStatus, c.ItemValue StatusName , a.UserId, a.SWUserName UserName,
			    a.ApplyTime,a.ApproveTime, a.RejectCause, a.Remark
		      FROM WR_HouseManagement a ]]>
        </replace>
      </replaces>
      <body>
        <![CDATA[
       SELECT ROW_NUMBER() over(order by ApplyTime DESC) as RowNumber,
			 T.WRHouseId,T.WRStationId, T.StructureId, T.StructureName,
			 T.StationCode,T.StationName,T.HouseCode, T.HouseName,
			 T.HouseStatus,T.StatusName , T.UserId, T.UserName,
			 T.ApplyTime,T.ApproveTime, T.RejectCause, T.Remark  		
	     FROM (
        $[SqlStr1]
        INNER JOIN WR_StationManagement b ON a.WRStationId = b.WRStationId
	      INNER JOIN WR_DataItem c ON a.HouseStatus = c.ItemId AND c.EntryId = 5
	      INNER JOIN TBL_StationStructure e ON b.StructureId = e.StructureId
	      INNER JOIN TBL_Station f ON b.SWStationId = f.StationId
	      INNER JOIN TBL_House g ON b.SWStationId = g.StationId AND a.SWHouseId = g.HouseId
	      INNER JOIN ($[RoleMapNotNull]$[RoleMapNull]) h ON a.UserId = h.UserId
	      WHERE a.HouseStatus = 3
        $[WhereTime] $[WhereStructure] $[WhereSWHouseName]  $[WhereHouseCode] $[WhereSWStationName] $[WhereStationCode] $[WhereStatus]
        union all
        $[SqlStr2]
        INNER JOIN WR_StationManagement b ON a.WRStationId = b.WRStationId
		    INNER JOIN WR_DataItem c ON a.HouseStatus = c.ItemId AND c.EntryId = 5 
		    INNER JOIN TBL_StationStructure e ON b.StructureId = e.StructureId
		    INNER JOIN ($[RoleMapNotNull]$[RoleMapNull]) h ON a.UserId = h.UserId
		    WHERE a.HouseStatus != 3
        $[WhereTime] $[WhereStructure] $[WhereHouseName]  $[WhereHouseCode] $[WhereStationName] $[WhereStationCode] $[WhereStatus]
       ) AS T ORDER BY T.ApplyTime DESC; 
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_RejectWRHouse_HouseStatus" grant="">
      <parameters>
        <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT WR_HouseManagement.HouseStatus FROM WR_HouseManagement WHERE WR_HouseManagement.WRHouseId = @WRHouseId;
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_RejectWRHouse_UpdateHouse" grant="">
      <parameters>
        <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="RejectCause" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
					UPDATE WR_HouseManagement
	        SET HouseStatus = 2, RejectCause = @RejectCause
	        WHERE WR_HouseManagement.WRHouseId =  @WRHouseId;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_RejectWRHouse_UpdateFsu" grant="">
      <parameters>
        <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="RejectCause" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        UPDATE WR_FsuManagement
          SET FsuStatus = 2 ,RejectCause = '级联退回:' || @RejectCause
          WHERE WR_FsuManagement.WRHouseId = @WRHouseId;
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Upd_WRHouse_SelectInfo" grant="">
      <parameters>
        <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT a.HouseStatus myStatus, a.SWHouseId SWHouseId, b.SWStationId SWStationId, b.StationCode StationCode
	      FROM WR_HouseManagement a 
	      INNER JOIN WR_StationManagement b ON a.WRStationId = b.WRStationId
	      WHERE a.WRHouseId = @WRHouseId;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Upd_WRHouse_SelectOriHouseCode" grant="">
      <parameters>
        <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT WR_HouseManagement.HouseCode OriHouseCode FROM WR_HouseManagement WHERE WR_HouseManagement.WRHouseId = @WRHouseId;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Upd_WRHouse_SelectHouseInfo" grant="">
      <parameters>
        <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="HouseName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="WRStationId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT 'X' FROM WR_HouseManagement WHERE WR_HouseManagement.HouseName = @HouseName
        AND WR_HouseManagement.WRStationId = CAST(@WRStationId AS INTEGER) AND WR_HouseManagement.WRHouseId != @WRHouseId
			]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Upd_WRHouse_upadteHouse" grant="">
      <parameters>
        <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="HouseName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Remark" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        UPDATE TBL_House SET HouseName = @HouseName, Description = @Remark
		    WHERE TBL_House.StationId = @SWStationId AND TBL_House.HouseId = @SWHouseId;
			]]>
      </body>
    </procedure>
	  <procedure owner="" name="SP_Upd_WRHouse_upadteRoomCMCC" grant="">
      <parameters>
        <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="HouseCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="HouseName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Remark" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        UPDATE TBL_RoomCMCC SET RoomID = @HouseCode, RoomName = @HouseName, 
        SiteID = @StationCode, Description = @Remark
		    WHERE TBL_RoomCMCC.StationId = @SWStationId AND TBL_RoomCMCC.HouseId = @SWHouseId;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Upd_WRHouse_upadteConfigChangeMicroLog" grant="">
      <parameters>
        <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        UPDATE TBL_ConfigChangeMicroLog SET EditType = 2, UpdateTime = now()
		    WHERE ObjectId = CAST(@SWStationId AS TEXT) AND ConfigId = 5;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Upd_WRHouse_upadteConfigChangeMacroLog" grant="">
      <parameters>
        <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        UPDATE TBL_ConfigChangeMacroLog SET EditType = 2, UpdateTime = now()
          WHERE ObjectId = CAST(@SWStationId AS TEXT) AND ConfigId = 1;
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Upd_WRHouse_getLastString" grant="">
      <parameters>
        <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="HouseCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="HouseName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Remark" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT 'HouseCode:' || CAST(@HouseCode AS TEXT) || '-HouseName:' || CAST(@HouseName AS TEXT) || '-Remark:' || CAST(@Remark AS TEXT) AS LastString
          FROM WR_HouseManagement WHERE WR_HouseManagement.WRHouseId = @WRHouseId;
      ]]>
      </body>
     </procedure>
     <procedure owner="" name="SP_Upd_WRHouse_getStationName" grant="">
      <parameters>
        <parameter name="WRStationId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT COALESCE(StationName,'') StationName FROM WR_StationManagement WHERE WR_StationManagement.WRStationId = CAST(@WRStationId AS INTEGER);
      ]]>
      </body>
    </procedure>
  <procedure owner="" name="SP_Upd_WRHouse_HouseManagement" grant="">
      <parameters>
        <parameter name="WRStationId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="HouseCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="HouseName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Remark" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="myStatus" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        UPDATE WR_HouseManagement
	      SET WRStationId = CAST(@WRStationId AS INTEGER)
		    , HouseCode = @HouseCode 
		    , HouseName = @HouseName
		    , Remark = @Remark 
		    ,HouseStatus = CASE WHEN @myStatus = 2 THEN  1  ELSE HouseStatus END 
		    ,RejectCause = CASE WHEN @myStatus = 2 THEN  '' ELSE RejectCause END
	      WHERE WR_HouseManagement.WRHouseId = @WRHouseId;
			]]>
      </body>
     </procedure>
	  <procedure owner="" name="SP_ApproveWRHouse_Select_StationManagement" grant="">
		  <parameters>
			  <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
            SELECT a.StationStatus StationStatus,a.SWStationId SWStationId,b.HouseStatus HouseStatus
	        FROM WR_StationManagement a 
	        INNER JOIN WR_HouseManagement b ON a.WRStationId = b.WRStationId
	        WHERE b.WRHouseId = @WRHouseId;
			]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRHouse_maxHouseId" grant="">
		  <parameters>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
            SELECT max(HouseId) maxHouseId
	        FROM TBL_House WHERE TBL_House.StationId = @SWStationId;
			]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRHouse_Insert_House" grant="">
		  <parameters>
			  <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="currHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="currentTime" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
            INSERT INTO TBL_House(HouseId,StationId,HouseName,Description,LastUpdateDate) 
	        SELECT @currHouseId , @SWStationId , HouseName, Remark , @currentTime::timestamp
	        FROM WR_HouseManagement T
	        WHERE T.WRHouseId = @WRHouseId;
			]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRHouse_Insert_RoomCMCC" grant="">
		  <parameters>
			  <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="currHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
            INSERT INTO TBL_RoomCMCC (StationId, HouseId, RoomID, RoomName, SiteID, Description)	
	        SELECT @SWStationId, @currHouseId, a.HouseCode, a.HouseName, b.StationCode, a.Remark
	        FROM WR_HouseManagement a
	        INNER JOIN WR_StationManagement b ON a.WRStationId = b.WRStationId
	        WHERE a.WRHouseId = @WRHouseId;
			]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRHouse_Update_HouseManagement" grant="">
		  <parameters>
			  <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="currHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="currentTime" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
            UPDATE WR_HouseManagement
   	        SET HouseStatus = 3
           	,ApproveTime =@currentTime::timestamp
   	        ,SWHouseId = @currHouseId	,RejectCause = ''
   	        WHERE WR_HouseManagement.WRHouseId = @WRHouseId;
			]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRHouse_Insert_ConfigChangeMicroLog" grant="">
		  <parameters>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="currHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="currentTime" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
            INSERT INTO TBL_ConfigChangeMicroLog(ObjectId,ConfigId,EditType,UpdateTime)
	        SELECT (@SWStationId::TEXT || '.' || @currHouseId::TEXT) ObjectId, 5 ConfigId, 1 EditType,@currentTime::timestamp UpdateTime;
			]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRHouse_Update_ConfigChangeMacroLog" grant="">
		  <parameters>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="currentTime" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
            UPDATE TBL_ConfigChangeMacroLog
	       SET EditType = 2, UpdateTime = @currentTime::timestamp
	       WHERE TBL_ConfigChangeMacroLog.ObjectId = CAST(@SWStationId AS TEXT) AND TBL_ConfigChangeMacroLog.ConfigId = 1;
			]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRHouse_wr_syncinfo" grant="">
		  <parameters>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="currHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
			  INSERT INTO wr_syncinfo(StationId, HouseId, SyncType) VALUES (@SWStationId, @currHouseId, 2);
			]]>
		  </body>
	  </procedure>
    <procedure owner="" name="SP_Add_WRHouse_getStationId" grant="">
      <parameters>
        <parameter name="WRStationId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT T.SWStationId iSWStationId FROM WR_StationManagement T WHERE T.WRStationId = CAST(@WRStationId AS INTEGER);
			]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Add_WRHouse_selMonitorUnitCMCC" grant="">
      <parameters>
        <parameter name="HouseName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="iSWStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT 'X' FROM TSL_MonitorUnitCMCC WHERE StationId = @iSWStationId AND RoomName = @HouseName;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Add_WRHouse_selHouseName" grant="">
      <parameters>
        <parameter name="HouseName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="WRStationId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT 'X' FROM WR_HouseManagement A WHERE A.HouseName = @HouseName AND A.WRStationId = CAST(@WRStationId AS INTEGER);
			]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Add_WRHouse_selUserInfo" grant="">
      <parameters>
        <parameter name="LogonId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT TBL_Account.UserId,TBL_Account.UserName SWUserName FROM TBL_Account 
	      WHERE TBL_Account.LogonId = @LogonId ;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Add_WRHouse_insertHouse" grant="">
      <parameters>
        <parameter name="WRStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="HouseCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="HouseName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="myStatus" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWUserName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Remark" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        INSERT INTO WR_HouseManagement (WRStationId,HouseCode,HouseName,HouseStatus,UserId, SWUserName,ApplyTime,Remark)
	      VALUES (CAST(@WRStationId AS INTEGER), @HouseCode, @HouseName, @myStatus, @UserId, @SWUserName, now()::timestamp, @Remark);
			]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Add_WRHouse_getWRHouseId" grant="">
      <parameters>
        <parameter name="WRStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="HouseName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT WR_HouseManagement.WRHouseId FROM WR_HouseManagement 
        WHERE WR_HouseManagement.WRStationId = CAST(@WRStationId AS INTEGER) AND WR_HouseManagement.HouseName = @HouseName;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Add_WRHouse_getStationName" grant="">
      <parameters>
        <parameter name="WRStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT COALESCE(T.StationName,'') StationName FROM WR_StationManagement T WHERE T.WRStationId = CAST(@WRStationId AS INTEGER);
			]]>
      </body>
    </procedure>
  </procedures>
</root>
