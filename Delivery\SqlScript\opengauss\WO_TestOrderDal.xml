﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <procedures>
    <procedure owner="" name="WO_TestOrder_NotLock" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        Update WO_TestOrder set  LockUserId=null, LockTime=null where WO_TestOrder.OrderId =@OrderId
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_TestOrder_Lock" grant="">
      <parameters>
        <parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="OrderId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="LockTime" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        Update WO_TestOrder set  LockUserId=@UserId, LockTime=@LockTime where WO_TestOrder.OrderId =@OrderId
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_TestOrder_LockInfo" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        SELECT a.UserName LockUserName, LockTime from WO_TestOrder w INNER JOIN TBL_Account a ON a.UserId = w.LockUserId
	      where w.OrderId = @OrderId
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_TestOrder_ValidateConfig" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        SELECT 1 FROM WO_TestOrder w, TBL_Station s WHERE w.OrderId=@OrderId AND  w.StationId=s.StationId
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_TestOrder_ValidateEquipment" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        SELECT EquipmentId FROM WO_TestOrderEquipItem w WHERE w.OrderId=@OrderId AND  w.EquipmentId 
        NOT IN (SELECT EquipmentId FROM TBL_Equipment)
      ]]>
      </body>
    </procedure>
    
    <procedure owner="" name="WO_CreateTestOrder_getNeedUpload" grant="">
      <body>
        <![CDATA[ 
        select ValueInt NeedUpload from WO_SysConfig where WO_SysConfig.ConfigId=2;
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_CreateTestOrder_getApplyUserName" grant="">
      <parameters>
        <parameter name="ApplyUserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        select UserName ApplyUserName from TBL_Account where TBL_Account.UserId = @ApplyUserId;
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_CreateTestOrder_getInstallCompanyId" grant="">
      <parameters>
        <parameter name="InstallCompany" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        select CompanyId InstallCompanyId from WO_InstallCompany where WO_InstallCompany.CompanyName = @InstallCompany;
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_CreateTestOrder_getInstallClerkId" grant="">
      <parameters>
        <parameter name="InstallCompanyId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="InstallClerk" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        select ClerkId InstallClerkId from WO_InstallClerk where WO_InstallClerk.CompanyId=@InstallCompanyId And WO_InstallClerk.ClerkName = @InstallClerk; 
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_CreateTestOrder_getApplyUserFsuVendor" grant="">
      <parameters>
        <parameter name="ApplyUserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
       </parameters>
      <body>
        <![CDATA[ 
        SELECT r.RoleName RoleName from TBL_Account a
	      INNER JOIN TBL_UserRoleMap m ON a.UserId=m.UserId
	      INNER JOIN TBL_UserRole  r ON m.RoleId=r.RoleId
	      where a.UserId=@ApplyUserId;
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_CreateTestOrder_insertTestOrder" grant="">
      <parameters>
        <parameter name="OrderType" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Latitude" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Longitude" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EquipItems" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="InstallCompany" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="InstallClerk" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ApplyUserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ApplyUserName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ApplyUserFsuVendor" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ApplyTime" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StateChangeTime" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="OrderState" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StateSetUserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StateSetUserName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="InstallCompanyId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="InstallClerkId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="NeedUpload" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        INSERT INTO WO_TestOrder (
	      OrderType, StationId, Latitude, Longitude, EquipItems, InstallCompany, InstallClerk, ApplyUserId,
        ApplyUserName,ApplyUserFsuVendor, ApplyTime, StateChangeTime, OrderState, StateSetUserId, StateSetUserName,
	      MyOrderId, InstallCompanyId, InstallClerkId, NeedUpload)
	      VALUES (
	      @OrderType, @StationId, @Latitude, @Longitude, @EquipItems, @InstallCompany, @InstallClerk, @ApplyUserId, 
        @ApplyUserName, @ApplyUserFsuVendor, @ApplyTime, @StateChangeTime, @OrderState, @StateSetUserId, @StateSetUserName,
	      -1 , @InstallCompanyId, @InstallClerkId, @NeedUpload);
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_CreateTestOrder_getOrderId" grant="">
      <body>
        <![CDATA[ 
        SELECT MAX(orderId) OrderId FROM WO_TestOrder;
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_CreateTestOrder_updateTestOrder" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="MyOrderId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        update WO_TestOrder set MyOrderId = @MyOrderId where WO_TestOrder.OrderId=@OrderId;
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_CreateTestOrder_insertTestOrderArtSelfCheckList" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        insert into WO_TestOrderArtSelfCheckList
	      (OrderId,CheckDicId,CheckDicNote)
	      select @OrderId,CheckDicId,CheckDicNote from WO_DicUserArtSelfCheck;
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_CreateTestOrder_insertTestOrderExpertCheckList" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        insert into WO_TestOrderExpertCheckList
	      (OrderId,CheckDicId,CheckDicNote)
	      select @OrderId,CheckDicId,CheckDicNote from WO_DicUserExpertCheck;
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_CreateTestOrder_insertTestOrderFlow" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ApplyUserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StateSetUserName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FlowText" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ApplyTime" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        INSERT INTO WO_TestOrderFlow (
		    OrderId, OldOrderState, NewOrderState, StateSetUserId, StateSetUserName, 
		    Decision, Note, IsApprove, FlowText, SaveTime)
        VALUES ( @OrderId, 0, 1, @ApplyUserId, @StateSetUserName,
		    NULL, NULL, 1, @FlowText, @ApplyTime);
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_CreateTestOrder_selectTestOrder" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="MyOrderId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        select @OrderId as OrderId, @MyOrderId as MyOrderId;
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="BU_V3_UpdateEquipNameAndHouseIdCMCC_getEqs" grant="">
      <parameters>
        <parameter name="FSUID" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        SELECT e.StationId, e.EquipmentId , w.DeviceName, w.SWHouseId   
	      FROM TBL_Equipment e
	      INNER JOIN TBL_EquipmentCMCC eu ON eu.FSUID=@FSUID AND eu.EquipmentId =e.EquipmentId 
	      INNER JOIN WR_FsuManagement wf ON wf.FsuCode = @FSUID
	      INNER JOIN WR_DeviceManagement w ON w.WRFsuId = wf.WRFsuId 
		    AND w.SWStationId=eu.StationId AND w.DeviceCode=eu.DeviceID 
		    AND w.DeviceName <>'' AND  w.DeviceName IS NOT NULL 
		    AND w.SWHouseId IS NOT NULL 
	      where e.EquipmentName <> w.DeviceName or e.HouseId <>  w.SWHouseId;    
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="BU_V3_UpdateEquipNameAndHouseIdCMCC_updateEquipment" grant="">
      <parameters>
        <parameter name="TmpDeviceName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="TmpSWHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="TmpEquipmentId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        UPDATE TBL_Equipment SET EquipmentName = @TmpDeviceName, HouseId = @TmpSWHouseId
        WHERE TBL_Equipment.EquipmentId=@TmpEquipmentId;	    
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="BU_V3_UpdateEquipNameAndHouseIdCMCC_updateEquipmentCMCC" grant="">
      <parameters>
        <parameter name="TmpDeviceName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="TmpStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="TmpEquipmentId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        UPDATE TBL_EquipmentCMCC SET DeviceName = @TmpDeviceName 
        WHERE TBL_EquipmentCMCC.StationId = @TmpStationId AND TBL_EquipmentCMCC.EquipmentId = @TmpEquipmentId;	    
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="BU_V3_UpdateEquipNameAndHouseIdCMCC_isExistEquipItem" grant="">
      <parameters>
        <parameter name="TmpEquipmentId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        SELECT 1 FROM WO_TestOrderEquipItem  a
				INNER JOIN WO_TestOrder b ON a.OrderId = b.OrderId AND b.OrderState = 4
				WHERE a.EquipmentId = @TmpEquipmentId	    
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="BU_V3_UpdateEquipNameAndHouseIdCMCC_updateEquipmentTemplate" grant="">
      <body>
        <![CDATA[ 
        UPDATE TBL_EquipmentTemplate a
        SET EquipmentBaseType = b.BaseEquipmentID
        FROM TBL_BaseEquipmentCategoryMap b
        WHERE a.EquipmentCategory = b.EquipmentCategory
        AND a.EquipmentBaseType IS NULL;   
      ]]>
      </body>
    </procedure>
	  <procedure owner="" name="BU_V3_UpdateEquipNameAndHouseIdCMCC_gettsl_monitorunitcmcc" grant="">
		  <parameters>
			  <parameter name="FSUID" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[ 
                select MonitorUnitId from tsl_monitorunitcmcc a where a.FSUID = @FSUID; 
              ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="BU_V3_UpdateEquipNameAndHouseIdCMCC_getwr_syncinfo" grant="">
		  <parameters>
			  <parameter name="MonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[ 
			    select 1 from wr_syncinfo where MonitorUnitId = @MonitorUnitId
              ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="BU_V3_UpdateEquipNameAndHouseIdCMCC_insertwr_syncinfo" grant="">
		  <parameters>
			  <parameter name="FSUID" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[ 
			    insert into wr_syncinfo(StationId, MonitorUnitId, SyncType) 
		select a.StationId, a.MonitorUnitId, 3 from tsl_monitorunitcmcc a where a.FSUID = @FSUID;
              ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="BU_V3_UpdateEquipNameAndHouseIdCMCC_updatewr_syncinfo" grant="">
		  <parameters>
			  <parameter name="MonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[ 
			    update wr_syncinfo SET SyncFlag = 0 where MonitorUnitId = @MonitorUnitId;
              ]]>
		  </body>
	  </procedure>
	  
  </procedures>
</root>
