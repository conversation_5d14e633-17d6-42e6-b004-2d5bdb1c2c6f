﻿using Common.Logging.Pro;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;




using BDSTool.Entity.S2;
using BDSTool.DBUtility.Common;
using BDSTool.DBUtility;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace BDSTool.BLL.S2
{
    class EquipmentTemplateBiz
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static bool InsertConfigOfTSEC(TBL_EquipmentTemplate et) {
            logger.InfoFormat("InsertConfigOfSEC();EquipTempId={0}", et.EquipmentTemplateId);


            if (et.Signals.Count > 0) {
                if (!SignalBiz.SaveEntityList(et.Signals)) {
                    logger.ErrorFormat("DeepSaveEntity(); failed to SignalBiz.SaveEntityList; EquipmentTemplateName={0}", et.EquipmentTemplateName);
                    return false;
                }

            }

            if (et.Events.Count > 0) {
                if (!EventBiz.SaveEntityList(et.Events)) {
                    logger.ErrorFormat("DeepSaveEntity(); failed to EventBiz.SaveEntityList; EquipmentTemplateName={0}", et.EquipmentTemplateName);
                    return false;
                }

            }

            if (et.Controls.Count > 0) {
                if (!ControlBiz.SaveEntityList(et.Controls)) {
                    logger.ErrorFormat("DeepSaveEntity(); failed to Controls.SaveEntityList; EquipmentTemplateName={0}", et.EquipmentTemplateName);
                    return false;
                }

            }
            return true;
        }

        public static bool SaveEntity(TBL_EquipmentTemplate et) {
            try {
                string sql;
                if (CommonUtils.IsNoProcedure)
                {
                    string temp = Public_ExecuteSqlService.Instance.NoStore_TBLEquipmentTemplate_GetInsertSql();
                    sql = string.Format(temp , et.EquipmentTemplateId, SHelper.GetPara(et.EquipmentTemplateName), SHelper.GetPara(et.ParentTemplateId), SHelper.GetPara(et.Memo), SHelper.GetPara(et.ProtocolCode),
                    SHelper.GetPara(et.EquipmentCategory), SHelper.GetPara(et.EquipmentType), SHelper.GetPara(et.Property), SHelper.GetPara(et.Description), SHelper.GetPara(et.EquipmentStyle),
                    SHelper.GetPara(et.Unit), SHelper.GetPara(et.Vendor), SHelper.GetPara(et.EquipmentBaseType), SHelper.GetPara(et.StationCategory)
                    );
                }
                else 
                { 
                    sql = string.Format("INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId,EquipmentTemplateName,ParentTemplateId,Memo,ProtocolCode,EquipmentCategory,EquipmentType,Property,Description,EquipmentStyle, Unit,Vendor,EquipmentBaseType, StationCategory) VALUES({0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13})",
                        et.EquipmentTemplateId, SHelper.GetPara(et.EquipmentTemplateName), SHelper.GetPara(et.ParentTemplateId), SHelper.GetPara(et.Memo), SHelper.GetPara(et.ProtocolCode), 
                        SHelper.GetPara(et.EquipmentCategory), SHelper.GetPara(et.EquipmentType), SHelper.GetPara(et.Property), SHelper.GetPara(et.Description), SHelper.GetPara(et.EquipmentStyle),
                        SHelper.GetPara(et.Unit), SHelper.GetPara(et.Vendor), SHelper.GetPara(et.EquipmentBaseType), SHelper.GetPara(et.StationCategory)
                        );
                }
                DBHelper.ExecuteNonQuery(sql);
            }
            catch (Exception ex) {
                logger.ErrorFormat("EquipmentTemplateBiz.SaveEntity();(EquipmentTemplateId,EquipmentTemplateName)={0}.{1};Error={2}",
                    et.EquipmentTemplateId, et.EquipmentTemplateName, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }
      
    }
}
