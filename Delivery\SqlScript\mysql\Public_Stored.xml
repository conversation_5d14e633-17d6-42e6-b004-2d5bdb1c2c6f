﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
		<procedure owner="" name="Select_WR_OperationRecord" grant="">
			<parameters>
				<parameter name="LogonId" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
				 SELECT a.UserId iUserId  FROM TBL_Account a WHERE a.LogonId = @LogonId ;
			 ]]>
			</body>
		</procedure>
		<procedure owner="" name="Insert_WR_OperationRecord" grant="">
			<parameters>
				<parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="StationName" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="OpCategory" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="OpItemId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="OpItemValue" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="UpdateString" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="LastString" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="iUserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="CurrentTime" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
			 INSERT INTO WR_OperationRecord(WRStationId,WRStationName,OpCategory,OpItemId,OpItemValue,OpDateTime,UpdateString,LastString,OpUserId)
 		     VALUES(@WRStationId,@StationName,@OpCategory,@OpItemId,@OpItemValue,@CurrentTime,@UpdateString,@LastString,@iUserId);
			 ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_GenerateDeviceCode_MaxNo" grant="">
			<parameters>
				<parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceType" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
		    
		     SELECT max(Convert(right(a.DeviceCode,6),signed))  maxNo
	         FROM WR_DeviceManagement a
	         WHERE a.WRStationId = @WRStationId AND LEFT(a.DeviceCode, 4) = @DeviceType;
        
      ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_GenerateDeviceCode_DeviceCount" grant="">
			<parameters>
				<parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceType" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
		    
		     SELECT count(*) DeviceCount
	         FROM WR_DeviceManagement
	         WHERE WR_DeviceManagement.WRStationId = @WRStationId AND LEFT(WR_DeviceManagement.DeviceCode, 4) = @DeviceType;
        
      ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_GenerateHouseCode_ICurrentNo" grant="">
			<parameters>
				<parameter name="SHouseId" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
		   SELECT  max(convert(substring(a.HouseCode,9,length(a.HouseCode) - 8), signed)) iCurrentNo 
		   FROM WR_HouseManagement a
		   INNER JOIN WR_StationManagement b ON a.WRStationId = b.WRStationId
		   WHERE LEFT(a.HouseCode, 8) = @SHouseId AND b.SWStationId = @SWStationId;   
      ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_GenerateHouseCode_RoomId" grant="">
			<parameters>
				<parameter name="SHouseId" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
		   SELECT  max(substring(a.RoomID,9,length(a.RoomID) - 8)) RoomID
		   FROM TBL_RoomCMCC a 
    	   WHERE LEFT(a.RoomID, 8) = @SHouseId AND convert(substring(a.RoomID,9,length(a.RoomID) - 8),signed) = 1
		   AND a.StationId = @SWStationId;		
      ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_GenerateHouseCode_ICurrentNo2" grant="">
			<parameters>
				<parameter name="SHouseId" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
		  SELECT  max(Convert(substring(a.HouseCode,9,length(a.HouseCode) - 8),signed)) iCurrentNo
		  FROM WR_HouseManagementCUCC a 
		  INNER JOIN WR_StationManagementCUCC b ON a.WRStationId = b.WRStationId
		  WHERE LEFT(a.HouseCode, 8) = @SHouseId 
		  AND b.SWStationId = @SWStationId;
      ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_GenerateStationCode_X" grant="">
			<parameters>
				<parameter name="v_CountyId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
		   SELECT 'X' FROM WR_StationCode WHERE WR_StationCode.CountyId = @v_CountyId
      ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_GenerateStationCode_CountyId" grant="">
			<parameters>
				<parameter name="v_CountyId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
		   INSERT INTO WR_StationCode(CountyId, MinValue, CurrentValue)VALUES(@v_CountyId, 1, 0);
      ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_GenerateStationCode_CurrentValue" grant="">
			<parameters>
				<parameter name="v_CountyId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
		   SELECT ifnull(CurrentValue,0) + 1 v_CurrentValue FROM WR_StationCode WHERE WR_StationCode.CountyId = @v_CountyId;
      ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_GenerateStationCode_UpdateTable" grant="">
			<parameters>
				<parameter name="v_CountyId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="v_CurrentValue" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
		   UPDATE WR_StationCode SET WR_StationCode.CurrentValue = @v_CurrentValue WHERE WR_StationCode.CountyId = @v_CountyId;
      ]]>
			</body>
		</procedure>
	<procedure owner="" name="PCT_DeleteEquipment_SamplerUnitId" grant="">
		  <parameters>
			  <parameter name="EquipmentId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="StationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		   SELECT t.SamplerUnitId v_SamplerUnitId FROM TBL_Equipment t WHERE t.EquipmentId=@EquipmentId AND t.StationId=@StationId;
      ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteEquipment_EquipmentProjectInfo" grant="">
		  <parameters>
			  <parameter name="EquipmentId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="StationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		   DELETE FROM TBL_EquipmentProjectInfo t WHERE t.EquipmentId=@EquipmentId AND t.StationId=@StationId;
      ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteEquipment_MonitorUnitSignal" grant="">
		  <parameters>
			  <parameter name="EquipmentId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="StationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		   DELETE FROM TSL_MonitorUnitSignal t WHERE t.EquipmentId=@EquipmentId AND t.StationId=@StationId;
      ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteEquipment_MonitorUnitEvent" grant="">
		  <parameters>
			  <parameter name="EquipmentId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="StationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		   DELETE FROM TSL_MonitorUnitEvent t WHERE t.EquipmentId=@EquipmentId AND t.StationId=@StationId;
      ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteEquipment_MonitorUnitControl" grant="">
		  <parameters>
			  <parameter name="EquipmentId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="StationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		   DELETE FROM TSL_MonitorUnitControl t WHERE t.EquipmentId=@EquipmentId AND t.StationId=@StationId;
      ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteEquipment_Door" grant="">
		  <parameters>
			  <parameter name="EquipmentId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="StationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		   DELETE FROM TBL_Door t WHERE t.EquipmentId=@EquipmentId AND t.StationId=@StationId;
      ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteEquipment_Select_Equipment" grant="">
		  <parameters>
			  <parameter name="EquipmentId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="StationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="v_SamplerUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		  SELECT 'x' FROM TBL_Equipment t WHERE t.SamplerUnitId=@v_SamplerUnitId AND t.EquipmentId <> @EquipmentId AND t.StationId=@StationId
      ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteEquipment_SamplerUnit" grant="">
		  <parameters>
			  <parameter name="v_SamplerUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		   DELETE FROM TSL_SamplerUnit t WHERE t.SamplerUnitId=@v_SamplerUnitId;
      ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteEquipment_Equipment" grant="">
		  <parameters>
			  <parameter name="EquipmentId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="StationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		  DELETE FROM TBL_Equipment t WHERE t.EquipmentId=@EquipmentId AND t.StationId=@StationId;
      ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteEquipment_Update_Equipment" grant="">
		  <parameters>
			  <parameter name="EquipmentId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="StationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		  UPDATE TBL_Equipment t SET t.ParentEquipmentId=NULL WHERE t.ParentEquipmentId = CONVERT(@EquipmentId , CHAR) AND t.StationId=@StationId;
      ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteEquipment_Select_TableName" grant="">
		  <parameters>
			  <parameter name="EquipmentId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="StationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		  select 'x' from INFORMATION_SCHEMA.TABLES where TABLE_NAME='CorePointSECMap'
      ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteEquipment_CorePointSECMap" grant="">
		  <parameters>
			  <parameter name="EquipmentId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="StationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		   DELETE FROM  CorePointSECMap t where t.EquipmentId=@EquipmentId AND StationId=@StationId;
      ]]>
		  </body>
	  </procedure>
	   <procedure owner="" name="PCT_DeleteStation_StationProjectInfo" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
	          DELETE FROM 	TBL_StationProjectInfo WHERE StationId = @SStationId;
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_MonitorUnitProjectInfo" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
	         DELETE FROM 	TBL_MonitorUnitProjectInfo WHERE StationId=@SStationId;
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_EquipmentProjectInfo" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		
	         DELETE FROM 	TBL_EquipmentProjectInfo WHERE StationId=@SStationId;
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_House" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		
	         DELETE FROM  TBL_House WHERE StationId=@SStationId; 
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_MonitorUnitSignal" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		
	         DELETE FROM  TSL_MonitorUnitSignal WHERE StationId=@SStationId;
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_MonitorUnitEvent" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		
	        DELETE FROM  TSL_MonitorUnitEvent WHERE StationId=@SStationId;
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_MonitorUnitControl" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		
	        DELETE FROM  TSL_MonitorUnitControl WHERE StationId=@SStationId;
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_SamplerUnit" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		
	       DELETE FROM  TSL_SamplerUnit WHERE MonitorUnitId IN (SELECT MonitorUnitId FROM TSL_MonitorUnit WHERE StationId=@SStationId);
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_Port" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		
	        DELETE FROM  TSL_Port WHERE MonitorUnitId IN (SELECT MonitorUnitId FROM TSL_MonitorUnit WHERE StationId=@SStationId);
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_MonitorUnit" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		
	        DELETE FROM  TSL_MonitorUnit WHERE StationId=@SStationId;
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_EventMask" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		
	         DELETE FROM  TBL_EventMask WHERE StationId=@SStationId;
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_EquipmentMask" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		
	         DELETE FROM  TBL_EquipmentMask WHERE StationId=@SStationId;
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_StationMask" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		
	         DELETE FROM  TBL_StationMask WHERE StationId=@SStationId;
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_Equipment" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		
	         DELETE FROM  TBL_Equipment WHERE StationId=@SStationId;
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_Station" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		
	        DELETE FROM  TBL_Station WHERE StationId=@SStationId;
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_StationStructureMap" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		
	        DELETE FROM  TBL_StationStructureMap WHERE StationId=@SStationId;
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_StationSwatchMap" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		
	        DELETE FROM  TBL_StationSwatchMap WHERE StationId=@SStationId;
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_ControlLogAction" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		
	       DELETE FROM  TBL_ControlLogAction WHERE LogActionId IN(SELECT LogActionId FROM TBL_EventLogAction WHERE StationId=@SStationId);
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_EventLogAction" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		
	       DELETE FROM  TBL_EventLogAction WHERE StationId=@SStationId;
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_DoorTimeGroup" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		
	        DELETE FROM  TBL_DoorTimeGroup WHERE DoorId IN (SELECT DoorId FROM TBL_Door WHERE StationId =@SStationId);
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_Door" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
		
	         DELETE FROM  TBL_Door WHERE StationId=@SStationId;
			  
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_Select_CorePointSECMap" grant="">
		  <parameters>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
	        select 'x' from INFORMATION_SCHEMA.TABLES where TABLE_NAME='CorePointSECMap' 
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="PCT_DeleteStation_CorePointSECMap" grant="">
		  <parameters>
			  <parameter name="SStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
	         DELETE FROM  CorePointSECMap where StationId=@SStationId;
         ]]>
		  </body>
	  </procedure>
		<procedure owner="" name="SP_GenerateSiteWebId_DataItem" grant="">
			<parameters>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
	         SELECT TBL_DataItem.ItemId PostalCode FROM TBL_DataItem WHERE TBL_DataItem.EntryId=62;
         ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_GenerateSiteWebId_TableId" grant="">
			<parameters>
				<parameter name="TableName" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
	         SELECT m.TableId TableId 
             FROM  TBL_PrimaryKeyIdentity m   WHERE  m.TableName  = @TableName;
         ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_GenerateSiteWebId_PrimaryKeyValue" grant="">
			<parameters>
				<parameter name="TableId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="PostalCode" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
	         SELECT 'X' FROM TBL_PrimaryKeyValue p
             WHERE p.TableId = @TableId AND p.PostalCode = @PostalCode
         ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_GenerateSiteWebId_Insert_PrimaryKeyValue" grant="">
			<parameters>
				<parameter name="TableId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="PostalCode" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
	         INSERT INTO TBL_PrimaryKeyValue(TableId, PostalCode,MinValue,CurrentValue)
      	     VALUES(@TableId,@PostalCode ,1 ,1);
         ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_GenerateSiteWebId_Update_PrimaryKeyValue" grant="">
			<parameters>
				<parameter name="TableId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="PostalCode" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
	         UPDATE  TBL_PrimaryKeyValue
	         SET TBL_PrimaryKeyValue.CurrentValue = IFNULL(CurrentValue,0) + 1  WHERE TBL_PrimaryKeyValue.TableId  = @TableId
	         AND TBL_PrimaryKeyValue.PostalCode = @PostalCode;
         ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_GenerateSiteWebId_SiteWebId" grant="">
			<parameters>
				<parameter name="TableId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="PostalCode" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
	         SELECT  p.PostalCode*1000000+p.CurrentValue SiteWebId 
             FROM  TBL_PrimaryKeyValue p WHERE p.TableId = @TableId
             AND p.PostalCode = @PostalCode;
         ]]>
			</body>
		</procedure>
		<procedure owner="" name="BMDeviceConfigAdd_insertIntoTBLEquipmentTemplate" grant="">
			<parameters>
				<parameter name="EquipmentTemplateId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentTemplateName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentCategory" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DevDescribe" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Model" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Brand" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[
					INSERT INTO TBL_EquipmentTemplate 
					(		EquipmentTemplateId,EquipmentTemplateName,ParentTemplateId,Memo,ProtocolCode,
							EquipmentCategory,EquipmentType,Property,Description,EquipmentStyle, 
							Unit,Vendor,EquipmentBaseType, StationCategory 
					) 
					VALUES(
							@EquipmentTemplateId,@EquipmentTemplateName, 0, '', '',
							@EquipmentCategory, 1, '', @DevDescribe, @Model,
							'', @Brand, NULL, 0 
					);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="BMDeviceConfigAdd_insertIntoTBLEquipment" grant="">
			<parameters>
				<parameter name="StationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Model" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="BeginRunTime" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Brand" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentCategory" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DevDescribe" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentTemplateId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="HouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="MonitorUnitId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SamplerUnitId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="RatedCapacity" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[
					INSERT INTO TBL_Equipment 
					(		StationId, EquipmentId, EquipmentName, EquipmentNo, EquipmentStyle,
							UsedDate, Vendor, EquipmentCategory, EquipmentType, EquipmentClass, 
							Description, EquipmentTemplateId, HouseId, MonitorUnitId, SamplerUnitId, 
							UpdateTime, EquipmentState,DisplayIndex,ConnectState,
							InstalledModule,RatedCapacity,Property
					) 
					values (	
							@StationId, @EquipmentId, @DeviceName, '', @Model,
							@BeginRunTime, @Brand, @EquipmentCategory, 1, -1,
							@DevDescribe, @EquipmentTemplateId, @HouseId, @MonitorUnitId, @SamplerUnitId, 
							now(), 1, 0, 1, 
							'0', @RatedCapacity, ''
					);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="BMDeviceConfigAdd_GetSiteName" grant="">
			<parameters>
				<parameter name="StationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[
					select a.SiteName  from TBL_StationCMCC a where a.StationId = @StationId limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="BMDeviceConfigAdd_insertIntoTBLEquipmentCMCC" grant="">
			<parameters>
				<parameter name="DeviceID" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FSUID" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="StationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="MonitorUnitId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="EquipmentId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="RoomName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceType" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DeviceSubType" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Model" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Brand" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="RatedCapacity" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Version" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="BeginRunTime" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="DevDescribe" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SiteName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>

			</parameters>
			<body>
				<![CDATA[
					INSERT INTO TBL_EquipmentCMCC 
					(		DeviceID, DeviceName, FSUID, StationId, MonitorUnitId, 
							EquipmentId, RoomName, DeviceType, DeviceSubType, Model, 
							Brand, RatedCapacity, Version, BeginRunTime, DevDescribe,
							SiteName) 
					VALUES 
					 (		@DeviceID, @DeviceName, @FSUID, @StationId, @MonitorUnitId, 
							@EquipmentId, @RoomName, @DeviceType, @DeviceSubType, @Model, 
							@Brand, @RatedCapacity, @Version, @BeginRunTime, @DevDescribe,
							@SiteName );
				]]>
			</body>
		</procedure>
		<procedure owner="" name="PBL_GenerateId_GetItemId" grant="">
			<body>
				<![CDATA[
					SELECT ItemId FROM TBL_DataItem WHERE EntryId = 62 limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="PBL_GenerateId_GetTableId" grant="">
			<parameters>
				<parameter name="tableName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[
					SELECT  m.TableId FROM TBL_PrimaryKeyIdentity m  WHERE m.TableName = @tableName limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="PBL_GenerateId_judgeExist1" grant="">
			<parameters>
				<parameter name="TableId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="PostalCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[
					SELECT 1 FROM TBL_PrimaryKeyValue a WHERE a.TableId = @TableId AND a.PostalCode = @PostalCode limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="PBL_GenerateId_InsertIntoTBLPrimaryKeyValue" grant="">
			<parameters>
				<parameter name="TableId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="PostalCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[
					INSERT INTO TBL_PrimaryKeyValue(
							 TableId        ,
							 PostalCode     ,
							 MinValue       ,
							 CurrentValue   )
					VALUES(
							 @TableId       ,
							 @PostalCode    ,
							 1              ,
							 1              );
				]]>
			</body>
		</procedure>
		<procedure owner="" name="PBL_GenerateId_UpdateTBLPrimaryKeyValue" grant="">
			<parameters>
				<parameter name="TableId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="PostalCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[
					UPDATE TBL_PrimaryKeyValue a SET a.CurrentValue = (IFNULL(a.CurrentValue,0) + 1) WHERE a.TableId = @TableId AND a.PostalCode = @PostalCode;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="PBL_GenerateId_GetGlobalIdentity" grant="">
			<parameters>
				<parameter name="TableId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="PostalCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[
					SELECT  (@PostalCode * 1000000 + a.CurrentValue) GlobalIdentity FROM TBL_PrimaryKeyValue a WHERE a.TableId = @TableId  AND a.PostalCode = @PostalCode;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="BM_SaveImportConfig_judgeExist1" grant="">
			<parameters>
				<parameter name="FSUID" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[
					select 1 from TBL_FsuConfig a where a.FSUID=@fsuId limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="BM_SaveImportConfig_UpdateTBLFsuConfig" grant="">
			<parameters>
				<parameter name="FSUID" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SCSyncCfgCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SCSyncCfgContent" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[
					update TBL_FsuConfig a
					set a.NeedSyncFlag = 0, a.SCSyncCfgTime = now(),a.SCSyncCfgCode = @SCSyncCfgCode, 
					a.SCSyncCount = (a.SCSyncCount + 1), a.LastSCSyncCfgContent = a.SCSyncCfgContent, a.SCSyncCfgContent = @SCSyncCfgContent
					where a.FSUID = @FSUID;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="BM_SaveImportConfig_InsertTBLFsuConfig" grant="">
			<parameters>
				<parameter name="FSUID" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SCSyncCfgCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SCSyncCfgContent" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[
					insert into TBL_FsuConfig
					( FSUID, NeedSyncFlag, SCSyncCfgTime, SCSyncCfgCode, SCSyncCfgContent, SCSyncCount) 
					values 
					( @FSUID, 0, now(), @SCSyncCfgCode, @SCSyncCfgContent, 1);
				]]>
			</body>
		</procedure>
	</procedures>
</root>
