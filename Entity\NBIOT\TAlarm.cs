﻿using System;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 告警信息的结构
    /// </summary>
    public class TAlarm
    {
        /// <summary>
        /// 告警序号
        /// </summary>
        [Newtonsoft.Json.JsonProperty("SN")]
        public string SerialNo { get; set; }

        /// <summary>
        /// 设备ID
        /// </summary>
        [Newtonsoft.Json.JsonProperty("DevID")]
        public string DeviceID { get; set; }

        private string _value;
        public string SID 
        {
            get
            {
                return _value;
            }
            set
            {
                _value = value;
                if (value != null)
                {
                    string[] sId = _value.Split(new char[] { ':' });
                    ID = sId[0];
                    SignalNumber = sId[1];
                }
            }
        }

        /// <summary>
        /// 监控点ID
        /// </summary>
        [Newtonsoft.Json.JsonIgnore]
        public string ID { get; set; }

        /// <summary>
        /// 同设备同类测点顺序号
        /// </summary>
        [Newtonsoft.Json.JsonIgnore]
        public string SignalNumber { get; set; }

        /// <summary>
        /// 网管告警编号 （告警标准化编号）
        /// </summary>
        [Newtonsoft.Json.JsonIgnore]
        public string NMAlarmID { get; set; }

        /// <summary>
        /// 告警时间
        /// </summary>
        [Newtonsoft.Json.JsonConverter(typeof(CustomDateTimeConverter))]
        [Newtonsoft.Json.JsonProperty("Time")]
        public DateTime? AlarmTime { get; set; }

        /// <summary>
        /// 告警级别
        /// </summary>
        [Newtonsoft.Json.JsonProperty("Level")]
        public EnumState AlarmLevel { get; set; }

        /// <summary>
        /// 告警标志
        /// </summary>
        [Newtonsoft.Json.JsonProperty("Flag")]
        public EnumFlag AlarmFlag { get; set; }

        /// <summary>
        /// 告警的事件描述
        /// </summary>
        [Newtonsoft.Json.JsonProperty("EDescrip")]
        public string AlarmDesc { get; set; }

        /// <summary>
        /// 告警触发值
        /// </summary>
        [Newtonsoft.Json.JsonProperty("Evalue")]
        public float? EventValue { get; set; }

        /// <summary>
        /// 预留字段
        /// </summary>
        [Newtonsoft.Json.JsonIgnore]
        public string AlarmRemarK { get; set; }

        #region SiteWeb配置
        [Newtonsoft.Json.JsonIgnore]
        public int MyStationId { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public int MyDeviceId { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public int MyEventId { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public int MyConditionId { get; set; }

        /// <summary>
        /// 实体中的SerialNo可能不唯一，因此增加MySerialNo来标识(MySerialNo=StationId.EquipmentId.EventId.EventConditionId.SerialNo)
        /// </summary>
        [Newtonsoft.Json.JsonIgnore]
        public string MySerialNo { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public EnumLevel MyAlarmLevel { get; set; }

        #endregion

        public TAlarm() { }

        /// <summary>
        /// 告警消息的结构(使用EnumState)
        /// </summary>
        /// <param name="serialNo">告警序号</param>
        /// <param name="deviceId">设备ID</param>
        /// <param name="id">监控点ID</param>
        /// <param name="nmAlarmId">网管告警编号(告警标准化编号)</param>
        /// <param name="alarmTime">告警时间</param>
        /// <param name="alarmLevel">告警级别</param>
        /// <param name="alarmFlag">告警标志</param>
        /// <param name="eventValue">告警触发值</param>
        /// <param name="alarmDesc">告警的事件描述</param>
        /// <param name="alarmRemark">预留字段1</param>
        public TAlarm(string serialNo, string deviceId, string id, string nmAlarmId, DateTime? alarmTime, EnumState alarmLevel, EnumFlag alarmFlag,
            float? eventValue, string alarmDesc, string signalNumber, string alarmRemark)
        {
            SerialNo = serialNo;
            DeviceID = deviceId;
            ID = id;
            NMAlarmID = nmAlarmId;
            AlarmTime = alarmTime;
            AlarmLevel = alarmLevel;
            AlarmFlag = alarmFlag;
            EventValue = eventValue;
            AlarmDesc = alarmDesc;
            SignalNumber = signalNumber;
            AlarmRemarK = alarmRemark;
            //告警等级转换
            if (AlarmLevel == EnumState.CRITICAL)
            {
                MyAlarmLevel = EnumLevel.CRITICAL;
            }
            else if (AlarmLevel == EnumState.MAJOR)
            {
                MyAlarmLevel = EnumLevel.MAJOR;
            }
            else if (AlarmLevel == EnumState.MINOR)
            {
                MyAlarmLevel = EnumLevel.MINOR;
            }
            else if (AlarmLevel == EnumState.HINT)
            {
                MyAlarmLevel = EnumLevel.HINT;
            }
            else
            {
                MyAlarmLevel = EnumLevel.INVALID;
            }
        }

        /// <summary>
        /// 告警消息的结构(使用EnumLevel)
        /// </summary>
        /// <param name="serialNo">告警序号</param>
        /// <param name="deviceId">设备ID</param>
        /// <param name="id">监控点ID</param>
        /// <param name="nmAlarmId">>网管告警编号</param>
        /// <param name="alarmTime">告警时间</param>
        /// <param name="alarmLevel">告警级别</param>
        /// <param name="alarmFlag">告警标志</param>
        /// <param name="eventValue">告警触发值</param>
        /// <param name="alarmDesc">告警的事件描述</param>
        /// <param name="alarmRemark">预留字段1</param>
        public TAlarm(string serialNo, string deviceId, string id, string nmAlarmId, DateTime? alarmTime, EnumLevel alarmLevel, EnumFlag alarmFlag,
            float? eventValue, string alarmDesc, string signalNumber, string alarmRemark)
        {
            SerialNo = serialNo;
            DeviceID = deviceId;
            ID = id;
            NMAlarmID = nmAlarmId;
            AlarmTime = alarmTime;
            MyAlarmLevel = alarmLevel;
            AlarmFlag = alarmFlag;
            EventValue = eventValue;
            AlarmDesc = alarmDesc;
            SignalNumber = signalNumber;
            AlarmRemarK = alarmRemark;
            //告警等级转换
            if (MyAlarmLevel == EnumLevel.CRITICAL)
            {
                AlarmLevel = EnumState.CRITICAL;
            }
            else if (MyAlarmLevel == EnumLevel.MAJOR)
            {
                AlarmLevel = EnumState.MAJOR;
            }
            else if (MyAlarmLevel == EnumLevel.MINOR)
            {
                AlarmLevel = EnumState.MINOR;
            }
            else if (MyAlarmLevel == EnumLevel.HINT)
            {
                AlarmLevel = EnumState.HINT;
            }
        }
       
        public override string ToString()
        {
            Newtonsoft.Json.JsonSerializerSettings settings = new Newtonsoft.Json.JsonSerializerSettings();
            settings.ContractResolver = new CustomContractResolver();
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, settings);
        }
    }
}