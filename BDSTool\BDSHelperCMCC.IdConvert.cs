﻿
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Odbc;
using System.Linq;
using System.Text;
using Carrier.BDSTool;
using Common.Logging.Pro;


using ENPC.Kolo.Entity.B_CMCC;
using BDSTool.BLL.B;
using BDSTool.Utility;
using BDSTool.Entity.B;
//using Carrier.BDSTool.Entity;
//using Carrier.BDSTool.BLL;


namespace Carrier.BDSTool
{
    public partial class BDSHelperCMCC
    {
        #region ID转换
        #region SiteWebID 转换成 B接口ID
        /// <summary>
        /// 6-ID转换: SiteWebID -> B接口ID(信号)
        /// </summary>
        /// <param name="stationId"></param>
        /// <param name="equipmentId"></param>
        /// <param name="signalId"></param>
        /// <param name="FSUID">输出:</param>
        /// <param name="DeviceID">输出:</param>
        /// <param name="ID">输出:</param>
        /// <param name="SignalNumber">输出:</param>
        /// <returns></returns>
        public static bool ConvertSignal2BID(int stationId, int equipmentId, int signalId,
                    ref string FSUID, ref string DeviceID, ref string ID, ref string SignalNumber) {
            return ConvertSecId2BID(stationId, equipmentId, signalId, ref  FSUID, ref  DeviceID, ref  ID, ref  SignalNumber);
        }


 
        /// <summary>
        /// 7-ID转换: SiteWebID -> B接口ID(事件)
        /// </summary>
        /// <param name="stationId"></param>
        /// <param name="equipmentId"></param>
        /// <param name="EventId"></param>
        /// <param name="EventConditionId"></param>
        /// <param name="FSUID">输出</param>
        /// <param name="DeviceID">输出</param>
        /// <param name="ID">输出</param>
        /// <param name="SignalNumber">输出:</param>
        /// <returns></returns>
        public static bool ConvertEvent2BID(int stationId, int equipmentId, int EventId, int EventConditionId,
            ref string FSUID, ref string DeviceID, ref string ID, ref string SignalNumber) {
                return ConvertSecId2BID(stationId, equipmentId, EventId, ref  FSUID, ref  DeviceID, ref  ID, ref  SignalNumber);
        }


        /// <summary>
        /// 8-ID转换: SiteWebID -> B接口ID(控制)
        /// </summary>
        /// <param name="stationId"></param>
        /// <param name="equipmentId"></param>
        /// <param name="controlId"></param>
        /// <param name="FSUID">输出</param>
        /// <param name="DeviceID">输出</param>
        /// <param name="ID">输出</param>
        /// <param name="SignalNumber">输出:</param>
        /// <returns></returns>
        public static bool CovnertControl2BID(int stationId, int equipmentId, int controlId,
                    ref string FSUID, ref string DeviceID, ref string ID, ref string SignalNumber) {
                        return ConvertSecId2BID(stationId, equipmentId, controlId, ref  FSUID, ref  DeviceID, ref  ID, ref  SignalNumber);
        }


        #endregion
        #region B接口ID转换成SiteWebID
        /// <summary>
        /// 9-B接口ID转换成SiteWebID(信号)
        /// </summary>
        /// <param name="FSUID"></param>
        /// <param name="DeviceID"></param>
        /// <param name="ID">信号</param>
        /// <param name="SignalNumber"></param>
        /// <param name="StationId">输出</param>
        /// <param name="EquipmentId">输出</param>
        /// <param name="SignalId">输出</param>
        /// <returns></returns>
        public static bool ConvertBID2Signal(string FSUID, string DeviceID, string ID, string SignalNumber
            , ref int StationId, ref  int EquipmentId, ref  int SignalId) {
                return ConvertBID2SecId(FSUID, DeviceID, ID, SignalNumber, ref  StationId, ref  EquipmentId, ref  SignalId);
        }


        /// <summary>
        ///  10-B接口ID转换成SiteWebID(事件)
        /// </summary>
        /// <param name="FSUID"></param>
        /// <param name="DeviceID"></param>
        /// <param name="ID"></param>
        /// <param name="SignalNumber"></param>
        /// <param name="StationId">输出</param>
        /// <param name="EquipmentId">输出</param>
        /// <param name="EventId">输出</param>
        /// <param name="EventConditionId">输出</param>
        /// <returns></returns>
        public static bool ConvertBID2Event(string FSUID, string DeviceID, string ID, string SignalNumber
            , ref int StationId, ref int EquipmentId, ref int EventId, ref int EventConditionId) {
                EventConditionId = 0;
                return ConvertBID2SecId(FSUID, DeviceID, ID, SignalNumber, ref  StationId, ref  EquipmentId, ref   EventId);
        }

        /// <summary>
        /// 11-B接口ID转换成SiteWebID(控制)
        /// </summary>
        /// <param name="FSUID"></param>
        /// <param name="DeviceID"></param>
        /// <param name="ID"></param>
        /// <param name="SignalNumber"></param>
        /// <param name="StationId">输出</param>
        /// <param name="EquipmentId">输出</param>
        /// <param name="ControlId">输出</param>
        /// <returns></returns>
        public static bool CovnertBID2Control(string FSUID, string DeviceID, string ID, string SignalNumber
            , ref int StationId, ref int EquipmentId, ref  int ControlId) {
            return ConvertBID2SecId(FSUID, DeviceID, ID, SignalNumber,ref  StationId, ref  EquipmentId, ref   ControlId);
        }
        #endregion
        #endregion

        #region private
        private static bool ConvertSecId2BID(int stationId, int equipmentId, int secId,
         ref string FSUID, ref string DeviceID, ref string ID, ref string SignalNumber) {

            try {
                if (!BDSHelperCMCC.GetFSUDeviceIDByEquipmentId(stationId, equipmentId, ref  FSUID, ref  DeviceID)) {
                    logger.InfoFormat("ConvertSecId2BID failed; 找不到DeviceID; equipmentId={0}", equipmentId);
                    return false;
                }

                if (!TSignalEx.ConvertSiteWebId2BID(secId, ref ID, ref SignalNumber)) {
                    logger.InfoFormat("ConvertSecId2BID failed; 分解SiteWebId失败; secId={0}", secId);
                    return false;
                }
                    
            }
            catch (Exception ex) {
                logger.ErrorFormat("ConvertSecId2BID();stationId={0};equipmentId={1};secId={2}; Error={3}"
                , stationId, equipmentId, secId, ex.Message);
                logger.ErrorFormat("ConvertSecId2BID();Stack={0}", ex.StackTrace);
                return false;
            }
            return true;
        }

        private static bool ConvertBID2SecId(string FSUID, string DeviceID, string ID, string SignalNumber
            , ref int StationId, ref int EquipmentId, ref  int secId) {
            try {
                if (!BDSHelperCMCC.GetEquipmentIdByFSUDeviceID(FSUID, DeviceID, ref StationId, ref  EquipmentId)) {
                    logger.InfoFormat("ConvertBID2SecId failed; 找不到EquipId; FSUID.DeviceID={0}.{1} ", FSUID, DeviceID);
                    return false;
                }

                if (!TSignalEx.ConvertBID2SiteWebId(ID, SignalNumber, ref secId)) {
                    logger.InfoFormat("ConvertBID2SecId failed; 转换SiteWebId失败; ID={0},SignalNumber={1}", ID, SignalNumber);
                    return false;
                }
                    

            }
            catch (Exception ex) {
                logger.ErrorFormat("ConvertBID2SecId();FSU.DeviceID.ID={0}.{1}.{2};Error={3}", FSUID, DeviceID, ID, ex.Message);
                logger.ErrorFormat("ConvertBID2SecId();Stack={0}", ex.StackTrace);
                return false;
            }

            return true;
        }
        #endregion
    }
}
