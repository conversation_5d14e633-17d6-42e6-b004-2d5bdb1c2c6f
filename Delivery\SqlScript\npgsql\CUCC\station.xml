﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <procedures>
    <procedure owner="" name="StationInfo_userId" grant="">
      <parameters>
        <parameter name="LogonId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        SELECT a.UserId iUserId FROM TBL_Account a WHERE a.LogonId = @LogonId;
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="StationInfo_userRole" grant="">
      <parameters>
        <parameter name="iUserId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        SELECT 'X' FROM TBL_UserRoleMap r WHERE r.UserId = CAST(@iUserId AS INTEGER) AND r.RoleId = -1;
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="StationInfo_structureInfo" grant="">
      <body>
        <![CDATA[ 
        SELECT CAST(a.StructureId AS TEXT) sCenterId,a.StructureName CenterName FROM TBL_StationStructure a 
        WHERE a.StructureType = 2 AND a.ParentStructureId = 0;
      ]]>
      </body>
    </procedure>
   
  </procedures>
</root>
