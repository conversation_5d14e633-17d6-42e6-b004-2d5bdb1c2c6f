﻿


using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Delivery.DAL;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;

namespace DM.TestOrder.DAL
{

    public class BCodeDal
    {
        //   移动B，编码为13位数字
        //  
        static public string GetSiteCode(int stationId)
        {
            //var sql = string.Format("WO_BGetSiteCode {0}", stationId);
            //var rtn = DBHelper.ExecuteScalar(sql);
            //return rtn;
            if (CommonUtils.IsNoProcedure)
            {
                return WOBGetSiteCodeService.Instance.GetData(stationId);
            }
            else
            {
                var obj = new ExecuteSql().ExecuteStoredProcedureScalar("WO_BGetSiteCode", new QueryParameter[] {
                    new QueryParameter("StationId", DataType.Int, stationId.ToString()),
                });
                return obj.ToString();
            }
        }
    }
}
