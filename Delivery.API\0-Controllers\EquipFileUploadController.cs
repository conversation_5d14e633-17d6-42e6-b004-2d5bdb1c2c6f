﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;


using Newtonsoft.Json.Linq;
using System.IO;

using System.Diagnostics;
using BLL;

using System.Text.RegularExpressions;
using DM.TestOrder.Entity;
using DM.TestOrder.DAL;
using log4net;
using Delivery.API;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Hosting;

namespace DM.TestOrder.Controllers {
    public class EquipFileUploadController : BaseController
    {
        private static readonly ILog logger = LogManager.GetLogger("wo");
        private static int MAX_FILE_SIZE = 5 * 1024 * 1024;


        private readonly IHostingEnvironment _appEnvironment;

        public EquipFileUploadController(IHostingEnvironment appEnvironment)
        {
            _appEnvironment = appEnvironment;
        }

        [HttpPost]
        public async Task<IActionResult> Post(IFormFile upfile)
        {
            if (upfile == null)
            {
                BadRequest("cannot get file");
            }
            if (upfile.Length > MAX_FILE_SIZE + 1024)
            {
                logger.WarnFormat("EquipFileUploadController.Post();Request bigger than 5M; size ={0:N1}M", upfile.Length * 1.0 / (1024 * 1024));
                return new JsonResult(new
                {
                    errormsg = "上传文件最大不得超过5M"
                });
            }


            var orderId = int.Parse(HttpContext.Request.Form["orderid"]);
            var equipmentId = int.Parse(HttpContext.Request.Form["equipmentId"]);
            var filetype = HttpContext.Request.Form["filetype"];//PIC or DOC          
            var userid = HttpContext.Request.Form["userid"]; //父目录

            //目录结构设计: [userid]/[orderid]/
            //文件名设计  :  类型+设备ID,示例: PIC-755000015.ZIP	
            var myPath = "upload/" + userid.ToString() + "/" + orderId;

            WO_FileUploadRec woFileUploadRec = null;

            string uploadFolderPath = Path.Combine(_appEnvironment.WebRootPath, myPath);
            if (!Directory.Exists(uploadFolderPath))
                Directory.CreateDirectory(uploadFolderPath);
            var toSaveFileName = string.Format("{0}-{1}", filetype, equipmentId) + Path.GetExtension(upfile.FileName);

            using (var stream = System.IO.File.Create(Path.Combine(uploadFolderPath, toSaveFileName)))
            {
                await upfile.CopyToAsync(stream);
            }

         
            //接收文件  
            var fileSaveName = Path.GetFileName(Path.Combine(uploadFolderPath, toSaveFileName));
            woFileUploadRec = new WO_FileUploadRec()
            {
                OrderId = orderId,
                EquipmentId = equipmentId,
                FType = filetype,
                FSaveName = fileSaveName.ToLower(),
                Uri = myPath + "/" + fileSaveName.ToLower(),
                FOriginalName = upfile.FileName,
                FSize = (int)upfile.Length,
                UploadTime = DateTime.Now,
                UserId = int.Parse(userid)
            };
            WoFileUploadRecDal.AddOrUpdateOne(woFileUploadRec);



            return new JsonResult(new
            {
                uri = woFileUploadRec.Uri,
                errormsg = "OK"
            });

            
        }
        //public static string GetCommonString( string input) {
        //    var regex = new Regex(@"([^\u4e00-\u9fa5a-zA-z0-9\s].*?)");
        //    var inputReplaced = regex.Replace(input, "_");
        //    return inputReplaced;
        //}

        //[AcceptVerbs("POST")]
        //[ActionName("postNewFile")]
        //public string PostFilePic(HttpPostedFileBase fileData)
        //{
        //    //HttpPostedFile file = HttpContext.Current.Request.Files[0];
        //    //string strPath = "D:\\MyProjects\\StudySolution\\RestDemo\\Upload\\test2.rar";
        //    //file.SaveAs(strPath);
        //    string result = "0";
        //    return result;

        //}

        //[AcceptVerbs("Get")]
        //[ActionName("getNewFile")]
        //public string GetFilePic(HttpPostedFileBase fileData) {
        //    HttpPostedFile file = HttpContext.Current.Request.Files[0];
        //    string strPath = "D:\\MyProjects\\StudySolution\\RestDemo\\Upload\\test2.rar";
        //    file.SaveAs(strPath);
        //    string result = "0";
        //    return result;

        //}

    }

}
