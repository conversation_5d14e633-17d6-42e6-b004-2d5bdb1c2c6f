﻿using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace Delivery.Common.NoProcedure.Service.CUCC
{
    public class StationService
    {
        private static StationService _instance = null;

        public static StationService Instance
        {
            get
            {
                if (_instance == null) _instance = new StationService();
                return _instance;
            }
        }

        public DataTable GetStationInfo(string structureId, string StationName, string StationCode, string strBegin, string strEnd, string stationType, string stationStatus, string LogonId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("LogonId", LogonId);
                    DataTable dt = execHelper.ExecDataTable("StationInfo_userId", realParams);
                    int iUserId = Convert.ToInt32(dt.Rows[0]["iUserId"]);
                    realParams.Add("iUserId", iUserId);
                    DataTable dt1 = execHelper.ExecDataTable("StationInfo_userRole", realParams);
                    if(dt1 != null && dt1.Rows.Count > 0)
                    {
                        realParams.Add("RoleMapNotNull", true);
                    }
                    else
                    {
                        realParams.Add("RoleMapNull", true);
                    }
                    string sCenterId = "";
                    string CenterName = "";
                    DataTable dt2 = execHelper.ExecDataTable("StationInfo_structureInfo", realParams);
                    if(dt2 != null && dt2.Rows.Count > 0)
                    {
                        sCenterId = dt2.Rows[0]["sCenterId"].ToString();
                        CenterName = dt2.Rows[0]["CenterName"].ToString();
                    }
                    realParams.Add("sCenterId", sCenterId);
                    realParams.Add("CenterName", CenterName);

                    string endstring = DateTime.Now.AddMonths(1).ToString("yyyy-MM-dd");
                    strBegin = string.IsNullOrEmpty(strBegin) ? "2018-03-02" : strBegin;
                    strEnd = string.IsNullOrEmpty(strEnd) ? endstring : strEnd;
                    DateTime startTime = Convert.ToDateTime(strBegin);
                    DateTime endTime = Convert.ToDateTime(strEnd);
                    //ApplyTime
                    realParams.Add("WhereApplyTime", true);
                    realParams.Add("StartTime", startTime);
                    realParams.Add("EndTime", endTime);
                    if (structureId != null && structureId != "-1")
                    {
                        realParams.Add("WhereStructureId", true);
                        realParams.Add("StructureId", "%" + structureId + "%");
                    }
                    if(stationType != null && stationType != "-1" )
                    {
                        realParams.Add("WhereStationCategory", true);
                        realParams.Add("StationCategory", stationType);
                    }
                    if(StationName!= null && StationName.Trim() != "")
                    {
                        realParams.Add("WhereStationName", true);
                        realParams.Add("WhereSWStationName", true);
                        realParams.Add("StationName", "%" + StationName.Trim() + "%");
                    }
                    if(StationCode != null && StationCode.Trim() != "" )
                    {
                        realParams.Add("WhereStationCode", true);
                        realParams.Add("StationCode", "%" + StationCode.Trim() + "%");
                    }
                    if(stationStatus != null && stationStatus != "-1")
                    {
                        realParams.Add("WhereStatus", true);
                        realParams.Add("StationStatus", stationStatus);
                    }
                    realParams.Add("SqlStr1", true);
                    realParams.Add("SqlStr2", true);

                    //SELECT '$[sCenterId]' CenterId, '$[CenterName]' CenterName
                    DataTable resultDt1 = execHelper.ExecDataTable("StationInfo_GetData1", realParams);
                    DataTable resultDt2 = execHelper.ExecDataTable("StationInfo_GetData2", realParams);
                    resultDt1.Merge(resultDt2);
                    resultDt1.DefaultView.Sort = "ApplyTime DESC";
                    resultDt1 = resultDt1.DefaultView.ToTable();
                    return resultDt1;
                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    return new DataTable();
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
    }
}
