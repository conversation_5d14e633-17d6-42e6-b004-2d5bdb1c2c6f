﻿using System;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class TRealTimeData : BStruct
    {
        public string SID { get; set; }

        public string Value { get; set; }

        public EnumState Status { get; set; }

        [Newtonsoft.Json.JsonConverter(typeof(CustomDateTimeConverter))]
        public DateTime? Time { get; set; }

        /// <summary>
        /// 数据描述（对应于性能数据CSV文件中的数据描述字段）
        /// </summary>
        public string Description { get; set; }

        public int MySignalId { get; set; }

        public TRealTimeData(EnumType enumType, string sId, string value, EnumState enumState, DateTime? time)
        {
            Type = enumType;
            SID = sId;
            Value = value;
            Status = enumState;
            Time = time;
        }

        public TRealTimeData(EnumType enumType, string sId, string value, EnumState enumState, DateTime? time,string description)
        {
            Type = enumType;
            SID = sId;
            Value = value;
            Status = enumState;
            Time = time;
            Description = description;
        }

        public override string ToString()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
    }
}