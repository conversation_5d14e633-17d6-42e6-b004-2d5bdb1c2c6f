﻿

using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
//
//using Sybase.Data.AseClient;
//using ENPC.Kolo.DataServer;

namespace DM.TestOrder.Common.DBA
{

    public partial class DatabaseHelper : IDisposable
    {

        /// <summary>
        /// 构建存储过程命令
        /// </summary>
        public void CreateProcCommand(string sql) {
            CreateDatabaseConnection();

            CreateDatabaseCommand();

            Command.CommandText = sql;
            Command.CommandType = CommandType.StoredProcedure;
        }

        public void ExecuteStoredProcedureNoQuery(QueryParameter[] queryParameters) {
            try {                                
                for (var i = 0; i < queryParameters.Length; i++) {
                    DbType dbtype = DbType.Object;
                    Object paramValue = null;
                    switch (queryParameters[i].DataType) {
                        case DataType.Int:
                            dbtype = DbType.Int32;
                            int tempValue;
                            if (Int32.TryParse(queryParameters[i].Value, out tempValue)) {
                                paramValue = tempValue;
                            }
                            else {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.Number:
                            dbtype = DbType.Single;
                            Single tempSingleValue;
                            if (Single.TryParse(queryParameters[i].Value, out tempSingleValue)) {
                                paramValue = tempSingleValue;
                            }
                            else {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.String:
                            dbtype = DbType.AnsiString;
                            paramValue = queryParameters[i].Value;
                            break;
                        case DataType.DateTime:
                            dbtype = DbType.DateTime;
                            if (queryParameters[i].Value != null) {
                                paramValue = queryParameters[i].Value;
                            }
                            else {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.Bool:
                            dbtype = DbType.Boolean;
                            paramValue = Boolean.Parse(queryParameters[i].Value);
                            break;
                    }

                    DbParameter param = GetDbParameter(queryParameters[i].Name, dbtype, paramValue);

                    Command.Parameters.Add(param);
                }

                ExecuteNoQuery();
            }
            catch (Exception ex) {
                RecordErrorLog(ex);
                throw ex;
            }
        }

        public string ExecuteStoredProcedureForScalar(QueryParameter[] queryParameters) {
            try {
                for (var i = 0; i < queryParameters.Length; i++) {
                    DbType dbtype = DbType.Object;
                    Object paramValue = null;
                    switch (queryParameters[i].DataType) {
                        case DataType.Int:
                            dbtype = DbType.Int32;
                            int tempValue;
                            if (Int32.TryParse(queryParameters[i].Value, out tempValue)) {
                                paramValue = tempValue;
                            }
                            else {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.Number:
                            dbtype = DbType.Single;
                            Single tempSingleValue;
                            if (Single.TryParse(queryParameters[i].Value, out tempSingleValue)) {
                                paramValue = tempSingleValue;
                            }
                            else {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.String:
                            dbtype = DbType.AnsiString;
                            paramValue = queryParameters[i].Value;
                            break;
                        case DataType.DateTime:
                            dbtype = DbType.DateTime;
                            if (queryParameters[i].Value != null) {
                                paramValue = queryParameters[i].Value;
                            }
                            else {
                                paramValue = DBNull.Value;
                            }
                            break;
                        case DataType.Bool:
                            dbtype = DbType.Boolean;
                            paramValue = Boolean.Parse(queryParameters[i].Value);
                            break;
                    }

                    DbParameter param = GetDbParameter(queryParameters[i].Name, dbtype, paramValue);

                    Command.Parameters.Add(param);
                }

                var rtn =  ExecuteScalar();
                if (rtn != null)
                    return rtn.ToString();
                else
                    return null;
            }
            catch (Exception ex) {
                RecordErrorLog(ex);
                throw ex;
            }
        }

    

    }
}
