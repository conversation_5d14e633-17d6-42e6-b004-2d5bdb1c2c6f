﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.6.5.请求监控点门限数据应答
    /// </summary>
    public sealed class GetThresholdAck:BMessage
    {
        public EnumResult Result { get; private set; }

        public string FailureCause { get; private set; }

        public List<DeviceThreshold> Values { get; private set; }

        public GetThresholdAck(string fsuId, EnumResult result, string failureCause, List<DeviceThreshold> values)
            : base()
        {
            MessageType = (int)BMessageType.GET_THRESHOLD_ACK;
            FSUID = fsuId;
            Result = result;
            FailureCause = failureCause;
            Values = values;
        }

        public GetThresholdAck() : base() 
        {
            MessageType = (int)BMessageType.GET_THRESHOLD_ACK;
        }

        public static GetThresholdAck Deserialize(XmlDocument xmlDoc)
        {
            GetThresholdAck getThresholdAck = null;
            string errorMsg = string.Empty;
            try
            {
                string fsuId = xmlDoc.SelectSingleNode("/Response/Info/FSUID").InnerText;
                string strResult = xmlDoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim();
                string failureCause = xmlDoc.SelectSingleNode("/Response/Info/FailureCause").InnerText;
                EnumResult result;
                if (string.IsNullOrEmpty(strResult))
                {
                    result = EnumResult.FAILURE;
                }
                else
                {
                    result = (EnumResult)int.Parse(strResult);
                }
                XmlNodeList nodelist = xmlDoc.SelectNodes("/Response/Info/Values/DeviceList/Device");
                List<DeviceThreshold> values = new List<DeviceThreshold>();
                foreach (XmlNode device in nodelist)
                {
                    string deviceId = device.Attributes["ID"].Value.Trim();
                    List<TThreshold> thresholdList = new List<TThreshold>();
                    foreach (XmlNode tThreshold in device.ChildNodes)
                    {
                        string strType = tThreshold.Attributes["Type"].Value.Trim();
                        string strId = tThreshold.Attributes["ID"].Value.Trim();
                        if (!CheckNumberValue(strId))
                        {
                            errorMsg = "TThreshold.ID is invalid";
                            break;
                        }
                        string strSignalNumber = tThreshold.Attributes["SignalNumber"].Value.Trim();
                        if (!string.IsNullOrEmpty(strSignalNumber) && !CheckNumberValue(strSignalNumber))
                        {
                            errorMsg = "TThreshold.SignalNumber is invalid";
                            break;
                        }
                        string strThreshold = tThreshold.Attributes["Threshold"].Value.Trim();
                        //string strAbsoluteVal = tThreshold.Attributes["AbsoluteVal"].Value.Trim();
                        //string strRelativeVal = tThreshold.Attributes["RelativeVal"].Value.Trim();
                        string strAlarmLevel = tThreshold.Attributes["AlarmLevel"].Value.Trim();
                        string nmAlarmId = tThreshold.Attributes["NMAlarmID"].Value.Trim();
                        EnumType enumType = EnumType.INVALID;
                        if (!string.IsNullOrEmpty(strType))
                        {
                            enumType = (EnumType)int.Parse(strType);
                        }
                        float? fThreshold = null;
                        if (!string.IsNullOrEmpty(strThreshold))
                        {
                            fThreshold = float.Parse(strThreshold);
                        }
                        //float? fAbsoluteVal = null;
                        //if (!string.IsNullOrEmpty(strAbsoluteVal))
                        //{
                        //    fAbsoluteVal = float.Parse(strAbsoluteVal);
                        //}
                        //float? fRelativeVal = null;
                        //if (!string.IsNullOrEmpty(strRelativeVal))
                        //{
                        //    fRelativeVal = float.Parse(strRelativeVal);
                        //}
                        EnumState alarmLevel;
                        if (!string.IsNullOrEmpty(strAlarmLevel) && strAlarmLevel.ToUpper() != "NULL")
                        {
                            alarmLevel = (EnumState)int.Parse(strAlarmLevel);
                        }
                        else
                        {
                            errorMsg = "TThreshold.AlarmLevel is invalid";
                            break;
                        }
                        TThreshold threshold = new TThreshold(enumType, strId, strSignalNumber, fThreshold, null, null, alarmLevel, nmAlarmId);
                        thresholdList.Add(threshold);
                    }
                    if(errorMsg != string.Empty)
                    {
                        break;
                    }
                    DeviceThreshold deviceThreshold = new DeviceThreshold(deviceId, thresholdList);
                    values.Add(deviceThreshold);
                }
                if (errorMsg != string.Empty)
                {
                    getThresholdAck = new GetThresholdAck();
                    getThresholdAck.ErrorMsg = errorMsg;
                    getThresholdAck.StringXML = xmlDoc.InnerXml;
                    entityLogger.ErrorFormat("GetThresholdAck.Deserialize():{0}", errorMsg);
                }
                else
                {
                    getThresholdAck = new GetThresholdAck(fsuId, result, failureCause, values);
                    getThresholdAck.StringXML = xmlDoc.InnerXml;
                }
                entityLogger.DebugFormat("GetThresholdAck.Deserialize(),xml:\r\n{0}:", FormatXml(xmlDoc.InnerXml));
                return getThresholdAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetThresholdAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetThresholdAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                getThresholdAck = new GetThresholdAck();
                getThresholdAck.ErrorMsg = ex.Message;
                getThresholdAck.StringXML = FormatXml(xmlDoc.InnerXml);
                return getThresholdAck;
            }  
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}:{2},{3}, {4}", MessageId, MessageType, FSUID, Result, FailureCause);
            sb.AppendLine();
            if (Values != null)
            {
                foreach (DeviceThreshold device in Values)
                {
                    sb.AppendFormat("{0}\r\n", device.ToString());
                }
            }
            return sb.ToString();
        }

    }

    /// <summary>
    /// 设备监控点门限的数据结构
    /// </summary>
    public class DeviceThreshold
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceID { get; set; }

        /// <summary>
        /// 监控点ID号列表
        /// </summary>
        public List<TThreshold> Thresholds { get; set; }

        public DeviceThreshold(string deviceId, List<TThreshold> thresholds)
        {
            DeviceID = deviceId;
            Thresholds = thresholds;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}:", DeviceID);
            foreach (TThreshold threshold in Thresholds)
            {
                sb.Append("[").Append(threshold.ToString()).Append("]");
            }
            return sb.ToString();
        }

    }

}
