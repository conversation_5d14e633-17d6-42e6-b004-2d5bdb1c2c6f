﻿
using Common.Logging.Pro;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.Entity.B
{
    public class TDevConfHelper
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static bool IsPropertyChanged(TDevConf dev1, TDevConf dev2) {                 
            if(dev1.DeviceID  != dev2.DeviceID) 
                return true;
            if (dev1.DeviceName != dev2.DeviceName)
                return true;
            if (dev1.RoomName != dev2.RoomName)
                return true;
            if (dev1.DeviceType != dev2.DeviceType)
                return true;
            if (dev1.DeviceSubType != dev2.DeviceSubType)
                return true;
            if (dev1.Model != dev2.Model)
                return true;
            if (dev1.Brand != dev2.Brand)
                return true;
            if (dev1.RatedCapacity != dev2.RatedCapacity)
                return true;
            if (dev1.BeginRunTime != dev2.BeginRunTime)
                return true;
            if (dev1.DevDescribe != dev2.DevDescribe)
                return true;
            return false;
        }


    }
}
