﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using System.Security.Cryptography;

namespace Delivery.Common
{    
    public class FileMD5Helper
        {
            /// <summary>
            /// 对文件流进行MD5加密
            /// </summary>
            /// <param name="filePath"></param>
            /// <returns></returns>
            public static string MD5Stream(string filePath)
            {
                StringBuilder sb = new StringBuilder(32);
                if (File.Exists(filePath))
                {
                    //FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read);
                    FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);

                    MD5CryptoServiceProvider md5 = new MD5CryptoServiceProvider();
                    md5.ComputeHash(fs);
                    fs.Close();

                    byte[] b = md5.Hash;
                    md5.Clear();

                    for (int i = 0; i < b.Length; i++)
                    {
                        sb.Append(b[i].ToString("X2"));
                    }
                }
                return sb.ToString();
            }

            /// <summary>
            /// 对文件进行MD5加密
            /// </summary>
            /// <param name="filePath"></param>
            /// <returns></returns>
            public static string MD5File(string filePath)
            {
                StringBuilder sb = new StringBuilder(32);
                if (File.Exists(filePath))
                {
                    FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read);
                    int bufferSize = 1048576; // 缓冲区大小，1MB
                    byte[] buff = new byte[bufferSize];

                    MD5CryptoServiceProvider md5 = new MD5CryptoServiceProvider();
                    md5.Initialize();

                    long offset = 0;
                    while (offset < fs.Length)
                    {
                        long readSize = bufferSize;
                        if (offset + readSize > fs.Length)
                        {
                            readSize = fs.Length - offset;
                        }

                        fs.Read(buff, 0, Convert.ToInt32(readSize)); // 读取一段数据到缓冲区

                        if (offset + readSize < fs.Length) // 不是最后一块
                        {
                            md5.TransformBlock(buff, 0, Convert.ToInt32(readSize), buff, 0);
                        }
                        else // 最后一块
                        {
                            md5.TransformFinalBlock(buff, 0, Convert.ToInt32(readSize));
                        }

                        offset += bufferSize;
                    }
                    //对于空文件，也需要计算MD5码
                    if (offset == 0)
                    {
                        md5.TransformFinalBlock(buff, 0, 0);
                    }

                    fs.Close();
                    byte[] result = md5.Hash;
                    md5.Clear();

                    for (int i = 0; i < result.Length; i++)
                    {
                        sb.Append(result[i].ToString("X2"));
                    }
                }

                return sb.ToString();
            }

            /// <summary>
            ///  对字符串进行MD5加密
            /// </summary>
            /// <param name="str">原始字符串</param>
            /// <returns></returns>
            public static string MD5String(string str)
            {
                MD5CryptoServiceProvider md5 = new MD5CryptoServiceProvider();
                byte[] srcArray = Encoding.UTF8.GetBytes(str);
                byte[] dstArray = Encoding.Convert(Encoding.UTF8, Encoding.ASCII, srcArray, 0, srcArray.Length);
                byte[] result = md5.ComputeHash(dstArray);
                md5.Clear();
                StringBuilder sb = new StringBuilder();
                foreach (byte b in result)
                {
                    //sb.AppendFormat("{0:x2}", b);
                    sb.Append(b.ToString("X2"));
                }
                return sb.ToString();
            }
        }
   
}
