﻿@page
@model IndexModel
@{
    ViewData["Title"] = "登陆";
    var error = ViewData["error"];
}

<!-- <script type="text/javascript" src="~/Scripts/Sha1Security.js"></script> -->
<script type="text/javascript" src="~/Scripts/lib/jquery-1.8.0.min.js"></script>
<script type="text/javascript" src="~/Scripts/Sha256Security.js"></script>
<style>
    #bom {
        position: fixed;
        left: 0;
        bottom: 0;
    }

    p {
        width: 60px;
        height: 20px;
        display: inline-block;
        letter-spacing: 3px;
        border: 1px solid red
    }

    #code {
        height: 20px;
        margin: 0;
    }

    p:hover {
        cursor: pointer
    }
</style>
<script type="text/javascript">
    document.addEventListener("DOMContentLoaded", () => {
        document.addEventListener("keydown", handleEnterKey);
    });
    function handleEnterKey(event) {
        if (event.key === "Enter") {
            event.preventDefault(); // 阻止默认的表单提交行为
            encrypt2();
        }
    }
    function encrypt2(){
		const txt_password = $('#txt_password');
        const txt_userid = $('#txt_userid');
        const txt_code = $('#txt_code').val();
        const err_p = $('#err_p');
        err_p.text('');
        if (txt_password.val() === '' || txt_userid.val() === '') {
            err_p.text("用户名或密码不能为空！");
            return;
        }
        const hidden_password = sha256(txt_password.val() + '[' + txt_userid.val() + ']');
		if (txt_code === '') {
            err_p.text("验证码不能为空！");
        } else if (txt_code.toUpperCase() === $('#code').text().toUpperCase()) {
            txt_password.val(hidden_password);
            $('#hid_password').val(hidden_password);
            $('#button_login').click();
        } else {
            err_p.text("验证码错误！");
            GenerateCode();
        }
    }

</script>
<table style="border: 0; padding: 1pt; margin: 1pt; width: 100%;">
    <tr>
        <td style="width: 200px;">
            <img id="Image1" src="~/images/logo.gif" alt=""/>
        </td>
        <td>
            <label id="Label1" style="font-size:12pt;">接入信息系统</label>
        </td>
        <td style="width: 200px;">
            <img id="Image2" src="~/images/img_head_lg.gif" alt=""/>
        </td>
    </tr>
</table>

<div style="text-align: right; background-color: #003366; color: #FFFFFF;">
    ·VERTIV
</div>
<div style="height: 15pt;"></div>
<div style="height: 50px; vertical-align: bottom; padding-left: 200px; text-align: right; padding-right: 10pt;">
</div>
<div style="height: 50pt;"></div>

<form id="form1" method="post" style="font-size: 9pt; margin: 0; padding: 0;">
    <div style="width: 30%; text-align: center; margin: 0 auto;">
        <table style="border: 0; padding: 0; margin: auto;">
            <tr>
                <td style="width: 50px">
                    <label style="color: #333399;">用户名</label>
                </td>
                <td style="width: 80px; background-color: #F5F5F5;">
                    <input type="text" id="txt_userid" name="txt_userid" style="width: 150px;"/>
                </td>
            </tr>
            <tr>
                <td style="width: 50px;">
                    <label style="color: #333399;">密码</label>
                </td>
                <td style="width: 80px; background-color: #F5F5F5;">
                    <input id="txt_password" name="txt_password" type="password" style="width: 150px;"/>
                    <input name="hid_password" type="hidden" id="hid_password"/>
                </td>
            </tr>
            <tr>
                <td style="width: 50px;">
                    <label style="color: #333399;">验证码</label>
                </td>
                <td style="width: 80px; background-color: #F5F5F5;">
                    <input id="txt_code" name="txt_code" type="text" style="width: 80px;"/>
                    <p id="code"></p>
                </td>
            </tr>
            <tr style="display:none">
                <td style="width: 130px;" colspan="2">
                    <button type="submit" id="button_login">登录</button>
                </td>
            </tr>

            <tr>
                <td style="width: 130px;" colspan="2">
                    <label style="color: #333399;">@error</label>
                </td>
            </tr>
        </table>
    </div>
</form>
<div id="bom"
     style="text-align: center; vertical-align: middle; height: 50pt; width: 100%; color: #003366;">
    © 2018 Vertiv Tech Co., Ltd.All rights reserved.
</div>
<div style="width: 30%; text-align: center; margin: 0 auto;">
    <button id="login1" onclick="encrypt2()">登录</button>
    <span id="err_p" style="display: block; color: #333399;"></span>
</div>


<script type="text/javascript">
    const span = document.getElementById('code');
    function random(max, min) {
        return Math.round(Math.random() * (max - min) + min)
    }
    function GenerateCode() {
        const str = "1234567890qwertyuiopasdfghjklzxcvbnmQWERTYUIOPASDFGHJKLZXCVBNM";
        let res = '';
        for (let i = 0; i < 4; i++) {
            res += str[random(0, 62)];
        }
        span.innerHTML = res
    }
    GenerateCode();
    span.onclick = GenerateCode;
</script>