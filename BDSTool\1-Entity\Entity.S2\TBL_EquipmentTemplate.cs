
namespace BDSTool.Entity.S2
{
    using System;
    using System.Collections.Generic;
    
    public partial class TBL_EquipmentTemplate
    {
        public int EquipmentTemplateId { get; set; }
        public string EquipmentTemplateName { get; set; }
        public int ParentTemplateId { get; set; }
        public string Memo { get; set; }
        public string ProtocolCode { get; set; }
        public int EquipmentCategory { get; set; }
        public int EquipmentType { get; set; }
        public string Property { get; set; }
        public string Description { get; set; }
        public string EquipmentStyle { get; set; }
        public string Unit { get; set; }
        public string Vendor { get; set; }
        public Nullable<int> EquipmentBaseType { get; set; }
        public Nullable<int> StationCategory { get; set; }
    }
}
