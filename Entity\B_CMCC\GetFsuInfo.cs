﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.6.12.	获取FSU状态信息
    /// </summary>
    public sealed class GetFsuInfo: BMessage
    {

        public GetFsuInfo(string fusId):base()
        {
            MessageType = (int)BMessageType.GET_FSUINFO;

            FSUID = fusId;
        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmldoc = CreateXmlDocument();
                AppendPK_Type(xmldoc, "Request", BMessageType.GET_FSUINFO.ToString());

                XmlElement xe2 = xmldoc.CreateElement("Info");
                XmlElement xe21 = xmldoc.CreateElement("FSUID");
                xe21.InnerText = FSUID;
                xe2.AppendChild(xe21);

                XmlNode root = xmldoc.SelectSingleNode("Request");
                root.AppendChild(xe2);

                StringXML = xmldoc.InnerXml;
                entityLogger.DebugFormat("GetFsuInfo.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmldoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetFsuInfo.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetFsuInfo.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}: {2}", MessageId, (BMessageType)MessageType, FSUID);
            return sb.ToString();
        }

    }
}
