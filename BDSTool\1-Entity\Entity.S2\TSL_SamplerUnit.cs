namespace BDSTool.Entity.S2
{
    using System;
    using System.Collections.Generic;
    
    public partial class TSL_SamplerUnit
    {
        public int SamplerUnitId { get; set; }
        public int PortId { get; set; }
        public int MonitorUnitId { get; set; }
        public int SamplerId { get; set; }
        public int ParentSamplerUnitId { get; set; }
        public int SamplerType { get; set; }
        public string SamplerUnitName { get; set; }
        public int Address { get; set; }
        public Nullable<double> SpUnitInterval { get; set; }
        public string DllPath { get; set; }
        public int ConnectState { get; set; }
        public System.DateTime UpdateTime { get; set; }
        public string PhoneNumber { get; set; }
        public string Description { get; set; }
    }
}
