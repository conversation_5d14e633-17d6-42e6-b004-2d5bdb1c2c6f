﻿<?xml version="1.0" encoding="utf-8"?>
<?xml-stylesheet type="text/xsl" href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
		<procedure owner="" name="SP_Del_WRHouse_1" grant="">
			<parameters>
				<parameter name="LogonId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT a.UserId
				FROM TBL_Account a WHERE a.LogonId = :LogonId ;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Del_WRHouse_2" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="UserId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT 'X' FROM WR_HouseManagement
				WHERE WR_HouseManagement.WRHouseId = :WRHouseId AND WR_HouseManagement.UserId = :UserId
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Del_WRHouse_3" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT 'X' FROM WR_DeviceManagement
				WHERE WR_DeviceManagement.WRHouseId = :WRHouseId
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Del_WRHouse_4" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT 'X' FROM WR_FsuManagement
				WHERE WR_FsuManagement.WRHouseId = :WRHouseId
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Del_WRHouse_5" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT  b.SWStationId ,a.SWHouseId , a.HouseStatus
				FROM WR_HouseManagement a
				INNER JOIN WR_StationManagement b ON a.WRStationId = b.WRStationId
				WHERE a.WRHouseId = :WRHouseId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Del_WRHouse_6" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[ DELETE FROM WR_HouseManagement WHERE WR_HouseManagement.WRHouseId = :WRHouseId; ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Del_WRHouse_7" grant="">
			<parameters>
				<parameter name="SWHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
        <parameter name="SWStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[ DELETE FROM TBL_House WHERE TBL_House.StationId = :SWStationId AND TBL_House.HouseId = :SWHouseId; ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Del_WRHouse_8" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
        <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
      </parameters>
			<body>
				<![CDATA[ DELETE FROM TBL_RoomCMCC WHERE TBL_RoomCMCC.StationId = :SWStationId AND TBL_RoomCMCC.HouseId = :WRHouseId; ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Del_WRHouse_9" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[ SELECT  WRStationId,HouseName FROM WR_HouseManagement WHERE WR_HouseManagement.WRHouseId = :WRHouseId; ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Del_WRHouse_10" grant="">
			<parameters>
				<parameter name="WRStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[ SELECT  IFNULL(StationName,'') as StationName FROM WR_StationManagement WHERE WR_StationManagement.WRStationId =:WRStationId; ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Del_WRHouse_11" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[ DELETE FROM WR_HouseManagement WHERE WR_HouseManagement.WRHouseId = :WRHouseId; ]]>
			</body>
		</procedure>
	</procedures>
</root>
