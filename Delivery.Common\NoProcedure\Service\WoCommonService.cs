﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;

namespace Delivery.Common.NoProcedure.Service
{
    public class WoCommonService
    {
        private static WoCommonService _instance = null;
        public static WoCommonService Instance
        {
            get
            {
                if (_instance == null) _instance = new WoCommonService();
                return _instance;
            }
        }

        public DataTable GetCheckItemEvent(int OrderCheckId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("OrderCheckId", OrderCheckId);
                    DataTable dt = execHelper.ExecDataTable("CheckItem_EquipmentId", realParams);
                    string BaseTypeId = "", EquipmentId = "";
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        BaseTypeId = dt.Rows[0]["BaseTypeId"].ToString();
                        EquipmentId = Convert.ToString(dt.Rows[0]["EquipmentId"]);
                    }
                    realParams.Add("BaseTypeId", BaseTypeId);
                    realParams.Add("EquipmentId", EquipmentId);
                    DataTable dt2 = execHelper.ExecDataTable("CheckItem_OrderInfo", realParams);
                    DataTable dt3 = execHelper.ExecDataTable("CheckItem_AllConfig", realParams);

                    string EventStartTime1 = DateTime.Now.AddDays(-7).ToStr();
                    realParams.Add("EventStartTime1", EventStartTime1);

                    DataTable dt4 = execHelper.ExecDataTable("CheckItem_EventByStartTime", realParams);
                    return dt2;
                } catch (Exception ex)
                {
                    Logger.Log(ex);
                } finally
                {
                    dbHelper.Connection.Close();
                }
                return new DataTable();

            }
        }

        public DataTable GetTestOrderStats(int? UserId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    if(UserId == null)
                    {
                        UserId = -1;
                    }
                    realParams.Add("UserId", UserId);
                    DataTable dt = execHelper.ExecDataTable("TestOrder_RoleInfo", realParams);
                    string RoleId = "", RoleName = "";
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        RoleId = dt.Rows[0]["RoleId"].ToString();
                        RoleName = dt.Rows[0]["RoleName"].ToString();
                    }
                    realParams.Add("RoleId", RoleId);
                    realParams.Add("RoleName", RoleName);
                    DataTable result;
                    if (RoleName.Equals("系统管理员"))
                    {
                        result = execHelper.ExecDataTable("TestOrder_IsAdmin", realParams);
                    } else
                    {
                        result = execHelper.ExecDataTable("TestOrder_NotAdmin", realParams);
                    }
                    return result;

                } catch (Exception ex)
                {
                    Logger.Log(ex);
                } finally
                {
                    dbHelper.Connection.Close();
                }
                return new DataTable();

            }
        }
        public Object WO_GetWRStationCategoryName(int stationId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper);

                try
                {
                    int? StandardType = null;
                    string StationCategoryName = "";
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("stationId", stationId);
                    DataTable dtBtype = execHelper.ExecDataTable("WO_GetWRStationCategoryName_FuncBGetType", realParams);
                    if (dtBtype != null && dtBtype.Rows.Count > 0)
                    {
                        StandardType = int.Parse(dtBtype.Rows[0]["BType"].ToString());
                    }
                    if (StandardType==1)
                    {
                        DataTable dt = execHelper.ExecDataTable("WO_GetWRStationCategoryName_StationCategoryName1", realParams);
                        if (dt != null && dt.Rows.Count > 0)
                        {
                            StationCategoryName = dt.Rows[0]["StationCategoryName"].ToString();
                        }

                    } else if (StandardType==3)
                    {
                        DataTable dt2 = execHelper.ExecDataTable("WO_GetWRStationCategoryName_StationCategoryName3", realParams);
                        if (dt2 != null && dt2.Rows.Count > 0)
                        {
                            StationCategoryName = dt2.Rows[0]["StationCategoryName"].ToString();
                        }
                    }
                    if ("".Equals(StationCategoryName))
                    {
                        DataTable dt3 = execHelper.ExecDataTable("WO_GetWRStationCategoryName_StationCategoryName", realParams);
                        if (dt3 != null && dt3.Rows.Count > 0)
                        {
                            StationCategoryName = dt3.Rows[0]["StationCategoryName"].ToString();
                        }
                    }
                    return StationCategoryName;
                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                } finally {
                    dbHelper.Connection.Close();
                }
                return "";
            }
        }
        public DataTable WO_Equipment_Query(int OrderType, int UserId, int StationId, string HouseName, string EquipmentName, string EquipmentNo)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("OrderType", OrderType);
                    realParams.Add("UserId", UserId);
                    realParams.Add("StationId", StationId);
                    string strHouseName = "%" + HouseName + "%";
                    realParams.Add("HouseName", strHouseName);
                    string strEquipmentName = "%" + EquipmentName + "%";
                    realParams.Add("EquipmentName", strEquipmentName);
                    string strEquipmentNo = "%" + EquipmentNo + "%";
                    realParams.Add("EquipmentNo", strEquipmentNo);

                    int? RoleId = null; string RoleName = "";
                    DataTable dt = execHelper.ExecDataTable("WO_Equipment_Query_Role", realParams);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        RoleId = (int)dt.Rows[0]["RoleId"];
                        RoleName = dt.Rows[0]["RoleName"].ToString();
                    }
                    realParams.Add("RoleId", RoleId);
                    realParams.Add("RoleName", RoleName);
                    DataTable dt2 = execHelper.ExecDataTable("WO_Equipment_Query_AllInfo", realParams);
                    return dt2;
                }
                catch (Exception ex) { Logger.Log(ex); }
                finally { dbHelper.Connection.Close(); }
                return new DataTable();
            }
        }
        public DataTable WO_SiteWebGetStation(int StationId = -1)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("StationId", StationId);
                    DataTable dt = execHelper.ExecDataTable("WO_SiteWebGetStation", realParams);
                    return dt;
                } catch (Exception ex) { Logger.Log(ex); }
                finally { dbHelper.Connection.Close(); }
                return new DataTable();
            }
        }
        public DataTable WO_AcountInfoQuery(int UserId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                DataTable result = new DataTable();
                try
                {
                   
                    realParams.Add("UserId", UserId);
                    DataTable dt = execHelper.ExecDataTable("WO_AcountInfoQuery_UserRoleMap", realParams);
                    if (dt != null && dt.Rows.Count > 0)
                    {

                        result = execHelper.ExecDataTable("WO_AcountInfoQuery_RoleInfo", realParams);
                    } else
                    {
                        DataTable dt2 = execHelper.ExecDataTable("WO_AcountInfoQuery_CountUserRoleMap", realParams);
                        if (Convert.ToInt32(dt2.Rows[0]["count"]) == 1)
                        {
                            result = execHelper.ExecDataTable("WO_AcountInfoQuery_GetNotAdminInfo", realParams);
                        }


                    }
                    return result;

                } catch (Exception ex) { Logger.Log(ex); }
                finally { dbHelper.Connection.Close(); }
                return result;
            }
        }
        public void WO_FileUploadRec_AddOrUpdate(int OrderId, int EquipmentId, string FType, string FSaveName, string Uri, string FOriginalName, int FSize, int UserId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                { 
                    realParams.Add("OrderId", OrderId);
                    realParams.Add("EquipmentId", EquipmentId);
                    realParams.Add("FType", FType);
                    realParams.Add("FSaveName", FSaveName);
                    realParams.Add("Uri", Uri);
                    realParams.Add("FOriginalName", FOriginalName);
                    realParams.Add("FSize", FSize);
                    realParams.Add("UserId", UserId);
                    string CurrentTime = DateTime.Now.ToString();
                    realParams.Add("CurrentTime", CurrentTime);
                    DataTable dt = execHelper.ExecDataTable("WO_FileUploadRec_AddOrUpdate_MapOrdereId", realParams);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        execHelper.ExecuteNonQuery("WO_FileUploadRec_AddOrUpdate_FileUploadRec", realParams);
                    } else
                    {
                        execHelper.ExecuteNonQuery("WO_FileUploadRec_AddOrUpdate_Insert_FileUploadRec", realParams);
                    }
                    if (FType.Equals("PIC"))
                    {
                        execHelper.ExecuteNonQuery("WO_FileUploadRec_AddOrUpdate_Update_TestOrderEquipItem", realParams);
                    } else
                    {
                        execHelper.ExecuteNonQuery("WO_FileUploadRec_AddOrUpdate_Update_TestOrderEquipItem2", realParams);
                    }

                } catch (Exception ex) { Logger.Log(ex); }
                finally { dbHelper.Connection.Close(); }
            }
        }
        public void TestOrderArtSelfCheckList_Update(int OrderId, int CheckDicId, int IsPass)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("OrderId", OrderId);
                    realParams.Add("CheckDicId", CheckDicId);
                    realParams.Add("IsPass", IsPass);
                    execHelper.ExecuteNonQuery("Update_TestOrderArtSelfCheckList", realParams);

                } catch (Exception ex) { Logger.Log(ex); }
                finally { dbHelper.Connection.Close(); }
            }
        }
        public Object WO_ValidateGeoInfo(decimal Latitude, decimal Longitude)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, false);
                object result = null;
                try
                {
                    if (Latitude > 0 && Longitude > 0)
                    {
                        if ((Longitude >= 3 && Longitude <= 54) || (Longitude >= 73 && Longitude <= 136))
                        {
                            result = "ok";
                        }
                        else
                        {
                            result = "invalid";
                        }
                    }
                    else
                    {
                        result = "invalid";
                    }
                } catch (Exception ex) { Logger.Log(ex.Message); }
                finally { dbHelper.Connection.Close(); }
                return result;
            }
        }
        public DataTable WO_InstallClerk_QueryForOneCompany(int CompanyId, string kwClerk)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();

                try
                {
                    realParams.Add("KwClerk", kwClerk);
                    if (kwClerk.Trim() != "")
                    {
                        realParams.Add("WhereInstallClerk", true);
                    }
                    //string strKwClerk = "%" + kwClerk + "%";
                    //realParams.Add("kwClerk", kwClerk);
                    //realParams.Add("strKwClerk", strKwClerk);
                    realParams.Add("CompanyId", CompanyId);
                    return execHelper.ExecDataTable("WO_InstallClerk_QueryForOneCompany", realParams);
                } catch (Exception ex) { Logger.Log(ex.Message); }
                finally { dbHelper.Connection.Close(); }
                return new DataTable();
            }
        }
        public Object WO_InstallClerk_AddToCompany(int CompanyId, string ClerkName)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();

                try
                {
                    realParams.Add("CompanyId", CompanyId);
                    realParams.Add("ClerkName", ClerkName);
                    DataTable dt = execHelper.ExecDataTable("WO_InstallClerk_AddToCompany_ByClerkName", realParams);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        return "重复添加人员配置";
                    }

                    execHelper.ExecuteNonQuery("WO_InstallClerk_AddToCompany_Insert_InstallClerk", realParams);
                }
                catch (Exception ex) { Logger.Log(ex.Message); }
                finally { dbHelper.Connection.Close(); }
                return "OK";
            }
        }
        public DataTable WO_InstallClerk_QueryEx(string kwCompany, string kwClerk)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();

                try
                {
                    realParams.Add("kwCompany", kwCompany);
                    string strKwCompany = "%" + kwCompany + "%";
                    realParams.Add("strKwCompany", strKwCompany);
                    realParams.Add("kwClerk", kwClerk);
                    string strKwClerk = "%" + kwClerk + "%";
                    realParams.Add("strKwClerk", strKwClerk);
                    return execHelper.ExecDataTable("WO_InstallClerk_QueryEx", realParams);
                } catch (Exception ex) { Logger.Log(ex.Message); }
                finally { dbHelper.Connection.Close(); }
                return new DataTable();
            }
        }
        public Object WO_InstallCompany_Add(string CompanyName)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();

                try
                {
                    realParams.Add("CompanyName", CompanyName);

                    DataTable dt = execHelper.ExecDataTable("WO_InstallCompany_Add_CompanyConfig", realParams);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        return "重复添加安装公司配置";
                    }
                    execHelper.ExecuteNonQuery("WO_InstallCompany_CompanyName", realParams);
                    return "OK";
                }
                catch (Exception ex) { Logger.Log(ex.Message); }
                finally { dbHelper.Connection.Close(); }
                return null;
            }
        }
        public void WO_TestOrderArtSelfCheckList_Update(int OrderId, int CheckDicId, int IsPass)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();

                try
                {
                    realParams.Add("OrderId", OrderId);
                    realParams.Add("CheckDicId", CheckDicId);
                    realParams.Add("IsPass", IsPass);
                    execHelper.ExecuteNonQuery("WO_TestOrderArtSelfCheckList_Update", realParams);
                } catch (Exception ex) { Logger.Log(ex.Message); }
                finally { dbHelper.Connection.Close(); }

            }
        }
        public void TestOrderExpertCheckListUpdate(int OrderId, int CheckDicId, int IsPass,string PassNote)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string,object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("OrderId", OrderId);
                    realParams.Add("CheckDicId", CheckDicId);
                    realParams.Add("IsPass", IsPass);
                    realParams.Add("PassNote", PassNote);

                    execHelper.ExecuteNonQuery("Update_TestOrderExpertCheckList", realParams);
                }catch (Exception ex) { Logger.Log(ex.Message); }
                finally { dbHelper.Connection.Close(); }
            }
        }
        public void WO_TestOrderEquipItem_Del(int OrderId, int EquipmentId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string ,object> realParams = new Dictionary<string ,object>();
                try
                {
                    realParams.Add("OrderId", OrderId);
                    realParams.Add("EquipmentId", EquipmentId);

                    execHelper.ExecuteNonQuery("WO_TestOrderEquipItem_Del", realParams);
                    execHelper.ExecuteNonQuery("WO_TestOrderEquipItem_Del2", realParams);

                }catch (Exception ex) { Logger.Log(ex.Message); }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
        public string WO_CR2_DicStationTypeMap_Add(int WR_ItemId, int SiteWeb_ItemId)
        {
            using(DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string,object> realParams = new Dictionary<string ,object>();

                try
                {
                  realParams.Add("WR_ItemId", WR_ItemId);
                  realParams.Add("SiteWeb_ItemId", SiteWeb_ItemId);
                  string currentTime = DateTime.Now.ToStr();
                  realParams.Add("currentTime", currentTime);
                  DataTable dt = execHelper.ExecDataTable("DicStationTypeMap_ItemId", realParams);
                  if(dt !=null && dt.Rows.Count > 0)
                    {
                        execHelper.ExecuteNonQuery("Update_DicStationTypeMap", realParams);
                        return "OK";
                    }
                    execHelper.ExecuteNonQuery("Insert_DicStationTypeMap", realParams);
                    return "OK";
                }catch (Exception ex) { Logger.Log(ex.Message); }
                finally { dbHelper.Connection.Close(); }
                return null;
            }
        }
        public string WO_DicCmdCheckList_Update(int CheckId, string IsMust)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary <string,object> realParams = new Dictionary<string ,object>();

                try
                {
                    realParams.Add("CheckId", CheckId);
                    realParams.Add("IsMust", IsMust);
                    
                    if(IsMust.Equals("是"))
                    {
                        execHelper.ExecuteNonQuery("Update_DicCmdCheckList", realParams);
                    } else if(IsMust.Equals("否"))
                    {
                        execHelper.ExecuteNonQuery("Update_DicCmdCheckList2", realParams);
                        
                    }
                    DataTable dt = execHelper.ExecDataTable("Select_DicCmdCheckList", realParams);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        return "OK";
                    }
                    else
                    {
                        return "数据未更新";
                    }

                }catch (Exception ex) { Logger.Log(ex.Message); }
                finally
                {
                    dbHelper.Connection.Close();
                }
                return null;
            }
        }
        public string WO_DicEventCheckList_Update(int CheckId, string IsMust)
        {
            using(DbHelper dbHelper = new DbHelper()) {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string,object> realParams = new Dictionary<string ,object>();
                try
                {
                    realParams.Add("CheckId", CheckId);
                    realParams.Add("IsMust", IsMust);

                   if(IsMust.Equals("是"))
                    {
                        execHelper.ExecuteScalar("Update_DicEventCheckList", realParams);
                    } else if (IsMust.Equals("否"))
                    {
                        execHelper.ExecuteScalar("Update_DicEventCheckList2", realParams);
                    }
                    DataTable dt = execHelper.ExecDataTable("Select_DicEventCheckList", realParams);
                    if(dt.Rows.Count.Equals(1))
                    {
                        return "OK";
                    } else
                    {
                        return "数据未更新";
                    }

                }catch (Exception ex) { Logger.Log(ex.Message); }
                finally
                {
                    dbHelper.Connection.Close();
                }
                return null;
            }
        }
        public string WO_DicSigCheckList_Update(int CheckId, string IsMust, float? LimitDown, float? LimitUp)
        {
            using (DbHelper dbHelper = new DbHelper()) { 
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary <string,object> realParams = new Dictionary<string ,object>();

                try
                {
                    realParams.Add("CheckId", CheckId);
                    realParams.Add("IsMust", IsMust);
                    if(LimitDown!=null)
                    {
                        realParams.Add("LimitDown", LimitDown);
                    }
                    if(LimitUp!=null)
                    {
                        realParams.Add("LimitUp", LimitUp);
                    }
                    if (IsMust.Equals("是"))
                    {
                        execHelper.ExecuteScalar("Update_DicSigCheckList", realParams);
                    } else if(IsMust.Equals("否"))
                    {
                        execHelper.ExecuteScalar("Update_DicSigCheckList2", realParams);
                    }
                    DataTable dt = execHelper.ExecDataTable("Select_DicSigCheckList", realParams);
                    if(dt !=null && dt.Rows.Count==1)
                    {
                        return "OK";
                    }else
                    {
                        return "数据未更新";
                    }

                }catch (Exception ex) { Logger.Log(ex.Message); }
                finally
                {
                    dbHelper.Connection.Close();
                }
                return null;

            }
        }
        public string WO_CR2_StationType_Add(int ItemId, string ItemValue)
        {
            using (DbHelper dbHelper = new DbHelper()) {

                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string ,object> realParams = new Dictionary<string ,object>();

                try
                {
                  realParams.Add("ItemId", ItemId);
                  realParams.Add("ItemValue", ItemValue);
                  string currentTime = DateTime.Now.ToString();
                  realParams.Add("CurrentTime", currentTime);
                  DataTable dt = execHelper.ExecDataTable("Select_WRDataItem",realParams);
                  if(dt!=null && dt.Rows.Count > 0)
                    {
                        execHelper.ExecuteScalar("Update_WRDataItem", realParams);
                        return "OK";
                    }
                    execHelper.ExecuteScalar("Insert_WRDataItem", realParams);
                    return "OK";
                }
                catch (Exception ex) { Logger.Log(ex.Message); }
                finally { dbHelper.Connection.Close(); }
            }
            return null;
        }
    }
}
