﻿
using DM.TestOrder.Common.DBA;


using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using DM.TestOrder.Common;
using DM.TestOrder.Entity;


namespace DM.TestOrder.DAL.Helper
{
    public class WO_TestOrderFlowHelper
    {
        public static WO_TestOrderFlow FromDataRow(DataRow row) {
            var e = new WO_TestOrderFlow();
            try {
                e.OrderFlowId = SHelper.ToInt(row["OrderFlowId"]);
                e.OrderId = SHelper.ToInt(row["OrderId"]);
                e.OldOrderState = SHelper.ToInt(row["OldOrderState"]);
                e.NewOrderState = SHelper.ToInt(row["NewOrderState"]);
                e.StateSetUserId = int.Parse(row["StateSetUserId"].ToString());
                e.StateSetUserName = row["StateSetUserName"].ToString();
                e.Decision = row["Decision"].ToString();                
                e.Note = row["Note"].ToString();
                e.IsApprove = SHelper.ToInt(row["IsApprove"]);
                e.FlowText = row["FlowText"].ToString();
                e.SaveTime = SHelper.ToDateTime(row["SaveTime"].ToString());
            }
            catch (Exception ex) {
                MyLogger.ErrorFormat("WO_TestOrderFlowHelper.FromDataRow();error={0}", ex.Message);
                MyLogger.Error(ex.StackTrace);
                return null;
            }
            return e;
        }
    }
}
