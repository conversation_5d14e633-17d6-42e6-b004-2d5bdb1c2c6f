﻿using System.Collections.Generic;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class TSidDecription
    {
        public TSidDecription() { }

        public TSidDecription(string devId, List<string> sId)
        {
            DevID = devId;
            SID = sId;
        }

        public string DevID { get; set; }

        public List<string> SID { get; set; }

        public override string ToString()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
    }
}