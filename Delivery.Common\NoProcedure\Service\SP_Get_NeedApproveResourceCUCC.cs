﻿using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace Delivery.Common.NoProcedure.Service
{
    public class SP_Get_NeedApproveResourceCUCCService
    {
        private static SP_Get_NeedApproveResourceCUCCService _instance = null;

        public static SP_Get_NeedApproveResourceCUCCService Instance
        {
            get
            {
                if (_instance == null) _instance = new SP_Get_NeedApproveResourceCUCCService();
                return _instance;
            }
        }
        public DataTable GetNeedApproveResourceCUCC(string LogonId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                realParams.Add("LogonId", LogonId);
                try
                {
                    int? UserId =null;long p_StationCount;long p_HouseCount;
                    long p_FsuCount;
                    DataTable dt = executeHelper.ExecDataTable("NeedApproveResourceCUCCS_UserId", realParams);
                    if(dt!=null && dt.Rows.Count > 0)
                    {
                        UserId = (int)dt.Rows[0]["UserId"];    
                    }
                    realParams.Add("UserId", UserId);
                    object x = executeHelper.ExecuteScalar("NeedApproveResourceCUCCS_X", realParams);
                    string filterUser ;
                    if(x!=null && x!=DBNull.Value)
                    {
                        filterUser = XmlScriptProvider.Current.GetCommandSql("NeedApproveResourceCUCCS_Filter_UserId", realParams);
                        realParams.Add("filterUser", filterUser);
                        p_StationCount = (long)executeHelper.ExecuteScalar("NeedApproveResourceCUCCS_StationCount", realParams);
                        p_HouseCount = (long)executeHelper.ExecuteScalar("NeedApproveResourceCUCCS_HouseCount", realParams);
                        p_FsuCount = (long)executeHelper.ExecuteScalar("NeedApproveResourceCUCCS_FsuCount", realParams);
                        realParams.Add("p_StationCount", p_StationCount);
                        realParams.Add("p_HouseCount", p_HouseCount);
                        realParams.Add("p_FsuCount", p_FsuCount);
                        return executeHelper.ExecDataTable("NeedApproveResourceCUCCS", realParams);
                    } else
                    {
                        return executeHelper.ExecDataTable("NeedApproveResourceCUCCS2", realParams);
                    }
                    
                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    return new DataTable();
                }finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
    }
}
