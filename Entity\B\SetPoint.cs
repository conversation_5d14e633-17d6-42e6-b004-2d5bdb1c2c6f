﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Diagnostics;
using System.Xml;

namespace ENPC.Kolo.Entity.B
{
    /// <summary>
    /// 5.2.11.5　用户请求写监控点的设置值
    /// </summary>
    public sealed class SetPoint : BMessage
    {
        public int UserId { get; set; }
        public int ControlId { get; set; }
        public DateTime StartTime { get; set; }
        public DeviceSetPoint[] Devices { get; private set; }

        public SetPoint(string fsuId, string fsuCode, params DeviceSetPoint[] devices)
            : base()
        {
            Debug.Assert(devices != null);

            MessageType = (int)BMessageType.SetPoint;
            FsuId = fsuId;
            FsuCode = fsuCode;

            Devices = devices;
        }

        public override string Serialize()
        {
            XmlDocument xmldoc = CreateXmlDocument();
            AppendPK_Type(xmldoc, "Request", "SET_POINT", "1001");

            XmlElement xe2 = xmldoc.CreateElement("Info");
            XmlElement xe21 = xmldoc.CreateElement("FsuId");
            xe21.InnerText = FsuId;
            xe2.AppendChild(xe21);
            XmlElement xe22 = xmldoc.CreateElement("FsuCode");
            xe22.InnerText = FsuCode;
            xe2.AppendChild(xe22);


            XmlElement xe23 = xmldoc.CreateElement("Value");

            XmlElement xe231 = xmldoc.CreateElement("DeviceList");
            xe23.AppendChild(xe231);

            foreach (DeviceSetPoint device in Devices)
            {
                XmlElement xe2311 = xmldoc.CreateElement("Device");
                xe2311.SetAttribute("Id", device.DeviceId);
                xe2311.SetAttribute("Code", device.DeviceCode);
                xe231.AppendChild(xe2311);
                
                foreach(TSemaphore semaphore in device.Values)
                { 
                    XmlElement xe23111 = xmldoc.CreateElement("TSemaphore");
                    xe23111.SetAttribute("Type",  semaphore.Type.ToString());
                    xe23111.SetAttribute("Id", semaphore.Id);
                    xe23111.SetAttribute("MeasuredVal", semaphore.MeasuredValue.ToString());
                    xe23111.SetAttribute("SetupVal", semaphore.SetupValue.ToString());
                    xe23111.SetAttribute("Status", semaphore.State.ToString());
                    xe2311.AppendChild(xe23111);
                }
            }
            xe2.AppendChild(xe23);
            XmlNode root = xmldoc.SelectSingleNode("Request");
            root.AppendChild(xe2);


            return xmldoc.InnerXml;
        }

        public static SetPoint Deserialize(string raw)
        {
            throw new NotImplementedException();
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();

            sb.AppendFormat("{0}, {1}: {2}, {3}, {4}",
                MessageId, (BMessageType)MessageType, FsuId, FsuCode, Devices != null ? Devices.Length : 0);
            sb.AppendLine();
            if (Devices != null)
            {
                foreach (DeviceSetPoint item in Devices)
                {
                    sb.AppendFormat("{0}", item);
                    sb.AppendLine();
                }
            }

            return sb.ToString();
        }

    }

    public sealed class DeviceSetPoint
    {
        public string DeviceId { get; private set; }
        public string DeviceCode { get; private set; }

        public TSemaphore[] Values { get; private set; }

        public DeviceSetPoint(string deviceId, string deviceCode, params TSemaphore[] values)
        {
            DeviceId = deviceId;
            DeviceCode = deviceCode;
            Values = values;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}", DeviceId, DeviceCode);
            sb.AppendLine();

            foreach (TSemaphore item in Values)
            {
                sb.AppendFormat("{0}", item);
                sb.AppendLine();
            }

            return sb.ToString();
        }

    }
}
