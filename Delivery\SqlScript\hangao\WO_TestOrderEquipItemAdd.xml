﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <procedures>	
	  <procedure owner="" name="WO_FuncBGetType_GetData" grant="">
		  <body>
			  <![CDATA[ 
				SELECT  ConfigValue from TBL_SysConfig  where TBL_SysConfig.ConfigKey='StandardCategory';
			 ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="WO_FuncGetEquipBaseType_GetMinBaseTypeId" grant="">
		  <parameters>
			  <parameter name="EquipmentId"  type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[ 
				SELECT  min(sig.BaseTypeId ) BaseTypeId
				FROM   TBL_Signal sig 
				INNER JOIN TBL_Equipment e ON e.EquipmentId = @EquipmentId and e.EquipmentTemplateId=sig.EquipmentTemplateId
				where sig.BaseTypeId > 0;
			 ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="WO_FuncGetEquipBaseType_GetMinBaseTypeIdWithFirstQueryIsNull" grant="">
		  <parameters>
			  <parameter name="EquipmentId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[ 
				SELECT  min(ev.BaseTypeId ) BaseTypeId
				FROM   TBL_EventCondition ev 
				INNER JOIN TBL_Equipment e ON e.EquipmentId = @EquipmentId and e.EquipmentTemplateId=ev.EquipmentTemplateId
				where ev.BaseTypeId>0;
			 ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="WO_TestOrderEquipItem_Add_GetBaseName" grant="">
		  <parameters>
			  <parameter name="BaseEquipmentId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[ 
				select BaseEquipmentName  from TBL_EquipmentBaseType where TBL_EquipmentBaseType.BaseEquipmentId = @BaseEquipmentId;
			 ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="WO_TestOrderEquipItem_Add_InsertFirst" grant="">
		  <parameters>
			  <parameter name="OrderId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="EquipmentId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="BaseEquipmentId"  type="int" direction="Input" size="1024" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="BaseEquipmentName"  type="string" direction="Input" size="1024" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="UriProtocol"  type="string" direction="Input" size="1024" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="UriImage"  type="string" direction="Input" size="1024" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SaveTime"  type="string" direction="Input" size="1024" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
			  <replace keyName="LimitCount"> Limit @ReturnCount </replace>
		  </replaces>
		  <body>
			  <![CDATA[ 
				INSERT INTO WO_TestOrderEquipItem (OrderId, EquipmentId, BaseEquipmentID, BaseEquipmentName, UriProtocol, UriImage, SaveTime)
				VALUES (@OrderId, @EquipmentId, @BaseEquipmentId, @BaseEquipmentName, @UriProtocol, @UriImage, @SaveTime);
			 ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="WO_TestOrderEquipItem_Add_GetEquipInfo" grant="">
		  <parameters>
			  <parameter name="EquipmentId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[ 
					select EquipmentName ,d.ItemValue EquipmentCategoryName 
					from  TBL_Equipment e 
					inner join TBL_DataItem d on d.EntryId=7 and d.ItemId=e.EquipmentCategory
					where e.EquipmentId = @EquipmentId;
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="WO_TestOrderEquipItem_Add_CMCCInsertSecond" grant="">
		  <parameters>
			  <parameter name="EquipmentId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="StandardType"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="OrderId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="EquipmentName"  type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="EquipmentCategoryName"  type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="BaseEquipmentId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>

		  </parameters>
		  <body>
			  <![CDATA[ 
					-- 1-信号
					INSERT INTO WO_TestOrderEquipItemCheckList 
					(
					OrderId,EquipmentId,
					BaseEquipmentId, BaseEquipmentName, BaseTypeId, BaseTypeName, CheckType, LimitDown, LimitUp, Note,CheckId, Unit,
					EquipmentName, EquipmentCategoryName,
					SignalType,StandardName ,EquipmentLogicClass, 
					CheckTypeId)
					select  
					@OrderId, @EquipmentId,
					s.BaseEquipmentId, s.BaseEquipmentName, s.BaseTypeId, s.BaseTypeName, CheckType, LimitDown, LimitUp, Note,CheckId, u.BaseUnitSymbol,
					@EquipmentName, @EquipmentCategoryName
					,ds.SignalLogicClass,ds.SignalStandardName,ds.EquipmentLogicClass, 1 
					from WO_DicSigCheckList s
					inner join TBL_SignalBaseDic d on s.BaseTypeId=d.BaseTypeId
					LEFT  JOIN TBL_BaseUnitDic u ON d.UnitId=u.BaseUnitID
					inner  JOIN  TBL_SignalBaseMap m on m.StandardType = @StandardType and  m.StationBaseType=1 and  FLOOR(m.BaseTypeId / 1000)=FLOOR(s.BaseTypeId / 1000)
					inner  JOIN TBL_StandardDicSig ds on  ds.StandardType = @StandardType  and ds.StandardDicId=m.StandardDicId
					where s.BaseEquipmentId = @BaseEquipmentId and IsMust = '是'
					AND   EXISTS(
							SELECT 1 FROM   TBL_Signal sig 
							INNER JOIN TBL_Equipment e ON e.EquipmentId = @EquipmentId and e.EquipmentTemplateId=sig.EquipmentTemplateId
							WHERE sig.BaseTypeId =s.BaseTypeId ); 
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="WO_TestOrderEquipItem_Add_CMCCInsertThird" grant="">
		  <parameters>
			  <parameter name="EquipmentId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="StandardType"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="OrderId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="EquipmentName"  type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="EquipmentCategoryName"  type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="BaseEquipmentId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>

		  </parameters>
		  <body>
			  <![CDATA[ 
					-- 2-event
					INSERT INTO WO_TestOrderEquipItemCheckList 
					(
					OrderId,EquipmentId,
					BaseEquipmentId, BaseEquipmentName, BaseTypeId, BaseTypeName, CheckType,   Note,CheckId,
					EquipmentName, EquipmentCategoryName,
					LogicClass,StandardName ,EquipmentLogicClass,
					CheckTypeId)
					select   
					@OrderId, @EquipmentId,
					s.BaseEquipmentId, s.BaseEquipmentName, s.BaseTypeId, s.BaseTypeName, CheckType, Note,CheckId
					,@EquipmentName, @EquipmentCategoryName
					,ds.EventLogicClass,ds.EventStandardName,ds.EquipmentLogicClass, 2
					from WO_DicEventCheckList s
					inner  JOIN  TBL_EventBaseMap m on m.StandardType= @StandardType and  m.StationBaseType=1 and  m.BaseTypeId=s.BaseTypeId 
					inner  JOIN TBL_StandardDicEvent ds on  ds.StandardType= @StandardType  and ds.StandardDicId = m.StandardDicId
					where s.BaseEquipmentId= @BaseEquipmentId and IsMust ='是'
					AND    EXISTS(
							SELECT 1 FROM   TBL_EventCondition ec 
							INNER JOIN TBL_Equipment e ON e.EquipmentId = @EquipmentId and e.EquipmentTemplateId = ec.EquipmentTemplateId
							WHERE FLOOR(ec.BaseTypeId / 1000)=FLOOR(s.BaseTypeId / 1000) ); 
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="WO_TestOrderEquipItem_Add_CMCCInsertForth" grant="">
		  <parameters>
			  <parameter name="EquipmentId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="StandardType"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="OrderId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="EquipmentName"  type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="EquipmentCategoryName"  type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="BaseEquipmentId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[ 
					-- 3-控制
					INSERT INTO WO_TestOrderEquipItemCheckList 
					(
					OrderId,EquipmentId,
					BaseEquipmentId, BaseEquipmentName, BaseTypeId, BaseTypeName, CheckType, Note,CheckId
					,EquipmentName, EquipmentCategoryName
					,LogicClass,StandardName,EquipmentLogicClass,
					CheckTypeId
					)
					select   
					@OrderId, @EquipmentId,
					s.BaseEquipmentId, s.BaseEquipmentName, s.BaseTypeId, s.BaseTypeName, CheckType,  Note,CheckId
					,@EquipmentName, @EquipmentCategoryName
					,ds.ControlLogicClass,ds.ControlStandardName,ds.EquipmentLogicClass,
					3 
					from WO_DicCmdCheckList s
					inner  JOIN  TBL_CommandBaseMap m on m.StandardType = @StandardType and  m.StationBaseType=1  and  m.BaseTypeId=s.BaseTypeId 
					inner  JOIN TBL_StandardDicControl ds on  m.StandardType = @StandardType  and ds.StandardDicId=m.StandardDicId
					where s.BaseEquipmentId = @BaseEquipmentId and IsMust ='是'
					AND    EXISTS(
						SELECT 1 FROM   TBL_Control ct 
						INNER JOIN TBL_Equipment e ON e.EquipmentId = @EquipmentId and e.EquipmentTemplateId=ct.EquipmentTemplateId
						WHERE FLOOR(ct.BaseTypeId / 1000)=FLOOR(s.BaseTypeId / 1000) );
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="WO_TestOrderEquipItem_Add_CUCCInsertSignalDic" grant="">
		  <parameters>
			  <parameter name="EquipmentId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="StandardType"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="OrderId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="EquipmentName"  type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="EquipmentCategoryName"  type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="BaseEquipmentId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[ 
					-- 1-信号
					INSERT INTO WO_TestOrderEquipItemCheckList 
					(
					OrderId,EquipmentId,
					BaseEquipmentId, BaseEquipmentName, BaseTypeId, BaseTypeName, CheckType, LimitDown, LimitUp, Note,CheckId, Unit,
					EquipmentName, EquipmentCategoryName,
					SignalType,StandardName ,EquipmentLogicClass, 
					CheckTypeId)
					select  
					@OrderId, @EquipmentId,
					s.BaseEquipmentId, s.BaseEquipmentName, s.BaseTypeId, s.BaseTypeName, CheckType, LimitDown, LimitUp, Note,CheckId, u.BaseUnitSymbol,
					@EquipmentName, @EquipmentCategoryName
					,ds.SignalLogicClass,ds.SignalStandardName,ds.EquipmentLogicClass,
					1 
					from WO_DicSigCheckList s
					inner join TBL_SignalBaseDic d on s.BaseTypeId = d.BaseTypeId
					LEFT  JOIN TBL_BaseUnitDic u ON d.UnitId = u.BaseUnitID
					inner  JOIN  (
									SELECT BaseTypeId, SignalLogicClass, EquipmentLogicClass, 	
									REPLACE(
								        SUBSTRING(
								            STRING_AGG(SignalStandardName, ';' ORDER BY SignalStandardName),
								            2
								        ),
								        '#',
								        '&'
								    ) AS SignalStandardName					
									FROM (	SELECT m.*
									,ds.SignalLogicClass,ds.EquipmentLogicClass, ds.SignalStandardName	
									FROM TBL_SignalBaseMap m 
									INNER JOIN TBL_StandardDicSig ds ON ds.StandardType=3  AND m.StandardDicId = ds.StandardDicId 
									WHERE m.StandardType=3 
									ORDER BY m.BaseTypeId) tab
									GROUP BY BaseTypeId, SignalLogicClass,EquipmentLogicClass
					) ds on FLOOR(ds.BaseTypeId div 1000) = FLOOR(s.BaseTypeId div 1000) -- 20180622高映军修改 匹配模块电流、电池单体等多个问题
					where s.BaseEquipmentId = @BaseEquipmentId and IsMust ='是'
					AND    EXISTS(
							SELECT 1 FROM   TBL_Signal sig 
							INNER JOIN TBL_Equipment e ON e.EquipmentId = @EquipmentId and e.EquipmentTemplateId=sig.EquipmentTemplateId
							WHERE sig.BaseTypeId =s.BaseTypeId  
							); 
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="WO_TestOrderEquipItem_Add_CUCCInsertEventDic" grant="">
		  <parameters>
			  <parameter name="EquipmentId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="StandardType"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="OrderId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="EquipmentName"  type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="EquipmentCategoryName"  type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="BaseEquipmentId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[ 
					-- 2-event
					INSERT INTO WO_TestOrderEquipItemCheckList 
					(
					OrderId,EquipmentId,
					BaseEquipmentId, BaseEquipmentName, BaseTypeId, BaseTypeName, CheckType,   Note,CheckId,
					EquipmentName, EquipmentCategoryName,
					LogicClass,StandardName ,EquipmentLogicClass,
					CheckTypeId)
					select   
					@OrderId, @EquipmentId,
					s.BaseEquipmentId, s.BaseEquipmentName, s.BaseTypeId, s.BaseTypeName, CheckType, Note,CheckId
					,@EquipmentName, @EquipmentCategoryName
					,ds.EventLogicClass,ds.EventStandardName,ds.EquipmentLogicClass,
					2
					from WO_DicEventCheckList s
					inner  JOIN  TBL_EventBaseMap m on m.StandardType = @StandardType and  m.StationBaseType=1 and  m.BaseTypeId=s.BaseTypeId 
					inner  JOIN TBL_StandardDicEvent ds on  ds.StandardType = @StandardType  and ds.StandardDicId = m.StandardDicId

					where s.BaseEquipmentId = @BaseEquipmentId and IsMust ='是'
					AND    EXISTS(
							SELECT 1 FROM   TBL_EventCondition ec 
							INNER JOIN TBL_Equipment e ON e.EquipmentId = @EquipmentId and e.EquipmentTemplateId = ec.EquipmentTemplateId
							WHERE FLOOR(ec.BaseTypeId / 1000)=FLOOR(s.BaseTypeId / 1000)
							); 
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="WO_TestOrderEquipItem_Add_CUCCInsertControlDic" grant="">
		  <parameters>
			  <parameter name="EquipmentId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="StandardType"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="OrderId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="EquipmentName"  type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="EquipmentCategoryName"  type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="BaseEquipmentId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[ 
					-- 3-控制
					INSERT INTO WO_TestOrderEquipItemCheckList 
					(
					OrderId,EquipmentId,
					BaseEquipmentId, BaseEquipmentName, BaseTypeId, BaseTypeName, CheckType, Note,CheckId
					,EquipmentName, EquipmentCategoryName
					,LogicClass,StandardName,EquipmentLogicClass,
					CheckTypeId
					)
					select   
					@OrderId, @EquipmentId,
					s.BaseEquipmentId, s.BaseEquipmentName, s.BaseTypeId, s.BaseTypeName, CheckType,  Note,CheckId
					,@EquipmentName, @EquipmentCategoryName
					,ds.ControlLogicClass,ds.ControlStandardName,ds.EquipmentLogicClass,
					3 
					from WO_DicCmdCheckList s
					inner  JOIN  (
									SELECT BaseTypeId, ControlLogicClass, EquipmentLogicClass, 	
									REPLACE(
								        SUBSTRING(
								            STRING_AGG(ControlStandardName, ';' ORDER BY ControlStandardName),
								            2
								        ),
								        '#',
								        '&'
								    ) AS SignalStandardName
									FROM (	SELECT m.*
									,ds.ControlLogicClass,ds.EquipmentLogicClass, ds.ControlStandardName	
									FROM TBL_CommandBaseMap m 
									INNER JOIN TBL_StandardDicControl ds ON ds.StandardType = 3 AND m.StandardDicId=ds.StandardDicId 
									WHERE m.StandardType = 3 
									ORDER BY m.BaseTypeId ) taba
									GROUP BY BaseTypeId, ControlLogicClass,EquipmentLogicClass
					) ds on ds.BaseTypeId = s.BaseTypeId 
					where s.BaseEquipmentId = @BaseEquipmentId and IsMust ='是'
					AND    EXISTS(
						SELECT 1 FROM   TBL_Control ct 
						INNER JOIN TBL_Equipment e ON e.EquipmentId = @EquipmentId and e.EquipmentTemplateId=ct.EquipmentTemplateId
						WHERE FLOOR(ct.BaseTypeId / 1000)=FLOOR(s.BaseTypeId / 1000)
						);

				]]>
		  </body>
	  </procedure>
  </procedures>
</root>
