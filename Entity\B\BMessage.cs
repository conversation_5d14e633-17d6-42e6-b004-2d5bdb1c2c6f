﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B
{
    public abstract class BMessage : Message
    {
        public string SourceHostId { get; set; }

        public string FsuId { get; protected set; }
        public string FsuCode { get; protected set; }

        public BMessage()
            : base()
        {
            // B接口中没有消息Id，在这生成随机的Id，目地是兼容原有的数据库存储逻辑
            Random rand = new Random();
            MessageId = (uint)rand.Next(0, int.MaxValue);

            MessageFamily = MessageFamily.B;
            MessageType = (int)BMessageType.Undefined;
        }

        protected XmlDocument CreateXmlDocument()
        {
            XmlDocument xmldoc = new XmlDocument();

            XmlDeclaration xmldecl;
            xmldecl = xmldoc.CreateXmlDeclaration("1.0", "UTF-8", null);
            xmldoc.AppendChild(xmldecl);

            return xmldoc;
        }

        protected void AppendPK_Type(XmlDocument xmldoc, string protocolType, string name, string code)
        {
            XmlElement xmlelem = xmldoc.CreateElement("", protocolType , "");
            xmldoc.AppendChild(xmlelem);

            XmlNode root = xmldoc.SelectSingleNode(protocolType);

            XmlElement xe1 = xmldoc.CreateElement("PK_Type");
            XmlElement xesub1 = xmldoc.CreateElement("Name");
            xesub1.InnerText = name;
            xe1.AppendChild(xesub1);
            XmlElement xesub2 = xmldoc.CreateElement("Code");
            xesub2.InnerText = code;
            xe1.AppendChild(xesub2);

            root.AppendChild(xe1);
        }
    }
}
