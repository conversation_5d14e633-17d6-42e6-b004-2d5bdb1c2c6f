﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 监控点信号配置信息
    /// </summary>
    //public sealed class TSignal : BStruct
    public class TSignal : BStruct
    {
        /// <summary>
        /// 监控点ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 信号名称
        /// </summary>
        public string SignalName { get; set; }
        /// <summary>
        /// 告警等级
        /// </summary>
        public EnumState AlarmLevel { get; set; }
        /// <summary>
        /// 门限值
        /// </summary>
        public float? Threshold { get; set; }
        /// <summary>
        /// 绝对阀值
        /// </summary>
        public float? AbsoluteVal { get; set; }
        /// <summary>
        /// 百分比阀值
        /// </summary>
        public float? RelativeVal { get; set; }
        /// <summary>
        /// 描述信息
        /// </summary>
        public string Describe { get; set; }
        /// <summary>
        /// 网管告警编号
        /// </summary>
        public string NMAlarmID { get; set; }

        /// <summary>
        /// 同一设备同类测点顺序号
        /// </summary>
        public string SignalNumber { get; set; }

        #region SiteWeb配置

        public EnumLevel MyAlarmLevel { get; set; }

        #endregion

        /// <summary>
        /// 监控点信号配置信息
        /// </summary>
        public TSignal() { }

        /// <summary>
        /// 监控点信号配置信息
        /// </summary>
        /// <param name="type">监控系统数据的种类</param>
        /// <param name="id">监控点ID</param>
        /// <param name="signalName">信号名称</param>
        /// <param name="alarmLevel">告警等级</param>
        /// <param name="threshold">门限值</param>
        /// <param name="absoluteVal">绝对阀值</param>
        /// <param name="relativeVal">百分比阀值</param>
        /// <param name="describe">描述信息</param>
        /// <param name="nmAlarmId">网管告警编号</param>
        public TSignal(EnumType type, string id, string signalName, EnumState alarmLevel, float? threshold,
            float? absoluteVal, float? relativeVal, string describe, string nmAlarmId, string signalNumber)
        //public TSignal(EnumType type, string id, string signalName, EnumLevel alarmLevel, float? threshold,
        //    string nmAlarmId, string signalNumber)
        {
            Type = type;
            ID = id;
            SignalName = signalName;
            AlarmLevel = alarmLevel;
            Threshold = threshold;
            AbsoluteVal = absoluteVal;
            RelativeVal = relativeVal;
            Describe = describe;
            NMAlarmID = nmAlarmId;
            SignalNumber = signalNumber;

            //告警等级转换
            if (AlarmLevel == EnumState.CRITICAL)
            {
                MyAlarmLevel = EnumLevel.CRITICAL;
            }
            else if (AlarmLevel == EnumState.MAJOR)
            {
                MyAlarmLevel = EnumLevel.MAJOR;
            }
            else if (AlarmLevel == EnumState.MINOR)
            {
                MyAlarmLevel = EnumLevel.MINOR;
            }
            else if (AlarmLevel == EnumState.HINT)
            {
                MyAlarmLevel = EnumLevel.HINT;
            }
            else
            {
                MyAlarmLevel = EnumLevel.INVALID;
            }
        }

        /// <summary>
        /// 监控点信号配置信息(使用EnumLevel)
        /// </summary>
        /// <param name="type">监控系统数据的种类</param>
        /// <param name="id">监控点ID</param>
        /// <param name="signalName">信号名称</param>
        /// <param name="alarmLevel">告警等级</param>
        /// <param name="threshold">门限值</param>
        /// <param name="absoluteVal">绝对阀值</param>
        /// <param name="relativeVal">百分比阀值</param>
        /// <param name="describe">描述信息</param>
        /// <param name="nmAlarmId">网管告警编号</param>
        public TSignal(EnumType type, string id, string signalName, EnumLevel alarmLevel, float? threshold,
            float? absoluteVal, float? relativeVal, string describe, string nmAlarmId, string signalNumber)
        {
            Type = type;
            ID = id;
            SignalName = signalName;
            MyAlarmLevel = alarmLevel;
            Threshold = threshold;
            AbsoluteVal = absoluteVal;
            RelativeVal = relativeVal;
            Describe = describe;
            NMAlarmID = nmAlarmId;
            SignalNumber = signalNumber;

            //告警等级转换
            if (MyAlarmLevel == EnumLevel.CRITICAL)
            {
                AlarmLevel = EnumState.CRITICAL;
            }
            else if (MyAlarmLevel == EnumLevel.MAJOR)
            {
                AlarmLevel = EnumState.MAJOR;
            }
            else if (MyAlarmLevel == EnumLevel.MINOR)
            {
                AlarmLevel = EnumState.MINOR;
            }
            else if (MyAlarmLevel == EnumLevel.HINT)
            {
                AlarmLevel = EnumState.HINT;
            }
        }

        public override string ToString()
        {
            return String.Format(
                "[{0}, {1} ,{2} , {3}, {4}, {5}, {6}]",
                Type, ID, SignalName, AlarmLevel, Threshold, NMAlarmID, SignalNumber);
        }
    }
}
