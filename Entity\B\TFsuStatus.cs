﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B
{
    public class TFsuStatus
    {
        public float CpuUsage { get; private set; }
        public float MemUsage { get; private set; }

        public TFsuStatus(float cpuUsage, float memUsage)
        {
            CpuUsage = cpuUsage;
            MemUsage = memUsage;
        }

        public string Serialize()
        {
            throw new NotImplementedException();
        }

        public static TFsuStatus Deserialize(string raw)
        {
            throw new NotImplementedException();
        }

        public override string ToString()
        {
            return String.Format("{0:#0.0}, {1:#0.0}", CpuUsage, MemUsage);
        }
    }
}
