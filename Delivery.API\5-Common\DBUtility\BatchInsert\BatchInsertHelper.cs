﻿



using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using Delivery.TestOrder.Common;

using DM.TestOrder.Common;
using DM.TestOrder.Common.DBA;

namespace Delivery.TestOrder.Common.DBUtility
{

    public partial class BatchInsertHelper
    {


        private static int MAX_ROWS_ONE_BATCH = 1000;

        //IBatchInsertRow
        public static bool Execute(IList dataList) {
             try {
                 if (dataList.Count == 0)
                     return true;

                var sqlBatch = BatchInsertHelper.GetInsertSqlBatch(dataList);

                foreach (var sql in sqlBatch) {
                    DBHelper.ExecuteNonQueryInBatch(sql);
                }
            }
            catch (Exception ex) {
                MyLogger.ErrorFormat("BatchInsertHelper.Execute();Error={0}", ex.Message);
                MyLogger.Error(ex.StackTrace);
                return false;
            }
             return true;

         }
        private static List<string> GetInsertSqlBatch(IList dataList) {
            var sqlBatch = new List<string>();
          
            StringBuilder resultBuilder = new StringBuilder();


            if (dataList.Count == 0)
                return null;

            //var insertHeader = (dataList[0] as IBatchInsertRow).GetInsertHeader();
            var insertHeader = "\r"+(dataList[0] as IBatchInsertRow).GetInsertHeader();//log optimize


            for (int i = 0; i < dataList.Count; i++) {
                IBatchInsertRow sig = dataList[i] as IBatchInsertRow;
                StringBuilder sqlRow = new StringBuilder();

                if (i % MAX_ROWS_ONE_BATCH == 0)//最多N千行
                {
                    if (i != 0) {
                        //sqlRow.AppendLine("GO");
                        sqlBatch.Add(resultBuilder.ToString());
                        resultBuilder.Clear();
                    }

                    sqlRow.AppendLine(insertHeader);
                    sqlRow.AppendLine("VALUES");
                }


                var sqlValues = "";

                sqlValues = sig.GetDataRowValueString();


                sqlRow.Append(sqlValues);

                //last row, not add                     
                if ((i + 1) % MAX_ROWS_ONE_BATCH != 0 && i != dataList.Count - 1) {
                    sqlRow.AppendLine(",");
                }
                else
                    sqlRow.AppendLine();

                resultBuilder.Append(sqlRow);
            }

            if (resultBuilder.Length > 0)
                sqlBatch.Add(resultBuilder.ToString());

            return sqlBatch;
        }

    }
}
