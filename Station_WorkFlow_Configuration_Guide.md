# 站址管理 - WorkFlow配置功能说明

## 功能概述

本功能为站址管理页面的新建站址入网管理功能添加了基于配置的站址名称输入方式。根据 `appsettings.json` 中的 `WorkFlow` 属性值，系统会自动切换不同的站址名称输入模式。

## 配置说明

### appsettings.json 配置
```json
{
  "WorkFlow": "true"  // 或 "false"
}
```

### 配置效果对比

| WorkFlow配置 | 显示控件 | 数据来源 | 传值方式 |
|-------------|---------|---------|---------|
| `false` | 站址名称输入框 | 用户手动输入 | 直接获取输入框值 |
| `true` | 站址下拉选择框 | TIr_EomsStation表 | 获取选中项的文本值 |

## 数据库表结构

### TIr_EomsStation表
```sql
CREATE TABLE TIr_EomsStation (
    SsName         VARCHAR(255) NOT NULL,
    EomsStationId  BIGINT NOT NULL,
    EomsSationName VARCHAR(255) NOT NULL,
    DistrictName   VARCHAR(255),
    FloorNo        VARCHAR(255),
    PropList       VARCHAR(1024),
    Longitude      FLOAT,
    Latitude       FLOAT,
    ContainBts     VARCHAR(255),
    Acreage        FLOAT,
    BuildingType   VARCHAR(255),
    ContainNode    VARCHAR(255),
    SetupTime      VARCHAR(255),
    Vendors        VARCHAR(255),
    BordNumber     INT,
    ServiceLevel   VARCHAR(255),
    UpdateTime     DATETIME,
    ProcessType    TINYINT,
    StationId      INT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

## 技术实现

### 1. 后端API接口

#### 新增GetAssetStation接口
**文件：** `Delivery.API/0-Controllers/CMCC/StationController.cs`

```csharp
[HttpGet("[action]")]
public string GetAssetStation()
{
    DeliveryDataBsl ccbsl = new DeliveryDataBsl();
    DataTable dt = ccbsl.GetAssetStation();
    return JsonHelper.SerializeData(dt);
}
```

#### BSL层实现
**文件：** `Delivery.BSL/ManagementBSL.cs`

```csharp
/// <summary>
/// 获取资产站址信息
/// </summary>
/// <returns></returns>
public DataTable GetAssetStation()
{
    string sql = @"SELECT EomsStationId as ItemId, EomsSationName as ItemValue 
                  FROM TIr_EomsStation 
                  ORDER BY EomsSationName";
    
    DataTable dt = exesql.ExecuteSQL(sql);
    if (dt == null)
        return new DataTable();
    return dt;
}
```

### 2. 前端实现

#### HTML结构修改
**文件：** `Delivery/Pages/CMCC/StationManagement.cshtml`

```html
<td style="text-align: left; white-space: nowrap">站址名称</td>
<td>
    <input type='text' class="easyui-textbox" data-options="prompt:'请输入站址名称',required:'true'" id="txtStationName" style="width: 140px;" />
    <select id="cboAssetStation" class="easyui-combobox" style="width: 140px; display: none;" data-options="valueField:'ItemId',textField:'ItemValue',prompt:'请选择站址'"></select>
</td>
```

#### JavaScript功能实现
**文件：** `Delivery/wwwroot/CMCC/scripts/StationManagement.js`

**配置获取：**
```javascript
// 获取应用配置信息
var getAppSettings = function () {
    $.ajax({
        type: "GET",
        url: `${WORKFLOW_HANDLER}/GetAppSettings`,
        dataType: 'json',
        async: false,
        success: function (result) {
            isWorkFlowEnabled = result.Data === 'true';
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            console.error('获取应用配置失败：', errorThrown);
            isWorkFlowEnabled = false;
        }
    });
};
```

**资产站址获取：**
```javascript
// 获取资产站址列表
var getAssetStationList = function () {
    if (!isWorkFlowEnabled) {
        return;
    }
    
    $.ajax({
        type: "GET",
        url: `${STATION_HANDLER}/GetAssetStation`,
        dataType: 'json',
        async: false,
        success: function (data) {
            var stationData = JSON.parse(data);
            $('#cboAssetStation').combobox('loadData', stationData);
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            console.error('获取资产站址列表失败：', errorThrown);
        }
    });
};
```

**动态控件切换：**
```javascript
// 动态更新站址名称控件显示
var updateStationNameControls = function () {
    if (isWorkFlowEnabled) {
        // WorkFlow启用时，显示下拉选择框，隐藏输入框
        $('#txtStationName').hide();
        $('#cboAssetStation').show();
    } else {
        // WorkFlow未启用时，显示输入框，隐藏下拉选择框
        $('#txtStationName').show();
        $('#cboAssetStation').hide();
    }
};
```

**条件校验：**
```javascript
// 根据WorkFlow配置检查站址名称
var stationName = '';
if (isWorkFlowEnabled) {
    // WorkFlow启用时，从下拉选择框获取站址名称
    var selectedStation = $('#cboAssetStation').combobox('getValue');
    if (!selectedStation || selectedStation === '') {
        alertInfo('请选择站址！');
        return false;
    }
    stationName = $('#cboAssetStation').combobox('getText');
} else {
    // WorkFlow未启用时，从输入框获取站址名称
    stationName = $('#txtStationName').val();
    if (stationName === '') {
        alertInfo('站址名称没有输入！');
        return false;
    }
}
```

**条件传值：**
```javascript
// 根据WorkFlow配置获取站址名称
var stationNameValue = '';
if (isWorkFlowEnabled) {
    stationNameValue = $('#cboAssetStation').combobox('getText');
} else {
    stationNameValue = $('#txtStationName').val();
}
```

## 文件修改清单

### 1. 后端文件
- **StationController.cs** - 新增GetAssetStation接口
- **ManagementBSL.cs** - 新增GetAssetStation方法

### 2. 前端文件
- **StationManagement.cshtml** - 添加站址下拉选择框
- **StationManagement.js** - 添加WorkFlow配置逻辑

## 使用说明

### 1. 启用WorkFlow模式
1. 修改 `appsettings.json` 中的 `WorkFlow` 为 `"true"`
2. 重启应用程序
3. 访问站址管理页面，点击"新增站址入网"按钮
4. 在站址名称处会显示下拉选择框，可以选择预存的站址

### 2. 禁用WorkFlow模式
1. 修改 `appsettings.json` 中的 `WorkFlow` 为 `"false"`
2. 重启应用程序
3. 访问站址管理页面，功能恢复到原来的输入框模式

## 数据流程

### WorkFlow = false 模式
1. 用户在输入框中手动输入站址名称
2. 校验时检查输入框是否为空
3. 提交时直接获取输入框的值

### WorkFlow = true 模式
1. 页面加载时从TIr_EomsStation表获取站址列表
2. 用户从下拉框中选择预存的站址
3. 校验时检查是否已选择站址
4. 提交时获取选中项的文本值（EomsSationName）

## 注意事项

1. **配置生效**：修改配置后需要重启应用程序才能生效
2. **向下兼容**：当 `WorkFlow=false` 时，完全保持原有功能不变
3. **数据完整性**：站址下拉框依赖于TIr_EomsStation表中的数据，确保表中有有效数据
4. **性能考虑**：配置和站址列表在页面加载时获取并缓存，避免重复请求
5. **字段映射**：下拉框显示EomsSationName字段，传值时也使用该字段的值

## 测试建议

1. **配置测试**：分别测试 `WorkFlow=true` 和 `WorkFlow=false` 两种配置
2. **数据测试**：确保TIr_EomsStation表中有测试数据
3. **功能测试**：测试新增、编辑、校验等功能在两种模式下都正常工作
4. **兼容性测试**：确保原有功能在 `WorkFlow=false` 时正常工作

## API接口说明

### GetAssetStation
- **URL**: `/CMCC/api/Station/GetAssetStation`
- **方法**: GET
- **返回**: JSON格式的站址列表
- **数据格式**: 
```json
[
    {
        "ItemId": "1001",
        "ItemValue": "站址名称1"
    },
    {
        "ItemId": "1002", 
        "ItemValue": "站址名称2"
    }
]
```
