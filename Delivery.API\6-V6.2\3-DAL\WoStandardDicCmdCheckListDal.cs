﻿using BDSTool.DBUtility.Common;
using Delivery.DAL;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace DM.TestOrder.DAL
{
    public class WoStandardDicCmdCheckListDal
    {
        public static DataTable GetAll()
        {
            var sql = "SELECT EquipmentLogicClassId, EquipmentLogicClass, ControlLogicClass, StandardDicId, ControlStandardName, ExtendFiled1, ifnull(CheckIsMust,'否') AS CheckIsMust FROM TBL_StandardDicControl";
            DataTable tb = new ExecuteSql().ExecuteSQL(sql);
            return tb;
        }


        public static string UpdateOne(int StandardDicId, string IsMust)
        {
            //var rtn = new ExecuteSql().ExecuteStoredProcedureScalar("WO_DicEventCheckList_Update", new QueryParameter[] {
            //    new QueryParameter("CheckId", DataType.Int, CheckId.ToString()),
            //    new QueryParameter("IsMust", DataType.String, IsMust)
            //});
            //return rtn == null ? null : rtn.ToString();

            try
            {
                var sql = string.Format("Update TBL_StandardDicControl set CheckIsMust='{0}' where StandardDicId={1}", IsMust, StandardDicId);
                var rtn = DBHelper.ExecuteScalar(sql);
            }
            catch (Exception ex)
            {
                return null;
            }
            return "OK";
        }
    }
}
