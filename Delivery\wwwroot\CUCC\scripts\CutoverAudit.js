﻿'use strict';
//读取客户端cookie对象
var cookie = getCookie();

var OrderId = getQueryString('orderId'), //割接单的orderId
    UserId = cookie['userinfo']['UserId'],
    UserName,
    OrderState,
    Role, // 系统管理员 or 厂商 or 其他
    CompanyId,
    ClerkId,
    StationId,
    TestItemsChange = [], //测试项更改项
    ArtItemsChange = [], //工艺自查表更改项
    ExpertApproveChange = [],
    Interval;

//加载数据
function loadData() {
    //割接单
    $.ajax({
        type: "GET",
        contentType: "application/json; charset=UTF-8",
        url: '../api/OrderModel?id=' + OrderId,
        async: false,
        success: function (data) {
            if (!data.errormsg) {
                lockControl(1, false);
                rollPolling();

                TestItemsChange = []; //清空测试项表改变行
                ArtItemsChange = []; //清空工艺自查表改变行
                ExpertApproveChange = []; //专家审核改变行

                OrderState = data.OrderState;
                CompanyId = data.SectionOrder.PartOrder.InstallCompanyId;
                ClerkId = data.SectionOrder.PartOrder.InstallClerkId;
                StationId = data.SectionOrder.PartOrder.StationId;

                $('#txtOrderType').textbox('setValue', data.SectionOrder.PartOrder.OrderTypeString);
                $('#txtMyOrderId').textbox('setValue', data.SectionOrder.PartOrder.MyOrderId);
                $('#txtApplyUserName').textbox('setValue', data.SectionOrder.PartOrder.ApplyUserName);
                $('#txtApplyUserFsuVendor').textbox('setValue', data.SectionOrder.PartOrder.ApplyUserFsuVendor);
                $('#txtCenterName').textbox('setValue', data.SectionOrder.PartOrder.CenterName);
                $('#txtGroupName').textbox('setValue', data.SectionOrder.PartOrder.StationGroupName);
                $('#txtStationName').textbox('setValue', data.SectionOrder.PartOrder.StationName);
                $('#txtStationCode').textbox('setValue', data.SectionOrder.PartOrder.SiteCode);
                $('#txtStationCategory').textbox('setValue', data.SectionOrder.PartOrder.StationCategoryName);
                $('#txtLongitude').textbox('setValue', data.SectionOrder.PartOrder.Longitude);
                $('#txtLatitude').textbox('setValue', data.SectionOrder.PartOrder.Latitude);

                $('#txtInstallCompany').textbox('setValue', data.SectionOrder.PartOrder.InstallCompany);
                $('#txtInstaller').textbox('setValue', data.SectionOrder.PartOrder.InstallClerk);

                $('#txtScoreDocDevice').textbox('setValue', data.SectionOrder.PartOrder.ScoreDocDevice);
                $('#txtScoreDocPhoto').textbox('setValue', data.SectionOrder.PartOrder.ScoreDocPhoto);
                $('#txtScoreDocTest').textbox('setValue', data.SectionOrder.PartOrder.ScoreDocTest);
                $('#txtScoreDocArt').textbox('setValue', data.SectionOrder.PartOrder.ScoreDocArt);
                $('#dgExpertApprove').datagrid('loadData', data.SectionExpertApprove.PassResultList);
                $('#dgFlow').datagrid('loadData', data.SectionFlow.FlowList);

                $('#dgArtTestItems').datagrid('loadData', data.SectionOrder.PartArtTest.ItemCheckList);
                $('#dgTestItems').datagrid('loadData', data.SectionOrder.PartTest.ItemCheckList);

                if (!data.SectionOrder.PartOrder.NeedUpload) {
                    $('#dgEquips').datagrid('hideColumn', 'UriProtocol').datagrid('hideColumn', 'UriImage');
                    $('#txtScoreDocDevice').textbox('destroy').hide();
                    $('#txtScoreDocPhoto').textbox('destroy').hide();
                }

                $('#dgEquips').datagrid('loadData', data.SectionOrder.PartOrder.MyEquips);

                $('#txtExpertNote').textbox('setValue', data.SectionExpertApprove.ExpertNote);
                $('#txtExpertDecision').textbox('setValue', data.SectionExpertApprove.ExpertDecision);

                if (data.SectionFinalApprove.FinalGeneralReuslt === '部分通过')
                    $('#rdoPartPass').attr('checked', 'checked').change();
                else
                    $('#rdoAllPass').attr('checked', 'checked').change();
                $('#txtFinalDecision').textbox('setValue', data.SectionFinalApprove.FinalDecision);
                $('#txtFinalNote').textbox('setValue', data.SectionFinalApprove.FinalNote);

                $('#applyAccordion').accordion('select', '站址信息');
                $('#applyAccordion').accordion('select', '设备信息');
                $('#applyAccordion').accordion('select', '工程信息');

                if (OrderState === 2 && Role === '厂商')
                    $('#auditAccordion').accordion('select', '专家组审核');
                else
                    $('#auditAccordion').accordion('unselect', '专家组审核');
                if (OrderState === 3 && Role === '系统管理员')
                    $('#reviewAccordion').accordion('select', '入网复审');
                else
                    $('#reviewAccordion').accordion('unselect', '入网复审');
                $('#applyAccordion').accordion('unselect', '测试项');
                $('#flowAccordion').accordion('unselect', '流程');
            } else {
                $.messager.alert({
                    title: "提示",
                    msg: data.errormsg,
                    icon: 'info',
                    onClose: function () {
                        var tab = parent.$('#tabs').tabs('getSelected');
                        parent.$('#tabs').tabs('close', tab.panel('options').title);
                    }
                });
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
}

//上传文件
function upload(obj) {
    if (window.FormData) {
        $(obj).next().click();
    }
}

//下载文件
function downloadFile(obj) {
    var path = $(obj).attr('data-uri');
    if (!/upload\/.*\.zip/i.test(path)) {
        alertInfo('路径有误！', 'error');
        return;
    }
    var f = $('<form action="../api/EquipFileDownload" method="get" id="fm1" target="_blank" accept-charset="UTF-8"></form>');
    $('<input type="hidden"  name="path"/>').val(path).appendTo(f);
    f.appendTo(document.body).submit().remove();
}

//选中上传文件触发
function change(obj, equipmentId, fileType) {
    lockControl(1, false);
    if (!obj.value) return false;
    if (obj.value.split('.').pop().toUpperCase() !== 'ZIP') {
        alertInfo('上传格式错误，仅允许*.ZIP！', 'error');
        return false;
    }
    if (!window.FormData) {
        var objid = $(obj)[0].id;
        $.ajaxFileUpload({
            url: '../api/EquipFileUpload', //用于文件上传的服务器端请求地址
            secureuri: false, //是否需要安全协议，一般设置为false
            fileElementId: objid, //文件上传域的ID
            dataType: 'json', //返回值类型 一般设置为json
            data: { userid: UserId, OrderId: OrderId, fileType: fileType, equipmentId: equipmentId },
            success: function (data, status)  //服务器成功响应处理函数
            {
                // var data = JSON.parse(text);
                if (data.errormsg === 'OK') {
                    alertInfo("上传成功！");
                    if ($('#' + objid).next().css('display') === 'none') {
                        var arr;
                        if (fileType === 'DOC') {
                            arr = $('#txtScoreDocDevice').textbox('getValue').split('/');
                            $('#txtScoreDocDevice').textbox('setValue', ~~arr[0] + 1 + '/' + arr[1]);
                        } else if (fileType === 'PIC') {
                            arr = $('#txtScoreDocPhoto').textbox('getValue').split('/');
                            $('#txtScoreDocPhoto').textbox('setValue', ~~arr[0] + 1 + '/' + arr[1]);
                        }
                    }
                    $(obj).next().attr('data-uri', data.uri).show();

                }
                else
                    alertInfo(data.errormsg, 'error');
                $(obj).val('');
            },
            error: function (data, status, errorThrown) {
                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                $(obj).val('');
            }
        });
        return false;
    }
    
   
    var fd = new FormData();
    fd.append('UserId', UserId);
    fd.append('OrderId', OrderId);
    fd.append('EquipmentId', equipmentId);
    fd.append('fileType', fileType);
    fd.append('upfile', $(obj).get(0).files[0]);
    $.ajax({
        type: "POST",
        url: '../api/EquipFileUpload',
        data: fd,
        cache: false,
        processData: false,
        contentType: false,
        async: false,
        success: function (data) {
            //var data = JSON.parse(text);
            if (data.errormsg === 'OK') {
                alertInfo("上传成功!");
                if ($(obj).next().css('display') === 'none') {
                    var arr;
                    if (fileType === 'DOC') {
                        arr = $('#txtScoreDocDevice').textbox('getValue').split('/');
                        $('#txtScoreDocDevice').textbox('setValue', ~~arr[0] + 1 + '/' + arr[1]);
                    } else if (fileType === 'PIC') {
                        arr = $('#txtScoreDocPhoto').textbox('getValue').split('/');
                        $('#txtScoreDocPhoto').textbox('setValue', ~~arr[0] + 1 + '/' + arr[1]);
                    }
                }
                $(obj).next().attr('data-uri', data.uri).show();
                
            }
            else
                alertInfo(data.errormsg, 'error');
            $(obj).val('');
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
            $(obj).val('');
        }
    });
}

//初始化datagrid
function initDatagrid() {
    //设备信息
    $('#dgEquips').datagrid({
        singleSelect: true,
        rownumbers: true,
        fitColumns: true,
        columns: [[
            { field: 'EquipmentName', title: '设备名称', width: 200, align: 'left' },
            { field: 'EquipmentCategory', title: '设备类型', width: 150, align: 'left' },
            { field: 'Vendor', title: '设备厂家', width: 150, align: 'left' },
            { field: 'EquipmentStyle', title: '设备型号', width: 200, align: 'left' },
            { field: 'UriProtocol', title: '设备协议', width: 200, align: 'left', formatter: fileProtocol },
            { field: 'UriImage', title: '水印照片', width: 200, align: 'left', formatter: fileImage }
        ]]
    });

    //测试项表
    $('#dgTestItems').datagrid({
        rownumbers: true,
        pagination: true,
        pageSize: 10,
        loadFilter: pagerFilter,
        singleSelect: true,
        headerCls: 'txtCenter',
        columns: [[
            { field: 'EquipmentName', title: '设备名称', width: 200, align: 'left' },
            { field: 'EquipmentCategoryName', title: '设备类型', width: 150, align: 'left' },
            { field: 'BaseEquipmentName', title: '基类设备类型', width: 150, align: 'left' },
            { field: 'CheckType', title: '基类类别', width: 100, align: 'left' },
            { field: 'BaseTypeName', title: '基类名称', width: 150, align: 'left' },
            { field: 'LimitDown', title: '标准下限', width: 100, align: 'left' },
            { field: 'LimitUp', title: '标准上限', width: 100, align: 'left' },
            { field: 'BaseEquipmentName', title: '逻辑分类', width: 100, align: 'left' },
            { field: 'StandardName', title: '标准名', width: 150, align: 'left' },
            { field: 'SignalType', title: '信号类型', width: 100, align: 'left' },
            { field: 'Unit', title: '单位', width: 100, align: 'left' },
            {
                field: 'IsPass', title: '测试结果', width: 60, align: 'left',
                formatter: function (value, row, index) { return value === undefined ? value : value ? '通过' : '未通过'; }
            },
            { field: 'PassNote', title: '测试结果描述', width: 150, align: 'left' },
            { field: 'PassFailReason', title: '未测试通过原因', width: 150, align: 'left', formatter: textboxTest }
        ]]
    });    

    //工艺自查表
    $('#dgArtTestItems').datagrid({
        singleSelect: true,
        headerCls: 'txtCenter',
        rownumbers: true,
        fitColumns: true,
        columns: [[
            { field: 'CheckDicNote', title: '检查项', width: 250, align: 'left' },
            { field: 'IsPass', title: '自查结果', width: 150, align: 'left', formatter: checkboxArtTest }
        ]]
    });

    //专家组审核表
    $('#dgExpertApprove').datagrid({
        rownumbers: true,
        singleSelect: true,
        fitColumns: true,
        columns: [[
            { field: 'CheckDicNote', title: '测试项', width: 250, align: 'left' },
            { field: 'IsPass', title: '通过与否', width: 150, align: 'left', formatter: checkboxExpert },
            { field: 'PassNote', title: '备注', width: 150, align: 'left', formatter: textboxExpert }
        ]]
    });

    //流程表
    $('#dgFlow').datagrid({
        rownumbers: true,
        singleSelect: true,
        fitColumns: true,
        columns: [[
            { field: 'StateSetUserName', title: '操作人', width: 100, align: 'left' },
            {
                field: 'Action', title: '操作动作', width: 100, align: 'left',
                formatter: function (value, row, index) {
                    var action = '';
                    switch (row.OldOrderState) {
                        case 0: action = '新建割接单'; break;
                        case 1: action = '入网申请'; break;
                        case 2: action = '专家组审核'; break;
                        case 3: action = '入网复审'; break;
                    }
                    return action;
                }
            },
            {
                field: 'IsApprove', title: '操作结果', width: 80, align: 'left',
                formatter: function (value, row, index) {
                    if (value === 1) return '通过';
                    else return '退回';
                }
            },
            { field: 'SaveTime', title: '操作日期', width: 250, align: 'left' }
        ]]
    });

    //安装公司
    $('#dgInstallCompanyGrid').datagrid({
        title: '',
        singleSelect: true,
        width: function () { return document.body.clientWidth * 0.9; },
        height: 'auto',
        fitColumns: true,
        rownumbers: true,
        checkOnSelect: true,
        columns: [[
            { field: 'ck', title: '', checkbox: true, vawidth: 30 },
			{ field: 'CompanyId', width: 100, hidden: true },
			{ field: 'CompanyName', title: '公司名称', width: 100 }
        ]],
        onDblClickRow: function (index, row) {
            $('#txtInstallCompany').textbox('setValue', row.CompanyName);//.textbox('textbox').keyup();
            CompanyId = row.CompanyId;
            $('#InstallCompanyDialog').dialog('close');
        }
    });

    //安装人员
    $('#dgInstallerGrid').datagrid({
        title: '',
        singleSelect: true,
        width: function () { return document.body.clientWidth * 0.9; },
        height: 'auto',
        fitColumns: true,
        rownumbers: true,
        checkOnSelect: true,
        columns: [[
            { field: 'ck', title: '', checkbox: true, vawidth: 30 },
			{ field: 'CompanyId', hidden: true },
			{ field: 'CompanyName', title: '公司名称', width: 100 },
			{ field: 'ClerkId', hidden: true },
			{ field: 'ClerkName', title: '人员名称', width: 100 }
        ]],
        onDblClickRow: function (index, row) {
            ClerkId = row.ClerkId;
            $('#txtInstaller').textbox('setValue', row.ClerkName);
            $('#InstallerDialog').dialog('close');
        }
    });
}

//安装公司对话窗按钮事件
function installCompanyClick(event) {
    switch (event.data.action) {
        case 'query':
            if (checkTextBoxLength('txtCompanyNameC')) {
                $.ajax({
                    type: "GET",
                    contentType: "application/json; charset=UTF-8",
                    url: '../api/InstallCompany?keyword=' + encodeURI($("#txtCompanyNameC").val()),
                    success: function (data) {
                        $('#dgInstallCompanyGrid').datagrid('loadData', data);
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                    }
                });
            }
            break;
        case 'reset':
            $('#txtCompanyNameC').textbox('setValue', '');
            break;
        case 'select':
            ClerkId = null;
            $('#txtInstaller').textbox('setValue', '');
            var row = $('#dgInstallCompanyGrid').datagrid('getChecked');
            if (row && row.length > 0) {
                $('#txtInstallCompany').textbox('setValue', row[0].CompanyName);
                CompanyId = row[0].CompanyId;
                $('#installCompanyDialog').dialog('close');
            } else
                alertInfo("请选择安装公司！", 'error');
            break;
        case 'add':
            if (!$("#txtCompanyNameC").val()) {
                alertInfo("请先输入安装公司名称!", 'error');
                return;
            }
            if (checkTextBoxLength('txtCompanyNameC')) {
                $.ajax({
                    type: 'POST',
                    contentType: 'application/json; charset=UTF-8',
                    url: '../api/NewCompany',
                    data: JSON.stringify({ 'CompanyName': $('#txtCompanyNameC').val() }),

                    success: function (data) {
                        if (data.errormsg === 'OK')
                            $('#btnInstallCompanySeach').click();
                        else 
                            alertInfo('录入安装公司失败！', 'error');
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                    }
                });
            }
            break;
        default:
            break;
    }
}

//安装人员对话窗按钮事件
function installClerkClick(event) {
    switch (event.data.action) {
        case 'query':
            if (checkTextBoxLength('txtInstallerName')) {
                $.ajax({
                    type: "GET",
                    contentType: "application/json; charset=UTF-8",
                    url: '../api/InstallClerk?CompanyId=' + CompanyId + "&kwClerk=" + encodeURI($("#txtInstallerName").val()),
                    success: function (data) {
                        $('#dgInstallerGrid').datagrid('loadData', data);
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                    }
                });
            }
            break;
        case 'reset':
            $('#txtInstallerName').textbox('setValue', '');
            break;
        case 'select':
            var row = $('#dgInstallerGrid').datagrid('getChecked');
            if (row && row.length > 0) {
                ClerkId = row[0].ClerkId;
                $('#txtInstaller').textbox('setValue', row[0].ClerkName);
                $('#installerDialog').dialog('close');
            } else
                alertInfo("请选择安装人员!", 'error');
            break;
        case 'add':
            if (!CompanyId) {
                alertInfo("请先选择安装公司!", 'error');
                return;
            }
            if (!$("#txtInstallerName").val()) {
                alertInfo("请先输入安装人员名称!", 'error');
                return;
            }
            if (checkTextBoxLength('txtInstallerName')) {
                $.ajax({
                    type: 'POST',
                    contentType: 'application/json; charset=UTF-8',
                    url: '../api/NewClerk',
                    data: JSON.stringify({ 'CompanyId': CompanyId, 'ClerkName': $('#txtInstallerName').val()}),
                    success: function (data) {
                        if (data.errormsg === 'OK') {
                            alertInfo('录入安装人员成功！');
                            $('#btnInstallerSeach').click();
                        }
                        else {
                            alertInfo('录入安装人员失败！', 'error');
                        }
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                    }
                });
            }
            break;
        default:
            break;
    }
}

//测试项表未通过原因文本框改变事件
function getTestItemsChange(obj) {
    var row = $('#dgTestItems').datagrid('getRows')[obj.getAttribute('data-index')];
    row.IsPass = ~~obj.checked;

    row.PassFailReason = obj.value;
    for (var i = 0; i < TestItemsChange.length; i++) {
        if (TestItemsChange[i].OrderCheckId === row.OrderCheckId) {
            TestItemsChange[i].PassFailReason = row.PassFailReason;
            return;
        }
    }
    TestItemsChange.push(row);
}

//工艺自查表复选框改变事件
function getArtItemsChange(obj) {
    var row = $('#dgArtTestItems').datagrid('getRows')[obj.getAttribute('data-index')];
    row.IsPass = ~~obj.checked;
    var arr = $('#txtScoreDocArt').textbox('getValue').split('/');
    $('#txtScoreDocArt').textbox('setValue', ~~arr[0] + (~~row.IsPass ? 1 : -1) + '/' + arr[1]);
    for (var i = 0; i < ArtItemsChange.length; i++) {
        if (ArtItemsChange[i].OrderCheckId === row.OrderCheckId) {
            ArtItemsChange.splice(i, 1, row);
            return;
        }
    }
    ArtItemsChange.push(row);
}

//专家复审备注文本框改变事件
function getExpertApproveChange(obj) {
    var row = $('#dgExpertApprove').datagrid('getRows')[obj.getAttribute('data-index')];
    if (obj.type === 'checkbox') {
        row.IsPass = ~~obj.checked;
        for (var i = 0; i < ExpertApproveChange.length; i++) {
            if (ExpertApproveChange[i].OrderCheckId === row.OrderCheckId) {
                ExpertApproveChange[i].IsPass = row.IsPass;
                return;
            }
        }
    }
    else if (obj.type === 'text') {
        row.PassNote = obj.value;
        for (var j = 0; j < ExpertApproveChange.length; j++) {
            if (ExpertApproveChange[j].OrderCheckId === row.OrderCheckId) {
                ExpertApproveChange[j].PassNote = row.PassNote;
                return;
            }
        }
    }
    ExpertApproveChange.push(row);
}

//根据权限和割接单状态初始化文本框和按钮
function initInputControl() {
    //入网申请
    $('#txtLongitude').textbox('readonly', true);
    $('#txtLatitude').textbox('readonly', true);
    $('#txtInstallCompany').textbox('readonly', true);
    $('#txtInstaller').textbox('readonly', true);
    $('#btnInstallCompanySeach').on('click', { action: 'query' }, installCompanyClick);
    $('#btnInstallCompanyReset').on('click', { action: 'reset' }, installCompanyClick);
    $('#btnInstallCompanyAdd').on('click', { action: 'add' }, installCompanyClick);
    $('#btnInstallCompanyChoose').on('click', { action: 'select' }, installCompanyClick);
    $('#btnInstallerSeach').on('click', { action: 'query' }, installClerkClick);
    $('#btnInstallerReset').on('click', { action: 'reset' }, installClerkClick);
    $('#btnInstallerAdd').on('click', { action: 'add' }, installClerkClick);
    $('#btnInstallerChoose').on('click', { action: 'select' }, installClerkClick);
    $('#btnSubmit').on('click', submitForAudit).hide();
    $('#btnRefresh').on('click', refreshTestItems).hide();
    //专家组审核
    $('#txtExpertNote').textbox('readonly', true);
    $('#txtExpertDecision').textbox('readonly', true);
    $('#btnExpertPass').on('click', { status: 1 }, expertAudit).hide();
    $('#btnExpertReject').on('click', { status: 0 }, expertAudit).hide();
    //入网复审
    $('#rdoAllPass').attr('disabled', 'disabled');
    $('#rdoPartPass').attr('disabled', 'disabled');
    $('#txtFinalNote').textbox('readonly', true);
    $('#txtFinalDecision').textbox('readonly', true);
    $('#btnFinalPass').on('click', { status: 1 }, finalAudit).hide();
    $('#btnFinalReject').on('click', { status: 0 }, finalAudit).hide();

     //厂商：入网申请、专家组审核
    if (OrderState === 1 && Role === '厂商') {
        $('#txtLongitude').textbox('readonly', false);
        $('#txtLatitude').textbox('readonly', false);
        $('#txtInstallCompany').textbox('readonly', false).textbox('textbox').attr('readonly', true);
        $('#txtInstaller').textbox('readonly', false).textbox('textbox').attr('readonly', true);
        $('#btnSubmit').show();
        $('#btnRefresh').show();
    }
    if (OrderState === 2 && Role === '厂商') {
        $('#txtExpertNote').textbox('readonly', false);
        $('#txtExpertDecision').textbox('readonly', false);
        $('#btnExpertPass').show();
        $('#btnExpertReject').show();
    }

    //系统管理员：入网复审
    if (Role === '系统管理员' && OrderState === 3) {
        $('#rdoAllPass').removeAttr('disabled');
        $('#rdoPartPass').removeAttr('disabled');
        $('#txtFinalNote').textbox('readonly', false);
        $('#txtFinalDecision').textbox('readonly', false);
        $('#btnFinalPass').show();
        $('#btnFinalReject').show();
    }
}

//工艺自查表是否符合规范勾选框html字串
function checkboxArtTest(value, row, index) {
    if (Role === '厂商' && OrderState === 1)
        return '<input  type="checkbox" ' + (value ? 'checked="checked"' : '') + ' data-index="' + index + '"  onchange="getArtItemsChange(this);"/>符合规范';
    else
        return value ? '符合规范' : '不符合规范';
}

//专家组审核是否通过勾选框html字串
function checkboxExpert(value, row, index) {
    if (Role === '厂商' && OrderState === 2)
        return '<input  type="checkbox" ' + (value ? 'checked="checked"' : '') + ' data-index="' + index + '"  onchange="getExpertApproveChange(this);"/>通过';
    else
        return value ? '通过' : '不通过';
}

//专家组审核备注输入框html字串
function textboxExpert(value, row, index) {
    if (Role === '厂商' && OrderState === 2) {
        var html = '<input type="text" style="width:100%" maxlength="255" data-index="' + index + '" class="easyui-validatebox" value="'
            + value + '" onchange="getExpertApproveChange(this);" />';
        return html;
    } else
        return value;
}

//测试项表备注输入框html字串
function textboxTest(value, row, index) {
    if (Role === '厂商' && OrderState === 1) {
        var sHtml = '<input type="text" style="width:100%" maxlength="255" data-index="' + index + '" class="easyui-validatebox" value="'
            + value + '" onchange="getTestItemsChange(this);" />';
        return sHtml;
    } else
        return value;
}

//设备信息表设备协议html字串
function fileProtocol(value, row, index) {
    if (Role === '厂商' && OrderState === 1)
        return value ? '<input type="button" value="上传" onclick="upload(this)" /><input class="upload" type="file" id="upfile_' + row.EquipmentId + '" name="upfile_' + row.EquipmentId + '" accept=".zip" onchange="change(this,' + row.EquipmentId + ',\'DOC\')" /><a href="javascript:void(0);" data-uri="' + value + '" onclick="downloadFile(this)">协议</a>'
            : '<input type="button" value="上传" onclick="upload(this)" /><input class="upload" type="file" accept=".zip" id="upfile_' + row.EquipmentId + '" name="upfile" onchange="change(this,' + row.EquipmentId + ',\'DOC\')" /><a href="javascript:void(0);" style="display:none" onclick="downloadFile(this)">协议</a>';
    else
        return value ? '<a href="javascript:void(0);" data-uri="' + value + '" onclick="downloadFile(this)">协议</a>' : '<a href="javascript:void(0);" style="display:none" onclick="downloadFile(this)">协议</a>';
}

//设备信息表水印照片html字串
function fileImage(value, row, index) {
    if (Role === '厂商' && OrderState === 1)
        return value ? '<input type="button" value="上传" onclick="upload(this)" /><input class="upload" type="file" id="uppic_' + row.EquipmentId + '" name="uppic_' + row.EquipmentId + '" accept=".zip"  onchange="change(this,' + row.EquipmentId + ',\'PIC\')" /><a href="javascript:void(0);" data-uri="' + value + '" onclick="downloadFile(this)">照片</a>'
            : '<input type="button" value="上传" onclick="upload(this)" /><input class="upload" type="file" id="uppic_' + row.EquipmentId + '" name="uppic_' + row.EquipmentId + '" accept=".zip"  onchange="change(this,' + row.EquipmentId + ',\'PIC\')" /><a href="javascript:void(0);" style="display:none" onclick="downloadFile(this)">照片</a>';
    else
        return value ? '<a href="javascript:void(0);" data-uri="' + value + '" onclick="downloadFile(this)">照片</a>' : '<a href="javascript:void(0);" style="display:none" onclick="downloadFile(this)">照片</a>';
}

//提交审核按钮事件
function submitForAudit() {
    lockControl(1, false);
    if (!$("#txtInstallCompany").val()) {
        alertInfo("您还没选择安装公司，不能提交审核!", 'error');
        return;
    }

    if (!$("#txtInstaller").val()) {
        alertInfo("提示", "您还没选择安装人员，不能提交审核!", 'error');
        return;
    }
    $.messager.confirm("确认", "您确认提交审核吗？",
                function (r) {
                    if (r) {
                        $.ajax({
                            type: "GET",
                            url: '../api/Geo',
                            data: 'latitude=' + $("#txtLatitude").numberbox("getValue") + '&longitude=' + $("#txtLongitude").numberbox("getValue"),
                            async: false,
                            success: function (data) {
                                if (data.errormsg !== 'OK') {
                                    if (data.errormsg === "invalid")
                                        alertInfo("经纬度不合理！", 'error');
                                    else
                                        alertInfo("提交时出现错误！", 'error');
                                    return false;
                                }

                                var paras = {};
                                paras.PartOrder = {
                                    OrderId: OrderId,
                                    InstallClerk: $('#txtInstaller').numberbox('getValue'),
                                    InstallClerkId: ClerkId,
                                    InstallCompany: $('#txtInstallCompany').numberbox('getValue'),
                                    InstallCompanyId: CompanyId,
                                    Latitude: $('#txtLatitude').numberbox('getValue'),
                                    Longitude: $('#txtLongitude').numberbox('getValue'),
                                    ScoreDocArt: $('#txtScoreDocArt').val(),
                                    ScoreDocDevice: $('#txtScoreDocDevice').val(),
                                    ScoreDocPhoto: $('#txtScoreDocPhoto').val(),
                                    ScoreDocTest: $('#txtScoreDocTest').val(),
                                    StationId: StationId,
                                    MyEquips: $('#dgEquips').datagrid('getData').rows
                                };
                                paras.PartTest = { ItemCheckList: TestItemsChange };
                                paras.PartArtTest = { ItemCheckList: ArtItemsChange };

                                $.ajax({
                                    type: "POST",
                                    contentType: "application/json; charset=UTF-8",
                                    url: '../api/SectionOrder',
                                    data: JSON.stringify(paras),
                                    async: false,
                                    success: function (result) {
                                        //var result = JSON.parse(data);
                                        if (result.errormsg !== 'OK') {
                                            alertInfo("提交修改出错！", 'error');
                                            return false;
                                        }
                                        $.ajax({
                                            type: "POST",
                                            contentType: "application/json; charset=UTF-8",
                                            url: '../api/SubmitOnlineApply',
                                            data: JSON.stringify({ OrderId: OrderId, UserId: UserId }),
                                            async: false,
                                            success: function (data) {
                                                if (data.errormsg !== 'OK') {
                                                    alertInfo("提交审核出错！", 'error');
                                                    return false;
                                                }
                                                updateNeedApproveOrder();
                                                $.messager.alert({
                                                    title: "提示",
                                                    msg: "提交审核成功!",
                                                    icon: 'info',
                                                    onClose: function () {
                                                        var tab = parent.$('#tabs').tabs('getSelected');
                                                        if (parent.$('#tabs').tabs('exists', '综合查询'))
                                                            parent.$('#tabs').tabs('select', '综合查询').tabs('getTab', '综合查询').find('iframe').eq(0).contents().find('#btnSeach').click();
                                                        else
                                                            parent.$("#ComprehensiveQuery").click();
                                                        parent.$('#tabs').tabs('close', tab.panel('options').title);
                                                    }
                                                });
                                            },
                                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                                            }
                                        });
                                    },
                                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                                        alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                                    }
                                });
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                            }
                        });
                    }
                });

    
}

//刷新测试项按钮事件
function refreshTestItems() {
    lockControl(1, false);
    $.messager.confirm("提示", "刷新测试项前，请先在SiteWeb设备监控页面验证相关信号、事件、控制数据！",
                function (r) {
                    if (r) {
                        $.ajax({
                            type: "GET",
                            contentType: "application/json; charset=UTF-8",
                            url: '../api/EquipItemCheckList?id=' + OrderId,
                            success: function (data) {
                                var passCount = 0;
                                var changeCount = TestItemsChange.length;
                                for (var i = 0; i < data.ItemCheckList.length; i++) {
                                    if (data.ItemCheckList[i].IsPass)
                                        passCount++;
                                    if (changeCount) {
                                        for (var j = 0; j < TestItemsChange.length; j++) {
                                            if (TestItemsChange[j].OrderCheckId === data.ItemCheckList[i].OrderCheckId) {
                                                data.ItemCheckList[i].PassFailReason = TestItemsChange[j].PassFailReason;
                                                changeCount--;
                                                break;
                                            }
                                        }
                                    }
                                }
                                $('#txtScoreDocTest').textbox('setValue', passCount + '/' + data.ItemCheckList.length);
                                $('#dgTestItems').datagrid('loadData', data.ItemCheckList).datagrid('gotoPage', 1);
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                            }
                        });
                    }
                });
}

//专家组审核通过/退回按钮事件
function expertAudit(event) {
    lockControl(1, false);
    var status = event.data.status;
    if (!$('#txtExpertDecision').val()) {
        alertInfo('审核意见不允许为空！', 'error');
        return false;
    }
    if (checkTextBoxLength('txtExpertDecision') && checkTextBoxLength('txtExpertNote')) {
        $.messager.confirm("确认", "您确认" + (status ? "通过" : "退回") + "吗？",
                function (r) {
                    if (r) {
                        var paras = {
                            OrderId: OrderId,
                            StateSetUserId: UserId,
                            StateSetUserName: UserName,
                            ExpertNote: $('#txtExpertNote').textbox('getValue'),
                            ExpertDecision: $('#txtExpertDecision').textbox('getValue'),
                            ExpertIsApprove: status,
                            PassResultList: ExpertApproveChange
                        };
                        $.ajax({
                            type: "POST",
                            contentType: "application/json; charset=UTF-8",
                            url: '../api/SubmitExpertDecision',
                            data: JSON.stringify(paras),
                            async: false,
                            success: function (data) {
                                if (data.errormsg !== 'OK') {
                                    alertInfo("提交出错！", 'error');
                                    return false;
                                }
                                updateNeedApproveOrder();
                                $.messager.alert({
                                    title: "提示",
                                    msg: (status ? "专家组审核通过" : "专家组审核退回") + "成功!",
                                    icon: 'info',
                                    onClose: function () {
                                        var tab = parent.$('#tabs').tabs('getSelected');
                                        if (parent.$('#tabs').tabs('exists', '综合查询'))
                                            parent.$('#tabs').tabs('select', '综合查询').tabs('getTab', '综合查询').find('iframe').eq(0).contents().find('#btnSeach').click();
                                        else
                                            parent.$("#ComprehensiveQuery").click();
                                        parent.$('#tabs').tabs('close', tab.panel('options').title);
                                    }
                                });
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                            }
                        });
                    }
                });
    }
}

//入网复审通过/退回按钮事件
function finalAudit(event) {
    lockControl(1, false);
    var status = event.data.status;
    if (!$('#txtFinalDecision').val()) {
        alertInfo('审核意见不允许为空！', 'error');
        return false;
    }
    if (checkTextBoxLength('txtFinalDecision') && checkTextBoxLength('txtFinalNote')) {
        $.messager.confirm("确认", "您确认" + (status ? "通过" : "退回") + "吗？",
                function (r) {
                    if (r) {
                        var paras = {
                            OrderId: OrderId,
                            StateSetUserId: UserId,
                            StateSetUserName: UserName,
                            FinalGeneralReuslt: $('#rdoAllPass')[0].checked ? $('#rdoAllPass').val() : $('#rdoPartPass').val(),
                            FinalDecision: $('#txtFinalDecision').textbox('getValue'),
                            FinalNote: $('#txtFinalNote').textbox('getValue'),
                            FinalIsApprove: status
                        };
                        $.ajax({
                            type: "POST",
                            contentType: "application/json; charset=UTF-8",
                            url: '../api/SubmitFinalDecision',
                            data: JSON.stringify(paras),
                            async: false,
                            success: function (data) {
                                if (data.errormsg !== 'OK') {
                                    alertInfo("提交出错！", 'error');
                                    return false;
                                }
                                updateNeedApproveOrder();
                                $.messager.alert({
                                    title: "提示",
                                    msg: (status ? "入网复审通过" : "入网复审退回") + "成功!",
                                    icon: 'info',
                                    onClose: function () {
                                        var tab = parent.$('#tabs').tabs('getSelected');
                                        if (parent.$('#tabs').tabs('exists', '综合查询'))
                                            parent.$('#tabs').tabs('select', '综合查询').tabs('getTab', '综合查询').find('iframe').eq(0).contents().find('#btnSeach').click();
                                        else
                                            parent.$("#ComprehensiveQuery").click();
                                        parent.$('#tabs').tabs('close', tab.panel('options').title);
                                    }
                                });
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                            }
                        });
                    }
                });
    }
}

//检验easyui-textbox长度是否有效
function checkTextBoxLength(id) {
    if (!$('#' + id).textbox('isValid')) {
        alertInfo('请输入正确长度！', 'error');
        return false;
    }
    return true;
}


//根据userid获取用户相关信息
function getUserInfo() {
    $.ajax({
        type: "GET",
        url: '../api/Account?userid=' + UserId,
        dataType: 'json',
        async: false,
        success: function (data) {
            if (data && data.length > 0) {
                UserName = data[0].UserName;
                Role = data[0].RoleTypeName;
            }
            else
                alertInfo('无权限！', 'error');
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
}


//轮询占用当前割接单
function rollPolling() {
    Interval = setInterval(function () {
        lockControl(1, true);
    }, 60000);
}

//请求加锁解锁
function lockControl(islock, async) {
    $.ajax({
        type: "POST",
        contentType: "application/json; charset=UTF-8",
        url: '../api/OrderLock',
        data: JSON.stringify({ orderid: OrderId, userid: UserId, islock: islock }),
        async: async,
        success: function (data) {
            if (data.errormsg !== 'OK') {
                $.messager.alert({
                    title: "提示",
                    msg: "无法" + (islock ? "锁定" : "解锁") + "割接单!",
                    icon: 'error',
                    onClose: function () {
                        var tab = parent.$('#tabs').tabs('getSelected');
                        if (parent.$('#tabs').tabs('exists', '综合查询'))
                            parent.$('#tabs').tabs('select', '综合查询').tabs('getTab', '综合查询').find('iframe').eq(0).contents().find('#btnSeach').click();
                        else
                            parent.$("#ComprehensiveQuery").click();
                        parent.$('#tabs').tabs('close', tab.panel('options').title);
                    }
                });
                //$.messager.alert("提示", "无法" + islock ? "独占" : "解锁" + "割接单!", 'error');
                return false;
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
}

//关闭前取消割接单占用锁
var clearLock = function () {
    if (Interval) {
        clearInterval(Interval);
        lockControl(0, false);
    }
};

window.onbeforeunload = clearLock;

$(function () {
    getUserInfo();
    initDatagrid();

    loadData();
    initInputControl();
    $('div.mask').remove();
});
