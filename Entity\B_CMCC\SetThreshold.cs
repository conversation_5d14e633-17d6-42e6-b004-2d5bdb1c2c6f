﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.6.6.写监控点门限数据
    /// </summary>
    public sealed class SetThreshold:BMessage
    {
        public List<DeviceThreshold> Values { get; private set; }

        /// <summary>
        /// 写监控点门限数据
        /// </summary>
        /// <param name="fsuId">FSUID</param>
        /// <param name="deviceThresholdList">设备监控点门限列表</param>
        public SetThreshold(string fsuId, List<DeviceThreshold> deviceThresholdList):base()
        {
            MessageType = (int)BMessageType.SET_THRESHOLD;

            FSUID = fsuId;
            Values = deviceThresholdList;
        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmldoc = CreateXmlDocument();
                AppendPK_Type(xmldoc, "Request", BMessageType.SET_THRESHOLD.ToString());

                XmlElement xe2 = xmldoc.CreateElement("Info");
                XmlElement xe21 = xmldoc.CreateElement("FSUID");
                xe21.InnerText = FSUID;
                xe2.AppendChild(xe21);

                XmlElement xe22 = xmldoc.CreateElement("Value");
                XmlElement xe221 = xmldoc.CreateElement("DeviceList");
                foreach (DeviceThreshold device in Values)
                {
                    XmlElement xe2211 = xmldoc.CreateElement("Device");
                    xe2211.SetAttribute("ID", device.DeviceID);
                    foreach (TThreshold threshold in device.Thresholds)
                    {
                        XmlElement xe22111 = xmldoc.CreateElement("TThreshold");
                        xe22111.SetAttribute("Type", ((int)threshold.Type).ToString());
                        xe22111.SetAttribute("ID", threshold.ID);
                        xe22111.SetAttribute("SignalNumber", threshold.SignalNumber);
                        xe22111.SetAttribute("Threshold", threshold.Threshold.ToString());
                        //xe22111.SetAttribute("AbsoluteVal", threshold.AbsoluteVal.ToString());
                        //xe22111.SetAttribute("RelativeVal", threshold.RelativeVal.ToString());
                        xe22111.SetAttribute("AlarmLevel", ((int)threshold.AlarmLevel).ToString());
                        xe22111.SetAttribute("NMAlarmID", threshold.NMAlarmID);
                        xe2211.AppendChild(xe22111);
                    }
                    xe221.AppendChild(xe2211);
                }
                xe22.AppendChild(xe221);
                xe2.AppendChild(xe22);

                XmlNode root = xmldoc.SelectSingleNode("Request");
                root.AppendChild(xe2);

                StringXML = xmldoc.InnerXml;
                entityLogger.DebugFormat("SetThreshold.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmldoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetThreshold.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetThreshold.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}, {2}: ",
                MessageId, (BMessageType)MessageType, FSUID);
            foreach (DeviceThreshold value in Values)
            {
                sb.AppendFormat("{0}", value.ToString());
            }
            return sb.ToString();
        }

    }
}
