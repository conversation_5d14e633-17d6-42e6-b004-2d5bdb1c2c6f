﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <procedures>
    <procedure owner="" name="BM_GetFSUMainConfig" grant="">
      <parameters>
        <parameter name="FSUID" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
           SELECT c.FSUID,p.PortId,s.SamplerUnitId ,FSUName, SiteID, RoomID, c.StationId, c.MonitorUnitId, UserName, PassWord, 
           FSUIP, FSUMAC, FSUVER, Result, FailureCause, CPUUsage, MEMUsage, GetFSUInfoResult, GetFSUFaliureCause, GetFSUTime, 
           FTPUserName, FTPPassWord, ExtendField1, ExtendField2 
	         FROM TSL_MonitorUnitCMCC c
	         inner join TSL_MonitorUnit m on  c.MonitorUnitId=m.MonitorUnitId and m.MonitorUnitCategory=10 
	         inner join TBL_Station st on c.StationId=st.StationId
	         LEFT JOIN TSL_Port p ON c.MonitorUnitId=p.MonitorUnitId AND PortName='COM1'
	         LEFT JOIN TSL_SamplerUnit s ON c.MonitorUnitId=s.MonitorUnitId AND s.PortId=p.PortId And  DllPath='KoloBusinessServer.exe'
	         WHERE c.FSUId =@FSUID;
         ]]>
      </body>
    </procedure>

    <procedure owner="" name="BM_GetTSignalList_getTemplateId" grant="">
      <parameters>
        <parameter name="StationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EquipmentId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
           SELECT EquipmentTemplateId FROM TBL_Equipment a WHERE a.StationId=@StationId and a.EquipmentId=@EquipmentId;
         ]]>
      </body>
    </procedure>
    <procedure owner="" name="BM_GetTSignalList_insertTSignal" grant="">
      <parameters>
        <parameter name="EquipmentTemplateId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
           SELECT 
	         CASE WHEN sig.SignalCategory=1 THEN 3 
	         WHEN sig.SignalCategory=2 THEN 4 
	         END AS Type,
	         RIGHT('000000' || CAST(FLOOR(SignalId / 1000) AS TEXT), 6) ID,
	         RIGHT(CAST(sig.SignalId AS TEXT),3) SignalNumber,
	         SignalName, 
	         null AlarmLevel ,
	         null Threshold,  
	         CASE WHEN sig.AbsValueThreshold =0 OR sig.AbsValueThreshold IS NULL  THEN null 
		       else sig.AbsValueThreshold  
           END AbsoluteVal, 
	         CASE sig.PercentThreshold
		       WHEN sig.PercentThreshold =0 OR sig.PercentThreshold IS  NULL  THEN null 
		       else sig.PercentThreshold 
	         END RelativeVal, 
	         CASE WHEN POSITION('&' IN Description) <1 or POSITION('&' IN Description)=length(Description) THEN null
           else RIGHT(Description, LENGTH(Description) - POSITION('&' IN Description))
           END Describe,     
	         CASE WHEN POSITION('&' IN Description) = 1 then null
           WHEN POSITION('&' IN Description) = 0 then Description
           else LEFT(Description, POSITION('&' IN Description) - 1)
           END NMAlarmID
	         ,'1Signal' SiteWebType,sig.SignalId SiteWebId
           FROM TBL_Signal sig
	         WHERE sig.EquipmentTemplateId=CAST(@EquipmentTemplateId AS INTEGER)
           UNION ALL 
           SELECT 
	         CASE WHEN control.CommandType=1 THEN 2 
		      WHEN control.CommandType=2 THEN 1 
	        END AS Type,
	        RIGHT('000000' || CAST(FLOOR(ControlId / 1000) AS TEXT), 6) ID,
	        RIGHT(CAST(control.ControlId AS TEXT),3) SignalNumber,
	        ControlName SignalName, 
	        null AlarmLevel ,
	        null Threshold,  
	        null AbsoluteVal, 
	        null RelativeVal, 
	        CASE WHEN POSITION('&' IN Description) <1 or POSITION('&' IN Description)=length(Description) THEN null
           else RIGHT(Description, LENGTH(Description) - POSITION('&' IN Description))
           END Describe,     
	         CASE WHEN POSITION('&' IN Description) = 1 then null
           WHEN POSITION('&' IN Description) = 0 then Description
           else LEFT(Description, POSITION('&' IN Description) - 1)
           END NMAlarmID  
	         ,'2Control' SiteWebType,control.ControlId SiteWebId
           FROM TBL_Control control
	         WHERE Control.EquipmentTemplateId=CAST(@EquipmentTemplateId AS INTEGER)
           UNION ALL 
           SELECT 
	        0  Type, 
          RIGHT('000000' || CAST(FLOOR(event.EventID / 1000) AS TEXT), 6) ID,
	        RIGHT(CAST(event.EventId AS TEXT),3) SignalNumber,
	        EventName SignalName, 
	        CASE 
		      WHEN ec.EventSeverity=0 THEN 4  
		      WHEN ec.EventSeverity=1 THEN 3  
		      WHEN ec.EventSeverity=2 THEN 2  
		      WHEN ec.EventSeverity=3 THEN 1  
	        END AlarmLevel ,
	        ec.StartCompareValue Threshold,  
	        null AbsoluteVal,  
	        null RelativeVal, 
	        '' 	Describe,     
	        event.Description	NMAlarmID  	
	       ,'3Event' SiteWebType
	       ,event.EventId SiteWebId
         FROM TBL_Event event
         INNER JOIN TBL_EventCondition  ec 
		     ON  ec.EquipmentTemplateId=CAST(@EquipmentTemplateId AS INTEGER) and ec.EventId=event.EventId
	       WHERE event.EquipmentTemplateId=CAST(@EquipmentTemplateId AS INTEGER)
         ]]>
      </body>
    </procedure>
    <procedure owner="" name="BM_CreateConfigOfSample_TslPort" grant="">
      <parameters>
        <parameter name="PortId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="MonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      <parameter name="PortDescription" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
           INSERT INTO TSL_Port
		       (PortId, MonitorUnitId, PortNo, PortName, PortType, Setting, PhoneNumber, LinkSamplerUnitId, Description) 
		       VALUES 
		       (@PortId,@MonitorUnitId,1,'COM1',5,'',NULL,0,@PortDescription);
         ]]>
      </body>
    </procedure>
    <procedure owner="" name="BM_CreateConfigOfSample_TslSamplerUnit" grant="">
      <parameters>
        <parameter name="PortId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="MonitorUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SamplerUnitId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SamplerId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SamplerUnitDescription" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
          INSERT INTO TSL_SamplerUnit 
		      (SamplerUnitId, PortId, MonitorUnitId, SamplerId, ParentSamplerUnitId, SamplerType, SamplerUnitName, Address, SpUnitInterval, DllPath,ConnectState,	UpdateTime, PhoneNumber, Description) 
		      VALUES 
		     (@SamplerUnitId,@PortId,@MonitorUnitId,@SamplerId,0,0,'FSU采集单元',1,2,'KoloBusinessServer.exe',1,now(),'',@SamplerUnitDescription);
         ]]>
      </body>
    </procedure>
  </procedures>
</root>
