﻿
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Odbc;
using System.Linq;
using System.Text;
using Carrier.BDSTool;
using Common.Logging.Pro;


using ENPC.Kolo.Entity.B_CMCC;
using BDSTool.BLL.B;
using BDSTool.Utility;
using BDSTool.DBUtility.Common;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;
//using Carrier.BDSTool.Entity;
//using Carrier.BDSTool.BLL;


namespace Carrier.BDSTool
{
    public partial class BDSHelperCMCC
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        ///// <summary>
        ///// 备用，如果复用SSTool需要
        ///// </summary>
        ///// <param name="conn"></param>
        //public static void InitializeOdbc(OdbcConnection conn) {
        //    Carrier.Kolo.SSTool.PublicVar.Connection = conn;
        //}

        /// <summary>
        /// 1-检查数据库中是否存在 FSUID  -> StationID 的映射关系
        /// </summary>
        /// <param name="FSUID"></param>
        /// <returns></returns>
        public static bool ExistFSU(string FSUID)
        {
            try
            {
                string rtn = null;
                if (CommonUtils.IsNoProcedure)
                {
                    rtn = Public_ExecuteSqlService.Instance.NoStore_TSLMonitorUnitCMCC_GetMonitorUnitIdByFSUID(FSUID);
                }
                else
                {
                    var sql = string.Format("SELECT MonitorUnitId FROM TSL_MonitorUnitCMCC WHERE FSUId = '{0}'", FSUID);
                    rtn = DBHelper.ExecuteScalar(sql);
                }
                if (rtn == null)
                    return false;
                return true;
            }
            catch (Exception ex)
            {
                logger.ErrorFormat("ExistFSU();FSU={0};Error={1}", FSUID, ex.Message);
                logger.ErrorFormat("ExistFSU();Stack={0}", ex.StackTrace);
                return false;
            }
        }


        /// <summary>
        /// 2-ID转换: B接口ID   -> StationId
        /// </summary>
        /// <param name="FSUID"></param>
        /// <returns></returns>
        public static bool GetStationIdByFSUID(string FSUID, ref int StationId)
        {
            try
            {
                string rtn = null;
                if (CommonUtils.IsNoProcedure)
                {
                    rtn = Public_ExecuteSqlService.Instance.NoStore_TSLMonitorUnitCMCC_GetByFSUId(FSUID);
                }
                else
                {
                    var sql = string.Format("SELECT StationId FROM TSL_MonitorUnitCMCC WHERE FSUId = '{0}'", FSUID);
                    rtn = DBHelper.ExecuteScalar(sql);
                }
                if (rtn == null)
                {
                    return false;
                }
                StationId = int.Parse(rtn.ToString());
            }
            catch (Exception ex)
            {
                logger.ErrorFormat("GetStationIdByFSUID();FSU={0};Error={1}", FSUID, ex.Message);
                logger.ErrorFormat("GetStationIdByFSUID();Stack={0}", ex.StackTrace);
                return false;
            }

            return true;
        }


        /// <summary>
        /// 3-ID转换: StationId -> B接口ID
        /// </summary>
        /// <param name="stationId"></param>
        /// <returns></returns>
        public static bool GetFSUIDByStationId(int stationId, ref List<string> FSUIDs)
        {
            try
            {
                DataTable table = null;
                if (CommonUtils.IsNoProcedure)
                {
                    table = Public_ExecuteSqlService.Instance.NoStore_TSLMonitorUnitCMCC_GetFsuIdByStationId(stationId);
                }
                else
                {
                    var sql = string.Format("SELECT FSUId FROM TSL_MonitorUnitCMCC WHERE StationId ={0}", stationId);
                    table = DBHelper.GetTable(sql);
                }

                foreach (DataRow r in table.Rows)
                {
                    FSUIDs.Add(r["FSUId"].ToString());
                }
            }
            catch (Exception ex)
            {
                logger.ErrorFormat("GetFSUIDByStationId();stationId={0};Error={1}", stationId, ex.Message);
                logger.ErrorFormat("GetFSUIDByStationId();Stack={0}", ex.StackTrace);
                return false;
            }
            return true;
        }

        /// <summary>
        /// 3P1-ID转换:  MonitroUnitId -> B接口ID; add for yang-20160620
        /// </summary>
        /// <param name="FSUID"></param>
        /// <param name="MonitorUnitId"></param>
        /// <returns></returns>
        public static bool GetFSUIDByMonitroUnitId(ref string FSUID, int MonitorUnitId)
        {
            try
            {
                string rtn = null;
                if (CommonUtils.IsNoProcedure)
                {
                    rtn = Public_ExecuteSqlService.Instance.NoStore_TSLMonitorUnitCMCC_GetFsuIdByMonitorUnitId(MonitorUnitId);
                }
                else
                {
                    var sql = string.Format("SELECT FSUID FROM TSL_MonitorUnitCMCC WHERE MonitorUnitId = {0}", MonitorUnitId);
                    rtn = DBHelper.ExecuteScalar(sql);
                }
                if (rtn == null)
                    return false;

                FSUID = rtn.ToString();
            }
            catch (Exception ex)
            {
                logger.ErrorFormat("GetFSUIDByMonitroUnitId();MonitroUnitId={0};Error={1}", MonitorUnitId, ex.Message);
                logger.ErrorFormat("GetFSUIDByMonitroUnitId();Stack={0}", ex.StackTrace);
                return false;
            }
            return true;
        }


        /// <summary>
        /// 4P1-ID转换: B接口ID(FSUID)  -> MonitorUnitId
        /// </summary>
        /// <param name="FSUID"></param>
        /// <returns></returns>
        public static bool GetMonitorUnitIdByFSUID(string FSUID, ref int MonitorUnitId)
        {
            try
            {
                return MonitorUnitCMCCBiz.GetMonitorUnitIdByFSUID(FSUID, ref MonitorUnitId);
            }
            catch (Exception ex)
            {
                logger.ErrorFormat("GetMonitorUnitIdByFSUID();FSU={0};Error={1}", FSUID, ex.Message);
                logger.ErrorFormat("GetMonitorUnitIdByFSUID();Stack={0}", ex.StackTrace);
                return false;
            }

        }

        /// <summary>
        /// 4-ID转换: B接口ID   -> SiteWeb设备ID
        /// </summary>
        /// <param name="FSUID"></param>
        /// <param name="DeviceID"></param>
        /// <param name="stationId"></param>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        public static bool GetEquipmentIdByFSUDeviceID(string FSUID, string DeviceID, ref int stationId, ref int equipmentId)
        {
            try
            {
                DataTable table = null;
                if (CommonUtils.IsNoProcedure)
                {
                    table = Public_ExecuteSqlService.Instance.NoStore_TBLEquipmentCMCC_GetTableByFsuIdAndDeviceID(FSUID, DeviceID);
                }
                else
                {
                    var sql = string.Format("SELECT StationId,EquipmentId FROM TBL_EquipmentCMCC WHERE   FSUID = '{0}' and DeviceID='{1}'", FSUID, DeviceID);
                    table = DBHelper.GetTable(sql);
                }
                if (table.Rows.Count == 0)
                    return false;
                stationId = int.Parse(table.Rows[0][0].ToString());
                equipmentId = int.Parse(table.Rows[0][1].ToString());
                return true;
            }
            catch (Exception ex)
            {
                logger.ErrorFormat("GetEquipmentIdByFSUDeviceID();FSUID={0};DeviceID={1};Error={2}", FSUID, DeviceID, ex.Message);
                logger.ErrorFormat("GetEquipmentIdByFSUDeviceID();Stack={0}", ex.StackTrace);
                return false;
            }
        }

        /// <summary>
        /// 5-ID转换: SiteWeb设备ID -> B接口ID
        /// </summary>
        /// <param name="stationId"></param>
        /// <param name="equipmentId"></param>
        /// <param name="FSUID"></param>
        /// <param name="DeviceID"></param>
        /// <returns></returns>
        public static bool GetFSUDeviceIDByEquipmentId(int stationId, int equipmentId, ref string FSUID, ref string DeviceID, string dbConnString = null, int? timeOut = null)
        {
            {
                try
                {
                    DataTable table = null;
                    if (CommonUtils.IsNoProcedure)
                    {
                        table = Public_ExecuteSqlService.Instance.NoStore_TBLEquipmentCMCC_GetTableByStationIdAndEequipmentId(stationId, equipmentId);
                    }
                    else
                    {
                        var sql = string.Format("SELECT FSUID, DeviceID FROM TBL_EquipmentCMCC WHERE StationId={0} and  EquipmentId = {1}", stationId, equipmentId);
                        table = DBHelper.GetTable(sql, dbConnString, timeOut);
                    }
                    if (table.Rows.Count > 0)
                    {
                        FSUID = table.Rows[0][0].ToString();
                        DeviceID = table.Rows[0][1].ToString();
                        return true;
                    }
                    else
                        return false;
                }
                catch (Exception ex)
                {
                    logger.ErrorFormat("GetFSUDeviceIDByEquipmentId();stationId={0};equipmentId={1};Error={2}", stationId, equipmentId, ex.Message);
                    logger.ErrorFormat("GetFSUDeviceIDByEquipmentId();Stack={0}", ex.StackTrace);
                    return false;
                }
            }
        }
    }
}
