﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B
{
    /// <summary>
    /// SC向FSU注销请求应答报文
    /// </summary>
    public sealed class LogoutAck : BMessage
    {
        public EnumResult Result { get; private set; }

        public LogoutAck(EnumResult result)
            : base()
        {
            MessageType = (int)BMessageType.LogoutAck;
            Result = result;
        }

        public override string Serialize()
        {
            XmlDocument xmldoc = new XmlDocument();

            XmlDeclaration xmldecl;
            xmldecl = xmldoc.CreateXmlDeclaration("1.0", "UTF-8", null);
            xmldoc.AppendChild(xmldecl);

            XmlElement xmlelem = xmldoc.CreateElement("", "Response", "");
            xmldoc.AppendChild(xmlelem);

            XmlNode root = xmldoc.SelectSingleNode("Response");
            XmlElement xe1 = xmldoc.CreateElement("PK_Type");

            XmlElement xesub1 = xmldoc.CreateElement("Name");
            xesub1.InnerText = "LOGOUT_ACK";
            xe1.AppendChild(xesub1);
            XmlElement xesub2 = xmldoc.CreateElement("Code");
            xesub2.InnerText = "104";
            xe1.AppendChild(xesub2);


            XmlElement xe2 = xmldoc.CreateElement("Info");
            XmlElement xe21 = xmldoc.CreateElement("Result");
            xe21.InnerText = Result.ToString();
            xe2.AppendChild(xe21);

            root.AppendChild(xe1);
            root.AppendChild(xe2);

           
            root.AppendChild(xe2);

            return xmldoc.InnerXml;
        }

        public static LogoutAck Deserialize(string raw)
        {
            throw new NotImplementedException();
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}: {2}", MessageId, (BMessageType)MessageType, Result);
        }
    }
}
