﻿using BLL;
using DM.TestOrder.DAL;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using Delivery.API;
using Microsoft.AspNetCore.Mvc;
using Delivery.DAL;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace DM.TestOrder.Controllers {
        
    public class OrderListController : BaseController{

        //http://localhost/orderapi/OrderList
        [HttpPost]
        public JsonResult  POST([FromBody]JObject value) {

            var s = JsonConvert.SerializeObject(value);
            var queryParam = JsonConvert.DeserializeObject<QueryParamTestOrderQuery>(s);

            var dt = WoOrderListDal.Query(queryParam);
            DataTableColumnMapper.RenameColumns(dt, OrderFieldMap);
            //    var sReturn = JsonConvert.SerializeObject(dt);

            //    return new HttpResponseMessage() {
            //        Content = new StringContent(sReturn, Encoding.UTF8, "application/json"),
            //    };
            return new JsonResult(dt);
        }

        public static readonly Dictionary<string, string> OrderFieldMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            {"orderid","OrderId"},
            {"orderstate","OrderState"},
            {"myorderid","MyOrderId"},
            {"ordertypestring","OrderTypeString"},
            {"finalgeneralreuslt","FinalGeneralReuslt"},
            {"stationgroup","StationGroup"},
            {"stationname","StationName"},
            {"stationcode","StationCode"},
            {"applyuserfsuvendor","ApplyUserFsuVendor"},
            {"submittime","SubmitTime"},
            {"approvetime","ApproveTime"},
            {"orderstateString","OrderStateString"},
            {"dealer","Dealer"},
            {"applyTime","ApplyTime"}
        };

        [HttpGet("{id}")]
        public JsonResult Get(int id) {
            //var sql = string.Format("WO_TestOrder_Stats {0}", id);
            //var dt = DBHelper.GetTable(sql);
            if(CommonUtils.IsNoProcedure)
            {
                var dt1 = WoCommonService.Instance.GetTestOrderStats(id);
                DataTableColumnMapper.RenameColumns(dt1, testFieldMap);
                return new JsonResult(dt1);
            }
            var dt = new ExecuteSql().ExecuteStoredProcedure("WO_TestOrder_Stats", new QueryParameter[] {
                new QueryParameter("UserId", DataType.Int, id.ToString())
            });
            return new JsonResult(dt);
            //var sReturn = JsonConvert.SerializeObject(dt);


            //return new HttpResponseMessage() {
            //    Content = new StringContent(sReturn, Encoding.UTF8, "application/json"),
            //};
        }

        public static readonly Dictionary<string, string> testFieldMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            {"orderstate","OrderState"},
            {"ordercount","OrderCount"}
        };

        [HttpGet]
        public JsonResult Get() {
            if (CommonUtils.IsNoProcedure)
            {
                QueryParamTestOrderQuery mypara = new QueryParamTestOrderQuery();
                mypara.UserId = -1;
                var dt = WOTestOrderQueryService.Instance.WOTestOrderQuery(mypara);
                return new JsonResult(dt);
            }
            else
            {
                var dt = new ExecuteSql().ExecuteStoredProcedure("WO_TestOrder_Query", new QueryParameter[0]);
                return new JsonResult(dt);
            }

                //var sReturn = JsonConvert.SerializeObject(dt);


                //return new HttpResponseMessage() {
                //    Content = new StringContent(sReturn, Encoding.UTF8, "application/json"),
                //};
            }
    }
}
