﻿using Delivery.API;
using DM.TestOrder.DAL;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;




namespace DM.TestOrder.Controllers {

    public class DataItemOfStationTypeDeleteController : BaseController {

        [HttpPost]
        public JsonResult POST([FromBody]JObject value) {
            var ItemId = int.Parse(value["ItemId"].ToString());


            var rtnMsg = WrDataItemOfStationTypeDal.DeleteOne(ItemId);

            var rtn = new {
                errormsg = rtnMsg
            };

            return new JsonResult(rtn);
            //var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(rtn);


            //Debug.WriteLine(value.ToString());
            //Debug.WriteLine(jsonRtn.ToString());

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};
        }
    }
}
