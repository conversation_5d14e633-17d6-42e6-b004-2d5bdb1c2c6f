﻿using System;
using System.Collections.Generic;
using System.Data;
using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;

namespace Delivery.Common.NoProcedure.Service
{
    public class BslPartSPService
    {
        private static BslPartSPService _instance = null;

        public static BslPartSPService Instance
        {
            get
            {
                if (_instance == null) _instance = new BslPartSPService();
                return _instance;
            }
        }

        /// <summary> 存储过程 SP_RejectWRFsu 的实现 </summary>
        public DataTable RejectWRFsu(int wrFsuId, string rejectCause, ExecuteHelper helper = null)
        {
            if (helper == null)
            {
                using (DbHelper dbHelper = new DbHelper())
                {
                    ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                    try
                    {
                        DataTable rtn = CommonRejectWRFsu(wrFsuId, rejectCause, executeHelper);
                        executeHelper.Commit();
                        return rtn;

                    }
                    catch (Exception ex)
                    {
                        executeHelper.Rollback();
                        Logger.Log("BslPartSPService.RejectWRFsu throw Exception: ", LogType.Error);
                        Logger.Log(ex);
                        return null;
                    }
                    finally
                    {
                        dbHelper.Connection.Close();
                    }
                }
            }
            else
            {
                return CommonRejectWRFsu(wrFsuId, rejectCause, helper);
            }
        }

        private DataTable CommonRejectWRFsu(int wrFsuId, string rejectCause, ExecuteHelper helper)
        {

            Dictionary<string, object> realParams = new Dictionary<string, object>
            {
                { "WRFsuId", wrFsuId }, //FSU资源ID
                { "RejectCause", rejectCause }
            };
            object fsuStatusObj = helper.ExecuteScalar("RejectWRFsu_GetFsuStatus", realParams);
            int fsuStatus = fsuStatusObj == null || fsuStatusObj == DBNull.Value ? -999 : int.Parse(fsuStatusObj.ToString());
            if(fsuStatus == 3)
            {
                DataTable dataTable1 = new DataTable { Columns = { new DataColumn("ReturnValue", typeof(int)) },
                                                       Rows = { new object[] { -1 } } };
                return dataTable1;
            }
            helper.ExecuteNonQuery("RejectWRFsu_UpdateFsuManagement", realParams);
            DataTable dataTable2 = new DataTable { Columns = { new DataColumn("ReturnValue", typeof(int)) },
                                                   Rows = { new object[] { 1 } } };
            return dataTable2;
        }

        /// <summary> 存储过程 SP_Del_WRFsu 的实现. 【原注释】：----删除FSU----移动版20180828-- </summary>
        public DataTable DelWRFsu(int wrFsuId, string logonId, int checkRight = 1, ExecuteHelper helper = null)
        {
            if(helper == null)
            {
                using (DbHelper dbHelper = new DbHelper())
                {
                    ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                    try
                    {

                        DataTable rtn = CommonDelWRFsu(wrFsuId, logonId, checkRight, executeHelper);
                        //executeHelper.Commit();
                        return rtn;

                    }
                    catch (Exception ex)
                    {
                        executeHelper.Rollback();
                        Logger.Log("BslPartSPService.DelWRFsu throw Exception: ", LogType.Error);
                        Logger.Log(ex);
                        return null;
                    }
                    finally
                    {
                        dbHelper.Connection.Close();
                    }
                }
            } else
            {
                return CommonDelWRFsu(wrFsuId, logonId, checkRight, helper);
            } 
        }

        private DataTable CommonDelWRFsu(int wrFsuId, string logonId, int checkRight, ExecuteHelper helper)
        {
            Dictionary<string, object> realParams = new Dictionary<string, object>
            {
                { "WRFsuId", wrFsuId }, //-- --SU自增唯一标识
                { "LogonId", logonId }  //--  当前登录用户ID
            };
            object userIdObj = helper.ExecuteScalar("BslPartSp_GetUserId", realParams);
            if(userIdObj == null || userIdObj == DBNull.Value)
            {
                Logger.Log("userId is null. LogonId=" + logonId, LogType.Error);
                return null;
            }
            int userId = int.Parse(userIdObj.ToString());
            realParams.Add("UserId", userId);
            if(checkRight == 1)
            {
                object fmCountObj = helper.ExecuteScalar("DelWRFsu_GetFsuManagementCount", realParams);
                int fmCount = fmCountObj == null || fmCountObj == DBNull.Value ? -1 : int.Parse(fmCountObj.ToString());

                if(fmCount <= 0)
                {
                    Logger.Log("当前用户无权限删除此SU");
                    DataTable rtn = new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { -1 } }
                    };
                    return rtn;
                }
            }

            DataTable infoDt = helper.ExecDataTable("DelWRFsu_GetFsuInfo", realParams);
            object wRStationId = DBNull.Value, sWStationId = DBNull.Value, sWMonitorUnitId = DBNull.Value, fsuStatus = DBNull.Value;
            if(infoDt != null && infoDt.Rows.Count > 0)
            {
                DataRow row = infoDt.Rows[0];
                wRStationId = CommonUtils.GetNullableValue(row.Field<int?>("WRStationId"));//TODO：统一修改此用变量的取法
                sWStationId = CommonUtils.GetNullableValue(row.Field<int?>("SWStationId"));
                sWMonitorUnitId = CommonUtils.GetNullableValue(row.Field<int?>("SWMonitorUnitId"));
                fsuStatus = CommonUtils.GetNullableValue(row.Field<int?>("FsuStatus"));
            }
            if(fsuStatus == DBNull.Value)
            {
                Logger.Log("UnKnown fsuStatus: null 。 WRFsuId=" + wrFsuId);
                return null;
            }
            if((int)fsuStatus != 3)
            {
                int rtnVal = 1;
                try
                {
                    helper.ExecuteNonQuery("DelWRFsu_DelFsuManagement", realParams);
                    helper.Commit();
                } catch(Exception ex)
                {
                    rtnVal = -2;
                    Logger.Log("DelWRFsu_DelFsuManagement throw Exception: ");
                    Logger.Log("删除SU失败, @WRFsuId=" + wrFsuId);
                    Logger.Log(ex);
                    helper.Rollback();
                }
                if(checkRight == 1)
                {
                    DataTable rtn = new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { rtnVal } }
                    };
                    return rtn;
                } else
                {
                    return null;
                }
            }
            //--监控单元名称
            realParams.Add("SWMonitorUnitId", sWMonitorUnitId);
            realParams.Add("SWStationId", sWStationId);
            object nameObj = helper.ExecuteScalar("DelWRFsu_GetMonitorUnitName", realParams);
            string sWMonitorUnitName = nameObj == null || nameObj == DBNull.Value ? "" : nameObj.ToString();
            //--自诊断设备
            DataTable equipInfoDt = helper.ExecDataTable("DelWRFsu_GetEquipInfo", realParams);
            object sWEquipmentId = DBNull.Value;
            object sWEquipmentName = DBNull.Value; 
            int resultRtn = -9999;
            if (equipInfoDt != null && equipInfoDt.Rows.Count > 0)
            {
                sWEquipmentId = CommonUtils.GetNullableValue(equipInfoDt.Rows[0].Field<int?>("EquipmentId"));
                sWEquipmentName = CommonUtils.GetNullableValue(equipInfoDt.Rows[0].Field<string>("EquipmentName"));
            }
            realParams.Add("WRStationId", wRStationId);
            DataTable wRDeviceIdsDt = helper.ExecDataTable("DelWRFsu_GetWRDeviceIds", realParams);
            if(wRDeviceIdsDt != null && wRDeviceIdsDt.Rows.Count > 0)
            {
                //-- 删除SU下所有设备
                foreach (DataRow row in wRDeviceIdsDt.Rows)
                {
                    int? wRDeviceId = row.Field<int?>("WRDeviceId");
                    if(wRDeviceId.HasValue)
                    {
                        resultRtn = -9999;
                        try
                        {
                            DataTable dt = DelWRDevice(wRDeviceId.Value, logonId, 0);
                            if(dt != null && dt.Rows[0].Field<int>("ReturnValue") < 0)
                            {
                                resultRtn = -99;
                            } else
                            {
                                resultRtn = 1;
                            }
                        }
                        catch (Exception ex)
                        {
                            resultRtn = -99;
                            Logger.Log("删除SU下设备失败[异常], WRDeviceId=" + wRDeviceId);
                            Logger.Log(ex);
                        }
                        if (resultRtn != 1)
                        {
                            helper.Rollback();
                            Logger.Log("删除SU下设备失败,WRDeviceId=" + wRDeviceId);
                            DataTable rtn = new DataTable
                            {
                                Columns = { new DataColumn("ReturnValue", typeof(int)) },
                                Rows = { new object[] { -3 } }
                            };
                            return rtn;
                        }
                    }
                }
            }
            resultRtn = -9999;
            try { 
                //--20190119高映军日志，删除FSU
                //--------------------------------------------------
                object fsuNameObj = helper.ExecuteScalar("DelWRFsu_GetFsuName", realParams);
                string fsuName = fsuNameObj == null || fsuNameObj == DBNull.Value ? "" : fsuNameObj.ToString();
                object stationNameObj = helper.ExecuteScalar("DelWRFsu_GetStationName", realParams);
                string stationName = stationNameObj == null || stationNameObj == DBNull.Value ? "" : stationNameObj.ToString();
                string LastString = "SWMonitorUnitId:" + sWMonitorUnitId;
                Public_StoredService.Instance.SP_WR_OperationRecord(wRStationId, stationName, 8, wrFsuId, fsuName, "", LastString, logonId);
                //-- ------------------------------------------------	
                //--删除交维SU
                helper.ExecuteNonQuery("DelWRFsu_DelFsuManagement", realParams);
                resultRtn = 1;
            } catch(Exception ex)
            {
                resultRtn = -99;
                Logger.Log("删除交维SU失败[异常],WRFsuId=" + wrFsuId);
                Logger.Log(ex);
            }
            if(resultRtn != 1)
            {
                helper.Rollback(); 
                Logger.Log("删除交维SU失败,WRFsuId=" + wrFsuId);
                DataTable rtn = new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -4 } }
                };
                return rtn;
            }
            //-- 删除SiteWeb监控单元
            resultRtn = -9999;
            try
            {
                PCT_DeleteMonitorUnitService.Instance.PCT_DeleteMonitorUnit(int.Parse(sWMonitorUnitId.ToString()));
                
                resultRtn = 1;
            }
            catch (Exception ex)
            {
                resultRtn = -99;
                Logger.Log("删除SiteWeb监控单元失败[异常],SWMonitorUnitId=" + sWMonitorUnitId);
                Logger.Log(ex);
            }
            if(resultRtn != 1)
            {
                helper.Rollback();
                Logger.Log("删除SiteWeb监控单元失败,SWMonitorUnitId=" + sWMonitorUnitId);
                DataTable rtn = new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -5 } }
                };
                return rtn;
            }
            //-- 记录删除监控单元日志
            realParams.Add("SWMonitorUnitName", sWMonitorUnitName);
            resultRtn = -9999;
            try
            {
                helper.ExecuteNonQuery("DelWRFsu_InsertOperationDetail6", realParams);
                resultRtn = 1;
            } catch(Exception ex)
            {
                resultRtn = -99;
                Logger.Log("记录删除监控单元日志失败[异常]");
                Logger.Log(ex);
            }
            if (resultRtn != 1)
            {
                helper.Rollback();
                Logger.Log("记录删除监控单元日志失败");
                DataTable rtn = new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -6 } }
                };
                return rtn;
            }
            //-- 记录删除监控单元自诊断设备日志
            realParams.Add("SWEquipmentId", sWEquipmentId);
            realParams.Add("SWEquipmentName", sWEquipmentName);
            resultRtn = -9999;
            try
            {
                helper.ExecuteNonQuery("DelWRFsu_InsertOperationDetail11", realParams);
                resultRtn = 1;
            }
            catch (Exception ex)
            {
                resultRtn = -99;
                Logger.Log("记录删除监控单元自诊断设备日志失败[异常]");
                Logger.Log(ex);
            }
            if (resultRtn != 1)
            {
                helper.Rollback();
                Logger.Log("记录删除监控单元自诊断设备日志失败");
                DataTable rtn = new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -7 } }
                };
                return rtn;
            }
            //-- 删除SiteWeb监控单元扩展表信息
            resultRtn = -9999;
            try
            {
                helper.ExecuteNonQuery("DelWRFsu_DelMonitorUnitExtend", realParams);
                resultRtn = 1;
            }
            catch (Exception ex)
            {
                resultRtn = -99;
                Logger.Log("删除SiteWeb监控单元扩展表信息失败[异常],@SWMonitorUnitId=" + sWMonitorUnitId);
                Logger.Log(ex);
            }
            if (resultRtn != 1)
            {
                helper.Rollback();
                Logger.Log("删除SiteWeb监控单元扩展表信息失败");
                DataTable rtn = new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -8 } }
                };
                return rtn;
            }
            //-- 删除B接口监控单元信息
            resultRtn = -9999;
            try
            {
                helper.ExecuteNonQuery("DelWRFsu_DelMonitorUnitCMCC", realParams);
                resultRtn = 1;
            }
            catch (Exception ex)
            {
                resultRtn = -99;
                Logger.Log("删除B接口监控单元信息失败[异常],@SWMonitorUnitId=" + sWMonitorUnitId);
                Logger.Log(ex);
            }
            if (resultRtn != 1)
            {
                helper.Rollback();
                Logger.Log("删除B接口监控单元信息失败");
                DataTable rtn = new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -9 } }
                };
                return rtn;
            }
            helper.Commit();
            //-- --页面直接调用时,@CheckRight = 1,执行成功要返回结果
            //----否则直接return
            if (checkRight == 1)
            {
                DataTable rtn = new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { 1 } }
                };
                return rtn;
            }
            return null;
        }

        /// <summary> 存储过程 SP_Del_WRDevice 的实现. 【原注释】 ----删除设备----移动版--  </summary>
        public DataTable DelWRDevice(int wRDeviceId, string logonId, int checkRight = 1, ExecuteHelper helper = null)
        {
            if (helper == null)
            {
                using (DbHelper dbHelper = new DbHelper())
                {
                    ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                    try
                    {
                        DataTable rtn = CommonDelWRDevice(wRDeviceId, logonId, checkRight, executeHelper);
                        //executeHelper.Commit();
                        return rtn;

                    }
                    catch (Exception ex)
                    {
                        executeHelper.Rollback();
                        Logger.Log("BslPartSPService.DelWRDevice throw Exception: ", LogType.Error);
                        Logger.Log(ex);
                        return null;
                    }
                    finally
                    {
                        dbHelper.Connection.Close();
                    }
                }
            }
            else
            {
                return CommonDelWRDevice(wRDeviceId, logonId, checkRight, helper);
            }
        }

        private DataTable CommonDelWRDevice(int wRDeviceId, string logonId, int checkRight, ExecuteHelper helper)
        {
            int resultRtn = -9999;
            Dictionary<string, object> realParams = new Dictionary<string, object>
            {
                { "WRDeviceId", wRDeviceId }, //-- 设备自增唯一标识
                { "LogonId", logonId }  //-- 当前登录用户ID
            };
            object userIdObj = helper.ExecuteScalar("BslPartSp_GetUserId", realParams);
            if (userIdObj == null || userIdObj == DBNull.Value)
            {
                Logger.Log("userId is null, LogonId=" + logonId, LogType.Error);
                return null;
            }
            int userId = int.Parse(userIdObj.ToString());
            realParams.Add("UserId", userId);
            if (checkRight == 1)
            {
                object dmCountObj = helper.ExecuteScalar("DelWRDevice_GetDeviceManagementCount", realParams);
                int dmCount = dmCountObj == null || dmCountObj == DBNull.Value ? -1 : int.Parse(dmCountObj.ToString());

                if (dmCount <= 0)
                {
                    Logger.Log("当前用户无权限删除此设备");
                    DataTable rtn = new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { -1 } }
                    };
                    return rtn;
                }
            }
            DataTable infoDt = helper.ExecDataTable("DelWRDevice_GetDeviceInfo", realParams);
            object sWMonitorUnitId = DBNull.Value, sWStationId = DBNull.Value;
            object deviceCode = DBNull.Value;
            if (infoDt != null && infoDt.Rows.Count > 0)
            {
                DataRow row = infoDt.Rows[0];
                deviceCode = CommonUtils.GetNullableValue(row.Field<string>("DeviceCode"));
                sWMonitorUnitId = CommonUtils.GetNullableValue(row.Field<int?>("SWMonitorUnitId"));
                sWStationId = CommonUtils.GetNullableValue(row.Field<int?>("SWStationId"));
            }
            //-- 取SiteWeb的设备ID
            realParams.Add("SWStationId", sWStationId);
            realParams.Add("SWMonitorUnitId", sWMonitorUnitId);
            realParams.Add("DeviceCode", deviceCode);
            DataTable equipInfoDt = helper.ExecDataTable("DelWRDevice_GetEquipInfo", realParams);
            object sWEquipmentId = DBNull.Value;
            object sWEquipmentName = DBNull.Value;
            if (equipInfoDt != null && equipInfoDt.Rows.Count > 0)
            {
                DataRow row = equipInfoDt.Rows[0];
                sWEquipmentId = CommonUtils.GetNullableValue(row.Field<int?>("EquipmentId"));
                sWEquipmentName = CommonUtils.GetNullableValue(row.Field<string>("EquipmentName"));
            }
            if(sWEquipmentId != null)
            {
                resultRtn = -9999;
                //-- 删除SiteWeb的设备信息
                try
                {
                    Public_StoredService.Instance.PCT_DeleteEquipment(int.Parse(sWStationId.ToString()), int.Parse(sWEquipmentId.ToString()));
                    resultRtn = 1;
                }
                catch (Exception ex)
                {
                    resultRtn = -99;
                    Logger.Log("调用PCT_DeleteEquipment逻辑失败[异常],SWStationId=" + sWStationId + ";SWEquipmentId=" + sWEquipmentId);
                    Logger.Log(ex);
                }
                if (resultRtn != 1)
                {
                    helper.Rollback();
                    Logger.Log("调用PCT_DeleteEquipment逻辑失败");
                    DataTable rtn = new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { -2 } }
                    };
                    return rtn;
                }
                string objectId = sWStationId + "." + sWEquipmentId;
                resultRtn = -9999;
                try
                {
                    PblConfigChangeLogService.Instance.DoExecute(objectId, 3, 3, helper);
                    resultRtn = 1;
                }
                catch (Exception ex )
                {
                    resultRtn = -99;
                    Logger.Log("调用PBL_ConfigChangeLog逻辑失败[异常],objectId=" + objectId);
                    Logger.Log(ex);
                }
                if (resultRtn != 1)
                {
                    helper.Rollback();
                    Logger.Log("调用PBL_ConfigChangeLog逻辑失败");
                    DataTable rtn = new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { -1 } }
                    };
                    return rtn;
                }
                //-- 记录删除设备日志
                realParams.Add("SWEquipmentId", sWEquipmentId);
                realParams.Add("SWEquipmentName", sWEquipmentName);
                resultRtn = -9999;
                try
                {
                    helper.ExecuteNonQuery("DelWRDevice_InsertOperationDetail11", realParams);
                    resultRtn = 1;
                }
                catch (Exception ex)
                {
                    resultRtn = -99;
                    Logger.Log("记录删除设备日志失败[异常]:");
                    Logger.Log(ex);
                }
                if (resultRtn != 1)
                {
                    helper.Rollback();
                    Logger.Log("记录删除设备日志失败");
                    DataTable rtn = new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { -3 } }
                    };
                    return rtn;
                }
                //-- 删除B接口数据表的设备信息
                resultRtn = -9999;
                try
                {
                    helper.ExecuteNonQuery("DelWRDevice_DelEquipmentCMCC", realParams);
                    resultRtn = 1;
                }
                catch (Exception ex)
                {
                    resultRtn = -99;
                    Logger.Log("删除B接口数据表的设备信息[异常]:");
                    Logger.Log(ex);
                }
                if (resultRtn != 1)
                {
                    helper.Rollback();
                    Logger.Log("删除B接口数据表的设备信息");
                    DataTable rtn = new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { -4 } }
                    };
                    return rtn;
                }
            }
            resultRtn = -9999;
            try
            {
                //-- 20190119高映军日志，删除设备
                //-- ------------------------------------------------
                object wRStationId = DBNull.Value;
                object deviceName = DBNull.Value;
                DataTable wrEquipInfoDt = helper.ExecDataTable("DelWRDevice_GetWRDeviceInfo", realParams);
                if(wrEquipInfoDt != null && wrEquipInfoDt.Rows.Count > 0)
                {
                    wRStationId = CommonUtils.GetNullableValue(wrEquipInfoDt.Rows[0].Field<int?>("WRStationId"));
                    deviceName = CommonUtils.GetNullableValue(wrEquipInfoDt.Rows[0].Field<string>("DeviceName"));
                }
                realParams.Add("WRStationId", wRStationId);
                object stationNameObj = helper.ExecuteScalar("DelWRDevice_GetStationName", realParams);
                string stationName = stationNameObj == null || stationNameObj == DBNull.Value ? "" : stationNameObj.ToString();
                string lastString = "SWEquipmentId:" + sWEquipmentId;
                Public_StoredService.Instance.SP_WR_OperationRecord(wRStationId, stationName, 11, wRDeviceId, deviceName.ToString(), "", lastString, logonId);
                //	-- ------------------------------------------------	
                //--删除交维表设备信息
                helper.ExecuteNonQuery("DelWRDevice_DelDeviceManagement", realParams);
                resultRtn = 1;
            }
            catch (Exception ex)
            {
                resultRtn = -99;
                Logger.Log("删除交维表设备信息[异常]:");
                Logger.Log(ex);
            }
            if (resultRtn != 1)
            {
                helper.Rollback();
                Logger.Log("删除交维表设备信息");
                DataTable rtn = new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -5 } }
                };
                return rtn;
            }
            helper.Commit();
            return new DataTable
            {
                Columns = { new DataColumn("ReturnValue", typeof(int)) },
                Rows = { new object[] { 1 } }
            };
        }

        /// <summary> 存储过程 SP_Upd_WRDevice 的实现.  </summary>
        public DataTable UpdWRDevice(int wRDeviceId, string wRHouseId, string deviceCategory, string deviceSubCategory,
                        string sysSerialNo, string deviceName, string remark, int wRFsuId, string logonId, int portUse, string enableTime, string lifeTime, ExecuteHelper helper = null)
        {
            //入参注释：
            //	WRDeviceId			-- 设备自增唯一标识     
            //	WRHouseId			-- 机房资源ID,唯一标识
            //	DeviceCategory		-- 设备类型编码
            //	DeviceSubCategory 	-- 设备子类编码
            //	SysSerialNo			-- 移动系统顺序号
            //	DeviceName			-- 设备名称
            //	Remark              -- 备注  
            //	WRFsuId				-- SU资源ID,唯一标识   	  	
            //	LogonId				-- 当前登录用户ID 
            //  PortUse             -- 设备使用端口
            //  EnableTime          -- 启用时间
            //  LifeTime            -- 可使用年限
            if (helper == null)
            {
                using (DbHelper dbHelper = new DbHelper())
                {
                    ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, false);
                    try
                    {
                        DataTable rtn = CommonUpdWRDevice(wRDeviceId, wRHouseId, deviceCategory, deviceSubCategory, sysSerialNo,
                                                deviceName, remark, wRFsuId, logonId, executeHelper, portUse, enableTime, lifeTime);
                        executeHelper.Commit();
                        return rtn;
                    }
                    catch (Exception ex)
                    {
                        executeHelper.Rollback();
                        Logger.Log("BslPartSPService.TEST throw Exception: ", LogType.Error);
                        Logger.Log(ex);
                        return null;
                    }
                    finally
                    {
                        dbHelper.Connection.Close();
                    }
                }
            } else
            {
                return CommonUpdWRDevice(wRDeviceId, wRHouseId, deviceCategory, deviceSubCategory, sysSerialNo,
                                deviceName, remark, wRFsuId, logonId, helper, portUse, enableTime, lifeTime);
            }



            
        }

        private DataTable CommonUpdWRDevice(int wRDeviceId, string wRHouseId, string deviceCategory, string deviceSubCategory,
                        string sysSerialNo, string deviceName, string remark, int wRFsuId, string logonId, ExecuteHelper helper, int portUse, string enableTime, string lifeTime)
        {
            Dictionary<string, object> realParams = new Dictionary<string, object>
            {
                { "WRDeviceId", wRDeviceId },
                { "WRHouseId", wRHouseId } ,
                { "DeviceCategory", deviceCategory } ,
                { "DeviceSubCategory", deviceSubCategory } ,
                { "SysSerialNo", sysSerialNo } ,
                { "DeviceName", deviceName } ,
                { "Remark", remark } ,
                { "WRFsuId", wRFsuId } ,
                { "LogonId", logonId },
                { "PortUse", portUse },
                { "EnableTime", enableTime },
                { "LifeTime", lifeTime }
            };
            object hmCountObj = helper.ExecuteScalar("UpdWRDevice_GetHouseManagementCount", realParams);
            int hmCount = hmCountObj == null || hmCountObj == DBNull.Value ? -1 : int.Parse(hmCountObj.ToString());
            if(hmCount > 0)
            {
                Logger.Log("所选机房尚未通过审核,机房资源ID = " + wRHouseId);
                return new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -1 } }
                };
            }

            DataTable hmInfoDt = helper.ExecDataTable("UpdWRDevice_GetHmInfo", realParams);
            object wRStationId = DBNull.Value, sWStationId = DBNull.Value, sWHouseId = DBNull.Value;
            if(hmInfoDt != null && hmInfoDt.Rows.Count > 0)
            {
                DataRow row = hmInfoDt.Rows[0];
                wRStationId = CommonUtils.GetNullableValue(row.Field<int?>("WRStationId"));
                sWStationId = CommonUtils.GetNullableValue(row.Field<int?>("SWStationId"));
                sWHouseId = CommonUtils.GetNullableValue(row.Field<int?>("SWHouseId"));
            }
            realParams.Add("SWStationId", sWStationId);
            object dmCountObj = helper.ExecuteScalar("UpdWRDevice_GetDeviceManagementCount", realParams);
            int dmCount = dmCountObj == null || dmCountObj == DBNull.Value ? -1 : int.Parse(dmCountObj.ToString());
            if(dmCount > 0)
            {
                Logger.Log("设备名称重复:" + deviceName);
                return new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -2 } }
                };
            }
            string deviceType = deviceCategory + deviceSubCategory;
            string oriDeviceType = null, deviceCode = null;  
            DataTable dmInfoDt = helper.ExecDataTable("UpdWRDevice_GetDmInfo", realParams);
            if(dmInfoDt != null && dmInfoDt.Rows.Count > 0)
            {
                DataRow row = dmInfoDt.Rows[0];
                oriDeviceType = row.Field<string>("DeviceType");
                deviceCode = row.Field<string>("DeviceCode");
            }
            string oriSysSerialNo = string.IsNullOrEmpty(deviceCode) ? "" : deviceCode.Substring(6, 3); //存储过程中第一个参数从1开始计的，本方法是从0开始计的
            if(oriDeviceType != deviceType || oriSysSerialNo != sysSerialNo)
            {
                deviceCode = null;
                deviceCode = Public_StoredService.Instance.SP_GenerateDeviceCode(wRStationId, deviceType, sysSerialNo, deviceCode);
                if (deviceCode == null) 
                {
                    return new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { -3 } }
                    };
                }
            }
            object userId = DBNull.Value;
            object sWUserName = DBNull.Value;
            DataTable accountInfoDt = helper.ExecDataTable("UpdWRDevice_GetAccountInfo", realParams);
            if(accountInfoDt != null && accountInfoDt.Rows.Count > 0)
            {
                DataRow row = accountInfoDt.Rows[0];
                userId = CommonUtils.GetNullableValue(row.Field<int?>("UserId"));
                sWUserName = CommonUtils.GetNullableValue(row.Field<string>("UserName"));
            }
            string lastString = null;
            DataTable lastStringInfoDt = helper.ExecDataTable("UpdWRDevice_GetLastStringInfo", realParams);
            if(lastStringInfoDt != null && lastStringInfoDt.Rows.Count > 0)
            {
                DataRow row = lastStringInfoDt.Rows[0];
                lastString = "WRHouseId:" + row.Field<int?>("WRHouseId") +
                               "-DeviceType:" + row.Field<string>("DeviceType") +
                               "-DeviceCode:" + row.Field<string>("DeviceCode") +
                               "-DeviceName:" + row.Field<string>("DeviceName") +
                               "-UserId:" + row.Field<int?>("UserId") +
                               "-Remark:" + row.Field<string>("Remark") +
                               "-WRFsuId:" + row.Field<int?>("WRFsuId") +
                               "-PortUse:" + row.Field<int?>("PortUse") +
                               "-EnableTime:" + row.Field<DateTime?>("EnableTime")?.ToString("yyyy-MM-dd HH:mm:ss") +
                               "-LifeTime:" + row.Field<string>("LifeTime");
            }
            string updateString = "WRHouseId:" + wRHouseId +
                                   "-DeviceType:" + deviceType +
                                   "-DeviceCode:" + deviceCode +
                                   "-DeviceName:" + deviceName +
                                   "-UserId:" + userId +
                                   "-Remark:" + remark +
                                   "-WRFsuId:" + wRFsuId +
                                   "-PortUse:" + portUse +
                                   "-EnableTime:" + enableTime +
                                   "-LifeTime:" + lifeTime;
            realParams.Add("WRStationId", wRStationId);
            object stationNameObj = helper.ExecuteScalar("UpdWRDevice_GetStationName", realParams);
            string stationName = stationNameObj == null || stationNameObj == DBNull.Value ? null : stationNameObj.ToString();
            Public_StoredService.Instance.SP_WR_OperationRecord(wRStationId, stationName, 12, wRDeviceId, deviceName, updateString, lastString, logonId);

            realParams.Add("DeviceType", deviceType);
            realParams.Add("DeviceCode", deviceCode);
            realParams.Add("UserId", userId);
            realParams.Add("SWUserName", sWUserName);
            realParams.Add("SWHouseId", sWHouseId);
            helper.ExecuteNonQuery("UpdWRDevice_UptDeviceManagementInfo", realParams);

            object sWEquipmentIdObj = helper.ExecuteScalar("UpdWRDevice_GetSWEquipmentId", realParams);
            object sWEquipmentId = CommonUtils.GetNullableValue(sWEquipmentIdObj);
            if(sWEquipmentId != DBNull.Value)
            {
                sWEquipmentId = int.Parse(sWEquipmentId.ToString());
                realParams.Add("SWEquipmentId", sWEquipmentId);
                helper.ExecuteNonQuery("UpdWRDevice_UptEquipmentInfo", realParams);

                string objectId = sWStationId.ToString() + sWEquipmentId.ToString();
                PblConfigChangeLogService.Instance.DoExecute(objectId, 3, 2, helper);
            }
            return new DataTable
            {
                Columns = { new DataColumn("ReturnValue", typeof(int)) },
                Rows = { new object[] { 1 } }
            };
        }

    }
}
