﻿
using Common.Logging.Pro;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;




using BDSTool.Entity.S2;
using System.Data;
using BDSTool.Entity.DRHelper;
using BDSTool.Entity.B;
using BDSTool.BLL.Convert;
using ENPC.Kolo.Entity.B_CMCC;
using BDSTool.Utility;
using BDSTool.DBUtility.Common;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace BDSTool.BLL.S2
{
    public partial class CfgEquipmentOP : IDataOperation
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public int StationId {
            get;
            set;
        }
        public int EquipmentId {
            get;
            set;
        }

        public TBL_Equipment entity;

        public bool LogCfgChangeEnable {
            set {
            }
             get{
                 return true;
             }
        }

        public EditType EditType {
            get;
            set;
        }

        public object Execute() {
            return true;
        }

        //public static void LogConfigChange(EditType EditType, int StationId, int EquipmentId) {
        //    var op = new CfgEquipmentOP();
        //    op.EditType = EditType;
        //    op.StationId = StationId;
        //    op.EquipmentId = EquipmentId;
        //    CfgChangeLog.LogConfigChange(op);
        //}

        //[TBL_ConfigChangeDefine]
        //3	CfgEquipmentOP	TBL_Equipment	StationId.EquipmentId
        public static void LogConfigChange(EditType EditType, int StationId, int EquipmentId) {
            string objectId = string.Format("{0}.{1}", StationId, EquipmentId);
            int configId = 3;
            int editType = (int)EditType;
            var sql = string.Format("PBL_ConfigChangeLog '{0}',{1},{2}", objectId, configId, editType);
            //DBHelper.ExecuteNonQuery(sql);
            try
            {
                using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                {
                    if (CommonUtils.IsNoProcedure)
                    {
                        PblConfigChangeLogService.Instance.DoExecute(objectId, configId, editType);
                        return;
                    }
                    dbHelper.CreateProcedureCommand("PBL_ConfigChangeLog");
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("ObjectId", DbType.AnsiString, objectId));
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("ConfigId", DbType.Int32, configId));
                    dbHelper.AddDbParameter(dbHelper.GetDbParameter("EditType", DbType.Int32, editType));
                    dbHelper.AddDbParameter(dbHelper.GetReturnDbParameter());
                    dbHelper.ExecuteNoQuery();
                }
            }
            catch (Exception ex)
            {
                DBHelper.LogError(sql, ex);
            }
        }

    }
}
