﻿
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web.Http;


using DM.TestOrder.Model;
using DM.TestOrder.Service.Interface;


namespace DM.TestOrder.Controllers {
        
    public class OrderModelController : BaseApiController{
        //api/OrderModel/1
        public HttpResponseMessage Get(int id) {
            string errmsg;
            var ord = TestOrderApi.Instance.GetOrderModel(id,  out errmsg);

            var json = "";
            if(ord!= null)
                json = Newtonsoft.Json.JsonConvert.SerializeObject(ord);
            else {
                json = Newtonsoft.Json.JsonConvert.SerializeObject( new {errormsg = errmsg});
                
            }

            return new HttpResponseMessage() {
                Content = new StringContent(json, Encoding.UTF8, "application/json"),
            };
        }
    }
}
