﻿


using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using Delivery.DAL;

namespace DM.TestOrder.DAL {
    public class Account
    {
        public static string GetUserNameByUserId(int userId) {
            object rtn = null;
            if (CommonUtils.IsNoProcedure)
            {
                rtn = Public_ExecuteSqlService.Instance.NoStore_GetUserNameByUserId(userId);
            }
            else
            {
                string sql = "SELECT UserName FROM TBL_Account WHERE UserId=@userId";
                rtn = new ExecuteSql().ExecuteSQLScalar(sql, new QueryParameter[] {
                new QueryParameter("userId", DataType.Int, userId.ToString())
            });
            }
            return rtn == null ? null : rtn.ToString();
        }
    }
}

