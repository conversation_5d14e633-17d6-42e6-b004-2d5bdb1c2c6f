﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 监控对象配置信息 
    /// </summary>
    public class TDevConf
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceID { get; set; }
        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; }

        /// <summary>
        /// 所属站点编码
        /// </summary>
        public string SiteID { get; set; }
        /// <summary>
        /// FSU物理机房编码
        /// </summary>
        public string RoomID { get; set; }
        /// <summary>
        /// 设备所在的站点名称
        /// </summary>
        public string SiteName { get; set; }
        /// <summary>
        /// 站点/机房名称
        /// </summary>
        public string RoomName { get; set; }
        /// <summary>
        /// 设备类型
        /// </summary>
        public int? DeviceType { get; set; }
        /// <summary>
        /// 设备子类型
        /// </summary>
        public int? DeviceSubType { get; set; }
        /// <summary>
        /// 设备型号
        /// </summary>
        public string Model { get; set; }
        /// <summary>
        /// 设备品牌
        /// </summary>
        public string Brand { get; set; }
        /// <summary>
        /// 额定容量
        /// </summary>
        public float? RatedCapacity { get; set; }
        /// <summary>
        /// 版本
        /// </summary>
        public string Version { get; set; }
        /// <summary>
        /// 启用时间
        /// </summary>
        public DateTime? BeginRunTime { get; set; }
        /// <summary>
        /// 设备描述信息（包含设备的安装位置）
        /// </summary>
        public string DevDescribe { get; set; }
        /// <summary>
        /// 监控点信号配置信息
        /// </summary>
        public List<TSignal> Signals { get; set; }

        /// <summary>
        /// 配置预留字段
        /// </summary>
        public string ConfRemark { get; set; }

        /// <summary>
        /// 监控对象配置信息
        /// </summary>
        public TDevConf() { }

        /// <summary>
        /// 监控对象配置信息 
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="deviceName">设备名称</param>
        /// <param name="roomName">站点/机房名称</param>
        /// <param name="deviceType">设备类型</param>
        /// <param name="deviceSubType">设备子类型</param>
        /// <param name="model">设备型号</param>
        /// <param name="brand">设备品牌</param>
        /// <param name="ratedCapacity">额定容量</param>
        /// <param name="version">版本</param>
        /// <param name="beginRunTime">启用时间</param>
        /// <param name="devDescribe">设备描述信息（包含设备的安装位置）</param>
        /// <param name="signals">监控点信号配置信息</param>
        public TDevConf(string deviceId, string deviceName, string siteId, string roomId,string siteName, string roomName, int? deviceType, int? deviceSubType, string model,
            string brand, float? ratedCapacity, string version, DateTime? beginRunTime, string devDescribe, List<TSignal> signals, string confRemark)
        {
            DeviceID = deviceId;
            DeviceName = deviceName;
            SiteID = siteId;
            RoomID = roomId;
            SiteName = siteName;
            RoomName = roomName;
            DeviceType = deviceType;
            DeviceSubType = deviceSubType;
            Model = model;
            Brand = brand;
            RatedCapacity = ratedCapacity;
            Version = version;
            BeginRunTime = beginRunTime;
            DevDescribe = devDescribe;
            Signals = signals;
            ConfRemark = confRemark;
        }

        public override string ToString()
        {
            string strSignals = string.Empty;
            foreach(TSignal aSignal in Signals)
            {
                strSignals = strSignals + aSignal.ToString();
            }
            return String.Format(
                "{0}, {1} ,{2} , {3}, {4}, {5}, {6}, {7}, {8}, {9}, {10}, {11}, {12}, {13}, {14}, {15}",
                DeviceID, DeviceName, SiteID, RoomID, SiteName, RoomName, DeviceType, DeviceSubType, Model, Brand,
                RatedCapacity, Version, BeginRunTime.HasValue?Convert.ToDateTime(BeginRunTime).ToString("yyyy-MM-dd HH:mm:ss"):"",
                DevDescribe, strSignals, ConfRemark);
        }

    }
}
