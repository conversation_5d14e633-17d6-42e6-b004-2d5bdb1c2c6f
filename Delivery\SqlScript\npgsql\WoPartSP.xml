﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
    <procedure owner="" name="CR1_RemoveOrder_GetOrderState" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body> select OrderState from WO_TestOrder where WO_TestOrder.OrderId=@OrderId </body>
    </procedure>
    <procedure owner="" name="CR1_RemoveOrder_InsertOrderFlow" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="OldOrderState" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="NewOrderState" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StateSetUserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StateSetUserName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FinalDecision" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FinalIsApprove" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FlowText" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SaveTime" type="datetime" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body> INSERT INTO WO_TestOrderFlow (
            OrderId, OldOrderState, NewOrderState, StateSetUserId, StateSetUserName,
            Decision, Note, IsApprove, FlowText, SaveTime)
        VALUES ( @OrderId, @OldOrderState, @NewOrderState, @StateSetUserId, @StateSetUserName,
            @FinalDecision, '', @FinalIsApprove, @FlowText, @SaveTime)
      </body>
    </procedure>
    <procedure owner="" name="CR1_RemoveOrder_UpdateTestOrder" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="NewOrderState" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StateSetUserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StateSetUserName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FinalDecision" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FinalIsApprove" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SaveTime" type="datetime" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FinalGeneralReuslt" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FinalNote" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body> update WO_TestOrder set
          OrderState= @NewOrderState,
          StateSetUserId = @StateSetUserId,
          StateSetUserName = @StateSetUserName,
          StateChangeTime = @SaveTime,
          FinalUserId= @StateSetUserId,
          FinalGeneralReuslt= @FinalGeneralReuslt,
          FinalDecision= @FinalDecision,
          FinalNote= @FinalNote,
          FinalIsApprove= @FinalIsApprove,
          ApproveTime=now()
        where OrderId = @OrderId
      </body>
    </procedure>
    <procedure owner="" name="CR1_RemoveOrder_InsertListHis" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        insert into WO_TestOrderEquipItemCheckListHis
        (OrderCheckId, OrderId, CheckType, CheckTypeId, HasConfig, IsPass, PassNote, PassFailReason, EquipmentId, EquipmentName, EquipmentCategoryName, BaseEquipmentId, BaseEquipmentName, BaseTypeId, BaseTypeName, Unit, LimitDown, LimitUp, Note, CheckId, EquipmentLogicClass, LogicClass, StandardName, SignalType, SamplerValue, SamplerTime)
        select OrderCheckId, OrderId, CheckType, CheckTypeId, HasConfig, IsPass, PassNote, PassFailReason, EquipmentId, EquipmentName, EquipmentCategoryName, BaseEquipmentId, BaseEquipmentName, BaseTypeId, BaseTypeName, Unit, LimitDown, LimitUp, Note, CheckId, EquipmentLogicClass, LogicClass, StandardName, SignalType, SamplerValue, SamplerTime
        from WO_TestOrderEquipItemCheckList
        where WO_TestOrderEquipItemCheckList.OrderId = @OrderId
      </body>
    </procedure>
    <procedure owner="" name="CR1_RemoveOrder_DelCheckList" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        delete from WO_TestOrderEquipItemCheckList where WO_TestOrderEquipItemCheckList.OrderId = @OrderId
      </body>
    </procedure>
    <procedure owner="" name="CR1_RemoveOrder_DelCheckListSignal" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        delete from WO_TestOrderEquipItemCheckListSignal where WO_TestOrderEquipItemCheckListSignal.OrderId = @OrderId
      </body>
    </procedure>
    <procedure owner="" name="SubmitOnlineApply_GetOldInfo" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>select OrderState,ApplyUserId from WO_TestOrder where WO_TestOrder.OrderId=@OrderId </body>
    </procedure>
    <procedure owner="" name="SubmitOnlineApply_GetUserName" grant="">
      <parameters>
        <parameter name="StateSetUserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>select UserName from TBL_Account where TBL_Account.UserId = @StateSetUserId </body>
    </procedure>
    <procedure owner="" name="SubmitOnlineApply_Insert" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="OldOrderState" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="NewOrderState" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StateSetUserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StateSetUserName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Decision" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Note" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="IsApprove" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FlowText" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="CurrentDateTime" type="datetime" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>INSERT INTO WO_TestOrderFlow (
		                      OrderId, OldOrderState, NewOrderState, StateSetUserId, StateSetUserName, 
		                      Decision, Note, IsApprove, FlowText, SaveTime)
                    VALUES (
		                      @OrderId, @OldOrderState, @NewOrderState, @StateSetUserId, @StateSetUserName,
		                      @Decision, @Note, @IsApprove, @FlowText, @CurrentDateTime)</body>
    </procedure>
    <procedure owner="" name="SubmitOnlineApply_Update" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="NewOrderState" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StateSetUserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StateSetUserName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="CurrentDateTime" type="datetime" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body> update WO_TestOrder set OrderState = @NewOrderState, StateSetUserId = @StateSetUserId, StateSetUserName = @StateSetUserName,
                      StateChangeTime = @CurrentDateTime, SubmitTime = @CurrentDateTime where OrderId = @OrderId </body>
    </procedure>
    <procedure owner="" name="SubmitExpertDecision_GetOldOrderState" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>select OrderState from WO_TestOrder where OrderId = @OrderId </body>
    </procedure>
    <procedure owner="" name="SubmitExpertDecision_GetStateSetUserName" grant="">
      <parameters>
        <parameter name="StateSetUserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>select UserName from TBL_Account where UserId = @StateSetUserId </body>
    </procedure>
    <procedure owner="" name="SubmitExpertDecision_Insert" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="OldOrderState" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="NewOrderState" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StateSetUserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StateSetUserName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ExpertDecision" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ExpertNote" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ExpertIsApprove" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FlowText" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SaveTime" type="datetime" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body> INSERT INTO WO_TestOrderFlow (OrderId, OldOrderState, NewOrderState, StateSetUserId, StateSetUserName,Decision, Note, IsApprove, FlowText, SaveTime)
        VALUES (@OrderId, @OldOrderState, @NewOrderState, @StateSetUserId, @StateSetUserName,@ExpertDecision, @ExpertNote, @ExpertIsApprove, @FlowText, @SaveTime)
      </body>
    </procedure>
    <procedure owner="" name="SubmitExpertDecision_Update" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="NewOrderState" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StateSetUserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StateSetUserName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ExpertDecision" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ExpertNote" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ExpertIsApprove" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SaveTime" type="datetime" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body> update WO_TestOrder  set OrderState = @NewOrderState, StateSetUserId = @StateSetUserId, StateSetUserName = @StateSetUserName,
              StateChangeTime = @SaveTime, ExpertUserId = @StateSetUserId, ExpertDecision = @ExpertDecision, ExpertNote = @ExpertNote,
              ExpertIsApprove = @ExpertIsApprove where OrderId = @OrderId </body>
    </procedure>
    <procedure owner="" name="SubmitFinalDecision_SyncSiteWeb_GetOldInfo" grant="">
      <parameters>
        <parameter name="OrderId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>select  ApplyUserId,StationId,Latitude,Longitude from WO_TestOrder where OrderId=@OrderId </body>
    </procedure>
    <procedure owner="" name="SubmitFinalDecision_SyncSiteWeb_FindStationCount" grant="">
      <parameters>
        <parameter name="StationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>select count(*) from TBL_Station where StationId = @StationId AND Latitude>0 AND Longitude>0</body>
    </procedure>
    <procedure owner="" name="SubmitFinalDecision_SyncSiteWeb_UpdateStation" grant="">
      <parameters>
        <parameter name="StationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Latitude" type="decimal" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Longitude" type="decimal" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body> Update TBL_Station SET Latitude=@Latitude, Longitude=@Longitude where StationId = @StationId </body>
    </procedure>


    <procedure owner="" name="SubmitFinalDecision_SyncSiteWeb_GetExistsEquipmentMaintain" grant="">
      <parameters>
        <parameter name="StationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="OrderId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body> SELECT DISTINCT a.EquipmentId FROM TBL_EquipmentMaintain a  
                           INNER JOIN WO_TestOrderEquipItem i ON a.EquipmentId = i.EquipmentId 
              WHERE a.StationId = @StationId AND a.StartTime IS NOT NULL AND i.OrderId=@OrderId
      </body>
    </procedure>
    
	</procedures>
</root>
