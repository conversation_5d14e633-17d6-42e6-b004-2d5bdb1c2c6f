﻿using Delivery.BSL;
using Delivery.Controllers;
using Microsoft.AspNetCore.Mvc;
using System.Data;
using System.Net.Http;
using System.Text;
using Newtonsoft.Json.Linq;

namespace Delivery.Controllers.Cutover
{
    public class AccountController : BaseController {
        [HttpGet]
        public JsonResult Get(int userid) {
            return new JsonResult(CutoverBSL.GetAccount(userid));
        }

    }
}
