﻿using BDSTool.BLL.B;
using BDSTool.BLL.S2;
using BDSTool.Entity.B;
using BDSTool.Entity.S2;
using Common.Logging.Pro;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace BDSTool.BLL.Convert
{
    public class EquipmentTemplateConvert
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");
        public static  TBL_EquipmentTemplate TryParse(DevConfExBiz devEx, ref  string errmsg ) {
            TBL_EquipmentTemplate et = devEx.EquipmentTemplate;
            var dev = devEx.TDevice;

            try {
                //-----------------------------------------------------------------------------
                //关联配置
                et.EquipmentTemplateId = devEx.EquipmentTemplate.EquipmentTemplateId;

                et.EquipmentCategory = devEx.EquipmentCategory;
                
                //---------------------------------------------------------------------------
                //来源于dev
                et.EquipmentTemplateName = "B接口_" + dev.DeviceName + "_" + devEx.FSUID;
                et.Description = dev.DevDescribe;
                et.Vendor = dev.Brand;
                et.EquipmentStyle = dev.Model;
                //---------------------------------------------------------------------------
                //缺省值
                et.EquipmentType = 1;//int EquipmentType;//监控设备(0)、非监控设备(1)                
                et.ParentTemplateId = 0;
                et.StationCategory = 0;
                //---------------------------------------------------------------------------               
                //et.ProtocolCode = "ProtocolCode";//kkk
                //et.Property = string.Empty;
                //et.Memo = string.Empty;
                //et.Unit = string.Empty;
                //et.EquipmentBaseType = null;
            }
            catch (Exception ex) {
                logger.ErrorFormat("EquipmentTemplateConvert.TryParse();(FSUID,DeviceName)={0}.{1};Error={2}",
                    devEx.FSUID, dev.DeviceName, ex.Message);
                logger.Error(ex.StackTrace);
                return null;
            }
            return et;
        }

    }
}
