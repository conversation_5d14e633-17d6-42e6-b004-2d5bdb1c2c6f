﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web.Http;

using DM.TestOrder.Model;
using DM.TestOrder.Service.Interface;


namespace DM.TestOrder.Controllers {
        
    public class SectionOrderController : BaseApiController{
        //http://localhost:34380/api/SectionOrder
        public HttpResponseMessage Post([FromBody]JObject value) {
            //return Debug.WriteLine(value.ToString());


            var SectionOrder = Newtonsoft.Json.JsonConvert.DeserializeObject<SectionOrder>(value.ToString());

            var errmsg= TestOrderApi.Instance.SubmitModifyOrder(SectionOrder);

            var rtn = new {
                errormsg = errmsg
            };
            var json = Newtonsoft.Json.JsonConvert.SerializeObject(rtn);

            var response = Request.CreateResponse(HttpStatusCode.OK);
            response.StatusCode = HttpStatusCode.OK;
            response.Content = new StringContent(json);    // 响应内容
            return response;
        }
    }
}
