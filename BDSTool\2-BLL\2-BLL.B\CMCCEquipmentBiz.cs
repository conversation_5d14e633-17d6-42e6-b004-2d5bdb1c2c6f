﻿using BDSTool.DBUtility;
using BDSTool.DBUtility.Common;
using BDSTool.Entity.B;
using BDSTool.Entity.DRHelper;
using Common.Logging.Pro;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.BLL.B
{
    public partial class EquipmentCMCCBiz
    {
        public static bool Obsolete_SaveEntity(TBL_EquipmentCMCCEx e) {
            try {
                //var sql = string.Format("@INSERT INTO dbo.TBL_EquipmentCMCC (DeviceID, DeviceName, FSUID, StationId, MonitorUnitId, EquipmentId, RoomName, DeviceType, DeviceSubType, Model, Brand, RatedCapacity, Version, BeginRunTime, DevDescribe, ExtendField1, ExtendField2)
                if (CommonUtils.IsNoProcedure)
                {
                    Public_ExecuteSqlService.Instance.NoStore_InsertRowIntoTBLEquipmentCMCC(SHelper.GetPara(e.DeviceID), SHelper.GetPara(e.DeviceName), SHelper.GetPara(e.FSUID), SHelper.GetPara(e.StationId), SHelper.GetPara(e.MonitorUnitId),
                    SHelper.GetPara(e.EquipmentId), SHelper.GetPara(e.RoomName), SHelper.GetPara(e.DeviceType), SHelper.GetPara(e.DeviceSubType), SHelper.GetPara(e.Model),
                    SHelper.GetPara(e.Brand), SHelper.GetPara(e.RatedCapacity), SHelper.GetPara(e.Version), SHelper.GetPara(e.BeginRunTime), SHelper.GetPara(e.DevDescribe),
                    SHelper.GetPara(e.ExtendField1), SHelper.GetPara(e.ExtendField2));
                }
                else
                {
                    var sql = string.Format(@"INSERT INTO TBL_EquipmentCMCC (DeviceID, DeviceName, FSUID, StationId, MonitorUnitId, EquipmentId, RoomName, DeviceType, DeviceSubType, Model, Brand, RatedCapacity, Version, BeginRunTime, DevDescribe, ExtendField1, ExtendField2) VALUES ({0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13},{14},{15},{16})",
                    SHelper.GetPara(e.DeviceID), SHelper.GetPara(e.DeviceName), SHelper.GetPara(e.FSUID), SHelper.GetPara(e.StationId), SHelper.GetPara(e.MonitorUnitId),
                    SHelper.GetPara(e.EquipmentId), SHelper.GetPara(e.RoomName), SHelper.GetPara(e.DeviceType), SHelper.GetPara(e.DeviceSubType), SHelper.GetPara(e.Model),
                    SHelper.GetPara(e.Brand), SHelper.GetPara(e.RatedCapacity), SHelper.GetPara(e.Version), SHelper.GetPara(e.BeginRunTime), SHelper.GetPara(e.DevDescribe),
                    SHelper.GetPara(e.ExtendField1), SHelper.GetPara(e.ExtendField2));


                    DBHelper.ExecuteNonQuery(sql);
                }
            }
            catch (Exception ex) {
                logger.ErrorFormat("EquipmentCMCCBiz.SaveEntity();Error={0}", ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }
    }
}
