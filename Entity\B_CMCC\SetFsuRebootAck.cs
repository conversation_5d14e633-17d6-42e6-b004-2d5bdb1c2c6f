﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.6.14.重启FSU应答
    /// </summary>
    public sealed class SetFsuRebootAck:BMessage
    {
        public EnumResult Result { get; private set; }
        public string FailureCause { get; private set; }

        public SetFsuRebootAck(string fsuId, EnumResult result, string failureCause)
            : base()
        {
            MessageType = (int)BMessageType.SET_FSUREBOOT_ACK;

            FSUID = fsuId;
            Result = result;
            FailureCause = failureCause;
        }

        public SetFsuRebootAck() : base() 
        {
            MessageType = (int)BMessageType.SET_FSUREBOOT_ACK;
        }

        public static SetFsuRebootAck Deserialize(XmlDocument xmlDoc)
        {
            SetFsuRebootAck setFsuRebootAck = null;
            try
            {
                string fsuId = xmlDoc.SelectSingleNode("/Response/Info/FSUID").InnerText.Trim();
                string resultString = xmlDoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim();
                string failureCause = xmlDoc.SelectSingleNode("/Response/Info/FailureCause").InnerText.Trim();
                EnumResult result;
                if (string.IsNullOrEmpty(resultString))
                {
                    result = EnumResult.FAILURE;
                }
                else
                {
                    result = (EnumResult)int.Parse(resultString);
                }

                setFsuRebootAck = new SetFsuRebootAck(fsuId, result, failureCause);
                entityLogger.DebugFormat("SetFsuRebootAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                setFsuRebootAck.StringXML = xmlDoc.InnerXml;
                return setFsuRebootAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetFsuRebootAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetFsuRebootAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                setFsuRebootAck = new SetFsuRebootAck();
                setFsuRebootAck.ErrorMsg = ex.Message;
                setFsuRebootAck.StringXML = xmlDoc.InnerXml;
                return setFsuRebootAck;
            }
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}, {2}: {3},{4}",
                MessageId, (BMessageType)MessageType, FSUID, Result, FailureCause);
        }

    }


}
