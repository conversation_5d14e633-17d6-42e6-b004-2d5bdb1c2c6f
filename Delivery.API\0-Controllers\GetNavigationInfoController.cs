﻿using Delivery.BSL;

using Microsoft.AspNetCore.Mvc;

using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace Delivery.API._0_Controllers
{
    public class GetNavigationInfoController : BaseController
    {
        [HttpGet("[action]")]
        public JsonResult Get()
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            DataTable dt = ccbsl.GetNeedApproveResource(LogonId);

            return new JsonResult(dt);

        }
    }
}
