﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 请求监控点门限数据
    /// </summary>
    public class GetThreshold : BMessage
    {
        [Newtonsoft.Json.JsonProperty("DeviceList")]
        public List<DeviceGetThreshold> Values { get; set; }

        public GetThreshold() : base()
        {
            MessageType = (int)BMessageType.GET_THRESHOLD;
        }

        public GetThreshold(string fsuId, List<DeviceGetThreshold> values) : base()
        {
            MessageType = (int)BMessageType.GET_THRESHOLD;

            FSUID = fsuId;
            Values = values;
        }

        public override string Serialize()
        {
            string result = string.Empty;
            try
            {
                JsonSerializerSettings settings = new JsonSerializerSettings();
                settings.ContractResolver = new CustomContractResolver();
                TIotEntity iotEntity = new TIotEntity(this);
                result = JsonConvert.SerializeObject(iotEntity, settings);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetThreshold.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetThreshold.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
            }
            return result;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0},{1},{2}:", MessageId, (BMessageType)MessageType, FSUID);
            foreach (DeviceGetThreshold value in Values)
            {
                sb.AppendFormat("{0}", value.ToString());
            }
            return sb.ToString();
        }
    }
}