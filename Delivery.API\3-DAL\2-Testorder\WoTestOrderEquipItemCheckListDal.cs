﻿
using DM.TestOrder.Common.DBA;
using DM.TestOrder.Entity;
using System.Collections.Generic;
using System.Data;
using DM.TestOrder.DAL.Helper;
using Delivery.DAL;
using DataType = Delivery.DAL.DataType;
using QueryParameter = Delivery.DAL.QueryParameter;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common;

namespace DM.TestOrder.DAL {

    public class WoTestOrderEquipItemCheckListDal
    {
        private readonly static string _workflow = ConfigHelper.GetSection("WorkFlow").Value;
        public static List<WO_TestOrderEquipItemCheckList> GetByOrderId(int orderId) {
            var items = new List<WO_TestOrderEquipItemCheckList>();
            //var sql = string.Format("WO_TestOrderEquipItemCheckList_Query {0}", orderId);
            //var tb = DBHelper.GetTable(sql);
            var tb = new DataTable();
            if (CommonUtils.IsNoProcedure){
                tb = WoTestOrderEquipItemCheckListService.Instance.WODebugCheckItemSignal(orderId);
            }
            else {
                tb = new ExecuteSql().ExecuteStoredProcedure("WO_Debug_CheckItemSignal", new QueryParameter[] {
                 new QueryParameter("OrderId", DataType.Int, orderId.ToString()) });
            }
            if (tb.Rows.Count == 0)
                return items;
            foreach (DataRow row in tb.Rows) {
                var item = WO_TestOrderEquipItemCheckListHelper.FromDataRow(row);
                items.Add(item);
            }

            return items;
        }

        public static void DBUpdate(int OrderCheckId, string PassFailReason)
        {
            //var sql = string.Format("update WO_TestOrderEquipItemCheckList set PassFailReason= {0} where OrderCheckId={1}", 
            //    SHelper.GetPara(PassFailReason), SHelper.GetPara(OrderCheckId)
            //    );
            //DBHelper.ExecuteNonQuery(sql);
            if (CommonUtils.IsNoProcedure)
            {
                Public_ExecuteSqlService.Instance.NoStore_WOTestOrderEquipItemCheckList_UpdateByOrderCheckId(OrderCheckId, PassFailReason);
            }
            else
            {
                string sql = "update WO_TestOrderEquipItemCheckList set PassFailReason=@PassFailReason where OrderCheckId=@OrderCheckId";
                new ExecuteSql().ExecuteSQLNoQuery(sql, new QueryParameter[] {
                 new QueryParameter("PassFailReason", DataType.String, PassFailReason.ToString()),
                 new QueryParameter("OrderCheckId", DataType.Int, OrderCheckId.ToString())
                });
            }
        
        }
        
        public static void Refresh(int orderId) {

            if (CommonUtils.IsNoProcedure)
            {
                WoTestOrderEquipItemCheckListService.Instance.RefreshSignal(orderId);
                //RefreshEvent 负责这个存储过程的同事在此添加
                WoTestOrderEquipItemCheckListService.Instance.RefreshEvent(orderId);

                WoTestOrderEquipItemCheckListService.Instance.RefreshCmd(orderId);
                return;
            }




            ExecuteSql executeSql = new ExecuteSql();
            if (_workflow == "true")
            {
                executeSql.ExecuteStoredProcedureNoQuery("WO_EquipItemCheckList_RefreshSignal1", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, orderId.ToString())
                });
                executeSql.ExecuteStoredProcedureNoQuery("WO_EquipItemCheckList_RefreshEvent1", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, orderId.ToString())
                });
                executeSql.ExecuteStoredProcedureNoQuery("WO_EquipItemCheckList_RefreshCmd1", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, orderId.ToString())
                });
            }
            else {
                executeSql.ExecuteStoredProcedureNoQuery("WO_EquipItemCheckList_RefreshSignal", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, orderId.ToString())
                });
                executeSql.ExecuteStoredProcedureNoQuery("WO_EquipItemCheckList_RefreshEvent", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, orderId.ToString())
                });
                executeSql.ExecuteStoredProcedureNoQuery("WO_EquipItemCheckList_RefreshCmd", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, orderId.ToString())
                });
            }
            

            //var sql = string.Format("WO_EquipItemCheckList_RefreshSignal {0}",orderId );
            //DBHelper.ExecuteNonQuery(sql);

            //sql = string.Format("WO_EquipItemCheckList_RefreshEvent {0}", orderId);
            //DBHelper.ExecuteNonQuery(sql);


            //sql = string.Format("WO_EquipItemCheckList_RefreshCmd {0}", orderId);
            //DBHelper.ExecuteNonQuery(sql);

        }

        
    }
}
