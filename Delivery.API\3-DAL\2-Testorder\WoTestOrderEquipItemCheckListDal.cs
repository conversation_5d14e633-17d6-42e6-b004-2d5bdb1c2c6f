﻿
using DM.TestOrder.Common.DBA;
using DM.TestOrder.Entity;
using System.Collections.Generic;
using System.Data;
using DM.TestOrder.DAL.Helper;
using Delivery.DAL;
using DataType = Delivery.DAL.DataType;
using QueryParameter = Delivery.DAL.QueryParameter;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common;
using System;
using System.Linq;

namespace DM.TestOrder.DAL {

    public class WoTestOrderEquipItemCheckListDal
    {
        private readonly static string _workflow = ConfigHelper.GetSection("WorkFlow").Value;
        public static List<WO_TestOrderEquipItemCheckList> GetByOrderId(int orderId) {
            var items = new List<WO_TestOrderEquipItemCheckList>();
            //var sql = string.Format("WO_TestOrderEquipItemCheckList_Query {0}", orderId);
            //var tb = DBHelper.GetTable(sql);
            var tb = new DataTable();
            if (CommonUtils.IsNoProcedure){
                tb = WoTestOrderEquipItemCheckListService.Instance.WODebugCheckItemSignal(orderId);
            }
            else {
                tb = new ExecuteSql().ExecuteStoredProcedure("WO_Debug_CheckItemSignal", new QueryParameter[] {
                 new QueryParameter("OrderId", DataType.Int, orderId.ToString()) });
            }
            if (tb.Rows.Count == 0)
                return items;
            foreach (DataRow row in tb.Rows) {
                var item = WO_TestOrderEquipItemCheckListHelper.FromDataRow(row);
                items.Add(item);
            }

            return items;
        }

        public static void DBUpdate(int OrderCheckId, string PassFailReason)
        {
            //var sql = string.Format("update WO_TestOrderEquipItemCheckList set PassFailReason= {0} where OrderCheckId={1}", 
            //    SHelper.GetPara(PassFailReason), SHelper.GetPara(OrderCheckId)
            //    );
            //DBHelper.ExecuteNonQuery(sql);
            if (CommonUtils.IsNoProcedure)
            {
                Public_ExecuteSqlService.Instance.NoStore_WOTestOrderEquipItemCheckList_UpdateByOrderCheckId(OrderCheckId, PassFailReason);
            }
            else
            {
                string sql = "update WO_TestOrderEquipItemCheckList set PassFailReason=@PassFailReason where OrderCheckId=@OrderCheckId";
                new ExecuteSql().ExecuteSQLNoQuery(sql, new QueryParameter[] {
                 new QueryParameter("PassFailReason", DataType.String, PassFailReason.ToString()),
                 new QueryParameter("OrderCheckId", DataType.Int, OrderCheckId.ToString())
                });
            }
        
        }
        
        public static void Refresh(int orderId) {

            if (CommonUtils.IsNoProcedure)
            {
                WoTestOrderEquipItemCheckListService.Instance.RefreshSignal(orderId);
                //RefreshEvent 负责这个存储过程的同事在此添加
                WoTestOrderEquipItemCheckListService.Instance.RefreshEvent(orderId);

                WoTestOrderEquipItemCheckListService.Instance.RefreshCmd(orderId);
                return;
            }




            ExecuteSql executeSql = new ExecuteSql();
            if (_workflow == "true")
            {
                executeSql.ExecuteStoredProcedureNoQuery("WO_EquipItemCheckList_RefreshSignalPrep1", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, orderId.ToString())
                });
                var sql = "select * from WO_TestOrderEquipItemCheckListSignal where OrderId = @orderId and IsPass = 0";
                DataTable tb = new ExecuteSql().ExecuteSQL(sql, new QueryParameter[] {
                new QueryParameter("orderId", DataType.Int, orderId.ToString())
                });
                Dictionary<string, float> singalValues = new Dictionary<string, float>();
                if (tb.Rows.Count != 0) {
                    foreach (DataRow row in tb.Rows)
                    {
                        string singal = row.Field<int>("EquipmentId").ToString() + "." + row.Field<int>("signalId").ToString();
                        singalValues.Add(singal, 0);
                    }
                }
                // 组装redis查询参数
                List<string> keys = new List<string>();
                foreach (var key in singalValues.Keys) {
                    string esId = string.Format("RealTimeSignal:{0}", key);
                    keys.Add(esId);
                }
                List<string> values = RedisHelper.GetValues(keys);
                if (values != null) {
                    foreach (string value in values)
                    {
                        if (string.IsNullOrWhiteSpace(value))
                            continue;
                        // 处理引号
                        string[] valueArray = value.Trim('"').Split(new[] { '~' }, StringSplitOptions.RemoveEmptyEntries);
                        if (valueArray.Count() >= 5)
                        {
                            string key = string.Format("{0}.{1}", valueArray[0], valueArray[1]);// equipmentId + "." + signalId
                            if (singalValues.ContainsKey(key)) {
                                var sql1 = string.Format("update WO_TestOrderEquipItemCheckListSignal set FloatValue = {0},SamplerTime = '{1}' where orderId = {2} and IsPass = 0 and EquipmentId = {3} and signalId = {4}", valueArray[2], valueArray[3], orderId,valueArray[0],valueArray[1]);
                                new ExecuteSql().ExecuteSQLNoQuery(sql1);
                            }
                                
                        }
                    }
                }

                // 更新表中IsPass状态，并判断是否在设置范围内
                executeSql.ExecuteStoredProcedureNoQuery("WO_EquipItemCheckList_RefreshSignal1", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, orderId.ToString()) });



                executeSql.ExecuteStoredProcedureNoQuery("WO_EquipItemCheckList_RefreshEvent1", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, orderId.ToString())
                });
                executeSql.ExecuteStoredProcedureNoQuery("WO_EquipItemCheckList_RefreshCmd1", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, orderId.ToString())
                });
            }
            else {
                executeSql.ExecuteStoredProcedureNoQuery("WO_EquipItemCheckList_RefreshSignal", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, orderId.ToString())
                });
                executeSql.ExecuteStoredProcedureNoQuery("WO_EquipItemCheckList_RefreshEvent", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, orderId.ToString())
                });
                executeSql.ExecuteStoredProcedureNoQuery("WO_EquipItemCheckList_RefreshCmd", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, orderId.ToString())
                });
            }
            

            //var sql = string.Format("WO_EquipItemCheckList_RefreshSignal {0}",orderId );
            //DBHelper.ExecuteNonQuery(sql);

            //sql = string.Format("WO_EquipItemCheckList_RefreshEvent {0}", orderId);
            //DBHelper.ExecuteNonQuery(sql);


            //sql = string.Format("WO_EquipItemCheckList_RefreshCmd {0}", orderId);
            //DBHelper.ExecuteNonQuery(sql);

        }

        
    }
}
