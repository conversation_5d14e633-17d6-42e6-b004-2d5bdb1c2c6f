﻿
using DM.TestOrder.Common.DBA;
using Newtonsoft.Json.Linq;
using DM.TestOrder.DAL;
using Delivery.API;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System;

namespace DM.TestOrder.Controllers {
     
    public class InstallClerkController : BaseController{


 
        [HttpGet]
        public JsonResult Get(int companyId=-1, string kwClerk = "") {
            kwClerk = SHelper.RegulateParam(kwClerk);

            //Debug.WriteLine(companyId + "/t" + kwClerk);

            if (!string.IsNullOrEmpty(kwClerk))
                kwClerk = kwClerk.Replace("\'", "");

            var dt = WoInstallClerkDal.GetByKeywordForOneCompany(companyId, kwClerk); 
            DataTableColumnMapper.RenameColumns(dt, InstallFieldMap);

            //var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(dt);

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};
            return new JsonResult(dt);

        }

        public static readonly Dictionary<string, string> InstallFieldMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            {"companyid","CompanyId"},
            {"companyname","CompanyName"},
            {"clerkid","ClerkId"},
            {"clerkname","ClerkName"}
        };

        //未用，暂时保留
        [HttpPost]
        public JsonResult Post([FromBody]JObject value) {
            var kwCompany = value["KwCompany"].ToString();
            var kwClerk = value["KwClerk"].ToString();

            kwCompany = SHelper.RegulateParam(kwCompany);
            kwClerk = SHelper.RegulateParam(kwClerk);


            var dt = WoInstallClerkDal.GetByKeyword(kwCompany, kwClerk);
            //var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(dt);

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};
            return new JsonResult(dt);

        }

        //作废
        //http://localhost:56324/api/InstallClerk?kwCompany=1&kwClerk=tom
        //public HttpResponseMessage Get(string kwCompany = "", string kwClerk = "") {
        //    MyLogger.Info(kwCompany);

        //    Debug.WriteLine(kwCompany + "/t" + kwClerk);

        //    if (!string.IsNullOrEmpty(kwCompany))
        //        kwCompany = kwCompany.Replace("\'", "");
        //    if (!string.IsNullOrEmpty(kwClerk))
        //        kwClerk = kwClerk.Replace("\'", "");

        //    var dt = InstallClerkDal.GetByKeyword(kwCompany, kwClerk);
        //    var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(dt);

        //    return new HttpResponseMessage() {
        //        Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
        //    };

        //}
    }
}
