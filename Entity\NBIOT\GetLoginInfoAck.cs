﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 获取FSU注册信息应答
    /// </summary>
    public class GetLoginInfoAck : BMessage
    {
        public string UserName { get; set; }

        public string PassWord { get; set; }

        public string FSUIP { get; set; }

        public string FSUVER { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public string FSUMAC { get; set; }

        public string SiteID { get; set; }

        public string RoomID { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public string SiteName { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public string RoomName { get; set; }

        /// <summary>
        /// 返回结果
        /// </summary>
        public EnumResult Result { get; set; }

        /// <summary>
        /// 失败的原因
        /// </summary>
        public string FailureCause { get; set; }

        public GetLoginInfoAck() : base()
        {
            MessageType = (int)BMessageType.GET_LOGININFO_ACK;
        }

        public GetLoginInfoAck(string userName, string password, string fsuId, string fsuIp, string fsuVer, string fsuMac, string siteId,
           string roomId, string siteName, string roomName, EnumResult result, string failureCause)
            : base()
        {
            MessageType = (int)BMessageType.GET_LOGININFO_ACK;

            UserName = userName;
            PassWord = password;
            FSUID = fsuId;
            FSUIP = fsuIp;
            FSUVER = fsuVer;
            FSUMAC = fsuMac;
            SiteID = siteId;
            RoomID = roomId;
            SiteName = siteName;
            RoomName = roomName;
            Result = result;
            FailureCause = failureCause;
        }

        public static GetLoginInfoAck Deserialize(string json)
        {
            GetLoginInfoAck info = null;
            try
            {
                info = JsonConvert.DeserializeObject<GetLoginInfoAck>(json);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetLoginInfoAck.Deserialize:{0}", ex.Message);
                entityLogger.ErrorFormat("GetLoginInfoAck.Deserialize:{0}", ex.StackTrace);
                info = new GetLoginInfoAck();
                info.ErrorMsg = ex.Message;
                info.StringXML = json;
            }
            return info;
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}, {2}:{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13}",
                MessageId, (BMessageType)MessageType, FSUID, UserName, PassWord, FSUIP, FSUVER, FSUMAC, SiteID, RoomID, SiteName, RoomName, Result, FailureCause);
        }
    }
}