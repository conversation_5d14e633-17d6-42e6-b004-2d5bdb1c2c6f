﻿

using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using Delivery.DAL;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;


namespace DM.TestOrder.DAL {
    public class StationTypeMapDal
    {
        public static DataTable  GetAll() {
            var sql = "WO_CR2_DicStationTypeMap_GetAll";
            //var tb = DBHelper.GetTable(sql);
            var tb = new ExecuteSql().ExecuteStoredProcedure(sql, new QueryParameter[0]);
            return tb;
        }

        public static string AddOne(int WR_ItemId, int SiteWeb_ItemId) {
            if (CommonUtils.IsNoProcedure)
            {
                var rtn1 = WoCommonService.Instance.WO_CR2_DicStationTypeMap_Add(WR_ItemId, SiteWeb_ItemId);
                return rtn1 == null ? null : rtn1.ToString();
            }
            var sql = string.Format("WO_CR2_DicStationTypeMap_Add {0},{1}", WR_ItemId, SiteWeb_ItemId);

            //var rtn = DBHelper.ExecuteScalar(sql);
            var rtn = new ExecuteSql().ExecuteStoredProcedureScalar("WO_CR2_DicStationTypeMap_Add", new QueryParameter[]
            {
                new QueryParameter("WR_ItemId", DataType.Int, WR_ItemId.ToString()),
                new QueryParameter("SiteWeb_ItemId", DataType.Int, SiteWeb_ItemId.ToString())
            });
            return rtn == null ? null : rtn.ToString();
        }
        //public static string DeleteOne(int ItemId) {
        //    var sql = string.Format("update  WR_DataItem set ExtendField3='delete' where  EntryId=6 and  ItemId={0}", ItemId);
        //    DBHelper.ExecuteNonQuery(sql);
        //    return "OK";
        //} 
    }
}

