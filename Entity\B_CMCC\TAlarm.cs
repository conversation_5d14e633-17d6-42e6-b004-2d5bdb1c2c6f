﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 告警消息的结构
    /// </summary>
    public sealed class TAlarm 
    {
        /// <summary>
        /// 告警序号
        /// </summary>
        public string SerialNo { get; set; }
        /// <summary>
        /// 监控点ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceID { get; set; }

        /// <summary>
        /// 网管告警编号 （告警标准化编号）
        /// </summary>
        public string NMAlarmID { get; set; }
        /// <summary>
        /// 告警时间
        /// </summary>
        public DateTime? AlarmTime { get; set; }
        /// <summary>
        /// 告警级别
        /// </summary>
        public EnumState AlarmLevel { get; set; }
        /// <summary>
        /// 告警标志
        /// </summary>
        public EnumFlag AlarmFlag { get; set; }
        /// <summary>
        /// 告警的事件描述
        /// </summary>
        public string AlarmDesc { get; set; }
        /// <summary>
        /// 告警触发值
        /// </summary>
        public float? EventValue { get; set; }
        /// <summary>
        /// 同设备同类测点顺序号
        /// </summary>
        public string SignalNumber { get; set; }
        /// <summary>
        /// 预留字段
        /// </summary>
        public string AlarmRemarK { get; set; }


        #region SiteWeb配置

        public int MyStationId { get; set; }
        public int MyDeviceId { get; set; }
        public int MyEventId { get; set; }
        public int MyConditionId { get; set; }

        /// <summary>
        /// 实体中的SerialNo可能不唯一，因此增加MySerialNo来标识(MySerialNo=StationId.EquipmentId.EventId.EventConditionId.SerialNo)
        /// </summary>
        public string MySerialNo { get; set; }

        public EnumLevel MyAlarmLevel { get; set; }

        #endregion

        /// <summary>
        /// 告警消息的结构
        /// </summary>
        public TAlarm() { }
        /// <summary>
        /// 告警消息的结构
        /// </summary>
        /// <param name="serialNo">告警序号</param>
        /// <param name="id">监控点ID</param>
        /// <param name="deviceId">设备ID</param>
        /// <param name="nmAlarmId">网管告警编号 （告警标准化编号</param>
        /// <param name="alarmTime">告警时间，YYYY-MM-DD<SPACE键>hh:mm:ss（采用24小时的时间制式）</param>
        /// <param name="alarmLevel">告警级别</param>
        /// <param name="alarmFlag">告警标志</param>
        /// <param name="alarmDesc">告警的事件描述</param>
        /// <param name="eventValue">告警触发值</param>
        /// <param name="alarmRemark1">预留字段1</param>
        /// <param name="alarmRemark2">预留字段2</param>
        public TAlarm(string serialNo, string id, string deviceId, string nmAlarmId, DateTime? alarmTime, EnumState alarmLevel, EnumFlag alarmFlag,
            string alarmDesc, float? eventValue, string signalNumber, string alarmRemark)
        {
            SerialNo = serialNo;
            ID = id;
            DeviceID = deviceId;
            NMAlarmID = nmAlarmId;
            AlarmTime = alarmTime;
            AlarmLevel = alarmLevel;
            AlarmFlag = alarmFlag;
            AlarmDesc = alarmDesc;
            EventValue = eventValue;
            SignalNumber = signalNumber;
            AlarmRemarK = alarmRemark;
            //告警等级转换
            if(AlarmLevel == EnumState.CRITICAL)
            {
                MyAlarmLevel = EnumLevel.CRITICAL;
            }
            else if(AlarmLevel == EnumState.MAJOR)
            {
                MyAlarmLevel = EnumLevel.MAJOR;
            }
            else if (AlarmLevel == EnumState.MINOR)
            {
                MyAlarmLevel = EnumLevel.MINOR;
            }
            else if (AlarmLevel == EnumState.HINT)
            {
                MyAlarmLevel = EnumLevel.HINT;
            }
            else
            {
                MyAlarmLevel = EnumLevel.INVALID;
            }
        }

        /// <summary>
        /// 告警消息的结构(使用EnumLevel)
        /// </summary>
        /// <param name="serialNo">告警序号</param>
        /// <param name="id">监控点ID</param>
        /// <param name="deviceId">设备ID</param>
        /// <param name="nmAlarmId">网管告警编号 （告警标准化编号</param>
        /// <param name="alarmTime">告警时间，YYYY-MM-DD<SPACE键>hh:mm:ss（采用24小时的时间制式）</param>
        /// <param name="alarmLevel">告警级别</param>
        /// <param name="alarmFlag">告警标志</param>
        /// <param name="alarmDesc">告警的事件描述</param>
        /// <param name="eventValue">告警触发值</param>
        /// <param name="alarmRemark1">预留字段1</param>
        /// <para
        public TAlarm(string serialNo, string id, string deviceId, string nmAlarmId, DateTime? alarmTime, EnumLevel alarmLevel, EnumFlag alarmFlag,
                   string alarmDesc, float? eventValue, string signalNumber, string alarmRemark)
        {
            SerialNo = serialNo;
            ID = id;
            DeviceID = deviceId;
            NMAlarmID = nmAlarmId;
            AlarmTime = alarmTime;
            MyAlarmLevel = alarmLevel;
            AlarmFlag = alarmFlag;
            AlarmDesc = alarmDesc;
            EventValue = eventValue;
            SignalNumber = signalNumber;
            AlarmRemarK = alarmRemark;
            //告警等级转换
            if (MyAlarmLevel == EnumLevel.CRITICAL)
            {
                AlarmLevel = EnumState.CRITICAL;
            }
            else if (MyAlarmLevel == EnumLevel.MAJOR)
            {
                AlarmLevel = EnumState.MAJOR;
            }
            else if (MyAlarmLevel == EnumLevel.MINOR)
            {
                AlarmLevel = EnumState.MINOR;
            }
            else if (MyAlarmLevel == EnumLevel.HINT)
            {
                AlarmLevel = EnumState.HINT;
            }
        }

        public override string ToString()
        {
            return String.Format(
                "[{0}, {1} ,{2} , {3}, {4}, {5}, {6}, {7}, {8}, {9}, {10}]",
                SerialNo, ID, DeviceID, NMAlarmID, AlarmTime.HasValue?Convert.ToDateTime(AlarmTime).ToString("yyyy-MM-dd HH:mm:ss"):"", AlarmLevel, AlarmFlag,
                AlarmDesc, EventValue, SignalNumber, AlarmRemarK);
        }

    }
}
