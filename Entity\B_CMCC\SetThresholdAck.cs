﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    ///  5.6.6.写监控点门限数据应答
    /// </summary>
    public sealed class SetThresholdAck:BMessage
    {
        public EnumResult Result { get; private set; }

        public string FailureCause { get; private set; }

        public List<DeviceSetThresholdAck> Devices { get; private set; }

        #region SiteWeb配置

        public int MyStationId { get; set; }

        #endregion

        public SetThresholdAck(string fsuId, EnumResult result, string failureCause, List<DeviceSetThresholdAck> devices)
            : base()
        {
            MessageType = (int)BMessageType.SET_THRESHOLD_ACK;

            FSUID = fsuId;
            Result = result;
            FailureCause = failureCause;
            Devices = devices;
        }

        public SetThresholdAck() : base() 
        {
            MessageType = (int)BMessageType.SET_THRESHOLD_ACK;
        }

        public static SetThresholdAck Deserialize(XmlDocument xmldoc)
        {
            SetThresholdAck setThresholdAck = null;
            try
            {
                string fsuId = xmldoc.SelectSingleNode("/Response/Info/FSUID").InnerText;
                string result = xmldoc.SelectSingleNode("/Response/Info/Result").InnerText;
                EnumResult enumResult = EnumResult.SUCCESS;
                if (result != null && result != "")
                {
                    enumResult = (EnumResult)int.Parse(result);
                }
                string failureCause = xmldoc.SelectSingleNode("/Response/Info/FailureCause").InnerText;

                XmlNodeList nodelist = xmldoc.SelectNodes("/Response/Info/DeviceList/Device");

                List<DeviceSetThresholdAck> deviceSetThresholdAckList = new List<DeviceSetThresholdAck>();

                foreach (XmlNode xn in nodelist)//遍历所有子节点 
                {
                    XmlElement xe = (XmlElement)xn;
                    string deviceId = xe.GetAttribute("ID");

                    List<TSignalMeasurementId> successList = new List<TSignalMeasurementId>();
                    List<TSignalMeasurementId> failList = new List<TSignalMeasurementId>();

                    XmlNode xns = xe.SelectSingleNode("SuccessList");
                    if (xns != null)
                    {
                        foreach (XmlNode xnSub in xns.ChildNodes)
                        {
                            TSignalMeasurementId tsmid = new TSignalMeasurementId();
                            tsmid.ID = xnSub.Attributes["ID"].Value.Trim();
                            string strSignalNumber = xnSub.Attributes["SignalNumber"].Value.Trim();
                            if (string.IsNullOrEmpty(strSignalNumber) || strSignalNumber.ToUpper() == "NULL")
                            {
                                entityLogger.ErrorFormat("SetThresholdAck.Deserialize():ID = {0}, SignalNumber is NullOrEmpty", tsmid.ID);
                            }
                            else
                            {
                                tsmid.SignalNumber = strSignalNumber;
                            }
                            successList.Add(tsmid);
                        }
                    }
                    XmlNode xnf = xe.SelectSingleNode("FailList");
                    if (xnf != null)
                    {
                        foreach (XmlNode xnSub in xnf.ChildNodes)
                        {
                            TSignalMeasurementId tsmid = new TSignalMeasurementId();
                            tsmid.ID = xnSub.Attributes["ID"].Value.Trim();
                            string strSignalNumber = xnSub.Attributes["SignalNumber"].Value.Trim();
                            if (string.IsNullOrEmpty(strSignalNumber) || strSignalNumber.ToUpper() == "NULL")
                            {
                                entityLogger.ErrorFormat("SetThresholdAck.Deserialize():ID = {0}, SignalNumber is NullOrEmpty", tsmid.ID);
                            }
                            else
                            {
                                tsmid.SignalNumber = strSignalNumber;
                            }
                            failList.Add(tsmid);
                        }
                    }
                    DeviceSetThresholdAck deviceSetThresholdAck = new DeviceSetThresholdAck(deviceId, successList, failList);
                    deviceSetThresholdAckList.Add(deviceSetThresholdAck);
                }
                setThresholdAck = new SetThresholdAck(fsuId, enumResult, failureCause, deviceSetThresholdAckList);
                setThresholdAck.StringXML = xmldoc.InnerXml;
                entityLogger.DebugFormat("SetThresholdAck.Deserialize(),xml:\r\n{0}", FormatXml(xmldoc.InnerXml));
                return setThresholdAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetThresholdAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetThresholdAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmldoc.InnerXml));
                setThresholdAck = new SetThresholdAck();
                setThresholdAck.ErrorMsg = ex.Message;
                setThresholdAck.StringXML = xmldoc.InnerXml;
                return setThresholdAck;
            }  
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();

            sb.AppendFormat("{0}, {1}, {2}, {3}:",
                MessageId, (BMessageType)MessageType, FSUID, Devices != null ? Devices.Count : 0);
            sb.AppendLine();
            if (Devices != null)
            {
                foreach (DeviceSetThresholdAck item in Devices)
                {
                    sb.AppendFormat("{0}", item.ToString());
                    sb.AppendLine();
                }
            }

            return sb.ToString();
        }

    }

    /// <summary>
    /// 设备设置门限应答数据结构
    /// </summary>
    public sealed class DeviceSetThresholdAck
    {
        public string DeviceId { get; private set; }

        public List<string> SuccessIds { get; private set; }
        public List<string> FailIds { get; private set; }

        public List<TSignalMeasurementId> SuccessIds2 { get; private set; }
        public List<TSignalMeasurementId> FailIds2 { get; private set; }

        #region SiteWeb配置

        public int MyDeviceId { get; set; }

        #endregion

        public DeviceSetThresholdAck(string deviceId, List<TSignalMeasurementId> successIds, List<TSignalMeasurementId> failIds)
        {
            DeviceId = deviceId;

            SuccessIds2 = successIds;
            FailIds2 = failIds;

            SuccessIds = new List<string>();
            FailIds = new List<string>();

            if (SuccessIds2 != null && SuccessIds2.Count > 0)
            {
                foreach (TSignalMeasurementId tsmid in SuccessIds2)
                {
                    SuccessIds.Add(tsmid.ID + tsmid.SignalNumber);
                }
            }

            if (FailIds2 != null && FailIds2.Count > 0)
            {
                foreach (TSignalMeasurementId tsmid in FailIds2)
                {
                    FailIds.Add(tsmid.ID + tsmid.SignalNumber);
                }
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}", DeviceId);
            sb.AppendLine();

            sb.Append("SuccessList:");
            sb.AppendLine();
            foreach (TSignalMeasurementId item in SuccessIds2)
            {
                sb.AppendFormat("{0}", item);
                sb.AppendLine();
            }

            sb.Append("FailList:");
            sb.AppendLine();
            foreach (TSignalMeasurementId item in FailIds2)
            {
                sb.AppendFormat("{0}", item);
                sb.AppendLine();
            }

            return sb.ToString();
        }

    }

}
