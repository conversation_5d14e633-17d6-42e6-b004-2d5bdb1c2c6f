﻿<?xml version="1.0" encoding="utf-8"?>
<?xml-stylesheet type="text/xsl" href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
		<procedure owner="" name="SP_Get_WRDevice_1" grant="">
			<parameters>
				<parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StartTime" type="string" direction="Input" size="20" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="EndTime" type="string" direction="Input" size="20" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="DeviceCategory" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="DeviceSubCategory" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="DeviceName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="DeviceCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="WRFsuId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<replaces>
				<replace keyName="WhereTime">
					<![CDATA[ and a.ApplyTime >= @StartTime::timestamp and a.ApplyTime <= @EndTime::timestamp ]]>
				</replace>
				<replace keyName="WhereWRSationId">
					<![CDATA[ and a.WRStationId = @WRStationId ]]>
				</replace>
				<replace keyName="WhereDeviceCategory">
					<![CDATA[ and SUBSTRING(a.DeviceType FROM 1 FOR 2) = @DeviceCategory ]]>
				</replace>
				<replace keyName="WhereDeviceSubCategory">
					<![CDATA[  and right(a.DeviceType,2)  = @DeviceSubCategory ]]>
				</replace>
				<replace keyName="WhereDeviceName">
					<![CDATA[ and a.DeviceName like concat('%',@DeviceName,'%') ]]>
				</replace>
				<replace keyName="WhereDeviceCode">
					<![CDATA[ and a.DeviceCode like concat('%',@DeviceCode,'%') ]]>
				</replace>
				<replace keyName="WhereFsuId">
					<![CDATA[ and a.WRFsuId = @WRFsuId ]]>
				</replace>
			</replaces>
			<body>
				<![CDATA[
				SELECT
				ROW_NUMBER() OVER (ORDER BY a.ApplyTime DESC) AS RowNumber,
				a.WRDeviceId,
				a.WRStationId,
				c.StationName,
				a.WRHouseId,
				b.HouseName,
				a.WRFsuId,
				e.FsuCode,
				e.FsuName,
				SUBSTRING(a.DeviceType FROM 1 FOR 2) AS DeviceCategory,
				RIGHT(a.DeviceType, 2) AS DeviceSubCategory,
				d.ItemValue AS DeviceCategoryName,
				f.ItemValue AS DeviceSubCategoryName,
				a.DeviceCode,
				a.DeviceName,
				a.UserId,
				a.SWUserName,
				a.ApplyTime,
				a.SWStationId,
				a.SWHouseId,
				a.PortUse,
                a.EnableTime,
                a.LifeTime,  
				a.Remark
				FROM WR_DeviceManagement a
				INNER JOIN WR_HouseManagement b 
				ON a.WRStationId = b.WRStationId AND a.WRHouseId = b.WRHouseId
				INNER JOIN WR_StationManagement c 
				ON a.WRStationId = c.WRStationId
				INNER JOIN WR_DataItem d 
				ON CAST(SUBSTRING(a.DeviceType FROM 1 FOR 2) AS INTEGER) = d.ItemId 
			    AND d.EntryId = 8
				INNER JOIN WR_DataItem f 
				ON CAST(RIGHT(a.DeviceType, 2) AS INTEGER) = f.ItemId 
			    AND f.EntryId = 9
			    AND f.ParentEntryId = 8 
			    AND f.ParentItemId = d.ItemId
			 	LEFT JOIN WR_FsuManagement e 
				ON a.WRFsuId = e.WRFsuId
				WHERE 1 = 1
				$[WhereTime]
				$[WhereWRSationId]
				$[WhereDeviceCategory]
				$[WhereDeviceSubCategory]
				$[WhereDeviceCode]
				$[WhereDeviceName]
				$[WhereFsuId]
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Get_WRDevice_2" grant="">
			<parameters>
				<parameter name="LogonId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT TBL_Account.UserId
				FROM TBL_Account
				WHERE TBL_Account.LogonId = @LogonId
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Get_WRDevice_3" grant="">
			<parameters>
				<parameter name="iUserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[ SELECT 'X' FROM TBL_UserRoleMap WHERE TBL_UserRoleMap.UserId = CAST(@iUserId AS INTEGER) AND TBL_UserRoleMap.RoleId = -1 ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Get_WRDevice_4" grant="">
			<body>
				<![CDATA[
				SELECT DISTINCT
				UserId
				FROM
				WR_FsuManagement;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Get_WRDevice_5" grant="">
			<parameters>
				<parameter name="iUserId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT DISTINCT UserId FROM TBL_UserRoleMap
				WHERE TBL_UserRoleMap.RoleId IN (SELECT RoleId FROM TBL_UserRoleMap
				WHERE TBL_UserRoleMap.UserId = CAST(@iUserId  AS INTEGER));
				]]>
			</body>
		</procedure>
	</procedures>
</root>
