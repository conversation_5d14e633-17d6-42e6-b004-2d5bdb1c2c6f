﻿
$(function () {
    InitLeftMenu();
    $(document).ready(function () {

        /*
        var cusIdFromPortal = $.request.queryString["CustomerId"]
        if (cusIdFromPortal != undefined && new RegExp("^\\d+$").test(cusIdFromPortal)) {
            LoadCustomerInfo(cusIdFromPortal); 
        }
        else {
            $.messager.alert('错误', '系统异常!', 'error');
         }
        */

    });
    $('body').layout();
})

function InitLeftMenu() {
    $('.easyui-accordion li a').click(function () {
        
        //var tabTitle = $(this).text();
        var tabTitle = $(this).attr('title') ? $(this).attr('title') : $(this).text();
        if (tabTitle == "")
            return;
        //var url = $(this).attr("href");
        var url = $(this).attr("rel");
        addTab(tabTitle, url);
        $('.easyui-accordion li div').removeClass("selected");
        $(this).parent().addClass("selected");
    }).hover(function () {
        $(this).parent().addClass("hover");
    }, function () {
        $(this).parent().removeClass("hover");
    });
}

function LoadCustomerInfo(cus) {
    $.ajax({
        url: 'Handlers/MyCus/CustomerMainHandler.ashx?action=customer&cus=' + cus,
        data: null,
        success: function (result) {
            if (result != null) {
                
                $('#cusName').html('当前客户：' + result.CustomerName);
                $('#serviceName').html('由艾默生网络能源有限公司' + result.ServiceName + '负责服务');
                $('#bottom-version').html(result.CountryName + " - " + result.DisTrictName + " - " + result.ServiceName);

            }
        },
        dataType: 'json'
    });
}

function addTab(subtitle, url) {
    if (!$('#tabs').tabs('exists', subtitle)) {
        $('#tabs').tabs('add', {
            title: subtitle,
            content: createFrame(url),
            closable: true,
            width: $('#mainPanle').width() - 10,
            height: $('#mainPanle').height() - 26
        });

        //IE6下Frame Bug真他妹的多
        //http://www.cnblogs.com/xiaochaohuashengmi/archive/2010/08/12/1797797.html
        if (navigator.userAgent.indexOf("MSIE 6.0") > -1)//浏览器判断 如果是IE6,再重新加载一次Iframe
        {
            var ie6reloadTabFrame = $('#tabs').tabs('getTab', subtitle).find('iframe')[0];
            if (ie6reloadTabFrame != null && ie6reloadTabFrame != undefined)
                ie6reloadTabFrame.contentWindow.location.href = url;
        }
    } else {
        $('#tabs').tabs('select', subtitle);
        $('#tabs').tabs('update', { tab: $('#tabs').tabs('getSelected'), options: { content: '<iframe id="mytabframe"  frameborder="0"  scrolling="auto"  style="width:100%;height:99%;" src="' + url + '"></iframe>' } })
    }
}

function createFrame(url) {
    var s = '<iframe id="mytabframe"  frameborder="0"  scrolling="auto"  style="width:100%;height:99%;" src="' + url + '"></iframe>';
    return s;
}