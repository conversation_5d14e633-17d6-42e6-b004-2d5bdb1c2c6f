﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    /// <summary>
    /// SU向SC登出请求报文
    /// </summary>
    public sealed class Logout : BMessage
    {
        public Logout(string suId, string surId): base()
        {
            MessageType = (int)BMessageType.LOGOUT;
            SUId = suId;
            SURId = surId;
        }

        public Logout(): base()
        {
            MessageType = (int)BMessageType.LOGOUT;
        }

        public static Logout Deserialize(XmlDocument xmlDoc)
        {
            Logout logout = null;
            try
            {
                string suId = xmlDoc.SelectSingleNode("/Request/Info/SUId").InnerText.Trim();
                string surId = xmlDoc.SelectSingleNode("/Request/Info/SURId").InnerText.Trim();
                logout = new Logout(suId, surId);
                entityLogger.DebugFormat("Logout.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                logout.StringXML = xmlDoc.InnerXml;
                return logout;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("Logout.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("Logout.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                logout = new Logout();
                logout.ErrorMsg = ex.Message;
                logout.StringXML = xmlDoc.InnerXml;
                return logout;
            }
        }

        public override string ToString()
        {
            return String.Format("Logout:{0}, {1}, {2}", MessageId, (BMessageType)MessageType, SUId);
        }

    }

}
