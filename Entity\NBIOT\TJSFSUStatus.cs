﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class TJSFSUStatus
    {
        /// <summary>
        /// CPU使用率
        /// </summary>
        public string CPUUsage { get; set; }

        /// <summary>
        /// 内存使用率
        /// </summary>
        public string MEMUsage { get; set; }

        /// <summary>
        /// FSU硬盘占用率(含SD卡等存储介质)
        /// </summary>
        public string HardDiskUsage { get; set; }

        public TJSFSUStatus()
        { }

        public TJSFSUStatus(string cpuUsage, string memUsage, string hardDiskUsage)
        {
            CPUUsage = cpuUsage;
            MEMUsage = memUsage;
            HardDiskUsage = hardDiskUsage;
        }
    }
}
