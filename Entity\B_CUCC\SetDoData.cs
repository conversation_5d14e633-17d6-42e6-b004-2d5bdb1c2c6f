﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class SetDoData : BMessage
    {
        public string DeviceId { get; set; }
        public string DeviceRId { get; set; }
        public string Id { get; set; }

        #region 20161205
        public int UserId { get; set; }
        public DateTime StartTime { get; set; }
        public int ControlId { get; set; }
        #endregion

        public int MyStationId  { get; set;}
            
        public int MyDeviceId   { get; set;}

        public int MySignalId { get; set; }

        public SetDoData(string suids, string surids, string deviceId, string deviceRId, string id) : base()
        {
            MessageType = (int)BMessageType.SET_DODATA;
            SUId = suids;
            SURId = surids;
            DeviceId = deviceId;
            DeviceRId = deviceRId;
            Id = id;
        }
        public override string Serialize()
        {
            try
            {
                XmlDocument xmlDoc = CreateXmlDocument();
                AppendPK_Type(xmlDoc, "Request", BMessageType.SET_DODATA.ToString(), MessageType.ToString());
                XmlElement xel = xmlDoc.CreateElement("Info");
                XmlElement xelone = xmlDoc.CreateElement("SUId");
                XmlElement xeltwo = xmlDoc.CreateElement("SURId");
                //XmlElement xelthree = xmlDoc.CreateElement("DeviceId");
                //XmlElement xelfour = xmlDoc.CreateElement("DeviceRId");
                //XmlElement xelfive = xmlDoc.CreateElement("Id");
                xelone.InnerText = SUId;
                xeltwo.InnerText = SURId;

                XmlElement xelDevice = xmlDoc.CreateElement("Device");
                xelDevice.SetAttribute("Id", DeviceId);
                xelDevice.SetAttribute("RId", DeviceRId);
                XmlElement xelSignal = xmlDoc.CreateElement("Signal");
                xelSignal.SetAttribute("Id", Id);
                xelDevice.AppendChild(xelSignal);

                //xelthree.InnerText = DeviceId;
                //xelfour.InnerText = DeviceRId;
                //xelfive.InnerText = Id;
                xel.AppendChild(xelone);
                xel.AppendChild(xeltwo);
                //xel.AppendChild(xelthree);
                //xel.AppendChild(xelfour);
                //xel.AppendChild(xelfive);
                xel.AppendChild(xelDevice);
                XmlNode node = xmlDoc.SelectSingleNode("Request");
                node.AppendChild(xel);
                StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("SetDoData.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmlDoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetDoData.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetDoData.Serialize() Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1},{2},{3},{4},{5},{6}", MessageId, (BMessageType)MessageType, SUId, SURId, DeviceId, DeviceRId, Id);
            return sb.ToString();
        }
    }
}
