﻿'use strict';

//global
var cookie = getCookie();
var userRole = getUserRole();
// WorkFlow配置缓存
var isWorkFlowEnabled = false;

// 获取应用配置信息
var getAppSettings = function () {
    $.ajax({
        type: "GET",
        url: `${WORKFLOW_HANDLER}/GetAppSettings`,
        dataType: 'json',
        async: false,
        success: function (result) {
            isWorkFlowEnabled = result.Data === 'true';
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            console.error('获取应用配置失败：', errorThrown);
            isWorkFlowEnabled = false; // 默认为false
        }
    });
};

// 获取资产站址列表
var getAssetStationList = function () {
    if (!isWorkFlowEnabled) {
        return; // 如果WorkFlow未启用，则不获取资产站址列表
    }

    // 确保combobox已经初始化
    if (!$('#cboAssetStation').hasClass('combobox-f')) {
        console.warn('combobox尚未初始化，延迟重试...');
        setTimeout(getAssetStationList, 100);
        return;
    }

    $.ajax({
        type: "GET",
        url: `${STATION_HANDLER}/GetAssetStation`,
        dataType: 'text', // 明确指定为text，避免jQuery自动解析
        async: true,
        success: function (data) {
            try {
                // 手动解析JSON
                var stationData = JSON.parse(data);
                console.log('获取到的站址数据：', stationData);
                $('#cboAssetStation').combobox('loadData', stationData);
            } catch (e) {
                console.error('JSON解析失败：', e);
                console.error('原始数据：', data);
                // 加载空数据，避免控件出错
                $('#cboAssetStation').combobox('loadData', []);
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            console.error('获取资产站址列表失败：', errorThrown);
            // 加载空数据，避免控件出错
            $('#cboAssetStation').combobox('loadData', []);
        }
    });
};

// 初始化和控制站址名称控件显示
var initStationNameControls = function () {
    if (isWorkFlowEnabled) {
        // WorkFlow启用时，初始化并显示下拉选择框，隐藏输入框
        // 手动初始化combobox（因为HTML中没有easyui-combobox类）
        $('#cboAssetStation').combobox({
            valueField: 'ItemId',
            textField: 'ItemValue',
            prompt: '请选择站址',
            required: true
        });
        document.getElementById('cboAssetStationTD').style.display = 'block';

    } else {

        document.getElementById('txtStationNameTD').style.display = 'block';
    }
};

//加载站址信息列表
var loadStationManagementData = function () {
    $('#StationTable').datagrid({
        method: 'GET',
        url: `${STATION_HANDLER}/GetStationInfo`,
        title: '站址信息列表',  //rownumbers: true, //行号  striped: true, //隔行变色                
        idField: 'StationCode', //主键
        singleSelect: true,
        pagination: true,
        pageSize: 30,
        pageList: [30],
        queryParams: {
            //action: 'GetStationInfo',
            StructureId: $('#StructureIdLook').combotree('getValue'),
            StationName: $('#txtStationNameq').val(),
            StationCode: $('#txtStationCodeq').val(),
            strBegin: $('#txtApplyTimeq').val(),
            strEnd: $('#txtApplyTimeq2').val(),
            StationType: $('#StationCategoryq').combobox('getValue'),
            StationStatus: $('#StationStatusq').combobox('getValue')
        },
        width: function () { return document.body.clientWidth * 0.9; }, //设置表格宽度为客户端页面的0.9
        height: 'auto',
        //fitColumns: true,
        columns: [[
            { field: 'RowNumber', title: '序号', width: 40, align: 'center' },
            { field: 'StructureName', title: '分组', width: 80, align: 'left' },
            { field: 'StationName', title: '站址名称', width: 100, align: 'left' },
            { field: 'StationCode', title: '站址编码', width: 100, align: 'left' },
            { field: 'StationCategoryName', title: '站址类型', width: 100, align: 'left' },
            { field: 'ContractNo', title: '合同号', width: 100, align: 'left' },
            { field: 'ProjectName', title: '工程名称', width: 100, align: 'left' },
            {
                title: '申请单状态', field: 'StatusName', width: 80, align: 'center', halign: 'center',
                formatter: function (value, row, index) {
                    if (value === '申请退回')
                        return '<font color="red">' + value + '</font>';
                    else if (value === '申请通过')
                        return '<font color="green">' + value + '</font>';
                    else
                        return value;
                }
            },
            { field: 'UserName', title: '申请人', width: 80, align: 'left' },
            { field: 'ApplyTime', title: '申请日期', width: 120, align: 'left' },
            { field: 'ApproveTime', title: '批准日期', width: 120, align: 'left' },
            { field: 'CountyName', title: '行政区', width: 80, align: 'left' },
            { field: 'RejectCause', title: '退回原因', width: 120, align: 'left' }
        ]],
        onClickRow: function (index, row) {
            if (row.StationStatus === '1'  ) {
                $('#btn_Pass').removeAttr('disabled');
                $('#btn_Pass').css({ 'background-color': '' });
                $('#btn_back').removeAttr('disabled');
                $('#btn_back').css({ 'background-color': '' });
            } else if (row.StationStatus === '2' || row.StationStatus === '3' ) {
                $('#btn_Pass').attr('disabled', 'disabled');
                $('#btn_Pass').css({ 'background-color': 'Silver' });
                $('#btn_back').attr('disabled', 'disabled');
                $('#btn_back').css({ 'background-color': 'Silver' });
            }
        },
        onLoadError: function () {
            alertInfo('查询出错！', 'error');
        }
    });
};

//时间校验
var checkDateValid = function () {
    if (!$('#txtApplyTimeq').val() && $('#txtApplyTimeq2').val()
        || $('#txtApplyTimeq').val() && !$('#txtApplyTimeq2').val()) {
        alertInfo('申请开始日期和申请截止日期必须同时存在！');
        return false;
    }
    if ($('#txtApplyTimeq').datetimebox('isValid') && $('#txtApplyTimeq2').datetimebox('isValid'))
        return true;
    alertInfo('请输入正确的查询条件！');
    return false;
};

//新增/编辑检查输入
var checkinfo = function () {
    if ($('#StructureId').combobox('getValue') === '-1') {
        alertInfo('所属分组没选！');
        return false;
    }
    if ($('#Province').combobox('getValue') === '-1') {
        alertInfo('省份没选！');
        return false;
    }
    if ($('#City').combobox('getValue') === '-1') {
        alertInfo('所属市没选！');
        return false;
    }
    if ($('#County').combobox('getValue') === '-1') {
        alertInfo('所属区县没选！');
        return false;
    }
    if ($('#StationCategory').combobox('getValue') === '-1') {
        alertInfo('所属站址类型没选！');
        return false;
    }


    // 根据WorkFlow配置检查站址名称
    var stationName = '';
    if (isWorkFlowEnabled) {
        // WorkFlow启用时，从下拉选择框获取站址名称
        if ($('#cboAssetStation').hasClass('combobox-f')) {
            var selectedStation = $('#cboAssetStation').combobox('getValue');
            if (!selectedStation || selectedStation === '') {
                alertInfo('请选择站址！');
                return false;
            }
            stationName = $('#cboAssetStation').combobox('getText');
        } else {
            alertInfo('站址下拉框未正确初始化！');
            return false;
        }
    } else {
        // WorkFlow未启用时，从输入框获取站址名称
        stationName = $('#txtStationName').textbox('getValue');
        if (stationName === '') {
            alertInfo('站址名称没有输入！');
            return false;
        }
    }
    if ($('#txtContractNo').val() === '') {
        alertInfo('合同号没有输入！');
        return false;
    }
    if ($('#txtProjectName').val() === '') {
        alertInfo('工程名称没有输入！');
        return false;
    }
    if ($('#txtAddress').val() === '') {
        alertInfo('站址地址没有输入！');
        return false;
    }
    if ($('#txtRemark').val() === '') {
        alertInfo('备注信息没有输入！');
        return false;
    }
    return true;
};

//回车事件
document.onkeydown = function (event) {
    var e = event || window.event;
    if (e.keyCode === 13 && document.activeElement.tagName !== 'TEXTAREA') {
        e.returnValue = false;
        e.cancel = true;
        $('#btn_seach').click();
    }
};

//初始化控件
var init = function () {
    // 首先获取应用配置信息
    getAppSettings();

    // 初始化站址名称控件（必须在获取配置后）
    initStationNameControls();

    // 根据配置获取资产站址列表（必须在控件初始化后）
    getAssetStationList();
    var groupInfo = getGroupInfo();
    if (groupInfo) {
        var obj = JSON.parse(groupInfo);
        $('#StructureIdLook').combotree('tree').tree('loadData', obj);
        $('#StructureIdLook').combotree('setValues', [obj[0].id]);
        var obj1 = JSON.parse(groupInfo);
        $('#StructureId').combotree('tree').tree('loadData', obj1);
        $('#StructureId').combotree('setValues', [obj1[0].id]);
    }

    loadDicDropControl($('#Province'), 1, -1, -1);  //省            
    loadDicUiDropControl($('#StationStatusq'), 5, 5);
    loadDicUiDropControl($('#StationCategoryv'), 6, 6);
    loadDicUiDropControl($('#StationCategoryq'), 6, 6);
    loadDicUiDropControl($('#StationCategory'), 6, 6);

    $('#txtApplyTimeq').datetimebox('setValue', new Date(new Date().setDate(1)).format('yyyy-MM-dd') + ' 00:00:00');
    $('#txtApplyTimeq2').datetimebox('setValue', new Date(new Date().getTime() + 1000 * 60 * 60).format('yyyy-MM-dd hh:mm:ss'));

    //省市下拉框联动
    $('#Province').combobox({
        onChange: function (newValue, oldValue) {
            loadDicDropControl($('#County'), 3, 2, -12);
            if (newValue.toString() === '-1')
                loadDicDropControl($('#City'), -1, 1, -1);
            else
                loadDicDropControl($('#City'), 2, 1, newValue);
        }
    });
    $('#City').combobox({
        onChange: function (newValue, oldValue) {
            if (newValue.toString() === '-1')
                loadDicDropControl($('#County'), -1, 2, -1);
            else
                loadDicDropControl($('#County'), 3, 2, newValue);
        }
    });

    //查询
    $('#btn_seach').click(function () {
        var beginDate = $('#txtApplyTimeq').val();
        var endDate = $('#txtApplyTimeq2').val();
        ///日期校验
        var result;
        if (beginDate.length > 0) {
            result = beginDate.match(/^(\d+)-(\d{1,2})-(\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2})$/);
            if (!result) {
                alertInfo('录入申请开始日期格式不正确！');
                return false;
            }
        }
        if (endDate.length > 0) {
            result = endDate.match(/^(\d+)-(\d{1,2})-(\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2})$/);
            if (!result) {
                alertInfo('录入申请截止日期格式不正确！');
                return false;
            }
            var d1 = new Date(beginDate.replace(/\-/g, '\/'));
            var d2 = new Date(endDate.replace(/\-/g, '\/'));
            if (beginDate && endDate && d1 > d2) {
                alertInfo('申请开始日期不能大于申请截止日期！');
                return false;
            }
        }
        if (!checkDateValid())
            return false;

        loadStationManagementData();
    });

    //重置
    $('#btn_Reset').click(function () {
        $('#txtStationNameq').textbox('setValue', '');
        $('#txtStationCodeq').textbox('setValue', '');
        $('#txtApplyTimeq').datetimebox('setValue', '');
        $('#txtApplyTimeq2').datetimebox('setValue', '');
        $('#StationCategoryq').combobox('setValue', '-1');
        $('#StationStatusq').combobox('setValue', '-1');
        $('#StructureIdLook').combotree('setValue', '-1');
    });

    //新增
    $('#btn_opendialog').click(function () {
        $('#Province').combobox('enable');
        $('#City').combobox('enable');
        $('#County').combobox('enable');

        $('#Province').combobox('setValue', '-1');
        $('#StructureId').combotree('setValue', '-1');
        $('#StationCategory').combobox('setValue', '-1');
        // 根据WorkFlow配置清空站址名称
        if (isWorkFlowEnabled && $('#cboAssetStation').hasClass('combobox-f')) {
            $('#cboAssetStation').combobox('setValue', '');
        } else {
            $('#txtStationName').textbox('setValue', '');
        }

        $('#txtStationCode').textbox('setValue', '');
        $('#txtAddress').textbox('setValue', '');
        $('#txtRemark').textbox('setValue', '');
        $('#txtProjectName').textbox('setValue', '');
        $('#txtContractNo').textbox('setValue', '');

        $('#btn_new').show();
        $('#btn_modify').hide();
        $('#MyStationDialog').dialog('setTitle', '新增站址入网管理');
        $('#MyStationDialog').dialog('open');
    });

    //提交新增
    $('#btn_new').click(function () {
        if (checkinfo()) {
            $.messager.confirm('提示框', '你确认要提交吗?', function (r) {
                if (r) {
                    // 根据WorkFlow配置获取站址名称
                    var stationNameValue = '';
                    if (isWorkFlowEnabled && $('#cboAssetStation').hasClass('combobox-f')) {
                        stationNameValue = $('#cboAssetStation').combobox('getText');
                    } else {
                        stationNameValue = $('#txtStationName').textbox('getValue');
                    }
                    $.ajax({
                        type: 'POST',
                        url: `${STATION_HANDLER}/newStation`,
                        contentType: 'application/x-www-form-urlencoded',
                        data: {
                            //action: 'newStation',
                            Province: $('#Province').combobox('getValue'),
                            City: $('#City').combobox('getValue'),
                            County: $('#County').combobox('getValue'),
                            StructureId: $('#StructureId').combotree('getValue'),
                            StationCategory: $('#StationCategory').combobox('getValue'),
                            StationName: stationNameValue,
                            Address: $('#txtAddress').val(),
                            Remark: $('#txtRemark').val(),
                            ContractNo: $('#txtContractNo').textbox('getValue'),
                            ProjectName: $('#txtProjectName').textbox('getValue')
                        },
                        async: false,
                        success: function (data) {
                            if (data === '1') {
                                refreshNeedApproveResource();
                                alertInfo('保存成功');
                                loadStationManagementData();
                                $('#MyStationDialog').dialog('close');
                            }
                            else if (data === '-1')
                                alertInfo('-1: 保存失败，站址名称已经存在！');
                            else if (data === '-2')
                                alertInfo('-2: 保存失败，生成站址编码出错！');
                            else
                                alertInfo(data + ': 保存失败，请查看日志文件！');
                        },
                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                        }
                    });
                }
            });
        }
    });

    //编辑
    $('#btn_Edit').click(function () {
        var row = $('#StationTable').datagrid('getSelected');
        if (row) {
            $('#btn_new').hide();
            $('#btn_modify').show();
            $('#MyStationDialog').dialog('setTitle', '编辑站址入网管理');

            $('#Province').combobox('setValue', row.Province );
            $('#City').combobox('setValue', row.City );
            $('#County').combobox('setValue', row.County );
            // 根据WorkFlow配置设置站址名称
            if (isWorkFlowEnabled && $('#cboAssetStation').hasClass('combobox-f')) {
                // 在WorkFlow模式下，需要根据站址名称找到对应的资产站址ID
                // 这里简化处理，直接设置文本值
                $('#cboAssetStation').combobox('setText', row.StationName);
            } else {
                $('#txtStationName').textbox('setValue', row.StationName);
            }

            $('#txtAddress').textbox('setValue', row.Address );
            $('#txtRemark').textbox('setValue', row.Remark );
            $('#txtWRStationId').val(row.WRStationId  );
            $('#txtLogonId').val(row.UserId );
            $('#txtStationCode').textbox('setValue', row.StationCode );
            $('#StructureId').combotree('setValues', row.StructureId );
            $('#StationCategory').combobox('setValue', row.StationCategory  );
            $('#txtContractNo').textbox('setValue', row.ContractNo );
            $('#txtProjectName').textbox('setValue', row.ProjectName );
            $('#MyStationDialog').dialog('open');

            if (row.StationStatus === '3'  ) {
                $('#Province').combobox('disable');
                $('#City').combobox('disable');
                $('#County').combobox('disable');
            } else {
                $('#Province').combobox('enable');
                $('#City').combobox('enable');
                $('#County').combobox('enable');
            }
        }
        else {
            alertInfo('请选择要修改的行！');
        }
    });

    //提交修改
    $('#btn_modify').click(function () {
        if (checkinfo()) {
            $.messager.confirm('提示框', '你确认要修改吗?', function (r) {
                if (r) {
                    // 根据WorkFlow配置获取站址名称
                    var stationNameValue = '';
                    if (isWorkFlowEnabled && $('#cboAssetStation').hasClass('combobox-f')) {
                        stationNameValue = $('#cboAssetStation').combobox('getText');
                    } else {
                        stationNameValue = $('#txtStationName').textbox('getValue');
                    }

                    $.ajax({
                        type: 'POST',
                        url: `${STATION_HANDLER}/modifyStation`,
                        contentType: 'application/x-www-form-urlencoded',
                        data: {
                            //action: 'modifyStation',
                            Province: $('#Province').combobox('getValue'),
                            City: $('#City').combobox('getValue'),
                            County: $('#County').combobox('getValue'),
                            StructureId: $('#StructureId').combotree('getValue'),
                            StationCategory: $('#StationCategory').combobox('getValue'),
                            StationName: stationNameValue,
                            Address: $('#txtAddress').val(),
                            Remark: $('#txtRemark').val(),
                            WRStationId: $('#txtWRStationId').val(),
                            LogonId: $('#txtLogonId').val(),
                            ContractNo: $('#txtContractNo').textbox('getValue'),
                            ProjectName: $('#txtProjectName').textbox('getValue')
                        },
                        success: function (data) {
                            if (data === '1') {
                                $('#btn_Pass').removeAttr('disabled');
                                $('#btn_Pass').css({ 'background-color': '' });
                                $('#btn_back').removeAttr('disabled');
                                $('#btn_back').css({ 'background-color': '' });
                                alertInfo('修改成功！');
                                loadStationManagementData();
                                $('#MyStationDialog').dialog('close');
                            }
                            else if (data === '-1')
                                alertInfo('-1: 保存失败，站址名称已经存在！');
                            else if (data === '-5')
                                alertInfo('-5: 保存失败，修改之前状态已变为[审核通过]！');
                            else
                                alertInfo(data + ': 保存失败，请查看日志文件！');
                        },
                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                        }
                    });
                }
            });
        }
    });

    //关闭dialog
    $('#btn_close').click(function () {
        $('#MyStationDialog').dialog('close');
    });

    //通过
    $('#btn_Pass').click(function () {
        var row = $('#StationTable').datagrid('getSelected');
        if (row) {
            $.messager.confirm('提示', '你确定要通过审批吗？',
                function (r) {
                    if (r) {
                        $.ajax({
                            type: 'POST',
                            contentType: 'application/x-www-form-urlencoded',
                            url: `${STATION_HANDLER}/ApproveStationInfo`,
                            async: false,
                            data: {
                                //action: 'ApproveStationInfo',
                                WRStationId: row.WRStationId  
                            },
                            success: function (data) {
                                if (data === '1') {
                                    refreshNeedApproveResource();
                                    loadStationManagementData();
                                    $('#btn_Pass').attr('disabled', 'disabled');
                                    $('#btn_Pass').css({ 'background-color': 'Silver' });
                                    $('#btn_back').attr('disabled', 'disabled');
                                    $('#btn_back').css({ 'background-color': 'Silver' });
                                    alertInfo('审批成功！');
                                } else if (data === '-1')
                                    alertInfo('-1: 已审核过的站址不能重复审核！', 'error');
                                else if (data === '-13')
                                    alertInfo('-13: 缺少交维站址类型和SiteWeb局站类型的映射关系！', 'error');
                                else
                                    alertInfo(data + ': 出现错误！', 'error');

                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                            }
                        });

                    }
                });
        }
        else
            alertInfo('请选择要通过的行！');
    });

    //删除
    $('#btn_delete').on('click', function (e) {
        var row = $('#StationTable').datagrid('getSelected');
        if (row) {
            $.messager.confirm('确认', '您确认删除吗？',
                function (r) {
                    if (r)
                        $.ajax({
                            type: 'POST',
                            url: `${STATION_HANDLER}/DeleteStation`,
                            contentType: 'application/x-www-form-urlencoded',
                            data: {
                                //action: 'DeleteStation',
                                WRStationId: row.WRStationId 
                            },
                            async: false,
                            success: function (data) {
                                if (data === '1') {
                                    refreshNeedApproveResource();
                                    alertInfo('删除成功！');
                                    loadStationManagementData();
                                } else
                                    alertInfo(data + ': 删除出现错误！', 'error');
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                            }
                        });
                });
        } else
            alertInfo('请选择要删除的行！');
    });

    //退回
    $('#btn_back').click(function () {
        var row = $('#StationTable').datagrid('getSelected');
        if (row) {
            $('#TxtRejectReason').textbox('setValue', '');
            $('#txtStationCode').val(row.StationCode  );
            $('#txtWRStationId').val(row.WRStationId  );
            $('#BackDialog').dialog('open');
        }
        else {
            alertInfo('请选择要退回的行！');
        }
    });

    //提交退回
    $('#btn_AppBackRe').click(function () {
        if (!$('#TxtRejectReason').val()) {
            alertInfo('请录入退回原因！');
            return false;
        }
        var row = $('#StationTable').datagrid('getSelected');
        $.ajax({
            type: 'POST',
            url: `${STATION_HANDLER}/RejectWRStationInfo`,
            contentType: 'application/x-www-form-urlencoded',
            data: {
                //action: 'RejectWRStationInfo',
                StationCode: row.StationCode ,
                RejectCause: $('#TxtRejectReason').textbox('getValue'),
                WRStationId: row.WRStationId 
            },
            async: false,
            success: function (data) {
                if (data === '1') {
                    refreshNeedApproveResource();
                    $('#btn_Pass').attr('disabled', 'disabled');
                    $('#btn_Pass').css({ 'background-color': 'Silver' });
                    $('#btn_back').attr('disabled', 'disabled');
                    $('#btn_back').css({ 'background-color': 'Silver' });
                    loadStationManagementData();
                    alertInfo('退回成功！');
                    $('#BackDialog').dialog('close');
                } else
                    alertInfo(data + ': 出现错误！', 'error');
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
            }
        });
    });

    //打开规范浏览dialog
    $('#btn_help').click(function () {
        $.ajax({
            type: 'GET',
            url: GET_STANDARDDOC_HANDLER,
            contentType: 'application/x-www-form-urlencoded',
            data: { type: 1 },
            async: false,
            success: function (data) {
                $('#standardContent').val(data);
                $('#standardContent').attr('disabled', 'disbaled');
                $('#standardDoc').dialog('open');
                //$('#standardDoc').find('textarea').focus();
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
            }
        });
    });

    //规范浏览编辑
    $('#btn_docEdit').click(function () {
        $('#standardContent').removeAttr('disabled');
        $('#standardContent').focus();
    });

    //规范浏览保存
    $('#btn_docSave').click(function () {
        $('#standardContent').attr('disabled', 'disabled');
        $.messager.confirm('确认', '您确认保存吗？',
            function (r) {
                if (r) {
                    $.ajax({
                        type: 'POST',
                        contentType: 'application/json; charset=UTF-8',
                        url: SAVE_STANDARDDOC_HANDLER,
                        data: JSON.stringify({ FileName: 'StationStandard', Content: $('#standardContent').val().replace(/(?!\r)\n/g, '\r\n') }),
                        success: function (data) {
                            alertInfo(data);
                        },
                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                        }
                    });
                }
            });
    });
    
    //    //导出Excel
    //$('#btn_Excel').click(function () {
    //    var f = $('<form action="StationExcel.aspx" method="post" id="fm1" accept-charset="UTF-8"></form>');
    //    $('<input type="hidden"  name="StationName"/>').val($('#txtStationNameq').val()).appendTo(f);
    //    $('<input type="hidden"  name="StationCode"/>').val($('#txtStationCodeq').val()).appendTo(f);
    //    $('<input type="hidden"  name="strBegin"/>').val($("#txtApplyTimeq").val()).appendTo(f);
    //    $('<input type="hidden"  name="strEnd"/>').val($("#txtApplyTimeq2").val()).appendTo(f);
    //    $('<input type="hidden"  name="StationType"/>').val($('#StationCategoryq').combobox('getValue')).appendTo(f);
    //    $('<input type="hidden"  name="StationStatus"/>').val($('#StationStatusq').combobox('getValue')).appendTo(f);
    //    $('<input type="hidden"  name="StructureId"/>').val($('#StructureIdLook').combotree('getValue')).appendTo(f);
    //    f.appendTo(document.body).submit().remove();
    //});

    //导出Excel
    $('#btn_Excel').click(function () {
        var f = $('<form action="api/Station/ExportExcel" method="get" id="fm1" accept-charset="UTF-8"></form>');
        $('<input type="hidden"  name="StationName"/>').val($('#txtStationNameq').val()).appendTo(f);
        $('<input type="hidden"  name="StationCode"/>').val($('#txtStationCodeq').val()).appendTo(f);
        $('<input type="hidden"  name="strBegin"/>').val($("#txtApplyTimeq").val()).appendTo(f);
        $('<input type="hidden"  name="strEnd"/>').val($("#txtApplyTimeq2").val()).appendTo(f);
        $('<input type="hidden"  name="StationType"/>').val($('#StationCategoryq').combobox('getValue')).appendTo(f);
        $('<input type="hidden"  name="StationStatus"/>').val($('#StationStatusq').combobox('getValue')).appendTo(f);
        $('<input type="hidden"  name="StructureId"/>').val($('#StructureIdLook').combotree('getValue')).appendTo(f);
        f.appendTo(document.body).submit().remove();
    });


};

//onload
$(function () {
    init();

    if (cookie['userinfo']['UserId'] !== '-1' || userRole !== '系统管理员') {
        $('#btn_delete').remove();
        $('#toolbar').remove();
    }
    if (userRole !== '系统管理员') {
        $('#btn_Pass').remove();
        $('#btn_back').remove();
    } else
        $('#btn_opendialog').remove();

    loadStationManagementData();

    $('div.mask').remove();
});