﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B
{
    public enum BMessageType
    {
        /// <summary>
        /// 未定义
        /// </summary>
        Undefined = 0,

        /// <summary>
        /// 登录/响应
        /// </summary>
        Login = 101,
        LoginAck = 102,

        /// <summary>
        /// 登出/响应
        /// </summary>
        Logout = 103,
        LogoutAck = 104,

        /// <summary>
        /// 用户请求监控点数据/响应
        /// </summary>
        GetData = 401,
        GetDataAck = 402,

        /// <summary>
        /// 用户请求监控点历史数据/响应
        /// </summary>
        GetHisData = 403,
        GetHisDataAck = 404,

        /// <summary>
        /// 实时告警发送/确认
        /// </summary>
        SendAlarm = 501,
        SendAlarmAck = 502,

        /// <summary>
        /// 写数据请求/响应
        /// </summary>
        SetPoint = 1001,
        SetPointAck = 1002,

        /// <summary>
        /// 用户获取FSU的状态信息/响应
        /// </summary>
        GetFsuInfo = 1701,
        GetFsuInfoAck = 1702,
    }
}
