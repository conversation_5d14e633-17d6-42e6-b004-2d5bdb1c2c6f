﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class GetSuInfoAck : BMessage
    {
        public TSUStatus tSUStatus { get; private set; }
        public EnumResult Result { get; private set; }

        #region SiteWeb配置

        public int MyStationId { get; set; }

        #endregion
        //public int MyHostId { get; set; }
        public GetSuInfoAck(string suids, string surids, TSUStatus SUStatus, EnumResult result) : base()
        {
            MessageType = (int)BMessageType.GET_SUINFO_ACK;
            SUId = suids;
            SURId = surids;
            Result = result;
            tSUStatus = SUStatus;
        }
        public GetSuInfoAck() : base()
        {
            this.MessageType = (int)BMessageType.GET_SUINFO_ACK;
        }
        public static GetSuInfoAck Deserialize(XmlDocument xmlDoc)
        {
            GetSuInfoAck getSuStatusInfoAck = null;
            try
            {
                string code = xmlDoc.SelectSingleNode("/Response/PK_Type/Code").InnerText.Trim();
                string suid = xmlDoc.SelectSingleNode("/Response/Info/SUId").InnerText.Trim();
                string surid = xmlDoc.SelectSingleNode("/Response/Info/SURId").InnerText;
                string result = xmlDoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim();
                EnumResult er;
                TSUStatus ts = null;
                if (string.IsNullOrEmpty(result)) er = EnumResult.FAILURE;
                else er = (EnumResult)int.Parse(result);
                if (er == EnumResult.SUCCESS)
                {
                    string cPUUsage = xmlDoc.SelectSingleNode("/Response/Info/TSUStatus/CPUUsage").InnerText.Trim();
                    string mEMUsage = xmlDoc.SelectSingleNode("/Response/Info/TSUStatus/MEMUsage").InnerText.Trim();
                    float? cpusage = null;
                    float? memusage = null;
                    if (!string.IsNullOrEmpty(cPUUsage)) cpusage = float.Parse(cPUUsage);
                    if (!string.IsNullOrEmpty(mEMUsage)) memusage = float.Parse(mEMUsage);
                    ts = new TSUStatus(cpusage, memusage);
                    getSuStatusInfoAck = new GetSuInfoAck(suid, surid, ts, er);
                }
                else
                {
                    getSuStatusInfoAck = new GetSuInfoAck();
                    getSuStatusInfoAck.SUId = suid;
                    getSuStatusInfoAck.SURId = surid;
                    getSuStatusInfoAck.Result = er;
                    getSuStatusInfoAck.ErrorMsg = "GetSuInfo failed, please check su log";
                }
                entityLogger.DebugFormat("GetSuInfoAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                getSuStatusInfoAck.StringXML = xmlDoc.InnerXml;
                return getSuStatusInfoAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetSuInfoAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetSuInfoAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                getSuStatusInfoAck = new GetSuInfoAck();
                getSuStatusInfoAck.ErrorMsg = ex.Message;
                getSuStatusInfoAck.StringXML = xmlDoc.InnerXml;
                return getSuStatusInfoAck;
            }
        }
        public override string ToString()
        {
            return String.Format("{0}, {1},{2},{3}, {4}, {5}: {6}",
                MessageId, (BMessageType)MessageType, MessageType, SUId, SURId, tSUStatus.ToString(), Result);
        }
    }
}
