namespace BDSTool.Entity.B
{
    using BDSTool.BLL.B;
    using BDSTool.DBUtility;
    using BDSTool.DBUtility.Common;
    using Delivery.Common.NoProcedure.Service;
    using Delivery.Common.NoProcedure.Utils;
    using ENPC.Kolo.Entity.B_CMCC;
    using System;
    using System.Collections.Generic;

    public partial class TBL_FsuTSignalCMCC : IBatchInsertRow
    {
       public TBL_FsuTSignalCMCC(){

        }
    
        #region for batch insert
       private static string _InsertHeader = "INSERT INTO TBL_FsuTSignalCMCC (FSUID, DeviceID, ID, SignalNumber, Type, SignalName, AlarmLevel, Threshold, AbsoluteVal, RelativeVal, `Describe`,NMAlarmID,TSignalId,UpdateTime) ";
        
        public string GetInsertHeader() {
            if (CommonUtils.IsNoProcedure)
            {
                return Public_ExecuteSqlService.Instance.GetInsertHeader_TBL_FsuTSignalCMCC();
            }
            return _InsertHeader;
        }

        public string GetDataRowValueString() {
            return String.Format(@"({0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13})",
            SHelper.GetPara(FSUID), SHelper.GetPara(DeviceID), SHelper.GetPara(ID), SHelper.GetPara(SignalNumber), (int)Type, 
            SHelper.GetPara(SignalName), SHelper.GetPara((int)AlarmLevel), SHelper.GetPara(Threshold), SHelper.GetPara(AbsoluteVal), SHelper.GetPara(RelativeVal),
            SHelper.GetPara(Describe), SHelper.GetPara(NMAlarmID), SHelper.GetPara(TSignalId), SHelper.GetPara(DateTime.Now));
        }
        #endregion

        //public TBL_FsuTSignalCMCC(int logId, string fsuID, string deviceID, TSignal sig) {
        //    FSUID = fsuID;
        //    DeviceID = deviceID;

        //    Type          =  (int)sig.Type;
        //    ID            =  sig.ID;
        //    AlarmLevel = (int)sig.AlarmLevel;
        //    SignalName    =  sig.SignalName;
        //    Threshold     =  sig.Threshold;
        //    AbsoluteVal   =  sig.AbsoluteVal;
        //    RelativeVal   =  sig.RelativeVal;
        //    Describe      =  sig.Describe;
        //    NMAlarmID     =  sig.NMAlarmID;
        //    SignalNumber = sig.SignalNumber;
        //}
        public static TBL_FsuTSignalCMCC ConvertFromTSignal(string fsuID, DevConfExBiz deviceEx, TSignalEx sigEx) {
            var deviceID = deviceEx.TDevice.DeviceID;
            var EquipmentId = deviceEx.Equipment.EquipmentId;
            var EquipmentTempldateId = deviceEx.EquipmentTemplate.EquipmentTemplateId;

            var sig = sigEx.tsig;

            var sigData=new  TBL_FsuTSignalCMCC(){
            FSUID = fsuID,
            DeviceID = deviceID,

            Type = (int)sig.Type,
            ID = sig.ID,
            AlarmLevel = (int)sig.AlarmLevel,
            SignalName = sig.SignalName,
            Threshold = sig.Threshold,
            AbsoluteVal = sig.AbsoluteVal,
            RelativeVal = sig.RelativeVal,
            Describe = sig.Describe,
            NMAlarmID = sig.NMAlarmID,
            SignalNumber = sig.SignalNumber,

            TSignalId = sigEx.TSignalId
            };

            return sigData;
        }
    }
}
