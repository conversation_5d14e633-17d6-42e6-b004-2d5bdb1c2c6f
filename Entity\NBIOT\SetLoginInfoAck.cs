﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    //批量设置FSU注册信息应答
    public class SetLoginInfoAck : BMessage
    {
        /// <summary>
        /// 返回结果
        /// </summary>
        public EnumResult Result { get; set; }

        /// <summary>
        /// 失败的原因
        /// </summary>
        public string FailureCause { get; set; }

        public SetLoginInfoAck() : base()
        {
            MessageType = (int)BMessageType.SET_LOGININFO_ACK;
        }

        public SetLoginInfoAck(string fsuId, EnumResult result, string failureCause)
            : base()
        {
            MessageType = (int)BMessageType.SET_LOGININFO_ACK;

            FSUID = fsuId;
            Result = result;
            FailureCause = failureCause;
        }

        public static SetLoginInfoAck Deserialize(string json)
        {
            SetLoginInfoAck info = null;
            try
            {
                info = JsonConvert.DeserializeObject<SetLoginInfoAck>(json);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetLoginInfoAck.Deserialize:{0}", ex.Message);
                entityLogger.ErrorFormat("SetLoginInfoAck.Deserialize:{0}", ex.StackTrace);
                info = new SetLoginInfoAck();
                info.ErrorMsg = ex.Message;
                info.StringXML = json;
            }
            return info;
        }

        public override string ToString()
        {
            return string.Format("{0},{1},{2}:{3},{4}", MessageId, (BMessageType)MessageType, FSUID, Result, FailureCause);
        }
    }
}