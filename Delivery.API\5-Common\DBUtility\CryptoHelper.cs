﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace DM.TestOrder.Common.DBA
{
    public class CryptoHelper
    {
        //加密密钥
        private static string DESKEY = "Enpc_SiteWeb_beWetiScpnE";
        private static string DESIV = "EnpcSite";
        //加密头，表明该连接字符串是已经加密过的
        public static string ENCRYPTHEADER = "[ENCRYPTCONNECT]";
        /// <summary>
        /// 解密连接字符串
        /// </summary>
        public static string DecryptConnectionString(string inputValue) {
            if (string.IsNullOrEmpty(inputValue))
                return string.Empty;
            if (inputValue.StartsWith(ENCRYPTHEADER) == false)
                return inputValue;
            else {
                //inputValue = inputValue.TrimStart(ENCRYPTHEADER.ToCharArray());加密串为"[ENCRYPTCONNECT]EC0Ov0WDX/Y="时会出错  
                inputValue = inputValue.Substring(ENCRYPTHEADER.Length);
            }
            UTF8Encoding byteConverter = new UTF8Encoding();
            TripleDESCryptoServiceProvider des = new TripleDESCryptoServiceProvider();
            des.Key = byteConverter.GetBytes(DESKEY);
            des.IV = byteConverter.GetBytes(DESIV);
            byte[] inputByteArray = Convert.FromBase64String(inputValue);
            using (System.IO.MemoryStream ms = new System.IO.MemoryStream()) {
                using (CryptoStream cs = new CryptoStream(ms, des.CreateDecryptor(), CryptoStreamMode.Write)) {
                    cs.Write(inputByteArray, 0, inputByteArray.Length);
                    cs.FlushFinalBlock();
                    cs.Close();
                }
                return byteConverter.GetString(ms.ToArray());
            }
        }
        /// <summary>
        /// 加密连接字符串
        /// </summary>
        private static string EncryptConnectionString(string inputValue) {
            if (string.IsNullOrEmpty(inputValue))
                return string.Empty;
            UTF8Encoding byteConverter = new UTF8Encoding();
            var des = new TripleDESCryptoServiceProvider();
            des.Key = byteConverter.GetBytes(DESKEY);
            des.IV = byteConverter.GetBytes(DESIV);
            byte[] inputByteArray = byteConverter.GetBytes(inputValue);
            using (System.IO.MemoryStream ms = new System.IO.MemoryStream()) {
                using (CryptoStream cs = new CryptoStream(ms, des.CreateEncryptor(), CryptoStreamMode.Write)) {
                    cs.Write(inputByteArray, 0, inputByteArray.Length);
                    cs.FlushFinalBlock();
                    cs.Close();
                }
                return string.Format("{0}{1}", ENCRYPTHEADER, Convert.ToBase64String(ms.ToArray()));
            }
        }

    }
}
