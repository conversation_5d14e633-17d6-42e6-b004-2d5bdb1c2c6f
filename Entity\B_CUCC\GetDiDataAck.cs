﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class GetDiDataAck : BMessage
    {
        public List<Device> LDevice { get; set; }
        public string ReportTime { get; set; }

        #region SiteWeb配置

        public int MyStationId { get; set; }

        #endregion
        public GetDiDataAck() : base()
        {
            MessageType = (int)BMessageType.GET_DIDATA_ACK;
        }
        public GetDiDataAck(string suids, string surids, string reportTimes, List<Device> device) : base()
        {
            MessageType = (int)BMessageType.GET_DIDATA_ACK;
            SUId = suids;
            SURId = surids;
            ReportTime = reportTimes;
            LDevice = device;
        }
        public static GetDiDataAck Deserialize(XmlDocument xmlDoc)
        {
            GetDiDataAck getDiDataAck = null;
            try
            {
                string suid = xmlDoc.SelectSingleNode("/Response/Info/SUId").InnerText.Trim();
                string surid = xmlDoc.SelectSingleNode("/Response/Info/SURId").InnerText.Trim();
                string reportTime = xmlDoc.SelectSingleNode("/Response/Info/ReportTime").InnerText.Trim();
                XmlNodeList nodes = xmlDoc.SelectNodes("/Response/Info/Values/DeviceList/Device");
                List<Device> list = new List<Device>();
                foreach (XmlElement node in nodes)
                {
                    string id = node.GetAttribute("Id");
                    string rId = node.GetAttribute("RId");
                    List<Signal> signalList = new List<Signal>();
                    //XmlNodeList lists = node.SelectNodes("Id");
                    XmlNodeList lists = node.SelectNodes("Signal");
                    if (lists.Count > 0)
                    {
                        foreach (XmlElement n in lists)
                        {
                            Signal signal = new Signal();
                            //signal.Id = n.InnerText.Trim();
                            signal.Id = n.Attributes["Id"].Value.ToString();
                            signalList.Add(signal);
                        }
                    }
                    else
                    {
                        //返回的设备中无信号
                        entityLogger.InfoFormat("GetDiDataAck.Deserialize(),Device has no signals ,Device Id={0}, RId={1}", id, rId);
                    }
                    Device device = new Device(id, rId, signalList);
                    list.Add(device);
                }
                getDiDataAck = new GetDiDataAck(suid, surid, reportTime, list);
                entityLogger.DebugFormat("GetDiDataAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                getDiDataAck.StringXML = xmlDoc.InnerXml;
                return getDiDataAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetDiDataAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetDiDataAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                getDiDataAck = new GetDiDataAck();
                getDiDataAck.ErrorMsg = ex.Message;
                getDiDataAck.StringXML = xmlDoc.InnerXml;
                return getDiDataAck;
            }
        }
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            if (LDevice.Count > 0)
            {
                foreach (Device de in LDevice)
                {
                    sb.AppendFormat("{0},{1},{2},{3},{4},{5}\n", MessageId, (BMessageType)MessageType,MessageType, SUId, SURId, de.StringToString());
                }
            }
            else
            {
                sb.AppendFormat("{0},{1},{2},{3},{4},Device is null", MessageId, (BMessageType)MessageType,MessageType, SUId, SURId);
            }
            return sb.ToString();
        }
    }
}
