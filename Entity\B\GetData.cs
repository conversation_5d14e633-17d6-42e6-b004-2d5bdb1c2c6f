﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B
{
    /// <summary>
    /// 5.2.11.3　用户请求监控点数据
    /// </summary>
    public sealed class GetData : BMessage
    {
        public string DeviceId { get; private set; }
        public string DeviceCode { get; private set; }

        public string[] Ids { get; private set; }
        public DateTime SubscribeTime { get; private set; }
        public GetData(string fsuId, string fsuCode, string deviceId, string deviceCode, params string[] ids)
            : base()
        {
            MessageType = (int)BMessageType.GetData;

            FsuId = fsuId;
            FsuCode = fsuCode;
            DeviceId = deviceId;
            DeviceCode = deviceCode;
            Ids = ids;
            SubscribeTime = DateTime.Now;
        }

        public override string Serialize()
        {
            XmlDocument xmldoc = CreateXmlDocument();
            AppendPK_Type(xmldoc, "Request", "GET_DATA", "401");

            XmlElement xe2 = xmldoc.CreateElement("Info");
            XmlElement xe21=xmldoc.CreateElement("FsuId"); 
            xe21.InnerText= FsuId; 
            xe2.AppendChild(xe21);
            XmlElement xe22=xmldoc.CreateElement("FsuCode"); 
            xe22.InnerText=FsuCode; 
            xe2.AppendChild(xe22);

            XmlElement xe23= xmldoc.CreateElement("DeviceList");
            XmlElement xe231=xmldoc.CreateElement("Device"); 
            xe231.SetAttribute("Id", DeviceId);
            xe231.SetAttribute("Code", DeviceCode);
            xe23.AppendChild(xe231);
            xe2.AppendChild(xe23);

            foreach (string id in Ids)
            {
                XmlElement xe2311 = xmldoc.CreateElement("Id");
                xe2311.InnerText = id;
                xe231.AppendChild(xe2311);
            }

            XmlNode root = xmldoc.SelectSingleNode("Request"); 
            root.AppendChild(xe2);


            return xmldoc.InnerXml;
        }

        public static GetData Deserialize(string raw)
        {
            throw new NotImplementedException();
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            string str = String.Format("{0}, {1}: {2}.{3}.{4}, {5}, [",
                MessageId, (BMessageType)MessageType, FsuId, DeviceId, FsuCode, DeviceCode);
            sb.Append(str);

            foreach (string id in Ids)
            {
                sb.AppendFormat("{0}, ", id);
            }

            sb.Append("]");

            return sb.ToString();
        }
    }
}
