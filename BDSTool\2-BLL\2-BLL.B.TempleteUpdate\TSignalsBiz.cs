﻿using Common.Logging.Pro;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;




using BDSTool.Entity.S2;
using BDSTool.DBUtility.Common;
using BDSTool.DBUtility;
using BDSTool.Entity.B;
using ENPC.Kolo.Entity.B_CMCC;
using BDSTool.BLL.B;

namespace BDSTool.BLL.S2
{
    public class TSignalsBiz
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");
        public string ErrorMsg = string.Empty;

        public TBL_Equipment Equipment;
        public TBL_EquipmentTemplate    EquipmentTemplate;
        public List<TSignalEx>  TSignalsNew;

        public List<TSignalEx> ListOldToDel;
        public List<TSignalEx> ListNewToAdd;

        public List<TSignalEx> ListOldToUpdate;//type may change
        public List<TSignalEx> ListNewToUpdate;

        public TSignalsBiz(TBL_Equipment Equipment, TBL_EquipmentTemplate EquipmentTemplate, List<TSignalEx>  ListOfTSignalEx) {
            this.Equipment = Equipment;
            this.EquipmentTemplate = EquipmentTemplate;
            this.TSignalsNew = ListOfTSignalEx;
        }

        public bool Save() {
            var listOld = new List<TSignalEx>();
            if (!LoadListOfSignalFromDB(listOld)) {
                logger.InfoFormat("TSignalsBiz.Save();LoadListOfSignalFromDB Failed;dev={0}", Equipment.EquipmentName);
                ErrorMsg = string.Format("从数据库中加载设备配置信息失败.待导入设备名:{0},错误信息:{1}", Equipment.EquipmentName, ErrorMsg);
                return false;
            }
            //------------------------------------------------------------------------------------------------------
            if (!FindChange(listOld,  TSignalsNew)) {
                var s = string.Format("TSignalsBiz.Save();设备信号级配置无变化.设备名:{0}", Equipment.EquipmentName);
                logger.Info(s);
                return true;
            }
            //------------------------------------------------------------------------------------------------------
            var finalSigCount = listOld.Count + ListNewToAdd.Count - ListOldToDel.Count;
            var sCnt = "TSignalsBiz.Save(); T信号配置变化; 更新数量统计: ";
            if(ListOldToDel.Count>0)
                sCnt += " 删除=" + ListOldToDel.Count;
            if(ListNewToAdd.Count>0)
                sCnt += " 新增=" + ListNewToAdd.Count;
            if(ListNewToUpdate.Count>0)
                sCnt += " 修改=" + ListNewToUpdate.Count;
            sCnt += " 更新后T信号数量=" + finalSigCount;
            logger.Info(sCnt);
                
            // 从模板中删除T信号
            if (ListOldToDel.Count > 0) {
                logger.InfoFormat("TSignalsBiz.Save();listOldToDel={0}", string.Join(",", ListOldToDel.ConvertAll(o => o.tsig.SignalName).ToList()));
                if (TSignalsBiz.DeleteTSignals(EquipmentTemplate.EquipmentTemplateId, ListOldToDel) == false) {
                    logger.InfoFormat("UpdateConfigOfSec();DeleteTSignals Failed;EquipmentName={0}", Equipment.EquipmentName);
                    ErrorMsg = string.Format("删除设备信号级配置失败.待导入设备名:{0}", Equipment.EquipmentName);
                    return false;
                }
            }
                    

            // 在模板中新增T信号
            if (ListNewToAdd.Count > 0) {
                logger.InfoFormat("TSignalsBiz.Save();listNewToAdd={0}", string.Join(",", ListNewToAdd.ConvertAll(o => o.tsig.SignalName).ToList()));
                if (TSignalsBiz.InsertTSignals(EquipmentTemplate, ListNewToAdd) == false) {
                    ErrorMsg = string.Format("新增设备信号级配置失败.待导入设备名:{0}", Equipment.EquipmentName);
                    return false;
                }
            }

            // 更新T信号
            if (ListNewToUpdate.Count > 0) {
                logger.InfoFormat("TSignalsBiz.Save(); listOldToUpdate={0}", string.Join(",", ListNewToUpdate.ConvertAll(o => "["+o.tsig.ID+"]"+o.tsig.SignalName).ToList()));
                if (TSignalsBiz.UpdateConfigOfSec(EquipmentTemplate, ListNewToUpdate, ListOldToUpdate) == false) {
                    logger.InfoFormat("UpdateConfigOfSec();UpdateConfigOfSec Failed;dev={0}", Equipment.EquipmentName);
                    ErrorMsg = string.Format("更新设备信号级配置失败.待导入设备名:{0}", Equipment.EquipmentName);
                    return false;
                }
            }


            return true;
        }

        private bool LoadListOfSignalFromDB(List<TSignalEx> tsignalsOld) {
            var tSignals = TSignalBiz.GetTSignalList(Equipment.StationId, Equipment.EquipmentId);

            if (!TSignalEx.Convert2TSignalExList(tSignals, ref tsignalsOld, ref ErrorMsg)) {
                logger.WarnFormat("LoadListOfSignalFromDB(); Convert2TSignalExList()监控对象转换失败.设备名称：{0}", this.Equipment.EquipmentName);
                return false;
            }

            return true;
        }

        private bool FindChange(List<TSignalEx> listOld, List<TSignalEx> listNew) {

            //信号分类，新增、删除、修改

            //1) 找出交集
            var listUnChangeofOld = listOld.Intersect(listNew, new TSignalExComparer()).ToList();
            var listUnchangeOfNew = (from o in listNew
                                     join n in listUnChangeofOld on o.TSignalId equals n.TSignalId
                                     select o).ToList();

            if (listOld.Count == listUnChangeofOld.Count && listNew.Count == listUnchangeOfNew.Count) { 
                return false;
            }
            //------------------------------------------------------------------------------------------------------
            //old = unchange + del + update, find DEL
            var listOldToDelOrUpdate = listOld.Except(listUnChangeofOld).ToList();
            ListOldToUpdate = (from o in listOldToDelOrUpdate
                                   join n in listNew on o.TSignalId equals n.TSignalId
                                   select o).ToList();
            ListOldToDel = listOldToDelOrUpdate.Except(ListOldToUpdate).ToList();

            if (listUnChangeofOld.Count + ListOldToUpdate.Count + ListOldToDel.Count != listOld.Count) {
                logger.Warn("TSignalsBiz.FindChange(); 当前配置分类统计核对失败");
            }
            //------------------------------------------------------------------------------------------------------
            var listNewToAddOrUpdate = listNew.Except(listUnchangeOfNew).ToList();

            ListNewToUpdate = (from o in listNewToAddOrUpdate
                               join n in listOld on o.TSignalId equals n.TSignalId
                               select o).ToList();
            ListNewToAdd = listNewToAddOrUpdate.Except(ListNewToUpdate).ToList();
            
            if (listUnchangeOfNew.Count + ListNewToUpdate.Count + ListNewToAdd.Count != listNew.Count) {
                logger.Warn("TSignalsBiz.FindChange(); 导入配置分类统计核对失败");
            }

            return true;
        }    

        public static bool InsertConfigOfTSEC(TBL_EquipmentTemplate et) {
            logger.InfoFormat("InsertConfigOfSEC();EquipTempId={0};添加模板所有配置", et.EquipmentTemplateId);


            if (et.Signals.Count > 0) {
                if (!SignalBiz.SaveEntityList(et.Signals)) {
                    logger.ErrorFormat("InsertConfigOfTSEC(); failed to SignalBiz.SaveEntityList; EquipmentTemplateName={0}", et.EquipmentTemplateName);
                    return false;
                }

            }

            if (et.Events.Count > 0) {
                if (!EventBiz.SaveEntityList(et.Events)) {
                    logger.ErrorFormat("InsertConfigOfTSEC(); failed to EventBiz.SaveEntityList; EquipmentTemplateName={0}", et.EquipmentTemplateName);
                    return false;
                }

            }

            if (et.Controls.Count > 0) {
                if (!ControlBiz.SaveEntityList(et.Controls)) {
                    logger.ErrorFormat("InsertConfigOfTSEC(); failed to Controls.SaveEntityList; EquipmentTemplateName={0}", et.EquipmentTemplateName);
                    return false;
                }

            }
            return true;
        }

        public static bool InsertTSignals(TBL_EquipmentTemplate et, List<TSignalEx> NewTSigList) {

            var sigsNew = (from a in et.Signals
                         join n in NewTSigList on a.SignalId equals n.TSignalId
                         select a).ToList();
            if (sigsNew.Count > 0) {
                logger.InfoFormat("InsertTSignals();EquipmentTemplateId={0};批量添加信号,Count={1}", et.EquipmentTemplateId, sigsNew.Count);
                if (!SignalBiz.SaveEntityList(sigsNew)) {
                    logger.ErrorFormat("InsertTSignals(); failed to SignalBiz.SaveEntityList; EquipmentTemplateName={0}", et.EquipmentTemplateName);
                    return false;
                }

                foreach (var s in sigsNew) {
                    SiteWebSecOP.LogConfigChange(EnumOfOpConfigId.EquipmentSignalOP, EditType.Add, et.EquipmentTemplateId, s.SignalId);
                }
            }

            var eventsNew = (from a in et.Events
                           join n in NewTSigList on a.EventId equals n.TSignalId
                           select a).ToList();
            if (eventsNew.Count > 0) {
                logger.InfoFormat("InsertTSignals();EquipmentTemplateId={0};批量添加告警,Count={1}", et.EquipmentTemplateId, eventsNew.Count);
                if (!EventBiz.SaveEntityList(eventsNew)) {
                    logger.ErrorFormat("InsertTSignals(); failed to EventBiz.SaveEntityList; EquipmentTemplateName={0}", et.EquipmentTemplateName);
                    return false;
                }

                foreach (var s in eventsNew) {
                    SiteWebSecOP.LogConfigChange(EnumOfOpConfigId.EquipmentEventOP, EditType.Add, et.EquipmentTemplateId, s.EventId);
                }
            }

            var controlsNew = (from a in et.Controls
                             join n in NewTSigList on a.ControlId equals n.TSignalId
                             select a).ToList();
            if (controlsNew.Count > 0) {
                logger.InfoFormat("InsertTSignals();EquipmentTemplateId={0};批量添加控制,Count={1}", et.EquipmentTemplateId, controlsNew.Count);
                if (!ControlBiz.SaveEntityList(controlsNew)) {
                    logger.ErrorFormat("InsertTSignals(); failed to Controls.SaveEntityList; EquipmentTemplateName={0}", et.EquipmentTemplateName);
                    return false;
                }

                foreach (var s in controlsNew) {
                    SiteWebSecOP.LogConfigChange(EnumOfOpConfigId.EquipmentCommandOP, EditType.Add, et.EquipmentTemplateId, s.ControlId);
                }
            }


            return true;
        }

        public static bool DeleteTSignals( int equipmentTemplateId, List<TSignalEx> tsigs) {
            try {


                var signals = tsigs.Where(o => o.tsig.Type == EnumType.DI || o.tsig.Type == EnumType.AI).ToList();
                if (signals.Count > 0) {
                    logger.InfoFormat("DeleteTSignals();equipmentTemplateId={0};批量删除信号,Count={1}", equipmentTemplateId, signals.Count);
                    if (!SignalBizZ.DeleteList(equipmentTemplateId, signals)) {
                        logger.InfoFormat("DeleteTSignals(); Failed to SignalBizZ.DeleteList; equipmentTemplateId={0}", equipmentTemplateId);
                        return false;
                    }
                    foreach (var s in signals) {
                        SiteWebSecOP.LogConfigChange(EnumOfOpConfigId.EquipmentSignalOP, EditType.Delete, equipmentTemplateId, s.TSignalId);
                    }
                }
                //----------------------------------------------------------------------------------------------------------------
                var controls = tsigs.Where(o => o.tsig.Type == EnumType.DO || o.tsig.Type == EnumType.AO).ToList();
                if (controls.Count > 0) {
                    logger.InfoFormat("DeleteTSignals();equipmentTemplateId={0};批量删除控制,Count={1}", equipmentTemplateId, controls.Count);
                    if (!ControlBizZ.DeleteList(equipmentTemplateId, controls)) {
                        logger.InfoFormat("DeleteTSignals(); Failed to ControlBizZ.DeleteList ; equipmentTemplateId={0}", equipmentTemplateId);
                        return false;
                    }
                    foreach (var s in controls) {
                        SiteWebSecOP.LogConfigChange(EnumOfOpConfigId.EquipmentCommandOP, EditType.Delete, equipmentTemplateId, s.TSignalId);
                    }
                }


                //----------------------------------------------------------------------------------------------------------------
                var events = tsigs.Where(o => o.tsig.Type == EnumType.ALARM).ToList();
                if (events.Count > 0) {
                    logger.InfoFormat("DeleteTSignals();equipmentTemplateId={0};批量删除告警,Count={1}", equipmentTemplateId, events.Count);
                    var eventIds = events.ConvertAll(o => o.TSignalId);
                    if (!EventBizZ.ZDeleteList(equipmentTemplateId, eventIds)) {
                        logger.InfoFormat("DeleteTSignals(); Failed to del events; equipmentTemplateId={0}", equipmentTemplateId);
                        return false;
                    }
                    foreach (var s in events) {
                        SiteWebSecOP.LogConfigChange(EnumOfOpConfigId.EquipmentEventOP, EditType.Delete, equipmentTemplateId, s.TSignalId);
                    }
                }

            }

            catch (Exception ex) {
                logger.ErrorFormat("DeleteTSignals();equipmentTemplateId={0}, Error={1}; ", equipmentTemplateId, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }

            return true;
        }


        public static bool UpdateConfigOfSec(TBL_EquipmentTemplate et, List<TSignalEx> tSigListUpd, List<TSignalEx> ListOldToUpdate) {
            logger.InfoFormat("UpdateConfigOfSec();EquipTempId={0}", et.EquipmentTemplateId);


            if (!DeleteTSignals(et.EquipmentTemplateId, ListOldToUpdate)) {
                logger.InfoFormat("UpdateConfigOfSec(); failed to DeleteTSignals; EquipmentTemplateName={0}", et.EquipmentTemplateName);
                return false;
            }

            if (!InsertTSignals(et, tSigListUpd)) {
                logger.InfoFormat("UpdateConfigOfSec(); failed to InsertTSignals; EquipmentTemplateName={0}", et.EquipmentTemplateName);
                return false;
            }

            return true;
        }

       
    }
}
