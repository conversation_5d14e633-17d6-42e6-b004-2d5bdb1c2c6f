.datebox-calendar-inner {
  height: 180px;
}
.datebox-button {
  height: 18px;
  padding: 2px 5px;
  font-size: 12px;
  text-align: center;
}
.datebox-current,
.datebox-close,
.datebox-ok {
  text-decoration: none;
  font-weight: bold;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.datebox-current,
.datebox-close {
  float: left;
}
.datebox-close {
  float: right;
}
.datebox-button-hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.datebox .combo-arrow {
  background-image: url('images/datebox_arrow.png');
  background-position: center center;
}
.datebox-button {
  background-color: #F4F4F4;
}
.datebox-current,
.datebox-close,
.datebox-ok {
  color: #444;
}
