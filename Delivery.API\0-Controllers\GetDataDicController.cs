﻿using Delivery.BSL;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace Delivery.API._0_Controllers
{
    public class GetDataDicController : BaseController
    {
        [HttpGet]
        public JsonResult Get(string entryId, string parentEntryId, string parentItemId, string typeId,  string itemId)
        {
            var dt = GetDataDic(entryId, parentEntryId, parentItemId, typeId, itemId);
            // var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(dt);

            return new JsonResult(dt);

        }

        private string GetDataDic(string entityID, string parentEntryId, string parentItemId, string typeId, string itemId)
        {

            DataTable dt;

            if (!string.IsNullOrWhiteSpace(typeId) && !string.IsNullOrEmpty(itemId))
            {
                dt = new EntryDic().GetDataUiDic(typeId, itemId);
            }
            else
            {
                dt = new EntryDic().GetDataDic(entityID, parentEntryId, parentItemId);
            }
            return Json4EasyUI.onFormMultiRow(dt);
        }

    }
}
