﻿using BDSTool.BLL.S2;
using BDSTool.DBUtility.Common;
using BDSTool.Entity;
using BDSTool.Entity.B;
using BDSTool.Entity.S2;
using BDSTool.Utility;
using Common.Logging.Pro;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace BDSTool.BLL.Convert
{
    public class SiteWebSECConvert
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");



        public static bool ListOfTSignals2NewSiteWebSECs(ref string errmsg, int EquipmentTemplateId, List<TSignalEx> tsignals, ref  List<TBL_Signal> signals, ref List<TBL_Event> events, ref List<TBL_Control> controls, bool IsAppend = false) {
            var DisplayIndex = 0;
            if (IsAppend) {
                string rtn = null;
                if (CommonUtils.IsNoProcedure)
                {
                    rtn = Public_ExecuteSqlService.Instance.NoStore_TBLSignal_GetMaxIdByEquipmentTemplateId(EquipmentTemplateId);
                }
                else 
                {
                    rtn = DBHelper.ExecuteScalar("select max(DisplayIndex) from TBL_Signal where EquipmentTemplateId=" + EquipmentTemplateId);
                }

                var firstDispIndex = string.IsNullOrEmpty(rtn)?1: int.Parse(rtn) + 1;
                
                DisplayIndex = firstDispIndex;
            }
            else {
                DisplayIndex = 1;
            }

           
            foreach (var tsigEx in tsignals.Where(o => o.SECType==TypeOfSEC.Signal)) {
                TBL_Signal signal = new TBL_Signal();
                if (!TSignal2Signal(EquipmentTemplateId, tsigEx, DisplayIndex, ref  signal, ref errmsg)) {
                    if (string.IsNullOrEmpty(errmsg))
                        errmsg = string.Format("导入信号配置失败.信号名={0},信号ID={1}", tsigEx.tsig.SignalName,tsigEx.tsig.ID);
                    LoggerBDSTool.InfoFormat("ListOfTSignals2NewSiteWebSECs(); TSignal2Signal() failed; EquipmentTemplateId={0},原因={1}", EquipmentTemplateId, errmsg);
                    return false;
                }
                signals.Add(signal);
                DisplayIndex++;
            }
            //-------------------------------------------------------------------------------------------------------------------------
            if (IsAppend) {
                string rtn = null;
                if (CommonUtils.IsNoProcedure)
                {
                    rtn = Public_ExecuteSqlService.Instance.NoStore_TBLEvent_GetMaxIdByEquipmentTemplateId(EquipmentTemplateId);
                }
                else
                {
                    rtn = DBHelper.ExecuteScalar("select max(DisplayIndex) from TBL_Event where EquipmentTemplateId=" + EquipmentTemplateId);
                }
                var firstDispIndex4Event = string.IsNullOrEmpty(rtn) ? 1 : int.Parse(rtn) + 1;
                DisplayIndex = firstDispIndex4Event;
            }
            else
                DisplayIndex = 1;

            foreach (var tsigEx in tsignals.Where(o => o.SECType == TypeOfSEC.Event)) {
                var evt = new TBL_Event();
                if (!SiteWebSECConvert.TSignal2Event(EquipmentTemplateId, tsigEx, DisplayIndex, ref  evt, ref errmsg)) {
                    if (string.IsNullOrEmpty(errmsg))
                        errmsg = string.Format("导入告警配置失败.信号名={0},信号ID={1}，告警等级={2}", tsigEx.tsig.SignalName, tsigEx.tsig.ID,tsigEx.tsig.AlarmLevel);
                    LoggerBDSTool.InfoFormat("ListOfTSignals2NewSiteWebSECs(); TSignal2Event() failed; EquipmentTemplateId={0},原因={1}", EquipmentTemplateId, errmsg);
                    return false;
                }
                events.Add(evt);
                DisplayIndex++;
            }
            //-------------------------------------------------------------------------------------------------------------------------
            if (IsAppend) {
                string rtn = null;
                if (CommonUtils.IsNoProcedure)
                {
                    rtn = Public_ExecuteSqlService.Instance.NoStore_TBLControl_GetMaxIdByEquipmentTemplateId(EquipmentTemplateId);
                }
                else
                {
                    rtn = DBHelper.ExecuteScalar("select max(DisplayIndex) from TBL_Control where EquipmentTemplateId=" + EquipmentTemplateId);
                }
                var firstDispIndex4Control = string.IsNullOrEmpty(rtn) ? 1 : int.Parse(rtn) + 1;
                DisplayIndex = firstDispIndex4Control;
            }
            else
                DisplayIndex = 1;

            foreach (var tsigEx in tsignals.Where(o => o.SECType == TypeOfSEC.Control)) {
                var ctrl = new TBL_Control();
                if (!SiteWebSECConvert.TSignal2Control(EquipmentTemplateId, tsigEx, DisplayIndex, ref  ctrl, ref errmsg)) {
                    if (string.IsNullOrEmpty(errmsg))
                        errmsg = string.Format("导入控制配置失败.信号名={0},信号ID={1}", tsigEx.tsig.SignalName, tsigEx.tsig.ID);
                    LoggerBDSTool.InfoFormat("ListOfTSignals2NewSiteWebSECs(); TSignal2Control() failed; EquipmentTemplateId={0},原因={1}", EquipmentTemplateId, errmsg);
                    return false;
                }
                controls.Add(ctrl);
                DisplayIndex++;
            }
            //-------------------------------------------------------------------------------------------------------------------------
            return true;
        }



        public static bool TSignal2Signal(int EquipmentTemplateId, TSignalEx tsigEx, int dispIndex, ref TBL_Signal signal, ref string errmsg) {
            try {
                var tsig = tsigEx.tsig;

                signal.SignalId =  tsigEx.TSignalId;

                signal.Description = tsig.NMAlarmID+"&"+tsig.Describe;//kkk

                signal.EquipmentTemplateId = EquipmentTemplateId;


                signal.SignalName = tsig.SignalName;

                //SiteWeb-SignalCategory 1模拟 2数字
                //信号类型，其中：0为告警信号，1为遥控信号，2为遥调信号，3为模拟信号（遥测），4为状态信号（遥信）
                if(tsig.Type==EnumType.AI)//3为模拟信号（遥测）
                    signal.SignalCategory = 1;//1模拟
                else
                    signal.SignalCategory = 2;

                //siteweb 字典
                //EntryId=18
                //1	采集信号
                //2	虚拟信号
                //3	常量信号
                signal.SignalType = 2;//2	虚拟信号

                signal.ChannelNo = ConstsFsu.BChannelNo;//int.Parse(signalElement.GetAttribute("ChannelNo"));
            
                //EntryId=22
                //1	模拟量
                //2	数字量
                if( signal.SignalCategory==1)//1模拟
                    signal.ChannelType =1;
                else
                    signal.ChannelType =2;

                signal.DataType = 0;//默认浮点型

                //kkk mabybeChange
                if( signal.SignalCategory==1){//1模拟
                    signal.ShowPrecision = "0.0";

                    if(tsig.SignalName.Length>=2){
                        var last2=tsig.SignalName.Substring(tsig.SignalName.Length-2,2);                    
                        if (last2=="组数"||last2=="数量"||last2=="卡号")
                            signal.ShowPrecision = "0";
                    }                        
                }
                else
                    signal.ShowPrecision = "0";


                //AbsoluteVal	Float	绝对阀值
                //RelativeVal	Float	百分比阀值
                signal.StoreInterval =null;

                signal.AbsValueThreshold = tsig.AbsoluteVal;      
                signal.PercentThreshold = tsig.RelativeVal; 
                signal.StaticsPeriod = null;
                signal.Enable = true;
                signal.Visible = true;

                signal.BaseTypeId = null;
                signal.ChargeStoreInterVal =  null;
                signal.ChargeAbsValue = null;

                signal.DisplayIndex = dispIndex;
                signal.MDBSignalId = null;

                signal.Unit = string.Empty;
                if (signal.SignalCategory == 1)//模拟量
                    signal.Unit = tsig.Describe;

                //-------------------------------------
                signal.ModuleNo = 0;

                //-------------------------------------
                signal.Expression = string.Empty;

                //属性----------------------------------------------------------------------------------
                signal.SignalPropertyIds.Add(27);//可视

                //开关量状态----------------------------------------------------------------------------------
                //  状态信号为状态描述,，格式举例：0&正常;1&告警 ; 需要设置TBL_SignalMeanings
                if (tsig.Type ==EnumType.DI){
                    string[] states = tsig.Describe.Split(';');
                    foreach (var state in states) {
                        if (string.IsNullOrEmpty(state))
                            continue;

                        string[] stateDefine = state.Split('&');

                        int StateValue = int.Parse(stateDefine[0].Trim());
                        string Meanings = stateDefine[1];
                        signal.StateValue2Meanings.Add(StateValue, Meanings);
                    }
                }
            }
            catch (Exception ex) {
                errmsg = string.Format("导入信号{0}(名称={1},描述={2})的配置出错.错误信息:{3}",
                    tsigEx.tsig.ID, tsigEx.tsig.SignalName, tsigEx.tsig.Describe, ex.Message);

                logger.ErrorFormat("TSignal2Signal();{0}", errmsg);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }

        public static bool TSignal2Control(int EquipmentTemplateId, TSignalEx tsigEx, int dispIndex, ref TBL_Control control, ref string errmsg) {
            try {
                var tsig = tsigEx.tsig;

                control.EquipmentTemplateId = EquipmentTemplateId;

                control.ControlId = tsigEx.TSignalId;

                control.Description =  tsig.NMAlarmID + "&" + tsig.Describe;//kkk

                control.ControlName = tsig.SignalName;

                //TBL_DataItem WHERE EntryId=31
                control.ControlCategory = 1;//命令种类, 1=普通控制

                control.CmdToken = string.Empty;
                control.BaseTypeId = null;

                control.ControlSeverity = 1;//UI=重要度， 1=一般

                control.SignalId = null;//kkk 待确认

                control.TimeOut = null;
                control.Retry = null;

                control.Enable = true;
                control.Visible = true;
                control.DisplayIndex = dispIndex;

                //SiteWeb-CommandType 1=遥调，2=遥控
                //SiteWeb-SignalCategory 1模拟 2数字
                //信号类型，其中：0为告警信号，1为遥控信号，2为遥调信号，3为模拟信号（遥测），4为状态信号（遥信）
                if (tsig.Type == EnumType.AO){
                    //2为遥调信号
                    control.CommandType = 1;//1遥调
                    control.MinValue = 0;
                    control.MaxValue = 1000;
                }
                else {
                    control.CommandType = 2;
                    control.MinValue = 0;
                    control.MaxValue = 0;
                }
                

                control.ControlType = 1;//kkk 待确认

                control.DataType = 0;//kkk 待确认



                control.DefaultValue = null;

                control.ModuleNo = 0;

                //开关量状态----------------------------------------------------------------------------------
                if (tsig.Type == EnumType.DO) {
                    var describe = tsig.Describe;
                    if (string.IsNullOrEmpty(describe)) {
                        describe = "1&待命";
                        //logger.InfoFormat("导入控制{0}(名称={1}),描述自动设置为默认值:{2}",
                        //    tsigEx.tsig.ID, tsigEx.tsig.SignalName, tsigEx.tsig.Describe, describe);
                    }


                    string[] states = describe.Split(';');
                 
                    foreach (var state in states) {
                        //add 0831 ,最后可添加;
                        var stateString = state.Trim();
                        if (string.IsNullOrEmpty(stateString))
                            continue;

                        string[] stateDefine = stateString.Split('&');

                        int StateValue = int.Parse(stateDefine[0].Trim());
                        string Meanings = stateDefine[1];
                        Meanings = Meanings.Trim();//add 0831 
                        control.ParameterValue2Meanings.Add(StateValue, Meanings);
                    }
                }
            }
            catch (Exception ex) {
                errmsg=string.Format("导入控制{0}(名称={1},描述={2})的配置出错.错误信息:{3}",
                    tsigEx.tsig.ID, tsigEx.tsig.SignalName, tsigEx.tsig.Describe,ex.Message);

                logger.ErrorFormat("TSignal2Control();{0}", errmsg);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }

        public static bool TSignal2Event(int EquipmentTemplateId, TSignalEx tsigEx, int dispIndex, ref TBL_Event e, ref string errmsg) {
            try {
                var tsig = tsigEx.tsig;

                var ec = new TBL_EventCondition();
                e.eventConditions.Add(ec);

                int EventSeverity = 0;
                if (!GetEventSeverity(tsig.AlarmLevel, ref EventSeverity)) {
                    logger.WarnFormat("告警等级导入失败.AlarmLevel={0}",tsig.AlarmLevel);
                    return false;
                }
                ec.EventSeverity = EventSeverity;

                e.EquipmentTemplateId = EquipmentTemplateId;

                e.EventId = tsigEx.TSignalId;

                e.Description = tsig.NMAlarmID;//kkk

                e.EventName = tsig.SignalName;

                e.SignalId = null; //kkk to do?

                //kkk todo //可设为自身
                //原铁塔原型有误，关联事件应该是{},暂不做修改
                e.StartExpression = string.Format("[-1,{0}]", e.EventId);

                //事件类型，2, '设备事件'
                e.EventCategory = 2;

                //事件开始类型 SELECT * FROM TBL_DataEntry WHERE EntryId=25
                //1	条件事件             //2	非条件事件
                e.StartType = 1;

                //事件结束类型 SELECT * FROM TBL_DataEntry WHERE EntryId=26
                //1	脉冲事件            //2	间隔事件            //3	持续事件
                e.EndType = 3;

                e.SuppressExpression = string.Empty;
                e.Enable = true;
                e.Visible = true;

                e.DisplayIndex = dispIndex;

                e.ModuleNo = 0;

                //---------------------------------------------------------------------------


                ec.EquipmentTemplateId = e.EquipmentTemplateId;
                ec.EventId = e.EventId;
                ec.EventConditionId = 0;


                //kkk todo 
                if(tsig.SignalName.IndexOf("过高")!=-1||
                    tsig.SignalName.IndexOf("高于") != -1 ||
                    tsig.SignalName.IndexOf("大于") != -1)
                    ec.StartOperation = ">=";
                else if (tsig.SignalName.IndexOf("过低") != -1 ||
                            tsig.SignalName.IndexOf("低于") != -1 ||
                            tsig.SignalName.IndexOf("小于") != -1)
                    ec.StartOperation = "<=";
                else
                    ec.StartOperation = "=";

                if (tsig.Threshold.HasValue)
                    ec.StartCompareValue = tsig.Threshold.Value;
                ec.StartDelay = 0;
                ec.EndOperation = string.Empty;
                ec.EndCompareValue =  null;
                ec.EndDelay = null;
                ec.Frequency = null;
                ec.FrequencyThreshold = null;
                ec.Meanings = "有告警";//kkk to do
                ec.EquipmentState = null;
                ec.BaseTypeId = null;
                ec.StandardName = null;
            }
            catch (Exception ex) {
                errmsg=string.Format("导入告警{0}({1})的配置出错.错误信息:{2}",tsigEx.tsig.ID, tsigEx.tsig.SignalName, ex.Message);

                logger.ErrorFormat("TSignal2Event();{0}", errmsg);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }

        public static bool GetEventSeverity(ENPC.Kolo.Entity.B_CMCC.EnumState es, ref int EventSeverity) {
            switch (es) {
                case ENPC.Kolo.Entity.B_CMCC.EnumState.CRITICAL:// 一级告警
                    EventSeverity = 3;
                    break;
                case ENPC.Kolo.Entity.B_CMCC.EnumState.MAJOR://二级告警
                    EventSeverity = 2;
                    break;
                case ENPC.Kolo.Entity.B_CMCC.EnumState.MINOR://三级告警
                    EventSeverity = 1;
                    break;
                case ENPC.Kolo.Entity.B_CMCC.EnumState.HINT://四级告警
                    EventSeverity = 0;
                    break;
                default:
                    return false;
            }
            return true;
        }
        //public static string GetDescription(TSignal tsig) {
        //    return string.Format("{0}/{1}/{2}", 
        //        tsig.ID, 
        //        String.IsNullOrEmpty(tsig.SignalNumber)?"":tsig.SignalNumber,
        //        String.IsNullOrEmpty(tsig.NMAlarmID)?"":tsig.NMAlarmID
        //        );
        //}



        public static bool GetDisplayIndex4ExistSiteWebSEC(int EquipmentTemplateId, int sId, ref int DisplayIndex) {
            string rtn = null;
            if (CommonUtils.IsNoProcedure)
            {
                rtn = Public_ExecuteSqlService.Instance.NoStore_TBLSignal_GetMaxIdByEquipmentTemplateIdAndSId(EquipmentTemplateId, sId);
            }
            else
            {
                var sql = string.Format("select DisplayIndex from TBL_Signal where EquipmentTemplateId={0} and SignalId={1}", EquipmentTemplateId, sId);
                rtn = DBHelper.ExecuteScalar(sql);
            }
            
            if (string.IsNullOrEmpty(rtn))
                return false;

            DisplayIndex = int.Parse(rtn);
            return true;
        }
    }
}
