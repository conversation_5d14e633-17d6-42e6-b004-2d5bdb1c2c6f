﻿



using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using Delivery.DAL;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace DM.TestOrder.DAL {
/*    public class QueryParamTestOrderQuery {
        public int    UserId = -1; //	    用户ID
        public string Cond_StationName=""; //		站址名称	
        public string Cond_StationGroup = ""; //	站址名称	
        public string Cond_StationCode = ""; //    站址编码						
        public string Cond_FsuVendor = ""; //	FSU厂家			
        public string Cond_OrderType = ""; //.	割接类型																		
        public string Cond_ApplyTime1 = ""; //	申请开始日期
        public string Cond_ApplyTime2 = ""; //	申请截止日期					
        public string Cond_ApproveTime1 = ""; //	归档开始日期						
        public string Cond_ApproveTime2 = ""; //	归档截至日期																	
        public string Cond_OrderState = ""; //(255)=''--11.	申请单状态
    }*/

    public class WoOrderListDal
    {
        //11个参数
        public static DataTable Query(QueryParamTestOrderQuery mypara) {
            //if(string.IsNullOrWhiteSpace(mypara.Cond_ApplyTime1))
            //    mypara.Cond_ApplyTime1="";
            //if (string.IsNullOrWhiteSpace(mypara.Cond_ApplyTime2))
            //    mypara.Cond_ApplyTime2 = "";
            //if (string.IsNullOrWhiteSpace(mypara.Cond_ApproveTime1))
            //    mypara.Cond_ApproveTime1 = "";
            //if (string.IsNullOrWhiteSpace(mypara.Cond_ApproveTime2))
            //    mypara.Cond_ApproveTime2 = "";

            //string sql = string.Format("WO_TestOrder_Query {0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10}", 
            //    SHelper.GetPara(mypara.UserId), 
            //    SHelper.GetPara(mypara.Cond_StationName),
            //    SHelper.GetPara(mypara.Cond_StationGroup), 
            //    SHelper.GetPara(mypara.Cond_StationCode),
            //    SHelper.GetPara(mypara.Cond_FsuVendor), 
            //    SHelper.GetPara(mypara.Cond_OrderType),
            //    SHelper.GetPara(mypara.Cond_ApplyTime1), 
            //    SHelper.GetPara(mypara.Cond_ApplyTime2),
            //    SHelper.GetPara(mypara.Cond_ApproveTime1), 
            //    SHelper.GetPara(mypara.Cond_ApproveTime2),
            //    SHelper.GetPara(mypara.Cond_OrderState));

            //return DBHelper.GetTable(sql);
            var dt = new DataTable();
            if (CommonUtils.IsNoProcedure)
            {
                dt = WOTestOrderQueryService.Instance.WOTestOrderQuery(mypara);
                return dt;
            }

            var tb = new ExecuteSql().ExecuteStoredProcedure("WO_TestOrder_Query", new QueryParameter[] {
                 new QueryParameter("UserId", DataType.Int, mypara.UserId.ToString()),
                 new QueryParameter("Cond_StationName", DataType.String, mypara.Cond_StationName),
                 new QueryParameter("Cond_StationGroup", DataType.String, mypara.Cond_StationGroup),
                 new QueryParameter("Cond_StationCode", DataType.String, mypara.Cond_StationCode),
                 new QueryParameter("Cond_FsuVendor", DataType.String, mypara.Cond_FsuVendor),
                 new QueryParameter("Cond_OrderType", DataType.String, mypara.Cond_OrderType),
                 new QueryParameter("Cond_ApplyTime1", DataType.String, mypara.Cond_ApplyTime1),
                 new QueryParameter("Cond_ApplyTime2", DataType.String, mypara.Cond_ApplyTime2),
                 new QueryParameter("Cond_ApproveTime1", DataType.String, mypara.Cond_ApproveTime1),
                 new QueryParameter("Cond_ApproveTime2", DataType.String, mypara.Cond_ApproveTime2),
                 new QueryParameter("Cond_OrderState", DataType.String, mypara.Cond_OrderState)
            });
            return tb;
        }
    }
}

