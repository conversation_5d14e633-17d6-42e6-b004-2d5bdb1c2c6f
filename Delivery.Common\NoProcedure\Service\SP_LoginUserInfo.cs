﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;

namespace Delivery.Common.NoProcedure.Service
{
    public class SP_LoginUserInfoService
    {
        private static SP_LoginUserInfoService _instance = null;

        public static SP_LoginUserInfoService Instance
        {
            get
            {
                if (_instance == null) _instance = new SP_LoginUserInfoService();
                return _instance;
            }
        }
        private string FormatPass(string oldPwd)
        {
            var xSb = "";

            for (int i = 0; i < 40; i += 2)
            {
                xSb += Convert.ToInt32(oldPwd.Substring(i, 2), 16).ToString();
            }

            return xSb;
        }
        public DataTable SP_CheckLoginUserInfo(string LogonId, string Password)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                realParams.Add("LogonId", LogonId);
                realParams.Add("Password", Password);
                try
                {
                   string Pwd; int? UserId = null;int? isEnable=null;int ?isLocked=null;
                   string ValidTime;string NewPassword = FormatPass(Password);
                   if (LogonId == null || LogonId.TrimStart() == "")
                   {
                       return executeHelper.ExecDataTable("SP_CheckLoginUserInfo_NoData", realParams); 
                   }
                    DataTable dt = executeHelper.ExecDataTable("SP_CheckLoginUserInfo_LoginInfo", realParams);
                    if(dt!=null && dt.Rows.Count > 0)
                    {
                        UserId = (int)dt.Rows[0]["UserId"];
                        Pwd = dt.Rows[0]["Pwd"].ToString();
                        isEnable = Convert.ToInt32(dt.Rows[0]["isEnable"]);
                        isLocked = Convert.ToInt32(dt.Rows[0]["isLocked"]);
                        ValidTime = dt.Rows[0]["ValidTime"].ToString();
                        realParams.Add("UserId", UserId);
                        realParams.Add("Pwd", Pwd);
                        realParams.Add("isEnable", isEnable);
                        realParams.Add("isLocked", isLocked);
                        realParams.Add("ValidTime", ValidTime);
                        if ((Pwd.Equals(Password) || Pwd.Equals(NewPassword)) && isEnable==1 && isLocked==0 && (ValidTime == null || ValidTime == "" || DateTime.Parse(ValidTime)>DateTime.Now))
                        {
                            return executeHelper.ExecDataTable("SP_CheckLoginUserInfo_Success", realParams);
                        } else if (Pwd.Equals(Password) && (!Pwd.Equals(NewPassword)))
                        {
                            return executeHelper.ExecDataTable("SP_CheckLoginUserInfo_PassWordError", realParams);
                        } else if(isEnable == 0)
                        {
                            return executeHelper.ExecDataTable("SP_CheckLoginUserInfo_AccountInvalid", realParams);
                        } else if(isLocked == 1)
                        {
                            return executeHelper.ExecDataTable("SP_CheckLoginUserInfo_AccountLocked", realParams);
                        }else if (ValidTime == null || ValidTime == "" || (!string.IsNullOrEmpty(ValidTime) && DateTime.Parse(ValidTime) < DateTime.Now))
                        {
                            return executeHelper.ExecDataTable("SP_CheckLoginUserInfo_AccountExpired", realParams);
                        } else
                        {
                            return executeHelper.ExecDataTable("SP_CheckLoginUserInfo_AccountFailed", realParams);
                        }
                    } else
                    {
                        return new DataTable();
                    }

                }catch (Exception ex)
                {
                    Logger.Log(ex);
                    return new DataTable();
                }finally
                {
                    dbHelper.Connection.Close();
                }
            }
                
        }
        public DataTable SP_GetLoginUserInfo(int UserId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                realParams.Add("UserId", UserId);
                try
                {
                  return  executeHelper.ExecDataTable("SP_GetLoginUserInfo", realParams);
                }catch (Exception ex)
                {
                    Logger.Log(ex);
                    return new DataTable();
                }finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
    }
}
