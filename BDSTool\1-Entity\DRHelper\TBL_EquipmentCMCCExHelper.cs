﻿using BDSTool.DBUtility;
using BDSTool.Entity.B;
using Common.Logging.Pro;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.Entity.DRHelper
{
    public class TBL_EquipmentCMCCExHelper
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static TBL_EquipmentCMCCEx FromDataRow(DataRow row) {
            var entity = new TBL_EquipmentCMCCEx();
            try {                

                entity.DeviceID = row["DeviceID"].ToString();
                entity.DeviceName = row["DeviceName"].ToString();
                entity.FSUID = row["FSUID"].ToString();
                
                entity.StationId = SHelper.ToInt(row["StationId"]);
                entity.MonitorUnitId = SHelper.ToInt(row["MonitorUnitId"]);
                entity.EquipmentId = SHelper.ToInt(row["EquipmentId"]);


                entity.RoomName = row["RoomName"].ToString();
                entity.DeviceType =SHelper.ToIntNullable( row["DeviceType"]);
                entity.DeviceSubType = SHelper.ToIntNullable(row["DeviceSubType"]);
                entity.Model = row["Model"].ToString();
                entity.Brand = row["Brand"].ToString();
                entity.RatedCapacity = SHelper.ToFloat(row["RatedCapacity"]);
                entity.Version = row["Version"].ToString();
                entity.BeginRunTime = SHelper.ToDateTime(row["BeginRunTime"]);
                entity.DevDescribe = row["DevDescribe"].ToString();
                //-----------------------------------------------------------------------------------
                entity.EquipmentTemplateId = SHelper.ToInt(row["EquipmentTemplateId"]);
                entity.HouseId = SHelper.ToInt(row["HouseId"]);
                //-----------------------------------------------------------------------------------
            }
            catch (Exception ex) {
                logger.ErrorFormat("TBL_EquipmentCMCCEx.FromDataRow();error={0}", ex.Message);
                logger.Error(ex.StackTrace);
                return null;
            }
            return entity;
        }
    }
}
