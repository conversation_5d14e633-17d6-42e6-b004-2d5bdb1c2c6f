﻿@page
@model Delivery.Pages.CMCC.StationManagementModel
@{
}


@section Scripts{
    <script type="text/javascript" src="~/Scripts/lib/jquery-1.8.0.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="~/Scripts/common/ie8.js"></script>
    <script type="text/javascript" src="~/Scripts/common/common.js"></script>
    <script type="text/javascript" src="~/CMCC/scripts/StationManagement.js"></script>
}

@section Styles {
    <style type="text/css">
        #dialogtable tr td {
            padding: 5px 10px 5px 10px;
        }
        /*input.myNoInput {
            background: rgb(244, 244, 244);
            border-radius: 3px;
            height: 20px;
        }*/
    </style>
}

<div class="mask">
    <div style="position: relative; top: 50%; left: 50%">
        <div class="loading">
            正在加载，请等待...
        </div>
    </div>
</div>
<div id="MyStationDialog" title="新建站址入网管理" style="width: 850px; height: 450px;" class="easyui-dialog"
     data-options="modal:true,closed:true,closable:true">
    <table id="dialogtable" style="margin-left: 30px; margin-top: 45px">
        <tr style="height: 50px;">
            <td style="text-align: left; white-space: nowrap">分组</td>
            <td>
                <input id="StructureId" class="easyui-combotree" style="width: 140px" />
            </td>
            <td style="text-align: left; white-space: nowrap">行政区划</td>
            <td colspan="3" style="width: 440px;">
                <select id="Province" class="easyui-combobox" style="width: 137px;">
                    <option value="-1">请选择</option>
                </select>
                <select id="City" class="easyui-combobox" style="width: 137px;">
                    <option value="-1">请选择</option>
                </select>
                <select id="County" class="easyui-combobox" style="width: 137px;">
                    <option value="-1">请选择</option>
                </select>
            </td>
        </tr>
        <tr>
            <td style="text-align: left; white-space: nowrap">站址名称</td>
            <td style="display: none;" id="txtStationNameTD">
                <input type='text' class="easyui-textbox" data-options="prompt:'请输入站址名称',required:true" id="txtStationName" style="width: 140px;" />
            </td>
            <td style="display: none;" id="cboAssetStationTD">
                <select id="cboAssetStation" data-options="valueField:'ItemId',textField:'ItemValue',prompt:'请选择站址',required:true" style="width: 140px;"></select>
            </td>
            <td style="text-align: left; white-space: nowrap">站址编码</td>
            <td>
                <input type='text' class="easyui-textbox" data-options="prompt:'系统生成',disabled:'true'" id="txtStationCode" style="width: 140px;" />
            </td>
            <td style="text-align: left; white-space: nowrap">站址类型</td>
            <td style="text-align: left; white-space: nowrap">
                <select id="StationCategory" class="easyui-combobox" style="width: 140px;"></select>
            </td>
        </tr>
        <tr style="height: 50px;">
            <td style="text-align: left; white-space: nowrap">合同号</td>
            <td>
                <input type='text' class="easyui-textbox" data-options="prompt:'请输入合同号',required:'true'" id="txtContractNo" style="width: 140px;" />
            </td>
            <td style="text-align: left; white-space: nowrap">工程名称</td>
            <td>
                <input type='text' class="easyui-textbox" data-options="prompt:'请输入工程名',required:'true'" id="txtProjectName" style="width: 140px;" />
            </td>
            <td colspan="2"></td>
        </tr>
        <tr style="height: 50px;">
            <td style="text-align: left; white-space: nowrap">站址地址</td>
            <td colspan="5">
                <input type='text' class="easyui-textbox" data-options="prompt:'请输入站址地址',required:'true'" id="txtAddress" style="width: 650px;" />
            </td>
        </tr>
        <tr style="height: 50px;">
            <td>备注</td>
            <td colspan="5">
                <input type='text' class="easyui-textbox" data-options="prompt:'请输入备注信息',required:'true'" id="txtRemark" style="width: 650px;" />
            </td>
        </tr>
        <tr>
            <td colspan="6" style="height: 50px;"></td>
        </tr>

        <tr>
            <td colspan="3">
                <input type="hidden" id="txtWRStationId" />
                <input type="hidden" id="txtLogonId" />
            </td>
            <td>
                <input type="button" id="btn_new" value="提交" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btn_modify" value="修改" class="commonButton" style="width: 40pt;" />
                <input type="button" id="btn_close" value="关闭" class="commonButton" style="width: 40pt;" />
            </td>
            <td colspan="2"></td>
        </tr>
    </table>

</div>

<table style="width: 100%; margin: 0; padding: 0;">
    <tr>
        <td style="text-align: left; white-space: nowrap">站址名称:</td>
        <td>
            <input type='text' placeholder="请输入站址名称" data-options="prompt:'请输入站址名称'" class="easyui-textbox" id="txtStationNameq" style="width: 140px;" />
        </td>
        <td style="text-align: left; white-space: nowrap">站址编码:</td>
        <td>
            <input type='text' placeholder="请输入站址编码" data-options="prompt:'请输入站址编码'" class="easyui-textbox" id="txtStationCodeq" style="width: 140px;" />
        </td>
        <td style="text-align: left; white-space: nowrap">站址类型:</td>
        <td>
            <select id="StationCategoryq" class="easyui-combobox" style="width: 140px;">
                <option value=""> </option>
                <option value="0">新装入网</option>
                <option value="1">维护巡检</option>
            </select>
        </td>
    </tr>
    <tr>
        <td style="text-align: left; white-space: nowrap">申请开始日期:</td>
        <td>
            <input id="txtApplyTimeq" data-options="validType:['date[\'yyyy-MM-dd hh:mm:ss\']', 'less[\'#txtApplyTimeq2\']'],onChange:function(){$('#txtApplyTimeq2').datetimebox('validate')}" class="easyui-datetimebox" style="width: 140px;" />
        </td>
        <td style="text-align: left; white-space: nowrap">申请截止日期:</td>
        <td>
            <input id="txtApplyTimeq2" data-options="validType:['date[\'yyyy-MM-dd hh:mm:ss\']','greater[\'#txtApplyTimeq\']'],onChange:function(){$('#txtApplyTimeq').datetimebox('validate')}" class="easyui-datetimebox" style="width: 140px;" />
        </td>
        <td style="text-align: left; white-space: nowrap">申请单状态:</td>
        <td>
            <select id="StationStatusq" class="easyui-combobox" style="width: 140px;">
                <option value=""> </option>
                <option value="1">入网申请</option>
                <option value="2">申请退回</option>
                <option value="3">申请通过</option>
            </select>
        </td>
    </tr>

    <tr>
        <td>分组</td>
        <td style="text-align: left;">
            <input id="StructureIdLook" class="easyui-combotree" style="width: 140px;" />
        </td>
        <td colspan="4" style="text-align: center;">
            <input type="button" id="btn_seach" value="查询" class="commonButton" style="width: 40pt;" />
            <input type="button" id="btn_Reset" value="重置" class="commonButton" style="width: 40pt;" />
            <input type="button" id="btn_Edit" value="编辑" class="commonButton" style="width: 40pt;" />
            <input type="button" id="btn_delete" value="删除" class="commonButton" style="width: 40px;" />
            <input type="button" id="btn_Pass" value="通过" class="commonButton" style="width: 40pt;" />
            <input type="button" id="btn_back" value="退回" class="commonButton" style="width: 40pt;" />
            <input type="button" id="btn_Excel" value="导出Excel" class="commonButton" style="width: 60pt;" />
            <input type="button" id="btn_opendialog" value="新增站址入网" class="commonButton" style="width: 80pt;" />
            <button id="btn_help" class="commonButton" style="width: 80pt;">
                <i style="display:inline-block; vertical-align: middle;width: 16px; height: 16px; background-image: url(../Content/themes/icons/help.png); background-size: contain;"></i>
                规范浏览
            </button>
        </td>
    </tr>
</table>

<table id="StationTable"></table>

<div id="BackDialog" title="站址入网管理--退回申请" style="width: 550px; height: 250px;" class="easyui-dialog"
     data-options="modal:true,closed:true,closable:true">
    <table id="Table1">
        <tr style="height: 50px;">
            <td colspan="3"></td>
        </tr>
        <tr>
            <td style="width: 150px; height: 80px; text-align: right;">请填写退回原因:</td>
            <td>
                <textarea name="TxtRejectReason" id="TxtRejectReason" class="easyui-textbox" data-options="prompt:'请填写退回原因',required:'true'" style="width: 300px;"></textarea>
            </td>
            <td></td>
        </tr>
        <tr style="height: 10px;">
            <td colspan="3"></td>
        </tr>
        <tr>
            <td style="width: 50px;"></td>
            <td>
                <input type="button" id="btn_AppBackRe" value="提交" class="commonButton" style="width: 40pt;" />
            </td>
            <td></td>
        </tr>
    </table>
</div>

<div id="standardDoc" class="easyui-dialog" title="规范浏览" style="padding: 0px;" data-options="iconCls:'icon-help', closed: true, resizable: true, width:500">
    <textarea id="standardContent" style="margin: auto; width: 466px; height:300px; padding: 10px; border-width: 0; outline: none; resize: none; color:#000; font-family: Arial; font-size: 16px;" disabled="disabled">编码规范</textarea>
    <div id="toolbar" style="text-align: center; padding: 10px 0;">
        <a href="javascript:void(0);" id="btn_docEdit" class="easyui-linkbutton" data-options="iconCls:'icon-edit'">编辑</a>
        <a href="javascript:void(0);" id="btn_docSave" class="easyui-linkbutton" data-options="iconCls:'icon-save'">保存</a>
    </div>
</div>