div.pq-grid{
	/*border:1px solid #99bce8;*/
	/*border-radius:5px;*/
	background:url("");/*to remove ui-widget-content background*/	
	color:#333;
}
div.pq-grid-top{
	/*border-radius:5px 5px 0 0;*/
	background:#eee;
	color:#666;
}
div.pq-grid-bottom{
	background:#eee;
	color:#333;
	/*border-radius:0 0 5px 5px;*/	
}
.pq-grid div.pq-header-outer{	
	background:#eee;
	border-bottom:1px solid #c5c5c5;	
}
.pq-grid span.pq-grid-header{
	background:url('images/column-header-bg.png');
	
	background-image: linear-gradient(#fefefe,#e6e6e6);

	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fefefe', endColorstr='#e6e6e6'); 
	background: -webkit-gradient(linear, left top, left bottom, from(#fefefe), to(#e6e6e6)); 
	background: -moz-linear-gradient(top,  #fefefe,  #e6e6e6); /* for firefox 3.6+ */	
}
.pq-grid-header td.pq-col-sort-asc,.pq-grid-header td.pq-col-sort-desc{
	background: url(images/column-header-over-bg.gif) #ebf3fd repeat-x 0px top;								
}
.pq-grid td.pq-grid-col{/*header cell*/
	font-weight:bold;
	color:#666;
	text-shadow:0 1px 0 #fff;
	border-left-width:0px;
	border-bottom-width:0px;
}
div.pq-cont{
	background:#fff;
}
div.pq-cont *{
	font-weight:normal;
}
td.pq-grid-cell{
	background:#fff;
	color:#333;
}	
tr.pq-grid-oddRow td{
	background:#fafafa;
}
div.pq-hvscroll-square{
	background:#eee;
}			
tr.pq-grid-row-hover td
{
    background-color:#d8e8fa;
	/*background-color:#efefef;*/  
}     
tr.pq-row-select td
{
    background-color:#6699cc;
    color:#fff;
}
div.pq-grid tr td.pq-grid-cell-hover
{
    background:#d8e8fa;
	border-width:0px;/*TR*/	 
} 
.pq-grid tr td.pq-grid-number-cell
{
    background:#e4ecf7;
	/*border:1px solid #9eb6ce;
	border-top:0px;
	border-left:0px;*/		   
    color:#333; 
}
div.pq-grid tr td.pq-cell-select
{    
	background:#88bbdd;      
	color:#fff;
}       
.pq-grid div.pq-loading-mask{
	background:#dae5f3;
	border:1px solid steelblue;
	color:#333;
	font-weight:normal;
}
div.pq-loading-mask div{
	border:1px solid #999;
	background:#ddd;
}