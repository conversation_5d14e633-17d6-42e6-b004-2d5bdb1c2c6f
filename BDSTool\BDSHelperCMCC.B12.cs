﻿
using System;
using System.Collections.Generic;
using System.Data;
//using System.Data.Odbc;
using System.Linq;
using System.Text;
using Carrier.BDSTool;

using Common.Logging.Pro;


using ENPC.Kolo.Entity.B_CMCC;
using BDSTool.Entity.B;
using BDSTool.DBUtility.Common;
using BDSTool.DBUtility;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace BDSTool.Entity.B
{
    public class FSUStatus
    {
        public string FSUID {
            get;
            set;
        }
        public string FSUIP {
            get;
            set;
        }
        public float? CPUUsage {
            get;
            set;
        }
        public float? MEMUsage {
            get;
            set;
        }
        public int? GetFSUInfoResult {
            get;
            set;
        }
        public string GetFSUFaliureCause {
            get;
            set;
        }
        public DateTime? GetFSUTime {
            get;
            set;
        }

    }
}

namespace Carrier.BDSTool
{
    public partial class BDSHelperCMCC
    {     
        #region B12 获取FSU状态信息 for chenbinbin    
       public static bool UpdateFSUInfo(GetFsuInfoAck response) {
            var CPUUsage=response.FsuStatus.CPUUsage;
            var MEMUsage = response.FsuStatus.MEMUsage;
            var Result=response.Result;
            var FailureCause=response.FailureCause;
            var FSUID = response.FSUID;

            try {
                string sql;
                if (CommonUtils.IsNoProcedure) {
                    string TempSql = Public_ExecuteSqlService.Instance.NoStore_TSLMonitorUnitCMCC_GetUpdateSql();
                    sql = string.Format(TempSql, CPUUsage, MEMUsage, (int)Result, SHelper.GetPara(FailureCause), SHelper.GetPara(FSUID));
                }
                else
                {
                    sql = string.Format("UPDATE TSL_MonitorUnitCMCC SET CPUUsage={0}, MEMUsage={1}, GetFSUInfoResult={2}, GetFSUFaliureCause={3}, GetFSUTime=getdate() WHERE FSUID={4}",
                                        CPUUsage, MEMUsage, (int)Result, SHelper.GetPara(FailureCause), SHelper.GetPara(FSUID));
                }

                DbConfigPara para= DBHelper.GetDbConfig();
                if (para.ProviderName.Trim().ToLower() == "mysql")
                {
                    if (CommonUtils.IsNoProcedure)
                    {
                        string TempSql = Public_ExecuteSqlService.Instance.NoStore_TSLMonitorUnitCMCC_GetUpdateSql();
                        sql = string.Format(TempSql, CPUUsage, MEMUsage, (int)Result, SHelper.GetPara(FailureCause), SHelper.GetPara(FSUID));
                    }
                    else
                    {
                        sql = string.Format("UPDATE TSL_MonitorUnitCMCC SET CPUUsage={0}, MEMUsage={1}, GetFSUInfoResult={2}, GetFSUFaliureCause={3}, GetFSUTime=now() WHERE FSUID={4}",
                    CPUUsage, MEMUsage, (int)Result, SHelper.GetPara(FailureCause), SHelper.GetPara(FSUID));
                    }
                }
                DBHelper.ExecuteNonQuery(sql);

                return true;
            }
            catch (Exception ex) {
                logger.ErrorFormat("UpdateFSUInfo();fsuID={0};;Error={1}", FSUID, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
        }

        public static bool QueryAllFSUInfo(ref List<FSUStatus> listFSUStatus) {
            try {
                DataTable table = null;
                if (CommonUtils.IsNoProcedure)
                {
                    table = Public_ExecuteSqlService.Instance.NoStore_TSLMonitorUnitCMCC_GetInfo();
                }
                else
                {
                    var sql = string.Format("select FSUID, FSUIP, CPUUsage, MEMUsage, GetFSUInfoResult, GetFSUFaliureCause, GetFSUTime from TSL_MonitorUnitCMCC");
                    table = DBHelper.GetTable(sql);
                }

                foreach (DataRow r in table.Rows) {
                    var s=new FSUStatus();
                    s.FSUID = r["FSUID"].ToString();
                    s.FSUIP = r["FSUIP"].ToString();
                    s.CPUUsage = r["CPUUsage"] == DBNull.Value ? 0 : float.Parse(r["CPUUsage"].ToString());
                    s.MEMUsage = r["MEMUsage"] == DBNull.Value ? 0 : float.Parse(r["MEMUsage"].ToString());
                    s.GetFSUInfoResult = r["GetFSUInfoResult"] == DBNull.Value ? 0 : int.Parse(r["GetFSUInfoResult"].ToString());
                    s.GetFSUFaliureCause = r["GetFSUFaliureCause"].ToString();
                    //s.GetFSUTime = (r["GetFSUTime"] == DBNull.Value) ? null : DateTime.Now;//                   
                    if (r["GetFSUTime"] == DBNull.Value)
                        s.GetFSUTime = null;
                    else
                        s.GetFSUTime = DateTime.Parse(r["GetFSUTime"].ToString());



                    listFSUStatus.Add(s);
                }
                return true;
            }
            catch (Exception ex) {
                logger.ErrorFormat("QueryAllFSUInfo();Error={0}", ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
        }

        public static bool QueryOneFSUInfo(string FSUID, ref FSUStatus fsuStatus) {
            try {
                DataTable table = null;
                if (CommonUtils.IsNoProcedure)
                {
                    table = Public_ExecuteSqlService.Instance.NoStore_TSLMonitorUnitCMCC_GetInfosByFsuId(FSUID);
                }
                else
                {
                    var sql = string.Format(@"select FSUID, FSUIP, CPUUsage, MEMUsage, GetFSUInfoResult, GetFSUFaliureCause, GetFSUTime from TSL_MonitorUnitCMCC Where FSUID={0}",
                    SHelper.GetPara(FSUID));
                    table = DBHelper.GetTable(sql);
                }

                if (table.Rows.Count == 0)
                    return false;

                DataRow r=table.Rows[0];

                fsuStatus.FSUID = r["FSUID"].ToString();
                fsuStatus.FSUIP = r["FSUIP"].ToString();
                fsuStatus.CPUUsage = r["CPUUsage"] == DBNull.Value ? 0 : float.Parse(r["CPUUsage"].ToString());
                fsuStatus.MEMUsage = r["MEMUsage"] == DBNull.Value ? 0 : float.Parse(r["MEMUsage"].ToString());
                fsuStatus.GetFSUInfoResult = r["GetFSUInfoResult"] == DBNull.Value ? 0 : int.Parse(r["GetFSUInfoResult"].ToString());

                fsuStatus.GetFSUFaliureCause = r["GetFSUFaliureCause"].ToString();

                if (r["GetFSUTime"] == DBNull.Value)
                    fsuStatus.GetFSUTime = null;
                else
                    fsuStatus.GetFSUTime = DateTime.Parse(r["GetFSUTime"].ToString());

                return true;
            }
            catch (Exception ex) {
                logger.ErrorFormat("QueryOneFSUInfo();Error={0}", ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
        }
        #endregion
    }
}
