﻿using BDSTool.BLL.Convert;
using Common.Logging.Pro;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace BDSTool.Entity.B
{
    public enum TypeOfSEC
    {
        Signal=1,
        Event=2,
        Control=3
    }
    public class TSignalEx
    {
        public TSignal tsig;
        public int TSignalId;       
        public TypeOfSEC SECType;

        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static bool Convert2TSignalExList(List<TSignal> tSignals, ref List<TSignalEx> listTSignalsEx, ref string errmsg) {
            foreach (var tsig in tSignals) {
                var tsigEx = new TSignalEx();
                tsigEx.tsig = tsig;

                if (tsig.Type == EnumType.DI || tsig.Type == EnumType.AI) {
                    tsigEx.SECType = TypeOfSEC.Signal;
                }
                else if (tsig.Type == EnumType.ALARM) {
                    tsigEx.SECType = TypeOfSEC.Event;
                }
                else if (tsig.Type == EnumType.AO || tsig.Type == EnumType.DO) {
                    tsigEx.SECType = TypeOfSEC.Control;
                }
                //else if ((int)tsig.Type == 5) {//0818修改，字典表中被监控设备通信中断告警的类型数值为5
                //    tsigEx.SECType = TypeOfSEC.Event;
                //}
                else {
                    errmsg = string.Format("信号[{0}]的类型无效.类型={1}", tsig.SignalName,(int)tsig.Type);
                    logger.Info("Convert2TSignalExList(); "+errmsg);
                    return false;
                }

                int secId = 0;
                if (!ConvertBID2SiteWebId(tsig.ID, tsig.SignalNumber, ref secId)) {
                    logger.Info("Convert2TSignalExList(); ConvertBID2SiteWebId failed");
                    return false;
                }
                    
                tsigEx.TSignalId = secId;

                //数据有效性检查-ID不能重复
                if (listTSignalsEx.Exists(o=>o.TSignalId==tsigEx.TSignalId )){
                    errmsg = string.Format("ID为{0}的监控点信号的多个实例未正确设置顺序号", tsig.ID);
                    logger.Info("Convert2TSignalExList(); " + errmsg);
                    return false;
                }

                //格式化XXX
                //if (tsigEx.tsig.SignalNumber != "NULL") {
                //    tsigEx.tsig.SignalNumber = int.Parse(tsigEx.tsig.SignalNumber).ToString("000");
                //}

                listTSignalsEx.Add(tsigEx);
            }
            return true;
        }

        public static List<int> GetTSignaIdList(List<TSignal> tsigs) {
            var ids = new List<int>();
            foreach (var tsig in tsigs) {
                int sigId = 0;
                if (TSignalEx.ConvertBID2SiteWebId(tsig.ID, tsig.SignalNumber,ref sigId) == false)
                    return null;

                ids.Add(sigId);
            }
            return ids;
        }
        //public static bool GetTSignaId(TSignal tsig, ref int siteWebId) {
        //    var xxx = "000";
        //    if (string.IsNullOrEmpty(tsig.SignalNumber) == false && tsig.SignalNumber.ToUpper() != "NULL") {
        //        xxx = tsig.SignalNumber.PadLeft(3, '0');
        //    }
        //    if (int.TryParse(tsig.ID + xxx, out siteWebId) == false) {
        //        logger.ErrorFormat("TSignalEx.GetTSignaId(); Failded; ID={0},SignalNumber={1}; ", tsig.ID, xxx);
        //        return false;
        //    }


        //    return true;
        //}

        public static bool ConvertSiteWebId2BID(int secId, ref string ID, ref string SignalNumber) {
            string sSecId = secId.ToString();
            SignalNumber = sSecId.Substring(sSecId.Length - 3);
            ID = sSecId.Substring(0, sSecId.Length - 3);

            //20170522 ID补齐为6位
            ID = ID.PadLeft(6, '0');

            //20170522 移动B接口标准文档升级，SignalNumber可以为000，因此注释掉以下代码
            //if (SignalNumber == "000")
            //    SignalNumber = "NULL";//文档明确，不适应该场景，取值为NULL

            return true;
        }
        public static bool ConvertBID2SiteWebId(string ID, string SignalNumber, ref int siteWebId) {
            var xxx = "000";
            if (string.IsNullOrEmpty(SignalNumber) == false && SignalNumber.ToUpper() != "NULL") {
                xxx = SignalNumber.PadLeft(3, '0');
            }
            if (int.TryParse(ID + xxx, out siteWebId) == false) {
                logger.ErrorFormat("TSignalEx.ConvertBID2SiteWebId(); Failded; ID={0},SignalNumber={1}; ", ID, xxx);
                return false;
            }

            return true;
        }
    }
}
