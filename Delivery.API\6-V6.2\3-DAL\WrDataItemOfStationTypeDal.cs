﻿
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using Delivery.DAL;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;


namespace DM.TestOrder.DAL {
    public class WrDataItemOfStationTypeDal
    {
        public static DataTable  GetAll() {
            DataTable tb;
            if (CommonUtils.IsNoProcedure)
            {
                tb = Public_ExecuteSqlService.Instance.NoStore_WRDataItem_GetAll();
            }
            else
            {
                var sql = "select * from WR_DataItem where  EntryId=6 and (ExtendField3='' or ExtendField3 is NULL)";
                //var tb = DBHelper.GetTable(sql);
                tb = new ExecuteSql().ExecuteSQL(sql);
            }
            return tb;
        }

        public static string AddOne(int ItemId, string ItemValue) {
            if (CommonUtils.IsNoProcedure)
            {
                var rtn1 = WoCommonService.Instance.WO_CR2_StationType_Add(ItemId, ItemValue);
                return rtn1 == null ? null : rtn1.ToString();
            }
            //var sql = string.Format("WO_CR2_StationType_Add {0},{1}",
            //    ItemId, SHelper.GetPara(ItemValue));

            //var rtn = DBHelper.ExecuteScalar(sql);
            var rtn = new ExecuteSql().ExecuteStoredProcedureScalar("WO_CR2_StationType_Add", new QueryParameter[] {
                new QueryParameter("ItemId", DataType.Int, ItemId.ToString()),
                new QueryParameter("ItemValue", DataType.String, ItemValue)
            });
            return rtn == null ? null : rtn.ToString();
        }
        public static string DeleteOne(int ItemId) {
            //var sql = string.Format("update  WR_DataItem set ExtendField3='delete' where  EntryId=6 and  ItemId={0}", ItemId);
            //var sql = string.Format("delete from  WR_DataItem  where  EntryId=6 and  ItemId={0}", ItemId);
            //         DBHelper.ExecuteNonQuery(sql);

            //         sql = string.Format("delete WO_DicStationTypeMap where WR_ItemId={0}", ItemId);
            //         DBHelper.ExecuteNonQuery(sql);
            if (CommonUtils.IsNoProcedure)
            {
                Public_ExecuteSqlService.Instance.NoStore_DeleteTwoTablesByItemId(ItemId.ToString());
            }
            else { 
                ExecuteSql executeSql = new ExecuteSql();
                var sql = "delete from WR_DataItem  where EntryId = 6 and ItemId = @itemId";
                executeSql.ExecuteSQLNoQuery(sql, new QueryParameter[] { new QueryParameter("itemId", DataType.Int, ItemId.ToString()) });
                sql = "delete WO_DicStationTypeMap where WR_ItemId= @ItemId";
                executeSql.ExecuteSQLNoQuery(sql, new QueryParameter[] { new QueryParameter("itemId", DataType.Int, ItemId.ToString()) });
            }
            return "OK";
        } 
    }
}

