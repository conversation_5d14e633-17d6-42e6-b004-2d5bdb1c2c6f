﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 重启FSU
    /// </summary>
    public class SetFsuReboot : BMessage
    {
        public SetFsuReboot() : base()
        {
            MessageType = (int)BMessageType.SET_FSUREBOOT;
        }

        public SetFsuReboot(string fsuId) : base()
        {
            MessageType = (int)BMessageType.SET_FSUREBOOT;
            FSUID = fsuId;
        }

        public override string Serialize()
        {
            string result = string.Empty;
            try
            {
                JsonSerializerSettings settings = new JsonSerializerSettings();
                settings.ContractResolver = new CustomContractResolver();
                TIotEntity iotEntity = new TIotEntity(this);
                result = JsonConvert.SerializeObject(iotEntity, settings);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetFsuReboot.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetFsuReboot.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
            }
            return result;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0},{1},{2}", MessageId, (BMessageType)MessageType, FSUID);
            return sb.ToString();
        }
    }
}