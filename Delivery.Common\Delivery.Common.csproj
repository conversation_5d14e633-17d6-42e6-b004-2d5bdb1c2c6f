﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="dmdbms.DmProvider" Version="1.1.0.16649" />
    <PackageReference Include="log4net" Version="2.0.12" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.WebUtilities" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="5.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="5.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="5.0.0" />
    <PackageReference Include="Microsoft.Net.Http.Headers" Version="2.2.8" />
    <PackageReference Include="MySql.Data" Version="8.0.30" />
    <PackageReference Include="NPOI" Version="2.5.2" />
    <PackageReference Include="Serilog" Version="2.10.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="3.3.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Delivery.Entity\Delivery.Entity.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="NoProcedure\Service\EventCategoryService.cs">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </Compile>
  </ItemGroup>

</Project>
