﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.6.9.获取FSU的FTP信息应答
    /// </summary>
    public sealed class GetFtpAck:BMessage
    {
        public string UserName { get; private set; }
        public string PassWord { get; private set; }
        public EnumResult Result { get; private set; }
        public string FailureCause { get; private set; }

        public GetFtpAck(string userName, string password, string fsuId, EnumResult result, string failureCause):base()
        {
            MessageType = (int)BMessageType.GET_FTP_ACK;

            UserName = userName;
            PassWord = password;
            FSUID = fsuId;
            Result = result;
            FailureCause = failureCause;
        }

        public GetFtpAck() : base() 
        {
            MessageType = (int)BMessageType.GET_FTP_ACK;
        }

        public static GetFtpAck Deserialize(XmlDocument xmlDoc)
        {
            GetFtpAck getFtpAck = null;
            try
            {
                string userName = xmlDoc.SelectSingleNode("/Response/Info/UserName").InnerText.Trim();
                string password = xmlDoc.SelectSingleNode("/Response/Info/PassWord").InnerText.Trim();
                string fsuId = xmlDoc.SelectSingleNode("/Response/Info/FSUID").InnerText.Trim();
                string resultString = xmlDoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim();
                string failureCause = xmlDoc.SelectSingleNode("/Response/Info/FailureCause").InnerText.Trim();
                EnumResult result;
                if (string.IsNullOrEmpty(resultString))
                {
                    result = EnumResult.FAILURE;
                }
                else
                {
                    result = (EnumResult)int.Parse(resultString);
                }

                getFtpAck = new GetFtpAck(userName, password, fsuId, result, failureCause);
                entityLogger.DebugFormat("GetFtpAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                getFtpAck.StringXML = xmlDoc.InnerXml;
                return getFtpAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetFtpAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetFtpAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                getFtpAck = new GetFtpAck();
                getFtpAck.ErrorMsg = ex.Message;
                getFtpAck.StringXML = xmlDoc.InnerXml;
                return getFtpAck;
            }
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}, {2}:{3},{4},{5},{6}",
                MessageId, (BMessageType)MessageType, FSUID, UserName, PassWord, Result, FailureCause);
        }

    }
}
