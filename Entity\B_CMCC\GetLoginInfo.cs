﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.6.7.获取FSU注册信息
    /// </summary>
    public sealed class GetLoginInfo:BMessage
    {

        public GetLoginInfo(string fsuId):base()
        {
            MessageType = (int)BMessageType.GET_LOGININFO;

            FSUID = fsuId;
        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmldoc = CreateXmlDocument();
                AppendPK_Type(xmldoc, "Request", BMessageType.GET_LOGININFO.ToString());

                XmlElement xe2 = xmldoc.CreateElement("Info");
                XmlElement xe21 = xmldoc.CreateElement("FSUID");
                xe21.InnerText = FSUID;
                xe2.AppendChild(xe21);

                XmlNode root = xmldoc.SelectSingleNode("Request");
                root.AppendChild(xe2);

                StringXML = xmldoc.InnerXml;
                entityLogger.DebugFormat("GetLoginInfo.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmldoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetLoginInfo.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetLoginInfo.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}: {2}", MessageId, (BMessageType)MessageType, FSUID);
            return sb.ToString();
        }

    }
}
