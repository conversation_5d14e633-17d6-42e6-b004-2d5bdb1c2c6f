﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 更新FSU状态信息获取周期
    /// </summary>
    public sealed class UpdateFsuinfoIntervalAck:BMessage
    {

        public EnumResult Result { get; private set; }

        public string FailureCause { get; private set; }

        public UpdateFsuinfoIntervalAck(string fsuId, EnumResult result, string failureCause)
            : base()
        {
            MessageType = (int)BMessageType.UPDATE_FSUINFO_INTERVAL_ACK;

            FSUID = fsuId;
            Result = result;
            FailureCause = failureCause;
        }

        public UpdateFsuinfoIntervalAck() : base()
        {
            MessageType = (int)BMessageType.UPDATE_FSUINFO_INTERVAL_ACK;
        }

        public static UpdateFsuinfoIntervalAck Deserialize(XmlDocument xmlDoc)
        {
            UpdateFsuinfoIntervalAck updateFsuinfoIntervalAck = null;
            try
            {
                string fsuId = xmlDoc.SelectSingleNode("/Response/Info/FSUID").InnerText.Trim();
                string resultString = xmlDoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim();
                string failureCause = xmlDoc.SelectSingleNode("/Response/Info/FailureCause").InnerText.Trim();
                EnumResult result;
                if (string.IsNullOrEmpty(resultString))
                {
                    result = EnumResult.FAILURE;
                }
                else
                {
                    result = (EnumResult)int.Parse(resultString);
                }
                updateFsuinfoIntervalAck = new UpdateFsuinfoIntervalAck(fsuId, result, failureCause);
                entityLogger.DebugFormat("UpdateFsuinfoIntervalAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                updateFsuinfoIntervalAck.StringXML = xmlDoc.InnerXml;
                return updateFsuinfoIntervalAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("UpdateFsuinfoIntervalAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("UpdateFsuinfoIntervalAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                updateFsuinfoIntervalAck = new UpdateFsuinfoIntervalAck();
                updateFsuinfoIntervalAck.ErrorMsg = ex.Message;
                updateFsuinfoIntervalAck.StringXML = xmlDoc.InnerXml;
                return updateFsuinfoIntervalAck;
            }
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}, {2}: {3}, {4}",
                MessageId, (BMessageType)MessageType, FSUID, Result, FailureCause);
        }

    }
}
