﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class SetFTPAck : BMessage
    {
        public EnumResult Result { get; set; }
        public SetFTPAck() : base()
        {
            MessageType = (int)BMessageType.SET_FTP_ACK;
        }
        public SetFTPAck(string suids, string surids, EnumResult result) : base()
        {
            MessageType = (int)BMessageType.SET_FTP_ACK;
            SUId = suids;
            SURId = surids;
            Result = result;
        }
        public static SetFTPAck Deserialize(XmlDocument xmlDoc)
        {
            SetFTPAck setFTPParameterAck = null;
            try
            {
                string suid = xmlDoc.SelectSingleNode("/Response/Info/SUId").InnerText.Trim();
                string surid = xmlDoc.SelectSingleNode("/Response/Info/SURId").InnerText.Trim();
                string result = xmlDoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim();
                setFTPParameterAck = new SetFTPAck(suid, surid, string.IsNullOrEmpty(result) ? EnumResult.FAILURE : (EnumResult)int.Parse(result));
                entityLogger.DebugFormat("SetFTPAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                setFTPParameterAck.StringXML = xmlDoc.InnerXml;
                return setFTPParameterAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetFTPAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetFTPAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                setFTPParameterAck = new SetFTPAck();
                setFTPParameterAck.ErrorMsg = ex.Message;
                setFTPParameterAck.StringXML = xmlDoc.InnerXml;
                return setFTPParameterAck;
            }

        }
        public override string ToString()
        {
            return String.Format("{0}, {1},{2},{3}, {4}: {5}",
                MessageId, (BMessageType)MessageType,MessageType, SUId, SURId, Result);
        }
    }
}
