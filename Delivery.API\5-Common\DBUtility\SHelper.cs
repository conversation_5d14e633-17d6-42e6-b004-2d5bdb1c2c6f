﻿

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace DM.TestOrder.Common.DBA
{
    public class SHelper
    {
        public static string GetPara(bool para) {
            return para==true?"1":"0";
        }

        //to do
        public static string GetPara(DateTime? para) {
            if(para==null)
                return "NULL";

            return "'" + para.Value.ToString("yyyy-MM-dd HH:mm:ss") + "'";
        }


        public static string GetPara(string para) {
            if (para==null)
                return "NULL";
            else if (string.IsNullOrEmpty(para))
                return "''";
            else {
                if(para.IndexOf('\'')!=-1){
                    //int i = 1;
                    para = para.Replace("'", "''");
                }
                return "'" + para + "'";
            }
                
        }

        public static string GetPara(double? para) {
            if (!para.HasValue)
                return "NULL";
            else
                return para.ToString();
        }

        public static string GetPara(decimal? para) {
            if (!para.HasValue)
                return "NULL";
            else
                return para.ToString();
        }


        public static string GetPara(float? para) {
            if (!para.HasValue)
                return "NULL";
            else
                return para.ToString();
        }

        public static string GetPara(int? para) {
            if (!para.HasValue)
                return "NULL";
            else
                return para.ToString();
        }

        public static short ToShort(object o) {
            if (o == null || o == DBNull.Value)
                return 0;

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return 0;
            return short.Parse(s);
        }

        public static int ToInt(object o) {
            if (o == null || o == DBNull.Value)
                return 0;

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return 0;
            return int.Parse(s);
        }
        public static float ToFloat(object o) {
            if(o==null || o == DBNull.Value )
                return 0;

            var s=o.ToString();

            if(string.IsNullOrEmpty(s))
                return 0;
            return float.Parse(s);
        }
        public static double ToDouble(object o) {
            if (o == null || o == DBNull.Value)
                return 0;

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return 0;
            return double.Parse(s);
        }

        public static decimal ToDecimal(object o) {
            if (o == null || o == DBNull.Value)
                return 0;

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return 0;
            return decimal.Parse(s);
        }

        public static DateTime ToDateTime(object o) {
            if (o == null || o == DBNull.Value)
                return DateTime.Parse("1970-01-01 00:00:00");

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return DateTime.Parse("1970-01-01 00:00:00");

            return DateTime.Parse(s);
        }
        #region can be null
        public static int? ToIntNullable(object o) {
            if (o == null || o == DBNull.Value)
                return null;

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return null;

            return int.Parse(s);
        }
        public static long? ToLongNullable(object o) {
            if (o == null || o == DBNull.Value)
                return null;

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return null;

            return long.Parse(s);
        }


        public static short? ToShortNullable(object o) {
            if (o == null || o == DBNull.Value)
                return null;

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return null;

            return short.Parse(s);
        }

        public static float? ToFloatNullable(object o) {
            if (o == null || o == DBNull.Value)
                return null;

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return null;

            return float.Parse(s);
        }

        public static double? ToDoubleNullable(object o) {
            if (o == null || o == DBNull.Value)
                return null;

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return null;

            return double.Parse(s);
        }

        public static DateTime? ToDateTimeNullable(object o) {
            if (o == null || o == DBNull.Value)
                return null;

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return null;

            return DateTime.Parse(s);
        }

        public static bool ToBool(object o) {
            if (o == null || o == DBNull.Value)
                return false;

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return false;

            if (s.ToUpper() == "TRUE")
                return true;
            else
                return false;
        }
        #endregion

        public static string  RegulateParam(string s) {
            if (string.IsNullOrWhiteSpace(s))
                return "";
            else{
                s = s.Replace("'", "");
                s = s.Trim();
                return s;
            }            
        }
    }
}
