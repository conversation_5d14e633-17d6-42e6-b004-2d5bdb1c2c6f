﻿



using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Text;
using testorder.dal;
using DM.TestOrder.DAL.Helper;

using DM.TestOrder.Model;
using Delivery.DAL;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace DM.TestOrder.DAL {


    public partial class WoFlowDal
    {
        //4.4.4.	【FT004_WO_04：提交入网申请】
        //1)	输入 用户ID，工单号

        public static void WO_SubmitOnlineApply(int OrderId, int ApplyUserId) {



            //var sql = string.Format("WO_SubmitOnlineApply {0},{1}", OrderId, ApplyUserId);

            //var rtn = DBHelper.ExecuteScalar(sql);
            if(CommonUtils.IsNoProcedure)
            {
                var rtn1 = WoPartSPService.Instance.WoSubmitOnlineApply(OrderId, ApplyUserId);
                if (rtn1 != "OK")
                {
                    Debug.WriteLine(rtn1);
                    throw new Exception(rtn1);
                }
                return;
            }
            var rtnVal = new ExecuteSql().ExecuteStoredProcedureScalar("WO_SubmitOnlineApply", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, OrderId.ToString()),
                new QueryParameter("StateSetUserId", DataType.Int, ApplyUserId.ToString())
            });
            var rtn = rtnVal == null ? null : rtnVal.ToString();
            if (rtn!="OK") {
                Debug.WriteLine(rtn);
                throw new Exception(rtn);
            }

        }

        //4.4.6.	【FT004_WO_06：提交专家组审核结果】
//@OrderId int,
//@StateSetUserId int,
//@ExpertIsApprove	int,
//@ExpertDecision		NVARCHAR(255),
//@ExpertNote			NVARCHAR(255)
        public static void WO_SubmitExpertDecision(SectionExpertApprove secExpert) {
            var OrderId = secExpert.OrderId;
            var     IsApprove = secExpert.ExpertIsApprove;
            var     StateSetUserId = secExpert.StateSetUserId;
            var     Decision = secExpert.ExpertDecision;
            var     Note = secExpert.ExpertNote;
            var     PassResultList = secExpert.PassResultList;

            ExecuteSql executeSql = new ExecuteSql();

            //var sql = string.Format("WO_SubmitExpertDecision {0},{1},{2},{3},{4}",
            //    OrderId,
            //    StateSetUserId,
            //     SHelper.GetPara(Decision),
            //     SHelper.GetPara(Note),
            //    SHelper.GetPara(IsApprove)
            //     );

            //var rtn = DBHelper.ExecuteScalar(sql);
            if(CommonUtils.IsNoProcedure)
            {
                var rtn1 = WoPartSPService.Instance.SubmitExpertDecision(OrderId, StateSetUserId, Decision, Note, IsApprove);
                if (rtn1 != "OK")
                {
                    Debug.WriteLine(rtn1);
                    throw new Exception(rtn1);
                }
            }
            var rtnVal = executeSql.ExecuteStoredProcedureScalar("WO_SubmitExpertDecision", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, OrderId.ToString()),
                new QueryParameter("StateSetUserId", DataType.Int, StateSetUserId.ToString()),
                new QueryParameter("ExpertDecision", DataType.String, Decision),
                new QueryParameter("ExpertNote", DataType.String, Note),
                new QueryParameter("ExpertIsApprove", DataType.Int, IsApprove.ToString()),
            });
            var rtn = rtnVal == null ? null : rtnVal.ToString();
            if (rtn != "OK") {
                Debug.WriteLine(rtn);
                throw new Exception(rtn);
            }

            foreach (var c in secExpert.PassResultList) {
                if (CommonUtils.IsNoProcedure)
                {
                    WO_SubmitExpertService.Instance.ExpertCheckUpdateList(c.OrderId, c.CheckDicId, c.IsPass, c.PassNote);
                }
                else
                {
                    executeSql.ExecuteStoredProcedureNoQuery("WO_TestOrderExpertCheckList_Update", new QueryParameter[] {
                      new QueryParameter("OrderId", DataType.Int, c.OrderId.ToString()),
                      new QueryParameter("CheckDicId", DataType.Int, c.CheckDicId.ToString()),
                      new QueryParameter("IsPass", DataType.Int, c.IsPass.ToString()),
                      new QueryParameter("PassNote", DataType.String, c.PassNote)
                    });
                }
            }
        }


        //4.4.8.	【FT004_WO_08：提交入网复审结果】
//CREATE PROC WO_SubmitFinalDecision
//    @OrderId int,
//    @StateSetUserId int,

//    @FinalGeneralReuslt		NVARCHAR(255),
//    @FinalDecision		NVARCHAR(255),
//    @FinalNote			NVARCHAR(255),
//    @FinalIsApprove	int
        public static void WO_SubmitFinalDecision(SectionFinalApprove secFinal) {

                var OrderId = secFinal.OrderId;
                var StateSetUserId = secFinal.StateSetUserId;
                var FinalGeneralReuslt = secFinal.FinalGeneralReuslt;
                var FinalDecision = secFinal.FinalDecision;
                var FinalNote = secFinal.FinalNote;
                var FinalIsApprove = secFinal.FinalIsApprove;


            //var sql = string.Format("WO_SubmitFinalDecision {0},{1},{2},{3},{4},{5}",
            //    OrderId,
            //    StateSetUserId,
            //    SHelper.GetPara(FinalGeneralReuslt),
            //     SHelper.GetPara(FinalDecision),
            //     SHelper.GetPara(FinalNote),
            //    SHelper.GetPara(FinalIsApprove)
            //     );
            ////DBHelper.ExecuteNonQuery(sql);
            //var rtn = DBHelper.ExecuteScalar(sql);
            if (CommonUtils.IsNoProcedure)
            {
                string rtnVal = WO_SubmitExpertService.Instance.SubmitFinalDecision(OrderId, StateSetUserId, FinalGeneralReuslt, FinalDecision, FinalNote, FinalIsApprove);
                if (rtnVal != "OK")
                {
                    Debug.WriteLine(rtnVal);
                    throw new Exception(rtnVal);
                }
            }
            else
            {
                var rtnVal = new ExecuteSql().ExecuteStoredProcedureScalar("WO_SubmitFinalDecision", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, OrderId.ToString()),
                new QueryParameter("StateSetUserId", DataType.Int, StateSetUserId.ToString()),
                new QueryParameter("FinalGeneralReuslt", DataType.String, FinalGeneralReuslt),
                new QueryParameter("FinalDecision", DataType.String, FinalDecision),
                new QueryParameter("FinalNote", DataType.String, FinalNote),
                new QueryParameter("FinalIsApprove", DataType.Int, FinalIsApprove.ToString())
                });
                var rtn = rtnVal == null ? null : rtnVal.ToString();
                if (rtn != "OK")
                {
                    Debug.WriteLine(rtn);
                    throw new Exception(rtn);
                }
                var sql = string.Format("select * from wo_testorder where orderId = {0}", OrderId.ToString());
                DataTable dt = new ExecuteSql().ExecuteSQL(sql);
                int stationId = dt.Rows[0].Field<int>("stationId");
                string equipItems = dt.Rows[0].Field<string>("EquipItems");
                // 删除设备工程态
                if (equipItems != null)
                    foreach (var e in equipItems)
                    {
                        new ExecuteSql().ExecuteStoredProcedureNoQuery("PBL_SaveEquipmentMaitain", new QueryParameter[] {
                         new QueryParameter("StationId", DataType.Int,stationId.ToString()),
                         new QueryParameter("EquipmentId", DataType.Int, e.ToString()),
                         new QueryParameter("EquipmentState", DataType.Int, "1"),
                         new QueryParameter("StartTime", DataType.DateTime,null),
                         new QueryParameter("EndTime", DataType.DateTime, null),
                         new QueryParameter("UserId", DataType.Int, StateSetUserId.ToString()),
                         new QueryParameter("Description", DataType.String, "割接设备完成")
                    });
                    }


            }
        }

        public static string WO_SubmitFinalDecision_SyncSiteWeb(int OrderId) {
            //var sql = string.Format("WO_SubmitFinalDecision_SyncSiteWeb {0}", OrderId);
            //var s = DBHelper.ExecuteScalar(sql);
            if(CommonUtils.IsNoProcedure)
            {
              return  WoPartSPService.Instance.SubmitFinalDecisionSyncSiteWeb(OrderId);
            }
            var rtn = new ExecuteSql().ExecuteStoredProcedureScalar("WO_SubmitFinalDecision_SyncSiteWeb", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, OrderId.ToString())
            });
            return rtn == null ? null : rtn.ToString() ;
        }
     }


}

