﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <procedures>
	  <procedure owner="" name="SP_Get_WRHouseCUCC_iUserId" grant="">
		  <parameters>
			  <parameter name="LogonId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
            SELECT  TBL_Account.UserId iUserId FROM TBL_Account 
	        WHERE TBL_Account.LogonId = :LogonId ;
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Get_WRHouseCUCC_X" grant="">
		  <parameters>
			  <parameter name="iUserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
           SELECT 'X' FROM TBL_UserRoleMap WHERE TBL_UserRoleMap.UserId = :iUserId AND TBL_UserRoleMap.RoleId = -1
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Get_WRHouseCUCC_FilterUser" grant="">
		  <parameters>
		  </parameters>
		  <body>
			  <![CDATA[
          SELECT DISTINCT UserId FROM WR_HouseManagementCUCC
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Get_WRHouseCUCC_FilterUser2" grant="">
		  <parameters>
			  <parameter name="iUserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
          SELECT DISTINCT UserId FROM TBL_UserRoleMap 
		  WHERE TBL_UserRoleMap.RoleId IN (SELECT RoleId FROM TBL_UserRoleMap 
		  WHERE TBL_UserRoleMap.UserId = :iUserId)
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Get_WRHouseCUCC_Result" grant="">
		  <parameters>
			  <parameter name="iUserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
          SELECT 1 RowNumber,
		  a.WRHouseId,a.WRStationId,
		  b.StructureId, e.StructureName,
		  b.StationCode,f.StationName,a.HouseCode, g.HouseName,
		  a.HouseStatus, c.ItemValue StatusName ,
		  a.UserId, a.SWUserName UserName,
		  a.ApplyTime,a.ApproveTime,
		  a.RejectCause,a.Remark,a.HouseRId
	      FROM WR_HouseManagementCUCC a
	      INNER JOIN WR_StationManagementCUCC b ON a.WRStationId = b.WRStationId
	      INNER JOIN WR_DataItem c ON a.HouseStatus = c.ItemId AND c.EntryId = 5
	      INNER JOIN TBL_StationStructure e ON b.StructureId = e.StructureId
	      INNER JOIN TBL_Station f ON b.SWStationId = f.StationId
	      INNER JOIN TBL_House g ON b.SWStationId = g.StationId AND a.SWHouseId = g.HouseId
	      INNER JOIN ($[FilterUser]) h ON a.UserId = h.UserId
	      WHERE a.HouseStatus = 3 $[WhereTime] $[WhereStructure] $[WhereSWHouseName]
		   $[WhereHouseCode] $[WhereSWStationName] $[WhereStationCode] $[WhereStatus]
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Get_WRHouseCUCC_Result2" grant="">
		  <parameters>
			  <parameter name="iUserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
          SELECT 1 RowNumber,
		  a.WRHouseId,a.WRStationId,
		  b.StructureId, e.StructureName,
		  b.StationCode,b.StationName,a.HouseCode, a.HouseName,
		  a.HouseStatus, c.ItemValue StatusName ,
		  a.UserId, a.SWUserName UserName,
		  a.ApplyTime,a.ApproveTime,
		  a.RejectCause,
		  a.Remark,
		  a.HouseRId
		FROM WR_HouseManagementCUCC a
		INNER JOIN WR_StationManagementCUCC b ON a.WRStationId = b.WRStationId
		INNER JOIN WR_DataItem c ON a.HouseStatus = c.ItemId AND c.EntryId = 5
		INNER JOIN TBL_StationStructure e ON b.StructureId = e.StructureId
		INNER JOIN ($[FilterUser]) h ON a.UserId = h.UserId
		WHERE a.HouseStatus != 3 $[WhereTime] $[WhereStructure] $[WhereHouseName]
		   $[WhereHouseCode] $[WhereStationName] $[WhereStationCode] $[WhereStatus]
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Get_WRHouseConditionCUCC_Result" grant="">
		  <parameters>
		  </parameters>
		  <body>
			  <![CDATA[
          SELECT 1 RowNumber,
		  a.WRHouseId,a.WRStationId,
		  b.StructureId, e.StructureName,
		  b.StationCode,f.StationName,a.HouseCode, g.HouseName,
		  a.HouseStatus, c.ItemValue StatusName ,
		  a.UserId, a.SWUserName UserName,
		  a.ApplyTime,a.ApproveTime,a.RejectCause,a.Remark,a.HouseRId
	      FROM WR_HouseManagementCUCC a
	      INNER JOIN WR_StationManagementCUCC b ON a.WRStationId = b.WRStationId
	      INNER JOIN WR_DataItem c ON a.HouseStatus = c.ItemId AND c.EntryId = 5
	      INNER JOIN TBL_StationStructure e ON b.StructureId = e.StructureId
	      INNER JOIN TBL_Station f ON b.SWStationId = f.StationId
	      INNER JOIN TBL_House g ON b.SWStationId = g.StationId AND a.SWHouseId = g.HouseId
	      WHERE a.HouseStatus = 3 $[WhereTime] $[WhereStructure] $[WhereSWHouseName]
		   $[WhereHouseCode] $[WhereSWStationName] $[WhereStationCode]
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Get_WRHouseConditionCUCC_Result2" grant="">
		  <parameters>
		  </parameters>
		  <body>
			  <![CDATA[
          SELECT 1 RowNumber,
		  a.WRHouseId,a.WRStationId,
		  b.StructureId, e.StructureName,
		  b.StationCode,b.StationName,a.HouseCode, a.HouseName,
		  a.HouseStatus, c.ItemValue StatusName ,a.UserId, a.SWUserName UserName,
		  a.ApplyTime,a.ApproveTime,a.RejectCause,a.Remark,a.HouseRId
		  FROM WR_HouseManagementCUCC a
		  INNER JOIN WR_StationManagementCUCC b ON a.WRStationId = b.WRStationId
		  INNER JOIN WR_DataItem c ON a.HouseStatus = c.ItemId AND c.EntryId = 5
		  INNER JOIN TBL_StationStructure e ON b.StructureId = e.StructureId
		  WHERE a.HouseStatus = 1 $[WhereTime] $[WhereStructure] $[WhereHouseName]
		   $[WhereHouseCode] $[WhereStationName] $[WhereStationCode]
         ]]>
		  </body>
	  </procedure>

	  <procedure owner="" name="SP_Add_WRFsuCUCC_FindByFsuCode" grant="">
		  <parameters>
			  <parameter name="FsuCode" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
          SELECT 'X' FROM WR_FsuManagementCUCC WHERE WR_FsuManagementCUCC.FsuCode = :FsuCode;
		  SELECT 'X' FROM TSL_MonitorUnitCUCC WHERE TSL_MonitorUnitCUCC.SUID = :FsuCode;
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Add_WRFsuCUCC_GetStationId" grant="">
		  <parameters>
			  <parameter name="WRHouseId" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
         SELECT a.WRStationId,b.SWStationId WRStationId, SWStationId FROM WR_HouseManagementCUCC a 
		 INNER JOIN WR_StationManagementCUCC b ON a.WRStationId = b.WRStationId
		 WHERE a.WRHouseId = :WRHouseId;
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Add_WRFsuCUCC_FindByStationIdAndFsuName" grant="">
		  <parameters>
			  <parameter name="FsuName" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="FsuCode" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
        SELECT 'X' FROM WR_FsuManagementCUCC a 
		INNER JOIN WR_HouseManagementCUCC b ON a.WRHouseId = b.WRHouseId 
		WHERE b.WRStationId = :WRStationId AND a.FsuCode != :FsuCode AND a.FsuName = :FsuName;
		
		
		SELECT 'X' FROM TSL_MonitorUnitCUCC 
        WHERE TSL_MonitorUnitCUCC.StationId = :SWStationId AND TSL_MonitorUnitCUCC.SUID != :FsuCode AND TSL_MonitorUnitCUCC.SUName = :FsuName;
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Add_WRFsuCUCC_FindIPAddressAndFsuName" grant="">
		  <parameters>
			  <parameter name="IPAddress" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="FsuCode" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
        SELECT 'X' FROM WR_FsuManagementCUCC WHERE WR_FsuManagementCUCC.IPAddress = :IPAddress
		
		
		SELECT 'X' FROM TSL_MonitorUnitCUCC 
        WHERE TSL_MonitorUnitCUCC.SUIP = :IPAddress AND TSL_MonitorUnitCUCC.SUID != :FsuCode
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Add_WRFsuCUCC_GetUser" grant="">
		  <parameters>
			  <parameter name="LogonId" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
        SELECT a.UserId UserId, a.UserName SWUserName FROM TBL_Account a 
	    WHERE a.LogonId = :LogonId;
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Add_WRFsuCUCC_Insert_FsuManagementCUCC" grant="">
		  <parameters>
			  <parameter name="WRHouseId" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="FsuCode" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="FsuName" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="IPAddress" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="ManufacturerId" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="Status" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWUserName" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="UserName" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="Password" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="FtpUserName" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="FtpPassword" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="Remark" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="ContractNo" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="ProjectName" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="FSURId" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
        INSERT INTO WR_FsuManagementCUCC
	    (WRHouseId,FsuCode,FsuName,IPAddress,ManufacturerId,FsuStatus,UserId, SWUserName,UserName,Password,FtpUserName,FtpPassword,ApplyTime,Remark,ContractNo,ProjectName,FsuRId)
	    VALUES
	    (:WRHouseId,:FsuCode,:FsuName,:IPAddress,:ManufacturerId,:Status,:UserId,:SWUserName,:UserName,:Password,:FtpUserName,:FtpPassword,now(),:Remark,:ContractNo,:ProjectName,:FSURId);
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Add_WRFsuCUCC_WRFsuId" grant="">
		  <parameters>
			  <parameter name="WRHouseId" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="FsuCode" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="FsuName" type="string" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
         SELECT WRFsuId WRFsuId FROM WR_FsuManagementCUCC WHERE WR_FsuManagementCUCC.WRHouseId = :WRHouseId 
         AND WR_FsuManagementCUCC.FsuCode = :FsuCode AND  WR_FsuManagementCUCC.FsuName = :FsuName;
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Add_WRFsuCUCC_StationName" grant="">
		  <parameters>
			  <parameter name="WRStationId" type="int" direction="Input" size="2055" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
        SELECT  IFNULL(StationName,'') StationName  FROM WR_StationManagementCUCC WHERE WR_StationManagementCUCC.WRStationId = :WRStationId
         ]]>
		  </body>
	  </procedure>
    <procedure owner="" name="SP_Add_WRHouseCUCC_getStationId" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT WR_StationManagementCUCC.SWStationId SWStationId  FROM WR_StationManagementCUCC
	      WHERE WR_StationManagementCUCC.WRStationId = :WRStationId; 
			]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Add_WRHouseCUCC_selectHouse" grant="">
      <parameters>
        <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="HouseName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT 'X' FROM TBL_House WHERE TBL_House.StationId = :SWStationId AND TBL_House.HouseName = :HouseName
			]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Add_WRHouseCUCC_selectHouseManagement" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="HouseName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT 'X' FROM WR_HouseManagementCUCC WHERE WR_HouseManagementCUCC.HouseName = :HouseName 
        AND WR_HouseManagementCUCC.WRStationId = :WRStationId
			]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Add_WRHouseCUCC_selUserInfo" grant="">
      <parameters>
        <parameter name="LogonId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT TBL_Account.UserId UserId, TBL_Account.UserName SWUserName 
	      FROM TBL_Account WHERE TBL_Account.LogonId = :LogonId ;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Add_WRHouseCUCC_insertHouse" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="HouseCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="HouseName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWUserName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Remark" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="HouseRId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        INSERT INTO WR_HouseManagementCUCC
	      (WRStationId,HouseCode,HouseName,HouseStatus,UserId, SWUserName,ApplyTime,Remark,HouseRId) VALUES
	      (:WRStationId, :HouseCode, :HouseName, 1, :UserId, :SWUserName, NOW(), :Remark, :HouseRId);
			]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Add_WRHouseCUCC_getWRHouseId" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="HouseName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT WR_HouseManagementCUCC.WRHouseId WRHouseId FROM WR_HouseManagementCUCC 
        WHERE WR_HouseManagementCUCC.WRStationId = :WRStationId 
        AND WR_HouseManagementCUCC.HouseName = :HouseName;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_Add_WRHouseCUCC_getStationName" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
        SELECT IFNULL(StationName,'') StationName FROM WR_StationManagementCUCC 
        WHERE WR_StationManagementCUCC.WRStationId = :WRStationId;
			]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_ApproveWRHouseCUCC_Select_StationManagementCUCC" grant="">
			<parameters>
				<parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
            SELECT   a.StationStatus StationStatus,a.SWStationId SWStationId,b.HouseStatus HouseStatus
	        FROM WR_StationManagementCUCC a  
	        INNER JOIN WR_HouseManagementCUCC b ON a.WRStationId = b.WRStationId
	        WHERE b.WRHouseId = :WRHouseId;
			]]>
			</body>
	</procedure>
		<procedure owner="" name="SP_ApproveWRHouseCUCC_maxHouseId" grant="">
			<parameters>
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
            SELECT max(HouseId) maxHouseId
	        FROM TBL_House WHERE TBL_House.StationId = :SWStationId;
			]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_ApproveWRHouseCUCC_Insert_House" grant="">
			<parameters>
				<parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="currHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="currentTime" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
            INSERT INTO TBL_House(HouseId,StationId,HouseName,Description,LastUpdateDate) 
	        SELECT :currHouseId , :SWStationId , HouseName, Remark , :currentTime
	        FROM WR_HouseManagementCUCC
	        WHERE WR_HouseManagementCUCC.WRHouseId = = :WRHouseId;
			]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_ApproveWRHouseCUCC_Update_HouseManagementCUCC" grant="">
			<parameters>
				<parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="currHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="currentTime" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
            UPDATE WR_HouseManagementCUCC
   	        SET WR_HouseManagementCUCC.HouseStatus = 3
           	,WR_HouseManagementCUCC.ApproveTime =:currentTime
   	        ,WR_HouseManagementCUCC.SWHouseId = :currHouseId,WR_HouseManagementCUCC.RejectCaus = ''
   	        WHERE WR_HouseManagementCUCC.WRHouseId = :WRHouseId;
			]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_ApproveWRHouseCUCC_Insert_ConfigChangeMicroLog" grant="">
			<parameters>
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="currHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="currentTime" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
            INSERT INTO TBL_ConfigChangeMicroLog(ObjectId,ConfigId,EditType,UpdateTime)
	        SELECT concat(TO_CHAR(:SWStationId),'.',TO_CHAR(:currHouseId))  ObjectId, 5 ConfigId, 1 EditType, :currentTime UpdateTime;
			]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_ApproveWRHouseCUCC_Update_ConfigChangeMacroLog" grant="">
			<parameters>
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="currHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="currentTime" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
            UPDATE TBL_ConfigChangeMacroLog
	       SET TBL_ConfigChangeMacroLog.EditType = 2, TBL_ConfigChangeMacroLog.UpdateTime = :currentTime
	       WHERE TBL_ConfigChangeMacroLog.ObjectId = TO_CHAR(:SWStationId) AND TBL_ConfigChangeMacroLog.ConfigId = 1;
			]]>
			</body>
		</procedure>
		  <procedure owner="" name="SP_ApproveWRHouseCUCC_wr_syncinfo" grant="">
			  <parameters>
				  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
				  <parameter name="currHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  </parameters>
			  <replaces>
			  </replaces>
			  <body>
				  <![CDATA[
				  INSERT INTO wr_syncinfo(StationId, HouseId, SyncType) VALUES (:SWStationId, :currHouseId, 2);
				]]>
			  </body>
		  </procedure>




	  
	  

	  <procedure owner="" name="SP_Upd_WRHouseCUCC_GetFieldValue1" grant="">
		  <parameters>
			  <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
					SELECT a.HouseStatus AS HStatus, a.SWHouseId, b.SWStationId 
					FROM WR_HouseManagementCUCC a 
					INNER JOIN WR_StationManagementCUCC b ON a.WRStationId = b.WRStationId
					WHERE a.WRHouseId = :WRHouseId;	
			]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Upd_WRHouseCUCC_GetFieldValue2" grant="">
		  <parameters>
			  <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
					SELECT HouseCode AS OriHouseCode  FROM WR_HouseManagementCUCC WHERE WR_HouseManagementCUCC.WRHouseId = :WRHouseId limit 1;
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Upd_WRHouseCUCC_JudgeExist1" grant="">
		  <parameters>
			  <parameter name="HouseName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
					SELECT 'X' FROM WR_HouseManagementCUCC WHERE WR_HouseManagementCUCC.HouseName = :HouseName
					AND WR_HouseManagementCUCC.WRStationId = :WRStationId
					AND WR_HouseManagementCUCC.WRHouseId != :WRHouseId limit 1;
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Upd_WRHouseCUCC_UpdateTBLHouse" grant="">
		  <parameters>
			  <parameter name="HouseName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="Remark" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
				UPDATE TBL_House
				SET TBL_House.HouseName = :HouseName, TBL_House.Description = :Remark
				WHERE TBL_House.StationId = :SWStationId AND TBL_House.HouseId = :SWHouseId;
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Upd_WRHouseCUCC_UpdateTBLConfigChangeMicroLog" grant="">
		  <parameters>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
				UPDATE TBL_ConfigChangeMicroLog
				SET TBL_ConfigChangeMicroLog.EditType = 2, TBL_ConfigChangeMicroLog.UpdateTime = now()
				WHERE TBL_ConfigChangeMicroLog.ObjectId = TO_CHAR(:SWStationId) AND TBL_ConfigChangeMicroLog.ConfigId = 5; 	
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Upd_WRHouseCUCC_UpdateTBLConfigChangeMacroLog" grant="">
		  <parameters>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
				UPDATE TBL_ConfigChangeMacroLog
				SET TBL_ConfigChangeMacroLog.EditType = 2, TBL_ConfigChangeMacroLog.UpdateTime = now()
				WHERE TBL_ConfigChangeMacroLog.ObjectId = TO_CHAR(:SWStationId) AND TBL_ConfigChangeMacroLog.ConfigId = 1; 	
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Upd_WRHouseCUCC_GetFieldValue3" grant="">
		  <parameters>
			  <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
					SELECT concat('HouseCode:' , TO_CHAR(HouseCode) ,
	    					'-HouseName:' , TO_CHAR(HouseName) ,
	    					'-Remark:' , TO_CHAR(Remark) ,
	    					'-HouseRId:' , ifnull(TO_CHAR(HouseRId) ,'')) 
	    					FROM WR_HouseManagementCUCC WHERE WR_HouseManagementCUCC.WRHouseId = :WRHouseId;
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Upd_WRHouseCUCC_GetFieldValue4" grant="">
		  <parameters>
			  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
					SELECT  IFNULL(StationName,'')  FROM WR_StationManagementCUCC WHERE WR_StationManagementCUCC.WRStationId = :WRStationId;
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_Upd_WRHouseCUCC_UpdateWRHouseManagementCUCC" grant="">
		  <parameters>
			  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="HouseCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="HouseName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="Remark" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="HStatus" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="HouseRId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
					UPDATE WR_HouseManagementCUCC
					SET WR_HouseManagementCUCC.WRStationId = :WRStationId
						, WR_HouseManagementCUCC.HouseCode = :HouseCode 
						,WR_HouseManagementCUCC.HouseName = :HouseName
						,WR_HouseManagementCUCC.Remark = :Remark 
						,WR_HouseManagementCUCC.HouseStatus = CASE WHEN :HStatus = 2 THEN  1 
							ELSE HouseStatus END 
						,WR_HouseManagementCUCC.RejectCause = CASE WHEN :HStatus = 2 THEN  ''
							ELSE RejectCause END
						,WR_HouseManagementCUCC.HouseRId = :HouseRId
					WHERE WR_HouseManagementCUCC.WRHouseId = :WRHouseId;
					]]>
		  </body>
	  </procedure>
    <procedure owner="" name="SP_RejectWRHouseCUCC_HouseStatus" grant="">
      <parameters>
        <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
					SELECT WR_HouseManagementCUCC.HouseStatus HouseStatus FROM WR_HouseManagementCUCC
	        WHERE WR_HouseManagementCUCC.WRHouseId = :WRHouseId;
				]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_RejectWRHouseCUCC_UpdateHouse" grant="">
      <parameters>
        <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="RejectCause" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
					UPDATE WR_HouseManagementCUCC
	        SET WR_HouseManagementCUCC.HouseStatus = 2
		      , WR_HouseManagementCUCC.RejectCause = :RejectCause
	        WHERE WR_HouseManagementCUCC.WRHouseId =  :WRHouseId; 
				]]>
      </body>
    </procedure>
    <procedure owner="" name="SP_RejectWRHouseCUCC_UpdateFsu" grant="">
      <parameters>
        <parameter name="WRHouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="RejectCause" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
					UPDATE WR_FsuManagementCUCC
	        SET WR_FsuManagementCUCC.FsuStatus = 2
		      ,WR_FsuManagementCUCC.RejectCause = concat('级联退回:' , :RejectCause)
	        WHERE WR_FsuManagementCUCC.WRHouseId = :WRHouseId; 
				]]>
      </body>
    </procedure>
  </procedures>
</root>
