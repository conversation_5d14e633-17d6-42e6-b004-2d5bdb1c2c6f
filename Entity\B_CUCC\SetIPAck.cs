﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    /// <summary>
    /// 设置为SU分配的采集服务器IP响应报文
    /// </summary>
    public sealed class SetIPAck: BMessage
    {

        public EnumResult Result { get; set; }

        public SetIPAck(string suId, string surId, EnumResult result): base()
        {
            MessageType = (int)BMessageType.SET_IP_ACK;
            SUId = suId;
            SURId = surId;

            Result = result;
        }

        public SetIPAck(): base()
        {
            MessageType = (int)BMessageType.SET_IP_ACK;
        }

        public static SetIPAck Deserialize(XmlDocument xmlDoc)
        {
            SetIPAck setIPAck = null;
            try
            {
                string code = xmlDoc.SelectSingleNode("/Response/PK_Type/Code").InnerText.Trim();
                string suId = xmlDoc.SelectSingleNode("/Response/Info/SUId").InnerText.Trim();
                string surId = xmlDoc.SelectSingleNode("/Response/Info/SURId").InnerText.Trim();
                string resultString = xmlDoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim();
                setIPAck = new SetIPAck(suId, surId, string.IsNullOrEmpty(resultString) ? EnumResult.FAILURE : (EnumResult)int.Parse(resultString));
                entityLogger.DebugFormat("SetIPAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                setIPAck.StringXML = xmlDoc.InnerXml;
                return setIPAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetIPAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetIPAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                setIPAck = new SetIPAck();
                setIPAck.ErrorMsg = ex.Message;
                setIPAck.StringXML = xmlDoc.InnerXml;
                return setIPAck;
            }
        }

        public override string ToString()
        {
            return string.Format("{0},{1},({2} and {3}):{4}", MessageId, (BMessageType)MessageType, SUId, SURId, Result.ToString());
        }

    }
}
