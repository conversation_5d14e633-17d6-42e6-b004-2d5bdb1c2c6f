﻿
using Common.Logging.Pro;

using Delivery.Common;

using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Xml.Linq;

namespace BDSTool.DBUtility.Common
{
    public class DbConfigPara
    {
        public string ConnectionString;
        public string ProviderName;
        public int DataBaseTimeout = 60;        //数据库超时时间，单位：秒
    }
    public class DbConfig
    {
        private readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public string ConnectionString;
        public string ProviderName;
        public  int DataBaseTimeout =60;        //数据库超时时间，单位：秒

        public DbConfig(){
            try {
                logger.Info("");
                logger.Info("******************************************************************************************");
                logger.Info("version 20180528 增加对GAO告警类型字典的处理");
                logger.Info("******************************************************************************************");
                logger.Info("");

                var conn = new ConnectionStringSettings("Default", ConfigHelper.GetSection("Database:Default:ConnectionString").Value, ConfigHelper.GetSection("Database:Default:ProviderName").Value);
                //var conn = ConfigurationManager.ConnectionStrings["Default"];
                if (conn == null) {
                    //throw new Exception("配置文件connectionStrings中无数据库连接配置");
                    logger.Info("配置文件connectionStrings中无数据库连接配置");
                    return;
                }
                    
                //ConnectionSettings.ConnectionString: Data Source='10.169.42.171';Database='test'; UID='sa'; PWD=''
                var decryConn = CryptoHelper.DecryptConnectionString(conn.ConnectionString);            //设定Connection对像的连接字符串       

                ConnectionString = decryConn;
                ProviderName = string.IsNullOrEmpty(conn.ProviderName) ? "mysql" : conn.ProviderName;
                //string key = GetAppSetting("DataBaseTimeout");
                //string key = ConfigurationManager.AppSettings.Get("DataBaseTimeout");
                string key = null;
                if (string.IsNullOrEmpty(key)) {
                    //默认60秒
                    DataBaseTimeout = 60;
                }
                else if (!int.TryParse(key, out DataBaseTimeout)) {
                    //默认60秒
                    DataBaseTimeout = 60;
                }
                else if (DataBaseTimeout < 60) {
                    //默认60秒
                    DataBaseTimeout = 60;
                }
                else if (DataBaseTimeout > 300) {
                    //默认最大300秒
                    DataBaseTimeout = 300;
                }

                logger.InfoFormat("DbConfig(); ConnectionString={0}; DataBaseTimeout={1}; ProviderName={2}", 
                    ConnectionString, DataBaseTimeout,ProviderName);
            }
            catch(Exception e){
                logger.ErrorFormat("DbConfig(); ex={0}", e.Message);
                logger.Error(e.StackTrace);
            }
        }
        
        //useless, same as the default config
        private static string GetAppSetting(string key) {
            var filePath = AppDomain.CurrentDomain.BaseDirectory + "\\KoloDataServer.exe.config";
            ExeConfigurationFileMap filemap = new ExeConfigurationFileMap();
            filemap.ExeConfigFilename = filePath;
            var cfg = ConfigurationManager.OpenMappedExeConfiguration(filemap, ConfigurationUserLevel.None);

            //var filePath = AppDomain.CurrentDomain.BaseDirectory + "\\KoloDataServer.exe";
            //Configuration cfg = ConfigurationManager.OpenExeConfiguration(path);

            if (!cfg.AppSettings.Settings.AllKeys.Contains(key)) return null;

            return cfg.AppSettings.Settings[key].Value;
        }

   
    }
}
