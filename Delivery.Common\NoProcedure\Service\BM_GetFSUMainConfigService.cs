﻿using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace Delivery.Common.NoProcedure.Service
{
    public class BM_GetFSUMainConfigService
    {
        private static BM_GetFSUMainConfigService _instance = null;
        public static BM_GetFSUMainConfigService Instance
        {
            get
            {
                if (_instance == null) _instance = new BM_GetFSUMainConfigService();
                return _instance;
            }
        }

        public DataTable BM_GetFSUMainConfig(string FSUID)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                try
                {
                    ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("FSUID", FSUID);
                    return execHelper.ExecDataTable("BM_GetFSUMainConfig", realParams);
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("BM_GetFSUMainConfig:{0}", ex));
                    return new DataTable();
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }   
        }

        public DataSet BM_GetTSignalList(int StationId, int EquipmentId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                try
                {
                    DataSet dataSet = new DataSet();
                    ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("StationId", StationId);
                    realParams.Add("EquipmentId", EquipmentId);
                    object EquipmentTemplateId = DBNull.Value;
                    DataTable dt = execHelper.ExecDataTable("BM_GetTSignalList_getTemplateId", realParams);
                    if(dt != null && dt.Rows.Count > 0)
                    {
                        EquipmentTemplateId = CommonUtils.GetNullableValue(dt.Rows[0].Field<string>("EquipmentTemplateId"));
                    }
                    realParams.Add("EquipmentTemplateId", EquipmentTemplateId);
                    DataTable dt1 = execHelper.ExecDataTable("BM_GetTSignalList_insertTSignal", realParams);
                    dt1.DefaultView.Sort = "SiteWebType DESC";
                    dt1 = dt1.DefaultView.ToTable();
                    dataSet.Tables.Add(dt1);
                    return dataSet;
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("BM_GetTSignalList:{0}", ex));
                    return new DataSet();
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public void BM_CreateConfigOfSample(string FSUID, int MonitorUnitId, int PortId, int SamplerUnitId, int SamplerId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, true);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    string PortConfigId = string.Concat(MonitorUnitId, '.', PortId);
                    string SamplerConfigId = string.Concat(MonitorUnitId, '.', PortId, '.', SamplerUnitId);
                    string PortDescription = string.Concat("FSU_", FSUID);
                    string SamplerUnitDescription = string.Concat("FSU_", FSUID);
                    realParams.Add("PortId", PortId);
                    realParams.Add("MonitorUnitId", MonitorUnitId);
                    realParams.Add("PortDescription", PortDescription);
                    realParams.Add("SamplerUnitId", SamplerUnitId);
                    realParams.Add("SamplerId", SamplerId);
                    realParams.Add("SamplerUnitDescription", SamplerUnitDescription);
                    execHelper.ExecuteNonQuery("BM_CreateConfigOfSample_TslPort", realParams);
                    PblConfigChangeLogService.Instance.DoExecute(PortConfigId, 16, 1, execHelper);
                    PblConfigChangeLogService.Instance.DoExecute(SamplerConfigId, 17, 1, execHelper);
                    execHelper.ExecuteNonQuery("BM_CreateConfigOfSample_TslSamplerUnit", realParams);
                    execHelper.Commit();
                }
                catch (Exception ex)
                {
                    execHelper.Rollback();
                    Logger.Log(string.Format("BM_CreateConfigOfSample:{0}", ex));
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
    }
}
