﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class SendDiData : BMessage
    {
        public string ReportTime { get; set; }

        public List<Device> LDevice { get; set; }

        //public string DeviceId { get; set; }
        //public string DeviceRid { get; set; }
        //public string ID { get; set; }
        //public SendDiData(string suids, string surids, string reportTimes, string deviceId, string deviceRid, string id): base()
        public SendDiData(string suid, string surid, string reportTime, List<Device> deviceList)
            : base()
        {
            MessageType = (int)BMessageType.SEND_DIDATA;
            SUId = suid;
            SURId = surid;
            ReportTime = reportTime;
            LDevice = deviceList;
            //DeviceId = deviceId;
            //DeviceRid = deviceRid;
            //ID = id;
        }
        public SendDiData()
            : base()
        {
            MessageType = (int)BMessageType.SEND_DIDATA;
        }
        public static SendDiData Deserialize(XmlDocument xmlDoc)
        {
            SendDiData sendDiData = null;
            string errMsg = string.Empty;
            string suid = string.Empty;
            string surid = string.Empty;

            try
            {
                //string suid = xmlDoc.SelectSingleNode("/Request/Info/SUId").InnerText.Trim();
                //string surid = xmlDoc.SelectSingleNode("/Request/Info/SURId").InnerText.Trim();
                //string reportTimes = xmlDoc.SelectSingleNode("/Request/Info/ReportTime").InnerText.Trim();
                //string deviceId = xmlDoc.SelectSingleNode("/Request/Info/DeviceID").InnerText.Trim();
                //string deviceRid = xmlDoc.SelectSingleNode("/Request/Info/DeviceRId").InnerText.Trim();
                //string id = xmlDoc.SelectSingleNode("/Request/Info/Id").InnerText.Trim();

                suid = xmlDoc.SelectSingleNode("/Request/Info/SUId").InnerText.Trim();
                surid = xmlDoc.SelectSingleNode("/Request/Info/SURId").InnerText.Trim();
                string reportTime = xmlDoc.SelectSingleNode("/Request/Info/ReportTime").InnerText.Trim();
                XmlNodeList nodes = xmlDoc.SelectNodes("/Request/Info/Values/DeviceList/Device");
                List<Device> list = new List<Device>();
                foreach (XmlElement node in nodes)
                {
                    string id = node.GetAttribute("Id");
                    string rId = node.GetAttribute("RId");
                    List<Signal> lst = new List<Signal>();
                    XmlNodeList lists = node.SelectNodes("Signal");
                    if (lists.Count > 0)
                    {
                        foreach (XmlElement n in lists)
                        {
                            Signal sdv = new Signal();
                            string strSignalId = n.GetAttribute("Id").Trim();
                            if (string.IsNullOrEmpty(strSignalId))
                            {
                                errMsg = "SEND_DIDATA Signal ID is nullOrEmpty";
                                break;
                            }
                            else
                            {
                                sdv.Id = strSignalId;
                            }
                            //sdv.RecordTime = n.GetAttribute("RecordTime");
                            lst.Add(sdv);
                        }
                    }
                    else
                    {
                        entityLogger.InfoFormat("SendDiData.Deserialize(),Device has no signals ,Device Id={0}, RId={1}", id, rId);
                    }
                    Device device = new Device(id, rId, lst);
                    list.Add(device);
                }
                if (errMsg != string.Empty)
                {
                    entityLogger.ErrorFormat("SendDiData.Deserialize():{0}", errMsg);
                    sendDiData = GetErrorEntity(suid, surid, errMsg);
                }
                else
                {
                    sendDiData = new SendDiData(suid, surid, reportTime, list);
                }
                entityLogger.DebugFormat("SendDiData.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                sendDiData.StringXML = xmlDoc.InnerXml;
                return sendDiData;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SendDiData.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SendDiData.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                sendDiData = GetErrorEntity(suid, surid, ex.Message);
                sendDiData.StringXML = xmlDoc.InnerXml;
                return sendDiData;
            }
        }

        /// <summary>
        /// 反序列化异常时，构造带错误信息的实体
        /// </summary>
        /// <param name="errorMsg">解析错误信息</param>
        /// <returns></returns>
        private static SendDiData GetErrorEntity(string suId, string surId, string errorMsg)
        {
            SendDiData errorEntity = new SendDiData();
            SendDiDataAck ackEntity = new SendDiDataAck(suId, surId, EnumResult.FAILURE);
            ackEntity.ErrorMsg = errorMsg;
            errorEntity.ErrorMsg = ackEntity.Serialize();
            return errorEntity;
        }

        public override string ToString()
        {
            string strDeviceList = "[";
            foreach(Device device in LDevice)
            {
                if (strDeviceList == "[")
                {
                    strDeviceList = strDeviceList + device.ToString();
                }
                else
                {
                    strDeviceList = strDeviceList +" , "+ device.ToString();
                }
            }
            strDeviceList = strDeviceList + "]";
            return string.Format("{0},{1},{2},{3},{4},{5},{6}", MessageId, (BMessageType)MessageType, MessageType, SUId, SURId, ReportTime, strDeviceList);
        }
    }
}
