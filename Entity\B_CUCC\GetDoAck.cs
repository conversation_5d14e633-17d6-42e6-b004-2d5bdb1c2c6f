﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class GetDoAck : BMessage
    {
        public List<Device> LDevice { get; set; }

        #region SiteWeb配置

        public int MyStationId { get; set; }

        #endregion
        public GetDoAck() : base()
        {
            MessageType = (int)BMessageType.GET_DO_ACK;

        }
        public GetDoAck(string suids, string surids, List<Device> ldevice) : base()
        {
            MessageType = (int)BMessageType.GET_DO_ACK;
            SUId = suids;
            SURId = surids;
            LDevice = ldevice;
        }
        public static GetDoAck Deserialize(XmlDocument xmlDoc)
        {
            GetDoAck getTelecontrolDOAck = null;
            try
            {
                string suid = xmlDoc.SelectSingleNode("/Response/Info/SUId").InnerText.Trim();
                string surid = xmlDoc.SelectSingleNode("/Response/Info/SURId").InnerText.Trim();
                XmlNodeList nodes = xmlDoc.SelectNodes("/Response/Info/Values/DeviceList/Device");
                List<Device> list = new List<Device>();
                foreach (XmlElement node in nodes)
                {
                    string id = node.GetAttribute("Id");
                    string rId = node.GetAttribute("RId");
                    List<Signal> signalList = new List<Signal>();
                    //XmlNodeList lists = node.SelectNodes("Id");
                    XmlNodeList lists = node.SelectNodes("Signal");
                    if (lists.Count > 0)
                    {
                        foreach (XmlElement n in lists)
                        {
                            Signal signal = new Signal();
                            //signal.Id = n.InnerText.Trim();
                            signal.Id = n.GetAttribute("Id").Trim();
                            signalList.Add(signal);
                        }
                    }
                    else
                    {
                        entityLogger.InfoFormat("GetDoAck.Deserialize(),Device has no signals ,Device Id={0}, RId={1}", id, rId);
                    }
                    Device device = new Device(id, rId, signalList);
                    list.Add(device);
                }
                getTelecontrolDOAck = new GetDoAck(suid, surid, list);
                entityLogger.DebugFormat("GetDoAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                getTelecontrolDOAck.StringXML = xmlDoc.InnerXml;
                return getTelecontrolDOAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetDoAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetDoAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                getTelecontrolDOAck = new GetDoAck();
                getTelecontrolDOAck.ErrorMsg = ex.Message;
                getTelecontrolDOAck.StringXML = xmlDoc.InnerXml;
                return getTelecontrolDOAck;
            }
        }
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            if (LDevice.Count > 0)
            {
                foreach (Device de in LDevice)
                {
                    sb.AppendFormat("{0},{1},{2},{3},{4},{5}\n", MessageId, (BMessageType)MessageType,MessageType, SUId, SURId, de.StringToString());
                }
            }
            else
            {
                sb.AppendFormat("{0},{1},{2},{3},Device is null", MessageId, (BMessageType)MessageType, SUId, SURId);
            }
            return sb.ToString();
        }
    }
}
