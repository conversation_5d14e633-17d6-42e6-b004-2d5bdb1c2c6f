.validatebox-tip {
  position: absolute;
  width: 200px;
  height: auto;
  display: none;
  z-index: 9900000;
}
.validatebox-tip-content {
  display: inline-block;
  position: absolute;
  top: 0px;
  left: 8px;
  width: 150px;
  border-width: 1px;
  border-style: solid;
  padding: 3px 5px;
  z-index: 9900001;
  font-size: 12px;
}
.validatebox-tip-pointer {
  display: inline-block;
  width: 8px;
  height: 16px;
  position: absolute;
  left: 1px;
  top: 0px;
  z-index: 9900002;
}
.validatebox-tip-left .validatebox-tip-content {
  left: auto;
  right: 8px;
}
.validatebox-tip-left .validatebox-tip-pointer {
  background-position: -20px center;
  left: auto;
  right: 1px;
}
.validatebox-invalid {
  background-image: url('images/validatebox_warning.png');
  background-repeat: no-repeat;
  background-position: right center;
  border-color: #ffa8a8;
  background-color: #fff3f3;
  color: #000;
}
.validatebox-tip-pointer {
  background: url('images/validatebox_arrows.png') no-repeat -4px center;
}
.validatebox-tip-content {
  border-color: #CC9933;
  background-color: #FFFFCC;
  color: #000;
}
