﻿using BDSTool.BLL.S2;
using BDSTool.DBUtility;
using BDSTool.DBUtility.Common;
using BDSTool.Entity.B;
using BDSTool.Entity.DRHelper;
using BDSTool.Utility;
using Common.Logging.Pro;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Xml;
using System.Xml.Linq;

namespace BDSTool.BLL.S2
{
    public partial class EquipmentBiz
    {
        public static bool OnUpdateStoreRule(int StationId, int EquipmentId, int signalId,
                 float? absoluteVal, float? relativeVal, long? storageInterval) {
            string rtn = null;
            if (CommonUtils.IsNoProcedure)
            {
                rtn = Public_ExecuteSqlService.Instance.NoStore_TBLEquipment_GetByStationIdAndEquipmentId(StationId, EquipmentId);
            }
            else
            {
                var sqlGetEId = string.Format("SELECT EquipmentTemplateId FROM TBL_Equipment WHERE StationId={0} AND EquipmentId={1}",
                StationId, EquipmentId);
                rtn = DBHelper.ExecuteScalar(sqlGetEId);
            }
            if (string.IsNullOrEmpty(rtn))
                return false;

            var EquipmentTemplateId = int.Parse(rtn.ToString());

            //-------------------------------------------------------------------------------------------------
            //20170618 文档中注明，存储时间间隔的单位为分钟
            if (storageInterval.HasValue)
                storageInterval = storageInterval * 60;

            if (CommonUtils.IsNoProcedure)
            {
                Public_ExecuteSqlService.Instance.NoStore_UpdateTBLSignal(SHelper.GetPara(absoluteVal), SHelper.GetPara(relativeVal), SHelper.GetPara(storageInterval),
                    EquipmentTemplateId, signalId);
            }
            else 
            {
                var sqlUpdate = string.Format("UPDATE TBL_Signal SET AbsValueThreshold={0}, PercentThreshold={1}, StoreInterval={2} WHERE EquipmentTemplateId={3} AND SignalId={4}",
                    SHelper.GetPara(absoluteVal), SHelper.GetPara(relativeVal), SHelper.GetPara(storageInterval),
                    EquipmentTemplateId, signalId
                );
                DBHelper.ExecuteNonQuery(sqlUpdate);
            }
            return true;
        }
    }
}
