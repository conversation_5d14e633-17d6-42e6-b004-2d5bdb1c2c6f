﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 上报告警信息
    /// </summary>
    public sealed class SendAlarm : BMessage
    {
        /// <summary>
        /// 告警信息
        /// </summary>
        private TAlarm _value;

        [JsonProperty("Values")]
        public TAlarm Value
        {
            get
            {
                return _value;
            }
            set
            {
                _value = value;
                if (value != null)
                {
                    Values = Values ?? new List<TAlarm>();
                    Values.Add(value);
                }
            }
        }

        [JsonIgnore]
        public List<TAlarm> Values { get; set; }

        public SendAlarm() : base()
        {
            MessageType = (int)BMessageType.SEND_ALARM;
        }

        public SendAlarm(string fsuId, TAlarm value):base()
        {
            MessageType = (int)BMessageType.SEND_ALARM;

            FSUID = fsuId;
            Value = value;
        }

        public SendAlarm(string fsuId, List<TAlarm> value)
            : base()
        {
            MessageType = (int)BMessageType.SEND_ALARM;

            FSUID = fsuId;
            Value = value.Count > 0 ? value[0] : null;
        }

        public static SendAlarm Deserialize(string json)
        {
            SendAlarm info = null;
            try
            {
                info = JsonConvert.DeserializeObject<SendAlarm>(json);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SendAlarm.Deserialize:{0}" , ex.Message);
                entityLogger.ErrorFormat("SendAlarm.Deserialize:{0}" , ex.StackTrace);
                info = new SendAlarm();
                info.ErrorMsg = ex.Message;
                info.StringXML = json;
            }
            return info;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0},{1},{2}:", MessageId, MessageType, FSUID);
            sb.AppendLine();
            if (Values != null)
            {
                foreach (TAlarm alarm in Values)
                {
                    sb.AppendFormat("{0}\r\n", alarm.ToString());
                }
            }
            return sb.ToString();
        }
    }
}