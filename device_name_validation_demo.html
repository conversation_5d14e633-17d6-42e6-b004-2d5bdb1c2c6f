<!DOCTYPE html>
<html>
<head>
    <title>设备名称校验功能演示</title>
    <link rel="stylesheet" type="text/css" href="https://www.jeasyui.com/easyui/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css" href="https://www.jeasyui.com/easyui/themes/icon.css">
    <script type="text/javascript" src="https://code.jquery.com/jquery-1.8.0.min.js"></script>
    <script type="text/javascript" src="https://www.jeasyui.com/easyui/jquery.easyui.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .demo-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .vendor-list { background-color: #f9f9f9; padding: 10px; margin: 10px 0; }
        .example { background-color: #e8f5e8; padding: 10px; margin: 10px 0; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>设备管理 - 设备名称校验功能演示</h1>
    
    <div class="demo-section">
        <h2>功能说明</h2>
        <p>本功能为设备管理页面的新增设备功能添加了设备名称格式校验，要求如下：</p>
        <ol>
            <li><strong>格式要求：</strong>楼层_系统名称及编号_设备子类及编号_设备厂商_型号_实配容量_投产年限</li>
            <li><strong>下划线校验：</strong>必须包含6个下划线"_"</li>
            <li><strong>厂商校验：</strong>设备厂商必须在数据库预存的厂商列表中</li>
        </ol>
    </div>

    <div class="demo-section vendor-list">
        <h3>预存的设备厂商列表：</h3>
        <ul id="vendorList">
            <li>动力源</li>
            <li>华为</li>
            <li>中兴</li>
            <li>艾默生</li>
            <li>江苏亚奥</li>
        </ul>
    </div>

    <div class="demo-section example">
        <h3>正确格式示例：</h3>
        <p><code>3F_1#开关电源_1#整流屏_动力源_DKD42_1000A_2011</code></p>
        <p>解析：</p>
        <ul>
            <li>楼层：3F</li>
            <li>系统名称及编号：1#开关电源</li>
            <li>设备子类及编号：1#整流屏</li>
            <li>设备厂商：动力源</li>
            <li>型号：DKD42</li>
            <li>实配容量：1000A</li>
            <li>投产年限：2011</li>
        </ul>
    </div>

    <div class="demo-section">
        <h3>实时校验演示：</h3>
        <table>
            <tr>
                <td style="text-align: left; white-space: nowrap; padding-right: 10px;">设备名称：</td>
                <td>
                    <input id="deviceNameInput" class="easyui-textbox" style="width: 400px;" 
                           data-options="prompt:'请填写设备名称格式：楼层_系统名称及编号_设备子类及编号_设备厂商_型号_实配容量_投产年限', validType:['deviceNameFormat'],required:'true'" />
                </td>
            </tr>
            <tr>
                <td colspan="2" style="padding-top: 10px;">
                    <button onclick="validateInput()">手动校验</button>
                    <button onclick="fillExample()">填入正确示例</button>
                    <button onclick="fillWrongExample()">填入错误示例</button>
                </td>
            </tr>
        </table>
        <div id="validationResult" style="margin-top: 10px;"></div>
    </div>

    <script>
        // 模拟设备厂商列表
        var deviceVendorList = [
            { ItemValue: '动力源', ItemId: '1' },
            { ItemValue: '华为', ItemId: '2' },
            { ItemValue: '中兴', ItemId: '3' },
            { ItemValue: '艾默生', ItemId: '4' },
            { ItemValue: '江苏亚奥', ItemId: '5' }
        ];

        // 扩展EasyUI验证规则
        $.extend($.fn.validatebox.defaults.rules, {
            deviceNameFormat: {
                validator: function (value) {
                    if (!value) return true; // 空值由required规则处理
                    
                    // 1. 检查下划线数量（应该有6个下划线）
                    var underscoreCount = (value.match(/_/g) || []).length;
                    if (underscoreCount !== 6) {
                        return false;
                    }
                    
                    // 2. 按下划线分割，应该有7个部分
                    var parts = value.split('_');
                    if (parts.length !== 7) {
                        return false;
                    }
                    
                    // 3. 检查每个部分是否为空
                    for (var i = 0; i < parts.length; i++) {
                        if (!parts[i] || parts[i].trim() === '') {
                            return false;
                        }
                    }
                    
                    // 4. 检查设备厂商（第4个部分，索引为3）是否在预存列表中
                    var vendor = parts[3];
                    var vendorExists = false;
                    for (var j = 0; j < deviceVendorList.length; j++) {
                        if (deviceVendorList[j].ItemValue === vendor) {
                            vendorExists = true;
                            break;
                        }
                    }
                    
                    if (!vendorExists) {
                        $.fn.validatebox.defaults.rules.deviceNameFormat.message = '设备厂商输入错误，请输入正确的厂商名称';
                        return false;
                    }
                    
                    return true;
                },
                message: '设备名称格式错误，请按照"楼层_系统名称及编号_设备子类及编号_设备厂商_型号_实配容量_投产年限"的格式输入，且必须包含6个下划线'
            }
        });

        function validateInput() {
            var isValid = $('#deviceNameInput').textbox('isValid');
            var value = $('#deviceNameInput').textbox('getValue');
            var resultDiv = document.getElementById('validationResult');
            
            if (isValid) {
                resultDiv.innerHTML = '<p class="success">✓ 校验通过！设备名称格式正确。</p>';
            } else {
                resultDiv.innerHTML = '<p class="error">✗ 校验失败！请检查设备名称格式。</p>';
            }
        }

        function fillExample() {
            $('#deviceNameInput').textbox('setValue', '3F_1#开关电源_1#整流屏_动力源_DKD42_1000A_2011');
            setTimeout(validateInput, 100);
        }

        function fillWrongExample() {
            $('#deviceNameInput').textbox('setValue', '3F_1#开关电源_1#整流屏_未知厂商_DKD42_1000A_2011');
            setTimeout(validateInput, 100);
        }

        $(document).ready(function() {
            // 绑定输入事件
            $('#deviceNameInput').textbox({
                onChange: function(newValue, oldValue) {
                    setTimeout(validateInput, 100);
                }
            });
        });
    </script>
</body>
</html>
