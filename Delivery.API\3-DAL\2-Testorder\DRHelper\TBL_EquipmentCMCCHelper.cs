﻿
using BDSTool.DBUtility;
using BDSTool.Entity.B;
using ENPC.Kolo.Entity.B_CMCC;

using log4net;

using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.Entity.DRHelper
{
    public class TBL_EquipmentCMCCHelper
    {
        private static readonly ILog logger = LogManager.GetLogger("BDSTool");

        public static TBL_EquipmentCMCC FromDataRow(DataRow row) {

            var entity = new TBL_EquipmentCMCC();

            entity.DeviceID = row["DeviceID"].ToString();
            entity.DeviceName = row["DeviceName"].ToString();
            entity.FSUID = row["FSUID"].ToString();
            entity.StationId = int.Parse(row["StationId"].ToString());
            entity.MonitorUnitId =int.Parse( row["MonitorUnitId"].ToString());
            entity.EquipmentId =int.Parse( row["EquipmentId"].ToString());
            entity.RoomName = row["RoomName"].ToString();
            entity.DeviceType = SHelper.ToIntNullable(row["DeviceType"]);
            entity.DeviceSubType = SHelper.ToIntNullable(row["DeviceSubType"]);
            entity.Model = row["Model"].ToString();
            entity.Brand = row["Brand"].ToString();
            entity.RatedCapacity = SHelper.ToFloatNullable(row["RatedCapacity"]);
            entity.Version = row["Version"].ToString();
            entity.BeginRunTime = SHelper.ToDateTimeNullable(row["BeginRunTime"]);
            entity.DevDescribe = row["DevDescribe"].ToString();
            //entity.ExtendField1 = row["ExtendField1"].ToString();
            //entity.ExtendField2 = row["ExtendField2"].ToString();                     
            return entity;
        }
    }
}
