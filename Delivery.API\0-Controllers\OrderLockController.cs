﻿using Delivery.API;
using DM.TestOrder.DAL;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;



namespace DM.TestOrder.Controllers {

    public class OrderLockController : BaseController {

        //var paras = {
        //        "orderid":1
        //        "userid":  -1,
        //        "islock": 1    //1:锁 0:解锁  
        //    };
        [HttpPost]
        public JsonResult POST([FromBody]JObject value) {
            var orderid = int.Parse(value["orderid"].ToString());
            var userid = int.Parse(value["userid"].ToString());
            var islock = int.Parse(value["islock"].ToString());

            WoTestOrderDal.UpdateLocker( orderid,  userid,  islock);

            var rtn = new {
                errormsg = "OK"
            };
            //var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(rtn);

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};
            return new JsonResult(rtn);
        }
    }
}
