﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class TTime
    {
        //此处是遵照移动的技术规范文档，月、日、时、分、秒都是char，只有年是short，
        //怀疑有问题，暂时按文档这么写
        public Int16 Year { get; private set; }

        public string Month { get; private set; }

        public string Day { get; private set; }

        public string Hour { get; private set; }

        public string Minute { get; private set; }

        public string Second { get; private set; }

        public TTime(Int16 year, string month, string day, string hour, string minute, string second)
        {
            Year = year;
            Month = month;
            Day = day;
            Hour = hour;
            Minute = minute;
            Second = second;
        }

        public TTime(DateTime dateTime)
        {
            if (dateTime == null)
            {
                dateTime = DateTime.Now;
            }
            Year = (Int16)dateTime.Year;
            Month = dateTime.Month.ToString();
            Day = dateTime.Day.ToString();
            Hour = dateTime.Hour.ToString();
            Minute = dateTime.Minute.ToString();
            Second = dateTime.Second.ToString();
        }

        public override string ToString()
        {
            string[] sTime = new string[] { Year.ToString(), Month, Day, Hour, Minute, Second };
            return string.Format("{0}-{1}-{2} {3}:{4}:{5}", sTime);
        }
    }
}
