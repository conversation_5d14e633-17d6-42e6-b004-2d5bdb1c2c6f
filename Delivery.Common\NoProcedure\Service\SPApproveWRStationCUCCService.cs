﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Service.CMCC;
using Delivery.Common.NoProcedure.Utils;

namespace Delivery.Common.NoProcedure.Service
{
    public class SPApproveWRStationCUCCService
    {
        private static SPApproveWRStationCUCCService _instance = null;

        public static SPApproveWRStationCUCCService Instance
        {
            get
            {
                if (_instance == null) _instance = new SPApproveWRStationCUCCService();
                return _instance;
            }
        }
        public DataTable ApproveWRStationCUCC(int? WRStationId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                realParams.Add("WRStationId", WRStationId);

                try
                {
                    int myStatus; int UserId; int SWStationId; int CenterId;
                    int AreaId; int StructureId; int SWStationCategory;
                    if (WRStationId == null)
                    {
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { 0 } }
                        };
                    }
                    DataTable dt = executeHelper.ExecDataTable("SP_ApproveWRStationCUCC_1", realParams);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        myStatus = (int)dt.Rows[0]["myStatus"];
                        UserId = (int)dt.Rows[0]["UserId"];
                        realParams.Add("UserId", UserId);
                        realParams.Add("myStatus", myStatus);
                        if (myStatus.Equals(3))
                        {
                            return new DataTable
                            {
                                Columns = { new DataColumn("ReturnValue", typeof(int)) },
                                Rows = { new object[] { -1 } }
                            };
                        }
                    }
                    try
                    {
                        Public_StoredService.Instance.SP_GenerateSiteWebId("TBL_Station", out SWStationId);
                        realParams.Add("SWStationId", SWStationId);
                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -2 } }
                        };

                    }
                    string currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    realParams.Add("currentTime", currentTime);

                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRStationCUCC_2", realParams);

                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -3 } }
                        };
                    }

                    DataTable dt2 = executeHelper.ExecDataTable("SP_ApproveWRStationCUCC_3", realParams);
                    if (dt2 != null && dt2.Rows.Count > 0)
                    {
                        CenterId = (int)dt2.Rows[0]["CenterId"];
                        realParams.Add("CenterId", CenterId);
                    }
                    DataTable dt3 = executeHelper.ExecDataTable("SP_ApproveWRStationCUCC_4", realParams);
                    if (dt3 != null && dt3.Rows.Count > 0)
                    {
                        SWStationCategory = (int)dt3.Rows[0]["SWStationCategory"];
                        if (SWStationCategory == 0)
                        {
                            executeHelper.Rollback();
                            return new DataTable
                            {
                                Columns = { new DataColumn("ReturnValue", typeof(int)) },
                                Rows = { new object[] { -13 } }
                            };
                        }
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRStationCUCC_5", realParams);
                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -4 } }
                        };
                    }


                    DataTable dt4 = executeHelper.ExecDataTable("SP_ApproveWRStationCUCC_6", realParams);
                    if (dt4 != null && dt4.Rows.Count > 0)
                    {
                        StructureId = (int)dt4.Rows[0]["StructureId"];
                        realParams.Add("StructureId", StructureId);
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRStationCUCC_7", realParams);
                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -6 } }
                        };
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRStationCUCC_8", realParams);
                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -7 } }
                        };
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRStationCUCC_9", realParams);
                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -8 } }
                        };
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRStationCUCC_10", realParams);
                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -9 } }
                        };
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRStationCUCC_11", realParams);
                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -10 } }
                        };
                    }


                    DataTable dt5 = executeHelper.ExecDataTable("SP_ApproveWRStationCUCC_12", realParams);
                    if (dt5 != null && dt5.Rows.Count > 0)
                    {
                        AreaId = (int)dt5.Rows[0]["AreaId"];
                        realParams.Add("AreaId", AreaId);
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRStationCUCC_13", realParams);
                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -11 } }
                        };
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRStationCUCC_14", realParams);
                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -12 } }
                        };
                    }

                    executeHelper.Commit();
                    return new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { 1 } }
                    };
                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    executeHelper.Rollback();
                    return new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { -2 } }
                    };
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

    }
}
