﻿


using BDSTool.DBUtility.Common;
using Common.Logging.Pro;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace BDSTool.BLL.S2
{
    public partial class MonitorUnitBiz
    {
        //字典表定义
        //34	0	VMU
        //34	1	RMU下MU
        //34	2	IDU
        //34	3	后台服务VMU
        //34	4	IPLU
        //34	5	IMU
        //34	6	eStone
        //34	7	IDU-X
        //34	8	ISU

        //SELECT * FROM TBL_DataItem WHERE EntryId=34
        //INSERT INTO dbo.TBL_DataItem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, [Enable], Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5)
        //VALUES (816, 0, 0, 34, 9, 'B接口', 'B接口', 1, 0, 1, '', NULL, NULL, NULL, NULL, NULL)
        //GO

       private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

       public static int? ZGetMonitorUnitCategory(int MonitorUnitId) {
           try {
                string rtn = null;
                if (CommonUtils.IsNoProcedure)
                {
                    rtn = Public_ExecuteSqlService.Instance.NoStore_TSLMonitorUnit_GetByMonitorUnitId(MonitorUnitId);
                }
                else
                {
                    var sql = string.Format(@"SELECT MonitorUnitCategory FROM TSL_MonitorUnit WHERE MonitorUnitId={0}", MonitorUnitId);
                    rtn = DBHelper.ExecuteScalar(sql);
                }
               if (rtn == null)
                   return null;

               return int.Parse(rtn);
           }
           catch (Exception ex) {
               logger.ErrorFormat("GetMonitorUnitCategory();MonitorUnitId={0};Error={1}", MonitorUnitId, ex.Message);
               logger.Error(ex.StackTrace);
               return null;
           }
       }

         public static bool ZIsSiteWebMonitorUnit(int MonitorUnitId) {
            try {
                string rtn = null;
                if (CommonUtils.IsNoProcedure)
                {
                    rtn = Public_ExecuteSqlService.Instance.NoStore_TSLMonitorUnit_GetByMonitorUnitId(MonitorUnitId);
                }
                else
                {
                    var sql = string.Format(@"SELECT MonitorUnitCategory FROM TSL_MonitorUnit WHERE MonitorUnitId={0}", MonitorUnitId);
                    rtn = DBHelper.ExecuteScalar(sql);
                }
                if (rtn == null)
                    return false;
                
                var MonitorUnitCategory = int.Parse(rtn);

                //TBL_DataItem entryId=34 itemId=9 itemname=B接口
                if (MonitorUnitCategory != 9)
                    return true;

                return false;
            }
            catch (Exception ex) {
                logger.ErrorFormat("IsSiteWebMonitorUnit();MonitorUnitId={0};Error={1}", MonitorUnitId, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
        }

    }
}
