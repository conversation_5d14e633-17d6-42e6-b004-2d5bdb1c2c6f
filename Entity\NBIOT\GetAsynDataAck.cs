﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 监控点数据应答(报文)
    /// </summary>
    public class GetAsynDataAck : BMessage
    {
        /// <summary>
        /// 返回结果
        /// </summary>
        [Newtonsoft.Json.JsonProperty(Order=1)]
        public EnumResult Result { get; set; }

        /// <summary>
        /// 失败的原因
        /// </summary>
        [Newtonsoft.Json.JsonProperty(Order=2)]
        public string FailureCause { get; set; }

        #region SiteWeb配置

        [Newtonsoft.Json.JsonIgnore]
        public int MyStationId { get; set; }

        #endregion   

        public GetAsynDataAck() : base()
        {
            MessageType = (int)BMessageType.GET_ASYN_DATA_ACK;
        }

        public GetAsynDataAck(string fsuId, EnumResult result, string failureCause)
            : base()
        {
            MessageType = (int)BMessageType.GET_DATA_ACK;

            FSUID = fsuId;
            Result = result;
            FailureCause = failureCause;
        }

        public static GetAsynDataAck Deserialize(string json)
        {
            GetAsynDataAck info = null;
            try
            {
                info = JsonConvert.DeserializeObject<GetAsynDataAck>(json);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetAsynDataAck.Deserialize:{0}", ex.Message);
                entityLogger.ErrorFormat("GetAsynDataAck.Deserialize:{0}", ex.StackTrace);
                info = new GetAsynDataAck();
                info.ErrorMsg = ex.Message;
                info.StringXML = json;
            }
            return info;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}, {2}:{3},{4}", MessageId, MessageType, FSUID, Result, FailureCause);
            return sb.ToString();
        }
    }
}