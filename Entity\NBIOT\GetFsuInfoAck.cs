﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 获取FSU状态信息响应
    /// </summary>
    public class GetFsuInfoAck : BMessage
    {
        /// <summary>
        /// 返回结果
        /// </summary>
        public EnumResult Result { get; set; }

        /// <summary>
        /// 失败的原因
        /// </summary>
        public string FailureCause { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public TFSUStatus FsuStatus { get; set; }

        private TJSFSUStatus _status;
        public TJSFSUStatus TFSUStatus
        {
            get { return _status; }
            set
            {
                _status = value;
                if (_status != null)
                {
                    FsuStatus = GetFSUStatusByJs(_status);
                }
            }
        }

        private TFSUStatus GetFSUStatusByJs(TJSFSUStatus status)
        {
            TFSUStatus fsuStatus = new TFSUStatus();
            if (status != null)
            {
                fsuStatus.CPUUsage = GetStatusByString(status.CPUUsage);
                fsuStatus.MEMUsage = GetStatusByString(status.MEMUsage);
                fsuStatus.HardDiskUsage = GetStatusByString(status.HardDiskUsage);
            }
            return fsuStatus;
        }

        private float GetStatusByString(string str)
        {
            float statusNo = 0;
            if (string.IsNullOrEmpty(str))
                return statusNo;
            if (str.Contains("%"))
            {
                int index = str.IndexOf("%");
                str = str.Substring(0, index);
            }
            double status = Convert.ToDouble(str);
            statusNo = (float)status;
            return statusNo;
        }

        #region SiteWeb配置

        [Newtonsoft.Json.JsonIgnore]
        public int MyHostId { get; set; }

        #endregion 

        public GetFsuInfoAck() : base()
        {
            MessageType = (int)BMessageType.GET_FSUINFO_ACK;
        }

        public GetFsuInfoAck(string fsuId, TFSUStatus fsuStatus, EnumResult result, string failureCause):base()
        {
            MessageType = (int)BMessageType.GET_FSUINFO_ACK;

            FSUID = fsuId;
            FsuStatus = fsuStatus;
            Result = result;
            FailureCause = failureCause;
        }

        public static GetFsuInfoAck Deserialize(string json)
        {
            GetFsuInfoAck info = null;
            try
            {
                info = JsonConvert.DeserializeObject<GetFsuInfoAck>(json);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetFsuInfoAck.Deserialize:{0}",ex.Message);
                entityLogger.ErrorFormat("GetFsuInfoAck.Deserialize:{0}",ex.StackTrace);
                info = new GetFsuInfoAck();
                info.ErrorMsg = ex.Message;
                info.StringXML = json;
            }
            return info;
        }

        public override string ToString()
        {
            return String.Format("{0},{1},{2},{3}:{4},{5}",
                MessageId, (BMessageType)MessageType, FSUID, FsuStatus.ToString(), Result, FailureCause);
        }
    }
}