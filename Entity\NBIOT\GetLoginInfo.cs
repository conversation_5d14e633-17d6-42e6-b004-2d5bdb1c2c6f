﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class GetLoginInfo : BMessage
    {
        public GetLoginInfo() : base()
        {
            MessageType = (int)BMessageType.GET_LOGININFO;
        }

        public GetLoginInfo(string fsuId) : base()
        {
            MessageType = (int)BMessageType.GET_LOGININFO;
            FSUID = fsuId;
        }

        public override string Serialize()
        {
            string result = string.Empty;
            try
            {
                JsonSerializerSettings settings = new JsonSerializerSettings();
                settings.ContractResolver = new CustomContractResolver();
                TIotEntity iotEntity = new TIotEntity(this);
                result = JsonConvert.SerializeObject(iotEntity, settings);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetLoginInfo.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetLoginInfo.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
            }
            return result;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}: {2}", MessageId, (BMessageType)MessageType, FSUID);
            return sb.ToString();
        }
    }
}