﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.6.8.批量设置FSU注册信息应答
    /// </summary>
    public sealed class SetLoginInfoAck:BMessage
    {
        public EnumResult Result { get; private set; }

        public string FailureCause { get; private set; }

        public SetLoginInfoAck(string fsuId, EnumResult result, string failureCause)
            : base()
        {
            MessageType = (int)BMessageType.SET_LOGININFO_ACK;

            FSUID = fsuId;
            Result = result;
            FailureCause = failureCause;
        }

        public SetLoginInfoAck() : base() 
        {
            MessageType = (int)BMessageType.SET_LOGININFO_ACK;
        }

        public static SetLoginInfoAck Deserialize(XmlDocument xmlDoc)
        {
            SetLoginInfoAck setLoginInfoAck = null;
            try
            {
                string fsuId = xmlDoc.SelectSingleNode("/Response/Info/FSUID").InnerText.Trim();
                string resultString = xmlDoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim();
                string failureCause = xmlDoc.SelectSingleNode("/Response/Info/FailureCause").InnerText.Trim();
                EnumResult result;
                if (string.IsNullOrEmpty(resultString))
                {
                    result = EnumResult.FAILURE;
                }
                else
                {
                    result = (EnumResult)int.Parse(resultString);
                }

                setLoginInfoAck = new SetLoginInfoAck(fsuId, result, failureCause);
                setLoginInfoAck.StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("SetLoginInfoAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                return setLoginInfoAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetLoginInfoAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetLoginInfoAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                setLoginInfoAck = new SetLoginInfoAck();
                setLoginInfoAck.ErrorMsg = ex.Message;
                setLoginInfoAck.StringXML = xmlDoc.InnerXml;
                return setLoginInfoAck;
            }
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}, {2}: {3}, {4}", MessageId, (BMessageType)MessageType, FSUID, Result, FailureCause);
        }

    }
}
