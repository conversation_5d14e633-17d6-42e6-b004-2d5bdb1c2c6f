﻿<?xml version="1.0" encoding="utf-8"?>
<?xml-stylesheet type="text/xsl" href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
		<procedure owner="" name="SP_ApproveWRStationCUCC_1" grant="">
			<parameters>
				<parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
				SELECT  WR_StationManagementCUCC.StationStatus,WR_StationManagementCUCC.UserId myStatus, UserId
				FROM WR_StationManagementCUCC WHERE WR_StationManagementCUCC.WRStationId = @WRStationId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_ApproveWRStationCUCC_2" grant="">
			<parameters>
				<parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="currentTime" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
				UPDATE WR_StationManagementCUCC SET WR_StationManagementCUCC.StationStatus = 3,
				WR_StationManagementCUCC.ApproveTime = @currentTime,WR_StationManagementCUCC.RejectCause = '',WR_StationManagementCUCC.SWStationId = @SWStationId
				WHERE WR_StationManagementCUCC.WRStationId = @WRStationId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_ApproveWRStationCUCC_3" grant="">
			<parameters>
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
				SELECT TBL_StationStructure.StructureId CenterId FROM TBL_StationStructure
				WHERE TBL_StationStructure.StructureType = 2 AND TBL_StationStructure.ParentStructureId = 0;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_ApproveWRStationCUCC_4" grant="">
			<parameters>
				<parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
				SELECT a.SiteWeb_ItemId SWStationCategory
				FROM WO_DicStationTypeMap a
				INNER JOIN WR_StationManagementCUCC b ON a.WR_ItemId = b.StationCategory
				WHERE b.WRStationId = @WRStationId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_ApproveWRStationCUCC_5" grant="">
			<parameters>
				<parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="CenterId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
				INSERT INTO TBL_Station (StationId, StationName, ConnectState, UpdateTime, StationCategory, StationGrade, StationState,
				CenterId, `Description`, InstallTime,ContainNode, `Enable`)
				SELECT a.SWStationId StationId, a.StationName, 2 ConnectState, NOW() UpdateTime,
				SWStationCategory StationCategory,
				1 StationGrade, 1 StationState,  @CenterId CenterId, '来源于交维' `Description`
				, a.ApplyTime, 0 ContainNode, 1 `Enable`
				FROM WR_StationManagementCUCC a
				WHERE a.WRStationId = @WRStationId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_ApproveWRStationCUCC_6" grant="">
			<parameters>
				<parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
				SELECT WR_StationManagementCUCC.StructureId StructureId FROM WR_StationManagementCUCC
				WHERE WR_StationManagementCUCC.WRStationId = @WRStationId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_ApproveWRStationCUCC_7" grant="">
			<parameters>
				<parameter name="StructureId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
				INSERT INTO TBL_StationStructureMap(StructureId, StationId)
				SELECT @StructureId,@SWStationId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_ApproveWRStationCUCC_8" grant="">
			<parameters>
				<parameter name="currentTime" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
				INSERT INTO TBL_House(HouseId, StationId, HouseName, Description, LastUpdateDate)
				SELECT 1 , @SWStationId , '默认局房'  , '默认局房' , @currentTime;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_ApproveWRStationCUCC_9" grant="">
			<parameters>
				<parameter name="currentTime" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
				INSERT INTO TBL_ConfigChangeMicroLog(ObjectId,ConfigId,EditType,UpdateTime)
				SELECT Convert( @SWStationId,CHAR) ObjectId, 1 ConfigId, 1 EditType, @currentTime UpdateTime;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_ApproveWRStationCUCC_10" grant="">
			<parameters>
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="currentTime" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
				INSERT INTO TBL_ConfigChangeMicroLog(ObjectId,ConfigId,EditType,UpdateTime)
				SELECT concat(Convert(@SWStationId,CHAR),'.1')  ObjectId, 5 ConfigId, 1 EditType, @currentTime UpdateTime;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_ApproveWRStationCUCC_11" grant="">
			<parameters>
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="currentTime" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
				INSERT INTO TBL_ConfigChangeMacroLog(ObjectId,ConfigId,EditType,UpdateTime)
				SELECT Convert(@SWStationId,CHAR) ObjectId, 1 ConfigId, 2 EditType, @currentTime UpdateTime;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_ApproveWRStationCUCC_12" grant="">
			<parameters>
				<parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
				SELECT b.OperationId AreaId FROM TBL_UserRoleMap a
				INNER JOIN TBL_UserRoleRight b ON a.RoleId = b.RoleId AND b.OperationType = 2
				WHERE a.UserId = @UserId limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_ApproveWRStationCUCC_13" grant="">
			<parameters>
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="AreaId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
				INSERT INTO TBL_AreaMap(StationId,AreaId)
				VALUES (@SWStationId , @AreaId);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_ApproveWRStationCUCC_14" grant="">
			<parameters>
				<parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<replaces>
			</replaces>
			<body>
				<![CDATA[
				INSERT INTO TBL_StationProjectInfo(StationId,ProjectName,ContractNo,InstallTime)
				SELECT SWStationId, T.ProjectName, T.ContractNo, T.ApplyTime
				FROM WR_StationManagementCUCC T WHERE T.WRStationId = @WRStationId;
				]]>
			</body>
		</procedure>
	</procedures>
</root>
