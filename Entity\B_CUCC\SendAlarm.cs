﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class SendAlarm : BMessage
    {
        public List<TAlarm> AlarmList { get; set; }
        public SendAlarm(List<TAlarm> alarmList,string suids,string surids) : base()
        {
            MessageType = (int)BMessageType.SEND_ALARM;
            AlarmList = alarmList;
            SUId = suids;
            SURId = surids;
        }
        public SendAlarm():base()
        {
            MessageType = (int)BMessageType.SEND_ALARM;
        }
        public static SendAlarm Deserialize(XmlDocument xmlDoc)
        {

            SendAlarm sendAlarm = null;
            string errMsg = string.Empty;
            string suId = string.Empty;
            string surId = string.Empty;
            try
            {
                suId = xmlDoc.SelectSingleNode("/Request/Info/SUId").InnerText.Trim();
                surId = xmlDoc.SelectSingleNode("/Request/Info/SURId").InnerText.Trim();
                XmlNodeList alarmNodeList = xmlDoc.SelectNodes("/Request/Info/Values/TAlarmList/TAlarm");
                List<TAlarm> list = new List<TAlarm>();
                foreach (XmlNode node in alarmNodeList)
                {
                    string serialNo = node.SelectSingleNode("SerialNo").InnerText.Trim();
                    //suId = node.SelectSingleNode("SUId").InnerText.Trim();
                    //surId = node.SelectSingleNode("SURId").InnerText.Trim();
                    string deviceId = node.SelectSingleNode("DeviceId").InnerText.Trim();
                    string deviceRId = node.SelectSingleNode("DeviceRId").InnerText.Trim();
                    //string id = node.SelectSingleNode("Id").InnerText.Trim();
                    //20180607 规范写的有问题，XML样例报文中不是标准的XML格式，基本定义里面又是采用<Id><Id>格式
                    //此处做一下处理，兼容<SignalId>XXX</SignalId>和<Signal Id="XXX"/>两种格式
                    string id = string.Empty;
                    XmlNode signalNode = node.SelectSingleNode("Signal");
                    XmlNode signalIdNode = node.SelectSingleNode("SignalId");
                    if (signalNode == null && signalIdNode != null)
                        id = signalIdNode.InnerText.Trim();
                    else if (signalNode != null && signalIdNode == null)
                        id = signalNode.Attributes["Id"].Value.Trim();
                    else
                        id = node.SelectSingleNode("Id").InnerText.Trim();
                    string strAlarmTime = node.SelectSingleNode("AlarmTime").InnerText.Trim();
                    DateTime? alarmTime;
                    if (string.IsNullOrEmpty(strAlarmTime))
                    {
                        errMsg = "SendAlarm TAlarm.AlarmTime is NullOrEmtpy";
                        break;
                    }
                    else
                    {
                        alarmTime = Convert.ToDateTime(strAlarmTime);
                    }
                    string alarmDesc = node.SelectSingleNode("AlarmDesc").InnerText.Trim();
                    string strTriggerVal = node.SelectSingleNode("TriggerVal").InnerText.Trim();
                    float? triggerVal;
                    if (string.IsNullOrEmpty(strTriggerVal))
                    {
                        errMsg = "SendAlarm TAlarm.TriggerVal is NullOrEmtpy";
                        break;
                    }
                    else
                    {
                        strTriggerVal = strTriggerVal.ToUpper();
                        strTriggerVal = strTriggerVal.Replace('V', ' ').Replace('A', ' ').Replace('℃', ' ').Replace('%', ' ').Replace('R', ' ').Replace('H', ' ');
                        strTriggerVal = strTriggerVal.Replace('K', ' ').Replace('B', ' ').Replace('Y', ' ').Replace('T', ' ').Replace('E', ' ').Replace('D', ' ');
                        strTriggerVal = strTriggerVal.Replace('D', ' ').Replace('M', ' ').Trim();
                        triggerVal = float.Parse(strTriggerVal);
                    }
                    string strAlarmFlag = node.SelectSingleNode("AlarmFlag").InnerText.Trim();
                    EnumFlag enumFlag;
                    if (string.IsNullOrEmpty(strAlarmFlag))
                    {
                        errMsg = "SendAlarm TAlarm.AlarmFlag is NullOrEmtpy";
                        break;
                    }
                    else
                    {
                        if (strAlarmFlag.ToUpper() == "BEGIN")
                        {
                            enumFlag = EnumFlag.BEGIN;
                        }
                        else if (strAlarmFlag.ToUpper() == "END")
                        {
                            enumFlag = EnumFlag.END;
                        }
                        else
                        {
                            enumFlag = (EnumFlag)int.Parse(strAlarmFlag);
                        }
                    }
                    TAlarm alarm = new TAlarm(serialNo, suId, surId, id, deviceId, deviceRId, alarmTime, alarmDesc, triggerVal, enumFlag);
                    list.Add(alarm);
                }
                if (errMsg != string.Empty)
                {
                    entityLogger.ErrorFormat("SendAlarm.Deserialize():{0}", errMsg);
                    sendAlarm = GetErrorEntity(suId, surId, errMsg);
                    sendAlarm.ErrorMsg = errMsg;
                }
                else
                {
                    sendAlarm = new SendAlarm();
                    sendAlarm.AlarmList = list;
                }
                sendAlarm.SUId = suId;
                sendAlarm.SURId = surId;
                entityLogger.DebugFormat("SendAlarm.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                sendAlarm.StringXML = xmlDoc.InnerXml;
                return sendAlarm;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SendAlarm.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SendAlarm.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                sendAlarm = GetErrorEntity(suId, surId, ex.Message);
                sendAlarm.StringXML = xmlDoc.InnerXml;
                return sendAlarm;
            }
        }

        /// <summary>
        /// 反序列化异常时，构造带错误信息的实体
        /// </summary>
        /// <param name="errorMsg">解析错误信息</param>
        /// <returns></returns>
        private static SendAlarm GetErrorEntity(string suId, string surId, string errorMsg)
        {
            SendAlarm errorEntity = new SendAlarm();
            SendAlarmAck ackEntity = new SendAlarmAck(suId, surId, EnumResult.FAILURE);
            ackEntity.ErrorMsg = errorMsg;
            errorEntity.ErrorMsg = ackEntity.Serialize();
            return errorEntity;
        }
       
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1},{2}", MessageId, (BMessageType)MessageType, MessageType);
            foreach (TAlarm alarm in AlarmList)
            {
                sb.Append(",").Append(alarm.ToString());
            }
            return sb.ToString();
        }
    }
}
