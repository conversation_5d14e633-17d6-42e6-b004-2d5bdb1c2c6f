﻿using Common.Logging.Pro;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace BDSTool.Utility
{
    public class LoggerBDSTool
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");
        private static readonly log4net.ILog loggerSql = LogManager.GetLogger("BDSToolSql");
        private static readonly string PartHeader = new string('=', 100) + ">";
        private static readonly string PartEnd =    "<"+new string('=', 100) +"\r\n";
        public static void InfoPartHeader() {
            logger.Info("");
            logger.Info(PartHeader);

            loggerSql.Info("");
            loggerSql.Info(PartHeader);
        }
        public static void InfoPartEnd() {
            logger.Info(PartEnd);
            loggerSql.Info(PartEnd);
        }

        public static void WarnPartEnd() {
            logger.Warn(PartEnd);
        }

        public static void Info(string message) {
            logger.Info(message);
        }

        public static void InfoFormat(string message, params object[] args) {
            logger.InfoFormat(message, args);
        }

        public static void InfoFormatOnPartEnd(string message, params object[] args) {
            logger.InfoFormat(message, args);
            LoggerBDSTool.InfoPartEnd();
        }
        public static void Warn(string message) {
            logger.Warn(message);
        }

        public static void WarnFormat(string message, params object[] args) {
            logger.WarnFormat(message, args);
        }
        public static void WarnFormatOnPartEnd(string message, params object[] args) {
            logger.WarnFormat(message, args);
            LoggerBDSTool.WarnPartEnd();
        }
        public static void Error(string message) {
            logger.Error(message);
        }

        public static void ErrorFormat(string message, params object[] args) {
            logger.ErrorFormat(message, args);
        }
    }
}
