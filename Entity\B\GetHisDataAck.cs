﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B
{
    public sealed class GetHistDataAckContent : BMessage
    {
        public GetHisDataAck[] Devices;

        public void GetHisDataAckContent()
        {
            MessageType = (int)BMessageType.GetHisDataAck;
        }

        public static GetHistDataAckContent Deserialize(XmlDocument xmldoc)
        {
            GetHistDataAckContent content = new GetHistDataAckContent();
            content.Devices = GetHisDataAck.Deserialize(xmldoc);
            content.MessageType = (int)BMessageType.GetHisDataAck;
            return content;
        }
    }

    public sealed class GetHisDataAck : BMessage
    {
        public string DeviceId { get; private set; }
        public string DeviceCode { get; private set; }

        public EnumResult Result { get; private set; }

        public TSemaphore[] Values { get; private set; }

        public int HisSignalCounts { get; private set; }

        #region SiteWeb配置

        public int MyStationId { get; set; }
        public int MyDeviceId { get; set; }

        #endregion

        public GetHisDataAck(string fsuId, string fsuCode, EnumResult result, params TSemaphore[] values)
            : base()
        {
            MessageType = (int)BMessageType.GetHisDataAck;

            FsuId = fsuId;
            FsuCode = fsuCode;
            Result = result;
            Values = values;

            HisSignalCounts = 0;
        }

        public override string Serialize()
        {
            throw new NotImplementedException();
        }

        public static GetHisDataAck[] Deserialize(XmlDocument xmldoc)
        {
            List<GetHisDataAck> listHisData = new List<GetHisDataAck>();

            string fsuId = xmldoc.SelectSingleNode("/Response/Info/FsuId").InnerText;
            string fsuCode = xmldoc.SelectSingleNode("/Response/Info/FsuCode").InnerText;

            string resultString = xmldoc.SelectSingleNode("/Response/Info/Result").InnerText;
            EnumResult enumResult = new EnumResult();
            if (resultString != null && resultString != "")
            {
                int result = int.Parse(xmldoc.SelectSingleNode("/Response/Info/Result").InnerText);
                enumResult = (EnumResult)Enum.Parse(typeof(EnumResult), result.ToString(), false);
            }

            XmlNodeList nodelist = xmldoc.SelectNodes("/Response/Info/Values/DeviceList/Device");
            TSemaphore[] semaphoreValue = null;

            string deviceId = null;
            string deviceCode = null;
            foreach (XmlNode xn in nodelist)//遍历所有子节点 
            {
                XmlElement xe = (XmlElement)xn;
                deviceId = xe.GetAttribute("Id");
                deviceCode = xe.GetAttribute("Code");

                List<TSemaphore> semaphoreList = new List<TSemaphore>();

                int counts = 0;
                GetHisDataAck getDataAck = null;
                foreach (XmlNode xn1 in xe.ChildNodes)//遍历 
                {
                    XmlElement xe2 = (XmlElement)xn1;//转换类型 

                    string type = xe2.GetAttribute("Type");
                    EnumType enumType = (EnumType)Enum.Parse(typeof(EnumType), type, false);
                    string signalId = xe2.GetAttribute("Id");
                    float measuredVal = float.Parse(xe2.GetAttribute("MeasuredVal"));
                    float setupVal = float.Parse(xe2.GetAttribute("SetupVal"));
                    string status = xe2.GetAttribute("Status");
                    EnumState enumState = (EnumState)Enum.Parse(typeof(EnumState), status, false);
                    DateTime recordTime = Convert.ToDateTime(xe2.GetAttribute("RecordTime"));

                    TSemaphore tSemaphore = new TSemaphore(enumType, signalId, measuredVal, setupVal, enumState, recordTime);
                    counts++;
                    semaphoreList.Add(tSemaphore);

                    //信号超过190条，则会导致存储过程PBL_BatchSaveHistorySignal的参数BSampleTime超过4000字节，导致存储失败
                    if (getDataAck == null && semaphoreList.Count >= 190)
                    {
                        semaphoreValue = semaphoreList.ToArray();

                        getDataAck = new GetHisDataAck(fsuId, fsuCode, enumResult, semaphoreValue);
                        getDataAck.DeviceCode = deviceCode;
                        getDataAck.DeviceId = deviceId;
                        getDataAck.HisSignalCounts = counts;
                        counts = 0;

                        listHisData.Add(getDataAck);
                        getDataAck = null;
                        semaphoreList = new List<TSemaphore>();
                    }
                }
                if (getDataAck == null)
                {
                    semaphoreValue = semaphoreList.ToArray();

                    getDataAck = new GetHisDataAck(fsuId, fsuCode, enumResult, semaphoreValue);
                    getDataAck.DeviceCode = deviceCode;
                    getDataAck.DeviceId = deviceId;
                    getDataAck.HisSignalCounts = counts;

                    listHisData.Add(getDataAck);
                }
            }

            return listHisData.ToArray();
        }

        public override string ToShortString()
        {
            return String.Format("{0}, {1}: {2}, {3}, {4}, {5}",
                MessageId, (BMessageType)MessageType, FsuId, FsuCode, Result, Values.Length);
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();

            sb.AppendFormat("{0}, {1}: {2}, {3}, {4}, {5}",
                MessageId, (BMessageType)MessageType, FsuId, FsuCode, Result, Values != null?Values.Length : 0);
            sb.AppendLine();
            if (Values != null)
            {
                foreach (TSemaphore item in Values)
                {
                    sb.AppendFormat("{0}\r\n", item);
                }
            }

            return sb.ToString();
        }
    }
}
