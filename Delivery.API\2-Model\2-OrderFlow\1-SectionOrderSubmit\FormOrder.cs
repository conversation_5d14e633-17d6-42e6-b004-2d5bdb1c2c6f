﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;




namespace DM.TestOrder.Model {
    
    //public static void CreateTestOrder(string ApplyOrderId, string ApplyUserId, string StationId, string StationName) {
    public class FormOrder {
        //public FormOrder() {
        //    ApplyOrderId = Guid.NewGuid().ToString();
        //}
        
        public int OrderId;//冗余

        public int NeedUpload;//是否要上载文件

        //GUID的格式为：xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
        //      f240263f-c589-421c-a026-a409df7cf4d7
        //public string ApplyOrderId;


        //public string MyTypeOfCheckOrder;
        public int StationId = 0;

        //割接类型
        public string OrderTypeString;//新装入网/维护巡检

        public string MyOrderId;        //割接单号
        public string StationName;//站址名称			
        public string CenterName;//监控中心				
        public string StationCategoryName;//站址类型		
        public string StationGroupName;//分    组				
        public string SiteCode; //TSL_MonitorUnitCMCC中有，可能无法保证获取 //站址编码				
        public string ApplyUserName;//创 建 人		
        public string ApplyUserFsuVendor; //FSU厂家, 用户的角色名	


        public int ApplyUserId;//冗余

        

        public decimal Latitude;//经    度	        //纬    度	
        public decimal Longitude;
        public List<ItemEquip> MyEquips = new List<ItemEquip>();
        public string InstallCompany;//安装公司
        public string InstallClerk;//安装人员

        public int InstallCompanyId;
        public int InstallClerkId;

        //协议归档		2/2		照片归档		2/3		通过项		18/20		工艺自查		4/4	
        public string ScoreDocDevice;
        public string ScoreDocPhoto;
        public string ScoreDocTest;
        public string ScoreDocArt;

    }
}
