﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B_CUCC
{

    /// <summary>
    /// SU向SC 提供的权限
    /// </summary>
    public enum EnumRightMode
    {
        /// <summary>
        /// 无权限
        /// </summary>
        INVALID = 0,

        /// <summary>
        /// 具备数据读的权限,当用户可以读某个数据，而无法写任何数据时返回这一权限值
        /// </summary>
        LEVEL1  = 1,

        /// <summary>
        /// 具备数据读、写的权限，当用户对某个数据具有读写权限时返回这一权限值
        /// </summary>
        LEVEL2  = 2
    }

    /// <summary>
    /// 报文返回结果
    /// </summary>
    public enum EnumResult
    {
        /// <summary>
        /// 失败
        /// </summary>
        FAILURE = 0,

        /// <summary>
        /// 成功
        /// </summary>
        SUCCESS = 1
    }

    /// <summary>
    /// 告警标志
    /// </summary>
    public enum EnumFlag
    {
        /// <summary>
        /// 开始
        /// </summary>
        BEGIN = 0,

        /// <summary>
        /// 结束
        /// </summary>
        END = 1
    }

    /// <summary>
    /// SC对SU返回的协议包进行解析后，设置的状态码
    /// </summary>
    public enum EnumReturnCode
    {
        /// <summary>
        /// 正常
        /// </summary>
        NORMAL = 0,

        /// <summary>
        /// 注册包中配置信息和数据库中设备不一致
        /// </summary>
        CONFIGCHANGE = 1,

        /// <summary>
        /// SU上送告警时，发现SC端设备已丢失
        /// </summary>
        EQUIPMENTLOST = 2

    }

}
