﻿using Delivery.Common.NoProcedure.Utils;
using MySql.Data.MySqlClient;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Common;
using System.Security.Cryptography;
using System.Text;

namespace Delivery.Common.NoProcedure.Utils.DB
{
    public class MysqlDatabase:DataAccessObject{

        public const String MYSQLRETURNPARAMETER = "ret";

        public MysqlDatabase(string connectString) 
        {
            this.ConnectionString = connectString;
        }
        /// <summary>
        /// 新建数据ProviderF工厂
        /// </summary>
        /// <returns></returns>
        public override DbProviderFactory GetClientFactory()
       {
            return MySqlClientFactory.Instance;
       }

        public override  DbParameter GetReturnDbParameter()
        {
            DbParameter dbParamter = null;

            dbParamter = GetDbParameter(MYSQLRETURNPARAMETER, DbType.Int32, 0, ParameterDirection.ReturnValue);


            return dbParamter;
        }

        public override Object GetReturnDbParameterValue() 
        {
            var parameterName = string.Empty;
            parameterName = BuildParameterName(MYSQLRETURNPARAMETER);
            return Command.Parameters[parameterName].Value;
        }

        private static string BuildParameterName(string parameterName)
        {
            //Sybase,SQLServer前缀需加@
            return "@" + parameterName;
        }

    }
}
