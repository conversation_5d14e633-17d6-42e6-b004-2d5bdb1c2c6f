﻿using Common.Logging.Pro;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace BDSTool.Utility
{
    public class BDSLogConfigAndMain
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");
        private static readonly log4net.ILog loggerConfig = LogManager.GetLogger("BDSToolConfig");
        private static readonly string PartHeader = new string('=', 100)+">";
        private static readonly string PartEnd = new string('^', 101);
        public static void InfoPartHeader() {
            logger.Info("");
            loggerConfig.Info("");
            logger.Info(PartHeader);
            loggerConfig.Info(PartHeader);
        }
        public static void InfoPartEnd() {
            logger.Info(PartEnd);
            loggerConfig.Info(PartEnd);
        }


        public static void Info(string message) {
            logger.Info(message);
            loggerConfig.Info(message);
        }

        public static void InfoFormat(string message, params object[] args) {
            logger.InfoFormat(message, args);
            loggerConfig.InfoFormat(message, args);
        }

        public static void Warn(string message) {
            logger.Warn(message);
            loggerConfig.Warn(message);
        }

        public static void WarnFormat(string message, params object[] args) {
            logger.WarnFormat(message, args);
            loggerConfig.WarnFormat(message, args);
        }

        public static void Error(string message) {
            logger.Error(message);
            loggerConfig.Error(message);
        }

        public static void ErrorFormat(string message, params object[] args) {
            logger.ErrorFormat(message, args);
            loggerConfig.ErrorFormat(message, args);
        }
    }
}
