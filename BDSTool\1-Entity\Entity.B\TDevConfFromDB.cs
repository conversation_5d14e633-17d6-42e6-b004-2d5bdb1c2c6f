﻿
using BDSTool.BLL.Convert;
using BDSTool.BLL.S2;
using BDSTool.Entity.S2;
using BDSTool.Utility;

using Common.Logging.Pro;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.Entity.B
{
    public partial class TDevConfFromDB 
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public TDevConf device=new TDevConf();
        #region prop
        //------------------------------------------------------
        public string FSUID {
            get;
            set;
        }
        public int StationId {
            get;
            set;
        }

        //public int? MonitorUnitId {
        //    get;
        //    set;
        //}

        //只有在从数据库构造TDev时使用
        public int EquipmentId {
            get;
            set;
        }


        //public int? SamplerUnitId {
        //    get;
        //    set;
        //}
        #endregion
    }
}
