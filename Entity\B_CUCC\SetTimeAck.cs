﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class SetTimeAck : BMessage
    {
        public EnumResult Result { get; set; }

        public SetTimeAck(string suids, string surids, EnumResult result) : base()
        {
            MessageType = (int)BMessageType.SET_TIME_ACK;
            SUId = suids;
            SURId = surids;
            Result = result;
        }
        public SetTimeAck() : base()
        {
            MessageType = (int)BMessageType.SET_TIME_ACK;
        }
        public static SetTimeAck Deserialize(XmlDocument xmldoc)
        {
            SetTimeAck setSameTimeAck = null;
            try
            {
                string suid = xmldoc.SelectSingleNode("/Response/Info/SUId").InnerText.Trim();
                string surid = xmldoc.SelectSingleNode("/Response/Info/SURId").InnerText.Trim();
                string result = xmldoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim();
                setSameTimeAck = new SetTimeAck(suid, surid, string.IsNullOrEmpty(result) ? EnumResult.FAILURE : (EnumResult)int.Parse(result));
                entityLogger.DebugFormat("SetTimeAck.Deserialize(),xml:\r\n{0}", FormatXml(xmldoc.InnerXml));
                setSameTimeAck.StringXML = xmldoc.InnerXml;
                return setSameTimeAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetTimeAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetTimeAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmldoc.InnerXml));
                setSameTimeAck = new SetTimeAck();
                setSameTimeAck.ErrorMsg = ex.Message;
                setSameTimeAck.StringXML = xmldoc.InnerXml;
                return setSameTimeAck;
            }
        }

        public override string ToString()
        {
            return String.Format("{0}, {1},{2},{3}, {4}: {5}",
                MessageId, (BMessageType)MessageType,MessageType, SUId, SURId, Result);
        }
    }
}
