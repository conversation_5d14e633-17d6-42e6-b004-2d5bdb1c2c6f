﻿using Delivery.API;
using DM.TestOrder.Common.DBA;
using DM.TestOrder.DAL;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using testorder.dal;

namespace DM.TestOrder.Controllers {
        
    public class AccountController : BaseController{
        [HttpGet]
        public JsonResult Get(int userid) {
            var dt = WoAccountDal.GetAccount(userid);
            // var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(dt);

            return new JsonResult(dt);

        }

    }
}
