﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B_CMCC
{

    public sealed class DeviceSetPointAck
    {
        public string DeviceId { get; private set; }

        public List<string> SuccessIds { get; private set; }
        public List<string> FailIds { get; private set; }

        public List<TSignalMeasurementId> SuccessIds2 { get; private set; }
        public List<TSignalMeasurementId> FailIds2 { get; private set; }

        #region SiteWeb配置

        public int MyDeviceId { get; set; }

        #endregion

        public DeviceSetPointAck(string deviceId, List<TSignalMeasurementId> successIds, List<TSignalMeasurementId> failIds)
        {
            DeviceId = deviceId;

            SuccessIds2 = successIds;
            FailIds2 = failIds;
            SuccessIds = new List<string>();
            FailIds = new List<string>();
            if (SuccessIds2 != null && SuccessIds2.Count > 0)
            {
                foreach (TSignalMeasurementId tsmid in SuccessIds2)
                {
                    SuccessIds.Add(tsmid.ID + tsmid.SignalNumber);
                }
            }
            if (FailIds2 != null && FailIds2.Count > 0)
            {
                foreach (TSignalMeasurementId tsmid in FailIds2)
                {
                    FailIds.Add(tsmid.ID + tsmid.SignalNumber);
                }
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}", DeviceId);
            sb.AppendLine();

            sb.Append("SuccessList:");
            sb.AppendLine();
            foreach (TSignalMeasurementId item in SuccessIds2)
            {
                sb.AppendFormat("{0}", item.ToString());
                sb.AppendLine();
            }

            sb.Append("FailList:");
            sb.AppendLine();
            foreach (TSignalMeasurementId item in FailIds2)
            {
                sb.AppendFormat("{0}", item.ToString());
                sb.AppendLine();
            }

            return sb.ToString();
        }

    }
}
