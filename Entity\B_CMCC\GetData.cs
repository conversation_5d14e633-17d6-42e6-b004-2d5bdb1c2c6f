﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.6.3.	请求监控点数据
    /// </summary>
    public sealed class GetData: BMessage
    {

        public string DeviceID { get; private set; }

        public string[] IDs { get; private set; }

        public GetData(string fsuId, string deviceId, string[] ids):base()
        {
            MessageType = (int)BMessageType.GET_DATA;

            FSUID = fsuId;
            DeviceID = deviceId;
            IDs = ids;
        }

        public override string Serialize()
        {
            try
            { 
                XmlDocument xmldoc = CreateXmlDocument();
                AppendPK_Type(xmldoc, "Request", BMessageType.GET_DATA.ToString());

                XmlElement xe2 = xmldoc.CreateElement("Info");
                XmlElement xe21 = xmldoc.CreateElement("FSUID");
                xe21.InnerText = FSUID;
                xe2.AppendChild(xe21);

                XmlElement xe22 = xmldoc.CreateElement("DeviceList");
                XmlElement xe221 = xmldoc.CreateElement("Device");
                xe221.SetAttribute("ID", DeviceID);
                xe22.AppendChild(xe221);
                xe2.AppendChild(xe22);

                foreach (string id in IDs)
                {
                    XmlElement xe2211 = xmldoc.CreateElement("ID");
                    xe2211.InnerText = id;
                    xe221.AppendChild(xe2211);
                }

                XmlNode root = xmldoc.SelectSingleNode("Request");
                root.AppendChild(xe2);

                StringXML = xmldoc.InnerXml;
                entityLogger.DebugFormat("GetData.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmldoc.InnerXml;
            }
            catch(Exception ex)
            {
                entityLogger.ErrorFormat("GetData.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetData.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }


        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            string str = String.Format("{0}, {1}, {2}.{3}: ",
                MessageId, (BMessageType)MessageType, FSUID, DeviceID);
            sb.Append(str);

            foreach (string id in IDs)
            {
                sb.AppendFormat("[{0}]", id);
            }

            return sb.ToString();
        }
    }
}
