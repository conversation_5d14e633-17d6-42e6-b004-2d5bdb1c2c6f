﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.6.4.	写监控点设置值
    /// </summary>
    public sealed class SetPoint: BMessage
    {
       
        public List<Device> Devices { get; private set; }

        public int UserId { get; set; }
        public DateTime StartTime { get; set; }
        public int ControlId { get; set; }

        /// <summary>
        /// 数据流格式定义中的变量名称定义不合适，此处根据需要定义一个设备实体
        /// </summary>
        /// <param name="fsuId">FSUID号</param>
        /// <param name="devices">写监控点的设备实体</param>
        public SetPoint(string fsuId, List<Device> devices):base()
        {
            MessageType = (int)BMessageType.SET_POINT;

            FSUID = fsuId;
            Devices = devices;

        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmlDoc = CreateXmlDocument();
                AppendPK_Type(xmlDoc, "Request", BMessageType.SET_POINT.ToString());

                XmlElement xe2 = xmlDoc.CreateElement("Info");
                XmlElement xe21 = xmlDoc.CreateElement("FSUID");
                xe21.InnerText = FSUID;
                xe2.AppendChild(xe21);

                XmlElement xe22 = xmlDoc.CreateElement("Value");
                XmlElement xe221 = xmlDoc.CreateElement("DeviceList");
                foreach(Device device in Devices)
                {
                    XmlElement xeDevice = xmlDoc.CreateElement("Device");
                    xeDevice.SetAttribute("ID", device.DeviceId);
                    foreach(TSemaphore semaphore in device.Semaphores)
                    {
                        XmlElement xeSemaphore = xmlDoc.CreateElement("TSemaphore");
                        xeSemaphore.SetAttribute("Type", ((int)semaphore.Type).ToString());
                        xeSemaphore.SetAttribute("ID", semaphore.ID);
                        xeSemaphore.SetAttribute("SignalNumber", semaphore.SignalNumber);
                        xeSemaphore.SetAttribute("MeasuredVal", "NULL");//semaphore.MeasuredVal.HasValue?semaphore.MeasuredVal.ToString():"NULL");
                        xeSemaphore.SetAttribute("SetupVal", semaphore.SetupVal.HasValue?semaphore.SetupVal.ToString():"NULL");
                        xeSemaphore.SetAttribute("Status", ((int)semaphore.Status).ToString());
                        xeSemaphore.SetAttribute("Time", semaphore.Time.HasValue?Convert.ToDateTime(semaphore.Time).ToString("yyyy-MM-dd HH:mm:ss"):"");
                        xeDevice.AppendChild(xeSemaphore);
                    }
                    xe221.AppendChild(xeDevice);
                }
                xe22.AppendChild(xe221);
                xe2.AppendChild(xe22);

                XmlNode root = xmlDoc.SelectSingleNode("Request");
                root.AppendChild(xe2);
                StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("SetPoint.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmlDoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetPoint.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetPoint.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            string str = String.Format("{0}, {1}, {2}:",
                MessageId, (BMessageType)MessageType, FSUID);
            sb.Append(str);

            foreach (Device device in Devices)
            {
                sb.AppendFormat("[{0}]", device.ToString());
            }
            return sb.ToString();
        }

    }
}
