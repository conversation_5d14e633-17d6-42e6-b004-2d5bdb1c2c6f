﻿using Delivery.API;
using DM.TestOrder.DAL;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using System.Net.Http;
using System.Text;



namespace DM.TestOrder.Controllers {
        
    public class NewClerkController : BaseController{

        //var paras = {
        //        "CompanyName": "新增公司",
        //        "ClerkName": "新增某人",
        //    };
        [HttpPost]
        public JsonResult POST([FromBody]JObject value) {
            var CompanyId =int.Parse( value["CompanyId"].ToString());
            //var CompanyName = value["CompanyName"].ToString();
            var ClerkName = value["ClerkName"].ToString();

            var rtnMsg = WoInstallClerkDal.AddOneToCompany(CompanyId, ClerkName);


            var rtn = new {
                errormsg = rtnMsg
            };
            //var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(rtn);


            //Debug.WriteLine(value.ToString());
            //Debug.WriteLine(jsonRtn.ToString());

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};
            return new JsonResult(rtn);
        }
    }
}
