﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class SendDataAck : BMessage
    {
        [Newtonsoft.Json.JsonProperty(Order = 1)]
        [Newtonsoft.Json.JsonConverter(typeof(CustomSringConverter))] 
        public EnumResult Result { get; set; }

        [Newtonsoft.Json.JsonProperty(Order = 2)]
        public string FailureCause { get; set; }

        public SendDataAck() : base()
        {
            MessageType = (int)BMessageType.SEND_DATA_ACK;
        }

        public SendDataAck(EnumResult result, string failureCause) : base()
        {
            MessageType = (int)BMessageType.SEND_DATA_ACK;
            Result = result;
            FailureCause = failureCause;
        }

        public override string Serialize()
        {
            string result = string.Empty;
            try
            {
                JsonSerializerSettings settings = new JsonSerializerSettings();
                settings.ContractResolver = new CustomContractResolver();
                TIotEntity iotEntity = new TIotEntity(this);
                result = JsonConvert.SerializeObject(iotEntity, settings);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SendDataAck.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SendDataAck.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
            }
            return result;
        }

        public override string ToString()
        {
            return string.Format("{0},{1}:{2},{3}", MessageId, (BMessageType)MessageType, Result, FailureCause);
        }
    }
}