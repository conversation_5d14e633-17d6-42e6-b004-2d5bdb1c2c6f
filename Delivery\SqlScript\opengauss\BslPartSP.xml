﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>

    <!-- $$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$  1  $$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$ -->
    <procedure owner="" name="RejectWRFsu_GetFsuStatus" grant="">
      <parameters>
        <parameter name="WRFsuId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body> SELECT FsuStatus FROM WR_FsuManagement WHERE WRFsuId = @WRFsuId </body>
    </procedure>
    <procedure owner="" name="RejectWRFsu_UpdateFsuManagement" grant="">
      <parameters>
        <parameter name="WRFsuId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="RejectCause" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body> UPDATE WR_FsuManagement SET FsuStatus = 2, RejectCause = @RejectCause WHERE WRFsuId = @WRFsuId </body>
    </procedure>
    <!-- ================================================================================================ -->
    <procedure owner="" name="BslPartSp_GetUserId" grant="">
      <parameters>
        <parameter name="LogonId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body> SELECT UserId FROM TBL_Account WHERE LogonId = @LogonId </body>
    </procedure>
    <procedure owner="" name="DelWRFsu_GetFsuManagementCount" grant="">
      <parameters>
        <parameter name="WRFsuId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="UserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body> SELECT count(*) FROM WR_FsuManagement WHERE WRFsuId = @WRFsuId AND UserId = @UserId </body>
    </procedure>
    <procedure owner="" name="DelWRFsu_GetFsuInfo" grant="">
      <parameters>
        <parameter name="WRFsuId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body> SELECT c.WRStationId, c.SWStationId, a.SWMonitorUnitId ,a.FsuStatus 
	            FROM WR_FsuManagement a 
	                 INNER JOIN WR_HouseManagement b ON a.WRHouseId = b.WRHouseId
	                 INNER JOIN WR_StationManagement c ON b.WRStationId = c.WRStationId
	            WHERE a.WRFsuId = @WRFsuId </body>
    </procedure>
    <procedure owner="" name="DelWRFsu_DelFsuManagement" grant="">
      <parameters>
        <parameter name="WRFsuId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>DELETE FROM WR_FsuManagement WHERE WRFsuId = @WRFsuId</body>
    </procedure>
    <procedure owner="" name="DelWRFsu_GetMonitorUnitName" grant="">
      <parameters>
        <parameter name="SWMonitorUnitId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body> SELECT MonitorUnitName FROM TSL_MonitorUnit
              WHERE MonitorUnitId = @SWMonitorUnitId AND StationId = @SWStationId </body>
    </procedure>
    <procedure owner="" name="DelWRFsu_GetEquipInfo" grant="">
      <parameters>
        <parameter name="SWMonitorUnitId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body> SELECT  EquipmentId, EquipmentName FROM TBL_Equipment WHERE StationId = @SWStationId 
                AND MonitorUnitId = @SWMonitorUnitId AND EquipmentCategory = 99 </body>
    </procedure>
    <procedure owner="" name="DelWRFsu_GetWRDeviceIds" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="WRFsuId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT WRDeviceId FROM WR_DeviceManagement WHERE WRStationId = @WRStationId AND WRFsuId = @WRFsuId</body>
    </procedure>
    <procedure owner="" name="DelWRFsu_GetFsuName" grant="">
      <parameters>
        <parameter name="WRFsuId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT FsuName FROM WR_FsuManagement WHERE WRFsuId = @WRFsuId</body>
    </procedure>
    <procedure owner="" name="DelWRFsu_GetStationName" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT StationName FROM WR_StationManagement WHERE WRStationId = @WRStationId</body>
    </procedure>
    <procedure owner="" name="DelWRFsu_InsertOperationDetail6" grant="">
      <parameters>
        <parameter name="UserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWMonitorUnitId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWMonitorUnitName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        INSERT INTO TBL_OperationDetail (UserId,	ObjectId,ObjectType,PropertyName,OperationTime,OperationType,OldValue)
        VALUES(@UserId, CAST(@SWMonitorUnitId AS TEXT), 6, '监控单元名称', NOW(), '删除',@SWMonitorUnitName)
      </body>
    </procedure>
    <procedure owner="" name="DelWRFsu_InsertOperationDetail11" grant="">
      <parameters>
        <parameter name="UserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWEquipmentId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWEquipmentName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>INSERT INTO TBL_OperationDetail(UserId,	ObjectId,ObjectType,PropertyName,OperationTime,OperationType,OldValue)
	            VALUES(@UserId, CAST(@SWEquipmentId AS TEXT), 11, '设备名称', NOW(), '删除',@SWEquipmentName)</body>
    </procedure>
    <procedure owner="" name="DelWRFsu_DelMonitorUnitExtend" grant="">
      <parameters>
        <parameter name="SWMonitorUnitId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>DELETE FROM TSL_MonitorUnitExtend WHERE MonitorUnitId = @SWMonitorUnitId</body>
    </procedure>
    <procedure owner="" name="DelWRFsu_DelMonitorUnitCMCC" grant="">
      <parameters>
        <parameter name="SWMonitorUnitId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>DELETE FROM TSL_MonitorUnitCMCC WHERE StationId = @SWStationId AND MonitorUnitId = @SWMonitorUnitId</body>
    </procedure>
    <!-- ================================================================================================ -->
    <procedure owner="" name="DelWRDevice_GetDeviceManagementCount" grant="">
      <parameters>
        <parameter name="WRDeviceId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="UserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT count(*) FROM WR_DeviceManagement WHERE WRDeviceId = @WRDeviceId AND UserId = @UserId</body>
    </procedure>
    <procedure owner="" name="DelWRDevice_GetDeviceInfo" grant="">
      <parameters>
        <parameter name="WRDeviceId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        SELECT a.DeviceCode,b.SWMonitorUnitId,d.SWStationId FROM WR_DeviceManagement a
        INNER JOIN WR_FsuManagement b ON a.WRFsuId = b.WRFsuId
        INNER JOIN WR_HouseManagement c ON b.WRHouseId = c.WRHouseId
        INNER JOIN WR_StationManagement d ON c.WRStationId = d.WRStationId
        WHERE a.WRDeviceId = @WRDeviceId
      </body>
    </procedure>
    <procedure owner="" name="DelWRDevice_GetEquipInfo" grant="">
      <parameters>
        <parameter name="SWStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWMonitorUnitId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="DeviceCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT a.EquipmentId,b.EquipmentName FROM TBL_EquipmentCMCC a 
	              INNER JOIN TBL_Equipment b ON a.StationId = b.StationId AND a.EquipmentId = b.EquipmentId
	          WHERE a.StationId = @SWStationId AND a.MonitorUnitId = @SWMonitorUnitId AND a.DeviceID = @DeviceCode</body>
    </procedure>
    <procedure owner="" name="DelWRDevice_InsertOperationDetail11" grant="">
      <parameters>
        <parameter name="UserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWEquipmentId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWEquipmentName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>INSERT INTO TBL_OperationDetail (UserId,	ObjectId,ObjectType,PropertyName,OperationTime,OperationType,OldValue)
		          VALUES(@UserId,CAST(@SWEquipmentId AS TEXT), 11, '设备名称', now(), '删除',@SWEquipmentName)</body>
    </procedure>
    <procedure owner="" name="DelWRDevice_DelEquipmentCMCC" grant="">
      <parameters>
        <parameter name="SWEquipmentId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWMonitorUnitId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>DELETE FROM TBL_EquipmentCMCC WHERE StationId = @SWStationId AND MonitorUnitId = @SWMonitorUnitId AND EquipmentId = @SWEquipmentId</body>
    </procedure>
    <procedure owner="" name="DelWRDevice_GetWRDeviceInfo" grant="">
      <parameters>
        <parameter name="WRDeviceId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT WRStationId,DeviceName FROM WR_DeviceManagement WHERE WRDeviceId = @WRDeviceId</body>
    </procedure>
    <procedure owner="" name="DelWRDevice_GetStationName" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT StationName FROM WR_StationManagement WHERE WRStationId = @WRStationId</body>
    </procedure>
    <procedure owner="" name="DelWRDevice_DelDeviceManagement" grant="">
      <parameters>
        <parameter name="WRDeviceId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>DELETE FROM WR_DeviceManagement WHERE WRDeviceId = @WRDeviceId</body>
    </procedure>
    <!-- ================================================================================================ -->
    <procedure owner="" name="UpdWRDevice_GetHouseManagementCount" grant="">
      <parameters>
        <parameter name="WRHouseId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT count(*) FROM WR_HouseManagement a WHERE a.WRHouseId = CAST(@WRHouseId AS INTEGER) AND a.HouseStatus != 3</body>
    </procedure>
    <procedure owner="" name="UpdWRDevice_GetHmInfo" grant="">
      <parameters>
        <parameter name="WRHouseId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        SELECT a.WRStationId,b.SWStationId,a.SWHouseId FROM WR_HouseManagement a
        INNER JOIN WR_StationManagement b ON a.WRStationId = b.WRStationId WHERE a.WRHouseId = CAST(@WRHouseId AS INTEGER)
      </body>
    </procedure>
    <procedure owner="" name="UpdWRDevice_GetDeviceManagementCount" grant="">
      <parameters>
        <parameter name="SWStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="WRDeviceId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="DeviceName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT count(*) FROM WR_DeviceManagement b WHERE b.SWStationId = @SWStationId AND b.WRDeviceId != @WRDeviceId AND b.DeviceName = @DeviceName</body>
    </procedure>
    <procedure owner="" name="UpdWRDevice_GetDmInfo" grant="">
      <parameters>
        <parameter name="WRDeviceId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT a.DeviceType,a.DeviceCode FROM WR_DeviceManagement a WHERE a.WRDeviceId = @WRDeviceId</body>
    </procedure>
    <procedure owner="" name="UpdWRDevice_GetAccountInfo" grant="">
      <parameters>
        <parameter name="LogonId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT UserId,UserName FROM TBL_Account WHERE LogonId = @LogonId</body>
    </procedure>
    <procedure owner="" name="UpdWRDevice_GetLastStringInfo" grant="">
      <parameters>
        <parameter name="WRDeviceId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
		<body>SELECT WRHouseId,DeviceType ,DeviceCode ,DeviceName ,UserId ,Remark ,WRFsuId,PortUse,EnableTime,LifeTime FROM WR_DeviceManagement WHERE WRDeviceId = @WRDeviceId</body>
    </procedure>
    <procedure owner="" name="UpdWRDevice_GetStationName" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT StationName FROM WR_StationManagement WHERE WRStationId = @WRStationId</body>
    </procedure>
    <procedure owner="" name="UpdWRDevice_UptDeviceManagementInfo" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="WRHouseId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="DeviceType" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="DeviceCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="DeviceName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="UserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWUserName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWHouseId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Remark" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="WRFsuId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="WRDeviceId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
		<parameter name="PortUse" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
		<parameter name="EnableTime" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
		<parameter name="LifeTime" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        UPDATE WR_DeviceManagement
        SET WRStationId = @WRStationId
        ,WRHouseId = CAST(@WRHouseId AS INTEGER)
        ,DeviceType = @DeviceType
        ,DeviceCode = @DeviceCode
        ,DeviceName = @DeviceName
        ,UserId = @UserId
        ,SWUserName = @SWUserName
        ,SWStationId = @SWStationId
        ,SWHouseId = @SWHouseId
        ,Remark = @Remark
        ,WRFsuId = @WRFsuId
        ,PortUse  = @PortUse
        ,EnableTime = @EnableTime::timestamp
        ,LifeTime  = @LifeTime
        WHERE WRDeviceId = @WRDeviceId</body>
    </procedure>
    <procedure owner="" name="UpdWRDevice_GetSWEquipmentId" grant="">
      <parameters>
        <parameter name="WRDeviceId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT b.EquipmentId FROM WR_DeviceManagement a 
	            INNER JOIN TBL_EquipmentCMCC b ON a.SWStationId = b.StationId AND a.DeviceCode = b.DeviceID 
	          WHERE a.WRDeviceId = @WRDeviceId</body>
    </procedure>
    
    <procedure owner="" name="UpdWRDevice_UptEquipmentInfo" grant="">
      <parameters>
        <parameter name="DeviceName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWHouseId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWEquipmentId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>UPDATE TBL_Equipment SET EquipmentName = @DeviceName, HouseId = @SWHouseId
		          WHERE StationId = @SWStationId AND EquipmentId = @SWEquipmentId</body>
    </procedure>

    <!-- $$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$  2 CUCC $$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$ -->
    <!-- ================================================================================================ -->

    <procedure owner="" name="BslPartSPCUCC_GetUserId" grant="">
      <parameters>
        <parameter name="LogonId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body> SELECT UserId FROM TBL_Account WHERE LogonId = @LogonId </body>
    </procedure>

    <!-- ================================================================================================ -->
    <procedure owner="" name="DelWRDeviceCUCC_GetDeviceManagementCount" grant="">
      <parameters>
        <parameter name="WRDeviceId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="UserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT count(*) FROM WR_DeviceManagementCUCC WHERE WRDeviceId = @WRDeviceId AND UserId = @UserId</body>
    </procedure>
    <procedure owner="" name="DelWRDeviceCUCC_GetDeviceInfo" grant="">
      <parameters>
        <parameter name="WRDeviceId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        SELECT a.DeviceCode,b.SWMonitorUnitId,d.SWStationId FROM WR_DeviceManagementCUCC a
        INNER JOIN WR_FsuManagementCUCC b ON a.WRFsuId = b.WRFsuId
        INNER JOIN WR_HouseManagementCUCC c ON b.WRHouseId = c.WRHouseId
        INNER JOIN WR_StationManagementCUCC d ON c.WRStationId = d.WRStationId
        WHERE a.WRDeviceId = @WRDeviceId
      </body>
    </procedure>
    <procedure owner="" name="DelWRDeviceCUCC_GetEquipInfo" grant="">
      <parameters>
        <parameter name="SWStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWMonitorUnitId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="DeviceCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        SELECT a.EquipmentId,b.EquipmentName FROM TBL_EquipmentCUCC a
        INNER JOIN TBL_Equipment b ON a.StationId = b.StationId AND a.EquipmentId = b.EquipmentId
        WHERE a.StationId = @SWStationId AND a.MonitorUnitId = @SWMonitorUnitId AND a.DeviceID = @DeviceCode
      </body>
    </procedure>
    <procedure owner="" name="DelWRDeviceCUCC_InsertOperationDetail11" grant="">
      <parameters>
        <parameter name="UserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWEquipmentId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWEquipmentName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        INSERT INTO TBL_OperationDetail (UserId,	ObjectId,ObjectType,PropertyName,OperationTime,OperationType,OldValue)
        VALUES(@UserId, CAST(@SWEquipmentId AS TEXT), 11, '设备名称', now(), '删除',@SWEquipmentName)
      </body>
    </procedure>
    <procedure owner="" name="DelWRDeviceCUCC_DelEquipmentCUCC" grant="">
      <parameters>
        <parameter name="SWEquipmentId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWMonitorUnitId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>DELETE FROM TBL_EquipmentCUCC a WHERE a.StationId = @SWStationId AND a.MonitorUnitId =  @SWMonitorUnitId AND a.EquipmentId = @SWEquipmentId</body>
    </procedure>
    <procedure owner="" name="DelWRDeviceCUCC_GetWRDeviceInfo" grant="">
      <parameters>
        <parameter name="WRDeviceId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT WRStationId,DeviceName FROM WR_DeviceManagementCUCC WHERE WRDeviceId = @WRDeviceId</body>
    </procedure>
    <procedure owner="" name="DelWRDeviceCUCC_GetStationName" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT StationName FROM WR_StationManagementCUCC WHERE WRStationId = @WRStationId</body>
    </procedure>
    <procedure owner="" name="DelWRDeviceCUCC_DelDeviceManagement" grant="">
      <parameters>
        <parameter name="WRDeviceId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>DELETE FROM WR_DeviceManagementCUCC WHERE WRDeviceId = @WRDeviceId</body>
    </procedure>

    <!-- ================================================================================================ -->
    <procedure owner="" name="AddWRStationCUCC_GetStationManagementCUCCCount" grant="">
      <parameters>
        <parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT count(*) FROM WR_StationManagementCUCC a WHERE a.StationName = @StationName</body>
    </procedure>
    <procedure owner="" name="AddWRStationCUCC_GetStationCount" grant="">
      <parameters>
        <parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT count(*) FROM TBL_Station b WHERE b.StationName = @StationName</body>
    </procedure>
    <procedure owner="" name="AddWRStationCUCC_GetUserInfo" grant="">
      <parameters>
        <parameter name="LogonId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT  a.UserId,a.UserName FROM TBL_Account a WHERE a.LogonId = @LogonId</body>
    </procedure>
    <procedure owner="" name="AddWRStationCUCC_InsertStationManagementCUCC" grant="">
      <parameters>
        <parameter name="StructureId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationCategory" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationStatus" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Address" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="UserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWUserName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Province" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="City" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="County" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Remark" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ContractNo" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ProjectName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationRId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        INSERT INTO WR_StationManagementCUCC
        (StructureId,StationCode,StationName,StationCategory,StationStatus,Address,UserId, SWUserName,ApplyTime,Province,City,County,Remark,ContractNo,ProjectName,StationRId)
        VALUES
        (@StructureId,@StationCode,@StationName,@StationCategory,@StationStatus, @Address,@UserId, @SWUserName,NOW(),@Province,@City,@County,@Remark,@ContractNo,@ProjectName,@StationRId)
      </body>
    </procedure>
    <procedure owner="" name="AddWRStationCUCC_GetWRStationId" grant="">
      <parameters>
        <parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT WRStationId FROM WR_StationManagementCUCC WHERE StationName = @StationName</body>
    </procedure>

    <!-- ================================================================================================ -->
    <procedure owner="" name="DelWRFsuCUCC_GetFsuManagementCount" grant="">
      <parameters>
        <parameter name="WRFsuId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="UserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body> SELECT count(*) FROM WR_FsuManagementCUCC WHERE WRFsuId = @WRFsuId AND UserId = @UserId </body>
    </procedure>
    <procedure owner="" name="DelWRFsuCUCC_GetFsuInfo" grant="">
      <parameters>
        <parameter name="WRFsuId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        SELECT c.WRStationId, c.SWStationId, a.SWMonitorUnitId ,a.FsuStatus
        FROM WR_FsuManagementCUCC a
        INNER JOIN WR_HouseManagementCUCC b ON a.WRHouseId = b.WRHouseId
        INNER JOIN WR_StationManagementCUCC c ON b.WRStationId = c.WRStationId WHERE a.WRFsuId = @WRFsuId
      </body>
    </procedure>
    <procedure owner="" name="DelWRFsuCUCC_DelFsuManagement" grant="">
      <parameters>
        <parameter name="WRFsuId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>DELETE FROM WR_FsuManagementCUCC WHERE WRFsuId = @WRFsuId</body>
    </procedure>
    <procedure owner="" name="DelWRFsuCUCC_GetMonitorUnitName" grant="">
      <parameters>
        <parameter name="SWMonitorUnitId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body> SELECT MonitorUnitName FROM TSL_MonitorUnit WHERE MonitorUnitId = @SWMonitorUnitId AND StationId = @SWStationId </body>
    </procedure>
    <procedure owner="" name="DelWRFsuCUCC_GetEquipInfo" grant="">
      <parameters>
        <parameter name="SWMonitorUnitId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        SELECT  EquipmentId, EquipmentName FROM TBL_Equipment WHERE StationId = @SWStationId
        AND MonitorUnitId = @SWMonitorUnitId AND EquipmentCategory = 99 and EquipmentType = 2
      </body>
    </procedure>
    <procedure owner="" name="DelWRFsuCUCC_GetWRDeviceIds" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="WRFsuId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT WRDeviceId FROM WR_DeviceManagementCUCC WHERE WRStationId = @WRStationId AND WRFsuId = @WRFsuId</body>
    </procedure>
    <procedure owner="" name="DelWRFsuCUCC_GetFsuName" grant="">
      <parameters>
        <parameter name="WRFsuId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT FsuName FROM WR_FsuManagementCUCC WHERE WRFsuId = @WRFsuId</body>
    </procedure>
    <procedure owner="" name="DelWRFsuCUCC_GetStationName" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT StationName FROM WR_StationManagementCUCC WHERE WRStationId = @WRStationId</body>
    </procedure>
    <procedure owner="" name="DelWRFsuCUCC_DelFsuManagement" grant="">
      <parameters>
        <parameter name="WRFsuId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>DELETE FROM WR_FsuManagementCUCC WHERE WRFsuId = @WRFsuId</body>
    </procedure>
    <procedure owner="" name="DelWRFsuCUCC_InsertOperationDetail6" grant="">
      <parameters>
        <parameter name="UserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWMonitorUnitId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWMonitorUnitName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        INSERT INTO TBL_OperationDetail (UserId,	ObjectId,ObjectType,PropertyName,OperationTime,OperationType,OldValue)
        VALUES(@UserId, CAST(@SWMonitorUnitId AS TEXT), 6, '监控单元名称', NOW(), '删除',@SWMonitorUnitName)
      </body>
    </procedure>
    <procedure owner="" name="DelWRFsuCUCC_InsertOperationDetail11" grant="">
      <parameters>
        <parameter name="UserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWEquipmentId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWEquipmentName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        INSERT INTO TBL_OperationDetail(UserId,	ObjectId,ObjectType,PropertyName,OperationTime,OperationType,OldValue)
        VALUES(@UserId, CAST(@SWEquipmentId AS TEXT), 11, '设备名称', NOW(), '删除',@SWEquipmentName)
      </body>
    </procedure>
    <procedure owner="" name="DelWRFsuCUCC_DelMonitorUnitExtend" grant="">
      <parameters>
        <parameter name="SWMonitorUnitId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>DELETE FROM TSL_MonitorUnitExtend WHERE MonitorUnitId = @SWMonitorUnitId</body>
    </procedure>
    <procedure owner="" name="DelWRFsuCUCC_DelMonitorUnitCUCC" grant="">
      <parameters>
        <parameter name="SWMonitorUnitId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="SWStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>DELETE FROM TSL_MonitorUnitCUCC WHERE StationId = @SWStationId AND MonitorUnitId = @SWMonitorUnitId</body>
    </procedure>

    <!-- ================================================================================================ -->
    <procedure owner="" name="DelStationCUCC_GetUserId" grant="">
      <parameters>
        <parameter name="LogonId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT UserId FROM TBL_Account WHERE LogonId = @LogonId</body>
    </procedure>
    <procedure owner="" name="DelStationCUCC_IsAdminRole" grant="">
      <parameters>
        <parameter name="UserId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        SELECT 'X' FROM TBL_UserRoleMap a INNER JOIN TBL_UserRoleRight b ON a.RoleId = b.RoleId
        WHERE b.OperationId = -1 AND b.OperationType = 1 AND a.UserId = CAST(@UserId AS INTEGER)
        UNION ALL
        SELECT 'X' FROM TBL_UserRoleMap a INNER JOIN TBL_UserRoleRight b ON a.RoleId = b.RoleId
        WHERE b.OperationId = -1 AND b.OperationType = 2 AND a.UserId = CAST(@UserId AS INTEGER)
        UNION ALL
        SELECT 'X' FROM TBL_UserRoleMap a INNER JOIN TBL_UserRoleRight b ON a.RoleId = b.RoleId
        WHERE b.OperationId = -1 AND b.OperationType = 3 AND a.UserId = CAST(@UserId AS INTEGER)
        UNION ALL
        SELECT 'X' FROM TBL_UserRoleMap a INNER JOIN TBL_UserRoleRight b ON a.RoleId = b.RoleId
        WHERE b.OperationId = -1 AND b.OperationType = 4 AND a.UserId = CAST(@UserId AS INTEGER)
      </body>
    </procedure>
    <procedure owner="" name="DelStationCUCC_GetStationInfo" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT a.SWStationId,a.StationStatus FROM WR_StationManagementCUCC a WHERE a.WRStationId = @WRStationId</body>
    </procedure>
    <procedure owner="" name="DelStationCUCC_GetFsuUnderStationInfo" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        SELECT c.WRFsuId FROM WR_StationManagementCUCC a
        INNER JOIN WR_HouseManagementCUCC b ON a.WRStationId = b.WRStationId
        INNER JOIN WR_FsuManagementCUCC c ON b.WRHouseId = c.WRHouseId
        WHERE a.WRStationId = @WRStationId
      </body>
    </procedure>
    <procedure owner="" name="DelStationCUCC_GetHouseUnderStationInfo" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        SELECT b.WRHouseId FROM WR_StationManagementCUCC a INNER JOIN WR_HouseManagementCUCC b ON a.WRStationId = b.WRStationId
        WHERE a.WRStationId = @WRStationId
      </body>
    </procedure>
    <procedure owner="" name="DelStationCUCC_GetStationName" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT a.StationName FROM WR_StationManagementCUCC a WHERE a.WRStationId = @WRStationId</body>
    </procedure>
    <procedure owner="" name="DelStationCUCC_DelStationManagementCUCC" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>DELETE FROM WR_StationManagementCUCC WHERE WRStationId = @WRStationId</body>
    </procedure>
    <procedure owner="" name="DelStationCUCC_DeleteMonitorUnitCUCC" grant="">
      <parameters>
        <parameter name="SWStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>DELETE FROM TSL_MonitorUnitCUCC WHERE StationId = @SWStationId</body>
    </procedure>
    <procedure owner="" name="DelStationCUCC_DelEquipmentCUCC" grant="">
      <parameters>
        <parameter name="SWStationId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>DELETE FROM TBL_EquipmentCUCC WHERE StationId = @SWStationId</body>
    </procedure>

    <!-- ================================================================================================ -->
    <procedure owner="" name="GetWRFsuCUCC_GetRoleMapCount" grant="">
      <parameters>
        <parameter name="iUserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT count(*) FROM TBL_UserRoleMap WHERE UserId = @iUserId AND RoleId = -1</body>
    </procedure>
    <procedure owner="" name="GetWRFsuCUCC_GetFilterUserInnerSqlAdmin" grant="">
      <body>SELECT DISTINCT UserId FROM WR_FsuManagementCUCC</body>
    </procedure>
    <procedure owner="" name="GetWRFsuCUCC_GetFilterUserInnerSqlNotAdmin" grant="">
      <parameters>
        <parameter name="iUserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        SELECT DISTINCT UserId FROM TBL_UserRoleMap WHERE RoleId IN
        (SELECT RoleId FROM TBL_UserRoleMap WHERE UserId = @iUserId)
      </body>
    </procedure>
    <procedure owner="" name="GetWRFsuCUCC_GetResultDt1" grant="">
      <parameters>
        <parameter name="StartTime" type="datetime" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EndTime" type="datetime" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FsuName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FsuCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ManufacturerId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StructureIdStr" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Status" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="iUserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
        <replace keyName ="WhereStartTime"> and a.ApplyTime >= @StartTime </replace>
        <replace keyName ="WhereEndTime"><![CDATA[ and a.ApplyTime <= @EndTime ]]></replace>
        <replace keyName ="WhereSWFsuName"> and j.SUName like concat('%',@FsuName,'%') </replace>
        <replace keyName ="WhereSWFsuCode"> and j.SUID like concat('%',@FsuCode,'%') </replace>
        <replace keyName ="WhereManufactureId"> and a.ManufacturerId = @ManufacturerId </replace>
        <replace keyName ="WhereStructure"> and d.LevelPath LIKE concat('%',@StructureIdStr,'%') </replace>
        <replace keyName ="WhereStatus"> and a.FsuStatus = @Status </replace>
      </replaces>
      <body>
        SELECT 1 RowNumber,
        a.WRFsuId,
        d.StructureName,
        c.StationCode,h.StationName,
        a.WRHouseId,i.HouseName,
        j.SUID FsuCode,j.SUName FsuName,j.SUIP IPAddress,
        a.ManufacturerId,e.ItemValue ManufactureName,
        a.FsuStatus,f.ItemValue StatusName,
        a.UserId, a.SWUserName UserName,
        j.UserName LoginName, j.PassWord,
        j.FTPUserName FtpUserName,j.FTPPassWord FtpPassword,
        a.ApplyTime,
        a.ApproveTime,
        a.RejectCause,
        a.Remark,
        a.ContractNo,
        a.ProjectName,
        a.FsuRId
        FROM WR_FsuManagementCUCC a
        INNER JOIN WR_HouseManagementCUCC b ON a.WRHouseId = b.WRHouseId
        INNER JOIN WR_StationManagementCUCC c ON b.WRStationId = c.WRStationId
        INNER JOIN TBL_StationStructure d ON c.StructureId = d.StructureId
        LEFT JOIN WR_DataItem e ON a.ManufacturerId = e.ItemId AND e.EntryId=4
        LEFT JOIN WR_DataItem f ON a.FsuStatus = f.ItemId AND f.EntryId = 5
        INNER JOIN TBL_Station h ON c.SWStationId = h.StationId
        INNER JOIN TBL_House i ON c.SWStationId = i.StationId AND b.SWHouseId = i.HouseId
        INNER JOIN TSL_MonitorUnitCUCC j ON a.SWMonitorUnitId = j.MonitorUnitId AND h.StationId = j.StationId
        INNER JOIN ($[filterUserInnerSql]) k ON a.UserId = k.UserId
        WHERE a.FsuStatus = 3 $[WhereStartTime] $[WhereEndTime] $[WhereSWFsuName] $[WhereSWFsuCode] $[WhereManufactureId] $[WhereStructure] $[WhereStatus]
      </body>
    </procedure>
    <procedure owner="" name="GetWRFsuCUCC_GetResultDt2" grant="">
      <parameters>
        <parameter name="StartTime" type="datetime" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EndTime" type="datetime" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FsuName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="FsuCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ManufacturerId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StructureIdStr" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="Status" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="iUserId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
        <replace keyName ="WhereStartTime"> and a.ApplyTime >= @StartTime </replace>
        <replace keyName ="WhereEndTime"><![CDATA[ and a.ApplyTime <= @EndTime ]]></replace>
        <replace keyName ="WhereFsuName"> and a.FsuName like concat('%',@FsuName,'%') </replace>
        <replace keyName ="WhereFsuCode"> and a.FsuCode like concat('%',@FsuCode,'%') </replace>
        <replace keyName ="WhereManufactureId"> and a.ManufacturerId = @ManufacturerId </replace>
        <replace keyName ="WhereStructure"> and d.LevelPath LIKE concat('%',@StructureIdStr,'%') </replace>
        <replace keyName ="WhereStatus"> and a.FsuStatus = @Status </replace>
      </replaces>
      <body>
        SELECT 1 RowNumber,
        a.WRFsuId,
        d.StructureName,
        c.StationCode,c.StationName,
        a.WRHouseId,b.HouseName,
        a.FsuCode,a.FsuName,a.IPAddress,
        a.ManufacturerId,e.ItemValue ManufactureName,
        a.FsuStatus,f.ItemValue StatusName,
        a.UserId, a.SWUserName UserName,
        a.UserName LoginName,a.Password,
        a.FtpUserName,a.FtpPassword,
        a.ApplyTime,
        a.ApproveTime,
        a.RejectCause,
        a.Remark,
        a.ContractNo,
        a.ProjectName,
        a.FsuRId
        FROM WR_FsuManagementCUCC a
        INNER JOIN WR_HouseManagementCUCC b ON a.WRHouseId = b.WRHouseId
        INNER JOIN WR_StationManagementCUCC c ON b.WRStationId = c.WRStationId
        INNER JOIN TBL_StationStructure d ON c.StructureId = d.StructureId
        LEFT JOIN WR_DataItem e ON a.ManufacturerId = e.ItemId AND e.EntryId=4
        LEFT JOIN WR_DataItem f ON a.FsuStatus = f.ItemId AND f.EntryId = 5
        INNER JOIN ($[filterUserInnerSql]) k ON a.UserId = k.UserId
        WHERE a.FsuStatus != 3 $[WhereStartTime] $[WhereEndTime] $[WhereFsuName] $[WhereFsuCode] $[WhereManufactureId] $[WhereStructure] $[WhereStatus]
      </body>
    </procedure>
    <procedure owner="" name="GetWRFsuCUCC_GetResultDt_OrderBy" grant="">
      <body>ApplyTime DESC</body>
    </procedure>

    
    
	</procedures>
</root>
