﻿using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace Delivery.Common.NoProcedure.Service
{
    public class BM_DeviceConfigService
    {
        private static BM_DeviceConfigService _instance = null;

        public static BM_DeviceConfigService Instance
        {
            get
            {
                if (_instance == null) _instance = new BM_DeviceConfigService();
                return _instance;
            }
        }
        public int BM_DeviceConfigDel(string FSUID, string DeviceID, int StationId, int EquipmentId, int EquipmentTemplateId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper,true);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                string EqtConfigId = StationId + "." + EquipmentId;
                realParams.Add("FSUID", FSUID);
                realParams.Add("DeviceID", DeviceID);
                realParams.Add("StationId", StationId);
                realParams.Add("EquipmentId", EquipmentId);
                realParams.Add("EquipmentTemplateId", EquipmentTemplateId);
                realParams.Add("EqtConfigId", EqtConfigId);
                try
                {
                    
                    try
                    {
                        executeHelper.ExecuteNonQuery("BM_DeviceConfigDel_Signal",realParams);
                    }catch (Exception ex)
                    {
                        executeHelper.Rollback();
                        Logger.Log(ex);
                        return - 1;
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("BM_DeviceConfigDel_SignalProperty", realParams);
                    }catch(Exception ex)
                    {
                        Logger.Log(ex);
                        executeHelper.Rollback();
                        return -1;
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("BM_DeviceConfigDel_SignalMeanings", realParams);
                    }
                    catch (Exception ex)
                    {
                        Logger.Log(ex);
                        executeHelper.Rollback();
                        return -1;
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("BM_DeviceConfigDel_Control", realParams);
                    }
                    catch (Exception ex)
                    {
                        Logger.Log(ex);
                        executeHelper.Rollback();
                        return -1;
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("BM_DeviceConfigDel_ControlMeanings", realParams);
                    }
                    catch (Exception ex)
                    {
                        Logger.Log(ex);
                        executeHelper.Rollback();
                        return -1;
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("BM_DeviceConfigDel_Event", realParams);
                    }
                    catch (Exception ex)
                    {
                        Logger.Log(ex);
                        executeHelper.Rollback();
                        return -1;
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("BM_DeviceConfigDel_EventCondition", realParams);
                    }
                    catch (Exception ex)
                    {
                        Logger.Log(ex);
                        executeHelper.Rollback();
                        return -1;
                    }
                    PblConfigChangeLogService.Instance.DoExecute(EquipmentTemplateId.ToString(), 6, 3);
                    try
                    {
                        executeHelper.ExecuteNonQuery("BM_DeviceConfigDel_EquipmentTemplate", realParams);
                    }
                    catch (Exception ex)
                    {
                        Logger.Log(ex);
                        executeHelper.Rollback();
                        return -1;
                    }
                    PblConfigChangeLogService.Instance.DoExecute(EqtConfigId, 6, 3);
                    try
                    {
                        executeHelper.ExecuteNonQuery("BM_DeviceConfigDel_Equipment", realParams);
                    }
                    catch (Exception ex)
                    {
                        Logger.Log(ex);
                        executeHelper.Rollback();
                        return -1;
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("BM_DeviceConfigDel_EquipmentCMCC", realParams);
                    }
                    catch (Exception ex)
                    {
                        Logger.Log(ex);
                        executeHelper.Rollback();
                        return -1;
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("BM_DeviceConfigDel_FsuTSignalCMCC", realParams);
                    }
                    catch (Exception ex)
                    {
                        Logger.Log(ex);
                        executeHelper.Rollback();
                        return -1;
                    }
                    executeHelper.Commit();
                    return 0;

                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    return -1;
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
        public int BM_DeviceConfigUpdate(string FSUID, string DeviceID, string DeviceName, string RoomName, int DeviceType, 
                           int DeviceSubType, string Model, string Brand,float RatedCapacity,string Version, string BeginRunTime,
                           string DevDescribe,int StationId, int HouseId,int? MonitorUnitId,int? SamplerUnitId, int EquipmentId,
                           int EquipmentTemplateId,int EquipmentCategory)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    if(string.IsNullOrEmpty(DeviceName))
                    {
                        DeviceName = "";
                    }
                    if(string.IsNullOrEmpty(FSUID))
                    {
                        FSUID = "";
                    }
                    realParams.Add("FSUID", FSUID);
                    realParams.Add("DeviceID", DeviceID);
                    realParams.Add("DeviceName", DeviceName);
                    realParams.Add("RoomName", RoomName);
                    realParams.Add("DeviceType", DeviceType);
                    realParams.Add("DeviceSubType", DeviceSubType);
                    realParams.Add("Model", Model);
                    realParams.Add("Brand", Brand);
                    realParams.Add("RatedCapacity", RatedCapacity);
                    realParams.Add("Version", Version);
                    realParams.Add("BeginRunTime", BeginRunTime);
                    realParams.Add("DevDescribe", DevDescribe);
                    realParams.Add("StationId", StationId);
                    realParams.Add("HouseId", HouseId);
                    realParams.Add("MonitorUnitId", MonitorUnitId);
                    realParams.Add("SamplerUnitId", SamplerUnitId);
                    realParams.Add("EquipmentId", EquipmentId);
                    realParams.Add("EquipmentTemplateId", EquipmentTemplateId);
                    realParams.Add("EquipmentCategory", EquipmentCategory);
                    string EquipmentTemplateName = "B接口_" + DeviceName + "_" + FSUID;
                    realParams.Add("EquipmentTemplateName", EquipmentTemplateName);
                    try
                    {
                        executeHelper.ExecuteNonQuery("BM_DeviceConfigUpdate_EquipmentTemplate", realParams);
                    }catch (Exception ex)
                    {
                        executeHelper.Rollback();
                        Logger.Log(ex);
                        return -1;
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("BM_DeviceConfigUpdate_Equipment", realParams);
                    }
                    catch (Exception ex)
                    {
                        executeHelper.Rollback();
                        Logger.Log(ex);
                        return -1;
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("BM_DeviceConfigUpdate_EquipmentCMCC", realParams);
                    }
                    catch (Exception ex)
                    {
                        executeHelper.Rollback();
                        Logger.Log(ex);
                        return -1;
                    }
                    executeHelper.Commit();
                    return 0;

                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    return -1;
                }finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
    }
}
