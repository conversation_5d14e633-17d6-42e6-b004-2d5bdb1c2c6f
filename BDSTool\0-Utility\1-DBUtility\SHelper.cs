﻿
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace BDSTool.DBUtility
{
    public class SHelper
    {

        //to do
        public static DateTime GetProcPara(DateTime? para)
        {
            if (para == null)
                return  new DateTime(1970,1,1);

            return DateTime.Parse(para.Value.ToString("yyyy-MM-dd HH:mm:ss"));
        }


        public static string GetProcPara(string para)
        {
            if (string.IsNullOrEmpty(para))
                return "";
            else
            {
                if (para.IndexOf('\'') != -1)
                {
                    //int i = 1;
                    para = para.Replace("'", "");
                }
                return para;
            }

        }

        public static double GetProcPara(double? para)
        {
            if (!para.HasValue)
                return -1;
            else
                return double.Parse(para.ToString());
        }

        public static decimal GetProcPara(decimal? para)
        {
            if (!para.HasValue)
                return -1;
            else
                return decimal.Parse(para.ToString());
        }


        public static float GetProcPara(float? para)
        {
            if (!para.HasValue)
                return -1f;
            else
                return float.Parse(para.ToString());
        }

        public static long GetProcPara(long? para)
        {
            if (!para.HasValue)
                return -1;
            else
                return long.Parse(para.ToString());
        }

        public static int GetProcPara(int? para)
        {
            if (!para.HasValue)
                return -1;
            else
                return int.Parse(para.ToString());
        }

        public static string GetPara(bool para) {
            return para==true?"1":"0";
        }

        //to do
        public static string GetPara(DateTime? para) {
            if(para==null)
                return "NULL";

            return "'" + para.Value.ToString("yyyy-MM-dd HH:mm:ss") + "'";
        }


        public static string GetPara(string para)
        {
            if (string.IsNullOrEmpty(para))
                return "''";
            else
            {
                if (para.IndexOf('\'') != -1)
                {
                    //int i = 1;
                    para = para.Replace("'", "''");
                }
                return "'" + para + "'";
            }

        }
        
        public static string GetPara(double? para) {
            if (!para.HasValue)
                return "NULL";
            else
                return para.ToString();
        }

        public static string GetPara(decimal? para) {
            if (!para.HasValue)
                return "NULL";
            else
                return para.ToString();
        }


        public static string GetPara(float? para) {
            if (!para.HasValue)
                return "NULL";
            else
                return para.ToString();
        }

        public static string GetPara(long? para) {
            if (!para.HasValue)
                return "NULL";
            else
                return para.ToString();
        }

        public static string GetPara(int? para) {
            if (!para.HasValue)
                return "NULL";
            else
                return para.ToString();
        }

        public static EnumType ToEnumType(object o) {
            if (o == null || o == DBNull.Value)
                return EnumType.INVALID;

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return EnumType.INVALID;

            return (EnumType)int.Parse(s);
        }

        public static EnumState ToEnumState(object o) {
            if (o == null || o == DBNull.Value)
                return EnumState.NOALARM;

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return EnumState.NOALARM;

            return (EnumState)int.Parse(s);
        }

        public static int ToInt(object o) {
            if (o == null || o == DBNull.Value)
                return 0;

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return 0;
            return int.Parse(s);
        }
        public static float ToFloat(object o) {
            if(o==null || o == DBNull.Value )
                return 0;

            var s=o.ToString();

            if(string.IsNullOrEmpty(s))
                return 0;
            return float.Parse(s);
        }

        public static DateTime ToDateTime(object o) {
            if (o == null || o == DBNull.Value)
                return DateTime.Parse("1970-01-01 00:00:00");

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return DateTime.Parse("1970-01-01 00:00:00");

            return DateTime.Parse(s);
        }
        #region can be null
        public static int? ToIntNullable(object o) {
            if (o == null || o == DBNull.Value)
                return null;

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return null;

            return int.Parse(s);
        }

        public static float? ToFloatNullable(object o) {
            if (o == null || o == DBNull.Value)
                return null;

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return null;

            return float.Parse(s);
        }

        public static double? ToDoubleNullable(object o) {
            if (o == null || o == DBNull.Value)
                return null;

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return null;

            return double.Parse(s);
        }

        public static DateTime? ToDateTimeNullable(object o) {
            if (o == null || o == DBNull.Value)
                return null;

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return null;

            return DateTime.Parse(s);
        }

        public static bool ToBool(object o) {
            if (o == null || o == DBNull.Value)
                return false;

            var s = o.ToString();

            if (string.IsNullOrEmpty(s))
                return false;

            if (s.ToUpper() == "TRUE")
                return true;
            else
                return false;
        }
        #endregion
    }
}
