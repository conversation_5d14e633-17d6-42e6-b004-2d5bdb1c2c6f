﻿


using BDSTool.DBUtility.Common;
using Common.Logging.Pro;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
namespace BDSTool.BLL.S2
{
    public class GlobalIdentityBiz
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");
        public static bool GetNewId(string tableName, ref int Id) {
            try {
                var sql = string.Format("PBL_GenerateId '{0}'", tableName);

                //var rtn=DBHelper.ExecuteScalar(sql);
                object rtn = null;
                try
                {
                    if (CommonUtils.IsNoProcedure)
                    {
                        rtn = Public_StoredService.Instance.PBL_GenerateId(tableName,0);
                    }
                    else
                    {
                        using (var dbHelper = new DatabaseHelper(DBHelper.GetDbConfig()))
                        {
                            dbHelper.CreateProcedureCommand("PBL_GenerateId");
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("TableName", DbType.AnsiString, tableName));
                            dbHelper.AddDbParameter(dbHelper.GetDbParameter("PostalCode", DbType.Int32, 0));
                            dbHelper.AddDbParameter(dbHelper.GetReturnDbParameter());
                            rtn = dbHelper.ExecuteScalar();
                        }
                    }

                }
                catch (Exception ex)
                {
                    DBHelper.LogError(sql, ex);
                }

                if (rtn==null)
                    return false;
                
                Id= int.Parse(rtn.ToString());
            }
            catch (Exception ex) {
                logger.ErrorFormat("GetNewId();tableName={0};Error={1}", tableName, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }
    }
}
