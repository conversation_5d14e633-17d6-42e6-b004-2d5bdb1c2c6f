﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_GetSomeFiledName1" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT a.WRStationId,b.SWStationId,a.HouseCode,b.StationCode ,a.HouseName ,b.StationName
					FROM WR_HouseManagement a 
					INNER JOIN WR_StationManagement b ON a.WRStationId = b.WRStationId
					WHERE a.WRHouseId = :WRHouseId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_GetSomeFiledName2" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT  WR_FsuManagement.FsuCode AS OriFsuCode 
					FROM WR_FsuManagement WHERE WR_FsuManagement.WRFsuId = :WRFsuId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_JudgeExist1" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM WR_FsuManagement 
					WHERE WR_FsuManagement.WRFsuId != :WRFsuId AND WR_FsuManagement.FsuCode = :FsuCode limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_JudgeExist2" grant="">
			<parameters>
				<parameter name="WRStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM WR_FsuManagement a 
					INNER JOIN WR_HouseManagement b ON a.WRHouseId = b.WRHouseId 
					WHERE b.WRStationId = :WRStationId AND a.WRFsuId != :WRFsuId AND a.FsuName = :FsuName limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_JudgeExist3" grant="">
			<parameters>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="OriFsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM TSL_MonitorUnitCMCC WHERE TSL_MonitorUnitCMCC.StationId = :SWStationId 
					AND TSL_MonitorUnitCMCC.FSUID != :OriFsuCode AND TSL_MonitorUnitCMCC.FSUName = :FsuName limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_JudgeExist4" grant="">
			<parameters>
				<parameter name="ManufacturerId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM WR_DataItem 
					WHERE WR_DataItem.EntryId = 4 AND WR_DataItem.ItemId = :ManufacturerId limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_GetSomeFiledName3" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT a.SWMonitorUnitId  FROM WR_FsuManagement a WHERE a.WRFsuId = :WRFsuId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_JudgeExist5" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM WR_FsuManagement WHERE WR_FsuManagement.WRFsuId != :WRFsuId AND WR_FsuManagement.IPAddress = :IPAddress 
					AND WR_FsuManagement.IPAddress != '' AND WR_FsuManagement.IPAddress IS NOT NULL limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_JudgeExist6" grant="">
			<parameters>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM TSL_MonitorUnitCMCC WHERE TSL_MonitorUnitCMCC.FSUIP = :IPAddress AND TSL_MonitorUnitCMCC.MonitorUnitId != :SWMonitorUnitId  
					AND TSL_MonitorUnitCMCC.FSUIP != '' AND TSL_MonitorUnitCMCC.FSUIP IS NOT NULL limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_GetSomeFiledName4" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT  WR_FsuManagement.FsuStatus 
					FROM WR_FsuManagement 
					WHERE WR_FsuManagement.WRFsuId = :WRFsuId;	
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_GetSomeFiledName5" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT  CONCAT('FsuCode:' , CAST(FsuCode AS VARCHAR) ,
	    					'-FsuName:' , CAST(FsuName AS VARCHAR) ,
	    					'-IPAddress:' , CAST(IPAddress AS VARCHAR) ,
	    					'-UserName:' , CAST(UserName AS VARCHAR) ,
	    					'-IPAddress:' , CAST(IPAddress AS VARCHAR) ,
	    					'-Password:' , CAST(Password AS VARCHAR) ,
	    					'-FtpUserName:' , CAST(FtpUserName AS VARCHAR) ,
	    					'-FtpPassword:' , CAST(FtpPassword AS VARCHAR) ,
	    					'-Remark:' , CAST(Remark AS VARCHAR)) 
	    					FROM WR_FsuManagement WHERE WR_FsuManagement.WRFsuId = :WRFsuId limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_UpdateStatusNotThree" grant="">
			<parameters>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRHouseId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ManufacturerId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="UserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Password" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpUserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpPassword" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Remark" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ContractNo" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ProjectName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					
					UPDATE WR_FsuManagement
					SET WR_FsuManagement.WRHouseId = :WRHouseId
						,WR_FsuManagement.FsuCode = :FsuCode
						,WR_FsuManagement.FsuName = :FsuName
						,WR_FsuManagement.IPAddress = :IPAddress
						,WR_FsuManagement.ManufacturerId = :ManufacturerId
						,WR_FsuManagement.FsuStatus = 1 -- 设为申请入网
						,WR_FsuManagement.UserName = :UserName 
						,WR_FsuManagement.Password = :Password
						,WR_FsuManagement.FtpUserName = :FtpUserName
						,WR_FsuManagement.FtpPassword = :FtpPassword
						,WR_FsuManagement.Remark = :Remark 
						,WR_FsuManagement.ContractNo = :ContractNo
						,WR_FsuManagement.ProjectName = :ProjectName
					WHERE WR_FsuManagement.WRFsuId = :WRFsuId ;	
					
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_UpdateStatusIsThree" grant="">
			<parameters>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRHouseId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ManufacturerId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="UserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Password" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpUserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpPassword" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Remark" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ContractNo" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ProjectName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE WR_FsuManagement
					SET WR_FsuManagement.WRHouseId = :WRHouseId
						,WR_FsuManagement.FsuCode = :FsuCode
						,WR_FsuManagement.FsuName = :FsuName
						,WR_FsuManagement.IPAddress = :IPAddress
						,WR_FsuManagement.ManufacturerId = :ManufacturerId
						,WR_FsuManagement.UserName = :UserName 
						,WR_FsuManagement.Password = :Password
						,WR_FsuManagement.FtpUserName = :FtpUserName
						,WR_FsuManagement.FtpPassword = :FtpPassword
						,WR_FsuManagement.ContractNo = :ContractNo
						,WR_FsuManagement.ProjectName = :ProjectName
						,WR_FsuManagement.Remark = :Remark 
					WHERE WR_FsuManagement.WRFsuId = :WRFsuId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_UpdateTSL_MonitorUnit" grant="">
			<parameters>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TSL_MonitorUnit
					SET TSL_MonitorUnit.MonitorUnitName = :FsuName
						,TSL_MonitorUnit.IpAddress = :IPAddress
					WHERE TSL_MonitorUnit.MonitorUnitId = :SWMonitorUnitId 
						AND TSL_MonitorUnit.StationId = :SWStationId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_UpdateTSL_MonitorUnitCMCC" grant="">
			<parameters>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="StationName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="StationCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="HouseCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="HouseName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="UserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Password" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpUserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpPassword" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TSL_MonitorUnitCMCC
					SET TSL_MonitorUnitCMCC.FSUID = :FsuCode
						,TSL_MonitorUnitCMCC.FSUName = :FsuName
						,TSL_MonitorUnitCMCC.SiteID = :StationCode
						,TSL_MonitorUnitCMCC.SiteName = :StationName
						,TSL_MonitorUnitCMCC.RoomID = :HouseCode
						,TSL_MonitorUnitCMCC.RoomName = :HouseName
						,TSL_MonitorUnitCMCC.FSUIP = :IPAddress
						,TSL_MonitorUnitCMCC.UserName = :UserName 
						,TSL_MonitorUnitCMCC.Password = :Password
						,TSL_MonitorUnitCMCC.FtpUserName = :FtpUserName
						,TSL_MonitorUnitCMCC.FtpPassword = :FtpPassword
					WHERE TSL_MonitorUnitCMCC.StationId = :SWStationId 
						AND TSL_MonitorUnitCMCC.MonitorUnitId = :SWMonitorUnitId ;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_GetBEquipmentInfo" grant="">
			<body>
				<![CDATA[ 
					SELECT  EquipmentTemplateId  FROM TBL_EquipmentTemplate 
					WHERE TBL_EquipmentTemplate.EquipmentTemplateName = 'BInterface-HOST设备' limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_UpdateTBL_Equipment" grant="">
			<parameters>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="BInterfaceHostT" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TBL_Equipment SET TBL_Equipment.EquipmentName = concat('BInterface自诊断设备-' , :FsuName)
					WHERE TBL_Equipment.EquipmentCategory = 99 AND TBL_Equipment.EquipmentTemplateId = :BInterfaceHostT
						AND TBL_Equipment.MonitorUnitId =  :SWMonitorUnitId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_UpdateTBL_MonitorUnitProjectInfo" grant="">
			<parameters>
				<parameter name="ContractNo" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ProjectName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TBL_MonitorUnitProjectInfo SET TBL_MonitorUnitProjectInfo.ContractNo = :ContractNo, TBL_MonitorUnitProjectInfo.ProjectName = :ProjectName
					WHERE TBL_MonitorUnitProjectInfo.MonitorUnitId = :SWMonitorUnitId ;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsu_UpdateTBL_ConfigChangeMicroLog" grant="">
			<parameters>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TBL_ConfigChangeMicroLog
					SET TBL_ConfigChangeMicroLog.EditType = 2, TBL_ConfigChangeMicroLog.UpdateTime = NOW()
					WHERE TBL_ConfigChangeMicroLog.ObjectId = concat(CAST( :SWStationId AS VARCHAR),'.',CAST(:SWMonitorUnitId AS VARCHAR))  AND 
					TBL_ConfigChangeMicroLog.ConfigId = 15;
				]]>
			</body>
		</procedure>






		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_GetSomeFiledName1" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT a.FsuStatus, a.FsuName,c.SWStationId, c.StationStatus,b.HouseStatus 
					FROM WR_FsuManagement a 
					INNER JOIN WR_HouseManagement b ON a.WRHouseId = b.WRHouseId
					INNER JOIN WR_StationManagement c ON b.WRStationId = c.WRStationId
					WHERE a.WRFsuId = :WRFsuId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTSLMonitorUnit" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body> 
				<![CDATA[ 
					INSERT INTO TSL_MonitorUnit
					(
						MonitorUnitId,MonitorUnitName,MonitorUnitCategory,MonitorUnitCode,
						StationId,IpAddress,RunMode,ConnectState,UpdateTime,
						IsSync,IsConfigOK,AppCongfigId,CanDistribute, ENABLE, InstallTime
					)
					SELECT  :SWMonitorUnitId,:FsuName ,10 MonitorUnitCategory, TO_CHAR(:SWMonitorUnitId) MonitorUnitCode,
						c.SWStationId,a.IPAddress, 1 RunMode, 2 ConnectState, NOW() UpdateTime,
						0 IsSync, 0 IsConfigOK, 1 AppCongfigId, 1 CanDistribute,  1 ENABLE ,a.ApplyTime
					FROM WR_FsuManagement a
					INNER JOIN WR_HouseManagement b ON a.WRHouseId = b.WRHouseId 
					INNER JOIN WR_StationManagement c ON b.WRStationId = c.WRStationId
					WHERE a.WRFsuId = :WRFsuId
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTSLMonitorUnitCMCC" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TSL_MonitorUnitCMCC
					( StationId,MonitorUnitId,FSUID,FSUName,SiteID,SiteName,RoomID,RoomName,
					UserName,PassWord,FSUIP,FTPUserName,FTPPassWord,GetConfigFlag 
					)
					SELECT c.SWStationId, :SWMonitorUnitId, a.FsuCode, a.FsuName,c.StationCode,c.StationName, b.HouseCode,b.HouseName, 
						a.UserName, a.Password, a.IPAddress, a.FtpUserName, a.FtpPassword, 0 GetConfigFlag		
					FROM WR_FsuManagement a
					INNER JOIN WR_HouseManagement b ON a.WRHouseId = b.WRHouseId
					INNER JOIN WR_StationManagement c ON b.WRStationId = c.WRStationId
					WHERE a.WRFsuId = :WRFsuId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTSLPort" grant="">
			<parameters>
				<parameter name="SWPortId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TSL_Port(PortId,MonitorUnitId,PortNo,PortName,PortType,Setting,PhoneNumber,LinkSamplerUnitId,Description)
					VALUES (:SWPortId, :SWMonitorUnitId,5,'COM5',5,'comm_host_dev.so','',0,'');
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_GetSomeFiledName2" grant="">
			<body>
				<![CDATA[ 
					SELECT a.SamplerId as SWSamplerId, a.SamplerName as SamplerUnitName, a.DllPath as swDllPath   
					FROM TSL_Sampler a
					WHERE a.SamplerType = 18 AND a.SamplerName LIKE '%BInterface-HOST%' limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTSLSamplerUnit" grant="">
			<parameters>
				<parameter name="SWSamplerUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWPortId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWSamplerId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SamplerUnitName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="swDllPath" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TSL_SamplerUnit
					(SamplerUnitId,PortId,MonitorUnitId,SamplerId,ParentSamplerUnitId,SamplerType,SamplerUnitName,Address,SpUnitInterval,DllPath,ConnectState,UpdateTime)
					VALUES 
					(:SWSamplerUnitId, :SWPortId, :SWMonitorUnitId, :SWSamplerId,0,18, :SamplerUnitName,1,2, :swDllPath,0,NOW());	 
				]]>
			</body>
		</procedure>

		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_GetEquTemplateId" grant="">
			<body>
				<![CDATA[ 
					SELECT  EquipmentTemplateId as swEquipmentTemplateId  FROM TBL_EquipmentTemplate 
					WHERE TBL_EquipmentTemplate.EquipmentCategory = 99 AND TBL_EquipmentTemplate.EquipmentTemplateName = 'BInterface-HOST设备' ;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTBLEquipment" grant="">
			<parameters>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="swEquipmentId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="swEquipmentName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="swEquipmentTemplateId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWSamplerUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TBL_Equipment (StationId, EquipmentId, EquipmentName,EquipmentNo, EquipmentCategory, EquipmentType, EquipmentClass, 
							EquipmentState, Property, EquipmentTemplateId, HouseId, MonitorUnitId, SamplerUnitId, DisplayIndex, ConnectState, UpdateTime,InstalledModule)
					VALUES (:SWStationId, :swEquipmentId, :swEquipmentName,'', 99, 2, -1, 1, '',:swEquipmentTemplateId, 1, :SWMonitorUnitId, :SWSamplerUnitId, 0, 2, now(),'');	
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_UpdateWRFsuManagement" grant="">
			<parameters>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
   					UPDATE WR_FsuManagement
   					SET WR_FsuManagement.FsuStatus = 3
   					,WR_FsuManagement.ApproveTime = now()
   					,WR_FsuManagement.SWMonitorUnitId = :SWMonitorUnitId
   					,WR_FsuManagement.RejectCause = '' -- 同时清除退回原因
   					WHERE WR_FsuManagement.WRFsuId = :WRFsuId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTBLConfigChangeMicroLog1" grant="">
			<parameters>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TBL_ConfigChangeMicroLog(ObjectId,ConfigId,EditType,UpdateTime)
					SELECT concat(TO_CHAR(:SWStationId),'.',TO_CHAR(:SWMonitorUnitId))  ObjectId, 15 ConfigId, 1 EditType, NOW() UpdateTime;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTBLConfigChangeMicroLog2" grant="">
			<parameters>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWPortId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TBL_ConfigChangeMicroLog(ObjectId,ConfigId,EditType,UpdateTime)
					SELECT concat(TO_CHAR(:SWMonitorUnitId),'.',TO_CHAR(:SWPortId))  ObjectId, 16 ConfigId, 1 EditType, NOW() UpdateTime;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTBLConfigChangeMicroLog3" grant="">
			<parameters>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWPortId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWSamplerUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TBL_ConfigChangeMicroLog(ObjectId,ConfigId,EditType,UpdateTime)
					SELECT concat(TO_CHAR(:SWMonitorUnitId),'.',TO_CHAR(:SWPortId),'.',TO_CHAR(:SWSamplerUnitId)) ObjectId, 17 ConfigId, 1 EditType, NOW() UpdateTime;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTBLConfigChangeMicroLog4" grant="">
			<parameters>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="swEquipmentId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TBL_ConfigChangeMicroLog(ObjectId,ConfigId,EditType,UpdateTime)
					SELECT concat(TO_CHAR(:SWStationId ),'.',TO_CHAR(:swEquipmentId))  ObjectId, 3 ConfigId, 1 EditType, NOW() UpdateTime;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTBLConfigChangeMacroLog" grant="">
			<parameters>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TBL_ConfigChangeMacroLog(ObjectId,ConfigId,EditType,UpdateTime)
					SELECT TO_CHAR(:SWMonitorUnitId)  ObjectId, 15 ConfigId, 2 EditType, NOW() UpdateTime;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_UpdateTBLConfigChangeMacroLog" grant="">
			<parameters>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TBL_ConfigChangeMacroLog
					SET TBL_ConfigChangeMacroLog.EditType = 2, TBL_ConfigChangeMacroLog.UpdateTime = NOW()
					WHERE TBL_ConfigChangeMacroLog.ObjectId = TO_CHAR(:SWStationId ) AND TBL_ConfigChangeMacroLog.ConfigId = 1 ; 
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_ApproveWRFsu_InsertIntoTBLMonitorUnitProjectInfo" grant="">
			<parameters>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					INSERT INTO TBL_MonitorUnitProjectInfo(StationId,MonitorUnitId,ProjectName,ContractNo,InstallTime) 
					SELECT :SWStationId , SWMonitorUnitId, ProjectName, ContractNo, ApplyTime 
					FROM WR_FsuManagement 
					WHERE WR_FsuManagement.WRFsuId = :WRFsuId; 
				]]>
			</body>
		</procedure>





		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_GetSomeFiledName1" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT a.WRStationId, b.SWStationId, a.HouseCode, b.StationCode 
						FROM WR_HouseManagementCUCC a 
						INNER JOIN WR_StationManagementCUCC b ON a.WRStationId = b.WRStationId
						WHERE a.WRHouseId = :WRHouseId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_JudgeExist1" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM WR_FsuManagementCUCC WHERE WR_FsuManagementCUCC.WRFsuId != :WRFsuId AND WR_FsuManagementCUCC.FsuCode = :FsuCode limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_JudgeExist2" grant="">
			<parameters>
				<parameter name="WRStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM WR_FsuManagementCUCC a 
					INNER JOIN WR_HouseManagementCUCC b ON a.WRHouseId = b.WRHouseId 
					WHERE b.WRStationId = :WRStationId AND a.FsuCode != :FsuCode AND a.FsuName = :FsuName limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_JudgeExist3" grant="">
			<parameters>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM TSL_MonitorUnitCUCC 
					WHERE TSL_MonitorUnitCUCC.StationId = :SWStationId AND TSL_MonitorUnitCUCC.SUID != :FsuCode AND TSL_MonitorUnitCUCC.SUName = :FsuName limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_JudgeExist4" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM WR_FsuManagementCUCC 
					WHERE WR_FsuManagementCUCC.WRFsuId != :WRFsuId AND WR_FsuManagementCUCC.IPAddress = :IPAddress limit 1;
					]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_JudgeExist5" grant="">
			<parameters>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM TSL_MonitorUnitCUCC WHERE TSL_MonitorUnitCUCC.SUIP = :IPAddress AND TSL_MonitorUnitCUCC.SUID != :FsuCode limit 1;
					]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_GetSomeFiledName2" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT  a.FsuStatus, a.SWMonitorUnitId 
					FROM WR_FsuManagementCUCC a
					WHERE a.WRFsuId = :WRFsuId;	
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_GetSomeFiledName3" grant="">
			<parameters>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT  concat('FsuCode:' , TO_CHAR(FsuCode) ,
	    						'-FsuName:' , TO_CHAR(FsuName ) ,
	    						'-IPAddress:' , TO_CHAR(IPAddress ) ,
	    						'-UserName:' , TO_CHAR(UserName ) ,
	    						'-IPAddress:' , TO_CHAR(IPAddress ) ,
	    						'-Password:' , TO_CHAR(Password ) ,
	    						'-FtpUserName:' , TO_CHAR(FtpUserName ) ,
	    						'-FtpPassword:' , TO_CHAR(FtpPassword ) ,
	    						'-Remark:' , TO_CHAR(Remark ) ,
	    						'-FsuRId:' , IFNULL(TO_CHAR(FsuRId ),''))  
	    						FROM WR_FsuManagementCUCC WHERE WR_FsuManagementCUCC.WRFsuId = :WRFsuId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_GetSomeFiledName4" grant="">
			<parameters>
				<parameter name="WRStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT IFNULL(StationName,'') FROM WR_StationManagementCUCC WHERE WR_StationManagementCUCC.WRStationId = :WRStationId limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_UpdateWRFsuManagementCUCC_FsuStatusNotThree" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="UserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Password" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpUserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpPassword" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Remark" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuRId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ManufacturerId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ContractNo" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ProjectName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SET sql_safe_updates = 0;
					UPDATE WR_FsuManagementCUCC
					SET WR_FsuManagementCUCC.WRHouseId = :WRHouseId
						,WR_FsuManagementCUCC.FsuCode = :FsuCode
						,WR_FsuManagementCUCC.FsuName = :FsuName
						,WR_FsuManagementCUCC.IPAddress = :IPAddress
						,WR_FsuManagementCUCC.ManufacturerId = :ManufacturerId
						,WR_FsuManagementCUCC.FsuStatus = 1 
						,WR_FsuManagementCUCC.UserName = :UserName 
						,WR_FsuManagementCUCC.Password = :Password
						,WR_FsuManagementCUCC.FtpUserName = :FtpUserName
						,WR_FsuManagementCUCC.FtpPassword = :FtpPassword
						, WR_FsuManagementCUCC.Remark = :Remark 
						,WR_FsuManagementCUCC.ContractNo = :ContractNo
						,WR_FsuManagementCUCC.ProjectName = :ProjectName	
						,WR_FsuManagementCUCC.FsuRId = :FsuRId
					WHERE WR_FsuManagementCUCC.WRFsuId = :WRFsuId;
					SET sql_safe_updates = 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_UpdateWRFsuManagementCUCC_FsuStatusIsThree" grant="">
			<parameters>
				<parameter name="WRHouseId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="UserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Password" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpUserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpPassword" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Remark" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuRId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRFsuId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ManufacturerId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ContractNo" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ProjectName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE WR_FsuManagementCUCC
					SET WR_FsuManagementCUCC.WRHouseId = :WRHouseId
						,WR_FsuManagementCUCC.FsuCode = :FsuCode
						,WR_FsuManagementCUCC.FsuName = :FsuName
						,WR_FsuManagementCUCC.IPAddress = :IPAddress
						,WR_FsuManagementCUCC.ManufacturerId = :ManufacturerId
						,WR_FsuManagementCUCC.UserName = :UserName 
						,WR_FsuManagementCUCC.Password = :Password
						,WR_FsuManagementCUCC.FtpUserName = :FtpUserName
						,WR_FsuManagementCUCC.FtpPassword = :FtpPassword
						, WR_FsuManagementCUCC.Remark = :Remark 
						,WR_FsuManagementCUCC.ContractNo = :ContractNo
						,WR_FsuManagementCUCC.ProjectName = :ProjectName
						,WR_FsuManagementCUCC.FsuRId = :FsuRId
					WHERE WR_FsuManagementCUCC.WRFsuId = :WRFsuId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_UpdateTSLMonitorUnit_FsuStatusIsThree" grant="">
			<parameters>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>

			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TSL_MonitorUnit
					SET TSL_MonitorUnit.MonitorUnitName = :FsuName
						,TSL_MonitorUnit.IpAddress = :IPAddress
					WHERE TSL_MonitorUnit.MonitorUnitId = :SWMonitorUnitId 
						AND TSL_MonitorUnit.StationId = :SWStationId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_UpdateTSLMonitorUnitCUCC_FsuStatusIsThree" grant="">
			<parameters>
				<parameter name="FsuCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FsuName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="IPAddress" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="UserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Password" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpUserName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="FtpPassword" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TSL_MonitorUnitCUCC
					SET TSL_MonitorUnitCUCC.SUID = :FsuCode
						,TSL_MonitorUnitCUCC.SUName = :FsuName
						,TSL_MonitorUnitCUCC.SUIP = :IPAddress
						,TSL_MonitorUnitCUCC.UserName = :UserName 
						,TSL_MonitorUnitCUCC.Password = :Password
						,TSL_MonitorUnitCUCC.FtpUserName = :FtpUserName
						,TSL_MonitorUnitCUCC.FtpPassword = :FtpPassword
					WHERE TSL_MonitorUnitCUCC.StationId = :SWStationId 
						AND TSL_MonitorUnitCUCC.MonitorUnitId = :SWMonitorUnitId;   
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_UpdateTBLMonitorUnitProjectInfo_FsuStatusIsThree" grant="">
			<parameters>
				<parameter name="ProjectName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ContractNo" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TBL_MonitorUnitProjectInfo 
					SET  TBL_MonitorUnitProjectInfo.ProjectName = :ProjectName
						, TBL_MonitorUnitProjectInfo.ContractNo = :ContractNo
					WHERE  TBL_MonitorUnitProjectInfo.StationId = :SWStationId AND TBL_MonitorUnitProjectInfo.MonitorUnitId = :SWMonitorUnitId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_UpdateTBLConfigChangeMicroLog_FsuStatusIsThree" grant="">
			<parameters>
				<parameter name="ObjectId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TBL_ConfigChangeMicroLog
					SET TBL_ConfigChangeMicroLog.EditType = 2, TBL_ConfigChangeMicroLog.UpdateTime = NOW()
					WHERE TBL_ConfigChangeMicroLog.ObjectId = :ObjectId 
					AND TBL_ConfigChangeMicroLog.ConfigId = 15;	
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRFsu_UpdWRFsuCUCC_UpdateTBLConfigChangeMacroLog_FsuStatusIsThree" grant="">
			<parameters>
				<parameter name="SWMonitorUnitId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TBL_ConfigChangeMacroLog
					SET TBL_ConfigChangeMacroLog.EditType = 2, TBL_ConfigChangeMacroLog.UpdateTime = NOW()
					WHERE TBL_ConfigChangeMacroLog.ObjectId = TO_CHAR(:SWMonitorUnitId)  AND TBL_ConfigChangeMacroLog.ConfigId = 15;
				]]>
			</body>
		</procedure>
	</procedures>
</root>
