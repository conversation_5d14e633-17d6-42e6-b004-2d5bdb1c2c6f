﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using Delivery.BSL;
using Delivery.Common;

namespace Delivery.API.CMCC
{
    [Route("CMCC/api/[controller]")]
    public class FsuController : BaseController
    {
        [HttpGet("[action]")]
        public string GetFsuInfo(string startTime, string endTime, string structureId, string fsuName, string fsuCode, string manufactureId, string fsuStatus, int page, int rows)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            DataTable dt = ccbsl.GetFsuManagement(startTime, endTime, structureId, fsuName, fsuCode, manufactureId, fsuStatus, LogonId);
            DataTableColumnMapper.RenameColumns(dt, FsuFieldMap);
            return Json4EasyUI.onDataGrid(dt, page, rows);
        }
        public static readonly Dictionary<string, string> FsuFieldMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            {"rownumber","RowNumber"},
            {"wrfsuid","WRFsuId"},
            {"structurename","StructureName"},
            {"stationcode","StationCode"},
            {"stationname","StationName"},
            {"wrhouseid","WRHouseId"},
            {"housename","HouseName"},
            {"fsucode","FsuCode"},
            {"fsuname","FsuName"},
            {"ipaddress","IPAddress"},
            {"manufacturerid","ManufacturerId"},
            {"manufacturename","ManufactureName"},
            {"fsustatus","FsuStatus"},
            {"statusname","StatusName"},
            {"userid","UserId"},
            {"username","UserName"},
            {"loginname","LoginName"},
            {"password","Password"},
            {"ftpusername","FtpUserName"},
            {"ftppassword","FtpPassword"},
            {"applytime","ApplyTime"},
            {"approvetime","ApproveTime"},
            {"rejectcause","RejectCause"},
            {"remark","Remark"},
            {"contractno","ContractNo"},
            {"projectname","ProjectName"},
            {"ftptype","FTPType"},
            {"portnumber","PortNumber"}
        };

        [HttpPost("[action]")]
        public string ApproveWRFsu([FromForm] string WRFsuId)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            return ccbsl.ApproveWRFsu(WRFsuId);
        }

        [HttpPost("[action]")]
        public string RejectWRFsu([FromForm] string WRFsuId, [FromForm] string RejectReason)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            return ccbsl.RejectWRFsu(WRFsuId, RejectReason);
        }

        /// <summary>
        /// 新增FSU
        /// </summary>
        [HttpPost("[action]")]
        public string NewOne([FromForm] string WRHouseId, [FromForm] string FsuCode, [FromForm] string FsuName, 
            [FromForm] string IPAddress, [FromForm] string UserName, [FromForm] string RegPass, [FromForm] string FtpUser, 
            [FromForm] string FtpPass, [FromForm] string Remark, [FromForm] string ContractNo, [FromForm] string ProjectName,
            [FromForm] string FTPType, [FromForm] string PortNumber)
        {

            string OriginalPassword = RegPass;
            string ShaPassword = string.Empty;
            string Sm3Password = string.Empty;

            if (!string.IsNullOrEmpty(RegPass))
            {
                RegPass = FileMD5Helper.MD5String(OriginalPassword);
                ShaPassword = FileSHA256Helper.sha256(OriginalPassword);
                Sm3Password = FileSM3Helper.Hash(OriginalPassword);
            }
      
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            string result = string.Empty;
            result = ccbsl.Add_WRFsu(WRHouseId, FsuCode, FsuName, IPAddress, UserName, RegPass, FtpUser, FtpPass, Remark, ContractNo, ProjectName, LogonId, OriginalPassword, ShaPassword, Sm3Password, FTPType, PortNumber).ToString();
            return result;
        }

        [HttpPost("[action]")]
        public string DeleteWRFsu([FromForm] string WRFsuId)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            return ccbsl.DeleteWRFsu(WRFsuId, LogonId);
        }


        [HttpPost("[action]")]
        public string ModifyWRFsu([FromForm] string WRFsuId, [FromForm] string WRHouseId, [FromForm] string FsuCode, 
            [FromForm] string FsuName, [FromForm] string IPAddress, [FromForm] string RegName, [FromForm] string RegPass, 
            [FromForm] string FtpUser, [FromForm] string FtpPass, [FromForm] string Remark, [FromForm] string ContractNo, 
            [FromForm] string ProjectName, [FromForm] string RegPassHide, [FromForm] string FTPType, [FromForm] string PortNumber)
        {
            if(RegPass != RegPassHide)
            {
                RegPass = FileMD5Helper.MD5String(RegPass);
            }
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            string result = string.Empty;
            result = ccbsl.UpdateFsu(WRFsuId, WRHouseId, FsuCode, FsuName, IPAddress, RegName, RegPass, FtpUser, FtpPass, Remark, ContractNo, ProjectName, LogonId, FTPType, PortNumber).ToString();

            return result;
        }

        /// <summary>
        /// 导出 Excel
        /// </summary>
        /// <returns></returns>              
        [HttpGet("[action]")]
        public IActionResult ExportExcel(string structureId, string fsuCode, string strBegin, string strEnd, string fsuName, string fsuStatus, string manufactureId)
        {
            //string LogonId = "admin";
            DataTable dataTable = GetExportData(structureId, fsuCode, strBegin, strEnd,  fsuName, fsuStatus, manufactureId,  LogonId);

            string name = "FSU管理";
            byte[] bytes = ExcelHelper.DataTableToExcel(dataTable, null, name);
            return File(bytes, "application/ms-excel", $"{name}_{DateTime.Now:yyyyMMddHHmmssfff}.xlsx");
        }

        private DataTable GetExportData(string structureId, string fsuCode, string strBegin, string strEnd, string fsuName, string fsuStatus, string manufactureId, string LogonId)
        {
            try
            {
                DeliveryDataBsl ccbsl = new DeliveryDataBsl();
                DataTable dt = ccbsl.GetFsuManagement(strBegin, strEnd, structureId, fsuName, fsuCode, manufactureId, fsuStatus, LogonId);
                string[] cols = new string[]
                {
                    "RowNumber","StructureName","StationName","StationCode","FsuName","FsuCode", "ManufactureName", "ContractNo", "ProjectName", "StatusName",
                    "LoginName","Password","FtpUserName","FtpPassword","UserName","ApplyTime","ApproveTime","RejectCause","Remark"
                };
                DataTable myDt = dt.DefaultView.ToTable(true, cols);
                myDt.Columns["RowNumber"].ColumnName = "序号";
                myDt.Columns["StructureName"].ColumnName = "分组";
                myDt.Columns["StationName"].ColumnName = "站址名称";
                myDt.Columns["StationCode"].ColumnName = "站址编码";
                myDt.Columns["FsuName"].ColumnName = "Fsu名称";
                myDt.Columns["FsuCode"].ColumnName = "Fsu编码";
                myDt.Columns["ManufactureName"].ColumnName = "Fsu厂家";
                myDt.Columns["ContractNo"].ColumnName = "合同号";
                myDt.Columns["ProjectName"].ColumnName = "工程名称";
                myDt.Columns["StatusName"].ColumnName = "状态";
                myDt.Columns["LoginName"].ColumnName = "注册用户名";
                myDt.Columns["Password"].ColumnName = "注册用口令";
                myDt.Columns["FtpUserName"].ColumnName = "FTP用户名";
                myDt.Columns["FtpPassword"].ColumnName = "FTP用口令";
                myDt.Columns["UserName"].ColumnName = "申请人";
                myDt.Columns["ApplyTime"].ColumnName = "申请日期";
                myDt.Columns["ApproveTime"].ColumnName = "批准日期";
                myDt.Columns["RejectCause"].ColumnName = "退回原因";
                myDt.Columns["Remark"].ColumnName = "备注";

                return myDt;
            }
            catch (Exception ex)
            {
                Logger.Log(ex);
            }
            return null;
        }

    }
}
