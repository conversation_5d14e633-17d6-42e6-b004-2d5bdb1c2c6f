﻿using BDSTool.BLL.B;
using BDSTool.DBUtility;
using BDSTool.Entity.B;
using log4net;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.Entity.DRHelper
{
    //public class TDevConfExHelper
    //{
    //    private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

    //    public static DevConfExBiz FromDataRow(DataRow row) {
    //        var entity = new DevConfExBiz();
    //        try {
    //            entity.TDevice.DeviceID = row["DeviceID"].ToString();
    //            entity.TDevice.DeviceName = row["DeviceName"].ToString();
    //            entity.TDevice.RoomName = row["RoomName"].ToString();
    //            entity.TDevice.DeviceType =SHelper.ToIntNullable( row["DeviceType"]);
    //            entity.TDevice.DeviceSubType = SHelper.ToIntNullable(row["DeviceSubType"]);
    //            entity.TDevice.Model = row["Model"].ToString();
    //            entity.TDevice.Brand = row["Brand"].ToString();

    //            entity.TDevice.RatedCapacity = SHelper.ToFloat(row["RatedCapacity"]);

    //            entity.TDevice.Version = row["Version"].ToString();

    //            entity.TDevice.BeginRunTime = SHelper.ToDateTime(row["BeginRunTime"]);

    //            entity.TDevice.DevDescribe = row["DevDescribe"].ToString();
    //            //------------------------------------------------
    //            entity.StationId = SHelper.ToInt(row["StationId"]);
    //        }
    //        catch (Exception ex) {
    //            logger.ErrorFormat("TBL_EquipmentCMCC.FromDataRow();error={0}", ex.Message);
    //            logger.Error(ex.StackTrace);
    //            return null;
    //        }
    //        return entity;
    //    }
    //}
}
