﻿using System;
using System.Collections.Generic;
using System.Web;
using System.Text;
using System.Data;
using System.Linq;

namespace Delivery.API
{
    public class Json4EasyUI
    {
        public static string onComboBox(DataTable dt)
        {
            StringBuilder sb = new StringBuilder(256);
            if(dt!=null && dt.Columns.Count > 0) { 
                string idField = dt.Columns[0].ColumnName;
                string textField = dt.Columns[1].ColumnName;
                sb.Append("[{\"id\":\"-1\",\"text\":\"请选择\"}");
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    var arr = from field in dt.Rows[i].ItemArray
                              select JsonCharFilter(field);
                    sb.Append(string.Format(",{{\"id\":\"{0}\",\"text\":\"{1}\"}}", arr.ToArray()));
                }
                sb.Append("]");
            }
            return sb.ToString();
        }
        public static string onDataGrid(DataTable dt, int page, int rows)
        {
            page = ((page == 0) ? 1 : page);
            rows = ((rows == 0) ? 10 : rows);
            int num = (page - 1) * rows;
            int num2 = page * rows;
            num2 = ((num2 > dt.Rows.Count) ? dt.Rows.Count : num2);
            StringBuilder stringBuilder = new StringBuilder();
            if (num == num2)
            {
                stringBuilder.Append("{\"total\":" + dt.Rows.Count + ",\"rows\":[]}");
            }
            else
            {
                stringBuilder.Append("{\"total\":" + dt.Rows.Count + ",\"rows\":[");
                for (int i = num; i < num2; i++)
                {
                    stringBuilder.Append("{");
                    for (int j = 0; j < dt.Columns.Count; j++)
                    {
                        stringBuilder.Append("\"");
                        stringBuilder.Append(dt.Columns[j].ColumnName);
                        stringBuilder.Append("\":\"");
                        stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[i][j]));
                        stringBuilder.Append("\",");
                    }
                    StringBuilder expr_11F = stringBuilder;
                    expr_11F.Remove(expr_11F.Length - 1, 1);
                    stringBuilder.Append("},");
                }
                StringBuilder expr_146 = stringBuilder;
                expr_146.Remove(expr_146.Length - 1, 1);
                stringBuilder.Append("]}");
            }
            return stringBuilder.ToString();
        }

        public static string onVertiveCompanyTreeData(DataTable dt)
        {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.Append("[");
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                Convert.ToString(dt.Rows[i]["ReportId"]);
                stringBuilder.Append("{");
                stringBuilder.Append("\"id");
                stringBuilder.Append("\":\"");
                stringBuilder.Append(string.Concat(new string[]
                {
                    Json4EasyUI.JsonCharFilter(dt.Rows[i]["ReportId"]),
                    "#",
                    Json4EasyUI.JsonCharFilter(dt.Rows[i]["Sequence"]),
                    "#",
                    Json4EasyUI.JsonCharFilter(dt.Rows[i]["Editable"])
                }));
                stringBuilder.Append("\",");
                stringBuilder.Append("\"text");
                stringBuilder.Append("\":\"");
                stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[i]["Status"]));
                stringBuilder.Append("\"");
                stringBuilder.Append("}");
                stringBuilder.Append(",");
            }
            StringBuilder expr_155 = stringBuilder;
            expr_155.Remove(expr_155.Length - 1, 1);
            stringBuilder.Append("]");
            return stringBuilder.ToString();
        }

        public static string onTreeData(DataTable dt)
        {
            StringBuilder stringBuilder = new StringBuilder();
            if (dt.Rows.Count == 0)
            {
                stringBuilder.Append("[{\"id\":\"\",\"text\":\"什么也没查到\"}]");
                return stringBuilder.ToString();
            }
            stringBuilder.Append("[");
            int i = 0;
            while (i < dt.Rows.Count)
            {
                string a = Convert.ToString(dt.Rows[i]["ProvinceName"]);
                if (i == 0)
                {
                    stringBuilder.Append("{");
                    stringBuilder.Append("\"id");
                    stringBuilder.Append("\":\"");
                    stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[i]["ProvinceName"]));
                    stringBuilder.Append("\",");
                    stringBuilder.Append("\"text");
                    stringBuilder.Append("\":\"");
                    stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[i]["ProvinceName"]));
                    stringBuilder.Append("\",");
                    if (dt.Rows.Count > 10)
                    {
                        stringBuilder.Append("\"state\":\"closed\",");
                    }
                    stringBuilder.Append("\"children\": [");
                    for (int j = 0; j < dt.Rows.Count; j++)
                    {
                        if ((string)dt.Rows[j]["ProvinceName"] == (string)dt.Rows[i]["ProvinceName"])
                        {
                            stringBuilder.Append("{");
                            stringBuilder.Append("\"id");
                            stringBuilder.Append("\":\"");
                            stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[j]["ContractId"]));
                            stringBuilder.Append("\",");
                            stringBuilder.Append("\"text");
                            stringBuilder.Append("\":\"<a>");
                            stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[j]["ContractNo"]) + "|" + Json4EasyUI.JsonCharFilter(dt.Rows[j]["ProjectName"]));
                            stringBuilder.Append("</a>\"},");
                        }
                    }
                    StringBuilder expr_45D = stringBuilder;
                    expr_45D.Remove(expr_45D.Length - 1, 1);
                    stringBuilder.Append("]");
                    goto IL_478;
                }
                if (!(a == Convert.ToString(dt.Rows[i - 1]["ProvinceName"])))
                {
                    stringBuilder.Append("{");
                    stringBuilder.Append("\"id");
                    stringBuilder.Append("\":\"");
                    stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[i]["ProvinceName"]));
                    stringBuilder.Append("\",");
                    stringBuilder.Append("\"text");
                    stringBuilder.Append("\":\"");
                    stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[i]["ProvinceName"]));
                    stringBuilder.Append("\",");
                    if (dt.Rows.Count > 10)
                    {
                        stringBuilder.Append("\"state\":\"closed\",");
                    }
                    stringBuilder.Append("\"children\": [");
                    for (int k = 0; k < dt.Rows.Count; k++)
                    {
                        if ((string)dt.Rows[k]["ProvinceName"] == (string)dt.Rows[i]["ProvinceName"])
                        {
                            stringBuilder.Append("{");
                            stringBuilder.Append("\"id");
                            stringBuilder.Append("\":\"");
                            stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[k]["ContractId"]));
                            stringBuilder.Append("\",");
                            stringBuilder.Append("\"text");
                            stringBuilder.Append("\":\"");
                            stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[k]["ContractNo"]) + "|" + Json4EasyUI.JsonCharFilter(dt.Rows[k]["ProjectName"]));
                            stringBuilder.Append("\"},");
                        }
                    }
                    StringBuilder expr_25C = stringBuilder;
                    expr_25C.Remove(expr_25C.Length - 1, 1);
                    stringBuilder.Append("]");
                    goto IL_478;
                }
            IL_490:
                i++;
                continue;
            IL_478:
                stringBuilder.Append("}");
                stringBuilder.Append(",");
                goto IL_490;
            }
            StringBuilder expr_4A6 = stringBuilder;
            expr_4A6.Remove(expr_4A6.Length - 1, 1);
            stringBuilder.Append("]");
            return stringBuilder.ToString();
        }

        internal static string onCoCompanyTreeData(DataTable dt)
        {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.Append("[");
            int i = 0;
            while (i < dt.Rows.Count)
            {
                string a = Convert.ToString(dt.Rows[i]["CompanyId"]);
                if (i == 0)
                {
                    stringBuilder.Append("{");
                    stringBuilder.Append("\"id");
                    stringBuilder.Append("\":");
                    stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[i]["CompanyId"]));
                    stringBuilder.Append(",");
                    stringBuilder.Append("\"text");
                    stringBuilder.Append("\":\"");
                    stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[i]["CompanyName"]));
                    stringBuilder.Append("\",");
                    stringBuilder.Append("\"children\": [");
                    for (int j = 0; j < dt.Rows.Count; j++)
                    {
                        if ((int)dt.Rows[j]["CompanyId"] == (int)dt.Rows[i]["CompanyId"])
                        {
                            stringBuilder.Append("{");
                            stringBuilder.Append("\"id");
                            stringBuilder.Append("\":\"");
                            stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[j]["ReportId"]));
                            stringBuilder.Append("\",");
                            stringBuilder.Append("\"text");
                            stringBuilder.Append("\":\"<a>");
                            stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[j]["Status"]));
                            stringBuilder.Append("</a>\"},");
                        }
                    }
                    StringBuilder expr_3B2 = stringBuilder;
                    expr_3B2.Remove(expr_3B2.Length - 1, 1);
                    stringBuilder.Append("]");
                    goto IL_3CD;
                }
                if (!(a == Convert.ToString(dt.Rows[i - 1]["CompanyId"])))
                {
                    stringBuilder.Append("{");
                    stringBuilder.Append("\"id");
                    stringBuilder.Append("\":");
                    stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[i]["CompanyId"]));
                    stringBuilder.Append(",");
                    stringBuilder.Append("\"text");
                    stringBuilder.Append("\":\"");
                    stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[i]["CompanyName"]));
                    stringBuilder.Append("\",");
                    stringBuilder.Append("\"children\": [");
                    for (int k = 0; k < dt.Rows.Count; k++)
                    {
                        if ((int)dt.Rows[k]["CompanyId"] == (int)dt.Rows[i]["CompanyId"])
                        {
                            stringBuilder.Append("{");
                            stringBuilder.Append("\"id");
                            stringBuilder.Append("\":\"");
                            stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[k]["ReportId"]));
                            stringBuilder.Append("\",");
                            stringBuilder.Append("\"text");
                            stringBuilder.Append("\":\"");
                            stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[k]["Status"]));
                            stringBuilder.Append("\"},");
                        }
                    }
                    StringBuilder expr_1F7 = stringBuilder;
                    expr_1F7.Remove(expr_1F7.Length - 1, 1);
                    stringBuilder.Append("]");
                    goto IL_3CD;
                }
            IL_3E5:
                i++;
                continue;
            IL_3CD:
                stringBuilder.Append("}");
                stringBuilder.Append(",");
                goto IL_3E5;
            }
            StringBuilder expr_3FB = stringBuilder;
            expr_3FB.Remove(expr_3FB.Length - 1, 1);
            stringBuilder.Append("]");
            return stringBuilder.ToString();
        }

        public static string onDataGrid(DataTable dt, int total)
        {
            StringBuilder stringBuilder = new StringBuilder();
            if (dt == null || dt.Rows.Count == 0)
            {
                stringBuilder.Append("{\"total\":" + total + ",\"rows\":[]}");
            }
            else
            {
                stringBuilder.Append("{\"total\":" + total + ",\"rows\":[");
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    stringBuilder.Append("{");
                    for (int j = 0; j < dt.Columns.Count; j++)
                    {
                        stringBuilder.Append("\"");
                        stringBuilder.Append(dt.Columns[j].ColumnName);
                        stringBuilder.Append("\":\"");
                        stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[i][j]));
                        stringBuilder.Append("\",");
                    }
                    StringBuilder expr_D7 = stringBuilder;
                    expr_D7.Remove(expr_D7.Length - 1, 1);
                    stringBuilder.Append("},");
                }
                StringBuilder expr_108 = stringBuilder;
                expr_108.Remove(expr_108.Length - 1, 1);
                stringBuilder.Append("]}");
            }
            return stringBuilder.ToString();
        }

        public static string onDataGrid(DataTable dt)
        {
            StringBuilder stringBuilder = new StringBuilder();
            if (dt == null || dt.Rows.Count == 0)
            {
                stringBuilder.Append("{\"total\":" + 0 + ",\"rows\":[]}");
            }
            else
            {
                stringBuilder.Append("{\"rows\":[");
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    stringBuilder.Append("{");
                    for (int j = 0; j < dt.Columns.Count; j++)
                    {
                        stringBuilder.Append("\"");
                        stringBuilder.Append(dt.Columns[j].ColumnName);
                        stringBuilder.Append("\":\"");
                        stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[i][j]));
                        stringBuilder.Append("\",");
                    }
                    StringBuilder expr_C7 = stringBuilder;
                    expr_C7.Remove(expr_C7.Length - 1, 1);
                    stringBuilder.Append("},");
                }
                StringBuilder expr_F8 = stringBuilder;
                expr_F8.Remove(expr_F8.Length - 1, 1);
                stringBuilder.Append("]}");
                Console.WriteLine(stringBuilder.ToString());
            }
            return stringBuilder.ToString();
        }

        public static string onForm(DataTable dt)
        {
            StringBuilder stringBuilder = new StringBuilder();
            if (dt.Rows.Count > 0)
            {
                stringBuilder.Append("{");
                for (int i = 0; i < dt.Columns.Count; i++)
                {
                    stringBuilder.Append("\"");
                    stringBuilder.Append(dt.Columns[i].ColumnName);
                    stringBuilder.Append("\":\"");
                    stringBuilder.Append(Json4EasyUI.JsonCharFilter(dt.Rows[0][i]));
                    stringBuilder.Append("\",");
                }
                StringBuilder expr_94 = stringBuilder;
                expr_94.Remove(expr_94.Length - 1, 1);
                stringBuilder.Append("}");
            }
            return stringBuilder.ToString();
        }

        public static string onFormMultiRow(DataTable dt)
        {
            StringBuilder stringBuilder = new StringBuilder();
            if (dt != null && dt.Rows.Count > 0)
            {
                foreach (DataRow dataRow in dt.Rows)
                {
                    stringBuilder.Append("{");
                    for (int i = 0; i < dt.Columns.Count; i++)
                    {
                        stringBuilder.Append("\"");
                        stringBuilder.Append(dt.Columns[i].ColumnName);
                        stringBuilder.Append("\":\"");
                        stringBuilder.Append(Json4EasyUI.JsonCharFilter(dataRow[i]));
                        stringBuilder.Append("\",");
                    }
                    StringBuilder expr_AC = stringBuilder;
                    expr_AC.Remove(expr_AC.Length - 1, 1);
                    stringBuilder.Append("},");
                }
                StringBuilder expr_E9 = stringBuilder;
                expr_E9.Remove(expr_E9.Length - 1, 1);
            }
            return string.Format("[{0}]", stringBuilder.ToString());
        }

        public static string JsonCharFilter(object value)
        {
            if (value.GetType().FullName.Equals("System.String"))
            {
                return value.ToString().Replace("\n", "").Replace("\\n", "").Replace("\r", "").Replace("\"", "").Replace("\\", "").Trim();
            }
            return value.ToString();
        }
    }
}