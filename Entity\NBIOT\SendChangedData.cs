﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 上报变化数据
    /// </summary>
    public class SendChangedData : BMessage
    {
        public List<Device> Values { get; set; }

        public SendChangedData() : base()
        {
            MessageType = (int)BMessageType.SEND_CHANGED_DATA;
        }

        public SendChangedData(string fsuId, List<Device> values)
            : base()
        {
            MessageType = (int)BMessageType.SEND_CHANGED_DATA;

            FSUID = fsuId;
            Values = values;
        }

        public static SendChangedData Deserialize(string json)
        {
            SendChangedData info = null;
            try
            {
                info = JsonConvert.DeserializeObject<SendChangedData>(json);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SendChangedData.Deserialize:{0}", ex.Message);
                entityLogger.ErrorFormat("SendChangedData.Deserialize:{0}", ex.StackTrace);
                info = new SendChangedData();
                info.ErrorMsg = ex.Message;
                info.StringXML = json;
            }
            return info;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0},{1},{2}:", MessageId, MessageType, FSUID);
            sb.AppendLine();
            if (Values != null)
            {
                foreach (Device device in Values)
                {
                    sb.AppendFormat("{0}\r\n", device.ToString());
                }
            }
            return sb.ToString();
        }
    }
}