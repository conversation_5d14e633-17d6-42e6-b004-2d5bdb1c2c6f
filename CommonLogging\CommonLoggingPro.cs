﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using log4net;
using System.Collections.Concurrent;
using System.IO;

namespace Common.Logging.Pro
{
    public class LogManager
    {
        static log4net.Repository.ILoggerRepository repository;
        private static ConcurrentDictionary<string, log4net.ILog> dict = new ConcurrentDictionary<string, log4net.ILog>();
        public static log4net.ILog GetLogger(string logName)
        {
            try
            {
                if (repository == null)
                {
                    repository = log4net.LogManager.CreateRepository("DataServer");
                    var fileInfo = new System.IO.FileInfo("log4net.config");
                    log4net.Config.XmlConfigurator.Configure(repository, fileInfo);
                    log4net.Config.BasicConfigurator.Configure(repository);
                }
                if (dict.ContainsKey(logName))
                {
                    log4net.ILog logMgr = dict[logName];
                    return logMgr;
                }
                else
                {
                    log4net.ILog mgr = log4net.LogManager.GetLogger(repository.Name, logName);
                    dict[logName] = mgr;
                    return mgr;
                }
            }
            catch (Exception)
            {
                repository = null;
                dict.Clear();
            }
            return null;
        }
    }
}
