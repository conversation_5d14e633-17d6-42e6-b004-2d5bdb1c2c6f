﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public class Signal
    {
        public string Id { get; set; }
        public float? Value { get; set; }
        public string RecordTime { get; set; }
        public float? SetValue { get; set; }
        public float? HLimit { get; set; }
        public float? SHLimit { get; set; }
        public float? LLimit { get; set; }
        public float? SLLimit { get; set; }
        public float? Threshold { get; set; }
        public float? RelativeVal { get; set; }
        public float? IntervalTime { get; set; }
        public float? BDelay { get; set; }
        public float? EDelay { get; set; }
        public Signal() { }

        #region SiteWeb配置
        public int MySignalId { get; set; }
        #endregion
    }
}
