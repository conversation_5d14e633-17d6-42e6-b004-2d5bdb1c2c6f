
namespace BDSTool.Entity.S2
{


    using BDSTool.DBUtility;
    using BDSTool.DBUtility.Common;
    using Delivery.Common.NoProcedure.Service;
    using Delivery.Common.NoProcedure.Utils;
    using System;
    using System.Collections.Generic;

    public partial class TBL_Control : IBatchInsertRow
    {
        #region for batch insert
        private static string _InsertHeader = @"INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, `MaxValue`, MinValue, DefaultValue, ModuleNo)";

        public string GetInsertHeader() {
            if (CommonUtils.IsNoProcedure)
            {
                return Public_ExecuteSqlService.Instance.GetInsertHeader_TBL_Control();
            }
            return _InsertHeader;
        }

        public string GetDataRowValueString() {
            return string.Format(@"({0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13},{14},{15},{16},{17},{18},{19},{20})",
                 EquipmentTemplateId, ControlId, SHelper.GetPara(ControlName), ControlCategory, SHelper.GetPara(CmdToken),
                SHelper.GetPara(BaseTypeId), ControlSeverity, SHelper.GetPara(SignalId), SHelper.GetPara(TimeOut), SHelper.GetPara(Retry), SHelper.GetPara(Description), SHelper.GetPara(Enable), SHelper.GetPara(Visible),
                DisplayIndex, CommandType, SHelper.GetPara(ControlType), SHelper.GetPara(DataType), MaxValue, MinValue, SHelper.GetPara(DefaultValue), ModuleNo);
        }

        #endregion
        public Dictionary<int, string> ParameterValue2Meanings = new Dictionary<int, string>();
    }
}
