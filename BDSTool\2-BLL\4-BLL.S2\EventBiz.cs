﻿using Common.Logging.Pro;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;



using BDSTool.Entity.S2;
using BDSTool.DBUtility.Common;
using Carrier.BDSTool;


namespace BDSTool.BLL.S2
{
    public class EventBiz
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static bool SaveEntityList(List<TBL_Event> events) {
            if (BatchInsertHelper.Execute(events) == false) {
                logger.InfoFormat("EventBiz.SaveEntityList();TBL_Event 批量插入失败");
                return false;
            }

            //------------------------------------------------------------------------

            var eventConditionsAll = new List<TBL_EventCondition>();
            foreach (var o in events) {
                eventConditionsAll.AddRange(o.eventConditions);
            }

            if (BatchInsertHelper.Execute(eventConditionsAll) == false) {
                logger.InfoFormat("EventBiz.SaveEntityList();TBL_EventCondition 批量插入失败");
                return false;
            }
            //------------------------------------------------------------------------
            ConfigHelper.UpdateBaseTypeIdOfEventCondition(events[0].EquipmentTemplateId);

            //------------------------------------------------------------------------
            //20180523 指定部分告警基类->SiteWeb告警类别 的映射
            if (BDSHelperCMCC.Global_IsNeedSetEventKind) {
                EventCategoryMapBiz.UpdateSiteWebEventCategoryId(events[0].EquipmentTemplateId);
            }
            return true;
        }
    }
}
