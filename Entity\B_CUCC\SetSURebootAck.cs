﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class SetSURebootAck : BMessage
    {
        public EnumResult Result { get; private set; }
        public SetSURebootAck(string suid, string surid, EnumResult result) : base()
        {
            MessageType = (int)BMessageType.SET_SUREBOOT_ACK;
            SUId = suid;
            SURId = surid;
            Result = result;
        }
        public SetSURebootAck() : base()
        {
            MessageType = (int)BMessageType.SET_SUREBOOT_ACK;
        }
        public static SetSURebootAck Deserialize(XmlDocument xmlDoc)
        {
            SetSURebootAck setSURebootAck = null;
            try
            {
                string code = xmlDoc.SelectSingleNode("/Response/PK_Type/Code").InnerText.Trim();
                string suId = xmlDoc.SelectSingleNode("/Response/Info/SUId").InnerText.Trim();
                string surId = xmlDoc.SelectSingleNode("/Response/Info/SURId").InnerText.Trim();
                string resultString = xmlDoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim();
                setSURebootAck = new SetSURebootAck(suId, surId, string.IsNullOrEmpty(resultString) ? EnumResult.FAILURE : (EnumResult)int.Parse(resultString));
                entityLogger.DebugFormat("SetSURebootAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                setSURebootAck.StringXML = xmlDoc.InnerXml;
                return setSURebootAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetSURebootAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetSURebootAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                setSURebootAck = new SetSURebootAck();
                setSURebootAck.ErrorMsg = ex.Message;
                setSURebootAck.StringXML = xmlDoc.InnerXml;
                return setSURebootAck;
            }
        }

        public override string ToString()
        {
            return string.Format("{0},{1},{2},({3} and {4}):{5}", MessageId, (BMessageType)MessageType,MessageType, SUId, SURId, Result);
        }
    }
}
