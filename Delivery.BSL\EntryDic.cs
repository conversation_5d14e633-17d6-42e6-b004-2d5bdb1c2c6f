﻿using Delivery.Common;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using Delivery.DAL;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Delivery.BSL
{
    public class EntryDic
    {
        readonly ExecuteSql exesql = new ExecuteSql();
        public DataTable GetDataDic(string entryId, string parentEntryId, string parentItemId)
        {
            //Must be numeric!
            if (Validation.IsNum(entryId) && Validation.IsNum(parentEntryId) && Validation.IsNum(parentItemId))
            {
                DataTable dt = null;
                if (CommonUtils.IsNoProcedure)
                {
                    dt = Public_ExecuteSqlService.Instance.NoStore_WRDataItem_GetDataDic(entryId, parentEntryId, parentItemId);
                }
                else
                {
                    StringBuilder sb = new StringBuilder(128);
                    List<QueryParameter> parasList = new List<QueryParameter>();
                    sb.Append(@"SELECT -1 AS ItemId,'请选择' AS ItemValue UNION ALL ");
                    sb.Append(@"select ItemId,ItemValue from WR_DataItem where EntryId = @EntryId ");
                    parasList.Add(new QueryParameter("EntryId", DataType.Int, entryId));
                    if (parentEntryId != "-1")
                    {
                        sb.Append(@"AND ParentEntryId = @ParentEntryId ");
                        parasList.Add(new QueryParameter("ParentEntryId", DataType.Int, parentEntryId));
                    }
                    if (parentItemId != "-1")
                    {
                        sb.Append(@"AND ParentItemId = @ParentItemId");
                        parasList.Add(new QueryParameter("ParentItemId", DataType.Int, parentItemId));
                    }
                    dt = exesql.ExecuteSQL(sb.ToString(), parasList.ToArray());
                    /*string sql = @"SELECT -1 AS ItemId,'请选择' AS ItemValue UNION ALL ";
                    sql += @"select ItemId,ItemValue from WR_DataItem where EntryId=" + entryId;
                    if (parentEntryId != "-1")
                    {
                        sql += string.Format(" and ParentEntryId={0} ", parentEntryId);
                    }
                    if (parentItemId != "-1")
                    {
                        sql += string.Format(" and ParentItemId={0} ", parentItemId);
                    }
                    DataTable dt = exesql.ExecuteSQL(sql, new DbParameter[] {
                    });*/
                }
                if (dt == null)
                    dt = new DataTable();
                return dt;
                
            }
            else
            {
                return new DataTable();
            }
        }
        /// <summary>
        /// 获取省，市，县，机房类型
        /// </summary>
        /// <param name="type"></param>
        /// <param name="ItemId"></param>
        /// <returns></returns>
        public DataTable GetDataUiDic(string type, string ItemId)
        {
            if (Validation.IsNum(ItemId) && Validation.IsNum(type))
            {
                if (CommonUtils.IsNoProcedure)
                {
                    DataTable res = Public_ExecuteSqlService.Instance.NoStore_GetDataUiDic(type, ItemId);
                    if (res == null)
                        res = new DataTable();
                    return res;
                }
                else {
                    string sql = string.Empty;
                    QueryParameter[] queryParameters;
                    //string sql = "select ItemId,ItemValue from WR_DataItem where EntryId={0} and ItemId ={1}";
                    if (type == "7" && ItemId == "-1") //因wr_dataitem entryid=7表示机房类型，添加特别项取设备类型
                    {
                        sql = @"SELECT '-1' AS ItemId,'请选择' AS ItemValue UNION ALL ";
                        sql += "select ExtendField2 as ItemId,ItemValue from TBL_DataItem where EntryId = @EntryId";
                        queryParameters = new QueryParameter[]
                        {
                        new QueryParameter("EntryId", DataType.Int, type)
                        };
                    }
                    else if (type == "4" || type == "5" || type == "6" || type == "7")
                    {
                        sql = @"SELECT '-1' AS ItemId,'请选择' AS ItemValue UNION ALL ";
                        sql += "select ItemId,ItemValue from WR_DataItem where EntryId = @EntryId";
                        queryParameters = new QueryParameter[]
                        {
                        new QueryParameter("EntryId", DataType.Int, type)
                        };
                    }
                    else if (type == "20" && ItemId == "20")
                    {
                        sql = @"SELECT -1 AS ItemId,'请选择' AS ItemValue UNION ALL ";
                        sql += "SELECT StructureId as ItemId,StructureName as ItemValue FROM TBL_StationStructure where structureType=1 AND StructureGroupId =1 AND IsUngroup =0";
                        queryParameters = new QueryParameter[] { };
                    }
                    else if (type == "14")
                    {
                        sql = @"SELECT '-1' AS ItemId,'请选择' AS ItemValue UNION ALL ";
                        sql += "select ExtendField2 as ItemId,ItemValue from TBL_DataItem where EntryId = @EntryId";
                        queryParameters = new QueryParameter[]
                        {
                        new QueryParameter("EntryId", DataType.Int, type)
                        };
                    }
                    else
                    {
                        sql = "select ItemId,ItemValue from WR_DataItem where EntryId = @EntryId and ItemId = @ItemId";
                        queryParameters = new QueryParameter[]
                        {
                        new QueryParameter("EntryId", DataType.Int, type),
                        new QueryParameter("ItemId", DataType.Int, ItemId)
                        };
                    }
                    //sql = string.Format(sql, type, ItemId);
                    //DataTable dt = exesql.ExecuteSQL(sql);
                    //这里
                    DataTable dt = exesql.ExecuteSQL(sql, queryParameters);
                    if (dt == null)
                        dt = new DataTable();
                    return dt;
                }
            }
            else
            {
                return new DataTable();
            }
        }
    }
}
