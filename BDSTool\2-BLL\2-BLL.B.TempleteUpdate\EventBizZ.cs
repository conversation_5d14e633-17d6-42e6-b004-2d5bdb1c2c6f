﻿using Common.Logging.Pro;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using BDSTool.Entity.S2;
using BDSTool.DBUtility;
using BDSTool.DBUtility.Common;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace BDSTool.BLL.S2
{
    public class EventBizZ
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static bool ZSetCondition(TBL_Event e) {
            try {
                foreach (var ec in e.eventConditions) {
                    if (CommonUtils.IsNoProcedure)
                    {
                        Public_ExecuteSqlService.Instance.NoStore_InsertRowIntoTBLEventCondition(ec.EventConditionId, ec.EquipmentTemplateId, ec.EventId, SHelper.GetPara(ec.StartOperation), SHelper.GetPara(ec.StartCompareValue),
                        ec.StartDelay, SHelper.GetPara(ec.EndOperation), SHelper.GetPara(ec.EndCompareValue), SHelper.GetPara(ec.EndDelay), SHelper.GetPara(ec.Frequency),
                        SHelper.GetPara(ec.FrequencyThreshold), SHelper.GetPara(ec.Meanings), SHelper.GetPara(ec.EquipmentState), SHelper.GetPara(ec.BaseTypeId), ec.EventSeverity,
                        SHelper.GetPara(ec.StandardName));
                    }
                    else
                    {
                        string sql = string.Format(@"
                        INSERT INTO TBL_EventCondition
                        (EventConditionId, EquipmentTemplateId, EventId, StartOperation, StartCompareValue, 
                        StartDelay, EndOperation, EndCompareValue, EndDelay, Frequency, 
                        FrequencyThreshold, Meanings, EquipmentState, BaseTypeId, EventSeverity, 
                        StandardName)
                        VALUES (
                        {0},{1},{2},{3},{4},
                        {5},{6},{7},{8},{9},
                        {10},{11},{12},{13},{14},
                        {15}
                        )",
                        ec.EventConditionId, ec.EquipmentTemplateId, ec.EventId, SHelper.GetPara(ec.StartOperation), SHelper.GetPara(ec.StartCompareValue),
                        ec.StartDelay, SHelper.GetPara(ec.EndOperation), SHelper.GetPara(ec.EndCompareValue), SHelper.GetPara(ec.EndDelay), SHelper.GetPara(ec.Frequency),
                        SHelper.GetPara(ec.FrequencyThreshold), SHelper.GetPara(ec.Meanings), SHelper.GetPara(ec.EquipmentState), SHelper.GetPara(ec.BaseTypeId), ec.EventSeverity,
                        SHelper.GetPara(ec.StandardName));

                        DBHelper.ExecuteNonQuery(sql);
                    }
                }
            }
            catch (Exception ex) {
                logger.ErrorFormat("ZSetCondition();EquipmentTemplateId={0},EventId={1};Error={2}", e.EquipmentTemplateId, e.EventId, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;

        }
        public static bool XAddOne(TBL_Event e) {
            if (XSaveEntity(e) == false)
                return false;


            if (EventBizZ.ZSetCondition(e) == false)
                return false;

            return true;
            
        }

        public static bool XSaveEntity(TBL_Event e) {
            try {
                if (CommonUtils.IsNoProcedure)
                {
                    Public_ExecuteSqlService.Instance.NoStore_InsertRowIntoTBLEvent(e.EquipmentTemplateId, e.EventId, SHelper.GetPara(e.EventName), e.StartType, e.EndType,
                    SHelper.GetPara(e.StartExpression), SHelper.GetPara(e.SuppressExpression), e.EventCategory, SHelper.GetPara(e.SignalId), SHelper.GetPara(e.Enable),
                    SHelper.GetPara(e.Visible), SHelper.GetPara(e.Description), e.DisplayIndex, e.ModuleNo) ;
                }
                else
                {
                    var sql = string.Format(@"
                    INSERT INTO TBL_Event
                    (EquipmentTemplateId, EventId, EventName, StartType, EndType, 
                    StartExpression, SuppressExpression, EventCategory, SignalId, Enable, 
                    Visible, Description, DisplayIndex, ModuleNo)
                    VALUES (
                    {0},{1},{2},{3},{4},
                    {5},{6},{7},{8},{9},
                    {10},{11},{12},{13}
                    )",
                    e.EquipmentTemplateId, e.EventId, SHelper.GetPara(e.EventName), e.StartType, e.EndType,
                    SHelper.GetPara(e.StartExpression), SHelper.GetPara(e.SuppressExpression), e.EventCategory, SHelper.GetPara(e.SignalId), SHelper.GetPara(e.Enable),
                    SHelper.GetPara(e.Visible), SHelper.GetPara(e.Description), e.DisplayIndex, e.ModuleNo);

                    DBHelper.ExecuteNonQuery(sql);
                }
            }
            catch (Exception ex) {
                logger.ErrorFormat("SaveEntity();EquipmentTemplateId={0},EventName={1};Error={2}", e.EquipmentTemplateId, e.EventName, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;

        }

        public static bool ZDeleteList(int EquipmentTemplateId, List<int> eventIds)
        {
                if (eventIds.Count == 0)
                    return true;

            StringBuilder sb = new StringBuilder();
            DbConfigPara para = DBHelper.GetDbConfig();
            foreach (var sigId in eventIds)
            {
                try
                {
                    string sql1, sql2;
                    if (CommonUtils.IsNoProcedure)
                    {
                        string tempSql1 = Public_ExecuteSqlService.Instance.NoStore_TBLEvent_GetSql();
                        sql1 = string.Format(tempSql1, EquipmentTemplateId, sigId);
                    }
                    else
                    {
                        sql1 = string.Format("delete from TBL_Event where EquipmentTemplateId={0} and EventId={1}", EquipmentTemplateId, sigId);
                    }
                    sb.AppendLine(DBHelper.GetExecSql(sql1));
                    if (CommonUtils.IsNoProcedure)
                    {
                        string tempSql2 = Public_ExecuteSqlService.Instance.NoStore_TBLEventCondition_GetSql();
                        sql2 = string.Format(tempSql2, EquipmentTemplateId, sigId);
                    }
                    else
                    {
                        sql2 = string.Format("delete from TBL_EventCondition where EquipmentTemplateId={0} and EventId={1}", EquipmentTemplateId, sigId);
                    }
                    sb.AppendLine(DBHelper.GetExecSql(sql2));
                }
                catch (Exception ex)
                {
                    logger.ErrorFormat("EventBiz.DeleteList();EquipmentTemplateId={0},TSignalCount={1};Error={2}",
                        EquipmentTemplateId, eventIds.Count, ex.Message);
                    logger.Error(ex.StackTrace);
                    //return false;
                }
                var sqlAll = sb.ToString();
                DBHelper.ExecuteNonQuery(sqlAll);

            }
            return true;
        }

    }
}
