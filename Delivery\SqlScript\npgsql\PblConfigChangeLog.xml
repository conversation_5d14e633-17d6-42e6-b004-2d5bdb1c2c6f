﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
    <procedure owner="" name="ConfigChangeLog_GetOldCount" grant="">
      <parameters>
        <parameter name="ObjectId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ConfigId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EditType" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT count(*) FROM TBL_ConfigChangeMicroLog T WHERE T.ObjectId = @ObjectId AND T.ConfigId = @ConfigId AND T.EditType = @EditType</body>
    </procedure>
    <procedure owner="" name="ConfigChangeLog_UpdateChangeLog" grant="">
      <parameters>
        <parameter name="ObjectId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ConfigId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EditType" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>UPDATE TBL_ConfigChangeMicroLog SET UpdateTime = now() WHERE ObjectId =@ObjectId AND ConfigId = @ConfigId AND EditType = @EditType </body>
    </procedure>
    <procedure owner="" name="ConfigChangeLog_DelChangeLog" grant="">
      <parameters>
        <parameter name="ObjectId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ConfigId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>DELETE FROM TBL_ConfigChangeMicroLog WHERE ObjectId = @ObjectId AND ConfigId = @ConfigId</body>
    </procedure>
    <procedure owner="" name="ConfigChangeLog_InsertChangeLog" grant="">
      <parameters>
        <parameter name="ObjectId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ConfigId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EditType" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>INSERT INTO TBL_ConfigChangeMicroLog VALUES (@ObjectId,@ConfigId,@EditType,now());</body>
    </procedure>
    <procedure owner="" name="ConfigChangeLog_GetOldCountC" grant="">
      <parameters>
        <parameter name="ObjectId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ConfigId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EditType" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT count(*) FROM C_ConfigChangeMicroLog T WHERE T.ObjectId = @ObjectId AND T.ConfigId = @ConfigId AND T.EditType = @EditType</body>
    </procedure>
    <procedure owner="" name="ConfigChangeLog_UpdateChangeLogC" grant="">
      <parameters>
        <parameter name="ObjectId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ConfigId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EditType" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>UPDATE C_ConfigChangeMicroLog SET UpdateTime = now() WHERE ObjectId = @ObjectId AND ConfigId = @ConfigId AND EditType = @EditType</body>
    </procedure>
    <procedure owner="" name="ConfigChangeLog_InsertChangeLogC" grant="">
      <parameters>
        <parameter name="ObjectId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ConfigId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EditType" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>INSERT INTO C_ConfigChangeMicroLog VALUES (@ObjectId,@ConfigId,@EditType,now())</body>
    </procedure>
    <procedure owner="" name="ConfigChangeLog_GetMapInfo" grant="">
      <parameters>
        <parameter name="ConfigId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EditType" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT T.MacroConfigId,T.MacroEditType,T.IdConvertRule FROM TBL_ConfigChangeMap T 
                    WHERE T.MicroConfigId = @ConfigId AND T.MicroEditType = @EditType</body>
    </procedure>
    <procedure owner="" name="ConfigChangeLog_GetOldCount_MacroLog" grant="">
      <parameters>
        <parameter name="MacroObjectId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="MacroConfigId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="MacroEditType" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>SELECT count(*) FROM TBL_ConfigChangeMacroLog T WHERE T.ObjectId = @MacroObjectId AND T.ConfigId = @MacroConfigId AND T.EditType = @MacroEditType</body>
    </procedure>
    <procedure owner="" name="ConfigChangeLog_UpdateChangeLog_MacroLog" grant="">
      <parameters>
        <parameter name="MacroObjectId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="MacroConfigId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="MacroEditType" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>UPDATE TBL_ConfigChangeMacroLog SET UpdateTime = now() WHERE ObjectId = @MacroObjectId AND ConfigId = @MacroConfigId AND EditType = @MacroEditType</body>
    </procedure>
    <procedure owner="" name="ConfigChangeLog_DelChangeLog_MacroLog" grant="">
      <parameters>
        <parameter name="MacroObjectId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="MacroConfigId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>DELETE FROM TBL_ConfigChangeMacroLog WHERE ObjectId = @MacroObjectId AND ConfigId = @MacroConfigId</body>
    </procedure>
    <procedure owner="" name="ConfigChangeLog_InsertChangeLog_MacroLog" grant="">
      <parameters>
        <parameter name="MacroObjectId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="MacroConfigId" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="MacroEditType" type="int" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>INSERT INTO TBL_ConfigChangeMacroLog VALUES (@MacroObjectId,@MacroConfigId,@MacroEditType,now())</body>
    </procedure>
    
    
	</procedures>
</root>
