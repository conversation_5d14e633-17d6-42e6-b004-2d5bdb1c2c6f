﻿using BDSTool.DBUtility;
using BDSTool.Entity.S2;
using Common.Logging.Pro;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.Entity.DRHelper
{
    public class TBL_EquipmentHelper
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static TBL_Equipment FromDataRow(DataRow row) {
            var e = new TBL_Equipment();
            try {
                e.StationId =   int.Parse(row["StationId"].ToString());
                e.EquipmentId = int.Parse(row["EquipmentId"].ToString());
                e.EquipmentName = row["EquipmentName"].ToString();
                e.EquipmentNo = row["EquipmentNo"].ToString();
                e.EquipmentModule = row["EquipmentModule"].ToString();
                e.EquipmentStyle = row["EquipmentStyle"].ToString();
                e.AssetState = SHelper.ToInt(row["AssetState"]);
                e.Price =  SHelper.ToInt(row["Price"].ToString());
                e.UsedLimit =  SHelper.ToInt(row["UsedLimit"]);
                e.UsedDate =  SHelper.ToDateTimeNullable(row["UsedDate"]);
                e.BuyDate =  SHelper.ToDateTimeNullable(row["BuyDate"]);
                e.Vendor = row["Vendor"].ToString();
                e.Unit = row["Unit"].ToString();
                e.EquipmentCategory =  SHelper.ToInt(row["EquipmentCategory"]);
                e.EquipmentType =  SHelper.ToInt(row["EquipmentType"]);
                e.EquipmentClass =  SHelper.ToInt(row["EquipmentClass"]);
                e.EquipmentState =  SHelper.ToInt(row["EquipmentState"]);
                e.EventExpression =  row["EventExpression"].ToString();
                e.StartDelay =  SHelper.ToDoubleNullable(row["StartDelay"]);
                e.EndDelay =  SHelper.ToDoubleNullable(row["EndDelay"]);
                e.Property = row["Property"].ToString();
                e.Description = row["Description"].ToString();
                e.EquipmentTemplateId = SHelper.ToInt(row["EquipmentTemplateId"]);
                e.HouseId =  SHelper.ToInt(row["HouseId"]);
                e.MonitorUnitId =  SHelper.ToInt(row["MonitorUnitId"]);
                e.WorkStationId =  SHelper.ToInt(row["WorkStationId"]);
                e.SamplerUnitId =  SHelper.ToInt(row["SamplerUnitId"]);
                e.DisplayIndex =  SHelper.ToInt(row["DisplayIndex"]);
                e.ConnectState =  SHelper.ToInt(row["ConnectState"]);
                e.UpdateTime =  SHelper.ToDateTime(row["UpdateTime"]);
                e.ParentEquipmentId = row["ParentEquipmentId"].ToString();
                e.RatedCapacity = row["RatedCapacity"].ToString();
                e.InstalledModule = row["InstalledModule"].ToString();
                e.ProjectName = row["ProjectName"].ToString();
                e.ContractNo = row["ContractNo"].ToString();
                e.InstallTime =  SHelper.ToDateTimeNullable(row["InstallTime"].ToString());
                e.EquipmentSN = row["EquipmentSN"].ToString();
                e.SO = row["SO"].ToString();

            }
            catch (Exception ex) {
                logger.ErrorFormat("TDevConfEx.FromDataRow();error={0}", ex.Message);
                logger.Error(ex.StackTrace);
                return null;
            }
            return e;
        }
    }
}
