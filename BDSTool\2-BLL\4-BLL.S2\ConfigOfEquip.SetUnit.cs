﻿


using BDSTool.DBUtility.Common;
using Common.Logging.Pro;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace BDSTool.BLL.S2 {
    public partial class ConfigHelper {
        //UPDATE TBL_Signal
        //SET Unit=d.ExtendFiled1
        //FROM TBL_Signal s, TBL_StandardDicSig  d
        //WHERE 
        //EquipmentTemplateId=11000156
        //AND d.StandardType=1  AND d.StandardDicId/1000= s.SignalId/1000
        //-----------------------------------------------------
        //SELECT s.Unit,s .* 
        //FROM TBL_Signal s 
        //WHERE 
        //EquipmentTemplateId=11000156
        //ORDER BY s.EquipmentTemplateId, s.SignalId
        public static void UpdateUnitOfSignal(int EquipmentTemplateId) {
            //20170519 
            // 1-总是更新所有的基类信息。
            // 2- 映射不区分局站类型（不可能存在 基类+多个局站类型 对应 不同 字典的情况 ）。适应江苏移动的字典
            // 3- 20170824 fix bug -000基类不存在
            //var sql2 = string.Format("UPDATE TBL_Signal SET BaseTypeId=floor(m.BaseTypeId/1000)*1000+RIGHT(SignalId,3) FROM TBL_Signal s, TBL_SignalBaseMap m WHERE s.EquipmentTemplateId={0} AND s.BaseTypeId IS NULL AND m.StandardType=1 AND m.StationBaseType=0 AND m.StandardDicId/1000=s.SignalId/1000", EquipmentTemplateId);
            //var sql2 = string.Format("UPDATE TBL_Signal SET BaseTypeId=floor(m.BaseTypeId/1000)*1000+RIGHT(SignalId,3) FROM TBL_Signal s, TBL_SignalBaseMap m WHERE s.EquipmentTemplateId={0} AND m.StandardType=1 AND m.StandardDicId/1000=s.SignalId/1000", EquipmentTemplateId);
            string sql2;
            if (CommonUtils.IsNoProcedure)
            {
                string temp = Public_ExecuteSqlService.Instance.NoStore_TBLSignal_UpdateSql_UpdateUnitOfSignal();
                sql2 = string.Format(temp, EquipmentTemplateId);
            }
            else 
            {
                sql2 = string.Format("UPDATE TBL_Signal SET Unit=d.ExtendFiled1 FROM TBL_Signal s, TBL_StandardDicSig  d WHERE EquipmentTemplateId={0} AND d.StandardType=1  AND d.StandardDicId/1000= s.SignalId/1000", EquipmentTemplateId);
            }
            DbConfigPara para = DBHelper.GetDbConfig();
            if (para.ProviderName.ToLower() == "mysql")
            {
                if (CommonUtils.IsNoProcedure)
                {
                    string temp = Public_ExecuteSqlService.Instance.NoStore_TBLSignal_UpdateSql_UpdateUnitOfSignal_mysql();
                    sql2 = string.Format(temp, EquipmentTemplateId);
                }
                else
                {
                    sql2 = string.Format("UPDATE TBL_Signal s INNER JOIN TBL_StandardDicSig  d ON s.EquipmentTemplateId={0} AND d.StandardType=1  AND d.StandardDicId/1000= s.SignalId/1000 SET s.Unit=d.ExtendFiled1  ", EquipmentTemplateId);
                }
            }

            logger.InfoFormat("UpdateUnitOfSignal(); EquipmentTemplateId={0}", EquipmentTemplateId);

            DBHelper.ExecuteNonQuery(sql2);
        }
    }
}
