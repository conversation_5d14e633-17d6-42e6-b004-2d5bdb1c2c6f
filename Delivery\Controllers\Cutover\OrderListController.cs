﻿using BLL;
using DM.TestOrder.DAL;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web.Http;
using testorder.dal;
using DM.TestOrder.Common.DBA;


namespace DM.TestOrder.Controllers {
        
    public class OrderListController : BaseApiController{

        //http://localhost/orderapi/OrderList

        public HttpResponseMessage  POST([FromBody]JObject value) {

            var s = JsonConvert.SerializeObject(value);
            var queryParam = JsonConvert.DeserializeObject<QueryParamTestOrderQuery>(s);

            var dt = WoOrderListDal.Query(queryParam);
            var sReturn = JsonConvert.SerializeObject(dt);

            return new HttpResponseMessage() {
                Content = new StringContent(sReturn, Encoding.UTF8, "application/json"),
            };
        }

        public HttpResponseMessage Get(int userid) {
            var sql = string.Format("WO_TestOrder_Stats {0}", userid);
            var dt = DBHelper.GetTable(sql);
            var sReturn = JsonConvert.SerializeObject(dt);


            return new HttpResponseMessage() {
                Content = new StringContent(sReturn, Encoding.UTF8, "application/json"),
            };
        }

        public HttpResponseMessage Get() {

            var dt = DBHelper.GetTable("WO_TestOrder_Query");
            var sReturn = JsonConvert.SerializeObject(dt);


            return new HttpResponseMessage() {
                Content = new StringContent(sReturn, Encoding.UTF8, "application/json"),
            };
        }
    }
}
