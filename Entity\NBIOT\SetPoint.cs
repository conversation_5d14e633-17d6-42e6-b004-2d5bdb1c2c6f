﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class SetPoint : BMessage
    {
        //[Newtonsoft.Json.JsonProperty(Order = 1)]
        //public List<TSemaphoreDel<TPointValue>> Value { get; set; }

        [JsonProperty("Value")]
        public List<Device> Devices { get; set; }

        [JsonIgnore]
        public int UserId { get; set; }

        [JsonIgnore]
        public DateTime StartTime { get; set; }

        [JsonIgnore]
        public int ControlId { get; set; }

        public SetPoint() : base()
        {
            MessageType = (int)BMessageType.SET_POINT;
        }

        /// <summary>
        /// 根据JS文件格式，定义一个设备实体
        /// </summary>
        /// <param name="fsuId">FSUID号</param>
        /// <param name="devices">写监控点的设备实体</param>
        public SetPoint(string fsuId, List<Device> devices):base()
        {
            messageType = (int)BMessageType.SET_POINT;

            FSUID = fsuId;
            Devices = devices;
        }

        //public SetPoint(string fsuId, List<TSemaphoreDel<TPointValue>> value) : base()
        //{
        //    MessageType = (int)BMessageType.SET_POINT;
        //    FSUID = fsuId;
        //    Value = value;
        //}

        public override string Serialize()
        {
            string result = string.Empty;
            try
            {
                JsonSerializerSettings settings = new JsonSerializerSettings();
                settings.ContractResolver = new CustomContractResolver();
                TIotEntity iotEntity = new TIotEntity(this);
                result = JsonConvert.SerializeObject(iotEntity, settings);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetPoint.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetPoint.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
            }
            return result;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            string str = String.Format("{0},{1},{2}:", MessageId, (BMessageType)MessageType, FSUID);
            sb.Append(str);

            foreach (Device device in Devices)
            {
                sb.AppendFormat("[{0}]",device.ToString());
            }
            return sb.ToString();
        }
    }
}