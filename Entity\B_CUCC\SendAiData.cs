﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class SendAiData : BMessage
    {
        public string ReportTime { get; set; }
        public List<Device> LDevice { get; set; }
        public SendAiData(string suids, string surids, string reportTime, List<Device> ldevice) : base()
        {
            MessageType = (int)BMessageType.SEND_AIDATA;
            SUId = suids;
            SURId = surids;
            ReportTime = reportTime;
            LDevice = ldevice;
        }
        public SendAiData():base()
        {
            MessageType = (int)BMessageType.SEND_AIDATA;
        }
        public static SendAiData Deserialize(XmlDocument xmlDoc)
        {
            SendAiData sendAiData = null;
            string errMsg = string.Empty;
            string suid = string.Empty;
            string surid = string.Empty;
            try
            {
                suid = xmlDoc.SelectSingleNode("/Request/Info/SUId").InnerText.Trim();
                surid = xmlDoc.SelectSingleNode("/Request/Info/SURId").InnerText.Trim();
                string reportTime= xmlDoc.SelectSingleNode("/Request/Info/ReportTime").InnerText.Trim();
                XmlNodeList nodes = xmlDoc.SelectNodes("/Request/Info/Values/DeviceList/Device");
                List<Device> ldevice = new List<Device>();
                foreach (XmlElement node in nodes)
                {
                    Device dev = new Device();
                    dev.Id = node.GetAttribute("Id");
                    dev.RId = node.GetAttribute("RId");
                    XmlNodeList ndlists = node.SelectNodes("Signal");
                    dev.SignalList = new List<Signal>();
                    foreach (XmlElement nd in ndlists)
                    {
                        Signal sdv = new Signal();
                        sdv.Id = nd.GetAttribute("Id");
                        string strValue = nd.GetAttribute("Value").Trim();
                        if (!string.IsNullOrEmpty(strValue))
                        {
                            sdv.Value = float.Parse(strValue);
                        }
                        else
                        {
                            errMsg = "SEND_AIDATA Signal Value is nullOrEmpty";
                            break;
                        }
                        dev.SignalList.Add(sdv);
                    }
                    ldevice.Add(dev);
                }
                if (errMsg != string.Empty)
                {
                    entityLogger.ErrorFormat("SendAiData.Deserialize():{0}", errMsg);
                    sendAiData = GetErrorEntity(suid, surid, errMsg);
                }
                else
                {
                    sendAiData = new SendAiData(suid, surid, reportTime, ldevice);
                }
                entityLogger.DebugFormat("SendAiData.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                sendAiData.StringXML = xmlDoc.InnerXml;
                return sendAiData;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SendAiData.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SendAiData.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                //直接返回带回复包的错误实体
                sendAiData = GetErrorEntity(suid, surid, ex.Message);
                sendAiData.StringXML = xmlDoc.InnerXml;
                return sendAiData;
            }
        }

        /// <summary>
        /// 反序列化异常时，构造带错误信息的实体
        /// </summary>
        /// <param name="errorMsg">解析错误信息</param>
        /// <returns></returns>
        private static SendAiData GetErrorEntity(string suId, string surId, string errorMsg)
        {
            SendAiData errorEntity = new SendAiData();
            SendAiDataAck ackEntity = new SendAiDataAck(suId, surId, EnumResult.FAILURE);
            ackEntity.ErrorMsg = errorMsg;
            errorEntity.ErrorMsg = ackEntity.Serialize();
            return errorEntity;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            if (LDevice.Count > 0)
            {
                foreach (Device de in LDevice)
                {
                    sb.AppendFormat("{0},{1},{2},{3},{4},{5}\n", MessageId, (BMessageType)MessageType, MessageType, SUId, SURId, de.PartValueToString());
                }
            }
            else
            {
                sb.AppendFormat("{0},{1},{2},{3},{4},Device is null", MessageId, (BMessageType)MessageType, MessageType, SUId, SURId);
            }
            return sb.ToString();
        }
    }
}
