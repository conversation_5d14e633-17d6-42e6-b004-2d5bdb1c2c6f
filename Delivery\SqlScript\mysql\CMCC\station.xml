﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <procedures>
    <procedure owner="" name="StationInfo_userId" grant="">
      <parameters>
        <parameter name="LogonId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        SELECT a.UserId iUserId FROM TBL_Account a WHERE a.LogonId = @LogonId
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="StationInfo_userRole" grant="">
      <parameters>
        <parameter name="iUserId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        SELECT 'X' FROM TBL_UserRoleMap r WHERE r.UserId = @iUserId AND r.RoleId = -1
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="StationInfo_structureInfo" grant="">
      <body>
        <![CDATA[ 
        SELECT Convert(a.StructureId,CHAR) sCenterId,a.StructureName CenterName FROM TBL_StationStructure a 
        WHERE a.StructureType = 2 AND a.ParentStructureId = 0
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="StationInfo_GetData1" grant="">
      <parameters>
        <parameter name="StructureId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationCategory" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationStatus" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="sCenterId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="CenterName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StartTime" type="datetime" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EndTime" type="datetime" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="iUserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
        <replace keyName ="WhereStructureId"> and b.LevelPath LIKE @StructureId</replace>
        <replace keyName ="WhereStationCategory"> and a.StationCategory = @StationCategory</replace>
        <replace keyName ="WhereSWStationName"> and i.StationName like @StationName</replace>
        <replace keyName ="WhereStationCode"> and a.StationCode like @StationCode</replace>
        <replace keyName ="WhereStatus"> and a.StationStatus = @StationStatus</replace>
        <replace keyName="WhereApplyTime">
          <![CDATA[ AND a.ApplyTime >= @StartTime AND a.ApplyTime <= @EndTime ]]>
        </replace>
        <replace keyName ="RoleMapNotNull">
          SELECT DISTINCT T.UserId FROM WR_StationManagement T
        </replace>
        <replace keyName ="RoleMapNull">
          SELECT DISTINCT T.UserId FROM TBL_UserRoleMap T WHERE T.RoleId IN 
          (SELECT RoleId FROM TBL_UserRoleMap WHERE TBL_UserRoleMap.UserId = @iUserId)
        </replace>
        <replace keyName ="SqlStr1">
          <![CDATA[ SELECT @sCenterId CenterId, @CenterName CenterName,
		      a.WRStationId,
		      a.StructureId, b.StructureName,a.StationCode,i.StationName,
		      a.StationCategory , c.ItemValue StationCategoryName,
		      a.StationStatus, d.ItemValue StatusName ,
		      a.Address,a.UserId, a.SWUserName UserName,
		      a.ApplyTime, a.ApproveTime,
		      a.Province , f.ItemValue ProvinceName,
		      a.City , g.ItemValue CityName,
		      a.County , h.ItemValue CountyName,
		      a.SWStationId, a.RejectCause, a.Remark, i.Latitude, i.Longitude, a.ContractNo, a.ProjectName
	        FROM WR_StationManagement a ]]>
        </replace>
      </replaces>
      <body>
        <![CDATA[
        $[SqlStr1]
        INNER JOIN TBL_StationStructure b ON a.StructureId = b.StructureId
	      INNER JOIN WR_DataItem c ON a.StationCategory = c.ItemId AND c.EntryId = 6
	      INNER JOIN WR_DataItem d ON a.StationStatus = d.ItemId AND d.EntryId = 5 
	      INNER JOIN WR_DataItem f ON a.Province = f.ItemId AND f.EntryId = 1
	      INNER JOIN WR_DataItem g ON a.City = g.ItemId AND g.EntryId = 2
	      INNER JOIN WR_DataItem h ON a.County = h.ItemId AND h.EntryId = 3 
	      INNER JOIN TBL_Station i ON a.SWStationId = i.StationId
	      INNER JOIN ( $[RoleMapNotNull]$[RoleMapNull] ) j ON a.UserId = j.UserId
	      WHERE a.StationStatus =3 
        $[WhereApplyTime] 
        $[WhereStructureId]
        $[WhereStationCategory]
        $[WhereSWStationName]
        $[WhereStationCode]
        $[WhereStatus]
      ]]>
      </body>
    </procedure>
    <procedure owner="" name="StationInfo_GetData2" grant="">
      <parameters>
        <parameter name="StructureId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationCategory" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationStatus" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="sCenterId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="CenterName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StartTime" type="datetime" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EndTime" type="datetime" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="iUserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
        <replace keyName ="WhereStructureId"> and b.LevelPath LIKE @StructureId</replace>
        <replace keyName ="WhereStationCategory"> and a.StationCategory = @StationCategory</replace>
        <replace keyName ="WhereStationName"> and a.StationName like @StationName</replace>
        <replace keyName ="WhereStationCode"> and a.StationCode like @StationCode</replace>
        <replace keyName ="WhereStatus"> and a.StationStatus = @StationStatus</replace>
        <replace keyName="WhereApplyTime">
          <![CDATA[ AND a.ApplyTime >= @StartTime AND a.ApplyTime <= @EndTime ]]>
        </replace>
        <replace keyName ="RoleMapNotNull">
          SELECT DISTINCT T.UserId FROM WR_StationManagement T
        </replace>
        <replace keyName ="RoleMapNull">
          SELECT DISTINCT T.UserId FROM TBL_UserRoleMap T WHERE T.RoleId IN
          (SELECT RoleId FROM TBL_UserRoleMap WHERE TBL_UserRoleMap.UserId = @iUserId)
        </replace>
        <replace keyName ="SqlStr2">
          <![CDATA[ SELECT @sCenterId CenterId, @CenterName CenterName,
		      a.WRStationId,
		      a.StructureId, b.StructureName,a.StationCode,a.StationName,
		      a.StationCategory , c.ItemValue StationCategoryName,
		      a.StationStatus, d.ItemValue StatusName ,
		      a.Address, a.UserId, a.SWUserName UserName,
		      a.ApplyTime, a.ApproveTime,
		      a.Province , f.ItemValue ProvinceName,
		      a.City , g.ItemValue CityName,
		      a.County , h.ItemValue CountyName,
		      a.SWStationId, a.RejectCause, a.Remark, 0.00 Latitude, 0.00 Longitude, a.ContractNo, a.ProjectName		
	        FROM WR_StationManagement a ]]>
        </replace>
      </replaces>
      <body>
        <![CDATA[
        $[SqlStr2]
        INNER JOIN TBL_StationStructure b ON a.StructureId = b.StructureId
	      INNER JOIN WR_DataItem c ON a.StationCategory = c.ItemId AND c.EntryId = 6
	      INNER JOIN WR_DataItem d ON a.StationStatus = d.ItemId AND d.EntryId = 5
	      INNER JOIN WR_DataItem f ON a.Province = f.ItemId AND f.EntryId = 1
	      INNER JOIN WR_DataItem g ON a.City = g.ItemId AND g.EntryId = 2
	      INNER JOIN WR_DataItem h ON a.County = h.ItemId AND h.EntryId = 3 
        INNER JOIN ( $[RoleMapNotNull]$[RoleMapNull] ) j ON a.UserId = j.UserId
	      WHERE a.StationStatus !=3 
        $[WhereApplyTime] 
        $[WhereStructureId]
        $[WhereStationCategory]
        $[WhereStationName]
        $[WhereStationCode]
        $[WhereStatus]
      ]]>
      </body>
    </procedure>
	  
   <procedure owner="" name="SP_RejectWRStation_StationStatus" grant="">
      <parameters>
		  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>      
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
		    
		    SELECT  T.StationStatus FROM WR_StationManagement T WHERE T.WRStationId = @WRStationId;	  
        
      ]]>
      </body>
    </procedure>
	<procedure owner="" name="SP_RejectWRStation_Update" grant="">
      <parameters>
        <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="RejectCause" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
		    set sql_safe_updates = 0;
			  
		    UPDATE WR_StationManagement
	        SET WR_StationManagement.StationStatus = 2, WR_StationManagement.RejectCause = @RejectCause
	        WHERE WR_StationManagement.WRStationId = @WRStationId;
	    
	        UPDATE WR_HouseManagement
	        SET WR_HouseManagement.HouseStatus = 2,WR_HouseManagement.RejectCause = concat('级联退回:' , @RejectCause)
	        WHERE WR_HouseManagement.WRStationId = @WRStationId;
	
	        UPDATE WR_FsuManagement,WR_HouseManagement
	        SET WR_FsuManagement.FsuStatus = 2,WR_FsuManagement.RejectCause = concat('级联退回:' , @RejectCause)
	        WHERE WR_FsuManagement.WRHouseId = WR_HouseManagement.WRHouseId
	        AND WR_HouseManagement.WRStationId = @WRStationId;
			
	        set sql_safe_updates = 1;        
      ]]>
      </body>
    </procedure>

    <procedure owner="" name="SP_Del_Station_GetUserId" grant="">
      <parameters>
		  <parameter name="LogonId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>      
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
		    SELECT a.UserId FROM TBL_Account a WHERE a.LogonId = @LogonId ;	        
      ]]>
      </body>
    </procedure>
	  
   <procedure owner="" name="SP_Del_Station_isAdminRole" grant="">
      <parameters>
		  <parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>      
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[

            SELECT 'X' FROM TBL_UserRoleMap a 
	        INNER JOIN TBL_UserRoleRight b ON a.RoleId = b.RoleId
	        WHERE b.OperationId = -1 AND b.OperationType = 1 AND a.UserId = @UserId
            union all 
            SELECT 'X' FROM TBL_UserRoleMap a 
	        INNER JOIN TBL_UserRoleRight b ON a.RoleId = b.RoleId
	        WHERE b.OperationId = -1 AND b.OperationType = 2 AND a.UserId = @UserId
            union all 
            SELECT 'X' FROM TBL_UserRoleMap a 
	        INNER JOIN TBL_UserRoleRight b ON a.RoleId = b.RoleId
	        WHERE b.OperationId = -1 AND b.OperationType = 3 AND a.UserId = @UserId
            union all 
	        SELECT 'X' FROM TBL_UserRoleMap a 
	        INNER JOIN TBL_UserRoleRight b ON a.RoleId = b.RoleId
	        WHERE b.OperationId = -1 AND b.OperationType = 4 AND a.UserId = @UserId
        ]]>
      </body>
    </procedure> 
	  <procedure owner="" name="SP_Del_Station_GetStationId" grant="">
      <parameters>
		  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>      
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[

          SELECT s.SWStationId,s.StationStatus FROM WR_StationManagement s WHERE s.WRStationId = @WRStationId;	
		  
        ]]>
      </body>
    </procedure> 
	  
	  <procedure owner="" name="SP_Del_Station_GetFsuId" grant="">
      <parameters>
		  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>      
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[

          SELECT c.WRFsuId FROM WR_StationManagement a 
	        INNER JOIN WR_HouseManagement b ON a.WRStationId = b.WRStationId
	        INNER JOIN WR_FsuManagement c ON b.WRHouseId = c.WRHouseId
	        WHERE a.WRStationId = @WRStationId
		  
        ]]>
      </body>
    </procedure> 
	  
	 <procedure owner="" name="SP_Del_Station_GetHouseId" grant="">
      <parameters>
		  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>      
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[

         SELECT b.WRHouseId FROM WR_StationManagement a 
	        INNER JOIN WR_HouseManagement b ON a.WRStationId = b.WRStationId
	        WHERE a.WRStationId = @WRStationId
		  
        ]]>
      </body>
    </procedure> 
	  
	 <procedure owner="" name="SP_Del_Station_GetStationName" grant="">
      <parameters>
		  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>      
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[

        SELECT s.StationName FROM WR_StationManagement s WHERE s.WRStationId = @WRStationId;
		
        ]]>
      </body>
    </procedure>
	  
	  <procedure owner="" name="SP_Del_Station_DelStationManagement" grant="">
      <parameters>
		  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>      
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
		
            DELETE FROM WR_StationManagement WHERE WR_StationManagement.WRStationId = @WRStationId;
		
        ]]>
      </body>
    </procedure>
	   <procedure owner="" name="SP_Del_Station_DelStationCMCC" grant="">
      <parameters>
		  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>      
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
		
          DELETE FROM TBL_StationCMCC WHERE TBL_StationCMCC.StationId = @SWStationId;
		  
         ]]>
      </body>
    </procedure>
  <procedure owner="" name="SP_Del_Station_DelMonitorUnitCMCC" grant="">
      <parameters>
		  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>      
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[

            DELETE FROM TSL_MonitorUnitCMCC WHERE TSL_MonitorUnitCMCC.StationId = @SWStationId;

            ]]>
      </body>
    </procedure>

     <procedure owner="" name="SP_Del_Station_DelEquipmentCMCC" grant="">
      <parameters>
		  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>      
      </parameters>
      <replaces>
      </replaces>
      <body>
        <![CDATA[
		
	          DELETE FROM TBL_EquipmentCMCC WHERE TBL_EquipmentCMCC.StationId = @SWStationId;
			  
         ]]>
      </body>
    </procedure>

    <procedure owner="" name="SP_Get_WRStationCondition" grant="">
      <parameters>
        <parameter name="StructureId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationCategory" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StationCode" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="sCenterId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="CenterName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="StartTime" type="datetime" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EndTime" type="datetime" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <replaces>
        <replace keyName ="WhereStructureId"> and b.LevelPath LIKE @StructureId</replace>
        <replace keyName ="WhereStationCategory"> and a.StationCategory = @StationCategory</replace>
        <replace keyName ="WhereStationName"> and a.StationName like @StationName</replace>
        <replace keyName ="WhereSWStationName"> and i.StationName like @StationName</replace>
        <replace keyName ="WhereStationCode"> and a.StationCode like @StationCode</replace>
        <replace keyName="WhereTime">
          <![CDATA[ AND a.ApplyTime >= @StartTime AND a.ApplyTime <= @EndTime ]]>
        </replace>
        <replace keyName ="SqlStr1">
          <![CDATA[ SELECT @sCenterId CenterId, @CenterName CenterName,
		      a.WRStationId,
		      a.StructureId, b.StructureName,a.StationCode,i.StationName,
		      a.StationCategory , c.ItemValue StationCategoryName,
		      a.StationStatus, d.ItemValue StatusName ,
		      a.Address,a.UserId, a.SWUserName UserName,
		      a.ApplyTime,a.ApproveTime,
		      a.Province , f.ItemValue ProvinceName,
		      a.City , g.ItemValue CityName,
		      a.County , h.ItemValue CountyName,
		      a.SWStationId, a.RejectCause, a.Remark, a.ContractNo, a.ProjectName				
	        FROM WR_StationManagement a ]]>
        </replace>
        <replace keyName ="SqlStr2">
          <![CDATA[ SELECT @sCenterId CenterId, @CenterName CenterName,
		      a.WRStationId,
		      a.StructureId, b.StructureName,a.StationCode,a.StationName,
		      a.StationCategory , c.ItemValue StationCategoryName,
		      a.StationStatus, d.ItemValue StatusName ,
		      a.Address,a.UserId, a.SWUserName UserName,
		      a.ApplyTime,a.ApproveTime,
		      a.Province , f.ItemValue ProvinceName,
		      a.City , g.ItemValue CityName,
		      a.County , h.ItemValue CountyName,
		      a.SWStationId, a.RejectCause, a.Remark, a.ContractNo, a.ProjectName		
	        FROM WR_StationManagement a ]]>
        </replace>
      </replaces>
      <body>
        <![CDATA[
       SELECT ROW_NUMBER() over(order by ApplyTime DESC) as RowNumber,
		   T.CenterId, T.CenterName, T.WRStationId,T.StructureId, T.StructureName,T.StationCode,T.StationName,
		   T.StationCategory , T.StationCategoryName, T.StationStatus, T.StatusName,
		   T.Address,T.UserId, T.UserName, T.ApplyTime,T.ApproveTime, T.Province , T.ProvinceName,
		   T.City , T.CityName, T.County , T.CountyName, T.SWStationId,
		   T.RejectCause, T.Remark, T.ContractNo, T.ProjectName		
	     FROM (
        $[SqlStr1]
        INNER JOIN TBL_StationStructure b ON a.StructureId = b.StructureId
	      INNER JOIN WR_DataItem c ON a.StationCategory = c.ItemId AND c.EntryId = 6
	      INNER JOIN WR_DataItem d ON a.StationStatus = d.ItemId AND d.EntryId = 5
	      INNER JOIN WR_DataItem f ON a.Province = f.ItemId AND f.EntryId = 1
	      INNER JOIN WR_DataItem g ON a.City = g.ItemId AND g.EntryId = 2
	      INNER JOIN WR_DataItem h ON a.County = h.ItemId AND h.EntryId = 3 
	      INNER JOIN TBL_Station i ON a.SWStationId = i.StationId
	      WHERE a.StationStatus =3 
        $[WhereTime] $[WhereStructureId] $[WhereStationCategory]  $[WhereSWStationName] $[WhereStationCode]
        union all
        $[SqlStr2]
        INNER JOIN TBL_StationStructure b ON a.StructureId = b.StructureId
	      INNER JOIN WR_DataItem c ON a.StationCategory = c.ItemId AND c.EntryId = 6
	      INNER JOIN WR_DataItem d ON a.StationStatus = d.ItemId AND d.EntryId = 5 
	      INNER JOIN WR_DataItem f ON a.Province = f.ItemId AND f.EntryId = 1
	      INNER JOIN WR_DataItem g ON a.City = g.ItemId AND g.EntryId = 2
	      INNER JOIN WR_DataItem h ON a.County = h.ItemId AND h.EntryId = 3 
	      WHERE a.StationStatus =1
        $[WhereTime] $[WhereStructureId] $[WhereStationCategory]  $[WhereStationName] $[WhereStationCode]
       ) AS T ORDER BY T.ApplyTime DESC; 
      ]]>
      </body>
    </procedure>
	  <procedure owner="" name="SP_ApproveWRStation_Select_StationManagement" grant="">
		  <parameters>
			  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
	         SELECT  WR_StationManagement.StationStatus,WR_StationManagement.UserId myStatus, UserId
	         FROM WR_StationManagement WHERE WR_StationManagement.WRStationId = @WRStationId;
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRStation_Update_StationManagement" grant="">
		  <parameters>
			  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="currentTime" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
	         UPDATE WR_StationManagement SET WR_StationManagement.StationStatus = 3,
		     WR_StationManagement.ApproveTime = @currentTime,WR_StationManagement.RejectCause = '',WR_StationManagement.SWStationId = @SWStationId 
	         WHERE WR_StationManagement.WRStationId = @WRStationId;
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRStation_Select_StationStructure" grant="">
		  <parameters>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
	         SELECT TBL_StationStructure.StructureId CenterId FROM TBL_StationStructure 
	         WHERE TBL_StationStructure.StructureType = 2 AND TBL_StationStructure.ParentStructureId = 0;
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRStation_Insert_Station" grant="">
		  <parameters>
			  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="currentTime" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="CenterId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
			 INSERT INTO TBL_Station (StationId, StationName, ConnectState, UpdateTime, StationCategory, StationGrade, StationState, 
	         CenterId, Description, ContractNo, ProjectName, InstallTime, ContainNode,`Enable`)
	         SELECT @SWStationId StationId, a.StationName, 2 ConnectState, @currentTime UpdateTime, 
             CASE WHEN b.SiteWeb_ItemId IS NULL THEN 1 ELSE b.SiteWeb_ItemId END  StationCategory,
		     1 StationGrade, 1 StationState,  @CenterId CenterId, '来源于交维' Description, 
		     a.ContractNo, a.ProjectName, a.ApplyTime, 0 ContainNode, 1 `Enable`
	         FROM WR_StationManagement a
	         LEFT JOIN WO_DicStationTypeMap  b ON b.WR_ItemId = a.StationCategory   
	         WHERE a.WRStationId = @WRStationId; 
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRStation_Insert_StationCMCC" grant="">
		  <parameters>
			  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
	        INSERT INTO TBL_StationCMCC (StationId, SiteID, SiteName, Description)
	        SELECT SWStationId, StationCode, StationName, Remark
	        FROM WR_StationManagement WHERE WR_StationManagement.WRStationId = @WRStationId;
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRStation_StructureId" grant="">
		  <parameters>
			  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
	        SELECT WR_StationManagement.StructureId StructureId FROM WR_StationManagement 
	        WHERE WR_StationManagement.WRStationId = @WRStationId; 
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRStation_Insert_StationStructureMap" grant="">
		  <parameters>
			  <parameter name="StructureId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
	        INSERT INTO TBL_StationStructureMap(StructureId, StationId)
	        SELECT @StructureId,@SWStationId;
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRStation_Insert_House" grant="">
		  <parameters>
			  <parameter name="currentTime" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="HouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
	        INSERT INTO TBL_House(HouseId, StationId, HouseName, Description, LastUpdateDate)
            SELECT @HouseId , @SWStationId , '默认局房'  , '默认局房' , @currentTime;
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRStation_Insert_RoomCMCC" grant="">
		  <parameters>
			  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="HouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
	         INSERT INTO TBL_RoomCMCC (StationId,HouseId,RoomName,SiteID)
	         SELECT @SWStationId, @HouseId, '默认局房', T.StationCode
	         FROM WR_StationManagement T WHERE T.WRStationId = @WRStationId;
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRStation_Insert_ConfigChangeMicroLog" grant="">
		  <parameters>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="currentTime" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
	         INSERT INTO TBL_ConfigChangeMicroLog(ObjectId,ConfigId,EditType,UpdateTime)
	         SELECT Convert(@SWStationId,CHAR) ObjectId, 1 ConfigId, 1 EditType, @currentTime UpdateTime;
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRStation_Insert_ConfigChangeMicroLog2" grant="">
		  <parameters>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="currentTime" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
	         INSERT INTO TBL_ConfigChangeMicroLog(ObjectId,ConfigId,EditType,UpdateTime)
	        SELECT concat(Convert(@SWStationId,CHAR),'.1')  ObjectId, 5 ConfigId, 1 EditType, @currentTime UpdateTime;
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRStation_Insert_ConfigChangeMacroLog" grant="">
		  <parameters>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="currentTime" type="string" direction="Input" size="2000" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
	        INSERT INTO TBL_ConfigChangeMacroLog(ObjectId,ConfigId,EditType,UpdateTime)
	        SELECT Convert(@SWStationId,CHAR) ObjectId, 1 ConfigId, 2 EditType, @currentTime UpdateTime;
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRStation_Select_UserRoleMap" grant="">
		  <parameters>
			  <parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
	         SELECT b.OperationId AreaId FROM TBL_UserRoleMap a
             INNER JOIN TBL_UserRoleRight b ON a.RoleId = b.RoleId AND b.OperationType = 2
             WHERE a.UserId = @UserId limit 1;
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRStation_Insert_AreaMap" grant="">
		  <parameters>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="AreaId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
	         INSERT INTO TBL_AreaMap(StationId,AreaId)
	         VALUES (@SWStationId , @AreaId);
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRStation_Insert_StationProjectInfo" grant="">
		  <parameters>
			  <parameter name="WRStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
	         INSERT INTO TBL_StationProjectInfo(StationId,ProjectName,ContractNo,InstallTime)
             SELECT SWStationId, T.ProjectName, T.ContractNo, T.ApplyTime  
             FROM WR_StationManagement T WHERE T.WRStationId = @WRStationId; 
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRStation_wr_syncinfo1" grant="">
		  <parameters>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
			  INSERT INTO wr_syncinfo(StationId, SyncType) VALUES (@SWStationId, 1);
         ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="SP_ApproveWRStation_wr_syncinfo2" grant="">
		  <parameters>
			  <parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="HouseId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
		  </replaces>
		  <body>
			  <![CDATA[
			  INSERT INTO wr_syncinfo(StationId, HouseId, SyncType) VALUES (@SWStationId, @HouseId, 2);	
         ]]>
		  </body>
	  </procedure>
  </procedures>
</root>
