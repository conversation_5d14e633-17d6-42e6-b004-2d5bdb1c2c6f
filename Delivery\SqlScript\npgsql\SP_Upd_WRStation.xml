﻿<?xml version="1.0" encoding="utf-8"?>
<?xml-stylesheet type="text/xsl" href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
		<procedure owner="" name="SP_Upd_WRStation_1" grant="">
			<parameters>
				<parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="WRStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT 'X' FROM WR_StationManagement w
				WHERE w.WRStationId !=  CAST(@WRStationId  AS INTEGER) AND w.StationName = @StationName
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Upd_WRStation_2" grant="">
			<parameters>
				<parameter name="WRStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT  s.StationStatus,s.StationCode,s.SWStationId,s.County,s.StationCategory
				FROM WR_StationManagement s WHERE s.WRStationId = CAST(@WRStationId  AS INTEGER);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Upd_WRStation_3" grant="">
			<parameters>
				<parameter name="WRStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT 
					'StructureId:' || CAST(StructureId AS TEXT) ||
					'-StationName:' || CAST(StationName AS TEXT) ||
					'-StationCategory:' || CAST(StationCategory AS TEXT) ||
					'-Address:' || CAST(Address AS TEXT) ||
					'-Remark:' || CAST(Remark AS TEXT) ||
					'-ContractNo:' || CAST(ContractNo AS TEXT) ||
					'-ProjectName:' || CAST(ProjectName AS TEXT)
				FROM WR_StationManagement
				WHERE WRStationId = CAST(@WRStationId  AS INTEGER);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Upd_WRStation_4" grant="">
			<parameters>
				<parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="WRStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StructureId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StationCategory" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="Province" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="City" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="County" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="Address" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="Remark" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="ContractNo" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="ProjectName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				UPDATE WR_StationManagement
				SET StationName = @StationName,StructureId = CAST(@StructureId  AS INTEGER),StationCategory = CAST(@StationCategory  AS INTEGER),Province = CAST(@Province  AS INTEGER),City = CAST(@City  AS INTEGER),County = CAST(@County  AS INTEGER),Address = @Address,Remark = @Remark,ContractNo = @ContractNo,ProjectName = @ProjectName
				WHERE WR_StationManagement.WRStationId = CAST(@WRStationId  AS INTEGER);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Upd_WRStation_5" grant="">
			<parameters>
				<parameter name="StationCategory" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT d.SiteWeb_ItemId FROM WO_DicStationTypeMap d
				WHERE d.WR_ItemId = CAST(@StationCategory  AS INTEGER);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Upd_WRStation_6" grant="">
			<parameters>
				<parameter name="SWStationCategory" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				UPDATE TBL_Station
				SET StationName = @StationName,StationCategory = CAST(@SWStationCategory  AS INTEGER)
				,UpdateTime = now()
				WHERE TBL_Station.StationId = CAST(@SWStationId  AS INTEGER);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Upd_WRStation_7" grant="">
			<parameters>
				<parameter name="ContractNo" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="ProjectName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				UPDATE TBL_StationProjectInfo
				SET ContractNo = @ContractNo, ProjectName = @ProjectName
				WHERE TBL_StationProjectInfo.StationId = CAST(@SWStationId  AS INTEGER);
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Upd_WRStation_8" grant="">
			<parameters>
				<parameter name="WRStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				
				UPDATE TBL_StationCMCC
				SET 
					SiteID = WR.StationCode,
					SiteName = WR.StationName,
					Description = WR.Remark
				FROM WR_StationManagement WR
				WHERE TBL_StationCMCC.StationId = WR.SWStationId
				  AND WR.WRStationId = CAST(@WRStationId  AS INTEGER);

				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Upd_WRStation_9" grant="">
			<parameters>
				<parameter name="SWStationId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				UPDATE TBL_ConfigChangeMicroLog
				SET EditType = 2, UpdateTime = now()
				WHERE TBL_ConfigChangeMicroLog.ObjectId = CAST(@SWStationId AS TEXT) AND TBL_ConfigChangeMicroLog.ConfigId = 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Upd_WRStation_10" grant="">
			<parameters>
				<parameter name="LogonId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				SELECT  a.UserId,a.UserName
				FROM TBL_Account a WHERE a.LogonId = @LogonId ;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_Upd_WRStation_11" grant="">
			<parameters>
				<parameter name="StructureId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StationName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StationCategory" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="Address" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="Remark" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="ContractNo" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="ProjectName" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StationCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="StationStatus" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="SWStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="County" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="myCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="Province" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="City" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
				<parameter name="WRStationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault="" />
			</parameters>
			<body>
				<![CDATA[
				UPDATE WR_StationManagement
				SET StructureId = CAST(@StructureId  AS INTEGER)
				,StationCode = @myCode
				,StationName = @StationName
				,StationCategory = CAST(@StationCategory  AS INTEGER)
				,StationStatus = 1
				,Province = CAST(@Province  AS INTEGER)
				,ApplyTime = now()::timestamp
				,City = CAST(@City  AS INTEGER)
				,County = CAST(@County  AS INTEGER)
				,Address = @Address
				,Remark = @Remark
				,ContractNo = @ContractNo
				,ProjectName = @ProjectName
				WHERE WR_StationManagement.WRStationId = CAST(@WRStationId  AS INTEGER);
				]]>
			</body>
		</procedure>
	</procedures>
</root>
