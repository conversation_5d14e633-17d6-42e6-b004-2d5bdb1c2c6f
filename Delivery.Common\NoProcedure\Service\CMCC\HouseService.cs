﻿using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace Delivery.Common.NoProcedure.Service.CMCC
{
    public class HouseService
    {

        private static HouseService _instance = null;

        public static HouseService Instance
        {
            get
            {
                if (_instance == null) _instance = new HouseService();
                return _instance;
            }
        }

        public DataTable SP_Get_WRHouseCondition(string structureId, string StationCode, string strBegin, string strEnd, string StationName, string HouseName, string HouseCode, string Status, string LogonId)
        {
            try
            {
                using (DbHelper dbHelper = new DbHelper())
                {
                    ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);

                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    realParams.Add("StartTime", strBegin);
                    realParams.Add("EndTime", strEnd);
                    realParams.Add("StructureId", structureId);
                    realParams.Add("StationName", StationName);
                    realParams.Add("StationCode", StationCode);
                    realParams.Add("HouseName", HouseName);
                    realParams.Add("HouseCode", HouseCode);

                    realParams.Add("WhereTime", " and a.ApplyTime >= '"+strBegin+"' and a.ApplyTime <='"+ strEnd + "'");
                    if (structureId != null && !structureId.Equals(""))
                        realParams.Add("WhereStructure", " and e.LevelPath LIKE '%"+ structureId + "%' ");
                    if (StationName != null && !StationName.Equals(""))
                        realParams.Add("WhereStationName", " and b.StationName like '%"+ StationName + "%'");
                    if (StationName != null && !StationName.Equals(""))
                        realParams.Add("WhereSWStationName", " and f.StationName like '%" + StationName + "%'");
                    if (StationCode != null && !StationCode.Equals(""))
                        realParams.Add("WhereStationCode", " and b.StationCode like '%" + StationCode + "%' ");
                    if (HouseName != null && !HouseName.Equals(""))
                        realParams.Add("WhereHouseName", " and a.HouseName like '%" + HouseName + "%' ");
                    if (HouseName != null && !HouseName.Equals(""))
                        realParams.Add("WhereSWHouseName", " and g.HouseName like '%" + HouseName + "%' ");
                    if (HouseCode != null && !HouseCode.Equals(""))
                        realParams.Add("WhereHouseCode", " and a.HouseCode like '%" + HouseCode + "%' ");

                    //执行语句
                    return execHelper.ExecDataTable("SP_Get_WRHouseCondition", realParams);
                }
            }
            catch (Exception ex)
            {
                Logger.Log(ex);
                return new DataTable();
            }
        }

        public DataTable GetHouseInfo(string structureId, string StationCode, string strBegin, string strEnd, string StationName, string HouseName, string HouseCode, string Status, string LogonId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    DateTime startTime = Convert.ToDateTime(strBegin);
                    DateTime endTime = Convert.ToDateTime(strEnd);
                    if (startTime > endTime)
                    {
                        return null;
                    }
                    endTime = endTime == null ? DateTime.Now : endTime;
                    startTime = startTime == null ? endTime.AddMonths(-1) : startTime;
                    realParams.Add("WhereTime", true);
                    realParams.Add("StartTime", startTime);
                    realParams.Add("EndTime", endTime);
                    if (structureId != null && structureId != "-1")
                    {
                        realParams.Add("WhereStructure", true);
                        realParams.Add("StructureId", "%" + structureId + "%");
                    }
                    if (StationName != null && StationName.Trim() != "")
                    {
                        realParams.Add("WhereStationName", true);
                        realParams.Add("WhereSWStationName", true);
                        realParams.Add("StationName", "%" + StationName.Trim() + "%");
                    }
                    if (StationCode != null && StationCode.Trim() != "")
                    {
                        realParams.Add("WhereStationCode", true);
                        realParams.Add("StationCode", "%" + StationCode.Trim() + "%");
                    }
                    if (HouseName != null && HouseName.Trim() != "")
                    {
                        realParams.Add("WhereHouseName", true);
                        realParams.Add("WhereSWHouseName", true);
                        realParams.Add("HouseName", "%" + HouseName.Trim() + "%");
                    }
                    if (HouseCode != null && HouseCode.Trim() != "")
                    {
                        realParams.Add("WhereHouseCode", true);
                        realParams.Add("HouseCode", "%" + HouseCode.Trim() + "%");
                    }
                    if (Status != null && Status.Trim() != "-1")
                    {
                        realParams.Add("WhereStatus", true);
                        realParams.Add("Status", Status);
                    }
                    realParams.Add("LogonId", LogonId);
                    object userIdObj = execHelper.ExecuteScalar("StationInfo_userId", realParams);
                    object userId = CommonUtils.GetNullableValue(userIdObj);
                    realParams.Add("iUserId", userId);
                    DataTable dt1 = execHelper.ExecDataTable("StationInfo_userRole", realParams);
                    if (dt1 != null && dt1.Rows.Count > 0)
                    {
                        realParams.Add("RoleMapNotNull", true);
                    }
                    else
                    {
                        realParams.Add("RoleMapNull", true);
                    }
                    realParams.Add("SqlStr1", true);
                    realParams.Add("SqlStr2", true);

                    return execHelper.ExecDataTable("SP_Get_WRHouse", realParams);
                  
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("SP_Get_WRHouse:{0}", ex));
                    return null;
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public string RejectWRHouse(object WRHouseId, string RejectCause)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);

                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    if (WRHouseId == null)
                    {
                        return "0";
                    }
                    realParams.Add("WRHouseId", Convert.ToInt32(WRHouseId));
                    Object resultSql = execHelper.ExecuteScalar("SP_RejectWRHouse_HouseStatus", realParams);
                    if (resultSql != null && !resultSql.ToString().Equals(""))
                    {
                        if (Convert.ToInt32(resultSql) == 3)
                        {
                            return "-1";
                        }
                    }
                    realParams.Add("RejectCause", RejectCause);

                    execHelper.ExecuteNonQuery("SP_RejectWRHouse_UpdateHouse", realParams);
                    execHelper.ExecuteNonQuery("SP_RejectWRHouse_UpdateFsu", realParams);
                    return "1";
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("SP_RejectWRHouse:{0}", ex));
                    return "0";
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public int UpdateHouse(string WRHouseId, string WRStationId, string HouseCode, string HouseName, string Remark)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);

                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    if (WRHouseId == null)
                    {
                        return 0;
                    }
                    realParams.Add("WRHouseId", Convert.ToInt32(WRHouseId));
                    object myStatus = DBNull.Value;
                    object SWHouseId = DBNull.Value;
                    object SWStationId = DBNull.Value;
                    object StationCode = DBNull.Value;
                    DataTable dt = execHelper.ExecDataTable("SP_Upd_WRHouse_SelectInfo", realParams);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        DataRow dr = dt.Rows[0];
                        myStatus = CommonUtils.GetNullableValue(dr.Field<int?>("myStatus"));
                        SWHouseId = CommonUtils.GetNullableValue(dr.Field<int?>("SWHouseId"));
                        SWStationId = CommonUtils.GetNullableValue(dr.Field<int?>("SWStationId"));
                        StationCode = CommonUtils.GetNullableValue(dr.Field<string>("StationCode"));
                    }
                    string OriHouseCode = null;
                    Object resultSql = execHelper.ExecuteScalar("SP_Upd_WRHouse_SelectOriHouseCode", realParams);
                    if (resultSql != null && !resultSql.ToString().Equals(""))
                    {
                        OriHouseCode = resultSql.ToString();
                        if (OriHouseCode.Substring(0, 8) != HouseCode)
                        {
                            if (SWStationId != null)
                            {
                                string OutputHouseCode = "";
                                string OutHouseCode = Public_StoredService.Instance.SP_GenerateHouseCode((int)SWStationId, HouseCode, "CMCC", OutputHouseCode);
                                HouseCode = OutHouseCode;
                            }
                        }
                        else
                        {
                            HouseCode = OriHouseCode;
                        }
                    }
                    realParams.Add("HouseName", HouseName);
                    realParams.Add("WRStationId", WRStationId);
                    DataTable dt1 = execHelper.ExecDataTable("SP_Upd_WRHouse_SelectHouseInfo", realParams);
                    if (dt1 != null && dt1.Rows.Count > 0)
                    {
                        return -2;
                    }
                    realParams.Add("Remark", Remark);
                    realParams.Add("SWStationId", SWStationId);
                    realParams.Add("SWHouseId", SWHouseId);
                    realParams.Add("HouseCode", HouseCode);
                    realParams.Add("StationCode", StationCode);
                    realParams.Add("myStatus", myStatus);
                    if (myStatus != DBNull.Value && (int)myStatus == 3)
                    {
                        execHelper.ExecuteNonQuery("SP_Upd_WRHouse_upadteHouse", realParams);
                        execHelper.ExecuteNonQuery("SP_Upd_WRHouse_upadteRoomCMCC", realParams);
                        execHelper.ExecuteNonQuery("SP_Upd_WRHouse_upadteConfigChangeMicroLog", realParams);
                        execHelper.ExecuteNonQuery("SP_Upd_WRHouse_upadteConfigChangeMacroLog", realParams);
                    }
                    string LastString = "";
                    object resultStr = execHelper.ExecuteScalar("SP_Upd_WRHouse_getLastString", realParams);
                    if (resultStr != null)
                    {
                        LastString = resultStr.ToString();
                    }
                    string UpdateString = string.Concat("HouseCode:", HouseCode, "-HouseName:", HouseName, "-Remark:", Remark);
                    string StationName = "";
                    DataTable table = execHelper.ExecDataTable("SP_Upd_WRHouse_getStationName", realParams);
                    if (table != null && table.Rows.Count > 0)
                    {
                        StationName = table.Rows[0][0].ToString();
                    }
                    Public_StoredService.Instance.SP_WR_OperationRecord(WRStationId, StationName, 6, WRHouseId, HouseName, UpdateString, LastString, "-1");
                    execHelper.ExecuteNonQuery("SP_Upd_WRHouse_HouseManagement", realParams);
                    return 1;
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("SP_Upd_WRHouse:{0}", ex));
                    return 0;
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public int InsertHouse(string WRStationId, string HouseCode, string HouseName, string Remark, string LogonId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);

                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("WRStationId", Convert.ToInt32(WRStationId));
                    realParams.Add("HouseName", HouseName);
                    DataTable dt = execHelper.ExecDataTable("SP_Add_WRHouse_getStationId", realParams);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        int iSWStationId = Convert.ToInt32(dt.Rows[0][0]);
                        string OutputHouseCode = "";
                        string OutHouseCode = Public_StoredService.Instance.SP_GenerateHouseCode(iSWStationId, HouseCode, "CMCC", OutputHouseCode);
                        HouseCode = OutHouseCode;
                        realParams.Add("iSWStationId", iSWStationId);
                        object resultSql = execHelper.ExecuteScalar("SP_Add_WRHouse_selMonitorUnitCMCC", realParams);
                        if(resultSql != null && !resultSql.ToString().Equals(""))
                        {
                            return -2;
                        }
                    }
                    DataTable dt1 = execHelper.ExecDataTable("SP_Add_WRHouse_selHouseName", realParams);
                    if ((dt1 != null && dt1.Rows.Count > 0))
                    {
                        return -2;
                    }
                    realParams.Add("LogonId", LogonId);
                    object UserId = DBNull.Value;
                    object SWUserName = DBNull.Value;
                    DataTable dt2 = execHelper.ExecDataTable("SP_Add_WRHouse_selUserInfo", realParams);
                    if(dt2 != null && dt2.Rows.Count >0)
                    {
                        DataRow dr = dt2.Rows[0];
                        UserId = CommonUtils.GetNullableValue(dr.Field<int?>("UserId"));
                        SWUserName = CommonUtils.GetNullableValue(dr.Field<string>("SWUserName"));
                    }
                    realParams.Add("HouseCode", HouseCode);
                    realParams.Add("myStatus", 1);
                    realParams.Add("UserId", UserId);
                    realParams.Add("SWUserName", SWUserName);
                    realParams.Add("Remark", Remark);
                    execHelper.ExecuteNonQuery("SP_Add_WRHouse_insertHouse", realParams);
                    object StationName = DBNull.Value;
                    DataTable rt = execHelper.ExecDataTable("SP_Add_WRHouse_getStationName", realParams);
                    if(rt != null && rt.Rows.Count > 0)
                    {
                        StationName = CommonUtils.GetNullableValue(rt.Rows[0].Field<string>("StationName"));
                    }
                    DataTable dt3 = execHelper.ExecDataTable("SP_Add_WRHouse_getWRHouseId", realParams);
                    if(dt3 != null && dt3.Rows.Count > 0)
                    {
                        int WRHouseId = Convert.ToInt32(dt3.Rows[0][0]);
                        Public_StoredService.Instance.SP_WR_OperationRecord(WRStationId, StationName.ToString(), 4, WRHouseId, HouseName, "", "", LogonId);
                    }
                    return 1;
                }
                catch (Exception ex)
                {
                    Logger.Log(string.Format("SP_Add_WRHouse:{0}", ex));
                    return 0;
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }

        public DataTable SP_ApproveWRHouse(int? WRHouseId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                realParams.Add("WRHouseId", WRHouseId);
                try
                {
                    int? StationStatus=null;int? SWStationId=null;int? HouseStatus=null;
                    int? maxHouseId=null;int?currHouseId=null;
                    if(WRHouseId==null)
                    {
                        return new DataTable();
                    }
                    DataTable dt = executeHelper.ExecDataTable("SP_ApproveWRHouse_Select_StationManagement", realParams);
                    if(dt!=null && dt.Rows.Count > 0)
                    {
                        StationStatus = (int)dt.Rows[0]["StationStatus"];
                        SWStationId = (int)dt.Rows[0]["SWStationId"];
                        HouseStatus = (int)dt.Rows[0]["HouseStatus"];
                        if(StationStatus!=3)
                        {
                            return new DataTable
                            {
                                Columns = { new DataColumn("ReturnValue", typeof(int)) },
                                Rows = { new object[] { -1 } }
                            };
                        }
                        if(HouseStatus==3)
                        {
                            return new DataTable
                            {
                                Columns = { new DataColumn("ReturnValue", typeof(int)) },
                                Rows = { new object[] { -2 } }
                            };
                        }
                    }
                    realParams.Add("StationStatus", StationStatus);
                    realParams.Add("SWStationId", SWStationId);
                    realParams.Add("HouseStatus", HouseStatus);
                    object objmaxHouseId = executeHelper.ExecuteScalar("SP_ApproveWRHouse_maxHouseId", realParams);
                    if(objmaxHouseId!=null && objmaxHouseId!=DBNull.Value)
                    {
                        maxHouseId = Convert.ToInt32(objmaxHouseId);
                        currHouseId = maxHouseId + 1; 
                    }
                    realParams.Add("maxHouseId", maxHouseId);
                    realParams.Add("currHouseId", currHouseId);
                    try
                    {
                        string currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                        realParams.Add("currentTime", currentTime);
                        executeHelper.ExecuteNonQuery("SP_ApproveWRHouse_Insert_House", realParams);
                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -3 } }
                        };
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRHouse_Insert_RoomCMCC", realParams);
                    }catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -4 } }
                        };
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRHouse_Update_HouseManagement", realParams);
                    }catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -5 } }
                        };
                    }
                    try
                    {
                        executeHelper.ExecuteNonQuery("SP_ApproveWRHouse_Insert_ConfigChangeMicroLog", realParams);
                    }
                    catch (Exception)
                    {
                        executeHelper.Rollback();
                        return new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { -6 } }
                        };
                    }
                    executeHelper.ExecuteNonQuery("SP_ApproveWRHouse_Update_ConfigChangeMacroLog", realParams);
                    executeHelper.Commit();
                    return new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { 1 } }
                    };

                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    return new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { null } }
                    };
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
    }
}
