﻿using BDSTool.DBUtility;
using BDSTool.DBUtility.Common;
using BDSTool.Entity.B;
using BDSTool.Entity.DRHelper;
using BDSTool.Entity.S2;
using Common.Logging.Pro;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.BLL.B
{
    public partial class EquipmentCMCCExBiz
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

        public static bool GetAllByFsuId(string FSUID, List<TBL_EquipmentCMCCEx> listOfEquipmentCMCCEx) {
            try {
                DataTable table;
                if (CommonUtils.IsNoProcedure)
                {
                    table = Public_ExecuteSqlService.Instance.NoStore_TBLEquipmentCMCC_GetAnotherInfoByFsuId(FSUID);
                }
                else
                {
                    var sql = string.Format(@"SELECT e.HouseId, e.EquipmentTemplateId, DeviceID, DeviceName, FSUID, b.StationId, b.MonitorUnitId, b.EquipmentId, RoomName, DeviceType, DeviceSubType, Model, Brand, b.RatedCapacity, Version, BeginRunTime, DevDescribe FROM TBL_EquipmentCMCC b INNER JOIN TBL_Equipment e ON b.StationId=e.StationId AND b.EquipmentId=e.EquipmentId WHERE FSUID ={0}", SHelper.GetPara(FSUID));

                    table = DBHelper.GetTable(sql);
                }

                if (table != null) {
                    foreach (DataRow row in table.Rows) {
                        var devEx = TBL_EquipmentCMCCExHelper.FromDataRow(row);
                        listOfEquipmentCMCCEx.Add(devEx);
                    }
                }
            }
            catch (Exception ex) {
                logger.ErrorFormat("GetAllByFsuId();FSUID={0};Error={1}", FSUID, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }


    }
}
