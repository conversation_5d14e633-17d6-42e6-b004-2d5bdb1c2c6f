﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B
{
    /// <summary>
    /// 用户请求监控点数据应答报文
    /// </summary>
    public sealed class GetDataAck : BMessage
    {
        public string DeviceId { get; private set; }
        public string DeviceCode { get; private set; }

        public EnumResult Result { get; private set; }

        public TSemaphore[] Values { get; private set; }

        #region SiteWeb配置

        public int MyStationId { get; set; }
        public int MyDeviceId { get; set; }

        #endregion

        public GetDataAck(string fsuId, string fsuCode, EnumResult result, params TSemaphore[] values)
            : base()
        {
            MessageType = (int)BMessageType.GetDataAck;

            FsuId = fsuId;
            FsuCode = fsuCode;
            Result = result;
            Values = values;
        }

        public override string Serialize()
        {
            XmlDocument xmldoc = new XmlDocument();

            XmlDeclaration xmldecl;
            xmldecl = xmldoc.CreateXmlDeclaration("1.0", "UTF-8", null);
            xmldoc.AppendChild(xmldecl);

            XmlElement xmlelem = xmldoc.CreateElement("", "Response", "");
            xmldoc.AppendChild(xmlelem);

            XmlNode root = xmldoc.SelectSingleNode("Response");
            XmlElement xe1 = xmldoc.CreateElement("PK_Type");

            XmlElement xesub1 = xmldoc.CreateElement("Name");
            xesub1.InnerText = "GET_DATA_ACK";
            xe1.AppendChild(xesub1);
            XmlElement xesub2 = xmldoc.CreateElement("Code");
            xesub2.InnerText = "402";
            xe1.AppendChild(xesub2);

            XmlElement xe2 = xmldoc.CreateElement("Info");
            XmlElement xe21 = xmldoc.CreateElement("Result");
            xe21.InnerText = Result.ToString();
            xe2.AppendChild(xe21);

            root.AppendChild(xe1);
            root.AppendChild(xe2);

            return xmldoc.InnerXml;
        }

        public static GetDataAck Deserialize(XmlDocument xmldoc)
        {
            string fsuId = xmldoc.SelectSingleNode("/Response/Info/FsuId").InnerText;
            string fsuCode = xmldoc.SelectSingleNode("/Response/Info/FsuCode").InnerText;

            string resultString = xmldoc.SelectSingleNode("/Response/Info/Result").InnerText;
            EnumResult enumResult = new EnumResult();
            if (resultString != null && resultString != "")
            {
                int result = int.Parse(xmldoc.SelectSingleNode("/Response/Info/Result").InnerText);
                enumResult = (EnumResult)Enum.Parse(typeof(EnumResult), result.ToString(), false);
            }

            XmlNodeList nodelist = xmldoc.SelectNodes("/Response/Info/Values/DeviceList/Device");
            TSemaphore[] semaphoreValue = null;

            string deviceId = null;
            string deviceCode = null;
            foreach (XmlNode xn in nodelist)//遍历所有子节点 
            {
                XmlElement xe = (XmlElement)xn;
                deviceId = xe.GetAttribute("Id");
                deviceCode = xe.GetAttribute("Code");

                List<TSemaphore> semaphoreList = new List<TSemaphore>();

                foreach (XmlNode xn1 in xe.ChildNodes)//遍历 
                {
                    XmlElement xe2 = (XmlElement)xn1;//转换类型 

                    string type = xe2.GetAttribute("Type");
                    EnumType enumType = (EnumType)Enum.Parse(typeof(EnumType), type, false);
                    string signalId = xe2.GetAttribute("Id");
                    float measuredVal = float.Parse(xe2.GetAttribute("MeasuredVal"));
                    float setupVal = float.Parse(xe2.GetAttribute("SetupVal"));
                    string status = xe2.GetAttribute("Status");
                    EnumState enumState = (EnumState)Enum.Parse(typeof(EnumState), status, false);
                    TSemaphore tSemaphore = new TSemaphore(enumType, signalId, measuredVal, setupVal, enumState);
                    semaphoreList.Add(tSemaphore);
                }
                semaphoreValue = semaphoreList.ToArray();
            }

            GetDataAck getDataAck = new GetDataAck(fsuId, fsuCode, enumResult, semaphoreValue);
            getDataAck.DeviceCode = deviceCode;
            getDataAck.DeviceId = deviceId;

            return getDataAck;
        }

        public override string ToShortString()
        {
            return String.Format("{0}: {1}, {2}, {3}, {4}",
                (BMessageType)MessageType, FsuId, FsuCode, Result, Values.Length);
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();

            sb.AppendFormat("{0}, {1}: {2}, {3}, {4}, {5}",
                MessageId, (BMessageType)MessageType, FsuId, FsuCode, Result, Values != null?Values.Length : 0);
            if (Values != null)
            {
                foreach (TSemaphore item in Values)
                {
                    sb.AppendFormat("{0}\r\n", item);
                }
            }

            return sb.ToString();
        }
    }
}
