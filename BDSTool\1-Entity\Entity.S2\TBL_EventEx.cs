
namespace BDSTool.Entity.S2
{


    using BDSTool.DBUtility;
    using BDSTool.DBUtility.Common;
    using Delivery.Common.NoProcedure.Service;
    using Delivery.Common.NoProcedure.Utils;
    using System;
    using System.Collections.Generic;

    public partial class TBL_Event : IBatchInsertRow
    {
        #region for batch insert
        private static string _InsertHeader = " INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo) ";

        public string GetInsertHeader() {
            if (CommonUtils.IsNoProcedure)
            {
                return Public_ExecuteSqlService.Instance.GetInsertHeader_TBL_Event();
            }
            return _InsertHeader;
        }

        public string GetDataRowValueString() {
            return  string.Format("({0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13})",
                    EquipmentTemplateId, EventId, SHelper.GetPara(EventName), StartType, EndType,
                    SHelper.GetPara(StartExpression), SHelper.GetPara(SuppressExpression), EventCategory, SHelper.GetPara(SignalId), SHelper.GetPara(Enable),
                    SHelper.GetPara(Visible), SHelper.GetPara(Description), DisplayIndex, ModuleNo);
        }

        #endregion

        public List<TBL_EventCondition> eventConditions=new  List<TBL_EventCondition> ();
    }
}
