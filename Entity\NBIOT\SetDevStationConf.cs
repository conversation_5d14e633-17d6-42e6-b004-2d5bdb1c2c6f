﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class SetDevStationConf : BMessage
    {
        public SetDevStationConf() : base()
        {
            MessageType = (int)BMessageType.SET_DEV_STATION_CONF;
        }

        public SetDevStationConf(string fsuId, TDevStationConf values) : base()
        {
            MessageType = (int)BMessageType.SET_DEV_STATION_CONF;
            FSUID = fsuId;
            Values = values;
        }

        [Newtonsoft.Json.JsonProperty(Order = 1)]
        public TDevStationConf Values { get; set; }
    }
}