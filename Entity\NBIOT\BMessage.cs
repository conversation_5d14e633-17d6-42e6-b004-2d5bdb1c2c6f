﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using log4net;
using Newtonsoft.Json;
using System.IO;



namespace ENPC.Kolo.Entity.NBIOT
{
    public abstract class BMessage : Message
    {
        protected static ILog manager = null;

        public static ILog entityLogger
        {
            get
            {
                if (manager == null)
                {
                    log4net.Repository.ILoggerRepository repository = log4net.LogManager.CreateRepository("ds");
                    var fileInfo = new FileInfo("log4net.config");
                    log4net.Config.XmlConfigurator.Configure(repository, fileInfo);
                    log4net.Config.BasicConfigurator.Configure(repository);
                    manager = LogManager.GetLogger(repository.Name, "ds");
                }

                return manager;
            }
        }

        [JsonIgnore]
        public new int MessageType
        {
            get
            {
                return messageType;
            }
            protected set
            {
                messageType = value;
                StringMessageType = ((BMessageType)messageType).ToString();
            }
        }

        public BMessage()
            : base()
        {
            // B接口中没有消息Id，在这生成随机的Id，目地是兼容原有的数据库存储逻辑
            Random rand = new Random();
            MessageId = (uint)rand.Next(0, int.MaxValue);

            MessageFamily = MessageFamily.B;
            MessageType = (int)BMessageType.UNDEFINED;
            UserType = Entity.UserType.NBIOT;
        }


        public static string FormatJsonString(string strJson)
        {
            //格式化json字符串
            JsonSerializer serializer = new JsonSerializer();
            TextReader tr = new StringReader(strJson);
            JsonTextReader jtr = new JsonTextReader(tr);
            object obj = serializer.Deserialize(jtr);
            if (obj != null)
            {
                StringWriter textWriter = new StringWriter();
                JsonTextWriter jsonWriter = new JsonTextWriter(textWriter)
                {
                    Formatting = Formatting.Indented,
                    Indentation = 4,
                    IndentChar = ' '
                };
                serializer.Serialize(jsonWriter, obj);
                return textWriter.ToString();
            }
            else
            {
                return strJson;
            }
        }

        public override string ToString()
        {
            JsonSerializerSettings settting = new JsonSerializerSettings();
            settting.ContractResolver = new CustomContractResolver();
            return string.Format("{0}: {1}", ((BMessageType)MessageType).ToString(), JsonConvert.SerializeObject(this, settting));
        }
    }
}
