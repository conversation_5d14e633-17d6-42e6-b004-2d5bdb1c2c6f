﻿using BLL;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web.Http;




namespace DM.TestOrder.Controllers {
        
    public class SiteEquipController : BaseApiController{

        //http://localhost/orderapi/SiteEquip?stationId=755000001&userid=-3
        public HttpResponseMessage Get(int orderType, int userid,
            int stationId, string houseName = "", string equipmentName = "", string equipmentNo = "") {
            Debug.WriteLine(stationId);

           // var sql = string.Format(@"GetAllByStationId {0},{1}", stationId, userid);


            DataTable dt = EquipmentDal.GetByKeyword(orderType, userid, stationId, houseName, equipmentName, equipmentNo);

            var sReturn = JsonConvert.SerializeObject(dt);
            HttpResponseMessage result = new HttpResponseMessage {
                Content = new StringContent(sReturn, Encoding.GetEncoding("UTF-8"), "application/json")
            };
            return result; 


        }
    }
}
