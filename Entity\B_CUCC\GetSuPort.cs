﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class GetSuPort: BMessage
    {

        public GetSuPort(string suids, string surids):base()
        {
            MessageType = (int)BMessageType.GET_SUPORT;
            SUId = suids;
            SURId = surids;
        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmlDoc = CreateXmlDocument();
                AppendPK_Type(xmlDoc, "Request", BMessageType.GET_SUPORT.ToString(), MessageType.ToString());
                XmlElement xel = xmlDoc.CreateElement("Info");
                XmlElement xelone = xmlDoc.CreateElement("SUId");
                XmlElement xeltwo = xmlDoc.CreateElement("SURId");
                //XmlElement xelthree = xmlDoc.CreateElement("DeviceList");

                xelone.InnerText = SUId;
                xeltwo.InnerText = SURId;
                xel.AppendChild(xelone);
                xel.AppendChild(xeltwo);
                //xel.AppendChild(xelthree);
                XmlNode node = xmlDoc.SelectSingleNode("Request");
                node.AppendChild(xel);
                StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("GetSuPort.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmlDoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetSuPort.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetSuPort.Serialize() Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0},{1},{2},{3}", MessageId, (BMessageType)MessageType, SUId, SURId);
            return sb.ToString();
        }



    }
}
