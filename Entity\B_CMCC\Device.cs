﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 监控点数据请求和应答报文中的设备数据结构,
    /// 增加监控点存储规则
    /// </summary>
    public sealed class Device
    {
        public string DeviceId { get; set; }

        #region SiteWeb配置

        public int MyDeviceId { get; set; }

        #endregion

        public List<TSemaphore> Semaphores { get; set; }

        /// <summary>
        /// 监控点存储规则ID
        /// </summary>
        public List<TSignalMeasurementId> TSignalMeasurementIds { get; set; }

        /// <summary>
        /// 监控点存储规则ID
        /// </summary>
        public List<TStorageRule> TStorageRules { get; set; }

        public Device() { }

        /// <summary>
        /// 监控点数据请求和应答报文中的设备数据结构
        /// </summary>
        public Device(string deviceId, List<TSemaphore> semaphores)
        {
            DeviceId = deviceId;
            Semaphores = semaphores;
        }

        /// <summary>
        /// 构造监控点存储规则ID
        /// </summary>
        public Device(string deviceId, List<TSignalMeasurementId> tSignalMeasurementIds)
        {
            DeviceId = deviceId;
            TSignalMeasurementIds = tSignalMeasurementIds;
        }

        /// <summary>
        /// 构造监控点存储规则
        /// </summary>
        public Device(string deviceId, List<TStorageRule> tStorageRules)
        {
            DeviceId = deviceId;
            TStorageRules = tStorageRules;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append(DeviceId).Append(":\r\n");
            if ( Semaphores!= null && Semaphores.Count > 0)
            {
                foreach (TSemaphore semaphore in Semaphores)
                {
                    sb.Append(semaphore.ToString()).Append("\r\n");
                }
            }
            if (TSignalMeasurementIds != null && TSignalMeasurementIds.Count > 0)
            {
                foreach (TSignalMeasurementId tsmid in TSignalMeasurementIds)
                {
                    sb.Append(tsmid.ToString()).Append("\r\n");
                }
            }
            if (TStorageRules != null && TStorageRules.Count > 0)
            {
                foreach (TStorageRule tsr in TStorageRules)
                {
                    sb.Append(tsr.ToString()).Append("\r\n");
                }
            }
            return sb.ToString();
        }
    }
}
