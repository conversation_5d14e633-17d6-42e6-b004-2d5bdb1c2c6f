﻿

using BLL;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using testorder.dal;
using DM.TestOrder.Model;
using DM.TestOrder.Service.Convert;
using DM.TestOrder.DAL;
using DM.TestOrder.Common;



namespace DM.TestOrder.Service.Interface {
    public partial class TestOrderApi {
        //设备检查清单
        public FormTest GetFormTest(int orderId) {
            FormTest PartTest = new FormTest();

            if (WoTestOrderDal.IsExist(orderId)) {
                throw new Exception(string.Format("无此ID的工单(id={0})",orderId));
            }

            try {
                PartTest.ItemCheckList = Mapper.ToListOfItemCheck(WoTestOrderEquipItemCheckListDal.GetByOrderId(orderId));
                return PartTest;
            }
            catch (Exception ex) {
                MyLogger.ErrorFormat("TestOrderApi.GetFormTest();error={0}", ex.Message);
                MyLogger.Error(ex.StackTrace);
                throw ex;
            }

        }
    }
}
