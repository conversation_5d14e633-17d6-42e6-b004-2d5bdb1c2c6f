﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using Delivery.BSL;
using Delivery.Common;

namespace Delivery.API.CMCC
{
    [Route("CMCC/api/[controller]")]
    public class DeviceController : BaseController
    {
        public static readonly Dictionary<string, string> deviceFieldMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "applytime", "ApplyTime" },
            { "devicecategory", "DeviceCategory" },
            { "devicecategoryname", "DeviceCategoryName" },
            { "devicecode", "DeviceCode" },
            { "devicename", "DeviceName" },
            { "devicesubcategory", "DeviceSubCategory" },
            { "devicesubcategoryname", "DeviceSubCategoryName" },
            { "enabletime", "EnableTime" },
            { "fsucode", "FsuCode" },
            { "fsuname", "FsuName" },
            { "housename", "HouseName" },
            { "lifetime", "LifeTime" },
            { "portuse", "PortUse" },
            { "remark", "Remark" },
            { "rownumber", "RowNumber" },
            { "stationname", "StationName" },
            { "swhouseid", "SWHouseId" },
            { "swstationid", "SWStationId" },
            { "swusername", "SWUserName" },
            { "userid", "UserId" },
            { "wrdeviceid", "WRDeviceId" },
            { "wrfsuid", "WRFsuId" },
            { "wrhouseid", "WRHouseId" },
            { "wrstationid", "WRStationId" }
        };

        [HttpGet("[action]")]
        public string GetDeviceInfo(string startTime, string endTime, string stationId, string fsuId, string deviceCategory, string deviceSubCategory, string deviceName, string deviceCode, int page, int rows)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            DataTable dt = ccbsl.GetDeviceInfo(startTime, endTime, stationId, fsuId, deviceCategory, deviceSubCategory, deviceName, deviceCode, LogonId);
            DataTableColumnMapper.RenameColumns(dt, deviceFieldMap);
            return Json4EasyUI.onDataGrid(dt, page, rows);
        }


        [HttpPost("[action]")]
        public string AddDevice([FromForm] string houseId, [FromForm] string fsuId, [FromForm] string deviceCategory, [FromForm] string deviceSubCategory, [FromForm] string sysSerialNo, [FromForm] string deviceName, 
            [FromForm] string remark, [FromForm] string portUse, [FromForm] string enableTime, [FromForm] string lifeTime)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            return ccbsl.Add_WRDevice(houseId, fsuId, deviceCategory, deviceSubCategory, sysSerialNo, deviceName, remark, LogonId, portUse, enableTime, lifeTime).ToString();
        }

        [HttpPost("[action]")]
        public string DeleteDevice([FromForm] string deviceId)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            return ccbsl.Delete_WRDevice(deviceId, LogonId);
        }

        [HttpPost("[action]")]
        public string UpdateDevice([FromForm] string deviceId, [FromForm] string houseId, [FromForm] string fsuId, 
            [FromForm] string deviceCategory, [FromForm] string deviceSubCategory, [FromForm] string sysSerialNo, 
            [FromForm] string deviceName, [FromForm] string remark, [FromForm] string portUse, [FromForm] string enableTime, [FromForm] string lifeTime)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            return ccbsl.Update_WRDevice(deviceId, houseId, fsuId, deviceCategory, deviceSubCategory, sysSerialNo, deviceName, remark, LogonId, portUse, enableTime, lifeTime).ToString();
        }

        [HttpGet("[action]")]
        public string GetWRFsuId(string stationId, string houseId)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            DataTable dt = ccbsl.GetWRFsuId(stationId, houseId, LogonId);
            return Json4EasyUI.onComboBox(dt);
        }

        [HttpGet("[action]")]
        public string GetWRDeviceType()
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            DataTable dt = ccbsl.GetWRDeviceType();
            return Json4EasyUI.onComboBox(dt);
        }

        [HttpGet("[action]")]
        public string GetWRDeviceSubType(string parentType)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            DataTable dt = ccbsl.GetWRDeviceSubType(parentType);
            return Json4EasyUI.onComboBox(dt);
        }

        /// <summary>
        /// 导出 Excel
        /// </summary>
        /// <returns></returns>              
        [HttpGet("[action]")]
        public IActionResult ExportExcel(string stationId, string WRFsuId, string deviceCode, string strBegin, string strEnd, string deviceName, string deviceCategory, string deviceSubCategory)
        {
            //string LogonId = "admin";
            DataTable dataTable = GetExportData(stationId, WRFsuId, deviceCode, strBegin, strEnd, deviceName, deviceCategory, deviceSubCategory, LogonId);

            string name = "设备管理";
            byte[] bytes = ExcelHelper.DataTableToExcel(dataTable, null, name);
            return File(bytes, "application/ms-excel", $"{name}_{DateTime.Now:yyyyMMddHHmmssfff}.xlsx");
        }

        private DataTable GetExportData(string stationId, string WRFsuId, string deviceCode, string strBegin, string strEnd, string deviceName, string deviceCategory, string deviceSubCategory, string LogonId)
        {
            try
            {
                DeliveryDataBsl ccbsl = new DeliveryDataBsl();
                DataTable dt = ccbsl.GetDeviceInfo(strBegin, strEnd, stationId, WRFsuId, deviceCategory, deviceSubCategory, deviceName, deviceCode, LogonId);
                string[] cols = new string[]
                {
                        "StationName","FsuCode", "FsuName", "HouseName","DeviceCode","DeviceCategoryName","DeviceSubCategoryName","DeviceName","Remark","ApplyTime","SWUserName"
                };
                DataTable myDt = dt.DefaultView.ToTable(true, cols);
                myDt.Columns["StationName"].ColumnName = "站址名称";
                myDt.Columns["FsuCode"].ColumnName = "FSU编码";
                myDt.Columns["FsuName"].ColumnName = "FSU名称";
                myDt.Columns["HouseName"].ColumnName = "机房名称";
                myDt.Columns["DeviceCode"].ColumnName = "设备编码";
                myDt.Columns["DeviceCategoryName"].ColumnName = "设备类型";
                myDt.Columns["DeviceSubCategoryName"].ColumnName = "设备子类";
                myDt.Columns["DeviceName"].ColumnName = "设备名称";
                myDt.Columns["Remark"].ColumnName = "备注";
                myDt.Columns["ApplyTime"].ColumnName = "申请时间";
                myDt.Columns["SWUserName"].ColumnName = "申请人";

                return myDt;
            }
            catch (Exception ex)
            {
                Logger.Log(ex);
            }
            return null;
        }


    }
}
