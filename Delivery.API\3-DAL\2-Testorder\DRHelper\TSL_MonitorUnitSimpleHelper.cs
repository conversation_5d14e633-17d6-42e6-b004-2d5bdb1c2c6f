﻿
using BDSTool.Entity.S2;

using ENPC.Kolo.Entity.B_CMCC;

using log4net;

using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.Entity.DRHelper
{
    public class TSL_MonitorUnitSimpleHelper
    {
        private static readonly ILog logger = LogManager.GetLogger("BDSTool");

        public static TSL_MonitorUnitSimple FromDataRow(DataRow row) {

            var entity = new TSL_MonitorUnitSimple();
            entity.StationId = int.Parse(row["StationId"].ToString());
            entity.MonitorUnitId = int.Parse(row["MonitorUnitId"].ToString());
            entity.MonitorUnitName = row["MonitorUnitName"].ToString();
            entity.MonitorUnitCategory = int.Parse(row["MonitorUnitCategory"].ToString());
            
            return entity;
        }
    }
}
