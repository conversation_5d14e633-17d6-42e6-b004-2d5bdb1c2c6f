﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class LogoutAck : BMessage
    {

        public EnumResult Result { get; set; }

        public LogoutAck(EnumResult result):base()
        {
            MessageType = (int)BMessageType.LOGOUT_ACK;

            Result = result;
        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmlDoc = CreateXmlDocument();
                AppendPK_Type(xmlDoc, "Response", BMessageType.LOGOUT_ACK.ToString(), ((int)BMessageType.LOGOUT_ACK).ToString());

                XmlElement xe2 = xmlDoc.CreateElement("Info");
                XmlElement xe21 = xmlDoc.CreateElement("Result");
                xe21.InnerText = ((int)Result).ToString();

                xe2.AppendChild(xe21);
                XmlNode root = xmlDoc.SelectSingleNode("Response");
                root.AppendChild(xe2);

                StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("LogoutAck.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmlDoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("LogoutAck.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("LogoutAck.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}: {2}", MessageId, (BMessageType)MessageType, Result.ToString());
        }

    }
}
