﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
		<procedure owner="" name="WO_UpdateEventCategory" grant="">
			<parameters>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
           UPDATE TBL_Event e, (SELECT DISTINCT  ec.EventId AS MyEventId, m.SiteWebEventCategoryId MySiteWebEventCategoryId, m.BaseTypeName AS MyBaseTypeName  FROM TBL_EventCondition  ec
		   INNER  JOIN WO_DicEventCategoryMap m ON m.BaseTypeId=floor(ec.BaseTypeId div 1000) * 1000 + 1
		   WHERE ec.EquipmentTemplateId=@EquipmentTemplateId
		    ) ecat
	       SET EventCategory = MySiteWebEventCategoryId	
           WHERE @EquipmentTemplateId=@EquipmentTemplateId  AND e.EventId=ecat.MyEventId;
         ]]>
			</body>
		</procedure>
		<procedure owner="" name="WO_SelectUpdateEventCategory" grant="">
			<parameters>
				<parameter name="EquipmentTemplateId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
           SELECT count(*) count from TBL_Event e INNER  JOIN (SELECT DISTINCT  ec.EventId AS MyEventId, m.SiteWebEventCategoryId MySiteWebEventCategoryId, m.BaseTypeName AS MyBaseTypeName  FROM TBL_EventCondition  ec
		   INNER  JOIN WO_DicEventCategoryMap m ON m.BaseTypeId=floor(ec.BaseTypeId div 1000) * 1000 + 1
		   WHERE ec.EquipmentTemplateId=@EquipmentTemplateId
		  ) ecat ON EquipmentTemplateId=@EquipmentTemplateId  AND e.EventId=ecat.MyEventId;
         ]]>
			</body>
		</procedure>

	</procedures>
</root>
