﻿using System;
using System.Collections.Generic;
using System.Data;
using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;

namespace Delivery.Common.NoProcedure.Service
{
    public class BslPartSPCUCCService
    {
        private static BslPartSPCUCCService _instance = null;

        public static BslPartSPCUCCService Instance
        {
            get
            {
                if (_instance == null) _instance = new BslPartSPCUCCService();
                return _instance;
            }
        }

        /// <summary> 存储过程 SP_Del_WRDeviceCUCC 的实现. 【原注释】 ---删除设备----联通版--  [Tested] </summary>
        public DataTable DelWRDeviceCUCC(int wRDeviceId, string logonId, int checkRight = 1, ExecuteHelper helper = null)
        {
            //入参：
            //wRDeviceId --设备自增唯一标识
            //logonId    -- 当前登录用户ID
            //checkRight -- 是否检查用户权限, 1检查，0不检查
            if (helper == null)
            {
                using (DbHelper dbHelper = new DbHelper())
                {
                    ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                    try
                    {
                        DataTable rtn = CommonDelWRDeviceCUCC(wRDeviceId, logonId, checkRight, executeHelper);
                        //executeHelper.Commit();
                        return rtn;

                    }
                    catch (Exception ex)
                    {
                        executeHelper.Rollback();
                        Logger.Log("BslPartSPCUCCService.DelWRDeviceCUCC throw Exception: ", LogType.Error);
                        Logger.Log(ex);
                        return null;
                    }
                    finally
                    {
                        dbHelper.Connection.Close();
                    }
                }
            }
            else
            {
                return CommonDelWRDeviceCUCC(wRDeviceId, logonId, checkRight, helper);
            }
        }

        private DataTable CommonDelWRDeviceCUCC(int wRDeviceId, string logonId, int checkRight, ExecuteHelper helper)
        {
            int resultRtn = -9999;
            Dictionary<string, object> realParams = new Dictionary<string, object>
            {
                { "WRDeviceId", wRDeviceId }, //-- 设备自增唯一标识
                { "LogonId", logonId }  //-- 当前登录用户ID
            };
            object userIdObj = helper.ExecuteScalar("BslPartSPCUCC_GetUserId", realParams);
            object userId = CommonUtils.GetNullableValue(userIdObj);
            realParams.Add("UserId", userId);
            if (checkRight == 1)
            {
                object dmCountObj = helper.ExecuteScalar("DelWRDeviceCUCC_GetDeviceManagementCount", realParams);
                int dmCount = dmCountObj == null || dmCountObj == DBNull.Value ? -1 : int.Parse(dmCountObj.ToString());

                if (dmCount <= 0)
                {
                    Logger.Log("当前用户无权限删除此设备（CUCC）");
                    DataTable rtn = new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { -1 } }
                    };
                    return rtn;
                }
            }
            DataTable infoDt = helper.ExecDataTable("DelWRDeviceCUCC_GetDeviceInfo", realParams);
            object sWMonitorUnitId = DBNull.Value, sWStationId = DBNull.Value;
            object deviceCode = DBNull.Value;
            if (infoDt != null && infoDt.Rows.Count > 0)
            {
                DataRow row = infoDt.Rows[0];
                deviceCode = CommonUtils.GetNullableValue(row.Field<string>("DeviceCode"));
                sWMonitorUnitId = CommonUtils.GetNullableValue(row.Field<int?>("SWMonitorUnitId"));
                sWStationId = CommonUtils.GetNullableValue(row.Field<int?>("SWStationId"));
            }
            //-- 取SiteWeb的设备ID
            realParams.Add("SWStationId", sWStationId);
            realParams.Add("SWMonitorUnitId", sWMonitorUnitId);
            realParams.Add("DeviceCode", deviceCode);
            DataTable equipInfoDt = helper.ExecDataTable("DelWRDeviceCUCC_GetEquipInfo", realParams);
            object sWEquipmentId = DBNull.Value;
            object sWEquipmentName = DBNull.Value;
            if (equipInfoDt != null && equipInfoDt.Rows.Count > 0)
            {
                DataRow row = equipInfoDt.Rows[0];
                sWEquipmentId = CommonUtils.GetNullableValue(row.Field<int?>("EquipmentId"));
                sWEquipmentName = CommonUtils.GetNullableValue(row.Field<string>("EquipmentName"));
            }
            if (sWEquipmentId != DBNull.Value)
            {
                resultRtn = -9999;
                //-- 删除SiteWeb的设备信息
                try
                {
                    //TODO(CALL): later....
                    Public_StoredService.Instance.PCT_DeleteEquipment((int)sWStationId, (int)sWEquipmentId, helper);
                    resultRtn = 1;
                }
                catch (Exception ex)
                {
                    resultRtn = -99;
                    Logger.Log("调用PCT_DeleteEquipment逻辑失败[异常]（CUCC）,SWStationId=" + sWStationId + ";SWEquipmentId=" + sWEquipmentId);
                    Logger.Log(ex);
                }
                if (resultRtn != 1)
                {
                    helper.Rollback();
                    Logger.Log("调用PCT_DeleteEquipment逻辑失败（CUCC）");
                    DataTable rtn = new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { -1 } }
                    };
                    return rtn;
                }
                string objectId = sWStationId + "." + sWEquipmentId;
                resultRtn = -9999;
                try
                {
                    PblConfigChangeLogService.Instance.DoExecute(objectId, 3, 3, helper);
                    resultRtn = 1;
                }//17:53：这里以上测过了
                catch (Exception ex)
                {
                    resultRtn = -99;
                    Logger.Log("调用PBL_ConfigChangeLog逻辑失败[异常]（CUCC）,objectId=" + objectId);
                    Logger.Log(ex);
                }
                if (resultRtn != 1)
                {
                    helper.Rollback();
                    Logger.Log("调用PBL_ConfigChangeLog逻辑失败（CUCC）");
                    DataTable rtn = new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { -1 } }
                    };
                    return rtn;
                }
                //-- 记录删除设备日志
                realParams.Add("SWEquipmentId", sWEquipmentId);
                realParams.Add("SWEquipmentName", sWEquipmentName);
                resultRtn = -9999;
                try
                {
                    helper.ExecuteNonQuery("DelWRDeviceCUCC_InsertOperationDetail11", realParams);
                    resultRtn = 1;
                }
                catch (Exception ex)
                {
                    resultRtn = -99;
                    Logger.Log("记录删除设备日志失败[异常]（CUCC）:");
                    Logger.Log(ex);
                }
                if (resultRtn != 1)
                {
                    helper.Rollback();
                    Logger.Log("记录删除设备日志失败（CUCC）");
                    DataTable rtn = new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { -2 } }
                    };
                    return rtn;
                }
                //-- 删除B接口数据表的设备信息
                resultRtn = -9999;
                try
                {
                    helper.ExecuteNonQuery("DelWRDeviceCUCC_DelEquipmentCUCC", realParams);
                    resultRtn = 1;
                }
                catch (Exception ex)
                {
                    resultRtn = -99;
                    Logger.Log("删除B接口数据表的设备信息[异常]（CUCC）:");
                    Logger.Log(ex);
                }
                if (resultRtn != 1)
                {
                    helper.Rollback();
                    Logger.Log("删除B接口数据表的设备信息（CUCC）");
                    DataTable rtn = new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { -3 } }
                    };
                    return rtn;
                }
            }
            resultRtn = -9999;
            try
            {
                //-- 20190119高映军日志，删除设备
                //-- ------------------------------------------------
                object wRStationId = DBNull.Value;
                object deviceName = DBNull.Value;
                DataTable wrEquipInfoDt = helper.ExecDataTable("DelWRDeviceCUCC_GetWRDeviceInfo", realParams);
                if (wrEquipInfoDt != null && wrEquipInfoDt.Rows.Count > 0)
                {
                    wRStationId = CommonUtils.GetNullableValue(wrEquipInfoDt.Rows[0].Field<int?>("WRStationId"));
                    deviceName = CommonUtils.GetNullableValue(wrEquipInfoDt.Rows[0].Field<string>("DeviceName"));
                }
                realParams.Add("WRStationId", wRStationId);
                object stationNameObj = helper.ExecuteScalar("DelWRDeviceCUCC_GetStationName", realParams);
                string stationName = stationNameObj == null || stationNameObj == DBNull.Value ? "" : stationNameObj.ToString();
                if(wRStationId != DBNull.Value && deviceName != DBNull.Value)
                {
                    //TODO(CALL): later....
                    Public_StoredService.Instance.SP_WR_OperationRecord((int)wRStationId, stationName, 11, wRDeviceId, (string)deviceName, "", "", logonId, helper);
                }
                //	-- ------------------------------------------------	
                //--删除交维表设备信息
                helper.ExecuteNonQuery("DelWRDeviceCUCC_DelDeviceManagement", realParams);
                resultRtn = 1;
            }
            catch (Exception ex)
            {
                resultRtn = -99;
                Logger.Log("删除交维表设备信息[异常]（CUCC）:");
                Logger.Log(ex);
            }
            if (resultRtn != 1)
            {
                helper.Rollback();
                Logger.Log("删除交维表设备信息（CUCC）");
                DataTable rtn = new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -4 } }
                };
                return rtn;
            }
            helper.Commit();
            return new DataTable
            {
                Columns = { new DataColumn("ReturnValue", typeof(int)) },
                Rows = { new object[] { 1 } }
            };
        }


        /// <summary> 存储过程 SP_Add_WRStationCUCC 的实现.【原注释】 ---新增站址----联通版 </summary>
        public DataTable AddWRStationCUCC(string province, string city, string county, string structureId, string stationCategory,string stationRId,
             string stationName, string address, string remark, string contractNo, string projectName, string logonId, ExecuteHelper helper = null)
        {
            //入参：
            // -- Province 		  -- 省行政区划ID
            // -- City            -- 市行政区划ID
            // -- County		  -- 区县行政区划ID
            // -- StructureId     -- 分组ID
            // -- StationCategory -- 站址类型ID   
            // -- StationRId	  -- 站址资源ID
            // -- StationName     -- 站址名称
            // -- Address         -- 地址
            // -- Remark          -- 备注
            // -- ContractNo	  -- 合同号
            // -- ProjectName	  -- 工程名称
            // -- LogonId		  -- 用户ID

            if (helper == null)
            {
                using (DbHelper dbHelper = new DbHelper())
                {
                    ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                    try
                    {
                        DataTable rtn = CommonAddWRStationCUCC(province, city, county, structureId, stationCategory, stationRId,stationName, 
                                                address, remark, contractNo, projectName, logonId, executeHelper);
                        executeHelper.Commit();
                        return rtn;

                    }
                    catch (Exception ex)
                    {
                        executeHelper.Rollback();
                        Logger.Log("BslPartSPCUCCService.AddWRDeviceCUCC throw Exception: ", LogType.Error);
                        Logger.Log(ex);
                        return null;
                    }
                    finally
                    {
                        dbHelper.Connection.Close();
                    }
                }
            }
            else
            {
                return CommonAddWRStationCUCC(province, city, county, structureId, stationCategory, stationRId, stationName,
                                                address, remark, contractNo, projectName, logonId, helper);
            }
        }

        private DataTable CommonAddWRStationCUCC(string province, string city, string county, string structureId, string stationCategory, string stationRId,
             string stationName, string address, string remark, string contractNo, string projectName, string logonId, ExecuteHelper helper = null)
        {
            object structureIdObj = DBNull.Value, provinceObj = DBNull.Value, cityObj = DBNull.Value, countyObj = DBNull.Value, stationCategoryObj = DBNull.Value;
            if (int.TryParse(structureId, out int structureIdInt)) structureIdObj = structureIdInt;
            if (int.TryParse(province, out int provinceInt)) provinceObj = provinceInt;
            if (int.TryParse(city, out int cityInt)) cityObj = cityInt;
            if (int.TryParse(county, out int countyInt)) countyObj = countyInt;
            if (int.TryParse(stationCategory, out int stationCategoryInt)) stationCategoryObj = stationCategoryInt;
            Dictionary<string, object> realParams = new Dictionary<string, object>
            {
                { "Province", provinceObj },
                { "City", cityObj },
                { "County", countyObj },
                { "StructureId", structureIdObj },
                { "StationCategory", stationCategoryObj },
                { "StationRId", stationRId },
                { "StationName", stationName },
                { "Address", address },
                { "Remark", remark },
                { "ContractNo", contractNo },
                { "ProjectName", projectName },
                { "LogonId", logonId }
            };
            object smCountObj = helper.ExecuteScalar("AddWRStationCUCC_GetStationManagementCUCCCount", realParams);
            int smCount = smCountObj == null || smCountObj == DBNull.Value ? -1 : int.Parse(smCountObj.ToString());
            if(smCount > 0)
            {
                return new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -1 } }
                };
            }
            object sCountObj = helper.ExecuteScalar("AddWRStationCUCC_GetStationCount", realParams);
            int sCount = sCountObj == null || sCountObj == DBNull.Value ? -1 : int.Parse(sCountObj.ToString());
            if (sCount > 0)
            {
                return new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -1 } }
                };
            }

            DataTable userDt = helper.ExecDataTable("AddWRStationCUCC_GetUserInfo", realParams);
            object userId = DBNull.Value, sWUserName = DBNull.Value;
            if(userDt != null && userDt.Rows.Count > 0)
            {
                userId = CommonUtils.GetNullableValue(userDt.Rows[0].Field<int?>("UserId"));
                sWUserName = CommonUtils.GetNullableValue(userDt.Rows[0].Field<string>("UserName"));
            }

            string stationCode = null;
            //CALL SP_GenerateStationCode (County, StationCategory, stationCode)
            Public_StoredService.Instance.SP_GenerateStationCode(countyInt, stationCategoryInt, out stationCode);
            if (stationCode == null)
            {
                return new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -2 } }
                };
            }
            int stationStatus = 1; // -- 默认状态值为 1,申请入网
            realParams.Add("StationCode", stationCode);
            realParams.Add("StationStatus", stationStatus);
            realParams.Add("UserId", userId);
            realParams.Add("SWUserName", sWUserName);
            helper.ExecuteNonQuery("AddWRStationCUCC_InsertStationManagementCUCC", realParams);
            //--201811高映军日志
            //----------------------------------------------
            object wRStationIdObj = helper.ExecuteScalar("AddWRStationCUCC_GetWRStationId", realParams);
            object wRStationId = CommonUtils.GetNullableValue(wRStationIdObj);

            // CALL SP_WR_OperationRecord (WRStationId,StationName,1,WRStationId,StationName,'','',LogonId)
            Public_StoredService.Instance.SP_WR_OperationRecord(wRStationId, stationName, 1, wRStationId, stationName, "", "", logonId);
            //-- -----------------------------------------------	

            return new DataTable
            {
                Columns = { new DataColumn("ReturnValue", typeof(int)) },
                Rows = { new object[] { 1 } }
            };
        }


        /// <summary> 存储过程 SP_Del_WRFsuCUCC 的实现. 【原注释】：----删除SU----联通版-- </summary>
        public DataTable DelWRFsuCUCC(int wrFsuId, string logonId, int checkRight = 1, ExecuteHelper helper = null)
        {
            if (helper == null)
            {
                using (DbHelper dbHelper = new DbHelper())
                {
                    ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                    try
                    {

                        DataTable rtn = CommonDelWRFsuCUCC(wrFsuId, logonId, checkRight, executeHelper);
                        //executeHelper.Commit();
                        return rtn;

                    }
                    catch (Exception ex)
                    {
                        executeHelper.Rollback();
                        Logger.Log("BslPartSPCUCCService.DelWRFsuCUCC throw Exception: ", LogType.Error);
                        Logger.Log(ex);
                        return null;
                    }
                    finally
                    {
                        dbHelper.Connection.Close();
                    }
                }
            }
            else
            {
                return CommonDelWRFsuCUCC(wrFsuId, logonId, checkRight, helper);
            }
        }

        private DataTable CommonDelWRFsuCUCC(int wrFsuId, string logonId, int checkRight, ExecuteHelper helper)
        {
            Dictionary<string, object> realParams = new Dictionary<string, object>
            {
                { "WRFsuId", wrFsuId }, //-- --SU自增唯一标识
                { "LogonId", logonId }  //--  当前登录用户ID
            };
            object userIdObj = helper.ExecuteScalar("BslPartSPCUCC_GetUserId", realParams);
            if (userIdObj == null || userIdObj == DBNull.Value)
            {
                Logger.Log("userId is null. LogonId=" + logonId, LogType.Error);
                return null;
            }
            int userId = int.Parse(userIdObj.ToString());
            realParams.Add("UserId", userId);
            if (checkRight == 1)
            {
                object fmCountObj = helper.ExecuteScalar("DelWRFsuCUCC_GetFsuManagementCount", realParams);
                int fmCount = fmCountObj == null || fmCountObj == DBNull.Value ? -1 : int.Parse(fmCountObj.ToString());

                if (fmCount <= 0)
                {
                    Logger.Log("当前用户无权限删除此SU(CUCC)");
                    DataTable rtn = new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { -1 } }
                    };
                    return rtn;
                }
            }

            DataTable infoDt = helper.ExecDataTable("DelWRFsuCUCC_GetFsuInfo", realParams);
            object wRStationId = DBNull.Value, sWStationId = DBNull.Value, sWMonitorUnitId = DBNull.Value, fsuStatus = DBNull.Value;
            if (infoDt != null && infoDt.Rows.Count > 0)
            {
                DataRow row = infoDt.Rows[0];
                wRStationId = CommonUtils.GetNullableValue(row.Field<int?>("WRStationId"));//TODO：统一修改此用变量的取法
                sWStationId = CommonUtils.GetNullableValue(row.Field<int?>("SWStationId"));
                sWMonitorUnitId = CommonUtils.GetNullableValue(row.Field<int?>("SWMonitorUnitId"));
                fsuStatus = CommonUtils.GetNullableValue(row.Field<int?>("FsuStatus"));
            }
            if (fsuStatus == DBNull.Value)
            {
                Logger.Log("UnKnown fsuStatus: null (CUCC). WRFsuId=" + wrFsuId);
                return null;
            }
            if ((int)fsuStatus != 3) //-- SU还未通过审核,直接删除并返回
            {
                int rtnVal = 1;
                try
                {
                    helper.ExecuteNonQuery("DelWRFsuCUCC_DelFsuManagement", realParams);
                    helper.Commit();
                    if (checkRight == 1)
                    {
                        DataTable rtn = new DataTable
                        {
                            Columns = { new DataColumn("ReturnValue", typeof(int)) },
                            Rows = { new object[] { rtnVal } }
                        };
                        return rtn;
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception ex)
                {
                    helper.Rollback();
                    rtnVal = -2;
                    Logger.Log("DelWRFsuCUCC_DelFsuManagement throw Exception(CUCC): ");
                    Logger.Log("删除SU失败(CUCC), @WRFsuId=" + wrFsuId);
                    Logger.Log(ex);
                    DataTable rtn = new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { rtnVal } }
                    };
                    return rtn;
                }
            }
            //--监控单元名称
            realParams.Add("SWMonitorUnitId", sWMonitorUnitId);
            realParams.Add("SWStationId", sWStationId);
            object nameObj = helper.ExecuteScalar("DelWRFsuCUCC_GetMonitorUnitName", realParams);
            string sWMonitorUnitName = nameObj == null || nameObj == DBNull.Value ? "" : nameObj.ToString();
            //--自诊断设备
            DataTable equipInfoDt = helper.ExecDataTable("DelWRFsuCUCC_GetEquipInfo", realParams);
            object sWEquipmentId = DBNull.Value;
            object sWEquipmentName = DBNull.Value;
            int resultRtn = -9999;
            if (equipInfoDt != null && equipInfoDt.Rows.Count > 0)
            {
                sWEquipmentId = CommonUtils.GetNullableValue(equipInfoDt.Rows[0].Field<int?>("EquipmentId"));
                sWEquipmentName = CommonUtils.GetNullableValue(equipInfoDt.Rows[0].Field<string>("EquipmentName"));
            }
            realParams.Add("WRStationId", wRStationId);
            DataTable wRDeviceIdsDt = helper.ExecDataTable("DelWRFsuCUCC_GetWRDeviceIds", realParams);
            if (wRDeviceIdsDt != null && wRDeviceIdsDt.Rows.Count > 0)
            {
                //-- 删除SU下所有设备
                foreach (DataRow row in wRDeviceIdsDt.Rows)
                {
                    int? wRDeviceId = row.Field<int?>("WRDeviceId");
                    if(wRDeviceId.HasValue)
                    {
                        resultRtn = -9999;
                        try
                        {
                            
                            //TODO(CALL): 	CALL SP_Del_WRDeviceCUCC (WRDeviceId, LogonId, 0); -- 此处0表示不做删除权限检查
                            //TODO: 用返回值不等于1来判断是否异常了
                            DataTable dt = DelWRDeviceCUCC(wRDeviceId.Value, logonId, 0);
                            if (dt != null && dt.Rows[0].Field<int>("ReturnValue") < 0)
                            {
                                resultRtn = -99;
                            }
                            else
                            {
                                resultRtn = 1;
                            }
                        }
                        catch (Exception ex)
                        {
                            resultRtn = -99;
                            Logger.Log("删除SU下设备失败[异常](CUCC), WRDeviceId=" + wRDeviceId);
                            Logger.Log(ex);
                        }
                        if (resultRtn != 1)
                        {
                            helper.Rollback();
                            Logger.Log("删除SU下设备失败(CUCC),WRDeviceId=" + wRDeviceId);
                            DataTable rtn = new DataTable
                            {
                                Columns = { new DataColumn("ReturnValue", typeof(int)) },
                                Rows = { new object[] { -3 } }
                            };
                            return rtn;
                        }
                    }
                }
            }
            resultRtn = -9999;
            try
            {
                //-- 201811高映军日志
                //-- ------------------------------------------------
                object fsuNameObj = helper.ExecuteScalar("DelWRFsuCUCC_GetFsuName", realParams);
                string fsuName = fsuNameObj == null || fsuNameObj == DBNull.Value ? "" : fsuNameObj.ToString();
                object stationNameObj = helper.ExecuteScalar("DelWRFsuCUCC_GetStationName", realParams);
                string stationName = stationNameObj == null || stationNameObj == DBNull.Value ? "" : stationNameObj.ToString();
                Public_StoredService.Instance.SP_WR_OperationRecord((int)wRStationId, stationName, 8, wrFsuId, fsuName, "", "", logonId);
                //--删除交维SU
                helper.ExecuteNonQuery("DelWRFsuCUCC_DelFsuManagement", realParams);
                resultRtn = 1;
            }
            catch (Exception ex)
            {
                resultRtn = -99;
                Logger.Log("删除交维SU失败[异常](CUCC),WRFsuId=" + wrFsuId);
                Logger.Log(ex);
            }
            if (resultRtn != 1)
            {
                helper.Rollback();
                Logger.Log("删除交维SU失败(CUCC),WRFsuId=" + wrFsuId);
                DataTable rtn = new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -4 } }
                };
                return rtn;
            }
            //-- 删除SiteWeb监控单元
            resultRtn = -9999;
            try
            {

                //TODO(CALL): CALL PCT_DeleteMonitorUnit (SWMonitorUnitId)
                PCT_DeleteMonitorUnitService.Instance.PCT_DeleteMonitorUnit(int.Parse(sWMonitorUnitId.ToString()));
                resultRtn = 1;
            }
            catch (Exception ex)
            {
                resultRtn = -99;
                Logger.Log("删除SiteWeb监控单元失败[异常](CUCC),SWMonitorUnitId=" + sWMonitorUnitId);
                Logger.Log(ex);
            }
            if (resultRtn != 1)
            {
                helper.Rollback();
                Logger.Log("删除SiteWeb监控单元失败(CUCC),SWMonitorUnitId=" + sWMonitorUnitId);
                DataTable rtn = new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -5 } }
                };
                return rtn;
            }
            //-- 记录删除监控单元日志
            realParams.Add("SWMonitorUnitName", sWMonitorUnitName);
            resultRtn = -9999;
            try
            {
                helper.ExecuteNonQuery("DelWRFsuCUCC_InsertOperationDetail6", realParams);
                resultRtn = 1;
            }
            catch (Exception ex)
            {
                resultRtn = -99;
                Logger.Log("记录删除监控单元日志失败[异常](CUCC):");
                Logger.Log(ex);
            }
            if (resultRtn != 1)
            {
                helper.Rollback();
                Logger.Log("记录删除监控单元日志失败(CUCC)");
                DataTable rtn = new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -6 } }
                };
                return rtn;
            }
            //-- 记录删除监控单元自诊断设备日志
            realParams.Add("SWEquipmentId", sWEquipmentId);
            realParams.Add("SWEquipmentName", sWEquipmentName);
            resultRtn = -9999;
            try
            {
                helper.ExecuteNonQuery("DelWRFsuCUCC_InsertOperationDetail11", realParams);
                resultRtn = 1;
            }
            catch (Exception ex)
            {
                resultRtn = -99;
                Logger.Log("记录删除监控单元自诊断设备日志失败[异常](CUCC):");
                Logger.Log(ex);
            }
            if (resultRtn != 1)
            {
                helper.Rollback();
                Logger.Log("记录删除监控单元自诊断设备日志失败(CUCC)");
                DataTable rtn = new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -7 } }
                };
                return rtn;
            }
            //-- 删除SiteWeb监控单元扩展表信息
            resultRtn = -9999;
            try
            {
                helper.ExecuteNonQuery("DelWRFsuCUCC_DelMonitorUnitExtend", realParams);
                resultRtn = 1;
            }
            catch (Exception ex)
            {
                resultRtn = -99;
                Logger.Log("删除SiteWeb监控单元扩展表信息失败[异常](CUCC),@SWMonitorUnitId=" + sWMonitorUnitId);
                Logger.Log(ex);
            }
            if (resultRtn != 1)
            {
                helper.Rollback();
                Logger.Log("删除SiteWeb监控单元扩展表信息失败(CUCC)");
                DataTable rtn = new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -8 } }
                };
                return rtn;
            }
            //-- 删除B接口监控单元信息
            resultRtn = -9999;
            try
            {
                helper.ExecuteNonQuery("DelWRFsuCUCC_DelMonitorUnitCUCC", realParams);
                resultRtn = 1;
            }
            catch (Exception ex)
            {
                resultRtn = -99;
                Logger.Log("删除B接口监控单元信息失败[异常](CUCC),@SWMonitorUnitId=" + sWMonitorUnitId);
                Logger.Log(ex);
            }
            if (resultRtn != 1)
            {
                helper.Rollback();
                Logger.Log("删除B接口监控单元信息失败(CUCC)");
                DataTable rtn = new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -9 } }
                };
                return rtn;
            }
            helper.Commit();
            //-- --页面直接调用时,@CheckRight = 1,执行成功要返回结果
            //----否则直接return
            if (checkRight == 1)
            {
                DataTable rtn = new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { 1 } }
                };
                return rtn;
            }
            return null;
        }

        /// <summary> 存储过程 SP_Del_StationCUCC 的实现. 【原注释】：----删除站址----联通版-- </summary>
        public DataTable DelStationCUCC(int wrFsuId, string logonId, ExecuteHelper helper = null)
        {
            if (helper == null)
            {
                using (DbHelper dbHelper = new DbHelper())
                {
                    ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                    try
                    {

                        DataTable rtn = CommonDelStationCUCC(wrFsuId, logonId, executeHelper);
                        //executeHelper.Commit();
                        return rtn;

                    }
                    catch (Exception ex)
                    {
                        executeHelper.Rollback();
                        Logger.Log("BslPartSPCUCCService.DelStationCUCC throw Exception: ", LogType.Error);
                        Logger.Log(ex);
                        return null;
                    }
                    finally
                    {
                        dbHelper.Connection.Close();
                    }
                }
            }
            else
            {
                return CommonDelStationCUCC(wrFsuId, logonId, helper);
            }
        }

        private DataTable CommonDelStationCUCC(int wRStationId, string logonId, ExecuteHelper helper)
        {
            int resultRtn = -9999;
            Dictionary<string, object> realParams = new Dictionary<string, object>
            {
                { "WRStationId", wRStationId }, //-- Station自增唯一标识  
                { "LogonId", logonId }  //-- 当前登录用户ID
            };
            object userIdObj = helper.ExecuteScalar("DelStationCUCC_GetUserId", realParams);
            object userId = CommonUtils.GetNullableValue(userIdObj);
            //-- 删除站址的用户必须具备管理角色
            int isAdminRole = 0; //-非管理员角色
            realParams.Add("UserId", userId);
            DataTable dtAdmin = helper.ExecDataTable("DelStationCUCC_IsAdminRole", realParams);
            if (dtAdmin != null && dtAdmin.Rows.Count == 4)
            {
                isAdminRole = 1;  //--管理员角色
            }

            if (isAdminRole != 1)
            {
                Logger.Log("当前用户不是管理员,无权限删除站址(CUCC), LogonId=" + logonId);
                DataTable rtn = new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -1 } }
                };
                return rtn;
            }

            DataTable fsuUnderStationDt;
            object sWStationId = DBNull.Value, stationStatus = DBNull.Value;
            try
            {
                DataTable stationInfoDt = helper.ExecDataTable("DelStationCUCC_GetStationInfo", realParams);
                if (stationInfoDt != null && stationInfoDt.Rows.Count > 0)
                {
                    sWStationId = CommonUtils.GetNullableValue(stationInfoDt.Rows[0].Field<int?>("SWStationId"));
                    stationStatus = CommonUtils.GetNullableValue(stationInfoDt.Rows[0].Field<int?>("StationStatus"));
                }
                fsuUnderStationDt = helper.ExecDataTable("DelStationCUCC_GetFsuUnderStationInfo", realParams);
            }
            catch (Exception ex)
            {
                helper.Rollback();
                Logger.Log("GetStationInfo or GetFsuUnderStationInfo throw Exception: ");
                Logger.Log(ex);
                return new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -2 } }
                };
            }
            //-- 删除站址下所有FSU
            if(fsuUnderStationDt != null && fsuUnderStationDt.Rows.Count > 0)
            {
                foreach(DataRow row in fsuUnderStationDt.Rows)
                {
                    int? wRFsuId = row.Field<int?>("WRFsuId");
                    if(wRFsuId.HasValue)
                    {
                        resultRtn = 1;
                        try
                        {
                            DataTable result = DelWRFsuCUCC(wRFsuId.Value, logonId, 0, helper); // -- 此处0表示不做删除权限检查
                            if (result != null && result.Rows.Count > 0 && result.Rows[0].Field<int>("ReturnValue") <= -2)
                            {
                                resultRtn = -99;
                            }
                        }
                        catch (Exception ex)
                        {
                            resultRtn = -99;
                            Logger.Log($"Exec DelWRFsuCUCC({wRFsuId.Value},'{logonId}', 0, helper) throw Exception:");
                            Logger.Log(ex);
                        }
                        if(resultRtn == -99)
                        {
                            helper.Rollback();
                            return new DataTable
                            {
                                Columns = { new DataColumn("ReturnValue", typeof(int)) },
                                Rows = { new object[] { -3 } }
                            };
                        }
                    }
                }
            }
            DataTable houseUnderStationDt;
            try
            {
                houseUnderStationDt = helper.ExecDataTable("DelStationCUCC_GetHouseUnderStationInfo", realParams);
            }
            catch (Exception ex)
            {
                helper.Rollback();
                Logger.Log($"DelStationCUCC_GetHouseUnderStationInfo throw Exception:");
                Logger.Log(ex); 
                return new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -4 } }
                };
            }
            //-- 删除站址下所有House
            if(houseUnderStationDt != null && houseUnderStationDt.Rows.Count > 0)
            {
                foreach(DataRow row in houseUnderStationDt.Rows)
                {
                    int? wRHouseId = row.Field<int?>("WRHouseId");
                    if(wRHouseId.HasValue)
                    {
                        resultRtn = 1;
                        try
                        {
                            string result = SPDelWRHouseCUCCService.Instance.DeleteHouseCUCC(wRHouseId.ToString(), logonId, 0);
                            if (int.Parse(result) <= -2)
                            {
                                resultRtn = -99;
                            }
                        }
                        catch (Exception ex)
                        {
                            resultRtn = -99;
                            Logger.Log($"Exec SP_Del_WRHouseCUCC({wRHouseId.Value},'{logonId}', 0) throw Exception:");
                            Logger.Log(ex);
                        }
                        if (resultRtn == -99)
                        {
                            helper.Rollback();
                            return new DataTable
                            {
                                Columns = { new DataColumn("ReturnValue", typeof(int)) },
                                Rows = { new object[] { -5 } }
                            };
                        }
                    }
                }
            }
            try
            {
                //-- 201811高映军日志
                //------------------------------------------------
                object stationNameObj = helper.ExecuteScalar("DelStationCUCC_GetStationName", realParams);
                string stationName = stationNameObj == null || stationNameObj == DBNull.Value ? null : stationNameObj.ToString();
                Public_StoredService.Instance.SP_WR_OperationRecord(wRStationId, stationName, 2, wRStationId, stationName, "", "", logonId);
                //------------------------------------------------
                helper.ExecuteNonQuery("DelStationCUCC_DelStationManagementCUCC", realParams);
            }
            catch (Exception ex)
            {
                helper.Rollback();
                Logger.Log($"Exec DelStationCUCC_DelStationManagementCUCC throw Exception:");
                Logger.Log(ex);
                return new DataTable
                {
                    Columns = { new DataColumn("ReturnValue", typeof(int)) },
                    Rows = { new object[] { -6 } }
                };
            }
            //-- 对于已审核站址,再调用一次SiteWeb局站删除逻辑
            if(stationStatus != DBNull.Value && (int)stationStatus == 3)
            {
                //SET SQL_SAFE_UPDATES = 0; //??
                try
                {
                    if(sWStationId != DBNull.Value)
                    {
                        Public_StoredService.Instance.PCT_DeleteStation(int.Parse(sWStationId.ToString()));
                    }
                }
                catch (Exception ex)
                {
                    helper.Rollback();
                    Logger.Log($"Exec PCT_DeleteStation({sWStationId}) throw Exception:");
                    Logger.Log(ex);
                    return new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { -7 } }
                    };
                }

                try
                {
                    PblConfigChangeLogService.Instance.DoExecute(sWStationId.ToString(), 1, 3, helper);
                }
                catch (Exception ex)
                {
                    helper.Rollback();
                    Logger.Log($"Exec PblConfigChangeLog({sWStationId},1,3) throw Exception:");
                    Logger.Log(ex);
                    return new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { -7 } }
                    };
                }
                try
                {
                    realParams.Add("SWStationId", sWStationId);
                    helper.ExecuteNonQuery("DelStationCUCC_DeleteMonitorUnitCUCC", realParams);
                }
                catch (Exception ex)
                {
                    helper.Rollback();
                    Logger.Log($"Exec DelStationCUCC_DeleteMonitorUnitCUCC throw Exception:");
                    Logger.Log(ex);
                    return new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { -8 } }
                    };
                }
                try
                {
                    helper.ExecuteNonQuery("DelStationCUCC_DelEquipmentCUCC", realParams);
                }
                catch (Exception ex)
                {
                    helper.Rollback();
                    Logger.Log($"Exec DelStationCUCC_DelEquipmentCUCC throw Exception:");
                    Logger.Log(ex);
                    return new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { -9 } }
                    };
                }
            }
            helper.Commit();
            return new DataTable
            {
                Columns = { new DataColumn("ReturnValue", typeof(int)) },
                Rows = { new object[] { 1 } }
            };
        }


        /// <summary> 存储过程 SP_Get_WRFsuCUCC 的实现  【原注释】：-----查询FSU----联通版 [Tested]</summary>
        public DataTable GetWRFsuCUCC(string startTimeStr, string endTimeStr, string structureIdStr, string fsuName, 
            string fsuCode, string manufactureIdStr, string statusStr, string logonId)
        {
            //入参：
            //StartTime		    -- 申请开始日期
            //EndTime			-- 申请截止日期
            //StructureId		-- 局站分组ID,-1表全选
            //FsuName    		-- Fsu名称  
            //FsuCode			-- Fsu编码
            //ManufacturerId	-- Fsu厂商ID,-1表全选 	
            //Status			-- 申请单状态,-1表全选
            //LogonId			-- 用户登录账户

            DateTime endTime = DateTime.TryParse(endTimeStr, out DateTime time2) ? time2 : DateTime.Now;
            DateTime startTime = DateTime.TryParse(startTimeStr, out DateTime time1) ? time1 : endTime.AddMonths(-1);
            object structureId = DBNull.Value;
            if (structureIdStr != null && int.TryParse(structureIdStr, out int structureIdInt))
            {
                structureId = structureIdInt;
            }
            object manufactureId = DBNull.Value;
            if (manufactureIdStr != null && int.TryParse(manufactureIdStr, out int manufactureIdInt))
            {
                manufactureId = manufactureIdInt;
            }
            object status = DBNull.Value;
            if (statusStr != null && int.TryParse(statusStr, out int statusInt))
            {
                status = statusInt;
            }

            Dictionary<string, object> realParams = new Dictionary<string, object>
            {
                { "StartTime", startTime },
                { "EndTime", endTime },
                { "LogonId", logonId }
            };

            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper);
                try
                {
                    if(startTime > endTime)
                    {
                        Logger.Log($"startTime({startTime.ToStr()}) > endTime({endTime.ToStr()})");
                        return null;
                    }
                    //-- 用户权限
                    object userIdObj = executeHelper.ExecuteScalar("BslPartSPCUCC_GetUserId", realParams);
                    object userId = CommonUtils.GetNullableValue(userIdObj);
                    realParams.Add("iUserId", userId);
                    object roleMapCountObj = executeHelper.ExecuteScalar("GetWRFsuCUCC_GetRoleMapCount", realParams);
                    int roleMapCount = roleMapCountObj == null || roleMapCountObj == DBNull.Value ? 0 : int.Parse(roleMapCountObj.ToString());
                    string filterUserInnerSql;
                    if (roleMapCount > 0)
                    {//-- 管理员可以查看全部,也包括已删除用户创建的FSU
                        filterUserInnerSql = XmlScriptProvider.Current.GetCommandSql("GetWRFsuCUCC_GetFilterUserInnerSqlAdmin", realParams);
                    } else
                    {//-- 非管理员
                        filterUserInnerSql = XmlScriptProvider.Current.GetCommandSql("GetWRFsuCUCC_GetFilterUserInnerSqlNotAdmin", realParams);
                    }
                    realParams.Add("filterUserInnerSql", filterUserInnerSql);

                    realParams.Add("WhereTime", true);
                    if(!string.IsNullOrWhiteSpace(fsuName))
                    {
                        fsuName = fsuName.Trim();
                        realParams.Add("WhereSWFsuName", true);
                        realParams.Add("WhereFsuName", true);
                        realParams.Add("FsuName", fsuName);
                    }
                    if(!string.IsNullOrWhiteSpace(fsuCode))
                    {
                        fsuCode = fsuCode.Trim();
                        realParams.Add("WhereSWFsuCode", true);
                        realParams.Add("WhereFsuCode", true);
                        realParams.Add("FsuCode", fsuCode);
                    }
                    if(manufactureId != DBNull.Value && (int)manufactureId != -1)
                    {
                        realParams.Add("WhereManufactureId", true);
                        realParams.Add("ManufacturerId", manufactureId);
                    }
                    if(structureId != DBNull.Value && (int)structureId != -1)
                    {
                        realParams.Add("WhereStructure", true);
                        realParams.Add("StructureIdStr", structureId.ToString());
                    }
                    if(status != DBNull.Value && (int)status != -1)
                    {
                        realParams.Add("WhereStatus", true);
                        realParams.Add("Status", status);
                    }
                    DataTable resultDt1 = executeHelper.ExecDataTable("GetWRFsuCUCC_GetResultDt1", realParams);

                    DataTable resultDt2 = executeHelper.ExecDataTable("GetWRFsuCUCC_GetResultDt2", realParams);

                    resultDt1.Merge(resultDt2);//全部合并进入Dt1中
                    if(resultDt1.Rows.Count > 0)
                    {
                        // 对DataTable进行排序
                        resultDt1.DefaultView.Sort = XmlScriptProvider.Current.GetCommandSql("GetWRFsuCUCC_GetResultDt_OrderBy", null);
                        resultDt1 = resultDt1.DefaultView.ToTable();
                        int rowNumber = 1;
                        foreach (DataRow row in resultDt1.Rows)
                        {
                            row["RowNumber"] = rowNumber++;
                        }

                    }
                    return resultDt1;
                }
                catch (Exception ex)
                {
                    Logger.Log("BslPartSPCUCCService.GetWRFsuCUCC throw Exception: ", LogType.Error);
                    Logger.Log(ex);
                    return null;
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
    }
}
