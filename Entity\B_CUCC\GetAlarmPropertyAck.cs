﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class GetAlarmPropertyAck : BMessage
    {
        public List<Device> LDevice { get; set; }

        public GetAlarmPropertyAck() : base() 
        {
            MessageType = (int)BMessageType.GET_AlarmProperty_ACK;
        }


        public GetAlarmPropertyAck(string suid, string surid, List<Device> list)
            : base()
        {
            MessageType = (int)BMessageType.GET_AlarmProperty_ACK;
            SUId = suid;
            SURId = surid;
            LDevice = list;
        }

        public static GetAlarmPropertyAck Deserialize(XmlDocument xmlDoc)
        {
            GetAlarmPropertyAck getAlarmPropertyAck = null;
            string errMsg = string.Empty;
            string suid = string.Empty;
            string surid = string.Empty;
            try
            {
                suid = xmlDoc.SelectSingleNode("/Response/Info/SUId").InnerText.Trim();
                surid = xmlDoc.SelectSingleNode("/Response/Info/SURId").InnerText.Trim();
                XmlNodeList nodes = xmlDoc.SelectNodes("/Response/Info/Values/DeviceList/Device");
                List<Device> list = new List<Device>();
                foreach (XmlElement node in nodes)
                {
                    string id = node.GetAttribute("Id");
                    string rId = node.GetAttribute("RId");
                    List<Signal> lst = new List<Signal>();
                    XmlNodeList lists = node.SelectNodes("Signal");
                    foreach (XmlElement n in lists)
                    {
                        Signal signal = new Signal();
                        signal.Id = n.GetAttribute("Id");
                        string strBdelay = n.GetAttribute("BDelay").Trim();
                        string strEdelay = n.GetAttribute("EDelay").Trim();
                        if (!string.IsNullOrEmpty(strBdelay))
                        {
                            signal.BDelay = float.Parse(strBdelay);
                        }
                        if (!string.IsNullOrEmpty(strEdelay))
                        {
                            signal.EDelay = float.Parse(strEdelay);
                        }
                        lst.Add(signal);
                    }
                    Device device = new Device(id, rId, lst);
                    list.Add(device);
                }
                getAlarmPropertyAck = new GetAlarmPropertyAck(suid, surid, list);
                entityLogger.DebugFormat("GetAlarmPropertyAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                getAlarmPropertyAck.StringXML = xmlDoc.InnerXml;
                return getAlarmPropertyAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetAlarmPropertyAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetAlarmPropertyAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                getAlarmPropertyAck = new GetAlarmPropertyAck();
                getAlarmPropertyAck.ErrorMsg = ex.Message;
                getAlarmPropertyAck.StringXML = xmlDoc.InnerXml;
                return getAlarmPropertyAck;
            }


        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            if (LDevice.Count > 0)
            {
                foreach (Device de in LDevice)
                {
                    sb.AppendFormat("{0},{1},{2},{3},{4},{5}", MessageId, (BMessageType)MessageType, MessageType, SUId, SURId, de.PartDelayToString());
                }
            }
            else
            {
                sb.AppendFormat("{0},{1},{2},{3},{4},Device is null", MessageId, (BMessageType)MessageType, MessageType, SUId, SURId);
            }
            return sb.ToString();
        }


    }
}
