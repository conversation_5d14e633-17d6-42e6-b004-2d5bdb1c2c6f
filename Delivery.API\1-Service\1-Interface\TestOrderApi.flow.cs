﻿

using BLL;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using testorder.dal;
using DM.TestOrder.formData;
using DM.TestOrder.Model;
using DM.TestOrder.DAL;
using DM.TestOrder.Common;



namespace DM.TestOrder.Service.Interface {
    public partial class TestOrderApi {


        public string Section1_SubmitOnlineApply( int ApplyUserId, int OrderId) {             
            var errmsg = "OK";
            try {

                WoFlowDal.WO_SubmitOnlineApply(OrderId, ApplyUserId);

            }
            catch (Exception ex) {

                errmsg = ex.Message;
                if (ex.InnerException != null)
                    errmsg += "; " + ex.InnerException.Message;

                return errmsg;
            }
            return errmsg;
        }

        public string Section2_SubmitExpertDecision(SectionExpertApprove flow) {
            var errmsg = "OK";
            try {
                WoFlowDal.WO_SubmitExpertDecision(flow);
            }
            catch (Exception ex) {

                errmsg = ex.Message;
                if (ex.InnerException != null)
                    errmsg += "; " + ex.InnerException.Message;

                return errmsg;
            }
            return errmsg;
        }


        public string Section3_SubmitFinalDecision(SectionFinalApprove secFinal) {

            var errmsg = "OK";
            try {

                WoFlowDal.WO_SubmitFinalDecision(secFinal);

                //if no exception, sync siteweb config
                if (secFinal.FinalIsApprove==1) {
                    
                    var s = WoFlowDal.WO_SubmitFinalDecision_SyncSiteWeb(secFinal.OrderId);
                    MyLogger.InfoFormat("更新SiteWeb系统配置数据; OrderId={0}; result ={1}", secFinal.OrderId, s);
                }

            }
            catch (Exception ex) {

                errmsg = ex.Message;
                if (ex.InnerException != null)
                    errmsg += "; " + ex.InnerException.Message;

                return errmsg;
            }
            return errmsg;
        }
 
    }
}
