﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class TGetSemaphore
    {
        /// <summary>
        /// 监控点ID
        /// </summary>
        public string SID { get; set; }
        /// <summary>
        /// 实际值
        /// </summary>
        public float? Value { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public EnumState Status { get; set; }
        /// <summary>
        /// 时间
        /// </summary>
        public DateTime? Time { get; set; }

        public TGetSemaphore() { }

        public TGetSemaphore(string sId, float? value, EnumState status, DateTime? time)
        {
            SID = sId;
            Value = value;
            Status = status;
            Time = time;
        }
    }
}
