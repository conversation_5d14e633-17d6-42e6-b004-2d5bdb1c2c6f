﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity
{
    public abstract class Message
    {
        public uint MessageId { get; set; }
        public MessageFamily MessageFamily { get; protected set; }
        public int MessageType 
        { 
            get 
                { return messageType; }
            protected set
            {
                messageType = value;
            }
        }
        
        public int messageType;
        public string Address { get; set; }
        //协议名称
        public string StringMessageType { get; set; }
        /// <summary>
        /// 实体转换错误信息:转换正确时，值为string.Empty；出错时，值为回复信息
        /// </summary>
        public string ErrorMsg
        {
            get
            {
                return errorMsg;
            }
            set
            {
                errorMsg = value;
            }
        }
        /// <summary>
        /// 协议内容字符串
        /// </summary>
        public string StringXML
        {
            get
            {
                return stringXML;
            }
            set
            {
                stringXML = value;
            }
        }
        /// <summary>
        /// 给定的初始值为string.Empty
        /// </summary>
        private string errorMsg = string.Empty;

        private string stringXML = string.Empty;

        public string SourceHostId { get; set; }

        public string FSUID { get; set; }//20180905 原来的set是protected, 在做NBIOT时不方便处理，去掉protected
        /// <summary>
        /// 用户类型
        /// </summary>
        public UserType UserType{ get; protected set; }

        public Message()
        {
            MessageFamily = MessageFamily.Undefined;
            Address = "Unknown";
        }

        public virtual string Serialize()
        {
            return String.Empty;
        }

        public virtual string ToShortString()
        {
            return ToString();
        }
    }

    /// <summary>
    /// 消息族
    /// </summary>
    public enum MessageFamily
    {
        /// <summary>
        /// 未定义
        /// </summary>
        Undefined = -1,

        /// <summary>
        /// B接口互联消息
        /// </summary>
        B = 0,

        /// <summary>
        /// SiteWeb消息
        /// </summary>
        SiteWeb = 1,
    }

    /// <summary>
    /// 用户类型
    /// </summary>
    public enum UserType
    {
        /// <summary>
        /// 中国移动
        /// </summary>
        CMCC = 1,

        /// <summary>
        /// 中国联通
        /// </summary>
        CUCC = 2,

        /// <summary>
        /// 中国电信
        /// </summary>
        CTCC = 3,

        /// <summary>
        /// 移动物联网
        /// </summary>
        NBIOT = 4,
    }
}
