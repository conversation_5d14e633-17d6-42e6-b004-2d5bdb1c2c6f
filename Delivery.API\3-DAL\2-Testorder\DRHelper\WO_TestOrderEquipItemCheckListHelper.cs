﻿
using DM.TestOrder.Common.DBA;


using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using DM.TestOrder.Common;
using DM.TestOrder.Entity;


namespace DM.TestOrder.DAL.Helper
{
    public class WO_TestOrderEquipItemCheckListHelper
    {
        public static WO_TestOrderEquipItemCheckList FromDataRow(DataRow row) {
            var e = new WO_TestOrderEquipItemCheckList();
            try {
                e.OrderCheckId = SHelper.ToInt(row["OrderCheckId"]);
                e.OrderId = SHelper.ToInt(row["OrderId"]);
                e.EquipmentId = SHelper.ToInt(row["EquipmentId"]);
                e.BaseEquipmentId = SHelper.ToInt(row["BaseEquipmentId"]);
                e.BaseEquipmentName = row["BaseEquipmentName"].ToString();
                e.BaseTypeId = SHelper.ToDecimal(row["BaseTypeId"]);
                e.BaseTypeName = row["BaseTypeName"].ToString();
                e.CheckType = row["CheckType"].ToString();
                e.CheckTypeId = SHelper.ToInt(row["CheckTypeId"]);
                e.Unit = row["Unit"].ToString();
                //e.LimitDown = SHelper.ToInt(row["LimitDown"]);
                //e.LimitUp = SHelper.ToInt(row["LimitUp"]);
                e.LimitDown = SHelper.ToDouble(row["LimitDown"]);
                e.LimitUp = SHelper.ToDouble(row["LimitUp"]);
                e.Note = row["Note"].ToString();
                e.CheckId = SHelper.ToInt(row["CheckId"]);
                e.EquipmentName = row["EquipmentName"].ToString();
                e.EquipmentCategoryName = row["EquipmentCategoryName"].ToString();
                e.LogicClass = row["LogicClass"].ToString();
                e.StandardName = row["StandardName"].ToString();
                e.SignalType = row["SignalType"].ToString();
                e.IsPass = SHelper.ToInt(row["IsPass"]);
                e.PassNote = row["PassNote"].ToString();
                e.PassFailReason = row["PassFailReason"].ToString();
                e.SamplerValue = row["SamplerValue"].ToString();
                e.SamplerTime = SHelper.ToDateTime(row["SamplerTime"]);
                e.EquipmentLogicClass = row["EquipmentLogicClass"].ToString();
            }
            catch (Exception ex) {
                MyLogger.ErrorFormat("WO_TestOrderEquipItemCheckList.FromDataRow();error={0}", ex.Message);
                MyLogger.Error(ex.StackTrace);
                return null;
            }
            return e;
        }
    }
}
