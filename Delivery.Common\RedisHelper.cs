using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Delivery.Common
{
    public static class RedisHelper
    {
        private static Lazy<ConnectionMultiplexer> lazyConnection = new Lazy<ConnectionMultiplexer>(() =>
        {
            // 从配置中获取Redis连接字符串
            string redisConnection = ConfigHelper.GetSection("RedisConnection").Value;
            return ConnectionMultiplexer.Connect(redisConnection);
        });

        public static ConnectionMultiplexer Connection
        {
            get
            {
                return lazyConnection.Value;
            }
        }

        /// <summary>
        /// 从Redis获取字符串值
        /// </summary>
        /// <param name="key">键名</param>
        /// <returns>字符串值</returns>
        public static string Get(string key)
        {
            try
            {
                IDatabase cache = Connection.GetDatabase();
                return cache.StringGet(key);
            }
            catch (Exception ex)
            {
                Logger.Log($"Redis Get操作失败: {ex.Message}");
                return null;
            }
        }

        public static List<string> GetValues(List<string> keys, string connectStr = null)
        {
            List<string> value = null;
            try
            {
                using (var redisClient = RedisClientManager.GetClient(connectStr))
                {
                    value = redisClient.GetValues(keys);
                }
            }
            catch (Exception ex)
            {
                Logger.Log(String.Format("keyType:{0},connectStr:{1},error:{2}", keys, connectStr, ex.ToString()));
            }
            return value;
        }

       
        /// <summary>
        /// 从Redis获取泛型对象
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="key">键名</param>
        /// <returns>反序列化后的对象</returns>
        /*public static T Get<T>(string key)
        {
            try
            {
                string value = Get(key);
                if (!string.IsNullOrEmpty(value))
                {
                    return JsonHelper.Deserialize<T>(value);
                }
                return default(T);
            }
            catch (Exception ex)
            {
                Logger.Log($"Redis Get<T>操作失败: {ex.Message}");
                return default(T);
            }
        }*/
    }
}