﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class GetStationConf : BMessage
    {
        public GetStationConf() : base()
        {
            MessageType = (int)BMessageType.GET_STATION_CONF;
        }

        public GetStationConf(string fsuId, string devId) : base()
        {
            MessageType = (int)BMessageType.GET_STATION_CONF;
            FSUID = fsuId;
            DevID = devId;
        }

        [Newtonsoft.Json.JsonProperty(Order = 1)]
        public string DevID { get; set; }
    }
}