﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
		<procedure owner="" name="SP_CheckLoginUserInfo_NoData" grant="">
			<body>
				<![CDATA[ 
					SELECT 0 LoginResult, NULL UserId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_CheckLoginUserInfo_LoginInfo" grant="">
			<parameters>
				<parameter name="LogonId" type="string" direction="Input" size="4000" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
			SELECT  TBL_Account.UserId UserId,TBL_Account.Password Pwd,TBL_Account.Enable isEnable,TBL_Account.Locked isLocked,TBL_Account.ValidTime ValidTime
	        FROM TBL_Account 
	        WHERE TBL_Account.LogonId = :LogonId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_CheckLoginUserInfo_Success" grant="">
			<parameters>
				<parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
			 SELECT 1 LoginResult, :UserId UserId, '验证成功' Remark
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_CheckLoginUserInfo_PassWordError" grant="">
			<parameters>
				<parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
			 SELECT 0 LoginResult, NULL UserId, '密码错误' Remark;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_CheckLoginUserInfo_AccountInvalid" grant="">
			<parameters>
				<parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
			 SELECT 0 LoginResult, NULL UserId, '帐号已失效' Remark;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_CheckLoginUserInfo_AccountLocked" grant="">
			<parameters>
				<parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
			SELECT 0 LoginResult, NULL UserId, '帐号已被锁定' Remark ;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_CheckLoginUserInfo_AccountExpired" grant="">
			<parameters>
				<parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
			SELECT 0 LoginResult, NULL UserId, '帐号已过期' Remark;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_CheckLoginUserInfo_AccountFailed" grant="">
			<parameters>
				<parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
			SELECT 0 LoginResult, NULL UserId, '帐号验证失败' Remark;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_GetLoginUserInfo" grant="">
			<parameters>
				<parameter name="UserId" type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
			SELECT a.UserId,a.LogonId,b.EmployeeName,b.Mobile,b.Email,b.DepartmentId
            FROM TBL_Account a 
		    INNER JOIN TBL_Employee b ON a.UserId = b.EmployeeId AND a.UserId = :UserId;
				]]>
			</body>
		</procedure>
	</procedures>
</root>
