﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using Delivery.BSL;
using Delivery.Common;

namespace Delivery.API.CMCC
{
    [Route("CMCC/api/[controller]")]
    public class HouseController : BaseController
    {
        [HttpGet("[action]")]
        public string GetHouseInfo(string StructureId, string StationCode, string strBegin, string strEnd, string StationName, string HouseName, string HouseCode, string Status, int page, int rows)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            DataTable dt = ccbsl.GetHouseInfo(StructureId, StationCode, strBegin, strEnd, StationName, HouseName, HouseCode, Status, LogonId);
            return Json4EasyUI.onDataGrid(dt, page, rows);
        }

        [HttpGet("[action]")]
        public string GetHouseInfoCheck(string StructureId, string StationCode, string strBegin, string strEnd, string StationName, string HouseName, string HouseCode, string Status, int page, int rows)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            DataTable dt = ccbsl.GetHouseInfoCheck(StructureId, StationCode, strBegin, strEnd, StationName, HouseName, HouseCode, Status, LogonId);
            return Json4EasyUI.onDataGrid(dt, page, rows);
        }

        [HttpPost("[action]")]
        public string ApproveHouse([FromForm] string WRHouseId)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            return ccbsl.ApproveWRHouse(WRHouseId);

        }

        [HttpPost("[action]")]
        public string DeleteHouse([FromForm] string WRHouseId, [FromForm] string logonId)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            return ccbsl.DeleteHouse(WRHouseId, logonId);
        }

        [HttpPost("[action]")]
        public string RejectWRHouse([FromForm] string WRHouseId, [FromForm] string RejectReason)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            return ccbsl.RejectWRHouse(WRHouseId, RejectReason);

        }

        [HttpPost("[action]")]
        public string NewHouse([FromForm] string WRStationId, [FromForm] string HouseCode, [FromForm] string HouseName, [FromForm] string Remark)
        {
            string result = string.Empty;
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            result = ccbsl.InsertHouse(WRStationId, HouseCode, HouseName, Remark, LogonId).ToString();
            return result;
        }

        [HttpPost("[action]")]
        public string ModifyHouse([FromForm] string WRHouseId, [FromForm] string WRStationId, [FromForm] string HouseCode, [FromForm] string HouseName, [FromForm] string Remark)
        {
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            string result = string.Empty;
            result = ccbsl.UpdateHouse(WRHouseId, WRStationId, HouseCode, HouseName, Remark).ToString();
            return result;
        }

        /// <summary>
        /// 导出 Excel
        /// </summary>
        /// <returns></returns>              
        [HttpGet("[action]")]
        public IActionResult ExportExcel(string StructureId, string StationCode, string strBegin, string strEnd, string StationName, string HouseName, string HouseCode, string Status)
        {
            //string LogonId = "admin";
            DataTable dataTable = GetExportData(StructureId, StationCode, strBegin, strEnd, StationName, HouseName, HouseCode, Status, LogonId);

            string name = "机房管理";
            byte[] bytes = ExcelHelper.DataTableToExcel(dataTable, null, name);
            return File(bytes, "application/ms-excel", $"{name}_{DateTime.Now:yyyyMMddHHmmssfff}.xlsx");
        }

        private DataTable GetExportData(string StructureId, string StationCode, string strBegin, string strEnd, string StationName, string HouseName, string HouseCode, string Status, string LogonId)
        {
            try
            {
                DeliveryDataBsl ccbsl = new DeliveryDataBsl();
                DataTable dt = ccbsl.GetHouseInfo(StructureId, StationCode, strBegin, strEnd, StationName, HouseName, HouseCode, Status, LogonId);
                string[] cols = new string[]
                {
                        "RowNumber", "StructureName", "StationName", "StationCode", "HouseName",
                        "HouseCode","StatusName","UserName","ApplyTime","ApproveTime","RejectCause"
                };
                DataTable myDt = dt.DefaultView.ToTable(true, cols);
                myDt.Columns["RowNumber"].ColumnName = "序号";
                myDt.Columns["StructureName"].ColumnName = "分组";
                myDt.Columns["StationName"].ColumnName = "站址名称";
                myDt.Columns["StationCode"].ColumnName = "站址编码";
                myDt.Columns["HouseName"].ColumnName = "机房名称";
                myDt.Columns["HouseCode"].ColumnName = "机房编码";
                myDt.Columns["StatusName"].ColumnName = "状态";
                myDt.Columns["UserName"].ColumnName = "申请人";
                myDt.Columns["ApplyTime"].ColumnName = "申请日期";
                myDt.Columns["ApproveTime"].ColumnName = "批准日期";
                myDt.Columns["RejectCause"].ColumnName = "退回原因";

                return myDt;
            }
            catch (Exception ex)
            {
                Logger.Log(ex);
            }
            return null;
        }

    }
}
