﻿
using Common.Logging.Pro;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;




using BDSTool.Entity.S2;
using System.Data;
using BDSTool.Entity.DRHelper;
using BDSTool.Entity.B;
using BDSTool.BLL.Convert;
using ENPC.Kolo.Entity.B_CMCC;
using BDSTool.Utility;
using BDSTool.DBUtility;
using BDSTool.DBUtility.Common;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace BDSTool.BLL.S2
{
    public partial class EquipmentBiz 
    {
        private static readonly log4net.ILog logger = LogManager.GetLogger("BDSTool");

 
        public static bool SaveEntity(TBL_Equipment e) {
            try {
                string sql;
                if (CommonUtils.IsNoProcedure) 
                { 
                    string temp = Public_ExecuteSqlService.Instance.NoStore_TBLEquipment_GetInsertSql();
                    sql = string.Format(temp,
                    e.StationId, e.EquipmentId, SHelper.GetPara(e.EquipmentName), SHelper.GetPara(e.EquipmentNo), SHelper.GetPara(e.EquipmentStyle),
                    SHelper.GetPara(e.UsedDate), SHelper.GetPara(e.Vendor), e.EquipmentCategory, e.EquipmentType, SHelper.GetPara(e.EquipmentClass),
                    SHelper.GetPara(e.Description), SHelper.GetPara(e.EquipmentTemplateId), e.HouseId, e.MonitorUnitId, e.SamplerUnitId,
                    SHelper.GetPara(e.UpdateTime), SHelper.GetPara(e.SO), e.EquipmentState, e.DisplayIndex, e.ConnectState,
                    SHelper.GetPara(e.InstalledModule)
                    );
                }
                else {
                    sql = string.Format(@"INSERT INTO TBL_Equipment (StationId, EquipmentId, EquipmentName, EquipmentNo, EquipmentStyle, UsedDate, Vendor, EquipmentCategory, EquipmentType, EquipmentClass, Description, EquipmentTemplateId, HouseId, MonitorUnitId, SamplerUnitId, UpdateTime,    SO, EquipmentState,DisplayIndex,ConnectState,InstalledModule) values ({0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13},{14},{15},{16},{17},{18},{19},{20})",
                    e.StationId, e.EquipmentId, SHelper.GetPara(e.EquipmentName), SHelper.GetPara(e.EquipmentNo), SHelper.GetPara(e.EquipmentStyle),
                    SHelper.GetPara(e.UsedDate), SHelper.GetPara(e.Vendor), e.EquipmentCategory, e.EquipmentType, SHelper.GetPara(e.EquipmentClass),
                    SHelper.GetPara(e.Description), SHelper.GetPara(e.EquipmentTemplateId), e.HouseId, e.MonitorUnitId, e.SamplerUnitId,
                    SHelper.GetPara(e.UpdateTime), SHelper.GetPara(e.SO), e.EquipmentState, e.DisplayIndex, e.ConnectState,
                    SHelper.GetPara(e.InstalledModule)
                    );
                }

                DBHelper.ExecuteNonQuery(sql);
            }
            catch (Exception ex) {
                logger.ErrorFormat("EquipmentBiz.SaveEntity();Equipment={0},{1};Error={2}", e.EquipmentId, e.EquipmentName, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }
    }
}
