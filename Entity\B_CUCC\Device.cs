﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B_CUCC
{
    /// <summary>
    /// Login 报文中的设备数据结构
    /// </summary>
    public sealed class Device
    {
        public string Id { get; set; }

        public string RId { get; set; }

        public List<Signal> SignalList { get; set; }


        /// <summary>
        /// 设备地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 端口下挂设备使用的协议
        /// </summary>
        public string Protocol { get; set; }

        /// <summary>
        /// 协议版本
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// 端口下挂设备的时间，即设备接入监控的时间
        /// </summary>
        public string UpdateTime { get; set; }


        #region SiteWeb配置

        public int MyMonitorUnitId { get; set; }
        public int MyStationId { get; set; }
        public int MyDeviceId { get; set; }

        #endregion

        public Device() { }

        /// <summary>
        /// 设备
        /// </summary>
        /// <param name="id">设备编码</param>
        /// <param name="rid">设备资管编码</param>
        public Device(string id, string rid)
        {
            Id = id;
            RId = rid;
        }

        public Device(string id, string rid, List<Signal> listId)
        {
            Id = id;
            RId = rid;
            SignalList = listId;
        }

        public Device(string id, string rid, string address, string protocol, string version, string updateTime)
        {
            Id = id;
            RId = rid;
            Address = address;
            Protocol = protocol;
            Version = version;
            UpdateTime = updateTime;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append(Id).Append(",").Append(RId);
            return sb.ToString();
        }

        public string ConvertToString()
        {
            StringBuilder sb = new StringBuilder();
            if (SignalList.Count > 0)
            {
                foreach (Signal sdv in SignalList)
                {
                    sb.AppendFormat("{0},{1},{2},{3},{4},{5},{6},{7},{8},{9}\n",
                        Id, RId, sdv.Id, sdv.SetValue, sdv.HLimit, sdv.SHLimit, sdv.LLimit, sdv.SLLimit, sdv.Threshold, sdv.RelativeVal, sdv.IntervalTime);
                }
            }
            else
            {
                sb.AppendFormat("{0},{1}", Id, RId);
            }
            return sb.ToString();
        }

        public string PartToString()
        {
            StringBuilder sb = new StringBuilder();
            if (SignalList.Count > 0)
            {
                foreach (Signal sdv in SignalList)
                {
                    sb.AppendFormat("{0},{1},{2},{3}\n",
                        Id, RId, sdv.Id, sdv.RecordTime);
                }
            }
            else
            {
                sb.AppendFormat("{0},{1}", Id, RId);
            }
            return sb.ToString();
        }
        public string PartValueToString()
        {
            StringBuilder sb = new StringBuilder();
            if (SignalList.Count > 0)
            {
                foreach (Signal sdv in SignalList)
                {
                    sb.AppendFormat("{0},{1},{2},{3}\n",
                        Id, RId, sdv.Id, sdv.Value);
                }
            }
            else
            {
                sb.AppendFormat("{0},{1}", Id, RId);
            }
            return sb.ToString();
        }

        public string PartFiledToString()
        {
            StringBuilder sb = new StringBuilder();
            if (SignalList.Count > 0)
            {
                foreach (Signal sdv in SignalList)
                {
                    sb.AppendFormat("{0},{1},{2},{3},{4}\n",
                        Id, RId, sdv.Id, sdv.Value, sdv.RecordTime);
                }
            }
            else
            {
                sb.AppendFormat("{0},{1}", Id, RId);
            }
            return sb.ToString();
        }
        public string StringToString()
        {
            StringBuilder sb = new StringBuilder();
            if (SignalList.Count > 0)
            {
                foreach (Signal signal in SignalList)
                {
                    sb.AppendFormat("{0},{1},{2}\n", Id, RId, signal.Id);
                }
            }
            else
            {
                sb.AppendFormat("{0},{1}", Id, RId);
            }
            return sb.ToString();
        }
        public string PartDelayToString()
        {
            StringBuilder sb = new StringBuilder();
            if (SignalList.Count > 0)
            {
                foreach (Signal sdv in SignalList)
                {
                    sb.AppendFormat("{0},{1},{2},{3},{4}\n",
                        Id, RId, sdv.Id, sdv.BDelay, sdv.EDelay);
                }
            }
            else
            {
                sb.AppendFormat("{0},{1}", Id, RId);
            }
            return sb.ToString();
        }

        public string PortInfoToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0},{1},{2},{3},{4},{5}", Id, RId, Address, Protocol, Version, UpdateTime);
            return sb.ToString();
        }

    }
}
