﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 上报变化数据响应
    /// </summary>
    public class SendChangedDataAck : BMessage
    {
        /// <summary>
        /// 返回结果
        /// </summary>
        [Newtonsoft.Json.JsonProperty(Order = 1)]
        [Newtonsoft.Json.JsonConverter(typeof(CustomSringConverter))] 
        public EnumResult Result { get; set; }

        /// <summary>
        /// 失败的原因
        /// </summary>
        [Newtonsoft.Json.JsonProperty(Order = 2)]
        public string FailureCause { get; set; }

        public SendChangedDataAck() : base()
        {
            MessageType = (int)BMessageType.SEND_CHANGED_DATA_ACK;
        }

        public SendChangedDataAck(EnumResult result, string failureCause) : base()
        {
            MessageType = (int)BMessageType.SEND_CHANGED_DATA_ACK;

            Result = result;
            FailureCause = failureCause;
        }

        public override string Serialize()
        {
            string result = string.Empty;
            try
            {
                JsonSerializerSettings settings = new JsonSerializerSettings();
                settings.ContractResolver = new CustomContractResolver();
                TIotEntity iotEntity = new TIotEntity(this);
                result = JsonConvert.SerializeObject(iotEntity, settings);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SendChangedDataAck.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SendChangedDataAck.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
            }
            return result;
        }

        public override string ToString()
        {
            return string.Format("{0},{1}:{2},{3}", MessageId, (BMessageType)MessageType, Result, FailureCause);
        }
    }
}