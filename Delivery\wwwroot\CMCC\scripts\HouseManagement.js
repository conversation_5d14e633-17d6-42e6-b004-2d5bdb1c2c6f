﻿'use strict';

//global
var cookie = getCookie();
var userRole = getUserRole();

//加载站址信息
var loadStationManagementData = function () {
    $('#lookStationTable').datagrid({
        method: 'GET',
        url: `${STATION_HANDLER}/GetStationInfoCheck`,
        title: '站址信息列表',
        idField: 'WRStationId', //主键
        singleSelect: true,
        pagination: true,
        pageSize: 30,
        pageList: [30],
        queryParams: {
            //action: 'GetStationInfoCheck',
            StationName: $('#looktxtStationNameq').val(),
            StationCode: $('#looktxtStationCodeq').val(),
            strBegin: '',
            strEnd: '',
            StationType: '',
            StationStatus: '3'
        },
        width: function () { return document.body.clientWidth * 0.9; }, //设置表格宽度为客户端页面的0.9
        height: 'auto',
        fitColumns: true,
        columns: [[
            {
                field: 'ck', align: 'center', title: '选择 ', width: 9,
                formatter: function (value, row, index) {
                    return '<input type="radio" id="radio' + index + '" radiogroup="lookinfoCK" />';
                }
            },
            { field: 'RowNumber', title: '序号', width: 10, align: 'center' },
            { field: 'StationName', title: '站址名称', width: 10, align: 'left' },
            { field: 'StationCode', title: '站址编码', width: 10, align: 'left' },
            { field: 'StationCategoryName', title: '站址类型', width: 12, align: 'left' },
            { field: 'Address', title: '基站地址', width: 15, align: 'left' },
            { field: 'ProvinceName', title: '省', width: 22, align: 'left' },
            { field: 'CityName', title: '市', width: 22, align: 'left' },
            { field: 'CountyName', title: '区', width: 22, align: 'left' }

        ]],
        checkOnSelect: true,
        onClickRow: function (index, row) {
            var currentRadio = 'radio' + index;
            $('input:radio').each(function () {
                $(this).attr('checked', false);
            });
            $('#' + currentRadio).attr('checked', 'checked');
        },
        onDblClickRow: function (index, row) {
            $('#divLookforStation').dialog('close');
            $('#txtStationCode').val(row.WRStationId);
            $('#txtStationName').textbox('setValue', row.StationName);
            $('#HouseDialog').dialog('open');
        },
        onLoadError: function () {
            alertInfo('查询出错！', 'error');
        }
    });
};

//加载机房信息
var loadHouseData = function () {
    $('#HouseTable').datagrid({
        method: 'GET',
        url: `${HOUSE_HANDLER}/GetHouseInfo`,
        title: '机房信息列表',  //rownumbers: true, //行号  striped: true, //隔行变色                
        idField: 'HouseCode', //主键
        singleSelect: true,
        pagination: true,
        pageSize: 30,
        pageList: [30],
        queryParams: {
            //action: 'GetHouseInfo',
            HouseName: $('#txtHouseNameq').val(),
            HouseCode: $('#txtHouseCodeq').val(),
            strBegin: $('#txtApplyTimeq').val(),
            strEnd: $('#txtApplyTimeq2').val(),
            StructureId: $('#StructureIdLook').combotree('getValue'),
            Status: $('#HouseStatusQ').combobox('getValue')
        },
        width: function () { return document.body.clientWidth * 0.9; }, //设置表格宽度为客户端页面的0.9
        height: 'auto',
        fitColumns: true,
        columns: [[
            { field: 'RowNumber', title: '序号', width: 10, align: 'center' },
            { field: 'StructureName', title: '分组', width: 15, align: 'left' },
            { field: 'StationName', title: '站址名称', width: 10, align: 'left' },
            { field: 'StationCode', title: '站址编码', width: 10, align: 'left' },
            { field: 'HouseName', title: '机房名称', width: 12, align: 'left' },
            { field: 'HouseCode', title: '机房编码', width: 15, align: 'left' },
            {
                title: '状态', field: 'StatusName', width: 15, align: 'center', halign: 'center',
                formatter: function (value, row, index) {
                    if (value === '申请退回')
                        return '<font color="red">' + value + '</font>';
                    else if (value === '申请通过')
                        return '<font color="green">' + value + '</font>';
                    else
                        return value;
                }
            },
            { field: 'UserName', title: '申请人', width: 10, align: 'left' },
            { field: 'ApplyTime', title: '申请日期', width: 13, align: 'left' },
            { field: 'ApproveTime', title: '批准日期', width: 13, align: 'left' },
            { field: 'RejectCause', title: '退回原因', width: 30, align: 'left' }
        ]],
        onClickRow: function (index, row) {
            if (row.HouseStatus === '1') {
                $('#btn_Pass').removeAttr('disabled');
                $('#btn_Pass').css({ 'background-color': '' });
                $('#btn_back').removeAttr('disabled');
                $('#btn_back').css({ 'background-color': '' });
            }
            else if (row.HouseStatus === '2' || row.HouseStatus === '3') {
                $('#btn_Pass').attr('disabled', 'disabled');
                $('#btn_Pass').css({ 'background-color': 'Silver' });
                $('#btn_back').attr('disabled', 'disabled');
                $('#btn_back').css({ 'background-color': 'Silver' });
            }
        },
        onLoadError: function () {
            alertInfo('查询出错！', 'error');
        }
    });
};

//检查查询条件
var checkDateValid = function () {
    if (!$('#txtApplyTimeq').val() && $('#txtApplyTimeq2').val()
        || $('#txtApplyTimeq').val() && !$('#txtApplyTimeq2').val()) {
        alertInfo('申请开始日期和申请截止日期必须同时存在！');
        return false;
    }
    if ($('#txtApplyTimeq').datetimebox('isValid') && $('#txtApplyTimeq2').datetimebox('isValid'))
        return true;
    alertInfo('请输入正确的查询条件！');
    return false;
};

//检查新增/编辑机房信息
var checkinfo = function () {
    if (!$('#txtStationName').val()) {
        alertInfo('所属站址没有选择！');
        $('#txtStationName').next('span').find('input').focus();
        return false;
    }
    if (!$('#txtHouseName').val()) {
        alertInfo('机房名称没有输入！');
        $('#txtHouseName').next('span').find('input').focus();
        return false;
    }
    if (!$('#txtRemark').val()) {
        alertInfo('机房备注没有输入！');
        $('#txtRemark').next('span').find('input').focus();
        return false;
    }
    if (!(~~$('#buildingSN').val() > 0 && ~~$('#buildingSN').val() < 100)) {
        if (!$('#buildingSN').val())
            alertInfo('楼栋顺序号没有输入！');
        else
            alertInfo('楼栋顺序号需在1-99区间！');
        $('#buildingSN').next('span').find('input').focus();
        return false;
    }
    if (!(~~$('#floorSN').val() > 0 && ~~$('#floorSN').val() < 100 || ~~$('#floorSN').val() > -6 && ~~$('#floorSN').val() < 0)) {
        if (!$('#floorSN').val())
            alertInfo('楼层顺序号没有输入！');
        else
            alertInfo('楼层顺序号需在-1~-5或1~100区间！');
        $('#floorSN').next('span').find('input').focus();
        return false;
    }
    if ($('#houseType').combobox('getValue') === '-1') {
        alertInfo('机房类型没有选择！');
        $('#floorSN').next('span').find('input').focus();
        return false;
    }
    return true;
};

//回车事件
document.onkeydown = function (event) {
    var e = event || window.event;
    if (e.keyCode === 13 && document.activeElement.tagName !== 'TEXTAREA') {
        e.returnValue = false;
        e.cancel = true;
        if (!$('#divLookforStation').dialog('options')['closed']) {
            $('#lookbtn_seach').focus();
            $('#lookbtn_seach').click();
        } else {
            $('#btn_seach').focus();
            $('#btn_seach').click();
        }
    }
};

//初始化控件
var init = function () {
    var groupInfo = getGroupInfo();
    if (groupInfo) {
        var obj = JSON.parse(groupInfo);
        $('#StructureIdLook').combotree('tree').tree('loadData', obj);
        $('#StructureIdLook').combotree('setValues', [obj[0].id]);
    }

    loadDicUiDropControl($('#HouseStatusQ'), 5, 5);
    loadDicUiDropControl($('#houseType'), 7, 5);

    $('#txtApplyTimeq').datetimebox('setValue', new Date(new Date().setDate(1)).format('yyyy-MM-dd') + ' 00:00:00');
    $('#txtApplyTimeq2').datetimebox('setValue', new Date(new Date().getTime() + 1000 * 60 * 60).format('yyyy-MM-dd hh:mm:ss'));

    //新增按钮
    $('#btn_opendialog').click(function () {
        $('#txtStationCode').val('');
        $('#txtStationName').textbox('setValue', '');
        $('#txtHouseName').textbox('setValue', '');
        $('#txtHouseCode').textbox('setValue', '');
        $('#txtRemark').textbox('setValue', '');
        $('#buildingSN').numberbox('setValue', '');
        $('#floorSN').numberbox('setValue', '');
        $('#houseType').combobox('setValue', '-1');

        $('#btn_new').show();
        $('#btn_modify').hide();
        $('#HouseDialog').dialog('setTitle', '新增机房');
        $('#HouseDialog').dialog('open');
    });

    //查询
    $('#btn_seach').click(function () {
        if (!checkDateValid()) {
            return false;
        }
        loadHouseData();
    });

    //重置
    $('#btn_Reset').click(function () {
        $('#txtHouseNameq').textbox('setValue', '');
        $('#txtHouseCodeq').textbox('setValue', '');
        $('#txtApplyTimeq').datetimebox('setValue', '');
        $('#txtApplyTimeq2').datetimebox('setValue', '');
        $('#HouseStatusQ').combobox('setValue', '-1');
        $('#StructureIdLook').combotree('setValue', '-1');
    });

    //通过
    $('#btn_Pass').click(function () {
        var row = $('#HouseTable').datagrid('getSelected');
        if (row) {
            $.messager.confirm('提示框', '你确定要通过审批吗？', function (r) {
                if (r) {
                    $.ajax({
                        type: 'POST',
                        url: `${HOUSE_HANDLER}/ApproveHouse`,
                        contentType: 'application/x-www-form-urlencoded',
                        data: {
                            //action: 'ApproveHouse',
                            WRHouseId: row.WRHouseId
                        },
                        success: function (data) {
                            if (data === '1') {
                                refreshNeedApproveResource();
                                loadHouseData();
                                $('#btn_Pass').attr('disabled', 'disabled');
                                $('#btn_Pass').css({ 'background-color': 'Silver' });
                                $('#btn_back').attr('disabled', 'disabled');
                                $('#btn_back').css({ 'background-color': 'Silver' });
                                alertInfo('审批成功！');
                            } else if (data === '-1')
                                alertInfo('-1: 机房所属站址尚未通过审核,不允许审核机房！', 'error');
                            else if (data === '-2')
                                alertInfo('-2: 已审核过的机房不能重复审核！', 'error');
                            else
                                alertInfo(data + ': 出现错误！', 'error');
                        },
                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                        }
                    });
                }
            });
        }
        else
            alertInfo('请选择要通过的行！');
    });

    //编辑
    $('#btn_Edit').click(function () {
        var row = $('#HouseTable').datagrid('getSelected');
        $('#btn_new').hide();
        $('#btn_modify').show();
        $('#HouseDialog').dialog('setTitle', '编辑机房');
        if (row) {
            $('#txtWRHouseId').val(row.WRHouseId);
            $('#txtStationCode').val(row.WRStationId);
            $('#txtStationName').textbox('setValue', row.StationName);
            $('#txtHouseName').textbox('setValue', row.HouseName);
            $('#txtHouseCode').textbox('setValue', row.HouseCode);
            $('#txtRemark').textbox('setValue', row.Remark);
            $('#buildingSN').numberbox('setValue', row.HouseCode.substr(0, 2));
            $('#floorSN').numberbox('setValue', row.HouseCode.substr(2, 2).replace(/^9(\d)/, '-$1'));
            $('#houseType').combobox('setValue', row.HouseCode.substr(6, 2).replace(/^0(\d)/g, '$1'));

            $('#HouseDialog').dialog('open');
        }
        else
            alertInfo('请选择要修改的行！');
    });

    //删除
    $('#btn_Delete').on('click', function (e) {
        var row = $('#HouseTable').datagrid('getSelected');
        if (row) {
            $.messager.confirm('确认', '删除机房前需先删除该机房设备或调整该机房设备所属机房，您确认删除吗？',
                function (r) {
                    if (r)
                        $.ajax({
                            type: 'POST',
                            url: `${HOUSE_HANDLER}/DeleteHouse`,
                            contentType: 'application/x-www-form-urlencoded',
                            data: {
                                //action: 'DeleteHouse',
                                WRHouseId: row.WRHouseId
                            },
                            async: false,
                            success: function (data) {
                                if (data === '1') {
                                    alertInfo('删除成功！');
                                    refreshNeedApproveResource();
                                    loadHouseData();
                                } else if (data === '-1')
                                    alertInfo('-1: 当前用户无权限删除此SU！', 'error');
                                else if (data === '-2')
                                    alertInfo('-2: 机房下有设备时不允许删除！', 'error');
                                else if (data === '-6')
                                    alertInfo('-6: 机房下有FSU时不允许删除！', 'error');
                                else
                                    alertInfo(data + ': 删除出现错误！', 'error');
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                            }
                        });
                });
        } else
            alertInfo('请选择要删除的行！');
    });

    //退回
    $('#btn_back').click(function () {
        var row = $('#HouseTable').datagrid('getSelected');
        if (row) {
            $('#txtStationCode').val(row.WRHouseId);
            $('#TxtRejectReason').textbox('setValue', '');
            $('#BackDialog').dialog('open');
        }
        else
            alertInfo('请选择要退回的行！');
    });

    //站址查询-查询按钮
    $('#lookbtn_seach').click(function () {
        loadStationManagementData();
    });

    //站址查询-关闭按钮
    $('#lookbtn_close').click(function () {
        $('#divLookforStation').dialog('close');
        $('#HouseDialog').dialog('open');
    });

    //站址查询-重置按钮
    $('#lookbtn_Reset').click(function () {
        $('#looktxtStationNameq').textbox('setValue', '');
        $('#looktxtStationCodeq').textbox('setValue', '');
    });

    //站址查询- 选择按钮
    $('#lookbtn_Select').click(function () {
        var row = $('#lookStationTable').datagrid('getSelected');
        if (row) {
            $('#divLookforStation').dialog('close');
            $('#txtStationCode').val(row.WRStationId);
            $('#txtStationName').textbox('setValue', row.StationName);
            $('#HouseDialog').dialog('open');
        }
        else
            alertInfo('请选择要添加行！');
    });

    //提交新增
    $('#btn_new').click(function () {
        if (checkinfo()) {
            $.messager.confirm('提示框', '你确认要提交吗?', function (r) {
                if (r) {
                    var houseCode = $('#buildingSN').numberbox('getValue').lpad(2, '0') + $('#floorSN').numberbox('getValue').replace(/-/g, '9').lpad(2, '0')
                        + '00' + $('#houseType').combobox('getValue').lpad(2, '0');
                    $.ajax({
                        type: 'POST',
                        url: `${HOUSE_HANDLER}/newHouse`,
                        contentType: 'application/x-www-form-urlencoded',
                        data: {
                            //action: 'newHouse',
                            WRStationId: $('#txtStationCode').val(),
                            //HouseCode: $('#txtHouseCode').val(),
                            HouseCode: houseCode,
                            HouseName: $('#txtHouseName').val(),
                            Remark: $('#txtRemark').val()
                        },
                        success: function (data) {
                            if (data === '1') {
                                refreshNeedApproveResource();
                                alertInfo('添加成功！');
                                loadHouseData();
                                $('#HouseDialog').dialog('close');
                            }
                            else if (data === '-1')
                                alertInfo('-1: 保存失败，机房编码重复！');
                            else if (data === '-2')
                                alertInfo('-2: 保存失败，机房名称重复！');
                            else
                                alertInfo(data + ': 保存失败，请查看日志文件！');
                        },
                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                        }
                    });
                }
            });
        }
    });

    //提交编辑修改
    $('#btn_modify').click(function () {
        if (checkinfo()) {
            $.messager.confirm('提示框', '你确认要修改吗?', function (r) {
                if (r) {
                    var houseCode = $('#buildingSN').numberbox('getValue').lpad(2, '0') + $('#floorSN').numberbox('getValue').replace(/-/g, '9').lpad(2, '0')
                        + '00' + $('#houseType').combobox('getValue').lpad(2, '0');
                    $.ajax({
                        type: 'POST',
                        url: `${HOUSE_HANDLER}/ModifyHouse`,
                        contentType: 'application/x-www-form-urlencoded',
                        data: {
                            //action: 'ModifyHouse',
                            WRHouseId: $('#txtWRHouseId').val(),
                            WRStationId: $('#txtStationCode').val(),
                            HouseCode: houseCode,
                            HouseName: $('#txtHouseName').val(),
                            Remark: $('#txtRemark').val()
                        },
                        success: function (data) {
                            if (data === '1') {
                                alertInfo('修改成功！');
                                $('#btn_Pass').removeAttr('disabled');
                                $('#btn_Pass').css({ 'background-color': '' });
                                $('#btn_back').removeAttr('disabled');
                                $('#btn_back').css({ 'background-color': '' });
                                loadHouseData();
                                $('#HouseDialog').dialog('close');
                            }
                            else if (data === '-1') {
                                alertInfo('-1: 保存失败，机房编码重复！');
                                return;
                            }
                            else if (data === '-2') {
                                alertInfo('-2: 保存失败，机房名称重复！');
                                return;
                            }
                            else if (data === '-5') {
                                alertInfo('-5: 保存失败，修改之前状态已变为[审核通过]！');
                                return;
                            }
                            else {
                                alertInfo(data + ': 保存失败，请查看日志文件！');
                                return;
                            }
                        },
                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                        }
                    });
                }
            });
        }
    });

    //提交退回
    $('#btn_AppBackRe').click(function () {
        if (!$('#TxtRejectReason').val()) {
            alertInfo('请录入退回原因！');
            return false;
        }
        $.messager.confirm('提示框', '你确认要退回吗?', function (r) {
            if (r) {
                $.ajax({
                    type: 'POST',
                    url: `${HOUSE_HANDLER}/RejectWRHouse`,
                    contentType: 'application/x-www-form-urlencoded',
                    data: {
                        //action: 'RejectWRHouse',
                        WRHouseId: $('#txtStationCode').val(),
                        RejectReason: $('#TxtRejectReason').val()
                    },
                    success: function (data) {
                        if (data === '1') {
                            refreshNeedApproveResource();
                            $('#btn_Pass').attr('disabled', 'disabled');
                            $('#btn_Pass').css({ 'background-color': 'Silver' });
                            $('#btn_back').attr('disabled', 'disabled');
                            $('#btn_back').css({ 'background-color': 'Silver' });
                            loadHouseData();
                            $('#BackDialog').dialog('close');
                            alertInfo('退回成功！');
                        } else if (data === '-1')
                            alertInfo('-1: 已审核通过的机房不能退回！', 'error');
                        else
                            alertInfo(data + ': 出现错误！', 'error');
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                    }
                });
            }
        });
    });

    //机房对话框关闭
    $('#btn_close').click(function () {
        $('#HouseDialog').dialog('close');
    });

    //站址选择弹出
    $('#btn_LookStation, #divStationName').click(function () {
        $('#looktxtStationNameq').textbox('setValue', '');
        $('#looktxtStationCodeq').textbox('setValue', '');
        loadStationManagementData();
        $('#HouseDialog').dialog('close');
        $('#divLookforStation').dialog('open');
        $('#looktxtStationNameq').focus();
    });

    //导出Excel
    $('#btn_Excel').click(function () {
        var f = $('<form action="api/house/exportexcel" method="get" id="fm1" accept-charset="UTF-8"></form>');
        $('<input type="hidden"  name="HouseName"/>').val($('#txtHouseNameq').val()).appendTo(f);
        $('<input type="hidden"  name="HouseCode"/>').val($('#txtHouseCodeq').val()).appendTo(f);
        $('<input type="hidden"  name="strBegin"/>').val($("#txtApplyTimeq").val()).appendTo(f);
        $('<input type="hidden"  name="strEnd"/>').val($("#txtApplyTimeq2").val()).appendTo(f);
        $('<input type="hidden"  name="Status"/>').val($('#HouseStatusQ').combobox('getValue')).appendTo(f);
        $('<input type="hidden"  name="StructureId"/>').val($('#StructureIdLook').combotree('getValue')).appendTo(f);
        $('<input type="hidden"  name="StationName"/>').val('').appendTo(f);
        f.appendTo(document.body).submit().remove();
    });

    //规范浏览
    $('#btn_help').click(function () {
        $.ajax({
            type: 'GET',
            url: GET_STANDARDDOC_HANDLER,
            contentType: 'application/x-www-form-urlencoded',
            data: { type: 2 },
            async: false,
            success: function (data) {
                $('#standardContent').val(data);
                $('#standardContent').attr('disabled', 'disbaled');
                $('#standardDoc').dialog('open');
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
            }
        });
        
        //$('#standardDoc').find('textarea').focus();
    });

    //规范浏览编辑
    $('#btn_docEdit').click(function () {
        $('#standardContent').removeAttr('disabled');
        $('#standardContent').focus();
    });

    //规范浏览保存
    $('#btn_docSave').click(function () {
        $('#standardContent').attr('disabled', 'disabled');
        $.messager.confirm('确认', '您确认保存吗？',
            function (r) {
                if (r) {
                    $.ajax({
                        type: 'POST',
                        contentType: 'application/json; charset=UTF-8',
                        url: SAVE_STANDARDDOC_HANDLER,
                        data: JSON.stringify({ FileName: 'HouseStandard', Content: $('#standardContent').val().replace(/(?!\r)\n/g, '\r\n') }),
                        success: function (data) {
                            alertInfo(data);
                        },
                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                        }
                    });
                }
            });
    });
};

//onload
$(function () {
    init();

    if (cookie['userinfo']['UserId'] !== '-1' || userRole !== '系统管理员')
        $('#toolbar').remove();
    if (userRole !== '系统管理员') {
        $('#btn_Pass').remove();
        $('#btn_back').remove();
    } else {
        $('#btn_Delete').remove();
        $('#btn_opendialog').remove();
    }

    loadHouseData();

    $('div.mask').remove();
});