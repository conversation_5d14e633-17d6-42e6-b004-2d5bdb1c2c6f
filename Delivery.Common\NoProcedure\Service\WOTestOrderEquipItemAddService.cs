﻿
using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;


namespace Delivery.Common.NoProcedure.Service
{

    public class WOTestOrderEquipItemAddService
    {
        private static WOTestOrderEquipItemAddService _instance = null;

        public static WOTestOrderEquipItemAddService Instance
        {
            get
            {
                if (_instance == null) _instance = new WOTestOrderEquipItemAddService();
                return _instance;
            }
        }

        public void TestOrderEquipItemAdd(int orderId, int equipmentId, string uriProtocol, string uriImage)
        { 
            try
            {
                using (DbHelper dbHelper = new DbHelper())
                {
                    ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                    Dictionary<string, object> realParams = new Dictionary<string, object>();

                    int standardType = 1;
                    string standardCategoryValue = FunctionWOGetType();
                    int baseEquipmentId = FunctionWOGetEquipBaseType(equipmentId);

                    realParams.Add("BaseEquipmentId", baseEquipmentId);
                    string baseEquipmentName = (string)execHelper.ExecuteScalar("WO_TestOrderEquipItem_Add_GetBaseName", realParams);

                    realParams.Add("OrderId", orderId);
                    realParams.Add("EquipmentId", equipmentId);
                    realParams.Add("BaseEquipmentName", baseEquipmentName);
                    realParams.Add("UriProtocol", uriProtocol);
                    realParams.Add("UriImage", uriImage);
                    realParams.Add("SaveTime", DateTime.Now.ToString());

                    execHelper.ExecuteNonQuery("WO_TestOrderEquipItem_Add_InsertFirst", realParams);

               
                    DataTable equipInfo = execHelper.ExecDataTable("WO_TestOrderEquipItem_Add_GetEquipInfo", realParams);
                    string equipmentName = "",equipmentCategoryName = "";
                    if (equipInfo != null && equipInfo.Rows.Count != 0 ) {
                        equipmentName = equipInfo.Rows[0]["EquipmentName"] == null ? "": equipInfo.Rows[0]["EquipmentName"].ToString();
                        equipmentCategoryName = equipInfo.Rows[0]["EquipmentCategoryName"] == null ? "" : equipInfo.Rows[0]["EquipmentCategoryName"].ToString();
                    }

                    realParams.Add("EquipmentName", equipmentName);
                    realParams.Add("EquipmentCategoryName", equipmentCategoryName);
                    standardCategoryValue = "3";
                    if (standardCategoryValue != null && !standardCategoryValue.Equals(""))
                    {
                        realParams.Add("StandardType", int.Parse(standardCategoryValue));
                        //CMCC
                        if (standardCategoryValue.Equals("1"))
                        {
                            //信号
                            execHelper.ExecuteNonQuery("WO_TestOrderEquipItem_Add_CMCCInsertSecond", realParams);
                            //事件
                            execHelper.ExecuteNonQuery("WO_TestOrderEquipItem_Add_CMCCInsertThird", realParams);
                            //控制
                            execHelper.ExecuteNonQuery("WO_TestOrderEquipItem_Add_CMCCInsertForth", realParams);
                        }
                        //CUCC
                        else if (standardCategoryValue.Equals("3"))
                        {
                            //信号
                            execHelper.ExecuteNonQuery("WO_TestOrderEquipItem_Add_CUCCInsertSignalDic", realParams);
                            //事件
                            execHelper.ExecuteNonQuery("WO_TestOrderEquipItem_Add_CUCCInsertEventDic", realParams);
                            //控制
                            execHelper.ExecuteNonQuery("WO_TestOrderEquipItem_Add_CUCCInsertControlDic", realParams);
                        }
                    } 
                }
            }
            catch (Exception ex)
            { 
                Logger.Log(ex);
            }
        }
        public static string FunctionWOGetType() {
            string configValue = "";
            try
            {
                using (DbHelper dbHelper = new DbHelper())
                {
                    ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                    configValue = (string)execHelper.ExecuteScalar("WO_FuncBGetType_GetData", null);
                }
            }
            catch (Exception ex)
            {
                Logger.Log(ex);
            }
            return configValue;
        }
        public static int FunctionWOGetEquipBaseType(int equipmentId)
        {
            int convertBaseEquipmentId = -999;
            try
            {
                using (DbHelper dbHelper = new DbHelper())
                {
                    Dictionary<string, object> realParams = new Dictionary<string, object>();
                    ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                    object baseTypeId = -999;
                    realParams.Add("EquipmentId", equipmentId);
                    baseTypeId = execHelper.ExecuteScalar("WO_FuncGetEquipBaseType_GetMinBaseTypeId", realParams);
                    if ((baseTypeId==DBNull.Value && baseTypeId == null)|| (decimal)baseTypeId == -999){
                        baseTypeId = (int)execHelper.ExecuteScalar("WO_FuncGetEquipBaseType_GetMinBaseTypeIdWithFirstQueryIsNull", realParams);
                    }
                    // 将字符串左侧填充零
                    string strAnyBaseId = baseTypeId.ToString().PadLeft(10, '0');

                    // 取左侧4个字符
                    string left4Chars = strAnyBaseId.Substring(0, Math.Min(4, strAnyBaseId.Length));

                    // 将最终字符串转换为有符号整数
                    convertBaseEquipmentId = int.Parse(left4Chars);
                }
            }
            catch (Exception ex)
            {
                Logger.Log(ex);
            }
            return convertBaseEquipmentId;
        }
    }
}
