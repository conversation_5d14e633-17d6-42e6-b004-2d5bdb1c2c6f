﻿using BLL;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using testorder.dal;

using DM.TestOrder.Model;

using DM.TestOrder.Entity;


namespace DM.TestOrder.Service.Convert {
    //public static void CreateTestOrder(string ApplyOrderId, string ApplyUserId, string StationId, string StationName) {
    public partial class Mapper {
        //  public static WO_TestOrderFlow FlowInfoToEntity(TestOrderFlowInfo info) {
        //    var flow = new WO_TestOrderFlow() {
        //        OrderId = info.OrderId,
        //        OldOrderState = info.OldOrderState,
        //        NewOrderState = info.NewOrderState,
        //        StateSetUserId = info.StateSetUserId,
        //        IsApprove= info.IsApprove,

        //        Decision = info.Decision,
        //        Note = info.Note,

        //        StateSetUserName =  Account.GetUserNameByUserId(info.StateSetUserId),
        //        SaveTime = DateTime.Now,
        //    };

        //    if (flow.NewOrderState == 2 && flow.OldOrderState != 1) {
        //        throw new Exception("工单非待待提交状态");
        //    }
        //    else if (flow.NewOrderState == 3 && flow.OldOrderState != 2) {
        //        throw new Exception("工单非待专家审批状态");
        //    }
        //    else if (flow.NewOrderState == 4 && flow.OldOrderState != 3) {
        //        throw new Exception("工单非待入网审批状态");
        //    }

        //    switch (flow.NewOrderState){
        //      case 2:
        //        flow.FlowText = string.Format("{0}_提交专家组审核_提交人:{1}", flow.SaveTime.ToString("yyyyMMdd HH:mm:ss"), info.StateSetUserName);
        //        break;
        //      case 3:
        //        flow.FlowText = string.Format("{0}_提交入网复审_提交人:{1}", flow.SaveTime.ToString("yyyyMMdd HH:mm:ss"), info.StateSetUserName);
        //        break;
        //      case 4:
        //        flow.FlowText = string.Format("{0}_归档_提交人:{1}", flow.SaveTime.ToString("yyyyMMdd HH:mm:ss"), info.StateSetUserName);
        //        break;
        //    }

            

        //    return flow;
        //}

        //public static WO_TestOrderFlow ToEntity_TestOrderFlow4Submit(int orderId) {
        //    var model = BLL.TestOrderDal.GetOne(orderId);
        //    var flow = new WO_TestOrderFlow() {
        //        OrderId = model.OrderId,
        //        OldOrderState = 1,
        //        NewOrderState = 2,
        //        StateSetUserId = model.ApplyUserId.Value,
        //        StateSetUserName = model.ApplyUserName,
        //        SaveTime = DateTime.Now,

        //        IsApprove= 1
        //    };

        //    if (flow.OldOrderState != 1) {
        //        throw new Exception("工单非待待提交状态");
        //    }

        //    flow.FlowText = string.Format("{0}_提交专家组审核_提交人:{1}",
        //        flow.SaveTime.ToString("yyyyMMdd HH:mm:ss"), model.StateSetUserName);

        //    return flow;
        //}


        //public static WO_TestOrderFlow ToEntity_TestOrderFlow4Expert( SectionExpertApprove secApprove) {
        //    var flow = new WO_TestOrderFlow(){
        //        OrderId = secApprove.OrderId,
        //        OldOrderState= 2,
        //        NewOrderState=secApprove.IsApprove?3:1,
        //        StateSetUserId = secApprove.StateSetUserId,
        //        StateSetUserName = secApprove.StateSetUserName,
        //        Decision= secApprove.ExpertDecision,
        //        Note= secApprove.ExpertNote,
        //        IsApprove = secApprove.IsApprove ? 1 : 0,
        //        SaveTime= DateTime.Now
        //    };

        //    //if (flow.OldOrderState != 2) {
        //    //    throw new Exception("工单非待专家组审批状态");
        //    //}

        //    flow.FlowText = string.Format("{0}_专家组审批{1}_审批人:{2}",
        //        flow.SaveTime.ToString("yyyyMMdd HH:mm:ss"), secApprove.IsApprove ? "通过" : "退回", secApprove.StateSetUserName);

        //    return flow;
        //}

        //public static WO_TestOrderFlow ToEntity_TestOrderFlow4FinalApprove(SectionFinalApprove SectionFinalApprove) {
        //    var flow = new WO_TestOrderFlow() {
        //        OrderId = SectionFinalApprove.OrderId,
        //        OldOrderState = 3,
        //        NewOrderState = SectionFinalApprove.IsApprove ? 4 : 1,
        //        StateSetUserId = SectionFinalApprove.StateSetUserId,
        //        StateSetUserName = SectionFinalApprove.StateSetUserName,
        //        Decision = SectionFinalApprove.FinalDecision,
        //        Note = SectionFinalApprove.FinalNote,
        //        IsApprove = SectionFinalApprove.IsApprove ? 1 : 0,
        //        SaveTime = DateTime.Now
        //    };

        //    if (flow.OldOrderState != 3) {
        //        throw new Exception("工单非待入网复审状态");
        //    }

        //    flow.FlowText = string.Format("{0}_入网复审{1}_审批人:{2}",
        //        flow.SaveTime.ToString("yyyyMMdd HH:mm:ss"), SectionFinalApprove.IsApprove ? "通过" : "退回", SectionFinalApprove.StateSetUserName);

        //    return flow;
        //}

        //只允许本人修改
        public static WO_TestOrder ToEntity4ModifyOrder(SectionOrder secOrder) {
            if (secOrder.PartOrder.StationId == 0)
                throw new Exception("no-station-data");

            //允许空设备，以重置工单
            //if (!ui.SectionOrder.MyEquips.Any())
            //    throw new Exception("no-device-data");

            var equipItemList = (from u in secOrder.PartOrder.MyEquips
                                 select new WO_TestOrderEquipItem() {
                                     EquipmentId = u.EquipmentId,
                                     UriProtocol = u.UriProtocol,
                                     UriImage = u.UriImage
                                 }).ToList();


            WO_TestOrder myOrder = new WO_TestOrder() {
                OrderId=secOrder.PartOrder.OrderId,

                //新装入网=0/维护巡检=1
                OrderType = secOrder.PartOrder.OrderTypeString == "新装入网" ? 0 : 1,

                //ApplyOrderId = secOrder.PartOrder.ApplyOrderId,

                StationId = secOrder.PartOrder.StationId,
                Latitude = secOrder.PartOrder.Latitude,
                Longitude = secOrder.PartOrder.Longitude,

                InstallCompany = secOrder.PartOrder.InstallCompany,
                InstallClerk = secOrder.PartOrder.InstallClerk,

                InstallCompanyId = secOrder.PartOrder.InstallCompanyId,
                InstallClerkId = secOrder.PartOrder.InstallClerkId,

                

                ApplyUserId = secOrder.PartOrder.ApplyUserId,
                ApplyUserName = secOrder.PartOrder.ApplyUserName,
                ApplyUserFsuVendor = secOrder.PartOrder.ApplyUserFsuVendor,


                StateSetUserId = secOrder.PartOrder.ApplyUserId,
                StateSetUserName = secOrder.PartOrder.ApplyUserName,


                OrderState = 1//修改时状态只可能为1
            };
            //----------------------------------------------------------------------
            //设备
            myOrder.EquipItemList = equipItemList;
            //----------------------------------------------------------------------
            //系统自动验收清单
            var TestOrderEquipItemCheckList = new List<WO_TestOrderEquipItemCheckList>();
            foreach (ItemCheck c in secOrder.PartTest.ItemCheckList) {
                var e = new WO_TestOrderEquipItemCheckList() {
                    OrderCheckId=c.OrderCheckId,
                    EquipmentId = c.EquipmentId,
                    EquipmentName = c.EquipmentName,
                    EquipmentCategoryName = c.EquipmentCategoryName,
                    BaseEquipmentName = c.BaseEquipmentName,
                    //基类类别	
                    CheckType = c.CheckType,
                    CheckTypeId = c.CheckTypeId,
                    //基类名称	
                    BaseTypeName = c.BaseTypeName,
                    //标准下限	
                    LimitDown = c.LimitDown,
                    //标准上限	
                    LimitUp = c.LimitUp,
                    //逻辑分类	：输入告警
                    LogicClass = c.LogicClass,
                    //标准名	
                    StandardName = c.StandardName,

                    //信号类型	：遥信 //todo  逻辑分类	从标准化中获取

                    //单位	
                    Unit = c.Unit,
                    //测试结果	
                    IsPass = c.IsPass,
                    //测试结果描述	
                    PassNote = c.PassNote,
                    //未测试通过原因
                    PassFailReason = c.PassFailReason,
                    SignalType = c.SignalType,
                };
                TestOrderEquipItemCheckList.Add(e);
            }
            myOrder.TestOrderEquipItemCheckList = TestOrderEquipItemCheckList;


            //----------------------------------------------------------------------
            //工艺自查清单
            var TestOrderArtSelfCheckList = new List<WO_TestOrderArtSelfCheckList>();
            foreach (FormArtTestItem c in secOrder.PartArtTest.ItemCheckList) {
                var e = new WO_TestOrderArtSelfCheckList() {
                    OrderCheckId= c.OrderCheckId,
                    OrderId = c.OrderId,
                    CheckDicId = c.CheckDicId,
                    CheckDicNote = c.CheckDicNote,
                    IsPass = c.IsPass
                };
                TestOrderArtSelfCheckList.Add(e);
            }
            myOrder.TestOrderArtSelfCheckList = TestOrderArtSelfCheckList;

            return myOrder;
        }
    }
}
