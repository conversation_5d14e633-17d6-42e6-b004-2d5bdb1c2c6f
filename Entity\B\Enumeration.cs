﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity
{
    public class BConstant
    {
        public const string All = "9999999999";

        public const int NameLength = 40;
        public const int UserLength = 20;
        public const int PasswordLength = 20;
        public const int EventLength = 160;

        public const int AlarmLength = 165;

        /// <summary>
        /// 登录事件信息长度
        /// </summary>
        public const int LoginLength = 100;


        public const int DesLength = 40;
        public const int UnitLength = 8;

        /// <summary>
        /// 态值描述长度
        /// </summary>
        public const int StateLength = 160;

        public const int VerLength = 20;

        public const int FsuIdLength = 14;
        public const int FsuCodeLength = 14;

    }

    /// <summary>
    /// 监控系统数据的种类
    /// </summary>
    public enum EnumType
    {
        /// <summary>
        /// 无效类型
        /// </summary>
        Invalid = -1,

        /// <summary>
        /// 局、站
        /// </summary>
        Station = 0,

        /// <summary>
        /// 设备
        /// </summary>
        Device = 1,

        /// <summary>
        /// 数字输入量（包含多态数字输入量）
        /// </summary>
        DI = 2,

        /// <summary>
        /// 模拟输入量
        /// </summary>
        AI = 3,

        /// <summary>
        /// 数字输出量
        /// </summary>
        DO = 4,

        /// <summary>
        /// 模拟输出量
        /// </summary>
        AO = 5,

        /// <summary>
        /// 区域
        /// </summary>
        Area = 9,

    }

    /// <summary>
    /// LSC向CSC提供的权限定义
    /// </summary>
    public enum EnumRightMode
    {
        /// <summary>
        /// 无权限
        /// </summary>
        Invalid = 0,

        /// <summary>
        /// 具备数据读的权限
        /// 当用户可以读某个数据，而无法写任何数据时返回这一权限值
        /// </summary>
        Level1 = 1,

        /// <summary>
        /// 具备数据读、写的权限
        /// 当用户对某个数据具有读写权限时返回这一权限值
        /// </summary>
        Level2 = 2,
    }

    /// <summary>
    /// 报文返回结果
    /// </summary>
    public enum EnumResult
    {
        /// <summary>
        /// 失败
        /// </summary>
        Failure = 0,

        /// <summary>
        /// 成功
        /// </summary>
        Success = 1,
    }

    /// <summary>
    /// 数据值的状态
    /// </summary>
    public enum EnumState
    {
        /// <summary>
        /// 正常数据
        /// </summary>
        NoAlarm = 0,

        /// <summary>
        /// 一级告警
        /// </summary>
        Critical = 1,

        /// <summary>
        /// 二级告警
        /// </summary>
        Major = 2,

        /// <summary>
        /// 三级告警
        /// </summary>
        Minor = 3,

        /// <summary>
        /// 四级告警
        /// </summary>
        Hint = 4,

        /// <summary>
        /// 操作事件
        /// </summary>
        OpEvent = 5,

        /// <summary>
        /// 无效数据
        /// </summary>
        Invalid = 6,
    }

    public enum EnumFlag
    {
        /// <summary>
        /// 开始
        /// </summary>
        Begin = 0,

        /// <summary>
        /// 结束
        /// </summary>
        End = 1,
    }
}
