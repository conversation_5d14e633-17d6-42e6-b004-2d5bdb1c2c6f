﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;

namespace Delivery.Common.NoProcedure.Service
{
    public class Public_StoredService
    {
        private static Public_StoredService _instance = null;

        public static Public_StoredService Instance
        {
            get
            {
                if (_instance == null) _instance = new Public_StoredService();
                return _instance;
            }
        }
        public void SP_WR_OperationRecord(object WRStationId, string StationName, object OpCategory, object OpItemId, string OpItemValue, string UpdateString, string LastString, string LogonId, ExecuteHelper executeHelper = null)
        //OpCategory修改类型 OpItemId被修改对象id,OpItemValue被修改对象名称,UpdateString修改字符串组合,LastString修改前备份字符串
        {
            if (executeHelper != null)
            {
                CommonOperationRecord(WRStationId, StationName, OpCategory, OpItemId, OpItemValue, UpdateString, LastString, LogonId, executeHelper);
                return;
            }


            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);

                try
                {
                    CommonOperationRecord(WRStationId, StationName, OpCategory, OpItemId, OpItemValue, UpdateString, LastString, LogonId, execHelper);
                }
                catch (Exception ex) { Logger.Log(ex); return; }
                finally { dbHelper.Connection.Close(); }
            }
        }

        // WRStationId：只可接收int或DBNull.Value
        // OpItemId：只可接收int或DBNull.Value
        private void CommonOperationRecord(object WRStationId, string StationName, object OpCategory, object OpItemId, string OpItemValue, string UpdateString, string LastString, string LogonId, ExecuteHelper execHelper)
        {
            if (WRStationId ==null || OpCategory==null || OpItemId==null || OpItemValue==null)
            {
                return;
            }
            Dictionary<string, object> realParams = new Dictionary<string, object>();
            realParams.Add("WRStationId", WRStationId);
            realParams.Add("StationName", StationName);
            realParams.Add("OpCategory", OpCategory);
            realParams.Add("OpItemId", OpItemId);
            realParams.Add("OpItemValue", OpItemValue);
            string updateString;
            if (string.IsNullOrEmpty(UpdateString))
            {
                updateString = "";
            }
            else
            {
                updateString = UpdateString;
            }
            realParams.Add("UpdateString", updateString);
            string lastString;
            if (string.IsNullOrEmpty(UpdateString))
            {
                lastString = "";
            }
            else
            {
                lastString = LastString;
            }
            realParams.Add("LastString", lastString);
            realParams.Add("LogonId", LogonId);
            realParams.Add("CurrentTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            int iUserId = 0;
            DataTable dt = execHelper.ExecDataTable("Select_WR_OperationRecord", realParams);
            if (dt != null && dt.Rows.Count > 0)
            {
                iUserId = (int)dt.Rows[0]["iUserId"];
            }
            realParams.Add("iUserId", iUserId);
            execHelper.ExecuteNonQuery("Insert_WR_OperationRecord", realParams);
        }
        public string SP_GenerateDeviceCode(object WRStationId, string DeviceType, string SysSerialNo, string DeviceCode = "")
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                realParams.Add("WRStationId", WRStationId);
                realParams.Add("DeviceType", DeviceType);
                string OutputCode = DeviceCode;
                try
                {
                    string sCurrentNo; int?iCurrentNo; int maxNo; int? DeviceCount;
                    object maxObj = executeHelper.ExecuteScalar("SP_GenerateDeviceCode_MaxNo", realParams);
                    maxNo = Convert.ToInt32(maxObj);
                    object deviceObj = executeHelper.ExecuteScalar("SP_GenerateDeviceCode_DeviceCount", realParams);
                    DeviceCount = Convert.ToInt32(deviceObj);
                    iCurrentNo = null;
                    if (DeviceCount == 0 || DeviceCount == null)
                    {
                        iCurrentNo = 1;
                    }
                    else
                    {
                        iCurrentNo = maxNo + 1;
                    }
                    sCurrentNo = null;
                    string strICurrentNo = iCurrentNo.ToString().PadLeft(20, '0');

                    sCurrentNo = strICurrentNo.Substring(strICurrentNo.Length - 6);
                    if (SysSerialNo.Length >= 3)
                    {
                        SysSerialNo = SysSerialNo.PadLeft(3, '0').Substring(SysSerialNo.Length - 3);
                    }

                    DeviceCode = DeviceType + "00" + SysSerialNo + sCurrentNo;
                    OutputCode = DeviceCode;
                    return OutputCode;

                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
                return OutputCode;
            }
        }

        public string SP_GenerateHouseCode(object SWStationId, string SHouseId, string UserType, string OutputCode = "")
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper execHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                realParams.Add("SWStationId", SWStationId);
                realParams.Add("SHouseId", SHouseId);
                try
                {
                    string HouseCode; int iCurrentNo = 0; string RoomID;
                    if (UserType.Equals(""))
                    {
                        UserType = "CMCC";
                    }
                    if (UserType.Equals("CMCC"))
                    {
                        object objCurrentNo = execHelper.ExecuteScalar("SP_GenerateHouseCode_ICurrentNo", realParams);
                        if (objCurrentNo != null && objCurrentNo != DBNull.Value)
                        {
                            iCurrentNo = Convert.ToInt32(objCurrentNo);
                        }
                        object objRoomId = execHelper.ExecuteScalar("SP_GenerateHouseCode_RoomId", realParams);
                        RoomID = objRoomId.ToString();
                        int numberRoomId;
                        if (int.TryParse(RoomID, out numberRoomId))
                        {
                            if (numberRoomId > iCurrentNo)
                            {
                                iCurrentNo = numberRoomId;
                            }
                        }
                    }
                    else if (UserType.Equals("CUCC"))
                    {
                        object obj1CurrentNo = execHelper.ExecuteScalar("SP_GenerateHouseCode_ICurrentNo2", realParams);
                        if (obj1CurrentNo != null && obj1CurrentNo != DBNull.Value)
                        {
                            iCurrentNo = Convert.ToInt32(obj1CurrentNo);
                        }
                    }
                    if (iCurrentNo != 0)
                    {
                        iCurrentNo = iCurrentNo + 1;
                    }
                    HouseCode = SHouseId + iCurrentNo.ToString();
                    OutputCode = HouseCode;
                    return OutputCode;
                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                }
                finally { dbHelper.Connection.Close(); }
                return OutputCode;
            }
        }
        public DataTable SP_GenerateStationCode(int CountyId, int StationCategory, out string OutputCode)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                Dictionary<string, object> realParams = new Dictionary<string, object>();

                OutputCode = "";
                int v_CurrentValue; string sCurrentValue; string StationCode;
                int v_CountyId; v_CountyId = CountyId;
                realParams.Add("v_CountyId", v_CountyId);
                try
                {

                    DataTable dt = executeHelper.ExecDataTable("SP_GenerateStationCode_X", realParams);
                    if (dt == null || dt.Rows.Count <= 0)
                    {
                        executeHelper.ExecuteNonQuery("SP_GenerateStationCode_CountyId", realParams);
                    }
                    v_CurrentValue = 0;
                    object objCurrentValue = executeHelper.ExecuteScalar("SP_GenerateStationCode_CurrentValue", realParams);
                    if (objCurrentValue != null && objCurrentValue != DBNull.Value)
                    {
                        v_CurrentValue = Convert.ToInt32(objCurrentValue);
                    }
                    realParams.Add("v_CurrentValue", v_CurrentValue);
                    executeHelper.ExecuteNonQuery("SP_GenerateStationCode_UpdateTable", realParams);
                    sCurrentValue = v_CurrentValue.ToString();
                    if (sCurrentValue.Length < 6)
                    {
                        sCurrentValue = sCurrentValue.PadLeft(6, '0');
                    }
                    StationCode = v_CountyId.ToString() + StationCategory.ToString() + sCurrentValue;
                    OutputCode = StationCode;
                    executeHelper.Commit();
                    DataTable rtn = new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { 1 } }
                    };

                    return rtn;
                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    executeHelper.Rollback();
                    DataTable ReturnTableT = new DataTable
                    {
                        Columns = { new DataColumn("ReturnValue", typeof(int)) },
                        Rows = { new object[] { -1 } }
                    };
                    return ReturnTableT;
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
        public void PCT_DeleteEquipment(int StationId, int EquipmentId, ExecuteHelper execHelper = null)
        {

            if(execHelper==null)
            {
                using (DbHelper dbHelper = new DbHelper())
                {
                    ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, false);
                    try
                    {
                        PCT_DeleteEquipmentCommon(StationId, EquipmentId, executeHelper);
                    }
                    catch (Exception ex) { Logger.Log(ex); }
                    finally { dbHelper.Connection.Close(); }
                }
            } else
            {
                PCT_DeleteEquipmentCommon(StationId, EquipmentId, execHelper);
            }
        }

        private void PCT_DeleteEquipmentCommon(int StationId, int EquipmentId, ExecuteHelper executeHelper)
        {
            Dictionary<string, object> realParams = new Dictionary<string, object>();
            realParams.Add("StationId", StationId);
            realParams.Add("EquipmentId", EquipmentId);
            int v_SamplerUnitId = 0;
            object objSamplerUnitId = executeHelper.ExecuteScalar("PCT_DeleteEquipment_SamplerUnitId", realParams);
            if (objSamplerUnitId != null && objSamplerUnitId != DBNull.Value)
            {
                v_SamplerUnitId = Convert.ToInt32(objSamplerUnitId);
            }
            realParams.Add("v_SamplerUnitId", v_SamplerUnitId);
            executeHelper.ExecuteNonQuery("PCT_DeleteEquipment_EquipmentProjectInfo", realParams);
            executeHelper.ExecuteNonQuery("PCT_DeleteEquipment_MonitorUnitSignal", realParams);
            executeHelper.ExecuteNonQuery("PCT_DeleteEquipment_MonitorUnitEvent", realParams);
            executeHelper.ExecuteNonQuery("PCT_DeleteEquipment_MonitorUnitControl", realParams);
            executeHelper.ExecuteNonQuery("PCT_DeleteEquipment_Door", realParams);
            DataTable dt = executeHelper.ExecDataTable("PCT_DeleteEquipment_Select_Equipment", realParams);
            if (dt == null || dt.Rows.Count <= 0)
            {
                executeHelper.ExecuteNonQuery("PCT_DeleteEquipment_SamplerUnit", realParams);
            }
            executeHelper.ExecuteNonQuery("PCT_DeleteEquipment_Equipment", realParams);
            executeHelper.ExecuteNonQuery("PCT_DeleteEquipment_Update_Equipment", realParams);
            DataTable dt2 = executeHelper.ExecDataTable("PCT_DeleteEquipment_Select_TableName", realParams);
            if (dt != null && dt.Rows.Count > 0)
            {
                executeHelper.ExecuteNonQuery("PCT_DeleteEquipment_CorePointSECMap", realParams);
            }
        }

        public void PCT_DeleteStation(int SStationId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                realParams.Add("SStationId", SStationId);
                try
                {
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_StationProjectInfo", realParams);
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_MonitorUnitProjectInfo", realParams);
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_EquipmentProjectInfo", realParams);
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_House", realParams);
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_MonitorUnitSignal", realParams);
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_MonitorUnitEvent", realParams);
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_MonitorUnitControl", realParams);
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_SamplerUnit", realParams);
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_Port", realParams);
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_MonitorUnit", realParams);
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_EventMask", realParams);
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_EquipmentMask", realParams);
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_StationMask", realParams);
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_Equipment", realParams);
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_Station", realParams);
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_StationStructureMap", realParams);
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_StationSwatchMap", realParams);
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_ControlLogAction", realParams);
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_EventLogAction", realParams);
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_DoorTimeGroup", realParams);
                    executeHelper.ExecuteNonQuery("PCT_DeleteStation_Door", realParams);
                    DataTable dt = executeHelper.ExecDataTable("PCT_DeleteStation_Select_CorePointSECMap", realParams);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        executeHelper.ExecuteNonQuery("PCT_DeleteStation_CorePointSECMap", realParams);
                    }

                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
        public void SP_GenerateSiteWebId(string TableName, out int SiteWebId, ExecuteHelper execHelper = null)
        {
            if (execHelper != null)
            {
                SiteWebId = Common_SP_GenerateSiteWebId(TableName, out SiteWebId, execHelper);
                return;
            }

            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, false);
                try
                {
                    SiteWebId = Common_SP_GenerateSiteWebId(TableName, out SiteWebId, executeHelper);
                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    SiteWebId = -99999;
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }
        public int Common_SP_GenerateSiteWebId(string TableName, out int SiteWebId, ExecuteHelper executeHelper)
        {
            Dictionary<string, object> realParams = new Dictionary<string, object>();
            int PostalCode = 0; int TableId;
            DataTable dt = executeHelper.ExecDataTable("SP_GenerateSiteWebId_DataItem", realParams);
            realParams.Add("TableName", TableName);
            if (dt != null && dt.Rows.Count > 0)
            {
                PostalCode = (int)dt.Rows[0]["PostalCode"];

            }
            realParams.Add("PostalCode", PostalCode);
            if (PostalCode == 0)
            {
                SiteWebId = 1;
                return SiteWebId;
            }
            DataTable dt2 = executeHelper.ExecDataTable("SP_GenerateSiteWebId_TableId", realParams);
            if (dt2 != null && dt.Rows.Count > 0)
            {
                TableId = (int)dt2.Rows[0]["TableId"];
                realParams.Add("TableId", TableId);
            }
            DataTable dt3 = executeHelper.ExecDataTable("SP_GenerateSiteWebId_PrimaryKeyValue", realParams);
            if (dt3 == null && dt3.Rows.Count <= 0)
            {
                executeHelper.ExecuteNonQuery("SP_GenerateSiteWebId_Insert_PrimaryKeyValue", realParams);

            }
            else
            {
                executeHelper.ExecuteNonQuery("SP_GenerateSiteWebId_Update_PrimaryKeyValue", realParams);
            }
            DataTable dt4 = executeHelper.ExecDataTable("SP_GenerateSiteWebId_SiteWebId", realParams);

            SiteWebId = (int)dt4.Rows[0]["SiteWebId"];

            return SiteWebId;
        }
        public void BM_DeviceConfigAdd(string fsuId, string deviceID, string deviceName, string roomName, string deviceType,
            string deviceSubType, string model, string brand, string ratedCapacity, string version,
            string beginRunTime, string devDescribe, int stationId, int houseId, int? monitorUnitId, int? samplerUnitId, int equipmentId, int equipmentTemplateId, int equipmentCategory)
        {

            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    string equipmentTemplateName = "B接口_" + deviceID + "_" + fsuId;
                    string eqtConfigId = stationId + "." + equipmentId;
                    realParams.Add("EquipmentTemplateId", CommonUtils.GetNullableValue(equipmentTemplateId));
                    realParams.Add("EquipmentTemplateName", CommonUtils.GetNullableValue(equipmentTemplateName));
                    realParams.Add("EquipmentCategory", CommonUtils.GetNullableValue(equipmentCategory));
                    realParams.Add("DevDescribe", CommonUtils.GetNullableValue(devDescribe));
                    realParams.Add("Model", CommonUtils.GetNullableValue(model));
                    realParams.Add("Brand", CommonUtils.GetNullableValue(brand));
                    //插入TBL_EquipmentTemplate表
                    executeHelper.ExecuteNonQuery("BMDeviceConfigAdd_insertIntoTBLEquipmentTemplate", realParams);

                    //调用存储过程call PBL_ConfigChangeLog(@EquipmentTemplateId, 6 , 1);
                    PblConfigChangeLogService.Instance.DoExecute(equipmentTemplateId.ToString(), 6, 1, executeHelper);

                    realParams.Add("StationId", CommonUtils.GetNullableValue(stationId));
                    realParams.Add("EquipmentId", CommonUtils.GetNullableValue(equipmentId));
                    realParams.Add("DeviceName", CommonUtils.GetNullableValue(deviceName));
                    realParams.Add("BeginRunTime", CommonUtils.GetNullableValue(beginRunTime));
                    realParams.Add("HouseId", CommonUtils.GetNullableValue(houseId));
                    realParams.Add("MonitorUnitId", CommonUtils.GetNullableValue(monitorUnitId));
                    realParams.Add("SamplerUnitId", CommonUtils.GetNullableValue(samplerUnitId));
                    realParams.Add("RatedCapacity", CommonUtils.GetNullableValue(ratedCapacity));

                    //插入TBL_Equipment表
                    executeHelper.ExecuteNonQuery("BMDeviceConfigAdd_insertIntoTBLEquipment", realParams);

                    //调用存储过程call PBL_ConfigChangeLog(@EquipmentTemplateId, 6 , 1);
                    PblConfigChangeLogService.Instance.DoExecute(eqtConfigId, 3, 1, executeHelper);

                    //获取siteName
                    object siteNameObj = executeHelper.ExecuteScalar("BMDeviceConfigAdd_GetSiteName", realParams);
                    string siteName = null;
                    if (siteNameObj != DBNull.Value && siteNameObj != null) {
                        siteName = siteNameObj.ToString();
                    }
                    realParams.Add("SiteName",CommonUtils.GetNullableValue(siteName));
                    realParams.Add("DeviceID", CommonUtils.GetNullableValue(deviceID));
                    realParams.Add("FSUID", CommonUtils.GetNullableValue(fsuId));
                    realParams.Add("RoomName", CommonUtils.GetNullableValue(roomName));
                    realParams.Add("DeviceType", CommonUtils.GetNullableValue(deviceType));
                    realParams.Add("DeviceSubType", CommonUtils.GetNullableValue(deviceSubType));
                    realParams.Add("Version", CommonUtils.GetNullableValue(version));

                    //插入TBL_EquipmentCMCC表
                    executeHelper.ExecuteNonQuery("BMDeviceConfigAdd_insertIntoTBLEquipmentCMCC", realParams);
                    executeHelper.Commit();
                }
                catch (Exception ex)
                {
                    executeHelper.Rollback();
                    Logger.Log(ex);
                }
            }
        }
        public object PBL_GenerateId(string tableName, int postalCode)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    object postalCodeObj = executeHelper.ExecuteScalar("PBL_GenerateId_GetItemId", null);
                    if (postalCodeObj != DBNull.Value && postalCodeObj != null) 
                    {
                        postalCode = (int)postalCodeObj;
                    }

                    realParams.Add("tableName",CommonUtils.GetNullableValue(tableName));
                    object tableIdObj = executeHelper.ExecuteScalar("PBL_GenerateId_GetTableId", realParams);
                    if(tableIdObj == DBNull.Value || tableIdObj == null)
                    {
                        return -2;
                    }
                    string tableId = tableIdObj.ToString();
                    if (tableName.Equals("TBL_DataItem")) 
                    {
                        postalCode = 0;
                    }

                    realParams.Add("TableId",CommonUtils.GetNullableValue(tableId));
                    realParams.Add("PostalCode", CommonUtils.GetNullableValue(postalCode));
                    object judgeExist1 = executeHelper.ExecuteScalar("PBL_GenerateId_judgeExist1", realParams);
                    if (judgeExist1 == DBNull.Value || judgeExist1 == null)
                    {
                        //插入TBL_PrimaryKeyValue表
                        executeHelper.ExecuteNonQuery("PBL_GenerateId_InsertIntoTBLPrimaryKeyValue", realParams);
                    }
                    else 
                    {
                        //更新TBL_PrimaryKeyValue表
                        executeHelper.ExecuteNonQuery("PBL_GenerateId_UpdateTBLPrimaryKeyValue", realParams);
                    }
                    object GlobalIdentity = executeHelper.ExecuteScalar("PBL_GenerateId_GetGlobalIdentity",realParams);
                    executeHelper.Commit();

                    return GlobalIdentity;
                }
                catch (Exception ex)
                {
                    executeHelper.Rollback();
                    Logger.Log(ex);
                    return -3;
                }
            }
        }
        public void BM_SaveImportConfig(string FSUID, string SCSyncCfgCode, string SCSyncCfgContent)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                try
                {
                    realParams.Add("FSUID", CommonUtils.GetNullableValue(FSUID));
                    realParams.Add("SCSyncCfgCode", CommonUtils.GetNullableValue(SCSyncCfgCode));
                    realParams.Add("SCSyncCfgContent", CommonUtils.GetNullableValue(SCSyncCfgContent));
                    object judgeExist1 = executeHelper.ExecuteScalar("BM_SaveImportConfig_judgeExist1",realParams);
                    if (judgeExist1 != DBNull.Value && judgeExist1 != null)
                    {
                        //更新TBL_FsuConfig
                        executeHelper.ExecuteNonQuery("BM_SaveImportConfig_UpdateTBLFsuConfig", realParams);
                    }
                    else 
                    {
                        //插入TBL_FsuConfig
                        executeHelper.ExecuteNonQuery("BM_SaveImportConfig_InsertTBLFsuConfig", realParams);

                    }
                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                }
            }
        }
    }
}
