﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using log4net;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 性能数据处理类型
    /// </summary>
    public class Measurement
    {
        protected static ILog manager = null;

        public static ILog eLogger
        {
            get
            {
                if (manager == null)
                {
                    log4net.Repository.ILoggerRepository repository = log4net.LogManager.CreateRepository("ds");
                    var fileInfo = new FileInfo("log4net.config");
                    log4net.Config.XmlConfigurator.Configure(repository, fileInfo);
                    log4net.Config.BasicConfigurator.Configure(repository);
                    manager = LogManager.GetLogger(repository.Name, "ds");
                }

                return manager;
            }
        }

        /// <summary>
        /// 解析性能文件
        /// </summary>
        /// <param name="dicHisData">储有多个历史数据文件内容的字典， key= 文件名(PM_FSUID_YYYYMMDDHHmm.csv)， value=文件内容byte数组</param>
        /// <returns>返回List<SendData>，这样沿用之前的SendData协议，可以检查数据存储这块的改动</returns>
        public static List<SendData> ParseMeasurementFile(Dictionary<string, byte[]> dicHisData)
        {
            List<SendData> sendDataList = new List<SendData>();

            if(dicHisData.Count == 0)
            {
                eLogger.Debug("Measurement.ParseMeasurementFile(): 传入了空的历史数据文件内容的字典");
                return sendDataList;
            }
            try
            {                 
                Encoding gb2312Encoding = System.Text.Encoding.GetEncoding("gb2312");
                Encoding utf8Encoding = System.Text.Encoding.GetEncoding("utf-8");
                Encoding encoding = utf8Encoding;//默认采用utf-8
                byte[] firstFileByte = dicHisData.FirstOrDefault().Value;
                string utf8String = utf8Encoding.GetString(firstFileByte);
                string uFirst5Char = utf8String.Substring(0, 5);
                string gb2312String = gb2312Encoding.GetString(firstFileByte);
                string gFirst5Char = gb2312String.Substring(0, 5);
                if (uFirst5Char.Contains("序"))//utf8
                {
                    //表明csv性能数据文件采用utf8编码格式
                    encoding = utf8Encoding;
                    eLogger.Debug("Measurement.ParseMeasurementFile(),已判断历史数据文件编码格式为 : utf-8 ");
                }
                if (gFirst5Char.Contains("序"))//gb2312
                {
                    //表明csv性能数据文件采用gb2312编码格式
                    encoding = gb2312Encoding;
                    eLogger.Debug("Measurement.ParseMeasurementFile(),已判断历史数据文件编码格式为 : gb2312 ");
                }
                if (!uFirst5Char.Contains("序") && !gFirst5Char.Contains("序"))
                {
                    eLogger.Debug("Measurement.ParseMeasurementFile(),历史数据文件中可能没有字段名称这一行,或者采用了非常用的编码格式 ");
                    eLogger.Debug("Measurement.ParseMeasurementFile(),无法简单判断编码格式，将尝试采用 utf-8 编码进行解析 ");
                }

                foreach (var aFile in dicHisData)
                {
                    eLogger.DebugFormat("Measurement.ParseMeasurementFile(),开始解析历史数据文件:{0}", aFile.Key);
                    int Index1 = aFile.Key.IndexOf("_");
                    int Index2 = aFile.Key.LastIndexOf("_");
                    string fsuId = aFile.Key.Substring(Index1 + 1, Index2 - Index1 - 1);
                    //Encoding encoding = System.Text.Encoding.GetEncoding("gb2312");
                    string fileContent = encoding.GetString(aFile.Value);
                    eLogger.DebugFormat("Measurement.ParseMeasurementFile(),文件内容:{0}", fileContent);

                    string[] fileRows = null;
                    if (fileContent.Contains("\r\n"))
                        fileRows = fileContent.Split(new string[] { "\r\n" }, StringSplitOptions.None);
                    else if (!fileContent.Contains("\r\n") && fileContent.Contains("\n"))
                        fileRows = fileContent.Split(new string[] { "\n" }, StringSplitOptions.None);

                    //string[] fileRows = fileContent.Split(new string[] { "\r\n" }, StringSplitOptions.None);
                    Dictionary<string, List<TSemaphore>> dicDivece = new Dictionary<string, List<TSemaphore>>();
                    if (fileRows != null && fileRows.Length > 0)
                    {
                        foreach (string row in fileRows)
                        {
                            string deviceId = string.Empty;
                            TSemaphore semaphore = ParseMeasurementRow(aFile.Key, row, ref deviceId);
                            if (semaphore == null)
                            {
                                continue;
                            }
                            if (!dicDivece.ContainsKey(deviceId))
                            {
                                List<TSemaphore> sList = new List<TSemaphore>();
                                sList.Add(semaphore);
                                dicDivece.Add(deviceId, sList);
                            }
                            else
                            {
                                List<TSemaphore> sList = dicDivece[deviceId];
                                if (!sList.Contains(semaphore))
                                {
                                    sList.Add(semaphore);
                                }
                            }
                        }
                    }

                    if (dicDivece.Count == 0)
                    {
                        eLogger.DebugFormat("Deserialize(): 请检查传入的文件 {0} 内容是否符合规范", aFile.Key);
                    }
                    List<Device> deviceList = new List<Device>();
                    foreach (var item in dicDivece)
                    {
                        Device device = new Device(item.Key, item.Value);
                        deviceList.Add(device);
                    }
                    SendData sendData = new SendData(fsuId, deviceList);
                    //sendData对象只存放文件名，以便DS知道当前处理的对象对应哪个CSV文件
                    sendData.StringXML = aFile.Key;
                    sendDataList.Add(sendData);
                    eLogger.DebugFormat("Measurement.ParseMeasurementFile(),历史数据文件:{0}解析完成", aFile.Key);
                }
            }
            catch(Exception ex)
            {
                eLogger.Error("Measurement.Deserialize(),性能数据文件解析出错");
                eLogger.ErrorFormat("Measurement.Deserialize(),错误信息：{0}", ex.Message);
                eLogger.ErrorFormat("Measurement.Deserialize(),StackTrace: {0}", ex.StackTrace);
            }
            return sendDataList;
        }

        /// <summary>
        /// 解析性能文件中的单个数据行字符串
        /// </summary>
        /// <param name="fileName">性能文件名(传入此参数是为了方便打印日志)</param>
        /// <param name="strRecord">性能文件中的单个数据行字符串</param>
        /// <param name="deviceId">性能文件中的单个数据行内包含的设备ID</param>
        /// <returns>TSemaphore实体</returns>
        public static TSemaphore ParseMeasurementRow(string fileName, string strRecord, ref string deviceId)
        {
            TSemaphore semaphore = null;
            string errorMsg = string.Empty;
            try
            {
                if (string.IsNullOrEmpty(strRecord.Trim()))
                {
                    errorMsg = string.Format("文件{0}中存在空白数据行,直接丢弃", fileName);
                    eLogger.Debug(errorMsg);//空数据行不算错误，打开日志Debug开关时，给出提醒就行了，平常直接丢弃
                    return null;
                }
                if (strRecord.Contains("\""))
                {
                    strRecord = strRecord.Replace("\"", "");
                }
                string[] fields = strRecord.Split(new char[] { ',' }, StringSplitOptions.None);
                //if (fields[0].Trim() == "序号")
                if (fields[0].Contains("序"))
                {
                    eLogger.DebugFormat("文件{0}内包含标题行:{0}", fileName, strRecord);
                    return null;
                }
                string strNumber = fields[0].Trim();
                string strTime = fields[1].Trim();
                DateTime? time = null;
                if (!string.IsNullOrEmpty(strTime))
                {
                    if(strTime.Length < 14)
                    {
                        errorMsg = string.Format("文件{0}序号为{1}的数据行采集时间格式不正确:{2}", fileName, strNumber, strTime);
                        eLogger.Error(errorMsg);
                        return null;
                    }
                    string strYear = strTime.Substring(0, 4);
                    string strMonth = strTime.Substring(4, 2);
                    string strDay = strTime.Substring(6, 2);
                    string strHour = strTime.Substring(8, 2);
                    string strMinute = strTime.Substring(10,2);
                    string strSecond = strTime.Substring(12,2);
                    strTime = strYear + "-" + strMonth + "-" + strDay + " " + strHour + ":" + strMinute + ":" + strSecond;
                    time = Convert.ToDateTime(strTime);
                }
                else
                {
                    errorMsg = string.Format("文件{0}序号为{1}的数据行缺少监控点数据采集时间", fileName, strNumber);
                    eLogger.Error(errorMsg);
                    return null;
                }
                deviceId = fields[2].Trim();
                if (string.IsNullOrEmpty(deviceId))
                {
                    errorMsg = string.Format("文件{0}序号为{1}的数据行缺少监控点设备ID", fileName, strNumber);
                    eLogger.Error(errorMsg);
                    return null;
                }
                string id = fields[3].Trim();
                if (string.IsNullOrEmpty(id))
                {
                    errorMsg = string.Format("文件{0}序号为{1}的数据行缺少监控点ID", fileName, strNumber);
                    eLogger.Error(errorMsg);
                    return null;
                }
                string signalNumber = fields[4].Trim();
                if (string.IsNullOrEmpty(signalNumber))
                {
                    errorMsg = string.Format("文件{0}序号为{1}的数据行缺少监控点SignalNumber", fileName, strNumber);
                    eLogger.Error(errorMsg);
                    return null;
                }
                string description = fields[5].Trim();
                string strType = fields[6].Trim();
                EnumType enumType = EnumType.INVALID;
                if (!string.IsNullOrEmpty(strType))
                {
                    if(strType.ToUpper() == "AI" || strType == "3")
                    {
                        enumType = EnumType.AI;
                    }
                    else if (strType.ToUpper() == "DI" || strType == "4")
                    {
                        enumType = EnumType.DI;
                    }
                    else
                    {
                        errorMsg = string.Format("文件{0}序号为{1}的数据行监控点数据类型错误:{2}", fileName, strNumber, strType);
                        eLogger.Error(errorMsg);
                        return null;
                    }
                }
                else
                {
                    errorMsg = string.Format("文件{0}序号为{1}的数据行缺少监控点数据类型", fileName, strNumber);
                    eLogger.Error(errorMsg);
                    return null;
                }
                string strValue = fields[7].Trim();
                float? measureValue = null;
                if (!string.IsNullOrEmpty(strValue))
                {
                    measureValue = float.Parse(strValue);
                }
                else
                {
                    errorMsg = string.Format("文件{0}序号为{1}的数据行缺少监控点数据测量值", fileName, strNumber);
                    eLogger.Error(errorMsg);
                    return null;
                }
                float? setupValue = null;
                //新协议文档内，性能数据文件内容中没有数据测点的设置值和状态值（此处构造对象时填null和正常数据）
                semaphore = new TSemaphore(enumType, id, signalNumber, measureValue, setupValue, EnumState.NOALARM, time);
            }
            catch(Exception ex)
            {
                errorMsg = ex.Message;
                eLogger.ErrorFormat("DeserializeRecord(),性能数据行解析出错：{0}", strRecord);
                eLogger.ErrorFormat("DeserializeRecord(),错误信息：{0}", ex.Message);
                eLogger.ErrorFormat("DeserializeRecord(),StackTrace: {0}", ex.StackTrace);
            }
            return semaphore;

        }



    }
}
