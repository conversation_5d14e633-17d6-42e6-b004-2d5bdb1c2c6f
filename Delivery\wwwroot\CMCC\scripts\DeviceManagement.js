﻿'use strict';

var cookie = getCookie();

//获取FsuId列表
var getFsucboList = function (selector, params, value) {
    var result = false;
    $.ajax({
        type: "GET",
        url: `${DEVICE_HANDLER}/${params.action}`,
        contentType: "application/x-www-form-urlencoded",
        data: getParams(params),
        async: false,
        success: function (data) {
            $(selector).combobox({ editable: false });
            $(selector).combobox('loadData', JSON.parse(data));
            $(selector).combobox('select', value ? value : '-1');
            result = true;
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
    return result;
};

//获取设备类型
var getDeviceType = function (selector, params, value) {
    var result = false;
    $.ajax({
        type: "GET",
        url: `${DEVICE_HANDLER}/${params.action}`,
        contentType: "application/x-www-form-urlencoded",
        data: getParams(params),
        async: false,
        success: function (data) {
            $(selector).combobox({ editable: false });
            $(selector).combobox('loadData', JSON.parse(data));
            $(selector).combobox('select', value ? value : '-1');
            
            result = true;
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
    return result;
};

document.onkeydown = function (e) {
    var event = e || window.event;
    if (event.keyCode === 13 && document.activeElement.tagName !== 'TEXTAREA') {
        event.returnValue = false;
        event.cancel = true;
        $("#btn_query").click();
    }
};

var init = function () {
    
    $('#cbo_deviceType').combobox({
        onChange: function (newValue, oldValue) {
            getDeviceType('#cbo_deviceSubType', { action: 'GetWRDeviceSubType', parentType: newValue });
        }
    });

    if (!getDeviceType('#cbo_deviceType', { action: 'GetWRDeviceType' })) return;
    //loadDicUiDropControl($('#dlg_cbo_deviceType,#cbo_deviceType'), 7, -1);
    if (!getFsucboList('#cbo_FsuId,#dlg_cbo_FsuId', { action: 'GetWRFsuId', stationId: '0', houseId: '-1' })) return;
    $("#txt_startTime").datetimebox("setValue", new Date(new Date().setDate(1)).format('yyyy-MM-dd') + ' 00:00:00');
    $("#txt_endTime").datetimebox("setValue", new Date(new Date().getTime() + 1000 * 60 * 60).format('yyyy-MM-dd hh:mm:ss'));

    //datagrid
    $('#dg_deviceManagement').datagrid({
        title: '设备信息列表',
        singleSelect: true,
        url: `${DEVICE_HANDLER}/GetDeviceInfo`,
        width: function () { return document.body.clientWidth * 0.9; },
        rownumbers: true,
        remoteSort: false,
        pagination: true,
        pageSize: 50,
        pageList: [50, 100, 150, 200],
        method: 'GET',
        queryParams: {
            //action: "GetDeviceInfo",
            deviceCode: $('#txt_deviceCode').textbox('getValue'),
            deviceName: $('#txt_deviceName').textbox('getValue'),
            deviceCategory: $('#cbo_deviceType').combobox('getValue'),
            deviceSubCategory: $('#cbo_deviceSubType').combobox('getValue'),
            stationId: $('#hid_stationId').val(),
            fsuId: $('#cbo_FsuId').combobox('getValue'),
            startTime: $('#txt_startTime').datetimebox('getValue'),
            endTime: $('#txt_endTime').datetimebox('getValue')
        },
        //loadFilter: pagerFilter,
        columns: [[
            { field: 'StationName', title: '站址名称', width: 200, align: 'left' },
            { field: 'HouseName', title: '机房名称', width: 200, align: 'left' },
            { field: 'FsuCode', title: 'FSU编码', width: 150, align: 'left' },
            { field: 'FsuName', title: 'FSU名称', width: 200, align: 'left' },
            { field: 'PortUse', title: '所用端口', width: 150, align: 'left' },
            { field: 'DeviceCode', title: '设备编码', width: 150, align: 'left' },
            { field: 'DeviceCategoryName', title: '设备类型', width: 150, align: 'left' },
            { field: 'DeviceSubCategoryName', title: '设备子类', width: 150, align: 'left' },
            { field: 'DeviceName', title: '设备名称', width: 200, align: 'left' },
            { field: 'EnableTime', title: '启用时间', width: 150, align: 'left' },
            { field: 'LifeTime', title: '可使用年限', width: 100, align: 'left' },
            { field: 'Remark', title: '备注', width: 200, align: 'left' },
            { field: 'ApplyTime', title: '申请时间', width: 150, align: 'left' },
            { field: 'SWUserName', title: '申请人', width: 100, align: 'left' }
        ]]
    });
    $('#subdlg_dg_house').datagrid({
        method: 'GET',
        url: `${HOUSE_HANDLER}/GetHouseInfoCheck`,
        title: '机房信息列表',  //rownumbers: true, //行号  striped: true, //隔行变色                
        idField: 'HouseCode', //主键
        singleSelect: true,
        selectOnCheck: true,
        pagination: true,
        pageSize: 30,
        pageList: [30],
        queryParams: {
            //action: "GetHouseInfoCheck",
            StationName: $('#subdlg_txt_stationName').val(),
            HouseName: $('#subdlg_txt_houseName').val(),
            StationCode: $('#subdlg_txt_stationCode').val(),
            HouseCode: '',
            strBegin: '',
            strEnd: '',
            Status: '3'
        },
        width: function () { return document.body.clientWidth * 0.9; }, //设置表格宽度为客户端页面的0.9
        height: 'auto',
        fitColumns: true,
        columns: [[
            {
                field: 'ck', align: 'center', title: '选择 ', width: 9,
                formatter: function (value, row, index) {
                    return '<input type="radio" id="radio' + index + '" radiogroup="lookinfoCK" />';
                }
            },
            { field: 'RowNumber', title: '序号', width: 10, align: 'center' },
            { field: 'StationName', title: '站址名称', width: 15, align: 'left' },
            { field: 'StationCode', title: '站址编码', width: 20, align: 'left' },
            { field: 'HouseName', title: '机房名称', width: 12, align: 'left' },
            { field: 'HouseCode', title: '机房编码', width: 15, align: 'left' }

        ]],
        checkOnSelect: true,
        onClickRow: function (index, row) {
            var currentRadio = 'radio' + index;
            $("input:radio").each(function () {
                $(this).attr("checked", false);
            });
            $("#" + currentRadio).attr("checked", "checked");
        },
        onDblClickRow: function (index, row) {
            $('#subdlg_btn_choose').click();
        },
        onLoadError: function () {
            alertInfo('查询出错！', 'error');
        }
    });
    $('#dlg_dg_station_m').datagrid({
        method: 'GET',
        url: `${STATION_HANDLER}/GetStationInfoCheck`,
        title: '站址信息列表',  //rownumbers: true, //行号  striped: true, //隔行变色                
        singleSelect: true,
        pagination: true,
        pageSize: 30,
        pageList: [30],
        queryParams: {
            //action: "GetStationInfoCheck",
            StationName: $('#dlg_txt_stationName_m').val(),
            StationCode: $('#dlg_txt_stationCode_m').val(),
            strBegin: '',
            strEnd: '',
            StationType: '',
            StationStatus: '3'
        },
        width: function () { return document.body.clientWidth * 0.9; }, //设置表格宽度为客户端页面的0.9
        height: 'auto',
        fitColumns: true,
        columns: [[
            {
                field: 'ck', align: 'center', title: '选择 ', width: 9,
                formatter: function (value, row, index) {
                    return '<input type="radio" id="rdo_' + index + '" radiogroup="station" />';
                }
            },
            { field: 'RowNumber', title: '序号', width: 10, align: 'center' },
            { field: 'StationName', title: '站址名称', width: 10, align: 'left' },
            { field: 'StationCode', title: '站址编码', width: 10, align: 'left' },
            { field: 'StationCategoryName', title: '站址类型', width: 12, align: 'left' },
            { field: 'Address', title: '基站地址', width: 15, align: 'left' },
            { field: 'ProvinceName', title: '省', width: 22, align: 'left' },
            { field: 'CityName', title: '市', width: 22, align: 'left' },
            { field: 'CountyName', title: '区', width: 22, align: 'left' }

        ]],
        checkOnSelect: true,
        onClickRow: function (index, row) {
            var currentRadio = 'rdo_' + index;
            $("input:radio").each(function () {
                $(this).attr("checked", false);
            });
            $("#" + currentRadio).attr("checked", "checked");
        },
        onDblClickRow: function (index, row) {
            $('#dlg_btn_choose_m').click();
        },
        onLoadError: function () {
            alertInfo('查询出错！', 'error');
        }
    });

    //main control event
    $('#txt_stationName').textbox('textbox').attr('readonly', true).on('click', function () {
        $('#dlg_btn_reset_m').click();
        $('#dlg_btn_query_m').click();
        $('#dlg_station_m').dialog('open');
    });
    $('#btn_add').on('click', function (e) {
        if (!getFsucboList('#dlg_cbo_FsuId', { action: 'GetWRFsuId', stationId: '-1', houseId: '-1' })) return;
        $('#dlg_device').dialog('setTitle', '新增');
        $('#dlg_btn_submit').val('添加');
        $('#dlg_hid_deviceId').val('');
        $('#dlg_hid_houseId').val('');
        $('#dlg_txt_houseName').textbox('setValue', '');
        $('#dlg_txt_deviceName').textbox('setValue', '');
        $('#dlg_txt_systemSeqNo').numberbox('setValue', '');
        $('#dlg_txt_remark').textbox('setValue', '');
        //$('#dlg_cbo_FsuId').combobox('setValue', '-1');
        $('#dlg_cbo_deviceType').combobox('setValue', '-1');
        $('#dlg_cbo_deviceSubType').combobox('setValue', '-1');
        $('#dlg_device').dialog('open');
    });
    $('#btn_edit').on('click', function (e) {
        var row = $('#dg_deviceManagement').datagrid('getSelected');
        if (row && getFsucboList('#dlg_cbo_FsuId', { action: 'GetWRFsuId', stationId: row.WRStationId, houseId: row.WRHouseId }, row.WRFsuId)) {
            $('#dlg_device').dialog('setTitle', '编辑');
            $('#dlg_btn_submit').val('修改');
            $('#dlg_device').dialog('open');
            $('#dlg_cbo_deviceType').combobox('setValue', row.DeviceCategory);
            if (!getDeviceType('#dlg_cbo_deviceSubType', { action: 'GetWRDeviceSubType', parentType: row.DeviceCategory }, row.DeviceSubCategory)) return;
            $('#dlg_txt_systemSeqNo').textbox('setValue', ~~(row.DeviceCode.substr(6, 3)));
            $('#dlg_hid_deviceId').val(row.WRDeviceId);
            $('#dlg_hid_houseId').val(row.WRHouseId);
            $('#dlg_txt_houseName').textbox('setValue', row.HouseName);
            $('#dlg_txt_deviceName').textbox('setValue', row.DeviceName);
            $('#dlg_cbo_port').textbox('setValue', row.PortUse);
            //$('#txt_enableTime').textbox('setValue', row.EnableTime);
            $("#txt_enableTime").datetimebox("setValue", new Date(row.EnableTime).format('yyyy-MM-dd hh:mm:ss'));
            $('#txt_lifeTime').textbox('setValue', row.LifeTime);
            $('#dlg_txt_remark').textbox('setValue', row.Remark);
        } else
            alertInfo('请选择一行设备记录！');
    });
    $('#btn_delete').on('click', function (e) {
        var row = $('#dg_deviceManagement').datagrid('getSelected');
        if (row) {
            $.messager.confirm('确认', '您确认删除吗？',
                function (r) {
                    if (r)
                        $.ajax({
                            type: "POST",
                            url: `${DEVICE_HANDLER}/DeleteDevice`,
                            contentType: "application/x-www-form-urlencoded",
                            data: {
                                //action: 'DeleteDevice',
                                deviceId: row.WRDeviceId
                            },
                            async: false,
                            success: function (data) {
                                if (data === '1') {
                                    alertInfo('删除成功！');
                                    $('#btn_query').click();
                                }else if (data === '-1')
                                    alertInfo('-1: 当前用户无权限删除此设备！', 'error');
                                else
                                    alertInfo(data + ': 删除出现错误！', 'error');
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                            }
                        });
                });
        } else
            alertInfo('请选择一行设备记录！');
    });
    $('#btn_query').on('click', function (e) {
        $('#dg_deviceManagement').datagrid({
            queryParams: {
                //action: "GetDeviceInfo",
                deviceCode: $('#txt_deviceCode').textbox('getValue'),
                deviceName: $('#txt_deviceName').textbox('getValue'),
                deviceCategory: $('#cbo_deviceType').combobox('getValue'),
                deviceSubCategory: $('#cbo_deviceSubType').combobox('getValue'),
                stationId: $('#hid_stationId').val(),
                fsuId: $('#cbo_FsuId').combobox('getValue'),
                startTime: $('#txt_startTime').datetimebox('getValue'),
                endTime: $('#txt_endTime').datetimebox('getValue')
            }
        });
    });
    $("#btn_export").click(function (e) {
        var f = $('<form action="api/device/exportexcel" method="get" id="fm1" accept-charset="UTF-8"></form>');
        $('<input type="hidden"  name="action"/>').val('ExportDeviceInfo').appendTo(f);
        $('<input type="hidden"  name="deviceCode"/>').val($('#txt_deviceCode').textbox('getValue')).appendTo(f);
        $('<input type="hidden"  name="deviceName"/>').val($('#txt_deviceName').textbox('getValue')).appendTo(f);
        $('<input type="hidden"  name="deviceCategory"/>').val($('#cbo_deviceType').combobox('getValue')).appendTo(f);
        $('<input type="hidden"  name="deviceSubCategory"/>').val($('#cbo_deviceSubType').combobox('getValue')).appendTo(f);
        $('<input type="hidden"  name="stationId"/>').val($('#hid_stationId').val()).appendTo(f);
        $('<input type="hidden"  name="fsuId"/>').val($('#cbo_FsuId').combobox('getValue')).appendTo(f);
        $('<input type="hidden"  name="startTime"/>').val($('#txt_startTime').datetimebox('getValue')).appendTo(f);
        $('<input type="hidden"  name="endTime"/>').val($('#txt_endTime').datetimebox('getValue')).appendTo(f);
        f.appendTo(document.body).submit().remove();
    });
    $("#btn_reset").click(function (e) {
        $('#txt_deviceCode').textbox('setValue', '');
        $('#txt_deviceName').textbox('setValue', '');
        $("#txt_startTime").datetimebox("setValue", '');
        $("#txt_endTime").datetimebox("setValue", '');
        $('#hid_stationId').val('-1');
        $('#txt_stationName').textbox('setValue', '');
        $('#cbo_deviceType').combobox('setValue', '-1');
        getFsucboList('#cbo_FsuId', { action: 'GetWRFsuId', stationId: '0', houseId: '-1' });
    });
    $('#btn_help').click(function () {
        $.ajax({
            type: "GET",
            url:GET_STANDARDDOC_HANDLER,
            contentType: "application/x-www-form-urlencoded",
            data: { type: 4 },
            async: false,
            success: function (data) {
                $('#standardContent').val(data);
                $('#standardContent').attr('disabled','disbaled');
                $('#standardDoc').dialog('open');
                //$('#standardDoc').find('textarea').focus();
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
            }
        });
        
    });
    

    //device dialog button event
    $('#dlg_cbo_deviceType').combobox({
        onChange: function (newValue, oldValue) {
            getDeviceType('#dlg_cbo_deviceSubType', { action: 'GetWRDeviceSubType', parentType: newValue });
        }
    });
    if (!getDeviceType('#dlg_cbo_deviceType', { action: 'GetWRDeviceType' })) return;
    $('#dlg_txt_houseName').textbox('textbox').attr('readonly', true).on('click', function () {
        $('#subdlg_btn_reset').click();
        $('#subdlg_btn_query').click();
        $('#subdlg_house').dialog('open');
    });
    $('#dlg_btn_submit').on('click', function (e) {
        if (!$('#dlg_hid_houseId').val()) {
            alertInfo('请选择机房！');
            return false;
        } else if ($('#dlg_cbo_FsuId').combobox('getValue') === '-1') {
            alertInfo('请选择所属FSU！');
            return false;
        } else if ($('#dlg_cbo_deviceType').combobox('getValue') === '-1' || $('#dlg_cbo_deviceSubType').combobox('getValue') === '-1') {
            alertInfo('请选择设备类型！');
            return false;
        } else if (!$('#dlg_txt_deviceName').textbox('getValue') || !$('#dlg_txt_deviceName').textbox('isValid')) {
            alertInfo('请正确填写设备名称！');
            return false;
        } else if (!$('#dlg_txt_remark').textbox('getValue') || !$('#dlg_txt_remark').textbox('isValid')) {
            alertInfo('请正确填写备注！');
            return false;
        } else if (!$('#dlg_txt_systemSeqNo').textbox('getValue')) {
            alertInfo('请正确填写系统顺序号！');
            return false;
        }
        var btn = $(this);
        $.messager.confirm('确认', '您确认' + btn.val() + '吗？',
                function (r) {
                    if (r) {
                        var params = {};
                        if (btn.val() === '添加') {
                            params = {
                                action: 'AddDevice',
                                deviceName: $("#dlg_txt_deviceName").textbox('getValue'),
                                deviceCategory: $("#dlg_cbo_deviceType").combobox('getValue'),
                                deviceSubCategory: $("#dlg_cbo_deviceSubType").combobox('getValue'),
                                houseId: $('#dlg_hid_houseId').val(),
                                fsuId: $("#dlg_cbo_FsuId").combobox('getValue'),
                                remark: $('#dlg_txt_remark').textbox('getValue'),
                                sysSerialNo: $('#dlg_txt_systemSeqNo').textbox('getValue'),
                                portUse: $('#dlg_cbo_port').textbox('getValue'),
                                enableTime: $('#txt_enableTime').textbox('getValue'),
                                lifeTime: $('#txt_lifeTime').textbox('getValue')
                                
                            };
                        } else if (btn.val() === '修改') {
                            params = {
                                action: 'UpdateDevice',
                                deviceId: $("#dlg_hid_deviceId").val(),
                                deviceName: $("#dlg_txt_deviceName").textbox('getValue'),
                                deviceCategory: $("#dlg_cbo_deviceType").combobox('getValue'),
                                deviceSubCategory: $("#dlg_cbo_deviceSubType").combobox('getValue'),
                                houseId: $('#dlg_hid_houseId').val(),
                                fsuId: $("#dlg_cbo_FsuId").combobox('getValue'),
                                remark: $('#dlg_txt_remark').textbox('getValue'),
                                sysSerialNo: $('#dlg_txt_systemSeqNo').textbox('getValue'),
                                portUse: $('#dlg_cbo_port').textbox('getValue'),
                                enableTime: $('#txt_enableTime').textbox('getValue'),
                                lifeTime: $('#txt_lifeTime').textbox('getValue')
                            };
                        }
                        $.ajax({
                            type: "POST",
                            url: `${DEVICE_HANDLER}/${params.action}`,
                            contentType: "application/x-www-form-urlencoded",
                            data: getParams(params),
                            async: false,
                            success: function (data) {
                                if (data === '1') {
                                    alertInfo($('#dlg_btn_submit').val() + '成功！');
                                    $('#dlg_device').dialog('close');
                                    $('#btn_query').click();
                                }
                                else if (data === '-2')
                                    alertInfo('-2: 设备名称已存在！', 'error');
                                else
                                    alertInfo(data + ': 出现错误！', 'error');
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                            }
                        });
                    }
                });        
    });
    $('#dlg_btn_close').on('click', function (e) {
        $('#dlg_device').dialog('close');
    });

    //house dialog button event
    $('#subdlg_btn_reset').on('click', function (e) {
        $('#subdlg_txt_stationName').textbox('setValue', '');
        $('#subdlg_txt_stationCode').textbox('setValue', '');
        $('#subdlg_txt_houseName').textbox('setValue', '');
    });
    $('#subdlg_btn_close').on('click', function (e) {
        $('#subdlg_house').dialog('close');
    });
    $('#subdlg_btn_choose').on('click', function (e) {
        var row = $('#subdlg_dg_house').datagrid('getSelected');
        if (row) {
            if (row.HouseStatus !== '3') {
                alertInfo('选中机房未通过审核！');
                return false;
            }
            $('#dlg_hid_houseId').val(row.WRHouseId);
            $('#dlg_txt_houseName').textbox("setValue", row.HouseName);
            $('#subdlg_house').dialog('close');
            getFsucboList('#dlg_cbo_FsuId', { action: 'GetWRFsuId', stationId: '-1', houseId: row.WRHouseId });
        } else
            alertInfo('请选择机房！');
    });
    $('#subdlg_btn_query').on('click', function (e) {
        $('#subdlg_dg_house').datagrid({
            queryParams: {
                //action: "GetHouseInfoCheck",
                StationName: $('#subdlg_txt_stationName').val(),
                HouseName: $('#subdlg_txt_houseName').val(),
                StationCode: $('#subdlg_txt_stationCode').val(),
                HouseCode: '',
                strBegin: '',
                strEnd: '',
                Status: '3'
            }
        });
    });

    //station dialog button event
    $('#dlg_btn_reset_m').on('click', function (e) {
        $('#dlg_txt_stationName_m').textbox('setValue', '');
        $('#dlg_txt_stationCode_m').textbox('setValue', '');
    });
    $('#dlg_btn_close_m').on('click', function (e) {
        $('#dlg_station_m').dialog('close');
    });
    $('#dlg_btn_choose_m').on('click', function (e) {
        var row = $('#dlg_dg_station_m').datagrid('getSelected');
        if (row) {
            if (row.StationStatus !== '3') {
                alertInfo('选中站址未通过审核！');
                return false;
            }
            $('#hid_stationId').val(row.WRStationId);
            $('#txt_stationName').textbox("setValue", row.StationName);
            $('#dlg_station_m').dialog('close');
            getFsucboList('#cbo_FsuId', { action: 'GetWRFsuId', stationId: $('#hid_stationId').val() , houseId: '-1' });
        } else
            alertInfo('请选择站址！');
    });
    $('#dlg_btn_query_m').on('click', function (e) {
        $('#dlg_dg_station_m').datagrid({
            queryParams: {
                //action: "GetStationInfoCheck",
                StationName: $('#dlg_txt_stationName_m').val(),
                StationCode: $('#dlg_txt_stationCode_m').val(),
                strBegin: '',
                strEnd: '',
                StationType: '',
                StationStatus: '3'
            }
        });
    });

    //standardDoc dialog
    $('#btn_docEdit').click(function () {
        $('#standardContent').removeAttr('disabled');
        $('#standardContent').focus();
    });
    $('#btn_docSave').click(function () {
        $('#standardContent').attr('disabled', 'disabled');
        $.messager.confirm('确认', '您确认保存吗？',
            function (r) {
                if (r) {
                    $.ajax({
                        type: "POST",
                        contentType: "application/json; charset=UTF-8",
                        url: SAVE_STANDARDDOC_HANDLER,
                        data: JSON.stringify({ FileName: "DeviceStandard", Content: $('#standardContent').val().replace(/(?!\r)\n/g, '\r\n') }),
                        success: function (data) {
                            alertInfo(data);
                        },
                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
                        }
                    });
                }
            });
    });
};

$(function () {
    init();
    if (cookie['userinfo']['UserId'] !== '-1') {
        $('#toolbar').remove();
    } else {
        $('#btn_delete').remove();
        $('#btn_add').remove();
    }

    $('div.mask').remove();
});