﻿using BLL;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using DM.TestOrder.Model;

using DM.TestOrder.Entity;


namespace DM.TestOrder.Service.Convert {
    //public static void CreateTestOrder(string ApplyOrderId, string ApplyUserId, string StationId, string StationName) {
    public partial class Mapper {
        public static WO_TestOrder ToEntity4CreateOrder(FormOrder newOrder) {
            if (newOrder.StationId == 0)
                throw new Exception("no-station-data");

            //允许空设备，以重置工单
            //if (!ui.SectionOrder.MyEquips.Any())
            //    throw new Exception("no-device-data");

            var equipItemList = (from u in newOrder.MyEquips
                                 select new WO_TestOrderEquipItem() {
                                     EquipmentId = u.EquipmentId,
                                     UriProtocol = u.UriProtocol,
                                     UriImage = u.UriImage
                                 }).ToList();


            WO_TestOrder myOrder = new WO_TestOrder() {
                //OrderId = newOrder.OrderId 待数据库分配

                //新装入网=0/维护巡检=1
                OrderType = newOrder.OrderTypeString == "新装入网" ? 0 : 1,

                //ApplyOrderId = newOrder.ApplyOrderId,

                StationId = newOrder.StationId,
                Latitude = newOrder.Latitude,
                Longitude = newOrder.Longitude,

                InstallCompany = newOrder.InstallCompany,
                InstallClerk = newOrder.InstallClerk,


                ApplyUserId = newOrder.ApplyUserId,
                ApplyUserName = newOrder.ApplyUserName,


                StateSetUserId = newOrder.ApplyUserId,
                StateSetUserName = newOrder.ApplyUserName,

                OrderState = 1//newOrder.OrderState
            };
            //----------------------------------------------------------------------
            //设备
            myOrder.EquipItemList = equipItemList;
            //----------------------------------------------------------------------
           
            //数据库填充
            //myOrder.TestOrderEquipItemCheckList = TestOrderEquipItemCheckList;



            return myOrder;
        }
 
    }
}
