﻿@page
@model Delivery.Pages.CUCC.DeviceManagementModel
@{
}

@section Scripts {
    <script type="text/javascript" src="~/Scripts/lib/jquery-1.8.0.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="~/Scripts/lib/easyui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="~/Scripts/DeliveryCommon.js"></script>
    <script type="text/javascript" src="~/Scripts/common/ie8.js"></script>
    <script type="text/javascript" src="~/Scripts/common/common.js"></script>
    <script src="~/CUCC/scripts/DeviceManagement.js"></script>
}

<!--mask-->
<div class="mask">
    <div style="position: relative; top: 50%; left: 50%">
        <div class="loading">
            正在加载，请等待...
        </div>
    </div>
</div>

<!--device dialog-->
<div id="dlg_device" title="设备管理" style="width: 700px; height: 300px;" class="easyui-dialog" data-options="modal:true,closed:true,closable:true">
    <div style="margin: auto; width: 90%;">
        <input type="hidden" id="dlg_hid_deviceId" />
        <table style="width: 100%;">
            <tr style="height: 50px;">
                <td style="text-align: left; white-space: nowrap">所属机房</td>
                <td>
                    <input type="hidden" id="dlg_hid_houseId" />
                    <input id="dlg_txt_houseName" class="easyui-textbox" style="width: 150px" data-options="
                            prompt: '请选择所属机房',
			                icons:[{
				                iconCls:'icon-search',
				                handler: function(e){
                                    $('#dlg_txt_houseName').textbox('textbox').click();
				                }
			                }]" />
                </td>
                <td style="text-align: left; white-space: nowrap">所属SU</td>
                <td>
                    <input id="dlg_cbo_FsuId" class="easyui-combobox" style="width: 150px;" data-options="editable: false,valueField: 'id', textField: 'text'" />
                </td>
            </tr>
            <tr style="height: 50px;">
                <td style="text-align: left; white-space: nowrap">设备类型</td>
                <td>
                    <input id="dlg_cbo_deviceType" class="easyui-combobox" style="width: 150px;" />
                </td>
                <td style="text-align: left; white-space: nowrap">设备名称</td>
                <td>
                    <input id="dlg_txt_deviceName" class="easyui-textbox" style="width: 150px;" data-options="prompt:'请填写设备名称', validType:'maxLength[255]',required:'true'" />
                </td>
            </tr>
            <tr style="height: 50px;">
                <td style="text-align: left; white-space: nowrap">设备资源ID</td>
                <td>
                    <input id="dlg_txt_deviceRId" class="easyui-textbox" style="width: auto;" data-options="prompt:'请填写设备资源ID', validType:'maxLength[128]'" />
                </td>
                <td style="text-align: left; white-space: nowrap">备注</td>
                <td>
                    <input id="dlg_txt_remark" class="easyui-textbox" style="width: auto;" data-options="prompt:'请填写备注', validType:'maxLength[255]',required:'true'" />
                </td>
            </tr>
            <tr style="height: 30px;"></tr>
            <tr style="height: 50px;">
                <td colspan="4" style="text-align: center;">
                    <input type="button" class="commonButton" id="dlg_btn_submit" value="提交" />
                    <input type="button" class="commonButton" id="dlg_btn_close" value="关闭" />
                </td>
            </tr>
        </table>
    </div>
</div>

<!--house dialog-->
<div id="subdlg_house" title="机房选择" style="width: 850px; height: 450px; padding: 20px 10px 5px 10px;" class="easyui-dialog"
     data-options="modal:true,closed:true,closable:true">
    <div>
        <table style="width: 100%; margin: auto; padding: 0;">
            <tr>
                <td style="text-align: left; white-space: nowrap">站址名称:</td>
                <td>
                    <input type='text' placeholder="请输入站址名称" class="easyui-textbox" id="subdlg_txt_stationName" style="width: 140px;" />
                </td>
                <td style="text-align: left; white-space: nowrap">站址编码:</td>
                <td>
                    <input type='text' placeholder="请输入站址编码" class="easyui-textbox" id="subdlg_txt_stationCode" style="width: 140px;" />
                </td>
            </tr>
            <tr>
                <td style="text-align: left;">机房名称:</td>
                <td>
                    <input type='text' placeholder="请输入机房名称" class="easyui-textbox" id="subdlg_txt_houseName"
                           style="width: 140px;" />
                </td>
                <td colspan="2" style="text-align: left;">
                    <input type="button" id="subdlg_btn_query" value="查询" class="commonButton" style="width: 40pt;" />
                    <input type="button" id="subdlg_btn_reset" value="重置" class="commonButton" style="width: 40pt;" />
                    <input type="button" id="subdlg_btn_close" value="关闭" class="commonButton" style="width: 40pt;" />
                    <input type="button" id="subdlg_btn_choose" value="选择" class="commonButton" style="width: 40pt;" />
                </td>
            </tr>
        </table>
    </div>
    <div>
        <table id="subdlg_dg_house">
        </table>
    </div>
</div>

<!--station dialog-->
<div id="dlg_station_m" title="站址选择" style="width: 850px; height: 450px; padding: 20px 10px 5px 10px;"
     class="easyui-dialog" data-options="modal:true,closed:true,closable:true">
    <table style="width: 100%; margin: 0; padding: 0;">
        <tr>
            <td colspan="2" style="text-align: left; width: 120px;"></td>
            <td style="text-align: left; white-space: nowrap">
                站址名称:
            </td>
            <td>
                <input type='text' placeholder="请输入站址名称" data-options="prompt:'请输入站址名称'" class="easyui-textbox" id="dlg_txt_stationName_m"
                       style="width: 140px;" />
            </td>
            <td style="text-align: left; white-space: nowrap">
                站址编码:
            </td>
            <td>
                <input type='text' placeholder="请输入站址编码" data-options="prompt:'请输入站址编码'" class="easyui-textbox" id="dlg_txt_stationCode_m"
                       style="width: 140px;" />
            </td>
        </tr>
        <tr>
            <td colspan="6"></td>
        </tr>
        <tr style="height: 30px;">
            <td colspan="2" style="text-align: left;"></td>
            <td colspan="4" style="text-align: center;">
                <input type="button" id="dlg_btn_query_m" value="查询" class="commonButton" style="width: 40pt;" />
                <input type="button" id="dlg_btn_reset_m" value="重置" class="commonButton" style="width: 40pt;" />
                <input type="button" id="dlg_btn_close_m" value="关闭" class="commonButton" style="width: 40pt;" />
                <input type="button" id="dlg_btn_choose_m" value="选择" class="commonButton" style="width: 40pt;" />
            </td>
        </tr>
    </table>
    <div>
        <table id="dlg_dg_station_m">
        </table>
    </div>
</div>

<!--query condition-->
<table style="margin: auto; width: 100%;">
    <tr>
        <td style="text-align: left; white-space: nowrap">站址名称</td>
        <td>
            <input id="hid_stationId" type="hidden" value="-1" />
            <input id="txt_stationName" class="easyui-textbox" style="width: 150px" data-options="
                            prompt: '请选择所属站址',
			                icons:[{
				                iconCls:'icon-search',
				                handler: function(e){
                                    $('#txt_stationName').textbox('textbox').click();
				                }
			                }]" />
        </td>
        <td style="text-align: left; white-space: nowrap">所属SU</td>
        <td>
            <input id="cbo_FsuId" class="easyui-combobox" style="width: 150px;" data-options="valueField: 'id', textField: 'text'" />
        </td>
        <td style="text-align: left; white-space: nowrap">设备类型</td>
        <td>
            <input id="cbo_deviceType" class="easyui-combobox" style="width: 150px;" />
        </td>
    </tr>
    <tr>
        <td style="text-align: left; white-space: nowrap">设备名称</td>
        <td>
            <input id="txt_deviceName" class="easyui-textbox" style="width: 150px;" data-options="validType:'maxLength[255]'" />
        </td>
        <td class="labTdStyle">申请开始日期</td>
        <td class="contorlTdStyle">
            <input id="txt_startTime" class="easyui-datetimebox" data-options="validType:['date[\'yyyy-MM-dd hh:mm:ss\']', 'less[\'#txt_endTime\']'],onChange:function(){$('#txt_endTime').datebox('validate')}" style="width: 150px;" /><!---->
        </td>
        <td class="labTdStyle">申请截止日期</td>
        <td class="contorlTdStyle">
            <input id="txt_endTime" class="easyui-datetimebox" data-options="validType:['date[\'yyyy-MM-dd hh:mm:ss\']','greater[\'#txt_startTime\']'],onChange:function(){$('#txt_startTime').datebox('validate')}" style="width: 150px;" />
        </td>
    </tr>
    <tr>
        <td style="text-align: left; white-space: nowrap">设备编码</td>
        <td>
            <input id="txt_deviceCode" class="easyui-textbox" style="width: 150px;" data-options="validType:'maxLength[255]'" />
        </td>
        <td colspan="2"></td>
        <td colspan="2">
            <input type="button" class="commonButton" id="btn_query" value="查询" style="width: 40pt;" />
            <input type="button" class="commonButton" id="btn_edit" value="编辑" style="width: 40pt;" />
            <input type="button" class="commonButton" id="btn_delete" value="删除" style="width: 40pt;" />
            <input type="button" class="commonButton" id="btn_reset" value="重置" style="width: 40pt;" />
            <input type="button" class="commonButton" id="btn_export" value="导出Excel" style="width: 60pt;" />
            <input type="button" class="commonButton" id="btn_add" value="新增设备" style="width: 80pt;" />
            <button id="btn_help" class="commonButton" style="width: 80pt;">
                <i style="display: inline-block; vertical-align: middle; width: 14px; height: 14px; background-image: url(../Content/themes/icons/help.png); background-size: contain;"></i>
                规范浏览
            </button>
        </td>
    </tr>
</table>

<!--datagrid-->
<table id="dg_deviceManagement"></table>

<!--standardDoc-->
<div id="standardDoc" class="easyui-dialog" title="规范浏览" style="padding: 0px;" data-options="iconCls:'icon-help', closed: true, resizable: true, width:500">
    <textarea id="standardContent" style="margin: auto; width: 466px; height: 300px; padding: 10px; border-width: 0; outline: none; resize: none; color: #000; font-family: Arial; font-size: 16px;" disabled="disabled">编码规范</textarea>
    <div id="toolbar" style="text-align: center; padding: 10px 0;">
        <a href="javascript:void(0);" id="btn_docEdit" class="easyui-linkbutton" data-options="iconCls:'icon-edit'">编辑</a>
        <a href="javascript:void(0);" id="btn_docSave" class="easyui-linkbutton" data-options="iconCls:'icon-save'">保存</a>
    </div>
</div>