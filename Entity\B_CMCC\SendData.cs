﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.6.15.	上报监控点数据
    /// </summary>
    public sealed class SendData:BMessage
    {

        public List<Device> Values { get; private set; }

        public SendData(string fsuId, List<Device> values): base()
        {
            MessageType = (int)BMessageType.SEND_DATA;

            FSUID = fsuId;
            Values = values;
        }

        public SendData() : base() 
        {
            MessageType = (int)BMessageType.SEND_DATA;
        }

        public static SendData Deserialize(XmlDocument xmlDoc)
        {
            string errorMsg = string.Empty;
            try
            {
                string fsuId = xmlDoc.SelectSingleNode("/Request/Info/FSUID").InnerText;
                XmlNodeList deviceNodeList = xmlDoc.SelectNodes("/Request/Info/Values/DeviceList/Device");

                List<Device> deviceList = new List<Device>();
                foreach (XmlNode deviceNode in deviceNodeList)
                {
                    Device device = new Device();
                    device.DeviceId = deviceNode.Attributes["ID"].Value.Trim();
                    List<TSemaphore> semaphoreList = new List<TSemaphore>();
                    foreach (XmlNode node in deviceNode.ChildNodes)
                    {
                        EnumType type = (EnumType)int.Parse(node.Attributes["Type"].Value.Trim());
                        string id = node.Attributes["ID"].Value.Trim();
                        if (!CheckNumberValue(id))
                        {
                            errorMsg = "TSemaphore.ID is invalid";
                            break;
                        }
                        string signalNumber = node.Attributes["SignalNumber"].Value.Trim();
                        if (!string.IsNullOrEmpty(signalNumber) && !CheckNumberValue(signalNumber))
                        {
                            errorMsg = "TSemaphore.SignalNumber is invalid";
                            break;
                        }
                        //注意：上报的信号不允许为空，否则直接回复上报失败，不存库
                        string strMeasureVal = node.Attributes["MeasuredVal"].Value.Trim();
                        float? measureVal = null;
                        if (string.IsNullOrEmpty(strMeasureVal) || strMeasureVal.ToUpper()=="NULL")
                        {
                            errorMsg = "TSemaphore.MeasuredVal is NullOrEmtpy";
                            break;
                        }
                        else
                        {
                            measureVal = float.Parse(strMeasureVal);
                        }
                        //上报的设置值是可以为空
                        string strSetupVal = node.Attributes["SetupVal"].Value.Trim();
                        float? setupVal = null;
                        if (!string.IsNullOrEmpty(strSetupVal) && strSetupVal.ToUpper() != "NULL")
                        {
                            setupVal = float.Parse(strSetupVal);
                        }
                        EnumState status = (EnumState)int.Parse(node.Attributes["Status"].Value.Trim());
                        string strTime = node.Attributes["Time"].Value.Trim();
                        DateTime time = DateTime.Now;
                        if (string.IsNullOrEmpty(strTime))
                        {
                            errorMsg = "TSemaphore.Time is NullOrEmtpy";
                            break;
                        }
                        else
                        {
                            time = Convert.ToDateTime(strTime);
                        }
                        TSemaphore semaphore = new TSemaphore(type, id, signalNumber, measureVal, setupVal, status, time);
                        semaphoreList.Add(semaphore);
                    }
                    if(errorMsg != string.Empty)
                    {
                        break;
                    }
                    device.Semaphores = semaphoreList;
                    deviceList.Add(device);
                }
                //一旦上报信号值为空，则直接构建一个带错误信息的空实体
                SendData sendData = null;
                if (errorMsg != string.Empty)
                {
                    sendData = GetErrorEntity(errorMsg);
                    entityLogger.ErrorFormat("SendData.Deserialize():{0}", errorMsg);
                }
                else
                {
                    sendData = new SendData(fsuId, deviceList);
                }
                entityLogger.DebugFormat("SendData.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                sendData.StringXML = xmlDoc.InnerXml;
                return sendData;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SendData.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SendData.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                //反序列化异常时，返回带错误信息的实体
                SendData errorEntity = GetErrorEntity(string.Format("SEND_DATA Deserialize failure:{0}", ex.Message));
                errorEntity.StringXML = xmlDoc.InnerXml;
                return errorEntity;
            }
        }

        /// <summary>
        /// 反序列化异常时，构造带错误信息的实体
        /// </summary>
        /// <param name="errorMsg"></param>
        /// <returns></returns>
        private static SendData GetErrorEntity(string errorMsg)
        {
            SendData errorEntity = new SendData(string.Empty, null);
            SendDataAck ackEntity = new SendDataAck(EnumResult.FAILURE, errorMsg);
            errorEntity.ErrorMsg = ackEntity.Serialize();
            return errorEntity;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}, {2}:", MessageId, MessageType, FSUID);
            sb.AppendLine();
            if (Values != null)
            {
                foreach (Device device in Values)
                {
                    sb.AppendFormat("{0}\r\n", device.ToString());
                }
            }
            return sb.ToString();
        }

    }
}
