﻿using BLL;
using Delivery.API;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;




namespace DM.TestOrder.Controllers {
        
    public class SiteEquipController : BaseController{

        //http://localhost/orderapi/SiteEquip?stationId=755000001&userid=-3
        [HttpGet]
        public JsonResult Get(int orderType, int userid,
            int stationId, string houseName = "", string equipmentName = "", string equipmentNo = "") {
            Debug.WriteLine(stationId);

           // var sql = string.Format(@"GetAllByStationId {0},{1}", stationId, userid);


            DataTable dt = EquipmentDal.GetByKeyword(orderType, userid, stationId, houseName, equipmentName, equipmentNo);

            return new JsonResult(dt);
            //var sReturn = JsonConvert.SerializeObject(dt);
            //HttpResponseMessage result = new HttpResponseMessage {
            //    Content = new StringContent(sReturn, Encoding.GetEncoding("UTF-8"), "application/json")
            //};
            //return result; 


        }
    }
}
