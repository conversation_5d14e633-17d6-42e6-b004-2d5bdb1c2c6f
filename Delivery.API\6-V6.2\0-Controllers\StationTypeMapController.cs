﻿using Delivery.API;
using DM.TestOrder.DAL;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;




namespace DM.TestOrder.Controllers {

    public class StationTypeMapController : BaseController {
        [HttpGet]
        public JsonResult Get() {
            var dt = StationTypeMapDal.GetAll();
            return new JsonResult(dt);
            //var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(dt);

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};

        }

        [HttpPost]
        public JsonResult POST([FromBody]JObject value) {
            var WR_ItemId = int.Parse(value["WR_ItemId"].ToString());
            var SiteWeb_ItemId = int.Parse(value["SiteWeb_ItemId"].ToString());


            var rtnMsg = StationTypeMapDal.AddOne(WR_ItemId, SiteWeb_ItemId);

            var rtn = new {
                errormsg = rtnMsg
            };
            return new JsonResult(rtn);

            //var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(rtn);


            //Debug.WriteLine(value.ToString());
            //Debug.WriteLine(jsonRtn.ToString());

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};
        }
    }
}
