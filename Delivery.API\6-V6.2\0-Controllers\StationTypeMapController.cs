﻿using Delivery.API;
using DM.TestOrder.DAL;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;

namespace DM.TestOrder.Controllers {

    public class StationTypeMapController : BaseController {
        [HttpGet]
        public JsonResult Get() {
            var dt = StationTypeMapDal.GetAll();
            DataTableColumnMapper.RenameColumns(dt, DicTypeMapFieldMap);
            return new JsonResult(dt);
            //var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(dt);

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};

        }

        private static readonly Dictionary<string, string> DicTypeMapFieldMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "wr_itemid", "WR_ItemId" },
            { "wr_itemvalue", "WR_ItemValue" },
            { "siteweb_itemid", "SiteWeb_ItemId" },
            { "siteweb_itemvalue", "SiteWeb_ItemValue" }
        };

        [HttpPost]
        public JsonResult POST([FromBody]JObject value) {
            var WR_ItemId = int.Parse(value["WR_ItemId"].ToString());
            var SiteWeb_ItemId = int.Parse(value["SiteWeb_ItemId"].ToString());


            var rtnMsg = StationTypeMapDal.AddOne(WR_ItemId, SiteWeb_ItemId);

            var rtn = new {
                errormsg = rtnMsg
            };
            return new JsonResult(rtn);

            //var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(rtn);


            //Debug.WriteLine(value.ToString());
            //Debug.WriteLine(jsonRtn.ToString());

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};
        }
    }
}
