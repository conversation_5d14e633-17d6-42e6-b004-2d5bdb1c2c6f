﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Diagnostics;
using System.Xml;

namespace ENPC.Kolo.Entity.B
{
    /// <summary>
    /// 用户请求写监控点的设置值应答报文
    /// </summary>
    public sealed class SetPointAck : BMessage
    {
        public int UserId { get; set; }
        public int ControlId { get; set; }
        public DateTime StartTime { get; set; }
        public EnumResult Result { get; private set; }

        public DeviceSetPointAck[] Devices { get; private set; }

        #region SiteWeb配置

        public int MyStationId { get; set; }

        #endregion

        public SetPointAck(string fsuId, string fsuCode, EnumResult result, params DeviceSetPointAck[] devices)
            : base()
        {
            Debug.Assert(devices != null);

            MessageType = (int)BMessageType.SetPoint;
            FsuId = fsuId;
            FsuCode = fsuCode;

            Devices = devices;
        }

        public override string Serialize()
        {
            throw new NotImplementedException();
        }

        public static SetPointAck Deserialize(XmlDocument xmldoc)
        {
            string fsuId = xmldoc.SelectSingleNode("/Response/Info/FsuId").InnerText;
            string fsuCode = fsuId;
            XmlNode xTmp = xmldoc.SelectSingleNode("/Response/Info/FsuCode");
            if (xTmp != null)
            {
                fsuCode = xTmp.InnerText;
            }

            string resultString = xmldoc.SelectSingleNode("/Response/Info/Result").InnerText;
            EnumResult enumResult = new EnumResult();
            if (resultString != null && resultString != "")
            {
                int result = int.Parse(xmldoc.SelectSingleNode("/Response/Info/Result").InnerText);
                enumResult = (EnumResult)Enum.Parse(typeof(EnumResult), result.ToString(), false);
            }

            XmlNodeList nodelist = xmldoc.SelectNodes("/Response/Info/DeviceList/Device");
           
            List<DeviceSetPointAck> deviceSetPointAckList = new List<DeviceSetPointAck>();

            foreach (XmlNode xn in nodelist)//遍历所有子节点 
            {
                XmlElement xe = (XmlElement)xn;
                string deviceId = xe.GetAttribute("Id");
                string deviceCode = xe.GetAttribute("Code");

                List<string> successList = new List<string>();
                List<string> failList = new List<string>();

                XmlNode xns = xe.SelectSingleNode("SuccessList");

                if (xns != null)
                {
                    foreach (XmlNode xnSub in xns.ChildNodes)
                    {
                        successList.Add(xnSub.InnerText);
                    }
                }

                XmlNode xnf = xe.SelectSingleNode("FailList");

                if (xnf != null)
                {
                    foreach (XmlNode xnSub in xnf.ChildNodes)
                    {
                        failList.Add(xnSub.InnerText);
                    }
                }

                DeviceSetPointAck deviceSetPointAck = new DeviceSetPointAck(deviceId, deviceCode, successList.ToArray(), failList.ToArray());
                deviceSetPointAckList.Add(deviceSetPointAck);
            }

            SetPointAck setPointAck = new SetPointAck(fsuId, fsuCode, enumResult, deviceSetPointAckList.ToArray());
            return setPointAck;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();

            sb.AppendFormat("{0}, {1}: {2}, {3}, {4}",
                MessageId, (BMessageType)MessageType, FsuId, FsuCode, Devices != null ? Devices.Length : 0);
            sb.AppendLine();
            if (Devices != null)
            {
                foreach (DeviceSetPointAck item in Devices)
                {
                    sb.AppendFormat("{0}", item);
                    sb.AppendLine();
                }
            }

            return sb.ToString();
        }

    }

    public sealed class DeviceSetPointAck
    {
        public string DeviceId { get; private set; }
        public string DeviceCode { get; private set; }

        public string[] SuccessIds { get; private set; }
        public string[] FailIds { get; private set; }

        #region SiteWeb配置

        public int MyDeviceId { get; set; }

        #endregion

        public DeviceSetPointAck(string deviceId, string deviceCode, string[] successIds, params string[] failIds)
        {
            DeviceId = deviceId;
            DeviceCode = deviceCode;

            SuccessIds = successIds;
            FailIds = failIds;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}", DeviceId, DeviceCode);
            sb.AppendLine();

            sb.Append("SuccessList");
            sb.AppendLine();
            foreach (string item in SuccessIds)
            {
                sb.AppendFormat("{0}", item);
                sb.AppendLine();
            }

            sb.Append("FailList");
            sb.AppendLine();
            foreach (string item in SuccessIds)
            {
                sb.AppendFormat("{0}", FailIds);
                sb.AppendLine();
            }

            return sb.ToString();
        }

    }
}
