﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<procedures>
		<procedure owner="" name="SP_WRStation_CUCCGetStation_GetUserId" grant="">
			<parameters>
				<parameter name="LogonId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT  TBL_Account.UserId  
					FROM TBL_Account 
					WHERE TBL_Account.LogonId = :LogonId ;
				 ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRStation_CUCCGetStation_JudgeExist" grant="">
			<parameters>
				<parameter name="userId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM TBL_UserRoleMap WHERE TBL_UserRoleMap.UserId = :userId AND TBL_UserRoleMap.RoleId = -1 ;
				 ]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRStation_CUCCGetStation_GetFiledValue" grant="">
			<parameters>
				<parameter name="userId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT TO_CHAR(a.StructureId) AS sCenterId ,a.StructureName AS CenterName 
					FROM TBL_StationStructure a
					WHERE a.StructureType = 2 AND a.ParentStructureId = 0;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRStation_CUCCGetStation_GetResultTable1_IsAdmin" grant="">
			<parameters>
				<parameter name="sCenterId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="CenterName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT  :sCenterId As  CenterId, :CenterName As  CenterName ,
							a.WRStationId,
							a.StructureId, b.StructureName,a.StationCode,i.StationName,
							a.StationCategory , c.ItemValue StationCategoryName,
							a.StationStatus, d.ItemValue StatusName ,
							a.Address,a.UserId, a.SWUserName UserName,
							a.ApplyTime,a.ApproveTime,
							a.Province , f.ItemValue ProvinceName,
							a.City , g.ItemValue CityName,
							a.County , h.ItemValue CountyName,
							a.SWStationId,
							a.RejectCause,
							a.Remark,
							i.Latitude,
							i.Longitude,
							a.ContractNo,
							a.ProjectName,
							a.StationRId
						FROM WR_StationManagementCUCC a
						INNER JOIN TBL_StationStructure b ON a.StructureId = b.StructureId
						INNER JOIN WR_DataItem c ON a.StationCategory = c.ItemId AND c.EntryId = 6
						INNER JOIN WR_DataItem d ON a.StationStatus = d.ItemId AND d.EntryId = 5 
						INNER JOIN WR_DataItem f ON a.Province = f.ItemId AND f.EntryId = 1
						INNER JOIN WR_DataItem g ON a.City = g.ItemId AND g.EntryId = 2
						INNER JOIN WR_DataItem h ON a.County = h.ItemId AND h.EntryId = 3 
						INNER JOIN TBL_Station i ON a.SWStationId = i.StationId
						INNER JOIN (SELECT DISTINCT UserId FROM WR_StationManagementCUCC) j ON a.UserId = j.UserId
						WHERE a.StationStatus = 3 
						$[WhereTime]
						$[WhereStructureId] 
						$[WhereStationCategory]
						$[WhereSWStationName]
						$[WhereStationCode]
						$[WhereStatus]
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRStation_CUCCGetStation_GetResultTable2_IsAdmin" grant="">
			<parameters>
				<parameter name="sCenterId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="CenterName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 
							:sCenterId As  CenterId, :CenterName As  CenterName ,
							a.WRStationId,
							a.StructureId, b.StructureName,a.StationCode,a.StationName,
							a.StationCategory , c.ItemValue StationCategoryName,
							a.StationStatus, d.ItemValue StatusName ,
							a.Address,a.UserId, a.SWUserName UserName,
							a.ApplyTime,a.ApproveTime,
							a.Province , f.ItemValue ProvinceName,
							a.City , g.ItemValue CityName,
							a.County , h.ItemValue CountyName,
							a.SWStationId,
							a.RejectCause,
							a.Remark,
							null Latitude,
							null Longitude,
							a.ContractNo,
							a.ProjectName,
							a.StationRId
						FROM WR_StationManagementCUCC a
						INNER JOIN TBL_StationStructure b ON a.StructureId = b.StructureId
						INNER JOIN WR_DataItem c ON a.StationCategory = c.ItemId AND c.EntryId = 6
						INNER JOIN WR_DataItem d ON a.StationStatus = d.ItemId AND d.EntryId = 5 
						INNER JOIN WR_DataItem f ON a.Province = f.ItemId AND f.EntryId = 1
						INNER JOIN WR_DataItem g ON a.City = g.ItemId AND g.EntryId = 2
						INNER JOIN WR_DataItem h ON a.County = h.ItemId AND h.EntryId = 3 
						INNER JOIN (SELECT DISTINCT UserId FROM WR_StationManagementCUCC) j ON a.UserId = j.UserId
						WHERE a.StationStatus != 3 
						$[WhereTime] 
						$[WhereStructureId] 
						$[WhereStationCategory]
						$[WhereStationName]
						$[WhereStationCode]
						$[WhereStatus]
				]]>
			</body>
		</procedure>

		<procedure owner="" name="SP_WRStation_CUCCGetStation_GetResultTable1_NotAdmin" grant="">
			<parameters>
				<parameter name="userId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="sCenterId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="CenterName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			
			<body>
				<![CDATA[ 
					SELECT  :sCenterId As  CenterId, :CenterName As  CenterName ,
							a.WRStationId,
							a.StructureId, b.StructureName,a.StationCode,i.StationName,
							a.StationCategory , c.ItemValue StationCategoryName,
							a.StationStatus, d.ItemValue StatusName ,
							a.Address,a.UserId, a.SWUserName UserName,
							a.ApplyTime,a.ApproveTime,
							a.Province , f.ItemValue ProvinceName,
							a.City , g.ItemValue CityName,
							a.County , h.ItemValue CountyName,
							a.SWStationId,
							a.RejectCause,
							a.Remark,
							i.Latitude,
							i.Longitude,
							a.ContractNo,
							a.ProjectName,
							a.StationRId
						FROM WR_StationManagementCUCC a
						INNER JOIN TBL_StationStructure b ON a.StructureId = b.StructureId
						INNER JOIN WR_DataItem c ON a.StationCategory = c.ItemId AND c.EntryId = 6
						INNER JOIN WR_DataItem d ON a.StationStatus = d.ItemId AND d.EntryId = 5 
						INNER JOIN WR_DataItem f ON a.Province = f.ItemId AND f.EntryId = 1
						INNER JOIN WR_DataItem g ON a.City = g.ItemId AND g.EntryId = 2
						INNER JOIN WR_DataItem h ON a.County = h.ItemId AND h.EntryId = 3 
						INNER JOIN TBL_Station i ON a.SWStationId = i.StationId
						INNER JOIN (SELECT DISTINCT UserId FROM TBL_UserRoleMap 
									WHERE RoleId IN (SELECT RoleId FROM TBL_UserRoleMap 
													 WHERE TBL_UserRoleMap.UserId = :userId)) j ON a.UserId = j.UserId
						WHERE a.StationStatus = 3 
						$[WhereTime] 
						$[WhereStructureId] 
						$[WhereStationCategory]
						$[WhereSWStationName]
						$[WhereStationCode]
						$[WhereStatus]
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRStation_CUCCGetStation_GetResultTable2_NotAdmin" grant="">
			<parameters>
				<parameter name="userId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="sCenterId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="CenterName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>

			<body>
				<![CDATA[ 
					SELECT 
							:sCenterId As  CenterId, :CenterName As  CenterName ,
							a.WRStationId,
							a.StructureId, b.StructureName,a.StationCode,a.StationName,
							a.StationCategory , c.ItemValue StationCategoryName,
							a.StationStatus, d.ItemValue StatusName ,
							a.Address,a.UserId, a.SWUserName UserName,
							a.ApplyTime,a.ApproveTime,
							a.Province , f.ItemValue ProvinceName,
							a.City , g.ItemValue CityName,
							a.County , h.ItemValue CountyName,
							a.SWStationId,
							a.RejectCause,
							a.Remark,
							null Latitude,
							null Longitude,
							a.ContractNo,
							a.ProjectName,
							a.StationRId
						FROM WR_StationManagementCUCC a
						INNER JOIN TBL_StationStructure b ON a.StructureId = b.StructureId
						INNER JOIN WR_DataItem c ON a.StationCategory = c.ItemId AND c.EntryId = 6
						INNER JOIN WR_DataItem d ON a.StationStatus = d.ItemId AND d.EntryId = 5 
						INNER JOIN WR_DataItem f ON a.Province = f.ItemId AND f.EntryId = 1
						INNER JOIN WR_DataItem g ON a.City = g.ItemId AND g.EntryId = 2
						INNER JOIN WR_DataItem h ON a.County = h.ItemId AND h.EntryId = 3 
						INNER JOIN (SELECT DISTINCT UserId FROM TBL_UserRoleMap 
									WHERE RoleId IN (SELECT RoleId FROM TBL_UserRoleMap 
													 WHERE TBL_UserRoleMap.UserId = :userId)) j ON a.UserId = j.UserId
						WHERE a.StationStatus != 3 
						$[WhereTime] 
						$[WhereStructureId] 
						$[WhereStationCategory]
						$[WhereStationName]
						$[WhereStationCode]
						$[WhereStatus]
				]]>
			</body>
		</procedure>


		
		
		
		<procedure owner="" name="SP_WRStation_CUCCGetStationCondition_GetResultTable_Audited" grant="">
			<parameters>
				<parameter name="sCenterId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="CenterName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT :sCenterId AS CenterId, :CenterName AS CenterName,
						a.WRStationId,
						a.StructureId, b.StructureName,a.StationCode,i.StationName,
						a.StationCategory , c.ItemValue StationCategoryName,
						a.StationStatus, d.ItemValue StatusName ,
						a.Address,a.UserId, a.SWUserName UserName,
						a.ApplyTime,a.ApproveTime,
						a.Province , f.ItemValue ProvinceName,
						a.City , g.ItemValue CityName,
						a.County , h.ItemValue CountyName,
						a.SWStationId,
						a.RejectCause,
						a.Remark,
						a.ContractNo,
						a.ProjectName,
						a.StationRId
					FROM WR_StationManagementCUCC a
					INNER JOIN TBL_StationStructure b ON a.StructureId = b.StructureId
					INNER JOIN WR_DataItem c ON a.StationCategory = c.ItemId AND c.EntryId = 6
					INNER JOIN WR_DataItem d ON a.StationStatus = d.ItemId AND d.EntryId = 5 
					INNER JOIN WR_DataItem f ON a.Province = f.ItemId AND f.EntryId = 1
					INNER JOIN WR_DataItem g ON a.City = g.ItemId AND g.EntryId = 2
					INNER JOIN WR_DataItem h ON a.County = h.ItemId AND h.EntryId = 3 
					INNER JOIN TBL_Station i ON a.SWStationId = i.StationId
					WHERE a.StationStatus = 3 
					$[WhereTime] 
					$[WhereStructureId] 
					$[WhereStationCategory]
					$[WhereSWStationName]
					$[WhereStationCode]
				]]>
			</body>
		</procedure>

		<procedure owner="" name="SP_WRStation_CUCCGetStationCondition_GetResultTable_NotAudited" grant="">
			<parameters>
				<parameter name="sCenterId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="CenterName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 
							:sCenterId AS CenterId , :CenterName AS CenterName, 
							a.WRStationId,
							a.StructureId, b.StructureName,a.StationCode,a.StationName,
							a.StationCategory , c.ItemValue StationCategoryName,
							a.StationStatus, d.ItemValue StatusName ,
							a.Address,a.UserId, a.SWUserName UserName,
							a.ApplyTime,a.ApproveTime,
							a.Province , f.ItemValue ProvinceName,
							a.City , g.ItemValue CityName,
							a.County , h.ItemValue CountyName,
							a.SWStationId,
							a.RejectCause,
							a.Remark,
							a.ContractNo,
							a.ProjectName,
							a.StationRId
						FROM WR_StationManagementCUCC a
						INNER JOIN TBL_StationStructure b ON a.StructureId = b.StructureId
						INNER JOIN WR_DataItem c ON a.StationCategory = c.ItemId AND c.EntryId = 6
						INNER JOIN WR_DataItem d ON a.StationStatus = d.ItemId AND d.EntryId = 5 
						INNER JOIN WR_DataItem f ON a.Province = f.ItemId AND f.EntryId = 1
						INNER JOIN WR_DataItem g ON a.City = g.ItemId AND g.EntryId = 2
						INNER JOIN WR_DataItem h ON a.County = h.ItemId AND h.EntryId = 3 
						WHERE a.StationStatus = 1 
						$[WhereTime] 
						$[WhereStructureId] 
						$[WhereStationCategory]
						$[WhereSWStationName]
						$[WhereStationCode]
				]]>
			</body>
		</procedure>







		<procedure owner="" name="SP_WRStation_UpdWRStationCUCC_JudgeExist1" grant="">
			<parameters>
				<parameter name="WRStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="StationName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT 'X' FROM WR_StationManagementCUCC 
							WHERE WR_StationManagementCUCC.WRStationId != :WRStationId AND WR_StationManagementCUCC.StationName = :StationName limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRStation_UpdWRStationCUCC_GetFiledValue1" grant="">
			<parameters>
				<parameter name="WRStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="StationName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT a.StationStatus AS iStatus, a.StationCode AS StnCode, a.SWStationId, a.County AS OriCounty, a.StationCategory AS OriStationCategory 
					FROM WR_StationManagementCUCC a
					WHERE a.WRStationId = :WRStationId limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRStation_UpdWRStationCUCC_GetFiledValue2" grant="">
			<parameters>
				<parameter name="WRStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT concat('StructureId:' , TO_CHAR(StructureId) ,
	    					'-StationName:' , TO_CHAR(StationName) ,
	    					'-StationCategory:' , TO_CHAR(StationCategory) ,
	    					'-Address:' , TO_CHAR(Address) ,
	    					'-Remark:' , TO_CHAR(Remark) ,
	    					'-ContractNo:' , TO_CHAR(ContractNo) ,
	    					'-ProjectName:' , TO_CHAR(ProjectName) ,
	    					'-StationRId:' , IFNULL(TO_CHAR(StationRId),'')) 
	    					FROM WR_StationManagementCUCC WHERE WR_StationManagementCUCC.WRStationId = :WRStationId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRStation_UpdWRStationCUCC_UpdateWRStationManagementCUCC_IStatusIsThree" grant="">
			<parameters>
				<parameter name="StructureId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="StationName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="StationCategory" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Province" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="City" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="County" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Address" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Remark" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ContractNo" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ProjectName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="StationRId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE WR_StationManagementCUCC
					SET WR_StationManagementCUCC.StructureId = :StructureId
						,WR_StationManagementCUCC.StationName = :StationName
						,WR_StationManagementCUCC.StationCategory = :StationCategory
						,WR_StationManagementCUCC.Province = :Province
						,WR_StationManagementCUCC.City = :City
						,WR_StationManagementCUCC.County = :County
						,WR_StationManagementCUCC.Address = :Address
						,WR_StationManagementCUCC.Remark = :Remark
						,WR_StationManagementCUCC.ContractNo = :ContractNo
						,WR_StationManagementCUCC.ProjectName = :ProjectName
						,WR_StationManagementCUCC.StationRId = :StationRId
					WHERE WR_StationManagementCUCC.WRStationId = :WRStationId ;  
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRStation_UpdWRStationCUCC_GetFiledValue3" grant="">
			<parameters>
				<parameter name="StationCategory" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT SiteWeb_ItemId AS SWStationCategory 
					FROM WO_DicStationTypeMap 
					WHERE WO_DicStationTypeMap.WR_ItemId = :StationCategory limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRStation_UpdWRStationCUCC_UpdateTBLStation_IStatusIsThree" grant="">
			<parameters>
				<parameter name="StationName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWStationCategory" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWStationId" type="int" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TBL_Station
					SET TBL_Station.StationName = :StationName
						,TBL_Station.StationCategory = :SWStationCategory
						,TBL_Station.UpdateTime = NOW()
					WHERE TBL_Station.StationId = :SWStationId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRStation_UpdWRStationCUCC_GetFiledValue4" grant="">
			<parameters>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					SELECT  a.StructureId AS OriStationStructureId 
					FROM TBL_StationStructureMap a 
					INNER JOIN TBL_StationStructure b ON a.StructureId = b.StructureId
					WHERE b.StructureGroupId = 1 AND a.StationId = :SWStationId limit 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRStation_UpdWRStationCUCC_UpdateTBLStationStructureMap_IStatusIsThree" grant="">
			<parameters>
				<parameter name="StructureId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="OriStationStructureId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TBL_StationStructureMap 
					SET TBL_StationStructureMap.StructureId = :StructureId
					WHERE TBL_StationStructureMap.StationId = :SWStationId 
					AND TBL_StationStructureMap.StructureId = :OriStationStructureId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRStation_UpdWRStationCUCC_UpdateTBLStationProjectInfo_IStatusIsThree" grant="">
			<parameters>
				<parameter name="ProjectName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ContractNo" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TBL_StationProjectInfo 
					SET TBL_StationProjectInfo.ProjectName = :ProjectName
						, TBL_StationProjectInfo.ContractNo = :ContractNo
					WHERE TBL_StationProjectInfo.StationId = :SWStationId;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRStation_UpdWRStationCUCC_UpdateTBLConfigChangeMicroLog_IStatusIsThree" grant="">
			<parameters>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TBL_ConfigChangeMicroLog
					SET TBL_ConfigChangeMicroLog.EditType = 2, TBL_ConfigChangeMicroLog.UpdateTime = NOW()
					WHERE TBL_ConfigChangeMicroLog.ObjectId = TO_CHAR(:SWStationId) AND TBL_ConfigChangeMicroLog.ConfigId = 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRStation_UpdWRStationCUCC_UpdateTBLConfigChangeMacroLog_IStatusIsThree" grant="">
			<parameters>
				<parameter name="SWStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE TBL_ConfigChangeMacroLog
					SET TBL_ConfigChangeMacroLog.EditType = 2, TBL_ConfigChangeMacroLog.UpdateTime = now()
					WHERE TBL_ConfigChangeMacroLog.ObjectId = TO_CHAR(:SWStationId) AND TBL_ConfigChangeMacroLog.ConfigId = 1;
				]]>
			</body>
		</procedure>
		<procedure owner="" name="SP_WRStation_UpdWRStationCUCC_UpdateWRStationManagementCUCC_IStatusNotThree" grant="">
			<parameters>
				<parameter name="StructureId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="StnCode" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="StationName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="StationCategory" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Province" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="City" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="County" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Address" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="Remark" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ContractNo" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="ProjectName" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="StationRId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
				<parameter name="WRStationId" type="string" direction="Input" size="200" precision="0" scale="0" param="" nulldefault=""/>
			</parameters>
			<body>
				<![CDATA[ 
					UPDATE WR_StationManagementCUCC
					SET WR_StationManagementCUCC.StructureId = :StructureId
						,WR_StationManagementCUCC.StationCode = :StnCode
						,WR_StationManagementCUCC.StationName = :StationName
						,WR_StationManagementCUCC.StationCategory = :StationCategory
						,WR_StationManagementCUCC.StationStatus = 1 -- --提交申请
						,WR_StationManagementCUCC.Province = :Province
						,WR_StationManagementCUCC.City = :City
						,WR_StationManagementCUCC.County = :County
						,WR_StationManagementCUCC.Address = :Address
						,WR_StationManagementCUCC.Remark = :Remark
						,WR_StationManagementCUCC.ContractNo = :ContractNo
						,WR_StationManagementCUCC.ProjectName = :ProjectName
						,WR_StationManagementCUCC.StationRId = :StationRId
					WHERE WR_StationManagementCUCC.WRStationId = :WRStationId;
				]]>
			</body>
		</procedure>
	</procedures>
</root>
