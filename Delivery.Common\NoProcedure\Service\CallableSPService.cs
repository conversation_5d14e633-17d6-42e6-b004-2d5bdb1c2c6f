﻿using System;
using System.Collections.Generic;
using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;

namespace Delivery.Common.NoProcedure.Service
{
    public class CallableSPService
    {
        private static CallableSPService _instance = null;

        public static CallableSPService Instance
        {
            get
            {
                if (_instance == null) _instance = new CallableSPService();
                return _instance;
            }
        }
        /// <summary> 存储过程 PBL_SaveEquipmentMaitain 的实现 </summary>
        public int PblSaveEquipmentMaitain(object stationId, object equipmentId, object equipmentState, DateTime? startTime, DateTime? endTime
            , object userId, string description, string extendFiled1)
        {
            //stationId、equipmentId、equipmentState、userId需要是int型object，主要是为了能接收DBNull.Value
            Dictionary<string, object> realParams = new Dictionary<string, object>();
            realParams.Add("StationId", stationId);
            realParams.Add("EquipmentId", equipmentId);
            realParams.Add("EquipmentState", equipmentState);
            realParams.Add("StartTime", startTime);
            realParams.Add("EndTime", endTime);
            realParams.Add("UserId", userId);
            realParams.Add("Description", description);
            realParams.Add("ExtendFiled1", extendFiled1);

            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                try
                {
                    object countObj = executeHelper.ExecuteScalar("SaveEquipmentMaitain_GetCount", realParams);
                    int count = countObj == null || countObj == DBNull.Value ? 0 : int.Parse(countObj.ToString());
                    if (count <= 0)
                    {//Not Exists
                        executeHelper.ExecuteNonQuery("SaveEquipmentMaitain_InsertMaintain", realParams);
                        executeHelper.ExecuteNonQuery("SaveEquipmentMaitain_InsertStateOperation1", realParams);
                    }
                    else
                    {
                        if (startTime == null || endTime == null)
                        {
                            executeHelper.ExecuteNonQuery("SaveEquipmentMaitain_InsertStateOperation2", realParams);
                        }
                        else
                        {
                            executeHelper.ExecuteNonQuery("SaveEquipmentMaitain_InsertStateOperation3", realParams);
                        }
                        executeHelper.ExecuteNonQuery("SaveEquipmentMaitain_UpdateMaintain", realParams);
                    }
                    executeHelper.Commit();
                }
                catch (Exception ex)
                {
                    executeHelper.Rollback();
                    Logger.Log("CallableSPService.PblSaveEquipmentMaitain throw Exception: ", LogType.Error);
                    Logger.Log(ex);
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
            return 0;
        }

        /// <summary> 存储过程 PAM_SaveOperationRecord 的实现 </summary>
        public int PamSaveOperationRecord(object userId, object operation, object stationId, object operationType, string operationContent)
        {
            //userId、operation、stationId、operationType需要是int型object，主要是为了能接收DBNull.Value
            if(userId == null || userId == DBNull.Value ||
                operation == null || operation == DBNull.Value ||
                operationType == null || operationType == DBNull.Value ||
                    operationContent == null) {
                return -1;
            }
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper);
                try
                {
                    object stationNameObj = executeHelper.ExecuteScalar("SaveOperationRecord_GetStationName", new Dictionary<string, object>() { { "StationId", stationId } });
                    string stationName = stationNameObj == null || stationNameObj == DBNull.Value ? "Not Find" : stationNameObj.ToString();

                    Dictionary<string, object> realParams = new Dictionary<string, object>
                    {
                        { "UserId", userId },
                        { "StationId", stationId },
                        { "StationName", stationName },
                        { "Operation", operation },
                        { "OperationType", operationType },
                        { "OperationContent", operationContent }
                    };
                    executeHelper.ExecuteNonQuery("SaveOperationRecord_InsertRecord", realParams);
                }
                catch (Exception ex)
                {
                    executeHelper.Rollback();
                    Logger.Log("CallableSPService.PamSaveOperationRecord throw Exception: ", LogType.Error);
                    Logger.Log(ex);
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
            return 0;
        }
    }
}
