﻿using Delivery.DAL;
using NPOI.SS.UserModel;
using NPOI.HSSF.UserModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NPOI.XSSF.UserModel;
using Delivery.Common;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service.CMCC;
using Delivery.Common.NoProcedure.Service;

namespace Delivery.BSL
{
    public class DeliveryDataBsl
    {
        readonly ExecuteSql exesql = new ExecuteSql();

        public DataTable GetGroupInfo()
        {
            DataTable dt;
            if (CommonUtils.IsNoProcedure)
            {
                dt = SP_GetStructureInfoService.Instance.GetStructureInfo();
            }
            else
            {
                dt = exesql.ExecuteStoredProcedure("SP_Get_StructureInfo", new QueryParameter[] {
                new QueryParameter("StructureId",DataType.Int,"0")
                });
            }
            if (dt == null)
                return new DataTable();
            return dt;
        }

        public DataTable GetNeedApproveResource(string LoginId)
        {
            DataTable dt;
            if(CommonUtils.IsNoProcedure)
            {
                dt = SP_GetStructureInfoService.Instance.GetNeedApproveResource(LoginId);
            }
            else
            {
                dt = exesql.ExecuteStoredProcedure("SP_Get_NeedApproveResource", new QueryParameter[] { new QueryParameter("LogonId", DataType.String, LoginId) });

            }
            if (dt == null)
                return new DataTable();
            return dt;
        }
        ///<summary>
        ///查询角色身份
        ///</summary>
        public DataTable GetUserRoleType(string logonid)
        {
            DataTable dt;
            if(CommonUtils.IsNoProcedure)
            {
                dt = SP_GetStructureInfoService.Instance.GetUserInfo(logonid);
            }
            else
            {
                dt = exesql.ExecuteStoredProcedure("SP_GetUserInfo", new QueryParameter[] { new QueryParameter("LogonId", DataType.String, logonid) });
            }
            if (dt == null)
                return new DataTable();
            return dt;
        }
             
        /// <summary>
        /// 查询 FSU入网申请 信息
        /// </summary>
        public DataTable GetFsuManagement(string startTime, string endTime, string structureId, string fsuName, string fsuCode, string manufactureId, string status,string LogonId)
        {
            string endstring = DateTime.Now.AddMonths(1).ToString("yyyy-MM-dd");
            startTime = string.IsNullOrEmpty(startTime) ? "2018-03-02" : startTime;
            endTime = string.IsNullOrEmpty(endTime) ? endstring : endTime; 
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Get_WRFsu", new QueryParameter[] {
                new QueryParameter("StartTime",DataType.DateTime,startTime),
                new QueryParameter("EndTime",DataType.DateTime,endTime),
                new QueryParameter("StructureId",DataType.Int,structureId),
                new QueryParameter("FsuName",DataType.String,fsuName),
                new QueryParameter("FsuCode",DataType.String,fsuCode),
                new QueryParameter("ManufacturerId",DataType.Int,fsuCode),
                new QueryParameter("Status", DataType.Int, status),
                new QueryParameter("LogonId",DataType.String,LogonId)
            });
            if (dt == null)
                return new DataTable();
            return dt;
        }

        /// <summary>
        /// 查找fsu关联设备信息供导出
        /// </summary>
        /// <param name="fsuCode"></param>
        /// <returns></returns>
        public DataSet GetFsuAssociatedDevice(string fsuCode)
        {
            DataSet ds;
            if(CommonUtils.IsNoProcedure)
            {
                ds = SP_GetStructureInfoService.Instance.GetFsuAssociatedDevice(fsuCode);
            }
            else
            {
                ds = exesql.ExecuteStoredProcedureDataSet("SP_Get_WRFsu_AssociatedDevice", new QueryParameter[] {
                new QueryParameter("FsuCode", DataType.String, fsuCode)
                });
            }
            if (ds == null)
            {
                ds = new DataSet();
            }
            return ds;
        }

        public MemoryStream WriteExcel(string templatePath, DataSet ds)
        {
            MemoryStream ms = new MemoryStream();
            DataRow fsuInfo = ds.Tables[0].Rows[0];
            DataTable deviceInfo = ds.Tables[1];
            using (FileStream stream = new FileStream(templatePath, FileMode.Open, FileAccess.Read))
            {
                IWorkbook workBook;
                ISheet sheet;
                if (templatePath.EndsWith(".xls")) // 2007前版本excel
                {
                    workBook = new HSSFWorkbook(stream);
                    
                }
                else
                {
                    workBook = new XSSFWorkbook(stream);
                }
                ICellStyle cellStyle = cellStyle = workBook.CreateCellStyle();
                cellStyle.BorderTop = BorderStyle.Thin;
                cellStyle.BorderRight = BorderStyle.Thin;
                cellStyle.BorderBottom = BorderStyle.Thin;
                cellStyle.BorderLeft = BorderStyle.Thin;
                cellStyle.Alignment = HorizontalAlignment.Center;
                cellStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.LightCornflowerBlue.Index;


                sheet = workBook.GetSheetAt(0);
                if (sheet == null)
                {
                    throw new FileLoadException("sheet页不存在");
                }
                
                ExcelReader.SetCellValue(sheet, 1, 1, fsuInfo["StationCode"].ToString());
                ExcelReader.SetCellValue(sheet, 1, 4, fsuInfo["StationName"].ToString());
                ExcelReader.SetCellValue(sheet, 2, 1, fsuInfo["StationCategory"].ToString());
                ExcelReader.SetCellValue(sheet, 2, 4, fsuInfo["Address"].ToString());

                ExcelReader.SetCellValue(sheet, 4, 1, fsuInfo["FsuCode"].ToString());
                ExcelReader.SetCellValue(sheet, 4, 4, fsuInfo["FsuName"].ToString());
                ExcelReader.SetCellValue(sheet, 5, 1, fsuInfo["HouseCode"].ToString());
                ExcelReader.SetCellValue(sheet, 5, 4, fsuInfo["HouseName"].ToString());
                ExcelReader.SetCellValue(sheet, 6, 1, fsuInfo["IPAddress"].ToString());
                ExcelReader.SetCellValue(sheet, 7, 1, fsuInfo["UserName"].ToString());
                ExcelReader.SetCellValue(sheet, 7, 4, fsuInfo["Password"].ToString());
                ExcelReader.SetCellValue(sheet, 8, 1, fsuInfo["FtpUserName"].ToString());
                ExcelReader.SetCellValue(sheet, 8, 4, fsuInfo["FtpPassword"].ToString());

                int startIndex = 11;
                for (int i = 0, len = deviceInfo.Rows.Count; i < len; i++)
                {
                    IRow row = sheet.CreateRow(startIndex++);
                    row.CreateCell(0).SetCellValue(deviceInfo.Rows[i]["DeviceCategory"].ToString());
                    row.CreateCell(1).SetCellValue(deviceInfo.Rows[i]["DeviceSubCategory"].ToString());
                    row.CreateCell(2).SetCellValue(deviceInfo.Rows[i]["DeviceName"].ToString());
                    row.CreateCell(3).SetCellValue(deviceInfo.Rows[i]["DeviceCode"].ToString());
                    row.CreateCell(4).SetCellValue(deviceInfo.Rows[i]["HouseCode"].ToString());
                    row.CreateCell(5).SetCellValue(deviceInfo.Rows[i]["HouseName"].ToString());
                    row.GetCell(0).CellStyle = cellStyle;
                    row.GetCell(1).CellStyle = cellStyle;
                    row.GetCell(2).CellStyle = cellStyle;
                    row.GetCell(3).CellStyle = cellStyle;
                    row.GetCell(4).CellStyle = cellStyle;
                    row.GetCell(5).CellStyle = cellStyle;
                }

                workBook.Write(ms);

            }
            return ms;
        }


   

        public DataTable GetWorkflowInfo(string WorkflowType)
        {
            string sql = @"SELECT a.AutoId,a.WorkflowId,a.WorkflowName,a.ApplyTime,b.UserName,a.WorkflowStatus,a.ReviewerL1,a.WorkflowType,a.Remark FROM WR_WorkflowCMCC a  
                            left join TBL_Account b on b.UserId = a.UserId where WorkflowType = @WorkflowType ORDER BY a.AutoId DESC";
            DataTable dt = exesql.ExecuteSQL(sql, new QueryParameter[] {
                 new QueryParameter("WorkflowType", DataType.String, WorkflowType)
            });
            if (dt == null)
                return new DataTable();
            return dt;
        }


        public bool ApproveWorkflow(string AutoId)
        {
            try
            {
                string sql = @"SELECT * FROM WR_WorkflowDetailCMCC where AutoId =@AutoId";
                DataTable dt = exesql.ExecuteSQL(sql, new QueryParameter[] {
                 new QueryParameter("AutoId", DataType.String, AutoId)
                });
               
                if (dt != null && dt.Rows.Count > 0)
                {
                    DeliveryDataBsl ccbsl = new DeliveryDataBsl();
                    foreach (DataRow row in dt.Rows)
                    {
                        int assetTypeId = Convert.ToInt32(row["AssetTypeId"]);
                        string assetId = row["AssetId"].ToString();

                        switch (assetTypeId)
                        {
                            case 1: // 站点
                                ccbsl.ApproveStation(assetId);
                                break;
                            case 2: // 机房
                                ccbsl.ApproveWRHouse(assetId);
                                break;
                            case 3: // FSU
                                ccbsl.ApproveWRFsu(assetId);
                                break;
                        }
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
            
        }


        public DataTable GetWorkflowDetailInfo(string AutoId)
        {
            string sql = @"SELECT c.StationName AssetName, m.ItemValue AssetType,b.AssetId,b.AssetTypeId  FROM WR_StationManagement c
		                    left join WR_WorkflowDetailCMCC b on b.AssetId = c.WRStationId and b.AssetTypeId = 1
                            left join wr_dataitem m on b.AssetTypeId = m.ItemId and m.EntryId = 10
                            where b.AutoId = @AutoId       
                            union
                            SELECT d.HouseName AssetName, m.ItemValue AssetType,b.AssetId,b.AssetTypeId FROM WR_HouseManagement d       
                                    left join WR_WorkflowDetailCMCC b on b.AssetId = d.WRHouseId and b.AssetTypeId = 2
                                    left join wr_dataitem m on b.AssetTypeId = m.ItemId and m.EntryId = 10
                            where b.AutoId = @AutoId        
                            union        
                            SELECT e.FsuName AssetName, m.ItemValue AssetType,b.AssetId,b.AssetTypeId  FROM WR_FsuManagement e       
		                            left join WR_WorkflowDetailCMCC b on b.AssetId = e.WRFsuId and b.AssetTypeId = 3
                                    left join wr_dataitem m on b.AssetTypeId = m.ItemId and m.EntryId = 10
                            where b.AutoId = @AutoId";
            DataTable dt = exesql.ExecuteSQL(sql, new QueryParameter[] {
                 new QueryParameter("AutoId", DataType.String, AutoId)
            });
            if (dt == null)
                return new DataTable();
            return dt;
        }

        public DataTable GetStationInfo(string structureId, string StationName, string StationCode, string strBegin, string strEnd, string stationType, string stationStatus,string LogonId)
        {
            if(CommonUtils.IsNoProcedure)
            {
                return StationService.Instance.GetStationInfo(structureId, StationName, StationCode, strBegin, strEnd, stationType, stationStatus, LogonId);
            }
           string endstring = DateTime.Now.AddMonths(1).ToString("yyyy-MM-dd");
           strBegin = string.IsNullOrEmpty(strBegin) ? "2018-03-02" : strBegin;
           strEnd = string.IsNullOrEmpty(strEnd) ? endstring : strEnd; 
           DataTable dt = exesql.ExecuteStoredProcedure("SP_Get_WRStation", new QueryParameter[] {
                new QueryParameter("StartTime",DataType.DateTime,strBegin),
                new QueryParameter("EndTime",DataType.DateTime,strEnd),
                new QueryParameter("StructureId",DataType.Int,structureId),
                new QueryParameter("StationCategory",DataType.Int,stationType),
                new QueryParameter("StationName",DataType.String,StationName),
                new QueryParameter("StationCode",DataType.String,StationCode),
                new QueryParameter("Status",DataType.Int,stationStatus),
                new QueryParameter("LogonId",DataType.String,LogonId)
            });
            if (dt == null)
                return new DataTable();
            return dt;
        }

        public DataTable GetFsuFtpInfo(string fsuCode)
        {
            string sql = @"SELECT a.FSUIP,a.FTPUserName,a.FTPPassWord,a.ExtendField1
                           FROM TSL_MonitorUnitCMCC a 
                           INNER JOIN TBL_Station b ON a.StationId = b.StationId
						   LEFT JOIN TBL_FsuConfig c ON a.FSUID = c.FSUID WHERE a.FSUID = @fsuCode";
            DataTable dt = exesql.ExecuteSQL(sql, new QueryParameter[] {
                new QueryParameter("fsuCode", DataType.String, fsuCode)
            });
            if (dt == null)
                return new DataTable();
            return dt;
        }

        public DataTable GetAllFsuFtpFile(string logonId, string stationName = "")
        {
            DataTable dt;
            if(CommonUtils.IsNoProcedure)
            {
                dt = SP_GetStructureInfoService.Instance.GetAllFsuFtpInfo(logonId, stationName);
            }
            else
            {
                dt = exesql.ExecuteStoredProcedure("SP_Get_AllFsuFtpInfo", new QueryParameter[] {
                new QueryParameter("LogonId",DataType.String,logonId),
                new QueryParameter("StationName",DataType.String,stationName)
                });
            }
            if (dt == null)
                return new DataTable();
            return dt;
        }

        public DataTable GetDeviceInfo(string startTime, string endTime, string stationId, string fsuId, string deviceCategory, string deviceSubCategory, string deviceName, string deviceCode, string logonId)
        {
            string endstring = DateTime.Now.AddMonths(1).ToString("yyyy-MM-dd");
            startTime = string.IsNullOrEmpty(startTime) ? "2018-03-02" : startTime;
            endTime = string.IsNullOrEmpty(endTime) ? endstring : endTime;
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Get_WRDevice", new QueryParameter[] {
                new QueryParameter("StartTime",DataType.DateTime,startTime),
                new QueryParameter("EndTime",DataType.DateTime,endTime),
                new QueryParameter("WRStationId",DataType.Int,stationId),
                new QueryParameter("WRFsuId",DataType.Int, fsuId),
                new QueryParameter("DeviceCategory",DataType.String,deviceCategory),
                new QueryParameter("DeviceSubCategory",DataType.String,deviceSubCategory),
                new QueryParameter("DeviceName",DataType.String,deviceName),
                new QueryParameter("DeviceCode",DataType.String,deviceCode),
                new QueryParameter("LogonId",DataType.String,logonId)
            });
            if (dt == null)
                return new DataTable();
            return dt;
        }

        public DataTable GetStationInfoCheck(string structureId, string StationName, string StationCode, string strBegin, string strEnd, string stationType, string stationStatus, string LogonId)
        {
            DataTable dt;
            string endstring = DateTime.Now.AddMonths(1).ToString("yyyy-MM-dd");
            strBegin = string.IsNullOrEmpty(strBegin) ? "2018-03-02" : strBegin;
            strEnd = string.IsNullOrEmpty(strEnd) ? endstring : strEnd;
            if (CommonUtils.IsNoProcedure)
            {
                dt = StationService.Instance.GetStationInfoCheck(structureId, StationName, StationCode, strBegin, strEnd, stationType, stationStatus, LogonId);
            }
            else
            {
                dt = exesql.ExecuteStoredProcedure("SP_Get_WRStationCondition", new QueryParameter[] {
                new QueryParameter("StartTime",DataType.DateTime,strBegin),
                new QueryParameter("EndTime",DataType.DateTime,strEnd),
                new QueryParameter("StructureId",DataType.Int,structureId),
                new QueryParameter("StationCategory",DataType.Int,stationType),
                new QueryParameter("StationName",DataType.String,StationName),
                new QueryParameter("StationCode",DataType.String,StationCode)
                });
            }
            if (dt == null)
                return new DataTable();
            return dt;
        }


        public DataTable GetHouseInfo(string structureId,string StationCode,string strBegin, string strEnd, string StationName, string HouseName, string HouseCode, string Status,string LogonId)
        {
            DataTable dt;
            string endstring = DateTime.Now.AddMonths(1).ToString("yyyy-MM-dd");
            strBegin = string.IsNullOrEmpty(strBegin) ? "2018-03-02" : strBegin;
            strEnd = string.IsNullOrEmpty(strEnd) ? endstring : strEnd; 
            if(CommonUtils.IsNoProcedure)
            {
                dt = HouseService.Instance.GetHouseInfo(structureId, StationCode, strBegin, strEnd, StationName, HouseName, HouseCode, Status, LogonId);
            }
            else
            {
                dt = exesql.ExecuteStoredProcedure("SP_Get_WRHouse", new QueryParameter[] {
                new QueryParameter("StartTime",DataType.DateTime,strBegin),
                new QueryParameter("EndTime",DataType.DateTime,strEnd),
                new QueryParameter("StructureId",DataType.Int,structureId),
                new QueryParameter("StationName",DataType.String,StationName),
                new QueryParameter("StationCode",DataType.String,StationCode),
                new QueryParameter("HouseName",DataType.String,HouseName),
                new QueryParameter("HouseCode",DataType.String,HouseCode),
                new QueryParameter("Status",DataType.Int,Status),
                new QueryParameter("LogonId",DataType.String,LogonId)
               });
            }
            if (dt == null)
                return new DataTable();
            return dt;
        }
        public DataTable GetHouseInfoCheck(string structureId, string StationCode, string strBegin, string strEnd, string StationName, string HouseName, string HouseCode, string Status, string LogonId)
        {
            string endstring = DateTime.Now.AddMonths(1).ToString("yyyy-MM-dd");
            strBegin = string.IsNullOrEmpty(strBegin) ? "2018-03-02" : strBegin;
            strEnd = string.IsNullOrEmpty(strEnd) ? endstring : strEnd;

            if(DateTime.Parse(strBegin)> DateTime.Parse(strEnd))
                return new DataTable();


            if (CommonUtils.IsNoProcedure)
            {
                return HouseService.Instance.SP_Get_WRHouseCondition(structureId, StationCode, strBegin, strEnd, StationName, HouseName, HouseCode, Status, LogonId);
            }


            DataTable dt = exesql.ExecuteStoredProcedure("SP_Get_WRHouseCondition", new QueryParameter[] {
                new QueryParameter("StartTime",DataType.DateTime,strBegin),
                new QueryParameter("EndTime",DataType.DateTime,strEnd),
                 new QueryParameter("StructureId",DataType.Int,structureId),
                new QueryParameter("StationName",DataType.String,StationName),
                new QueryParameter("StationCode",DataType.String,StationCode),
                new QueryParameter("HouseName",DataType.String,HouseName),
                new QueryParameter("HouseCode",DataType.String,HouseCode)
            });
            if (dt == null)
                return new DataTable();
            return dt;
        }

        public int Add_WRFsu(string WRHouseId, string FsuCode, string FsuName, string IPAddress, string UserName, string Password, string FtpUserName, string FtpPassword, string Remark, string ContractNo, string ProjectName, string LogonId, string OriginalPassword, string ShaPassword, string Sm3Password, string FTPType, string PortNumber)
        {
            if (CommonUtils.IsNoProcedure)
            {
                return FsuService.Instance.SP_Add_WRFsu(WRHouseId,  FsuCode,  FsuName,  IPAddress,  UserName,  Password,  FtpUserName,  FtpPassword,  Remark,  ContractNo,  ProjectName,  LogonId);                
            }

            int _result = 0;
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Add_WRFsu", new QueryParameter[] {
                new QueryParameter("WRHouseId",DataType.Int,WRHouseId),
                new QueryParameter("FsuCode",DataType.String,FsuCode),
                new QueryParameter("FsuName",DataType.String,FsuName),
                new QueryParameter("IPAddress",DataType.String,IPAddress),
                new QueryParameter("UserName",DataType.String,UserName),
                new QueryParameter("Password",DataType.String,Password),
                new QueryParameter("FtpUserName",DataType.String,FtpUserName),
                new QueryParameter("FtpPassword",DataType.String,FtpPassword),
                new QueryParameter("Remark",DataType.String,Remark),
                new QueryParameter("ContractNo",DataType.String,ContractNo),
                new QueryParameter("ProjectName",DataType.String,ProjectName),
                new QueryParameter("LogonId",DataType.String,LogonId),
                new QueryParameter("OriginalPassword",DataType.String,OriginalPassword),
                new QueryParameter("ShaPassword",DataType.String,ShaPassword),
                new QueryParameter("Sm3Password",DataType.String,Sm3Password),
                new QueryParameter("FTPType",DataType.String,FTPType),
                new QueryParameter("PortNumber",DataType.String,PortNumber)

            });
            if (dt == null)
            {
                return 0;
            }
            else if (dt.Rows.Count > 0)
            {
                _result = int.Parse(dt.Rows[0][0].ToString());
            }
            return _result;
        }

        public int Add_WRDevice(string houseId, string fsuId, string deviceCategory, string deviceSubCategory, string sysSerialNo, string deviceName, string remark, string logonId, string portUse, string enableTime, string lifeTime)
        {
            if (CommonUtils.IsNoProcedure)
            {
                return DeviceService.Instance.SP_Add_WRDevice(houseId, fsuId, deviceCategory, deviceSubCategory, sysSerialNo, deviceName, remark, logonId);
            }

            int _result = 0;
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Add_WRDevice", new QueryParameter[] {
                new QueryParameter("WRHouseId",DataType.Int,houseId),
                new QueryParameter("DeviceCategory",DataType.String,deviceCategory),
                new QueryParameter("DeviceSubCategory",DataType.String,deviceSubCategory),
				new QueryParameter("SysSerialNo",DataType.String,sysSerialNo),
                new QueryParameter("DeviceName",DataType.String,deviceName),
                new QueryParameter("Remark",DataType.String,remark),
                new QueryParameter("WRFsuId",DataType.Int,fsuId),
                new QueryParameter("LogonId",DataType.String,logonId),
                new QueryParameter("PortUse",DataType.String,portUse),
                new QueryParameter("EnableTime",DataType.String,enableTime),
                new QueryParameter("LifeTime",DataType.String,lifeTime)
            });
            if (dt == null)
            {
                return 0;
            }
            else if (dt.Rows.Count > 0)
            {
                _result = int.Parse(dt.Rows[0][0].ToString());
            }
            return _result;
        }
        public int InsertHouse(string WRStationId, string HouseCode, string HouseName, string Remark, string LogonId)
        {
            int _result = 0;
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Add_WRHouse", new QueryParameter[] {
                new QueryParameter("WRStationId",DataType.Int,WRStationId),
                new QueryParameter("HouseCode",DataType.String,HouseCode),
                new QueryParameter("HouseName",DataType.String,HouseName),
                new QueryParameter("Remark",DataType.String,Remark),
                new QueryParameter("LogonId",DataType.String,LogonId)
            });
            if (dt == null)
            {
                return 0;
            }
            else if (dt.Rows.Count > 0)
            {
                _result = int.Parse(dt.Rows[0][0].ToString());
            }
            return _result;

        }
        public int UpdateHouse(string WRHouseId, string WRStationId, string HouseCode, string HouseName, string Remark)
        {
            int _result = 0;
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Upd_WRHouse", new QueryParameter[] {
                new QueryParameter("WRHouseId",DataType.Int,WRHouseId),
                new QueryParameter("WRStationId",DataType.Int,WRStationId),
                new QueryParameter("HouseCode",DataType.String,HouseCode),
                new QueryParameter("HouseName",DataType.String,HouseName),
                new QueryParameter("Remark",DataType.String,Remark)
            });
            if (dt == null)
            {
                return 0;
            }
            else if (dt.Rows.Count >0)
            { 
                _result = int.Parse(dt.Rows[0][0].ToString());
            }
            return _result;

        }

        public int Update_WRDevice(string deviceId, string houseId, string fsuId, string deviceCategory, string deviceSubCategory, string sysSerialNo, string deviceName, string remark, string logonId, string portUse, string enableTime, string lifeTime)
        {
            int _result = 0;
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Upd_WRDevice", new QueryParameter[] {
                new QueryParameter("WRDeviceId",DataType.Int,deviceId),
                new QueryParameter("WRHouseId",DataType.Int,houseId),
                new QueryParameter("WRFsuId",DataType.Int,fsuId),
                new QueryParameter("DeviceCategory",DataType.String,deviceCategory),
                new QueryParameter("DeviceSubCategory",DataType.String,deviceSubCategory),
				new QueryParameter("SysSerialNo",DataType.String,sysSerialNo),
                new QueryParameter("DeviceName",DataType.String,deviceName),
                new QueryParameter("Remark",DataType.String,remark),
                new QueryParameter("LogonId",DataType.String,logonId),
                new QueryParameter("PortUse",DataType.String,portUse),
                new QueryParameter("EnableTime",DataType.String,enableTime),
                new QueryParameter("LifeTime",DataType.String,lifeTime)
            });
            if (dt == null)
            {
                return 0;
            }
            else if (dt.Rows.Count > 0)
            {
                _result = int.Parse(dt.Rows[0][0].ToString());
            }
            return _result;

        }

        public string Delete_WRDevice(string deviceId, string logonId)
        {
            string _result = "0";
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Del_WRDevice", new QueryParameter[] {
                new QueryParameter("WRDeviceId",DataType.Int,deviceId),
                new QueryParameter("LogonId",DataType.String,logonId)
            });
            if (dt.Rows.Count > 0)
            {
                _result = dt.Rows[0][0].ToString();
            }
            return _result;

        }
        public int UpdateFsu(string WRFsuId, string WRHouseId, string FsuCode, string FsuName, string IPAddress,string UserName, string Password, string FtpUserName, string FtpPassword,string Remark, string ContractNo, string ProjectName, string LogonId, string FTPType, string PortNumber)
        {
            int _result = 0;
            DataTable dt;
            if (CommonUtils.IsNoProcedure) {
                dt =  SP_WRFsuService.Instance.UpdWRFsu(WRFsuId, WRHouseId, FsuCode, FsuName, IPAddress, UserName, Password, FtpUserName, FtpPassword, Remark, ContractNo, ProjectName, LogonId);
            }
            else {
                dt = exesql.ExecuteStoredProcedure("SP_Upd_WRFsu", new QueryParameter[] {
                new QueryParameter("WRFsuId",DataType.Int,WRFsuId),
                new QueryParameter("WRHouseId",DataType.Int,WRHouseId),
                new QueryParameter("FsuCode",DataType.String,FsuCode),
                new QueryParameter("FsuName",DataType.String,FsuName),
                new QueryParameter("IPAddress",DataType.String,IPAddress),
                new QueryParameter("UserName",DataType.String,UserName),
                new QueryParameter("Password",DataType.String,Password),
                new QueryParameter("FtpUserName",DataType.String,FtpUserName),
                new QueryParameter("FtpPassword",DataType.String,FtpPassword),
                new QueryParameter("Remark",DataType.String,Remark),
                new QueryParameter("ContractNo",DataType.String,ContractNo),
                new QueryParameter("ProjectName",DataType.String,ProjectName),
                new QueryParameter("LogonId",DataType.String,LogonId),
                new QueryParameter("FTPType",DataType.String,FTPType),
                new QueryParameter("PortNumber",DataType.String,PortNumber)
                });
            }
            if (dt == null)
            {
                return 0;
            }
            else if (dt.Rows.Count > 0)
            {
                _result = int.Parse(dt.Rows[0][0].ToString());
            }
            return _result;


        }

        public int InsertStation(string StructureId, string Province, string City, string County, string StationCategory, string StationName, string Address, string Remark, string ContractNo, string ProjectName, string loginId)
        {
            int _result = 0;
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Add_WRStation", new QueryParameter[] {
                new QueryParameter("Province",DataType.Int,Province),
                new QueryParameter("City",DataType.Int,City),
                new QueryParameter("County",DataType.Int,County),
                new QueryParameter("StructureId",DataType.Int,StructureId),
                new QueryParameter("StationCategory",DataType.Int,StationCategory),
                new QueryParameter("StationName",DataType.String,StationName),
                new QueryParameter("Address",DataType.String,Address),
                new QueryParameter("Remark",DataType.String,Remark),
                new QueryParameter("ContractNo",DataType.String,ContractNo),
                new QueryParameter("ProjectName",DataType.String,ProjectName),
                new QueryParameter("LogonId",DataType.String,loginId)

            });
            if (dt == null)
            { 
                return 0;
            }
            else if (dt.Rows.Count > 0)
            {
                _result = int.Parse(dt.Rows[0][0].ToString());
            }
            return _result;
        }

        public int UpdateStation(string swsStationId, string StructureId, string Province, string City, string County, string StationCategory, string StationName, string Address, string Remark, string ContractNo, string ProjectName, string loginId)
        {
            int _result = 0;
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Upd_WRStation", new QueryParameter[] {
                new QueryParameter("WRStationId",DataType.Int,swsStationId),
                new QueryParameter("Province",DataType.Int,Province),
                new QueryParameter("City",DataType.Int,City),
                new QueryParameter("County",DataType.Int,County),
                new QueryParameter("StructureId",DataType.Int,StructureId),
                new QueryParameter("StationCategory",DataType.Int,StationCategory),
                new QueryParameter("StationName",DataType.String,StationName),
                new QueryParameter("Address",DataType.String,Address),
                new QueryParameter("Remark",DataType.String,Remark),
                new QueryParameter("ContractNo",DataType.String,ContractNo),
                new QueryParameter("ProjectName",DataType.String,ProjectName),
                new QueryParameter("LogonId",DataType.String,loginId)
            });
            if (dt == null)
            {
                return 0;
            }
            else if (dt.Rows.Count > 0)
            {
                _result = int.Parse(dt.Rows[0][0].ToString());
            }
            return _result;            
        }

        public string DeleteHouse(string WRHouseId, string logonId)
        {
            string _result = "0";
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Del_WRHouse", new QueryParameter[] {
                new QueryParameter("WRHouseId",DataType.Int, WRHouseId),
                new QueryParameter("LogonId",DataType.String, logonId)
            });
            
            if (dt.Rows.Count > 0)
            {
                _result = dt.Rows[0][0].ToString();
            }
            return _result;
        }

        public string DeleteStation(string WRStationId, string logonId)
        {
            if (CommonUtils.IsNoProcedure)
            {
                return StationService.Instance.DeleteStation(WRStationId, logonId);
            }


            string _result = "0";
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Del_Station", new QueryParameter[] {
                new QueryParameter("WRStationId",DataType.Int,WRStationId),
                new QueryParameter("LogonId",DataType.String, logonId)
            });
            if (dt.Rows.Count > 0)
            {
                _result = dt.Rows[0][0].ToString();
            }
            return _result;
        }
        public string ApproveStation(string swsStationId)
        {
            if (CommonUtils.IsNoProcedure)
            {
                int stationId = int.Parse(swsStationId);
                DataTable dt2 = StationService.Instance.SP_ApproveWRStation(stationId);
                if(dt2!=null)
                {
                    return dt2.Rows[0][0].ToString();
                } else
                {
                    return "0";
                }
                
            }
            
            DataTable dt = exesql.ExecuteStoredProcedure("SP_ApproveWRStation", new QueryParameter[] {
                new QueryParameter("WRStationId",DataType.Int,swsStationId)
                });
            if (dt != null)                
            {
                return dt.Rows[0][0].ToString();
            }
            else
            {
                return "0";
            }
        }
        public string ApproveWRHouse(string WRHouseId)
        {
            if (CommonUtils.IsNoProcedure)
            {
                int wrhouseId = int.Parse(WRHouseId);
                DataTable dt2 = HouseService.Instance.SP_ApproveWRHouse(wrhouseId);
                if (dt2 != null)
                {
                    return dt2.Rows[0][0].ToString();
                }
                else
                {
                    return "0";
                }

            }
            DataTable dt = exesql.ExecuteStoredProcedure("SP_ApproveWRHouse", new QueryParameter[] {
                new QueryParameter("WRHouseId",DataType.Int,WRHouseId)
            });
            if (dt != null)
            {
                return dt.Rows[0][0].ToString();
            }
            else
            {
                return "0";
            }
        }


        public string ApproveWRFsu(string WRFsuId)
        {
            DataTable dt;
            if (CommonUtils.IsNoProcedure) {
                dt = SP_WRFsuService.Instance.ApproveWRFsu(WRFsuId);
            }
            else {
                dt = exesql.ExecuteStoredProcedure("SP_ApproveWRFsu", new QueryParameter[] {
                new QueryParameter("WRFsuId",DataType.Int,WRFsuId)
            });
            }
            if (dt != null)
            {
                return dt.Rows[0][0].ToString();
            }
            else
            {
                return "0";
            }
        }


        public string RejectWRStation(string swsStationId, string StationCode, string RejectCause)
        {

            if (CommonUtils.IsNoProcedure)
            {
                return StationService.Instance.RejectWRStation(swsStationId, StationCode, RejectCause);               
            }



            DataTable dt = exesql.ExecuteStoredProcedure("SP_RejectWRStation", new QueryParameter[] {
                new QueryParameter("WRStationId",DataType.Int,swsStationId),
                new QueryParameter("StationCode",DataType.String,StationCode),
                new QueryParameter("RejectCause",DataType.String,RejectCause)
            });
            if (dt != null)
            {
                return dt.Rows[0][0].ToString();
            }
            else
            {
                return "0";
            }
        }
        public string RejectWRHouse(string WRHouseId, string RejectCause)
        {
            if(CommonUtils.IsNoProcedure)
            {
                return HouseService.Instance.RejectWRHouse(WRHouseId, RejectCause);
            }
            DataTable dt = exesql.ExecuteStoredProcedure("SP_RejectWRHouse", new QueryParameter[] {
                new QueryParameter("WRHouseId",DataType.Int,WRHouseId),
                new QueryParameter("RejectCause",DataType.String,RejectCause)
            });
            if (dt != null)
            {
                return dt.Rows[0][0].ToString();
            }
            else
            {
                return "0";
            }
        }

        public string RejectWRFsu(string WRFsuId, string RejectCause)
        {
            DataTable dt = exesql.ExecuteStoredProcedure("SP_RejectWRFsu", new QueryParameter[] {
                new QueryParameter("WRFsuId",DataType.Int,WRFsuId),
                new QueryParameter("RejectCause",DataType.String,RejectCause)
            });
            if (dt != null)
            {
                return dt.Rows[0][0].ToString();
            }
            else
            {
                return "0";
            }
        }
        public string DeleteWRFsu(string WRFsuId, string logonId)
        {
            string _result = "0";
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Del_WRFsu", new QueryParameter[] {
                new QueryParameter("WRFsuId",DataType.Int,WRFsuId),
                new QueryParameter("LogonId",DataType.String,logonId)
            });
            if (dt.Rows.Count > 0)
            {
                _result = dt.Rows[0][0].ToString();
            }
            return _result;
        }

        public DataTable GetWRFsuId(string stationId, string houseId, string logonId)
        {
            if(CommonUtils.IsNoProcedure)
            {
                return FsuService.Instance.SP_Get_WRFsuByStationOrHouse(int.Parse(stationId),int.Parse(houseId), logonId);
               
            }
            DataTable dt = exesql.ExecuteStoredProcedure("SP_Get_WRFsuByStationOrHouse", new QueryParameter[] {
                new QueryParameter("WRStationId",DataType.Int,stationId),
                new QueryParameter("WRHouseId",DataType.Int,houseId),
                new QueryParameter("LogonId",DataType.String,logonId)
            });
            return dt;
        }

        public DataTable GetWRDeviceType()
        {
            DataTable dt = exesql.ExecuteStoredProcedure("SP_GetDeviceCategory", new QueryParameter[] { });
            
            return dt;
        }

        public DataTable GetWRDeviceSubType(string parentType)
        {
            if(CommonUtils.IsNoProcedure)
            {
              return  DeviceService.Instance.SP_GetDeviceSubCategory(parentType);
            }
            DataTable dt = exesql.ExecuteStoredProcedure("SP_GetDeviceSubCategory", new QueryParameter[] {
                    new QueryParameter("DeviceCategory", DataType.String, parentType)
                });

            return dt;
        }

        public bool RemoveCooperationCompany(string userId, string companyId)
        {
            if (exesql.ExecuteStoredProcedureNoQuery("V_PJ_RemoveCooperationCompany", new QueryParameter[] {
                new QueryParameter("CompanyId",DataType.Int,companyId),
                new QueryParameter("UserId",DataType.Int,userId)
            }) > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 获取资产站址信息
        /// </summary>
        /// <returns></returns>
        public DataTable GetAssetStation()
        {
            string sql = @"SELECT EomsStationId as ItemId, EomsSationName as ItemValue
                          FROM TIr_EomsStation
                          ORDER BY EomsSationName";

            DataTable dt = exesql.ExecuteSQL(sql);
            if (dt == null)
                return new DataTable();
            return dt;
        }
    }
}
