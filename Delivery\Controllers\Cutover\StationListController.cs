﻿using BLL;
using DM.TestOrder.Common.DBA;
using DM.TestOrder.DAL;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web.Http;




namespace DM.TestOrder.Controllers
{

    public class StationListController : BaseApiController
    {

        //http://localhost/orderapi/StationList
        public HttpResponseMessage Get(string stationName = "", string stationCode = "")
        {
            if (CommonUtils.IsNoProcedure)
            {
                WOGetStationListService wOGetStationListService = new WOGetStationListService();
                var res = wOGetStationListService.GetStationList(stationName, stationCode);
                return new JsonResult(res);
            }
            else
            {
                var sql = string.Format("WO_GetStationList {0},{1}", SHelper.GetPara(stationName), SHelper.GetPara(stationCode));
                var tb = DBHelper.GetTable(sql);

                var sReturn = JsonConvert.SerializeObject(tb);
                HttpResponseMessage result = new HttpResponseMessage
                {
                    Content = new StringContent(sReturn, Encoding.GetEncoding("UTF-8"), "application/json")
                };
                return result;
            }

        }

    }
}
