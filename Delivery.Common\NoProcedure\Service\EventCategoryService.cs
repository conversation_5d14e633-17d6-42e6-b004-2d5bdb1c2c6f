﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Utils;

namespace Delivery.Common.NoProcedure.Service
{
    public class EventCategoryService
    {
        private static EventCategoryService _instance = null;

        public static EventCategoryService Instance
        {
            get
            {
                if (_instance == null) _instance = new EventCategoryService();
                return _instance;
            }
        }
        public  int UpdateEventCateory(int? EquipmentTemplateId)
        {
            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, false);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                int count = 0;
                try
                {
                    if(EquipmentTemplateId == null)
                    {
                        EquipmentTemplateId = -1;
                    }
                    realParams.Add("EquipmentTemplateId", EquipmentTemplateId);
                    executeHelper.ExecuteScalar("WO_UpdateEventCategory", realParams);
                    DataTable dt = executeHelper.ExecDataTable("WO_SelectUpdateEventCategory", realParams);
                    if(dt != null && dt.Rows.Count > 0)
                    {
                        count = int.Parse(dt.Rows[0]["count"].ToString());
                    }
                    return count;
                }
                catch (Exception ex)
                {
                    Logger.Log(ex);
                    return count;
                   
                }
                finally
                {
                    dbHelper.Connection.Close();
                }
            }
        }


    }
}
