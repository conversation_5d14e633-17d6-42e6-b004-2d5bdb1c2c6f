﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public class TSUStatus
    {
        /// <summary>
        /// CPU使用率
        /// </summary>
        public float? CPUUsage { get; private set; }

        /// <summary>
        /// 内存使用率
        /// </summary>
        public float? MEMUsage { get; private set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="cpuUsage">CPU使用率</param>
        /// <param name="memUsage">内存使用率</param>
        public TSUStatus(float? cpuUsage, float? memUsage)
        {
            CPUUsage = cpuUsage;
            MEMUsage = memUsage;
        }

        public override string ToString()
        {
            return string.Format("{0:#0.0},{1:#0.0}", CPUUsage, MEMUsage);
        }

    }
}
