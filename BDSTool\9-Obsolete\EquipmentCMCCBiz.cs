﻿using BDSTool.DBUtility;
using BDSTool.DBUtility.Common;
using BDSTool.Entity.B;
using BDSTool.Entity.DRHelper;
using Common.Logging.Pro;
using Delivery.Common.NoProcedure.Service;
using Delivery.Common.NoProcedure.Utils;
using ENPC.Kolo.Entity.B_CMCC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace BDSTool.BLL.B
{
    public partial class EquipmentCMCCBiz
    {
        public static bool Exist(string FSUID, string DeviceID) {
            string rtn = null;
            if (CommonUtils.IsNoProcedure)
            {
                rtn = Public_ExecuteSqlService.Instance.NoStore_TBLEquipmentCMCC_GetByFSUIDAndDeviceID(FSUID, DeviceID);
            }
            else
            {
                var sql = string.Format(@"SELECT 1 FROM TBL_EquipmentCMCC WHERE  FSUID={0} AND DeviceID={1}", SHelper.GetPara(FSUID), SHelper.GetPara(DeviceID));

                rtn = DBHelper.ExecuteScalar(sql);
            }

            if (string.IsNullOrEmpty(rtn))
                return false;
            return true;
        }

        public static bool GetAllByMuId(int MonitorUnitId, ref List<TDevConfFromDB> devExList) {
            try {
                DataTable table = null;
                if (CommonUtils.IsNoProcedure)
                {
                    table = Public_ExecuteSqlService.Instance.NoStore_TBLEquipmentCMCC_GetTableByMonitorUnitId(MonitorUnitId);
                }
                else
                {
                    var sql = string.Format(@"SELECT DeviceID, DeviceName, FSUID, StationId, MonitorUnitId, EquipmentId, RoomName, DeviceType, DeviceSubType, Model, Brand, RatedCapacity, Version, BeginRunTime, DevDescribe, ExtendField1, ExtendField2 FROM TBL_EquipmentCMCC WHERE MonitorUnitId ={0}", MonitorUnitId);

                    table = DBHelper.GetTable(sql);
                }
                devExList = new List<TDevConfFromDB>();

                if (table != null) {
                    foreach (DataRow row in table.Rows) {
                        var devEx = DevConfFromDBHelper.FromDataRow(row);
                        devExList.Add(devEx);
                    }
                }
            }
            catch (Exception ex) {
                logger.ErrorFormat("GetAllByMuId();MonitorUnitId={0};Error={1}", MonitorUnitId, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }

        public static bool ZGetDevListByDeviceIds(int MonitorUnitId, List<string> DevIds, ref List<TDevConfFromDB> devExList) {
            try {
                DataTable table = null;
                if (CommonUtils.IsNoProcedure)
                {
                    table = Public_ExecuteSqlService.Instance.NoStore_TBLEquipmentCMCC_GetTableByMonitorUnitId(MonitorUnitId);
                }
                else
                {
                    var sql = string.Format(@"SELECT DeviceID, DeviceName, FSUID, StationId, MonitorUnitId, EquipmentId, RoomName, DeviceType, DeviceSubType, Model, Brand, RatedCapacity, Version, BeginRunTime, DevDescribe, ExtendField1, ExtendField2 FROM TBL_EquipmentCMCC WHERE MonitorUnitId ={0}", MonitorUnitId);
                    table = DBHelper.GetTable(sql);
                }
                var allDev = new List<TDevConfFromDB>();

                if (table != null) {
                    foreach (DataRow row in table.Rows) {
                        var devEx = DevConfFromDBHelper.FromDataRow(row);
                        allDev.Add(devEx);
                    }
                }
                devExList = (from a in allDev
                             join b in DevIds on a.device.DeviceID equals b
                             select a).ToList();
            }
            catch (Exception ex) {
                logger.ErrorFormat("GetDevListByMuId();MonitorUnitId={0};Error={1}", MonitorUnitId, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
            return true;
        }
        public static bool TGetDeviceIDByEquipmentId(int stationId, int equipmentId, ref string FSUID, ref string deviceID) {
            try {
                DataTable table = null;
                if (CommonUtils.IsNoProcedure)
                {
                    table = Public_ExecuteSqlService.Instance.NoStore_TBLEquipmentCMCC_GetTableByStationIdAndEequipmentId(stationId, equipmentId);
                }
                else
                {
                    var sql = string.Format("SELECT FSUID, DeviceID FROM TBL_EquipmentCMCC WHERE StationId={0} and EquipmentId={1}", stationId, equipmentId);
                    table = DBHelper.GetTable(sql);
                }
                if (table.Rows.Count == 0)
                    return false;
                FSUID   =   table.Rows[0][0].ToString();
                deviceID = table.Rows[0][1].ToString();
                return true;
            }
            catch (Exception ex) {
                logger.ErrorFormat("GetEquipmentIdByFSUDeviceID();stationId={0};equipmentId={1};Error={2}", stationId, equipmentId, ex.Message);
                logger.Error(ex.StackTrace);
                return false;
            }
        }

    }
}
