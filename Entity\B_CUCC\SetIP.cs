﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    /// <summary>
    /// 设置为SU分配的采集服务器IP报文
    /// </summary>
    public sealed class SetIP: BMessage
    {
        public string SCIP { get; set; }

        public SetIP(string suId, string surId, string scIp): base()
        {
            MessageType = (int)BMessageType.SET_IP;
            SUId = suId;
            SURId = surId;
            SCIP = scIp;
        }

        public override string Serialize()
        {
            try
            {
                XmlDocument xmldoc = CreateXmlDocument();
                AppendPK_Type(xmldoc, "Request", BMessageType.SET_IP.ToString(), MessageType.ToString());

                XmlElement xe2 = xmldoc.CreateElement("Info");
                XmlElement xe21 = xmldoc.CreateElement("SUId");
                XmlElement xe22 = xmldoc.CreateElement("SURId");
                XmlElement xe23 = xmldoc.CreateElement("SCIP");
                xe21.InnerText = SUId;
                xe22.InnerText = SURId;
                xe23.InnerText = SCIP;
                xe2.AppendChild(xe21);
                xe2.AppendChild(xe22);
                xe2.AppendChild(xe23);
                XmlNode root = xmldoc.SelectSingleNode("Request");
                root.AppendChild(xe2);

                StringXML = xmldoc.InnerXml;
                entityLogger.DebugFormat("SetIP.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmldoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetIP.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetIP.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }

        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1},{2},{3},{4},{5}", MessageId, (BMessageType)MessageType, MessageType, SUId, SURId, SCIP);
            return sb.ToString();
        }


    }
}
