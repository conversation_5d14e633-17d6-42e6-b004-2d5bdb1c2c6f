﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <procedures>	
	  <procedure owner="" name="WO_TestOrder_Query_getMaxRowCount" grant="">
		  <body>
			  <![CDATA[ 
				select ValueInt as MaxRowCount from WO_SysConfig where WO_SysConfig.ConfigId = 3;
			 ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="WO_TestOrder_Query_getRoleIdAndName" grant="">
		  <parameters>
			  <parameter name="UserId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[ 
			  	SELECT m.RoleId,r.<PERSON><PERSON> 
				from TBL_Account a
				INNER JOIN TBL_UserRoleMap m ON a.UserId=m.UserId
				INNER JOIN TBL_UserRole  r ON m.RoleId=r.RoleId
				WHERE a.UserId= @UserId;
			 ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="WO_TestOrder_Query_getResult" grant="">
		  <parameters>
			  <parameter name="UserId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="ReturnCount"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="Cond_StationName"  type="string" direction="Input" size="1024" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="Cond_StationGroup"  type="string" direction="Input" size="1024" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="Cond_StationCode"  type="string" direction="Input" size="1024" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="Cond_FsuVendor"  type="string" direction="Input" size="1024" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="Cond_OrderType"  type="string" direction="Input" size="1024" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="Cond_ApplyTime1"  type="string" direction="Input" size="1024" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="Cond_ApplyTime2"  type="string" direction="Input" size="1024" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="Cond_ApproveTime1"  type="string" direction="Input" size="1024" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="Cond_ApproveTime2"  type="string" direction="Input" size="1024" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="Cond_OrderState"  type="string" direction="Input" size="1024" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="OrderState"  type="int" direction="Input" size="20" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="OrderType"  type="int" direction="Input" size="20" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="RoleName"  type="string" direction="Input" size="1024" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
			  <replace keyName="LimitCount"> Limit @ReturnCount </replace>
		  </replaces>
		  <body>
			  <![CDATA[ 
					SELECT 

					o.OrderId,	-- 内部单号
					o.OrderState, -- 工单状态
					o.MyOrderId,  -- 割接单号 
					case 
						when o.OrderType=0 then '新装入网'
						when o.OrderType=1 then '维护巡检'
					end
					OrderTypeString,  -- 割接类型
					COALESCE(o.FinalGeneralReuslt, '') AS FinalGeneralReuslt,-- 总体测试结果
					ss.StructureName  StationGroup, -- 关联查询，分组
					StationName, -- 站址名称
					site.SiteID StationCode, -- 关联查询，站址编码
					o.ApplyUserFsuVendor,  -- ，FSU厂家
					-- isnull(SubmitTime,'') 
					o.SubmitTime, -- 受理时间
					-- isnull(ApproveTime,'') 
					o.ApproveTime, -- 归档时间
					case 
						when o.OrderState=1 then '入网申请'
						WHEN o.OrderState=2 THEN '专家组审核'  
						WHEN o.OrderState=3 THEN '入网复审'  	
						WHEN o.OrderState=4 THEN '归档'  
					END  AS OrderStateString, -- 申请单状态
	
					case 
						when o.OrderState=1 then a.UserName 
						WHEN o.OrderState=2 THEN '专家组'  
						WHEN o.OrderState=3 THEN '系统管理员'  	
					END  AS Dealer  -- 基于状态分析，当前处理人
					, ApplyTime
					FROM WO_TestOrder o
					inner JOIN TBL_Account a ON o.ApplyUserId=a.UserId
					inner JOIN TBL_Station s ON 
						s.StationId=o.StationId 
					inner JOIN TBL_StationStructure ss ON 
						ss.StructureGroupId=1  
					inner JOIN TBL_StationStructureMap m on 
						ss.StructureId = m.StructureId AND m.StationId = S.StationId	
					LEFT JOIN TBL_StationCMCC site ON  
						site.StationId=s.StationId 
					WHERE o.OrderState>=1 
					and (@Cond_StationName ='' OR StationName LIKE concat('%',@Cond_StationName,'%'))
					and (@Cond_StationGroup='' OR StructureName = @Cond_StationGroup)
					AND (@Cond_StationCode ='' OR SiteID LIKE concat('%',@Cond_StationCode,'%'))
					AND (@Cond_FsuVendor='' OR ApplyUserFsuVendor= @Cond_FsuVendor)
					AND (@OrderType = -1 OR o.OrderType = @OrderType)	
					AND (	ApplyTime >= @Cond_ApplyTime1 :: timestamp 
							AND  ApplyTime <= @Cond_ApplyTime2 :: timestamp  
						)
					AND (	ApproveTime >= @Cond_ApproveTime1 :: timestamp  
							AND  ApproveTime <= @Cond_ApproveTime2 :: timestamp  
						)
					AND (@OrderState = -1 OR o.OrderState = @OrderState)
					AND (@RoleName = '系统管理员' OR ApplyUserFsuVendor = @RoleName)
					And o.OrderState <= 4  
					order by  OrderId desc 
					$[LimitCount];
			 ]]>
		  </body>
	  </procedure>
  </procedures>
</root>
