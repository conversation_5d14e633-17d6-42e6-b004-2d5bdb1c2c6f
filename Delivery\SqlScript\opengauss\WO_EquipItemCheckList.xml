﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <procedures>	
			  
	<procedure owner="" name="WO_EquipItemCheckList_RefreshSignalPrep_insert" grant="">
		<parameters>
			<parameter name="OrderId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		</parameters>
		<body>
			<![CDATA[ 
				INSERT INTO WO_TestOrderEquipItemCheckListSignal
				(OrderCheckId,SignalId, OrderId, EquipmentId, LimitDown, LimitUp)
				SELECT OrderCheckId,SignalId, OrderId, EquipmentId, LimitDown, LimitUp
				FROM (
					SELECT cc.OrderId, cc.OrderCheckId, cc.EquipmentId, s.SignalId, cc.LimitDown, cc.LimitUp
					FROM TBL_Signal s
					INNER JOIN TBL_Equipment e ON e.EquipmentTemplateId=s.EquipmentTemplateId
					INNER JOIN (
						SELECT c.OrderId, c.OrderCheckId, c.BaseTypeName, c.EquipmentId, c.BaseTypeId, c.LimitDown, c.LimitUp
						FROM WO_TestOrderEquipItemCheckList c
						WHERE 
							c.OrderId = @OrderId and c.CheckTypeId = 1 and c.IsPass=0)
						cc ON cc.EquipmentId=e.EquipmentId AND  s.BaseTypeId = cc.BaseTypeId 
				) r
				WHERE NOT EXISTS(
					SELECT 1 FROM WO_TestOrderEquipItemCheckListSignal 
					WHERE WO_TestOrderEquipItemCheckListSignal.OrderCheckId=r.OrderCheckId and WO_TestOrderEquipItemCheckListSignal.SignalId=r.SignalId);
        
		]]>
		</body>
	</procedure>
	<procedure owner="" name="WO_EquipItemCheckList_RefreshSignal_update1" grant="">
		<parameters>
			<parameter name="OrderId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		</parameters>
		<body>
			<![CDATA[ 
				UPDATE WO_TestOrderEquipItemCheckListSignal c
				SET 
				  FloatValue   = a.FloatValue,
				  SamplerTime  = a.SampleTime,
				  BaseTypeId   = a.BaseTypeId
				FROM TBL_ActiveSignal a
				WHERE 
				  c.EquipmentId = a.EquipmentId
				  AND c.SignalId = a.SignalId
				  AND c.OrderId = @OrderId  
				  AND c.IsPass = 0;

	
				update WO_TestOrderEquipItemCheckListSignal a
				set IsPass=1
				where 
					a.OrderId = @OrderId and a.IsPass=0 and 
					(
						(a.FloatValue >=a.LimitDown and a.FloatValue <=a.LimitUp)
						or
						(a.FloatValue is not null and (a.LimitDown is null or a.LimitUp is null)) 
					);	
		]]>
		</body>
	</procedure>
	<procedure owner="" name="WO_EquipItemCheckList_RefreshSignal_updateSignal" grant="">
      <parameters>
        <parameter name="OrderId"  type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 
        UPDATE WO_TestOrderEquipItemCheckListSignal
        SET IsPass = 1
        WHERE OrderCheckId IN (SELECT OrderCheckId FROM WO_TestOrderEquipItemCheckList 
        WHERE CheckTypeId = 1 AND IsPass = 0 AND OrderId = @OrderId);
		]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_EquipItemCheckList_RefreshSignal_updateEquip" grant="">
      <parameters>
        <parameter name="OrderId"  type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[ 	
        UPDATE WO_TestOrderEquipItemCheckList c
		SET 
		  PassNote      = s.FloatValue::TEXT,
		  SamplerValue  = s.FloatValue,
		  SamplerTime   = s.SamplerTime
		FROM WO_TestOrderEquipItemCheckListSignal s
		WHERE 
		  c.OrderCheckId = s.OrderCheckId
		  AND c.CheckTypeId = 1
		  AND c.IsPass = 0
		  AND s.IsPass = 1
		  AND c.OrderId =  @OrderId;
		]]>
      </body>
    </procedure>
	  <procedure owner="" name="WO_EquipItemCheckList_RefreshCmd" grant="">
		  <parameters>
			  <parameter name="OrderId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[ 
			  
				UPDATE WO_TestOrderEquipItemCheckList c
				SET 
				  IsPass      = 1,
				  PassNote    = '控制名称:' || e.ControlName,
				  SamplerTime = e.StartTime
				FROM TBL_ActiveControl e
				WHERE 
				  c.OrderId = @OrderId AND  
				  c.CheckTypeId = 3 AND 
				  c.IsPass = 0 AND 
				  c.HasConfig = 1 AND 
				  e.EquipmentId = c.EquipmentId AND
				  TRUNC(e.BaseTypeId / 1000) = TRUNC(c.BaseTypeId / 1000) AND
				  e.ControlResult = '控制成功';


				UPDATE WO_TestOrderEquipItemCheckList c
				SET 
				  IsPass      = 1,
				  PassNote    = '控制名称:' || e.ControlName,
				  SamplerTime = e.StartTime
				FROM TBL_HistoryControl e
				WHERE 
				  c.OrderId = @OrderId AND  
				  c.CheckTypeId = 3 AND 
				  c.IsPass = 0 AND 
				  c.HasConfig = 1 AND 
				  e.EquipmentId = c.EquipmentId AND
				  TRUNC(e.BaseTypeId / 1000) = TRUNC(c.BaseTypeId / 1000) AND
				  e.ControlResult = '控制成功';
					
			 ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="WO_Debug_CheckItemSignal_select1" grant="">
		  <parameters>
			  <parameter name="OrderId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[ 
				SELECT OrderCheckId, OrderId, CheckTypeId, CheckType, HasConfig, IsPass, PassNote, PassFailReason, EquipmentId, 
				EquipmentName, EquipmentCategoryName, BaseEquipmentId, BaseEquipmentName, 
				BaseTypeId, BaseTypeName, Unit, LimitDown, LimitUp, Note, 
				CheckId, EquipmentLogicClass, LogicClass, StandardName, SignalType, SamplerValue, SamplerTime
				FROM WO_TestOrderEquipItemCheckList
				where WO_TestOrderEquipItemCheckList.OrderCheckId= @OrderId;
			 ]]>
		  </body>
	  </procedure>
    <procedure owner="" name="RefreshEvent_ValueInt" grant="">
		  <body>
			  <![CDATA[ 
				select ValueInt from WO_SysConfig where WO_SysConfig.ConfigId=1;
			 ]]>
		  </body>
	  </procedure>
    <procedure owner="" name="WO_EquipItemCheckList_RefreshActiveEvent" grant="">
      <parameters>
        <parameter name="OrderId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ValueInt"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EventStartTime1" type="datetime" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[
			UPDATE WO_TestOrderEquipItemCheckList c
			SET 
			  IsPass = 1,
			  PassNote = '告警名称:' || e.EventName || ';描述:' || e.Meanings,
			  SamplerTime = e.StartTime
			FROM TBL_ActiveEvent e
			WHERE 
			  c.OrderId = @OrderId
			  AND c.CheckTypeId = 2 
			  AND c.IsPass = 0 
			  AND c.HasConfig = 1
			  AND e.EquipmentId = c.EquipmentId
			  AND e.StartTime >= @EventStartTime1
			  AND TRUNC(e.BaseTypeId / 1000) = TRUNC(c.BaseTypeId / 1000);
			 ]]>
      </body>
    </procedure>
    <procedure owner="" name="WO_EquipItemCheckList_RefreshHistoryEvent" grant="">
      <parameters>
        <parameter name="OrderId"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="ValueInt"  type="int" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
        <parameter name="EventStartTime1" type="datetime" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
      </parameters>
      <body>
        <![CDATA[
			UPDATE WO_TestOrderEquipItemCheckList c
			SET 
			  IsPass = 1,
			  PassNote = '告警名称:' || e.EventName || ';描述:' || e.Meanings,
			  SamplerTime = e.StartTime
			FROM TBL_HistoryEvent e
			WHERE 
			  c.OrderId = @OrderId 
			  AND c.CheckTypeId = 2
			  AND c.IsPass = 0
			  AND c.HasConfig = 1
			  AND e.EquipmentId = c.EquipmentId
			  AND e.StartTime >=  @EventStartTime1
			  AND TRUNC(e.BaseTypeId / 1000) = TRUNC(c.BaseTypeId / 1000);

			 ]]>
      </body>
    </procedure>
  </procedures>
</root>
