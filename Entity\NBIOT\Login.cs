﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    public class Login : BMessage
    {

        public Login() : base()
        {
            MessageType = (int)BMessageType.LOGIN;
        }

        #region SiteWeb配置
        [Newtonsoft.Json.JsonIgnore]
        public int MyMonitoringUnitId { get; set; }

        #endregion     

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get;  set; }

        /// <summary>
        /// 口令（采用MD5进行加密）
        /// </summary>
        public string PassWord { get;  set; }

        /// <summary>
        /// FSU的内网IP
        /// </summary>
        public string FSUIP { get;  set; }

        /// <summary>
        /// FSU的MAC地址，格式定义如下：XX: XX: XX: XX: XX: XX，其中每一个X代表一位16进制数
        /// </summary>
        public string FSUMAC { get;  set; }

        /// <summary>
        /// FSU版本号
        /// </summary>
        public string FSUVER { get;  set; }

        public Login(string userName, string password, string fsuId, string fsuIp, string fsuMac, string fsuVer)
        {
            MessageType = (int)BMessageType.LOGIN;
            UserName = userName;
            PassWord = password;
            FSUID = fsuId;
            FSUIP = fsuIp;
            FSUMAC = fsuMac;
            FSUVER = fsuVer;
        }


        public static Login Deserialize(string json)
        {
            Login info = null;
            try
            {
                info = JsonConvert.DeserializeObject<Login>(json);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("Login.Deserialize:{0}",ex.Message);
                entityLogger.ErrorFormat("Login.Deserialize:{0}",ex.StackTrace);
                info = new Login();
                info.ErrorMsg = ex.Message;
                info.StringXML = json;
            }
            return info;
        }

        public override string ToString()
        {
            try
            {
                return String.Format("{0},{1}:{2},{3},{4},{5},{6},{7}",
                MessageId, (BMessageType)MessageType, UserName.Trim(), PassWord.Trim(), FSUID, FSUIP, FSUMAC, FSUVER);
            }
            catch(Exception ex)
            {
                entityLogger.ErrorFormat("Login.ToString:{0}", ex.Message);
                return null;
            }
        }
    }
}