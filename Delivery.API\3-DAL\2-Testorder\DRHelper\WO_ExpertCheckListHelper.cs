﻿
using DM.TestOrder.Common.DBA;


using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using DM.TestOrder.Common;
using DM.TestOrder.Entity;

namespace DM.TestOrder.DAL.Helper
{
    public class WO_ExpertCheckListHelper
    {
        public static WO_TestOrderExpertCheckList FromDataRow(DataRow row) {
            var e = new WO_TestOrderExpertCheckList();
            try {
                e.OrderCheckId = int.Parse(row["OrderCheckId"].ToString());
                e.OrderId=   int.Parse(row["OrderId"].ToString());
                e.CheckDicId = int.Parse(row["CheckDicId"].ToString());
                e.CheckDicNote = row["CheckDicNote"].ToString();
                e.IsPass = int.Parse(row["IsPass"].ToString());
                e.PassNote = row["PassNote"].ToString();
            }
            catch (Exception ex) {
                MyLogger.ErrorFormat("WO_TestOrderExpertCheckList.FromDataRow();error={0}", ex.Message);
                MyLogger.Error(ex.StackTrace);
                return null;
            }
            return e;
        }
    }
}
