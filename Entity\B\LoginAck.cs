﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B
{
    /// <summary>
    /// SC向FSU注册请求应答报文
    /// </summary>
    public sealed class LoginAck : BMessage
    {
        public EnumRightMode RightLevel { get; private set; }

        public LoginAck(EnumRightMode rightLevel)
            : base()
        {
            MessageType = (int)BMessageType.LoginAck;
            RightLevel = rightLevel;
        }

        public override string Serialize()
        {
            XmlDocument xmldoc = CreateXmlDocument();
            AppendPK_Type(xmldoc, "Response", "LOGIN_ACK", "102");
 
            XmlElement xe2 = xmldoc.CreateElement("Info");
            XmlElement xe21 = xmldoc.CreateElement("RightLevel");
            xe21.InnerText = RightLevel.ToString();
            xe2.AppendChild(xe21);

            XmlNode root = xmldoc.SelectSingleNode("Response"); 
            root.AppendChild(xe2);

            return xmldoc.InnerXml;
        }

        public static LoginAck Deserialize(string raw)
        {
            throw new NotImplementedException();
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}: {2}", MessageId, (BMessageType)MessageType, RightLevel);
        }
    }
}
