﻿using BLL;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using testorder.dal;

using DM.TestOrder.Model;

using DM.TestOrder.Entity;
using DM.TestOrder.DAL;
using DM.TestOrder.Common;


namespace DM.TestOrder.Service.Convert {


    //public static void CreateTestOrder(string ApplyOrderId, string ApplyUserId, string StationId, string StationName) {
    public partial class Mapper {
        #region load-section1-order
        public static FormOrder ToPartOrder(WO_TestOrder orderData) {
            FormOrder partOrder = new FormOrder();

            //继承父元素
            partOrder.ApplyUserId = orderData.ApplyUserId;

            partOrder.OrderId = orderData.OrderId;
            partOrder.NeedUpload = orderData.NeedUpload;
            

            //新装入网=0/维护巡检=1
            partOrder.OrderTypeString = orderData.OrderType == 0 ? "新装入网" : "维护巡检";
            partOrder.StationId = orderData.StationId;

            //

            partOrder.OrderTypeString = orderData.OrderTypeString;
            partOrder.MyOrderId = orderData.MyOrderId;

            var stn = Station.GetStationById(orderData.StationId);
            partOrder.StationName = stn.StationName;

            //var scs = BLL.DataItem.GetStationCategoryDataSource();

            partOrder.CenterName = StationStructure.GetCenterName();
            partOrder.StationCategoryName = DataItem.GetWRStationCategoryName(orderData.StationId);

            partOrder.StationGroupName = StationStructure.GetStationStructrueName(orderData.StationId, 1);
            partOrder.SiteCode = BCodeDal.GetSiteCode(orderData.StationId);
            partOrder.ApplyUserName = orderData.ApplyUserName;
            partOrder.ApplyUserFsuVendor = orderData.ApplyUserFsuVendor;

            partOrder.Latitude = orderData.Latitude;
            partOrder.Longitude = orderData.Longitude;

            partOrder.InstallCompany = orderData.InstallCompany;
            partOrder.InstallClerk = orderData.InstallClerk;

            partOrder.InstallCompanyId = orderData.InstallCompanyId;
            partOrder.InstallClerkId = orderData.InstallClerkId;

            //从数据库加载订单的其他内容
            //BLL.EF_TestOrderDal.SubLoadEquipItem(orderData);
            orderData.EquipItemList = WoTestOrderEquipItemDal.GetAllByOrderId(orderData.OrderId);
            
            orderData.EquipItemList.ForEach(o => {
                partOrder.MyEquips.Add(ToItemEquip(o));
            });
            return partOrder;
        }



        #region art-check
        public static List<FormArtTestItem> ToListOfArtItemCheck(List<WO_TestOrderArtSelfCheckList> listOfCheckItems) {
            var rtn = new List<FormArtTestItem>();
            listOfCheckItems.ForEach(o => {
                rtn.Add(ToFormArtTestItem(o));
            });

            return rtn;
        }
        private static FormArtTestItem ToFormArtTestItem(WO_TestOrderArtSelfCheckList orderData) {

            var model = new FormArtTestItem() {
                OrderCheckId = orderData.OrderCheckId,
                OrderId = orderData.OrderId,
                CheckDicId = orderData.CheckDicId,
                CheckDicNote = orderData.CheckDicNote,
                IsPass = orderData.IsPass,
            };

            return model;
        }
        #endregion

        #region 采集检查    
        public static List<ItemCheck> ToListOfItemCheck(List<WO_TestOrderEquipItemCheckList> enListOfCheckItems) {
            var rtn = new List<ItemCheck>();
            enListOfCheckItems.ForEach(o => {
                rtn.Add(ToItemCheck(o));
            });

            return rtn;
        }
        private static ItemCheck ToItemCheck(WO_TestOrderEquipItemCheckList orderData) {

            var model = new ItemCheck() {

                OrderCheckId = orderData.OrderCheckId,
                EquipmentId = orderData.EquipmentId,
                EquipmentName = orderData.EquipmentName,
                EquipmentCategoryName = orderData.EquipmentCategoryName,
                BaseEquipmentName = orderData.BaseEquipmentName,
                //基类类别	
                CheckType = orderData.CheckType,
                CheckTypeId = orderData.CheckTypeId,
                //基类名称	
                BaseTypeName = orderData.BaseTypeName,
                //标准下限	
                LimitDown = orderData.LimitDown,
                //标准上限	
                LimitUp = orderData.LimitUp,

                EquipmentLogicClass = orderData.EquipmentLogicClass,
                //逻辑分类	：输入告警
                LogicClass = orderData.LogicClass,
                //标准名	
                StandardName = orderData.StandardName,

                //信号类型	：遥信 //todo  逻辑分类	从标准化中获取

                //单位	
                Unit = orderData.Unit,
                //测试结果	
                IsPass = orderData.IsPass,
                IsPassString = orderData.IsPass==1?"通过":"不通过",
                //测试结果描述	
                PassNote = orderData.PassNote,
                //未测试通过原因
                PassFailReason = orderData.PassFailReason,
                SignalType = orderData.SignalType,
            };

            return model;
        }
        #endregion

        private static ItemEquip ToItemEquip(WO_TestOrderEquipItem orderData) {
            var model = new ItemEquip() {
                OrderEquipId = orderData.OrderEquipId,
                EquipmentId = orderData.EquipmentId,
                UriProtocol = orderData.UriProtocol,
                UriImage = orderData.UriImage
            };
            var eq = EquipmentDal.GetOne(orderData.EquipmentId);
            if (eq == null) {
                string s = string.Format("Mapper.ToItemEquip();设备配置不存在; eqId={0}", orderData.EquipmentId);
                MyLogger.Warn(s);
                throw new Exception(s);
            }
            model.EquipmentName = eq.EquipmentName;
            model.EquipmentCategory = DataItem.GetEquipmentCategoryName(eq.EquipmentCategory);
            model.Vendor = eq.Vendor;
            model.EquipmentStyle = eq.EquipmentStyle;
            model.UriImage = orderData.UriImage;
            model.UriProtocol = orderData.UriProtocol;


            return model;
        }        
        #endregion

        public static List<TestOrderFlowInfo> ToListOfTestOrderFlowInfo(List<WO_TestOrderFlow> enFlows) {
            //var enFlows = WO_TestOrderFlowDal.GetByOrderId(entity.OrderId);
            var rtn = new List<TestOrderFlowInfo>();
            foreach(var ef in enFlows){
                var flow = new TestOrderFlowInfo() {
                    OrderFlowId = ef.OrderFlowId,
                    OrderId = ef.OrderId,
                    OldOrderState = ef.OldOrderState,
                    NewOrderState = ef.NewOrderState,
                    StateSetUserId = ef.StateSetUserId,
                    StateSetUserName = ef.StateSetUserName,
                    Decision = ef.Decision,
                    Note = ef.Note,
                    IsApprove = ef.IsApprove,
                    FlowText = ef.FlowText,
                    SaveTime = ef.SaveTime
                };
                rtn.Add(flow);
            }
            return rtn;
        }

    }
}
