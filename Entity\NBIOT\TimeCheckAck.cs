﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 时钟同步相应
    /// </summary>
    public class TimeCheckAck : BMessage
    {
        // 返回结果
        public EnumResult Result { get; set; }

        // 失败的原因
        public string FailureCause { get; set; }

        public TimeCheckAck() : base()
        {
            MessageType = (int)BMessageType.TIME_CHECK_ACK;
        }

        public TimeCheckAck(string fsuId, EnumResult result, string failureCause)
            : base()
        {
            MessageType = (int)BMessageType.TIME_CHECK_ACK;

            FSUID = fsuId;
            Result = result;
            FailureCause = failureCause;
        }

        public static TimeCheckAck Deserialize(string json)
        {
            TimeCheckAck info = null;
            try
            {
                info = JsonConvert.DeserializeObject<TimeCheckAck>(json);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("TimeCheckAck.Deserialize:{0}" , ex.Message);
                entityLogger.ErrorFormat("TimeCheckAck.Deserialize:{0}" , ex.StackTrace);
                info = new TimeCheckAck();
                info.ErrorMsg = ex.Message;
                info.StringXML = json;
            }
            return info;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0},{1},{2},{3}", MessageId, MessageType, Result, FailureCause);
            return sb.ToString();
        }
    }
}