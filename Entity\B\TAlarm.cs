﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B
{
    /// <summary>
    /// 当前告警值
    /// </summary>
    public sealed class TAlarm : BStruct
    {
        public uint SerialNo { get;  set; }

        public string FsuId { get;  set; }
        public string FsuCode { get;  set; }

        public string DeviceId { get;  set; }
        public string DeviceCode { get;  set; }

        public string Id { get;  set; }

        public DateTime AlarmTime { get; set; }

        public EnumState AlarmLevel { get; set; }
        public EnumFlag AlarmFlag { get; set; }
        public string Meanings { get; set; }

        #region SiteWeb配置

        public int MyStationId { get; set; }
        public int MyDeviceId { get; set; }
        public int MyEventId { get; set; }
        public int MyConditionId { get; set; }

        #endregion

        public TAlarm()
        {
        }

        public TAlarm(uint serialNo, string fsuId, string fsuCode, string deviceId, string deviceCode, string id)
        {
            SerialNo = serialNo;
            FsuId = fsuId;
            FsuCode = fsuCode;

            DeviceId = deviceId;
            DeviceCode = deviceCode;
            Id = id;

            Meanings = String.Empty;
        }

        public override string ToString()
        {
            return String.Format(
                "[{0}, {1}.{2}.{3}, {4}, {5}, {6}, {7}, {8}, {9}]",
                SerialNo, FsuId, DeviceId, Id, FsuCode, DeviceCode, AlarmFlag,
                AlarmTime.ToString("yyyy-MM-dd HH:mm:ss"), AlarmLevel, Meanings);
        }
    }
}
