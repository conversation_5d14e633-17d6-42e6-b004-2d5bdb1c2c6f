﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Linq;
using System.Xml;
using System.IO;
using Microsoft.AspNetCore.Mvc;
using Delivery.BSL;
using Microsoft.AspNetCore.Http;
using Delivery.Common;
using ENPC.Kolo.Entity.B_CMCC;
using Microsoft.AspNetCore.Hosting;
using Renci.SshNet;

namespace Delivery.API.CMCC
{
    [Route("CMCC/api/[controller]")]
    public class FileController: BaseController
    {

        private readonly IHostingEnvironment _appEnvironment;

        public FileController(IHostingEnvironment appEnvironment)
        {
            _appEnvironment = appEnvironment;
        }

        [HttpGet("[action]")]
        public string GetAllFsuFtpFile(string stationName, int page, int rows)
        {
            //int page = ParamsofEasyUI.page;
            //int rows = ParamsofEasyUI.rows;
            // int page = int.Parse(HttpContext.Request.Query["page"]);
            // int rows = int.Parse(HttpContext.Request.Query["rows"]);
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            DataTable dt = ccbsl.GetAllFsuFtpFile(LogonId, stationName);
            return Json4EasyUI.onDataGrid(dt, page, rows);
        }

        [HttpPost("[action]")]
        public string LocalImport()//(string fsuCode)
        {
            string result = string.Empty;
            Stream fileStream = HttpContext.Request.Form.Files[0].OpenReadStream();
            string fsuCode = HttpContext.Request.Form["fsuCode"];
            using (StreamReader reader = new StreamReader(fileStream))
            {
                var xmlStr = reader.ReadToEnd();
                XmlDocument xmlDoc = new XmlDocument();
                xmlDoc.LoadXml(xmlStr);
                List<TDevConf> devList = DevConfigFileDeserialize(xmlDoc);
                string errorMsg = string.Empty;
                if (Carrier.BDSTool.BDSHelperCMCC.OnImportFSUConfig(xmlDoc, fsuCode, devList, ref errorMsg))
                {
                    result = "OK";
                }
                else
                {
                    result = "配置数据导入出错！";
                    Logger.Log("配置数据导入异常:" + errorMsg);
                }
            }
            return result;
        }

        [HttpPost("[action]")]
        [Consumes("application/x-www-form-urlencoded")]
        public string FtpImport([FromForm] IFormCollection fc)
        {
            string fsuCode = fc["fsuCode"];
            string result = string.Empty;
            string ftpPath = @"ftp://" + fsuCode;

            string remotePath = "Config";
            string localPath = Path.Join(_appEnvironment.WebRootPath, "download", fsuCode, "Config");

            if (!Directory.Exists(localPath))
            {
                var res = Directory.CreateDirectory(localPath);
            }
            DeliveryDataBsl ccbsl = new DeliveryDataBsl();
            DataTable dt = ccbsl.GetFsuFtpInfo(fsuCode);
            string[] ftpInfo = new string[] { };

            string ftpFileName;
            string localFileFullName = string.Empty;
            if (dt.Rows.Count > 0)
            {
               ftpInfo = dt.Rows[0].ItemArray.Select(p => p.ToString()).ToArray();
            }
            
            if (ftpInfo.Length == 0)
            {
                result = "找不到FSU的FTP信息！";
            }
            else
            {

                // FsuFtpClient ftp = new FsuFtpClient(ftpInfo[0], ftpInfo[1], ftpInfo[2], remotePath, localPath);
                // ftp.Download();
                if (!string.IsNullOrEmpty(ftpInfo[3]) && ftpInfo[3] == "1")
                {
                    //SSHDownloadFile(ftpInfo[0], ftpInfo[1], ftpInfo[2], localPath);
                    SftpClient telnetSFTPClient = new SftpClient(ftpInfo[0], ftpInfo[1], ftpInfo[2]);
                    try
                    {
                        telnetSFTPClient.KeepAliveInterval = TimeSpan.FromSeconds(60);
                        telnetSFTPClient.ConnectionInfo.Timeout = TimeSpan.FromMinutes(180);
                        telnetSFTPClient.OperationTimeout = TimeSpan.FromMinutes(180);
                        telnetSFTPClient.Connect();
                        telnetSFTPClient.ChangeDirectory("../Config/");
                        var _remoteSFTPFiles = telnetSFTPClient.ListDirectory("/Config").Select(entry => entry.FullName).ToList();
                        bool connected = telnetSFTPClient.IsConnected;
                        ///*******SFTP                  
                        foreach (string f1 in _remoteSFTPFiles)
                        {
                            if (f1.EndsWith(".xml"))
                            {
                                string fileName = Path.GetFileName(f1);
                                var destinationFile = Path.Combine(localPath, fileName);
                                using (var file = new FileStream(destinationFile, FileMode.Create))
                                {
                                    telnetSFTPClient.DownloadFile(f1, file);
                                }
                            }
                        }

                        ftpFileName = "devices_" + fsuCode + ".xml";
                        localFileFullName = Path.Combine(localPath, ftpFileName);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error:{ex.Message}");
                    }
                    finally
                    {
                        if (telnetSFTPClient.IsConnected)
                        {
                            telnetSFTPClient.Disconnect();
                        }
                        telnetSFTPClient.Dispose();
                    }
                }
                else
                {
                    FtpHelper ftpHelper = new FtpHelper(ftpInfo[0], remotePath, ftpInfo[1], ftpInfo[2]);
                    string[] files = ftpHelper.GetFileList("*");
                    for (int i = 0; i < files.Length; i++)
                    {
                        ftpHelper.Download(localPath, files[i]);
                    }

                    ftpFileName = "devices_" + fsuCode + ".xml";
                    localFileFullName = Path.Combine(localPath, ftpFileName);
                }
                    //string ftpFileName = "devices_" + fsuCode + ".xml";
                    //string localFileFullName = Path.Combine(localPath, ftpFileName);

                    if (System.IO.File.Exists(localFileFullName))
                    {
                        XmlDocument xmlDoc = new XmlDocument();
                        xmlDoc.Load(localFileFullName);
                        List<TDevConf> devList = DevConfigFileDeserialize(xmlDoc);
                        string errorMsg = string.Empty;
                        if (Carrier.BDSTool.BDSHelperCMCC.OnImportFSUConfig(xmlDoc, fsuCode, devList, ref errorMsg))
                        {
                            result = "OK";
                        }
                        else
                        {
                            result = "配置数据导入出错！";
                            Logger.Log("配置数据导入异常:" + errorMsg);
                        }
                    }
                    else
                    {
                        result = "未能成功下载配置文件！";
                    }
                
            }
            return result;
        }

        public static List<TDevConf> DevConfigFileDeserialize(XmlDocument xmlDoc)
        {
            string errorMsg = string.Empty;
            List<TDevConf> devList = new List<TDevConf>();
            try
            {
                XmlNodeList devNodeList = xmlDoc.SelectNodes("/Devices/Device");
                foreach (XmlNode device in devNodeList)
                {
                    TDevConf devConf = new TDevConf();
                    devConf.DeviceID = device.Attributes["DeviceID"].Value.Trim();
                    devConf.DeviceName = device.Attributes["DeviceName"].Value.Trim();
                    devConf.SiteName = device.Attributes["SiteName"].Value.Trim();
                    devConf.RoomName = device.Attributes["RoomName"].Value.Trim();
                    string strDeviceType = device.Attributes["DeviceType"].Value.Trim();
                    if (string.IsNullOrEmpty(strDeviceType))
                    {
                        devConf.DeviceType = null;
                    }
                    else
                    {
                        devConf.DeviceType = int.Parse(strDeviceType);
                    }
                    string strDeviceSubType = device.Attributes["DeviceSubType"].Value.Trim();
                    if (string.IsNullOrEmpty(strDeviceSubType))
                    {
                        devConf.DeviceSubType = null;
                    }
                    else
                    {
                        devConf.DeviceSubType = int.Parse(strDeviceSubType);
                    }
                    devConf.Model = device.Attributes["Model"].Value.Trim();
                    devConf.Brand = device.Attributes["Brand"].Value.Trim();
                    //额定容量可能为空
                    string strRatedCapacity = device.Attributes["RatedCapacity"].Value.Trim();
                    if (string.IsNullOrEmpty(strRatedCapacity))
                    {
                        devConf.RatedCapacity = null;
                    }
                    else
                    {
                        devConf.RatedCapacity = float.Parse(strRatedCapacity);
                    }
                    devConf.Version = device.Attributes["Version"].Value.Trim();
                    //设备启用时间可能为空
                    string strBeginTime = device.Attributes["BeginRunTime"].Value.Trim();
                    if (string.IsNullOrEmpty(strBeginTime))
                    {
                        devConf.BeginRunTime = null;
                    }
                    else
                    {
                        devConf.BeginRunTime = Convert.ToDateTime(strBeginTime);
                    }
                    devConf.DevDescribe = device.Attributes["DevDescribe"].Value;
                    if (device.ChildNodes.Count > 0)
                    {
                        List<TSignal> signalList = new List<TSignal>();
                        foreach (XmlNode signal in device.ChildNodes[0].ChildNodes)
                        {
                            TSignal tSignal = new TSignal();
                            int iType = int.Parse(signal.Attributes["Type"].Value.Trim());
                            if (iType == 0 || iType == 5)
                            {
                                tSignal.Type = EnumType.ALARM;
                            }
                            else
                            {
                                tSignal.Type = (EnumType)iType;
                            }
                            tSignal.ID = signal.Attributes["ID"].Value.Trim();
                            if (!CheckNumberValue(tSignal.ID))
                            {
                                errorMsg = "TSignal.ID is invalid";
                                break;
                            }
                            tSignal.SignalName = signal.Attributes["SignalName"].Value.Trim();
                            tSignal.AlarmLevel = (EnumState)int.Parse(signal.Attributes["AlarmLevel"].Value.Trim());
                            //上报配置中的float类型值可以为空值
                            string strThreshold = signal.Attributes["Threshold"].Value.Trim();
                            if (string.IsNullOrEmpty(strThreshold))
                            {
                                tSignal.Threshold = null;
                            }
                            else
                            {
                                tSignal.Threshold = float.Parse(strThreshold);
                            }
                            //20170518此处是应对广东移动B接口测试临时办法，新协议去掉了AbsoluteVal,RelativeVal和Describe字段
                            //兼容新旧协议，让配置能够导入再说，免得影响其它协议的测试进度
                            if (signal.Attributes["AbsoluteVal"] == null)
                            {
                                tSignal.AbsoluteVal = 0;
                            }
                            else
                            {
                                string strAbsoluteVal = signal.Attributes["AbsoluteVal"].Value.Trim();
                                if (string.IsNullOrEmpty(strAbsoluteVal))
                                {
                                    tSignal.AbsoluteVal = null;
                                }
                                else
                                {
                                    tSignal.AbsoluteVal = float.Parse(strAbsoluteVal);
                                }
                            }
                            if (signal.Attributes["RelativeVal"] == null)
                            {
                                tSignal.RelativeVal = 0;
                            }
                            else
                            {
                                string strRelativeVal = signal.Attributes["RelativeVal"].Value.Trim();
                                if (string.IsNullOrEmpty(strRelativeVal))
                                {
                                    tSignal.RelativeVal = null;
                                }
                                else
                                {
                                    tSignal.RelativeVal = float.Parse(strRelativeVal);
                                }
                            }
                            if (signal.Attributes["Describe"] == null)
                            {
                                if (tSignal.Type == EnumType.DO)//遥控
                                {
                                    tSignal.Describe = "1&待命";
                                }
                                else if (tSignal.Type == EnumType.DI)//遥信
                                {
                                    tSignal.Describe = "0&0;1&1";
                                }
                                else
                                {
                                    tSignal.Describe = "";
                                }
                            }
                            else
                            {
                                tSignal.Describe = signal.Attributes["Describe"].Value.Trim();
                            }
                            tSignal.NMAlarmID = signal.Attributes["NMAlarmID"].Value.Trim();
                            tSignal.SignalNumber = signal.Attributes["SignalNumber"].Value.Trim();
                            if (!string.IsNullOrEmpty(tSignal.SignalNumber) && !CheckNumberValue(tSignal.SignalNumber))
                            {
                                errorMsg = "TSignal.SignalNumber is invalid";
                                break;
                            }
                            signalList.Add(tSignal);
                        }
                        if (errorMsg != string.Empty)
                        {
                            break;
                        }
                        devConf.Signals = signalList;
                    }
                    devList.Add(devConf);
                }
                if (errorMsg != string.Empty)
                {
                    Logger.Log(string.Format("DevConfigFileDeserialize():{0}", errorMsg));
                    devList.Clear();
                }
                return devList;
            }
            catch (Exception ex)
            {
                Logger.Log(string.Format("DevConfigFileDeserialize():{0}", ex.Message));
                Logger.Log(string.Format("DevConfigFileDeserialize():Stack=[{0}]", ex.StackTrace));
                devList.Clear();
                return devList;
            }
        }

        /// <summary>
        /// 检测输入字符串是否是数值类型
        /// </summary>
        public static bool CheckNumberValue(string strValue)
        {
            bool isNumber = false;
            try
            {
                Convert.ToInt32(strValue);
                isNumber = true;
            }
            catch (Exception ex)
            {
                Logger.Log(string.Format("{0} is not a number:{1}", strValue, ex.Message));
            }
            return isNumber;
        }


    }
}
