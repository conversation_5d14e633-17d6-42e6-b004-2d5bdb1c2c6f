﻿using Delivery.API._5_Common;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;

namespace Delivery.API
{
    [LogonIdRequire]
    [ApiController]
    [Route("api/[controller]")]
    public class BaseController: ControllerBase
    {


        public string Base64Decrypt(string input)
        {
            return Base64Decrypt(input, new UTF8Encoding());
        }

        public string Base64Decrypt(string input, Encoding encode)
        {
            return encode.GetString(Convert.FromBase64String(input));
        }

        public string LogonId
        {
            get
            {
                string userInfo;
                Request.Cookies.TryGetValue("userinfo", out userInfo);
                
                Match match = new Regex("(?<=LogonId=)(.*)(?=&)").Match(userInfo);
                return match.Value;
            }
        }

        public string UserId
        {
            get
            {
                string userInfo;
                Request.Cookies.TryGetValue("userinfo", out userInfo);
                Match match = new Regex("(?<=UserId=)(.*)(?=&?)").Match(userInfo);
                return match.Value;
            }
        }
    }
}
