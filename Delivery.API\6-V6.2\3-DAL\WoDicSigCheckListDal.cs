﻿using DM.TestOrder.Entity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Text;
using testorder.dal;
using DM.TestOrder.DAL.Helper;
using Delivery.DAL;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace DM.TestOrder.DAL {
    public class WoDicSigCheckListDal
    {
        public static DataTable GetAll() {
            DataTable tb;
            if (CommonUtils.IsNoProcedure)
            {
                tb = Public_ExecuteSqlService.Instance.NoStore_WODicSigCheckList_GetAll();
            }
            else
            {
                var sql = "SELECT BaseEquipmentId, BaseEquipmentName, BaseTypeId, BaseTypeName, CheckType, LimitDown, LimitUp, Note, CheckId, ifnull(IsMust,'否') AS IsMust FROM WO_DicSigCheckList";
                //var tb = DBHelper.GetTable(sql);

                tb = new ExecuteSql().ExecuteSQL(sql);
            }
            return tb;
        }


        public static string UpdateOne(int CheckId, string IsMust, float? LimitDown, float? LimitUp) {
            if (CommonUtils.IsNoProcedure)
            {
                var rtn1 = WoCommonService.Instance.WO_DicSigCheckList_Update(CheckId, IsMust, LimitDown, LimitUp);
                return rtn1== null ? null : rtn1.ToString();
            }
            //var sql = string.Format("WO_DicSigCheckList_Update {0},{1},{2},{3}",
            //    CheckId,
            //    SHelper.GetPara(IsMust),
            //    SHelper.GetPara(LimitDown),
            //    SHelper.GetPara(LimitUp));

            //var rtn = DBHelper.ExecuteScalar(sql);
            var rtn = new ExecuteSql().ExecuteStoredProcedureScalar("WO_DicSigCheckList_Update", new QueryParameter[] {
                new QueryParameter("CheckId", DataType.Int, CheckId.ToString()),
                new QueryParameter("IsMust", DataType.String, IsMust)
            });
            return rtn == null ? null : rtn.ToString();
        }          
      }


}

