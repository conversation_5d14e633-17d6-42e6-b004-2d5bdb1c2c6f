﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 信号量的值的结构
    /// </summary>
    public class TSemaphore : BStruct
    {
        /// <summary>
        /// 监控点ID
        /// </summary>
        public string ID { get; private set; }
        /// <summary>
        /// 同设备同类监控点顺序号
        /// </summary>
        public string SignalNumber { get; private set; }
        /// <summary>
        /// 实测值
        /// </summary>
        public float? MeasuredVal { get; private set; }
        /// <summary>
        /// 设置值
        /// </summary>
        public float? SetupVal { get; private set; }
        /// <summary>
        /// 状态
        /// </summary>
        public EnumState Status { get; private set; }
        /// <summary>
        /// 时间
        /// </summary>
        public DateTime? Time { get; private set; }

        /// <summary>
        /// 数据描述（对应于性能数据CSV文件中的数据描述字段）
        /// </summary>
        public string Description { get; set; }


        public int MySignalId { get; set; }

        /// <summary>
        /// 信号量的值的结构
        /// </summary>
        /// <param name="enumType">监控系统的数据种类</param>
        /// <param name="id">监控点ID</param>
        /// <param name="measureValue">实测值</param>
        /// <param name="setupValue">设置值</param>
        /// <param name="enumState">状态</param>
        /// <param name="time">时间，格式yyyy-MM-dd hh:mm:ss（采用24小时的时间制式</param>
        public TSemaphore(EnumType enumType, string id, string signalNumber, float? measureValue, float? setupValue, EnumState enumState, DateTime? time)
        {
            Type = enumType;
            ID = id.PadLeft(6, '0');//20160831根据无锡现场测试亚奥提议修改，不足6位时在前面补0
            SignalNumber = signalNumber;
            MeasuredVal = measureValue;
            SetupVal = setupValue;
            Status = enumState;
            Time = time;
        }

        public TSemaphore(EnumType enumType, string id, string signalNumber, float? measureValue, float? setupValue, EnumState enumState, DateTime? time, string description)
        {
            Type = enumType;
            ID = id.PadLeft(6, '0');//20160831根据无锡现场测试亚奥提议修改，不足6位时在前面补0
            SignalNumber = signalNumber;
            MeasuredVal = measureValue;
            SetupVal = setupValue;
            Status = enumState;
            Time = time;
            Description = description;
        }

        public override string ToString()
        {
            return String.Format("[{0}, {1}, {2}, {3:#0.00}, {4:#0.00}, {5}, {6}]",
                Type, ID, SignalNumber, MeasuredVal, SetupVal, Status, Time.HasValue?Convert.ToDateTime(Time).ToString("yyyy-MM-dd HH:mm:ss"):"");
        }
    }
}
