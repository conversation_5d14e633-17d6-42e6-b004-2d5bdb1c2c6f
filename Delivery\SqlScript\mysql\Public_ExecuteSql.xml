﻿<?xml version="1.0" encoding="utf-8" ?>
<?xml-stylesheet type='text/xsl' href="scriptsql.xsl"?>
<root xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <procedures>
	  <procedure owner="" name="Public_NoStore_TBLEquipmentCMCC_DeleteByFSUIDAndDeviceID_sql" grant="">
		  <parameters>
			  <parameter name="FSUID" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="DeviceID" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					DELETE FROM TBL_EquipmentCMCC WHERE FSUID = @FSUID AND DeviceID = @DeviceID;
            ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_InsertRowIntoTBLEquipmentCMCC_sql" grant="">
		  <parameters>
			  <parameter name="deviceID" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="deviceName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="fSUID" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="stationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="monitorUnitId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="equipmentId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="roomName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="deviceSubType" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="deviceType" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="model" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="brand" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="ratedCapacity" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="version" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="beginRunTime" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="devDescribe" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="extendField1" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="extendField2" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					INSERT INTO TBL_EquipmentCMCC (DeviceID, DeviceName, FSUID, StationId, MonitorUnitId, EquipmentId, RoomName, DeviceType, DeviceSubType, Model, Brand, RatedCapacity, Version, BeginRunTime, DevDescribe, ExtendField1, ExtendField2) VALUES
					(
					@deviceID, @deviceName, @fSUID, @stationId, @monitorUnitId,
					@equipmentId, @roomName, @deviceSubType, @deviceType, @model,
					@brand, @ratedCapacity, @version, @beginRunTime, @devDescribe,
					@extendField1, @extendField2 )
            ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLEquipmentCMCC_GetByFsuId_sql" grant="">
		  <parameters>
			  <parameter name="FSUID" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT DeviceID, DeviceName, FSUID, StationId, MonitorUnitId, EquipmentId, RoomName, DeviceType,  DeviceSubType, Model, Brand, RatedCapacity, Version, BeginRunTime, DevDescribe FROM TBL_EquipmentCMCC  WHERE FSUID = @FSUID;
            ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLEquipmentCMCC_GetAnotherInfoByFsuId_sql" grant="">
		  <parameters>
			  <parameter name="FSUID" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT e.HouseId, e.EquipmentTemplateId, DeviceID, DeviceName, FSUID, b.StationId, b.MonitorUnitId, b.EquipmentId, RoomName, DeviceType, DeviceSubType, Model, Brand, b.RatedCapacity, Version, BeginRunTime, DevDescribe FROM TBL_EquipmentCMCC b INNER JOIN TBL_Equipment e ON b.StationId=e.StationId AND b.EquipmentId=e.EquipmentId WHERE FSUID = @FSUID;
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TSLMonitorUnitCMCC_JudgeExistByFsuId_sql" grant="">
		  <parameters>
			  <parameter name="FSUID" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT 1 FROM TSL_MonitorUnitCMCC WHERE FSUId = @FSUID
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLDataItem_GetByDeviceType_sql" grant="">
		  <parameters>
			  <parameter name="deviceType" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT ItemId FROM TBL_DataItem WHERE EntryId=7 AND ExtendField2 = @deviceType;
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLConfigChangeDefine_GetByEntityName_sql" grant="">
		  <parameters>
			  <parameter name="entityName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT * FROM TBL_ConfigChangeDefine WHERE EntityName = @entityName;
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_InsertRowIntoTBLControl_sql" grant="">
		  <parameters>
			  <parameter name="equipmentTemplateId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="controlId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="controlName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="controlCategory" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="cmdToken" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="baseTypeId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="controlSeverity" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="signalId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="timeOut" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="retry" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="description" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="enable" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="visible" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="displayIndex" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="commandType" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="controlType" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="dataType" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="maxValue" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="minValue" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="defaultValue" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="moduleNo" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					INSERT INTO TBL_Control
                    (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, 
                    BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, 
                    DisplayIndex, CommandType, ControlType, DataType, MaxValue, MinValue, DefaultValue, ModuleNo)
                    values (
					@equipmentTemplateId, @controlId, @controlName, @controlCategory, @cmdToken,
					@baseTypeId, @controlSeverity, @signalId, @timeOut, @retry,
					@description, @enable, @visible, @displayIndex, @commandType,
					@controlType, @dataType, @maxValue, @minValue, @defaultValue, 
					@moduleNo )
            ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_InsertRowIntoTBLEventCondition_sql" grant="">
		  <parameters>
			  <parameter name="eventConditionId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="equipmentTemplateId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="eventId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="startOperation" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="startCompareValue" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="startDelay" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="endOperation" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="endCompareValue" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="endDelay" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="frequency" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="frequencyThreshold" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="meanings" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="equipmentState" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="baseTypeId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="eventSeverity" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="standardName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  </parameters>
		  <body>
			  <![CDATA[
					INSERT INTO TBL_EventCondition
                        (EventConditionId, EquipmentTemplateId, EventId, StartOperation, StartCompareValue, 
                        StartDelay, EndOperation, EndCompareValue, EndDelay, Frequency, 
                        FrequencyThreshold, Meanings, EquipmentState, BaseTypeId, EventSeverity, 
                        StandardName)
                        VALUES ( @eventConditionId, @equipmentTemplateId, @eventId, @startOperation, @startCompareValue, 
								@startDelay, @endOperation, @endCompareValue, @endDelay, @frequency, 
								@frequencyThreshold, @meanings, @equipmentState, @baseTypeId, @eventSeverity, 
								@standardName ）
            ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_InsertRowIntoTBLEvent_sql" grant="">
		  <parameters>
			  <parameter name="equipmentTemplateId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="eventId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="eventName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="startType" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="endType" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="startExpression" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="suppressExpression" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="eventCategory" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="signalId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="enable" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="visible" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="description" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="displayIndex" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="moduleNo" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					INSERT INTO TBL_Event
                    (EquipmentTemplateId, EventId, EventName, StartType, EndType, 
                    StartExpression, SuppressExpression, EventCategory, SignalId, Enable, 
                    Visible, Description, DisplayIndex, ModuleNo)
                    VALUES ( @equipmentTemplateId, @eventId, @eventName, @startType, @endType, 
								@startExpression, @suppressExpression, @eventCategory, @signalId, @enable, 
								@visible, @description, @displayIndex, @moduleNo ）
            ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="GetInsertHeader_TBL_FSU_SendDevConfData_TSignal_sql" grant="">
		  <body>
			  <![CDATA[INSERT INTO TBL_FSU_SendDevConfData_TSignal (LogId, FSUID, DeviceID, Type, ID,SignalName, AlarmLevel, Threshold, AbsoluteVal, RelativeVal, Describe,NMAlarmID,SignalNumber)]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="GetInsertHeader_TBL_FsuTSignalCMCC_sql" grant="">
		  <body>
			  <![CDATA[INSERT INTO TBL_FsuTSignalCMCC (FSUID, DeviceID, ID, SignalNumber, Type, SignalName, AlarmLevel, Threshold, AbsoluteVal, RelativeVal, `Describe`,NMAlarmID,TSignalId,UpdateTime)]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="GetInsertHeader_TBL_Control_sql" grant="">
		  <body>
			  <![CDATA[INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, `MaxValue`, MinValue, DefaultValue, ModuleNo)]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="GetInsertHeader_TBL_ControlMeanings_sql" grant="">
		  <body>
			  <![CDATA[INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings)]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="GetInsertHeader_TBL_EventCondition_sql" grant="">
		  <body>
			  <![CDATA[INSERT INTO TBL_EventCondition (EventConditionId, EquipmentTemplateId, EventId, StartOperation, StartCompareValue, StartDelay, EndOperation, EndCompareValue, EndDelay, Frequency, FrequencyThreshold, Meanings, EquipmentState, BaseTypeId, EventSeverity, StandardName)]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="GetInsertHeader_TBL_Event_sql" grant="">
		  <body>
			  <![CDATA[INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="GetInsertHeader_TBL_Signal_sql" grant="">
		  <body>
			  <![CDATA[INSERT INTO TBL_Signal( EquipmentTemplateId , SignalId , Enable , Visible , Description, SignalName , SignalCategory , SignalType , ChannelNo , ChannelType , Expression , DataType, ShowPrecision , Unit , DisplayIndex , ModuleNo ,AbsValueThreshold, PercentThreshold)]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="GetInsertHeader_TBL_SignalMeanings_sql" grant="">
		  <body>
			  <![CDATA[INSERT INTO TBL_SignalMeanings (EquipmentTemplateId, SignalId, StateValue, Meanings)]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="GetInsertHeader_TBL_SignalProperty_sql" grant="">
		  <body>
			  <![CDATA[INSERT INTO TBL_SignalProperty (EquipmentTemplateId, SignalId, SignalPropertyId)]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_GetUserNameByUserId_sql" grant="">
		  <parameters>
			  <parameter name="userId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT UserName FROM TBL_Account WHERE UserId = @userId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_GetEquipmentCategoryName_sql" grant="">
		  <parameters>
			  <parameter name="CatId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT ItemValue FROM TBL_DataItem WHERE EntryId=7 and ItemId = @CatId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_GetStationCategoryName_sql" grant="">
		  <parameters>
			  <parameter name="CatId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT ItemValue FROM TBL_DataItem WHERE EntryId=71 and ItemId = @CatId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_WOTestOrder_GetOneByOrderId_sql" grant="">
		  <parameters>
			  <parameter name="orderId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select * from WO_TestOrder where OrderId = @orderId and OrderState <= 4
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_WOTestOrder_IsExistByOrderId_sql" grant="">
		  <parameters>
			  <parameter name="orderId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select 1 from WO_TestOrder where OrderId = @orderId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="public_NoStore_GetTestOrderArtSelfCheckList_sql" grant="">
		  <parameters>
			  <parameter name="orderId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select * from WO_TestOrderArtSelfCheckList where OrderId = @orderId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="public_NoStore_GetTestOrderExpertCheckList_sql" grant="">
		  <parameters>
			  <parameter name="orderId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select * from WO_TestOrderExpertCheckList where OrderId = @orderId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="public_NoStore_WOTestOrder_GetAll_sql" grant="">
		  <body>
			  <![CDATA[
					select * from WO_TestOrder
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="public_NoStore_TBLEquipment_GetOneByEquipmentId_sql" grant="">
		  <parameters>
			  <parameter name="equipmentId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select * from TBL_Equipment where EquipmentId = @equipmentId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="public_NoStore_TBLEquipment_GetEquipmentsByStationId_sql" grant="">
		  <parameters>
			  <parameter name="stationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select * from TBL_Equipment where StationId = @stationId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLStation_GetStationById_sql" grant="">
		  <parameters>
			  <parameter name="stationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select * from TBL_Station where stationId = @stationId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_WOInstallClerk_GetAll_sql" grant="">
		  <body>
			  <![CDATA[
					select * from WO_InstallClerk
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_WOInstallCompany_GetAll_sql" grant="">
		  <body>
			  <![CDATA[
					select * from WO_InstallCompany
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_WOInstallCompany_GetByKeyword_sql" grant="">
		  <parameters>
			  <parameter name="companyName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select * from WO_InstallCompany  where CompanyName like @companyName
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_WOTestOrderArtSelfCheckList_GetByOrderId_sql" grant="">
		  <parameters>
			  <parameter name="orderId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select * from WO_TestOrderArtSelfCheckList where OrderId = @orderId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_WOTestOrderEquipItem_JudgeExist_sql" grant="">
		  <parameters>
			  <parameter name="orderId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="equipmentId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select 1 from WO_TestOrderEquipItem where OrderId = @orderId and EquipmentId = @equipmentId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_WOTestOrderEquipItem_GetByOrderIdAndEquipmentId_sql" grant="">
		  <parameters>
			  <parameter name="orderId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="equipmentId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select *  from WO_TestOrderEquipItem where OrderId = @orderId and EquipmentId = @equipmentId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_WOTestOrderEquipItem_GetAllByOrderId_sql" grant="">
		  <parameters>
			  <parameter name="orderId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select *  from WO_TestOrderEquipItem where OrderId = @orderId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_WOTestOrder_UpdateByOrderId_sql" grant="">
		  <parameters>
			  <parameter name="latitude" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="longitude" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="equipItems" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="installCompany" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="installClerk" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="installCompanyId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="installClerkId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="orderId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					update WO_TestOrder set Latitude = @latitude, Longitude = @longitude, EquipItems = @equipItems, 
					InstallCompany = @installCompany, InstallClerk = @installClerk , InstallCompanyId = @installCompanyId,
					InstallClerkId = @installClerkId where OrderId = @orderId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_WOTestOrderEquipItemCheckList_UpdateByOrderCheckId_sql" grant="">
		  <parameters>
			  <parameter name="orderCheckId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="passFailReason" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					update WO_TestOrderEquipItemCheckList set PassFailReason = @passFailReason where OrderCheckId = @orderCheckId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLDataItem_GetAll_sql" grant="">
		  <body>
			  <![CDATA[
					select * from  TBL_DataItem where  EntryId = 71
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_WODicCmdCheckList_GetAll_sql" grant="">
		  <body>
			  <![CDATA[
					SELECT CheckId, BaseEquipmentId, BaseEquipmentName, BaseTypeId, BaseTypeName, CheckType, ifnull(IsMust,'否') IsMust, Note FROM WO_DicCmdCheckList
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_WODicEventCheckList_GetAll_sql" grant="">
		  <body>
			  <![CDATA[
					SELECT BaseEquipmentId, BaseEquipmentName, BaseTypeId, BaseTypeName, CheckType, ifnull(IsMust,'否') IsMust, Note, CheckId FROM WO_DicEventCheckList
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_WODicSigCheckList_GetAll_sql" grant="">
		  <body>
			  <![CDATA[
					SELECT BaseEquipmentId, BaseEquipmentName, BaseTypeId, BaseTypeName, CheckType, LimitDown, LimitUp, Note, CheckId, ifnull(IsMust,'否') AS IsMust FROM WO_DicSigCheckList
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_WRDataItem_GetAll_sql" grant="">
		  <body>
			  <![CDATA[
					select * from WR_DataItem where  EntryId=6 and (ExtendField3='' or ExtendField3 is NULL)
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_DeleteTwoTablesByItemId_sql1" grant="">
		  <parameters>
			  <parameter name="ItemId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					delete from WR_DataItem  where EntryId = 6 and ItemId = @ItemId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_DeleteTwoTablesByItemId_sql2" grant="">
		  <parameters>
			  <parameter name="ItemId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					delete WO_DicStationTypeMap where WR_ItemId= @ItemId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TSLMonitorUnitCMCC_GetByFsuCode_sql" grant="">
		  <parameters>
			  <parameter name="fsuCode" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT a.FSUIP, a.FTPUserName, a.FTPPassWord
                           FROM TSL_MonitorUnitCMCC a 
                           INNER JOIN TBL_Station b ON a.StationId = b.StationId
						   LEFT JOIN TBL_FsuConfig c ON a.FSUID = c.FSUID WHERE a.FSUID = @fsuCode
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_WR_DataItem_GetDataDic_sql1" grant="">
		  <body>
			  <![CDATA[
					SELECT -1 AS ItemId,'请选择' AS ItemValue 
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_WR_DataItem_GetDataDic_sql2" grant="">
		  <parameters>
			  <parameter name="EntryId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="ParentEntryId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="ParentItemId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <replaces>
			  <replace keyName="parentEntryIdStr"> AND ParentEntryId = @ParentEntryId</replace>
			  <replace keyName="parentItemIdStr"> AND ParentItemId = @ParentItemId</replace>
		  </replaces>
		  <body>
			  <![CDATA[
					select ItemId,ItemValue from WR_DataItem where EntryId = @EntryId $[parentEntryIdStr] $[parentItemIdStr]
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_GetDataUiDic_sql1" grant="">
		  <body>
			  <![CDATA[
					SELECT -1 AS ItemId,'请选择' AS ItemValue 
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_GetDataUiDic_sql2" grant="">
		  <parameters>
			  <parameter name="EntryId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select ExtendField2 as ItemId,ItemValue from TBL_DataItem where EntryId = @EntryId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_GetDataUiDic_sql3" grant="">
		  <parameters>
			  <parameter name="EntryId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select ItemId,ItemValue from WR_DataItem where EntryId = @EntryId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_GetDataUiDic_sql4" grant="">
		  <body>
			  <![CDATA[
					SELECT StructureId as ItemId,StructureName as ItemValue FROM TBL_StationStructure where structureType = 1 AND StructureGroupId = 1 AND IsUngroup =0
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_GetDataUiDic_sql5" grant="">
		  <parameters>
			  <parameter name="EntryId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="ItemId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select ItemId,ItemValue from WR_DataItem where EntryId = @EntryId and ItemId = @ItemId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TSLMonitorUnitCMCC_GetMonitorUnitIdByFSUId_sql" grant="">
		  <parameters>
			  <parameter name="FSUID" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT MonitorUnitId FROM TSL_MonitorUnitCMCC WHERE FSUId = @FSUID;
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLSignal_GetMaxIdByEquipmentTemplateId_sql" grant="">
		  <parameters>
			  <parameter name="equipmentTemplateId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select max(DisplayIndex) from TBL_Signal where EquipmentTemplateId = @equipmentTemplateId;
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLEvent_GetMaxIdByEquipmentTemplateId_sql" grant="">
		  <parameters>
			  <parameter name="equipmentTemplateId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select max(DisplayIndex) from TBL_Event where EquipmentTemplateId = @equipmentTemplateId;
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLControl_GetMaxIdByEquipmentTemplateId_sql" grant="">
		  <parameters>
			  <parameter name="equipmentTemplateId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select max(DisplayIndex) from TBL_Control where EquipmentTemplateId = @equipmentTemplateId;
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_sysobjects_GetSql_sql" grant="">
		  <body>
			  <![CDATA[select 1 from sysobjects where name ='WO_BSetEventCategory']]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TABLES_GetSql_sql" grant="">
		  <body>
			  <![CDATA[select  TABLE_NAME  from  INFORMATION_SCHEMA.TABLES  where TABLE_NAME ='WO_BSetEventCategory']]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLHouse_GetByStationId_sql" grant="">
		  <parameters>
			  <parameter name="stationID" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT 1 FROM TBL_House WHERE  StationId = @stationID
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLHouse_GetHouseIdByStationIdAndHouseName_sql" grant="">
		  <parameters>
			  <parameter name="stationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="roomName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT HouseId FROM TBL_House WHERE StationId = @stationId and HouseName = @roomName
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLHouse_GetHouseNameByStationId_sql" grant="">
		  <parameters>
			  <parameter name="stationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT HouseName  FROM TBL_House WHERE StationId = @stationId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TSLMonitorUnitCMCC_GetFsuIdByMonitorUnitId_sql" grant="">
		  <parameters>
			  <parameter name="monitorUnitId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT FSUID FROM TSL_MonitorUnitCMCC WHERE MonitorUnitId = @monitorUnitId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLEquipmentCMCC_GetTableByFsuIdAndDeviceID_sql" grant="">
		  <parameters>
			  <parameter name="fSUID" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="deviceID" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT StationId,EquipmentId FROM TBL_EquipmentCMCC WHERE   FSUID = @fSUID and DeviceID = @deviceID
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TSLMonitorUnitCMCC_GetByFSUId_sql" grant="">
		  <parameters>
			  <parameter name="fSUID" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT StationId FROM TSL_MonitorUnitCMCC WHERE FSUId = @fSUID
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLEquipment_GetByStationIdAndEquipmentId_sql" grant="">
		  <parameters>
			  <parameter name="stationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="equipmentId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT EquipmentTemplateId FROM TBL_Equipment WHERE StationId = @stationId AND EquipmentId = @equipmentId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_UpdateTBLSignal_sql" grant="">
		  <parameters>
			  <parameter name="absoluteVal" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="relativeVal" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="storageInterval" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="equipmentTemplateId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="signalId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					UPDATE TBL_Signal SET AbsValueThreshold = @absoluteVal, PercentThreshold = @relativeVal, StoreInterval = @storageInterval
					WHERE EquipmentTemplateId = @equipmentTemplateId AND SignalId = @signalId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TSLMonitorUnit_GetByMonitorUnitId_sql" grant="">
		  <parameters>
			  <parameter name="monitorUnitId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT MonitorUnitCategory FROM TSL_MonitorUnit WHERE MonitorUnitId = @monitorUnitId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLEquipmentCMCC_GetByFSUIDAndDeviceID_sql" grant="">
		  <parameters>
			  <parameter name="fSUID" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="deviceID" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT 1 FROM TBL_EquipmentCMCC WHERE  FSUID = @fSUID AND DeviceID = @deviceID
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLEquipmentCMCC_GetTableByMonitorUnitId_sql" grant="">
		  <parameters>
			  <parameter name="monitorUnitId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT DeviceID, DeviceName, FSUID, StationId, MonitorUnitId, EquipmentId, RoomName, DeviceType, DeviceSubType, Model, Brand, RatedCapacity, Version, BeginRunTime, DevDescribe, ExtendField1, ExtendField2 FROM TBL_EquipmentCMCC WHERE MonitorUnitId = @monitorUnitId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLEquipmentCMCC_GetTableByStationIdAndEequipmentId_sql" grant="">
		  <parameters>
			  <parameter name="equipmentId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="stationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT FSUID, DeviceID FROM TBL_EquipmentCMCC WHERE StationId = @stationId and EquipmentId = @equipmentId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TSLSampler_GetSql1_sql1" grant="">
		  <body>
			  <![CDATA[SELECT TOP 1 SamplerId FROM TSL_Sampler WHERE  DllPath='KoloBusinessServer.exe']]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TSLSampler_GetSql1_sql2" grant="">
		  <body>
			  <![CDATA[SELECT SamplerId FROM TSL_Sampler WHERE  DllPath='KoloBusinessServer.exe' LIMIT 1]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLEquipmentCMCC_GetTableByFsuId_sql" grant="">
		  <parameters>
			  <parameter name="fsuId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select FSUID, DeviceID, StationId, EquipmentId FROM TBL_EquipmentCMCC WHERE FSUID= @fsuId and StationId is not null and EquipmentId is not null
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_GetTablesFromTwoTables_sql1" grant="">
		  <body>
			  <![CDATA[
					SELECT StandardDicId, Description FROM TBL_StandardDicSig WHERE StandardType=1 AND Description IS NOT NULL  AND Description<>''
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_GetTablesFromTwoTables_sql2" grant="">
		  <body>
			  <![CDATA[
					SELECT StandardDicId, Description FROM TBL_StandardDicControl WHERE StandardType=1 AND Description IS NOT NULL  AND Description<>''
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TSLMonitorUnitCMCC_GetInfosByFsuId_sql" grant="">
		  <parameters>
			  <parameter name="fsuId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select FSUID, FSUIP, CPUUsage, MEMUsage, GetFSUInfoResult, GetFSUFaliureCause, GetFSUTime from TSL_MonitorUnitCMCC Where FSUID = @fsuId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TSLMonitorUnitCMCC_GetFsuIdByStationId_sql" grant="">
		  <parameters>
			  <parameter name="stationId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					SELECT FSUId FROM TSL_MonitorUnitCMCC WHERE StationId = @stationId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLSignal_GetMaxIdByEquipmentTemplateIdAndSId_sql" grant="">
		  <parameters>
			  <parameter name="equipmentTemplateId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="sId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					select DisplayIndex from TBL_Signal where EquipmentTemplateId = @equipmentTemplateId and SignalId= @sId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TSLMonitorUnitCMCC_GetInfo_sql" grant="">
		  <body>
			  <![CDATA[
					select FSUID, FSUIP, CPUUsage, MEMUsage, GetFSUInfoResult, GetFSUFaliureCause, GetFSUTime from TSL_MonitorUnitCMCC
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLControl_GetSql_sql" grant="">
		  <body>
			  <![CDATA[delete from TBL_Control where EquipmentTemplateId={0} and ControlId={1}]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLControlMeanings_GetSql_sql" grant="">
		  <body>
			  <![CDATA[delete from TBL_ControlMeanings where EquipmentTemplateId={0} and ControlId={1}]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLEvent_GetSql_sql" grant="">
		  <body>
			  <![CDATA[delete from TBL_Event where EquipmentTemplateId={0} and EventId={1}]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLEventCondition_GetSql_sql" grant="">
		  <body>
			  <![CDATA[delete from TBL_EventCondition where EquipmentTemplateId={0} and EventId={1}]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLSignal_GetSql_sql" grant="">
		  <body>
			  <![CDATA[delete from TBL_Signal where EquipmentTemplateId={0} and SignalId={1}]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLSignalProperty_GetSql_sql" grant="">
		  <body>
			  <![CDATA[delete from TBL_SignalProperty where EquipmentTemplateId={0} and SignalId={1}]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLSignalMeanings_GetSql_sql" grant="">
		  <body>
			  <![CDATA[delete from TBL_SignalMeanings where EquipmentTemplateId={0} and SignalId={1}]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TSLMonitorUnitCMCC_GetUpdateSql_sql" grant="">
		  <body>
			  <![CDATA[UPDATE TSL_MonitorUnitCMCC SET CPUUsage={0}, MEMUsage={1}, GetFSUInfoResult={2}, GetFSUFaliureCause={3}, GetFSUTime=now() WHERE FSUID={4}]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLEquipmentTemplate_GetInsertSql_sql" grant="">
		  <body>
			  <![CDATA[INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId,EquipmentTemplateName,ParentTemplateId,Memo,ProtocolCode,EquipmentCategory,EquipmentType,Property,Description,EquipmentStyle, Unit,Vendor,EquipmentBaseType, StationCategory) VALUES({0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13})]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLEquipment_GetInsertSql_sql" grant="">
		  <body>
			  <![CDATA[INSERT INTO TBL_Equipment (StationId, EquipmentId, EquipmentName, EquipmentNo, EquipmentStyle, UsedDate, Vendor, EquipmentCategory, EquipmentType, EquipmentClass, Description, EquipmentTemplateId, HouseId, MonitorUnitId, SamplerUnitId, UpdateTime,    SO, EquipmentState,DisplayIndex,ConnectState,InstalledModule) values ({0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13},{14},{15},{16},{17},{18},{19},{20})]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TSLPort_DeleteByPortId_sql" grant="">
		  <parameters>
			  <parameter name="portId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					DELETE FROM TSL_Port WHERE PortId = @portId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLFsuTSignalCMCC_DeleteByFSUID_sql" grant="">
		  <parameters>
			  <parameter name="FsuId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					DELETE FROM TBL_FsuTSignalCMCC WHERE FSUID = @FsuId
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLSignalProperty_InsertSql_sql" grant="">
		  <body>
			  <![CDATA[INSERT INTO TBL_SignalProperty (EquipmentTemplateId, SignalId, SignalPropertyId) VALUES ({0}, {1}, {2})]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLSignalMeanings_InsertSql_sql" grant="">
		  <parameters>
			  <parameter name="equipmentTemplateId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="signalId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="stateValue" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="meanings" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
		  </parameters>
		  <body>
			  <![CDATA[
					INSERT INTO TBL_SignalMeanings
                        (EquipmentTemplateId, SignalId, StateValue, Meanings)
                        VALUES (@equipmentTemplateId, @signalId, @stateValue, @meanings)
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLSignal_InsertSql_sql" grant="">
		  <parameters>
			  <parameter name="equipmentTemplateId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="signalId" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="enable" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="visible" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="description" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="signalName" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="signalCategory" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="signalType" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="channelNo" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="channelType" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="expression" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="dataType" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="showPrecision" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="unit" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="displayIndex" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>
			  <parameter name="moduleNo" type="string" direction="Input" size="255" precision="0" scale="0" param="" nulldefault=""/>

		  </parameters>
		  <body>
			  <![CDATA[
					INSERT TBL_Signal 
                    ( EquipmentTemplateId , SignalId , Enable , Visible , Description , 
                    SignalName , SignalCategory , SignalType , ChannelNo , ChannelType , 
                    Expression , DataType , ShowPrecision , Unit, DisplayIndex , 
                    ModuleNo ) 
                    values(
                        @equipmentTemplateId,@signalId,@enable,@visible,@description,
                        @signalName,@signalCategory,@signalType,@channelNo,@channelType,
                        @expression,@dataType,@showPrecision,@unit,@displayIndex,
                        @moduleNo
                    )
				]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLSignal_UpdateSql_sql" grant="">
		  <body>
			  <![CDATA[UPDATE TBL_Signal SET BaseTypeId= CASE WHEN RIGHT(SignalId,3)='000' THEN floor(m.BaseTypeId/1000)*1000+'001' ELSE floor(m.BaseTypeId/1000)*1000+RIGHT(SignalId,3) END FROM TBL_Signal s, TBL_SignalBaseMap m WHERE s.EquipmentTemplateId={0} AND m.StandardType=1 AND m.StandardDicId/1000=s.SignalId/1000]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLSignalBaseMap_UpdateSql_sql" grant="">
		  <body>
			  <![CDATA[UPDATE TBL_Signal s  INNER JOIN TBL_SignalBaseMap m  on  s.EquipmentTemplateId={0} AND m.StandardType=1 AND m.StandardDicId/1000=s.SignalId/1000 SET s.BaseTypeId=CASE WHEN RIGHT(s.SignalId,3)='000' THEN floor(m.BaseTypeId/1000)*1000+'001' ELSE floor(m.BaseTypeId/1000)*1000+RIGHT(s.SignalId,3) END ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLControl_UpdateSql_sql" grant="">
		  <body>
			  <![CDATA[UPDATE TBL_Control SET  BaseTypeId= CASE WHEN RIGHT(ControlId,3)='000' THEN floor(m.BaseTypeId/1000)*1000+'001' ELSE floor(m.BaseTypeId/1000)*1000+RIGHT(ControlId,3) END FROM TBL_Control s, TBL_CommandBaseMap m WHERE s.EquipmentTemplateId={0} AND m.StandardType=1 AND m.StandardDicId/1000=s.ControlId/1000]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLCommandBaseMap_UpdateSql_sql" grant="">
		  <body>
			  <![CDATA[UPDATE TBL_Control s INNER JOIN TBL_CommandBaseMap m on s.EquipmentTemplateId={0} AND m.StandardType=1 AND m.StandardDicId/1000=s.ControlId/1000 SET  s.BaseTypeId= CASE WHEN RIGHT(s.ControlId,3)='000' THEN floor(m.BaseTypeId/1000)*1000+'001' ELSE floor(m.BaseTypeId/1000)*1000+RIGHT(s.ControlId,3) END ]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLEventCondition_UpdateSql_sql2" grant="">
		  <body>
			  <![CDATA[UPDATE TBL_EventCondition SET BaseTypeId= CASE WHEN RIGHT(EventId,3)='000' THEN floor(m.BaseTypeId/1000)*1000+'001' ELSE floor(m.BaseTypeId/1000)*1000+RIGHT(EventId,3) END FROM TBL_EventCondition ec, TBL_EventBaseMap m WHERE ec.EquipmentTemplateId={0} AND m.StandardType=1 AND m.StandardDicId=ec.EventId/1000]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLEventBaseMap_UpdateSql_sql" grant="">
		  <body>
			  <![CDATA[UPDATE TBL_EventCondition ec INNER JOIN TBL_EventBaseMap m on ec.EquipmentTemplateId={0} AND m.StandardType=1 AND m.StandardDicId=ec.EventId/1000 SET ec.BaseTypeId=CASE WHEN RIGHT(ec.EventId,3)='000' THEN floor(m.BaseTypeId/1000)*1000+'001' ELSE floor(m.BaseTypeId/1000)*1000+RIGHT(ec.EventId,3) END]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLSignal_UpdateSql_UpdateUnitOfSignal_sql" grant="">
		  <body>
			  <![CDATA[UPDATE TBL_Signal SET Unit=d.ExtendFiled1 FROM TBL_Signal s, TBL_StandardDicSig  d WHERE EquipmentTemplateId={0} AND d.StandardType=1  AND d.StandardDicId/1000= s.SignalId/1000]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLSignal_UpdateSql_UpdateUnitOfSignal_mysql_sql" grant="">
		  <body>
			  <![CDATA[UPDATE TBL_Signal s INNER JOIN TBL_StandardDicSig  d ON s.EquipmentTemplateId={0} AND d.StandardType=1  AND d.StandardDicId/1000= s.SignalId/1000 SET s.Unit=d.ExtendFiled1]]>
		  </body>
	  </procedure>
	  <procedure owner="" name="Public_NoStore_TBLControlMeanings_InsertSql_sql" grant="">
		  <body>
			  <![CDATA[INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings) VALUES ({0}, {1}, {2}, '{3}')]]>
		  </body>
	  </procedure>
  </procedures>
</root>
