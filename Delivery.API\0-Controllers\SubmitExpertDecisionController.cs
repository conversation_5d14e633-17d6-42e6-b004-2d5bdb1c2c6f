﻿using Newtonsoft.Json.Linq;


using DM.TestOrder.Model;
using DM.TestOrder.Service.Interface;
using Delivery.API;
using Microsoft.AspNetCore.Mvc;

namespace DM.TestOrder.Controllers {
        
    public class SubmitExpertDecisionController : BaseController{

        [HttpPost]
        public JsonResult POST([FromBody]JObject value) {
            var sectionExpertApprove = Newtonsoft.Json.JsonConvert.DeserializeObject<SectionExpertApprove>(value.ToString());

            var rtnmsg=TestOrderApi.Instance.Section2_SubmitExpertDecision(sectionExpertApprove);

            var rtn = new {
                errormsg = rtnmsg
            };

            //var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(rtn);
            //Debug.WriteLine("输入: "+value.ToString()+"\r\n输出: " + jsonRtn.ToString());

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};
            return new JsonResult(rtn);
        }

    }
}
