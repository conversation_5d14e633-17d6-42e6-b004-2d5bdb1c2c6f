﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using Delivery.BSL;
using Delivery.Common;

namespace Delivery.API.CUCC
{
    [Route("CUCC/api/[controller]")]
    public class DeviceController : BaseController
    {

        [HttpGet("[action]")]
        public string GetDeviceInfo(string startTime, string endTime, string stationId, string fsuId, string deviceCategory, string deviceName, string deviceCode, int page, int rows)
        {
            DeliveryDataBslCucc ccbsl = new DeliveryDataBslCucc();
            DataTable dt = ccbsl.GetDeviceInfo(startTime, endTime, stationId, fsuId, deviceCategory, deviceName, deviceCode, LogonId);
            return Json4EasyUI.onDataGrid(dt, page, rows);
        }


        [HttpPost("[action]")]
        public string AddDevice([FromForm] string houseId, [FromForm] string fsuId, [FromForm] string deviceType, 
            [FromForm] string deviceRId, [FromForm] string deviceName, [FromForm] string remark)
        {
            DeliveryDataBslCucc ccbsl = new DeliveryDataBslCucc();
            return ccbsl.Add_WRDevice(houseId, fsuId, deviceType, deviceName, deviceRId, remark, LogonId).ToString();
        }

        [HttpPost("[action]")]
        public string DeleteDevice([FromForm] string deviceId)
        {
            DeliveryDataBslCucc ccbsl = new DeliveryDataBslCucc();
            return ccbsl.Delete_WRDevice(deviceId, LogonId);
        }

        [HttpPost("[action]")]
        public string UpdateDevice([FromForm] string deviceId, [FromForm] string houseId, [FromForm] string fsuId, 
            [FromForm] string deviceType, [FromForm] string deviceRId, [FromForm] string deviceName, [FromForm] string remark)
        {
            DeliveryDataBslCucc ccbsl = new DeliveryDataBslCucc();
            return ccbsl.Update_WRDevice(deviceId, houseId, fsuId, deviceType, deviceName, deviceRId, remark, LogonId).ToString();
        }

        [HttpGet("[action]")]
        public string GetWRFsuId(string stationId, string houseId)
        {
            DeliveryDataBslCucc ccbsl = new DeliveryDataBslCucc();
            DataTable dt = ccbsl.GetWRFsuId(stationId, houseId, LogonId);
            return Json4EasyUI.onComboBox(dt);
        }

        //[HttpGet("[action]")]
        //public string GetWRDeviceType()
        //{
        //    DeliveryDataBsl ccbsl = new DeliveryDataBsl();
        //    DataTable dt = ccbsl.GetWRDeviceType();
        //    return Json4EasyUI.onComboBox(dt);
        //}

        //[HttpGet("[action]")]
        //public string GetWRDeviceSubType(string parentType)
        //{
        //    DeliveryDataBsl ccbsl = new DeliveryDataBsl();
        //    DataTable dt = ccbsl.GetWRDeviceSubType(parentType);
        //    return Json4EasyUI.onComboBox(dt);
        //}

        /// <summary>
        /// 导出 Excel
        /// </summary>
        /// <returns></returns>              
        [HttpGet("[action]")]
        public IActionResult ExportExcel(string stationId, string WRFsuId, string deviceCode, string strBegin, string strEnd, string deviceName, string deviceType)
        {
            //string LogonId = "admin";
            DataTable dataTable = GetExportData(stationId, WRFsuId, deviceCode, strBegin, strEnd, deviceName, deviceType, LogonId);

            string name = "设备管理";
            byte[] bytes = ExcelHelper.DataTableToExcel(dataTable, null, name);
            return File(bytes, "application/ms-excel", $"{name}_{DateTime.Now:yyyyMMddHHmmssfff}.xlsx");
        }

        private DataTable GetExportData(string stationId, string WRFsuId, string deviceCode, string strBegin, string strEnd, string deviceName, string deviceCategory, string LogonId)
        {
            try
            {
                DeliveryDataBslCucc ccbsl = new DeliveryDataBslCucc();
                DataTable dt = ccbsl.GetDeviceInfo(strBegin, strEnd, stationId, WRFsuId, deviceCategory, deviceName, deviceCode, LogonId);
                string[] cols = new string[]
                {
                        "StationName","FsuCode", "FsuName", "HouseName","DeviceCode","DeviceRId","DeviceTypeName","DeviceName","Remark","ApplyTime","SWUserName"
                };
                DataTable myDt = dt.DefaultView.ToTable(true, cols);
                myDt.Columns["StationName"].ColumnName = "站址名称";
                myDt.Columns["FsuCode"].ColumnName = "SU编码";
                myDt.Columns["FsuName"].ColumnName = "SU名称";
                myDt.Columns["HouseName"].ColumnName = "机房名称";
                myDt.Columns["DeviceCode"].ColumnName = "设备编码";
                myDt.Columns["DeviceRId"].ColumnName = "设备资源ID";
                myDt.Columns["DeviceTypeName"].ColumnName = "设备类型";
                myDt.Columns["DeviceName"].ColumnName = "设备名称";
                myDt.Columns["Remark"].ColumnName = "备注";
                myDt.Columns["ApplyTime"].ColumnName = "申请时间";
                myDt.Columns["SWUserName"].ColumnName = "申请人";

                return myDt;
            }
            catch (Exception ex)
            {
                Logger.Log(ex);
            }
            return null;
        }


    }
}
