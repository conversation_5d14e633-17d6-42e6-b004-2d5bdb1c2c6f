﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 5.6.10.设置FSU的FTP信息应答
    /// </summary>
    public sealed class SetFtpAck:BMessage
    {

        public EnumResult Result { get; private set; }
        public string FailureCause { get; private set; }

        public SetFtpAck(string fsuId, EnumResult result, string failureCause)
            : base()
        {
            MessageType = (int)BMessageType.SET_FTP_ACK;

            FSUID = fsuId;
            Result = result;
            FailureCause = failureCause;
        }

        public SetFtpAck() : base() 
        {
            MessageType = (int)BMessageType.SET_FTP_ACK;
        }

        public static SetFtpAck Deserialize(XmlDocument xmlDoc)
        {
            SetFtpAck setFtpAck = null;
            try
            {
                string fsuId = xmlDoc.SelectSingleNode("/Response/Info/FSUID").InnerText.Trim();
                string resultString = xmlDoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim();
                string failureCause = xmlDoc.SelectSingleNode("/Response/Info/FailureCause").InnerText.Trim();
                EnumResult result;
                if (string.IsNullOrEmpty(resultString))
                {
                    result = EnumResult.FAILURE;
                }
                else
                {
                    result = (EnumResult)int.Parse(resultString);
                }

                setFtpAck = new SetFtpAck(fsuId, result, failureCause);
                entityLogger.DebugFormat("SetFtpAck.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                setFtpAck.StringXML = xmlDoc.InnerXml;
                return setFtpAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetFtpAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetFtpAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + xmlDoc.InnerXml);
                setFtpAck = new SetFtpAck();
                setFtpAck.ErrorMsg = ex.Message;
                setFtpAck.StringXML = xmlDoc.InnerXml;
                return setFtpAck;
            }
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}, {2}: {3},{4}",
                MessageId, (BMessageType)MessageType, FSUID, Result, FailureCause);
        }

    }
}
