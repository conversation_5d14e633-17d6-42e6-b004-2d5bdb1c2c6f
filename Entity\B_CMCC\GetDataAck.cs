﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 监控点数据应答报文
    /// </summary>
    public sealed class GetDataAck: BMessage
    {

        public EnumResult Result { get; private set; }

        public List<Device> Values { get; private set; }

        public string FailureCause { get; private set; }

        #region SiteWeb配置

        public int MyStationId { get; set; }

        #endregion

        public GetDataAck(string fsuId, EnumResult result, List<Device> values, string failureCause):base()
        {
            MessageType = (int)BMessageType.GET_DATA_ACK;

            FSUID = fsuId;
            Result = result;
            Values = values;
            FailureCause = failureCause;
        }

        public GetDataAck() : base() 
        {
            MessageType = (int)BMessageType.GET_DATA_ACK;
        }

        public static GetDataAck Deserialize(XmlDocument xmlDoc)
        {
            GetDataAck getDataAck = null;
            string errorMsg = string.Empty; 
            try 
            { 
                string fsuId = xmlDoc.SelectSingleNode("/Response/Info/FSUID").InnerText.Trim();
                string strResult = xmlDoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim().ToUpper();
                EnumResult result = CheckEnumResult(strResult, ref errorMsg);//字符串类型判别
                //EnumResult result = (EnumResult)int.Parse(xmlDoc.SelectSingleNode("/Response/Info/Result").InnerText);//值类型判别
                string failureCause = xmlDoc.SelectSingleNode("/Response/Info/FailureCause").InnerText.Trim();

                XmlNodeList nodelist = xmlDoc.SelectNodes("/Response/Info/Values/DeviceList/Device");
                List<Device> deviceList = new List<Device>();
                foreach(XmlNode nodeDevice in nodelist)
                {
                    string deviceId = nodeDevice.Attributes["ID"].Value.Trim();
                    List<TSemaphore> semaphoreList = new List<TSemaphore>();
                    foreach(XmlNode node in nodeDevice.ChildNodes)
                    {
                        EnumType type = (EnumType)int.Parse(node.Attributes["Type"].Value.Trim());
                        string id = node.Attributes["ID"].Value.Trim();
                        if(!CheckNumberValue(id))
                        {
                            errorMsg = "TSemaphore.ID is invalid";
                            entityLogger.ErrorFormat("GetDataAck.Deserialize():{0}, abandon record, ID = {1}", errorMsg, id);
                            continue;
                        }
                        string signalNumber = node.Attributes["SignalNumber"].Value.Trim();
                        if (!string.IsNullOrEmpty(signalNumber) && !CheckNumberValue(signalNumber))
                        {
                            errorMsg = "TSemaphore.SignalNumber is invalid";
                            entityLogger.ErrorFormat("GetDataAck.Deserialize():{0}, abandon record, ID = {1}", errorMsg, id);
                            continue;
                        }
                        //底端返回的信号值不能为空，否则认为返回错误
                        string strMeasureVal = node.Attributes["MeasuredVal"].Value.Trim();
                        float? measureVal = null;
                        if(string.IsNullOrEmpty(strMeasureVal))
                        {
                            errorMsg = "TSemaphore.MeasuredVal is NullOrEmtpy";
                            entityLogger.ErrorFormat("GetDataAck.Deserialize():{0}, abandon record, ID = {1}", errorMsg, id);
                            continue;
                        }
                        else
                        {
                            measureVal = float.Parse(strMeasureVal);
                        }
                        //设置值SetupVal可为空
                        string strSetupVal = node.Attributes["SetupVal"].Value.Trim();
                        float? setupVal = null;
                        if (!string.IsNullOrEmpty(strSetupVal) && strSetupVal.ToUpper() != "NULL")
                        {
                            setupVal = float.Parse(strSetupVal);
                        }
                        //新版协议数据状态只有“0 有效数据”“1 无效数据”两态
                        string strStatus = node.Attributes["Status"].Value.Trim();
                        EnumState status = EnumState.NOALARM;
                        if (strStatus == "1")//无效数据
                        {
                            status = EnumState.INVALID;
                        }
                        string strTime = node.Attributes["Time"].Value.Trim();
                        DateTime time = DateTime.Now;
                        if (string.IsNullOrEmpty(strTime))
                        {
                            errorMsg = "TSemaphore.Time is NullOrEmtpy";
                            //数据类型是AO、DO，或者是无效数据时，补填当前时间
                            if(type == EnumType.AO || type == EnumType.DO || status == EnumState.INVALID)
                            {
                                time = DateTime.Now;
                                entityLogger.ErrorFormat("GetDataAck.Deserialize():{0}, fill current time , ID = {1}", errorMsg, id);
                            }
                            //数据类型不是AO、DO，且不是无效数据时，直接丢弃，打印日志
                            //if (type != EnumType.AO && type != EnumType.DO && status != EnumState.INVALID)
                            else
                            {
                                entityLogger.ErrorFormat("GetDataAck.Deserialize():{0}, abandon record, ID = {1}", errorMsg, id);
                                continue;
                            }

                        }
                        else
                        {
                            time = Convert.ToDateTime(strTime);
                        }
                        TSemaphore semaphore = new TSemaphore(type,id, signalNumber,measureVal,setupVal,status,time);
                        semaphoreList.Add(semaphore);
                    }
                    //if(errorMsg != string.Empty)
                    //{
                    //    break;
                    //}
                    Device device = new Device(deviceId, semaphoreList);
                    deviceList.Add(device);
                }
                //if (errorMsg != string.Empty)
                //{
                //    getDataAck = new GetDataAck();
                //    getDataAck.ErrorMsg = errorMsg;
                //    entityLogger.ErrorFormat("GetDataAck.Deserialize():{0}", errorMsg);
                //}
                //else
                //{
                    getDataAck = new GetDataAck(fsuId, result, deviceList, failureCause);
                    //getDataAck.ErrorMsg = errorMsg;//前面所有错误已打日志，此处不再带上错误信息，否则会影响protocolEngine中的逻辑
                //}
                entityLogger.DebugFormat("GetDataAck.Deserialize(),xml:\r\n" + FormatXml(xmlDoc.InnerXml));
                getDataAck.StringXML = xmlDoc.InnerXml;
                return getDataAck;
            }
            catch(Exception ex)
            {
                entityLogger.ErrorFormat("GetDataAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetDataAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                getDataAck = new GetDataAck();
                getDataAck.ErrorMsg = ex.Message;
                getDataAck.StringXML = xmlDoc.InnerXml;
                return getDataAck;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}, {2}:{3},{4}", MessageId, MessageType, FSUID, Result, FailureCause);
            sb.AppendLine();
            if (Values != null)
            {
                foreach (Device device in Values)
                {
                    sb.AppendFormat("{0}\r\n", device.ToString());
                }
            }
            return sb.ToString();
        }

    }

}
