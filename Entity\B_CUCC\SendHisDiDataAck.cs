﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class SendHisDiDataAck : BMessage
    {
        public EnumResult Result { get; set; }
        public SendHisDiDataAck(string suids, string surids, EnumResult result)
            : base()
        {
            MessageType = (int)BMessageType.SEND_HISDIDATA_ACK;
            SUId = suids;
            SURId = surids;
            Result = result;
        }
        public override string Serialize()
        {
            try
            {
                XmlDocument xmlDoc = CreateXmlDocument();
                AppendPK_Type(xmlDoc, "Response", BMessageType.SEND_HISDIDATA_ACK.ToString(), MessageType.ToString());
                XmlElement xel = xmlDoc.CreateElement("Info");
                XmlElement xel1 = xmlDoc.CreateElement("SUId");
                XmlElement xel2 = xmlDoc.CreateElement("SURId");
                XmlElement xel3 = xmlDoc.CreateElement("Result");
                xel1.InnerText = SUId;
                xel2.InnerText = SURId;
                xel3.InnerText = ((int)Result).ToString();
                xel.AppendChild(xel1);
                xel.AppendChild(xel2);
                xel.AppendChild(xel3);
                XmlNode node = xmlDoc.SelectSingleNode("Response");
                node.AppendChild(xel);
                StringXML = xmlDoc.InnerXml;
                entityLogger.DebugFormat("SendHisDiDataAck.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmlDoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SendHisDiDataAck.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SendHisDiDataAck.Serialize() Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }
        public override string ToString()
        {
            return string.Format("{0},{1},{2},({3} and {4}):{5}", MessageId, (BMessageType)MessageType, MessageType, SUId, SURId, Result);
        }
    }
}
