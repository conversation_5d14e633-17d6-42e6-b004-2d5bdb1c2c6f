﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B
{
    public class TSemaphore : BStruct
    {
        public DateTime RecordTime { get; private set; }

        public string Id { get; private set; }
        public float MeasuredValue { get; private set; }
        public float SetupValue { get; private set; }
        public EnumState State { get; private set; }
        public int MySignalId { get; set; }

        public TSemaphore(EnumType type, string id, float measuredValue, float setupValue, EnumState state)
        {
            Type = type;

            Id = id;
            MeasuredValue = measuredValue;
            SetupValue = setupValue;
            State = state;
        }

        public TSemaphore(EnumType type, string id, float measuredValue, float setupValue, EnumState state, DateTime recordTime)
        {
            Type = type;

            Id = id;
            MeasuredValue = measuredValue;
            SetupValue = setupValue;
            State = state;
            RecordTime = recordTime;
        }

        public override string Serialize()
        {
            throw new NotImplementedException();
        }

        public static TSemaphore Deserialize(string raw)
        {
            throw new NotImplementedException();
        }

        public override string ToString()
        {
            return String.Format("{0}: {1}, {2}, {3:#0.00}, {4:#0.00}, {5}", 
                Type, Id, RecordTime.ToString("yyyy-MM-dd HH:mm:ss"), MeasuredValue, SetupValue, State);
        }
    }
}
