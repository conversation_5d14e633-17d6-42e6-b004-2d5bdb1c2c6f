﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 信号量的门限值的结构
    /// </summary>
    public class TThreshold : BStruct
    {
        /// <summary>
        /// 监控点ID
        /// </summary>
        public string ID { get; private set; }
        /// <summary>
        /// 同设备同类监控点顺序号
        /// </summary>
        public string SignalNumber { get; private set; }
        /// <summary>
        /// 门限值
        /// </summary>
        public float? Threshold { get; private set; }
        /// <summary>
        /// 绝对阀值
        /// </summary>
        public float? AbsoluteVal { get; private set; }
        /// <summary>
        /// 百分比阀值
        /// </summary>
        public float? RelativeVal { get; private set; }
        /// <summary>
        /// 状态
        /// </summary>
        public EnumState AlarmLevel { get; private set; }

        /// <summary>
        /// 网管告警编号（告警标准化编号），即《中国移动动环命名及编码指导意见》第五章定义的14位数字的告警ID
        /// </summary>
        public string NMAlarmID { get; private set; }

        #region SiteWeb配置

        public EnumLevel MyAlarmLevel { get; set; }

        #endregion

        /// <summary>
        /// 信号量的门限值的结构
        /// </summary>
        /// <param name="type">数据类型</param>
        /// <param name="id">监控点ID</param>
        /// <param name="threshold">门限值</param>
        /// <param name="alarmLevel">告警等级</param>
        /// <param name="nmAlarmId">网管告警编号</param>
        public TThreshold(EnumType type, string id, string signalNumber, float? threshold, float? absoluteVal, float? relativeVal, EnumState alarmLevel, string nmAlarmId)
        //public TThreshold(EnumType type, string id, string signalNumber, float? threshold, EnumLevel alarmLevel, string nmAlarmId)
        {
            Type = type;
            ID = id;
            SignalNumber = signalNumber;
            Threshold = threshold;
            AbsoluteVal = absoluteVal;
            RelativeVal = relativeVal;
            AlarmLevel = alarmLevel;
            NMAlarmID = nmAlarmId;
            //告警等级转换
            if (AlarmLevel == EnumState.CRITICAL)
            {
                MyAlarmLevel = EnumLevel.CRITICAL;
            }
            else if (AlarmLevel == EnumState.MAJOR)
            {
                MyAlarmLevel = EnumLevel.MAJOR;
            }
            else if (AlarmLevel == EnumState.MINOR)
            {
                MyAlarmLevel = EnumLevel.MINOR;
            }
            else if (AlarmLevel == EnumState.HINT)
            {
                MyAlarmLevel = EnumLevel.HINT;
            }
            else
            {
                MyAlarmLevel = EnumLevel.INVALID;
            }
        }

        /// <summary>
        /// 信号量的门限值的结构(使用EnumLevel)
        /// </summary>
        /// <param name="type">数据类型</param>
        /// <param name="id">监控点ID</param>
        /// <param name="threshold">门限值</param>
        /// <param name="alarmLevel">告警等级</param>
        /// <param name="nmAlarmId">网管告警编号</param>
        public TThreshold(EnumType type, string id, string signalNumber, float? threshold, float? absoluteVal, float? relativeVal, EnumLevel alarmLevel, string nmAlarmId)
      
        {
            Type = type;
            ID = id;
            SignalNumber = signalNumber;
            Threshold = threshold;
            AbsoluteVal = absoluteVal;
            RelativeVal = relativeVal;
            MyAlarmLevel = alarmLevel;
            NMAlarmID = nmAlarmId;
            //告警等级转换
            if (MyAlarmLevel == EnumLevel.CRITICAL)
            {
                AlarmLevel = EnumState.CRITICAL;
            }
            else if (MyAlarmLevel == EnumLevel.MAJOR)
            {
                AlarmLevel = EnumState.MAJOR;
            }
            else if (MyAlarmLevel == EnumLevel.MINOR)
            {
                AlarmLevel = EnumState.MINOR;
            }
            else if (MyAlarmLevel == EnumLevel.HINT)
            {
                AlarmLevel = EnumState.HINT;
            }
        }

        public override string ToString()
        {
            return String.Format("{0}, {1}, {2}, {3:#0.00}, {4}, {5}",
                Type, ID, SignalNumber, Threshold, AlarmLevel, NMAlarmID);
        }
    }
}
