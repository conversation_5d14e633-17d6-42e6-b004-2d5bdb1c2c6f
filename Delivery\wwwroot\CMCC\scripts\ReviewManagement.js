'use strict';

//global
var cookie = getCookie();
var userRole = getUserRole();
var WorkflowAutoId = "";
var BaseUrl

//初始化获取appsettings
function InitappSetting() {
    $.ajax({
        //url: `${WORKFLOW_HANDLER}/DeleteWorkflowDetail`,
        url: `${WORKFLOW_HANDLER}/GetAppSettings`,
        type: 'GET',
        dataType: 'json',
        async: false,
        success: function (result) {
             BaseUrl = result.url
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });
}
//加载站址信息列表
var loadStationManagementData = function () {
    $('#dg_StationReview').datagrid({
        method: 'GET',
        url: `${STATION_HANDLER}/GetStationInfo`,
        title: '站址信息列表',  //rownumbers: true, //行号  striped: true, //隔行变色                
        idField: 'StationCode', //主键
        singleSelect: false,
        checkOnSelect: true,
        ctrlSelect: true,
        checkbox: true,
        pagination: true,
        pageSize: 5,
        pageList: [5],
        queryParams: {
            //action: 'GetStationInfo',
            StructureId: -1,
            StationName: '',
            StationCode: '',
            strBegin: $('#txtApplyTimeq').val(),
            strEnd: $('#txtApplyTimeq2').val(),
            StationType: -1,
            StationStatus: -1
        },
        width: function () { return document.body.clientWidth * 0.9; }, //设置表格宽度为客户端页面的0.9
        height: 'auto',
        //fitColumns: true,
        columns: [[
            { field: 'ck', title: '', checkbox: true, vawidth: 30 },
            { field: 'RowNumber', title: '序号', width: 40, align: 'center' },
            { field: 'StructureName', title: '分组', width: 80, align: 'left' },
            { field: 'StationName', title: '站址名称', width: 100, align: 'left' },
            { field: 'StationCode', title: '站址编码', width: 100, align: 'left' },
            { field: 'StationCategoryName', title: '站址类型', width: 100, align: 'left' },
            { field: 'ContractNo', title: '合同号', width: 100, align: 'left' },
            { field: 'ProjectName', title: '工程名称', width: 100, align: 'left' },
            {
                title: '申请单状态', field: 'StatusName', width: 80, align: 'center', halign: 'center',
                formatter: function (value, row, index) {
                    if (value === '申请退回')
                        return '<font color="red">' + value + '</font>';
                    else if (value === '申请通过')
                        return '<font color="green">' + value + '</font>';
                    else
                        return value;
                }
            },
            { field: 'UserName', title: '申请人', width: 80, align: 'left' },
            { field: 'ApplyTime', title: '申请日期', width: 120, align: 'left' },
            { field: 'ApproveTime', title: '批准日期', width: 120, align: 'left' },
            { field: 'CountyName', title: '行政区', width: 80, align: 'left' },
            { field: 'RejectCause', title: '退回原因', width: 120, align: 'left' }
        ]],
        onClickRow: function (index, row) {
            if (row.StationStatus === '1') {
                $('#btn_Pass').removeAttr('disabled');
                $('#btn_Pass').css({ 'background-color': '' });
                $('#btn_back').removeAttr('disabled');
                $('#btn_back').css({ 'background-color': '' });
            } else if (row.StationStatus === '2' || row.StationStatus === '3') {
                $('#btn_Pass').attr('disabled', 'disabled');
                $('#btn_Pass').css({ 'background-color': 'Silver' });
                $('#btn_back').attr('disabled', 'disabled');
                $('#btn_back').css({ 'background-color': 'Silver' });
            }
        },
        onLoadError: function () {
            alertInfo('查询出错！', 'error');
        }
    });
};
//加载机房信息
var loadHouseData = function () {
    $('#dg_HouseReview').datagrid({
        method: 'GET',
        url: `${HOUSE_HANDLER}/GetHouseInfo`,
        title: '机房信息列表',  //rownumbers: true, //行号  striped: true, //隔行变色                
        idField: 'HouseCode', //主键
        singleSelect: false,
        checkOnSelect: true,
        ctrlSelect: true,
        checkbox: true,
        pagination: true,
        pageSize: 5,
        pageList: [5],
        queryParams: {
            //action: 'GetHouseInfo',
            HouseName: '',
            HouseCode: '',
            strBegin: $('#txtApplyTimeq').val(),
            strEnd: $('#txtApplyTimeq2').val(),
            StructureId: -1,
            Status: -1
        },
        width: function () { return document.body.clientWidth * 0.9; }, //设置表格宽度为客户端页面的0.9
        height: 'auto',
        fitColumns: true,
        columns: [[
            { field: 'ck', title: '', checkbox: true, vawidth: 30 },
            { field: 'RowNumber', title: '序号', width: 10, align: 'center' },
            { field: 'StructureName', title: '分组', width: 15, align: 'left' },
            { field: 'StationName', title: '站址名称', width: 10, align: 'left' },
            { field: 'StationCode', title: '站址编码', width: 10, align: 'left' },
            { field: 'HouseName', title: '机房名称', width: 12, align: 'left' },
            { field: 'HouseCode', title: '机房编码', width: 15, align: 'left' },
            {
                title: '状态', field: 'StatusName', width: 15, align: 'center', halign: 'center',
                formatter: function (value, row, index) {
                    if (value === '申请退回')
                        return '<font color="red">' + value + '</font>';
                    else if (value === '申请通过')
                        return '<font color="green">' + value + '</font>';
                    else
                        return value;
                }
            },
            { field: 'UserName', title: '申请人', width: 10, align: 'left' },
            { field: 'ApplyTime', title: '申请日期', width: 13, align: 'left' },
            { field: 'ApproveTime', title: '批准日期', width: 13, align: 'left' },
            { field: 'RejectCause', title: '退回原因', width: 30, align: 'left' }
        ]],
        onClickRow: function (index, row) {
            if (row.HouseStatus === '1') {
                $('#btn_Pass').removeAttr('disabled');
                $('#btn_Pass').css({ 'background-color': '' });
                $('#btn_back').removeAttr('disabled');
                $('#btn_back').css({ 'background-color': '' });
            }
            else if (row.HouseStatus === '2' || row.HouseStatus === '3') {
                $('#btn_Pass').attr('disabled', 'disabled');
                $('#btn_Pass').css({ 'background-color': 'Silver' });
                $('#btn_back').attr('disabled', 'disabled');
                $('#btn_back').css({ 'background-color': 'Silver' });
            }
        },
        onLoadError: function () {
            alertInfo('查询出错！', 'error');
        }
    });
};
//FSU信息列表查询
var loadFsuManagement = function () {
    $('#dg_FsuReview').datagrid({
        method: 'GET',
        url: `${FSU_HANDLER}/GetFsuInfo`,
        title: 'FSU信息列表',  //rownumbers: true, //行号  striped: true, //隔行变色                
        idField: 'FsuCode', //主键
        singleSelect: false,
        checkOnSelect: true,
        ctrlSelect: true,
        checkbox: true,
        pagination: true,
        pageSize: 5,
        pageList: [5],
        queryParams: {
            //action: 'GetFsuInfo',
            startTime: $('#txtApplyTimeq').val(),
            endTime: $('#txtApplyTimeq2').val(),
            structureId: -1,
            fsuName: '',
            fsuCode: '',
            manufactureId: -1,
            fsuStatus: -1
        },
        width: function () { return document.body.clientWidth * 0.9; }, //设置表格宽度为客户端页面的0.9
        height: 'auto',
        columns: [[
            { field: 'ck', title: '', checkbox: true, vawidth: 30 },
            {
                field: 'btn', title: '', width: 100, align: 'center',
                formatter: function (value, row, index) {
                    if (row.FsuStatus === '3') {
                        return '<button type="button" onclick="exportConfig(\'' + row.FsuCode + '\',\'' + row.FsuName + '\')">导出关联配置</button>';
                    }
                }
            },
            { field: 'RowNumber', title: '序号', width: 40, align: 'center' },
            { field: 'StructureName', title: '分组', width: 80, align: 'left' },
            { field: 'StationName', title: '站址名称', width: 100, align: 'left' },
            { field: 'StationCode', title: '站址编码', width: 100, align: 'left' },
            { field: 'FsuName', title: 'FSU名称', width: 100, align: 'left' },
            { field: 'FsuCode', title: 'FSU编码', width: 100, align: 'left' },
            { field: 'ManufactureName', title: 'FSU厂家', width: 120, align: 'left' },
            { field: 'ContractNo', title: '合同号', width: 120, align: 'left' },
            { field: 'ProjectName', title: '工程名称', width: 120, align: 'left' },
            {
                title: '状态', field: 'StatusName', width: 60, align: 'center', halign: 'center',
                formatter: function (value, row, index) {
                    if (value === '申请退回')
                        return '<font color="red">' + value + '</font>';
                    else if (value === '申请通过')
                        return '<font color="green">' + value + '</font>';
                    else
                        return value;
                }
            },
            { field: 'LoginName', title: '注册用户名', width: 80, align: 'left' },
            { field: 'Password', title: '注册用口令', width: 80, align: 'left' },
            { field: 'FtpUserName', title: 'FTP用户名', width: 80, align: 'left' },
            { field: 'FtpPassword', title: 'FTP用口令', width: 80, align: 'left' },
            { field: 'UserName', title: '申请人', width: 60, align: 'left' },
            { field: 'ApplyTime', title: '申请日期', width: 120, align: 'left' },
            { field: 'ApproveTime', title: '批准日期', width: 120, align: 'left' },
            { field: 'RejectCause', title: '退回原因', width: 150, align: 'left' },
            {
                field: 'Remark', title: '备注', width: 150, align: 'left',
                formatter: function (value, row, index) {
                    return value.replace(/\s*/g, "");
                }
            }
        ]],
        onClickRow: function (index, row) {
            if (row.FsuStatus === '1') {
                $('#btn_Pass').removeAttr('disabled');
                $('#btn_Pass').css({ 'background-color': 'rgb(232, 244, 255)' });
                $('#btn_back').removeAttr('disabled');
                $('#btn_back').css({ 'background-color': 'rgb(232, 244, 255)' });
            }
            else if (row.FsuStatus === '2' || row.FsuStatus === '3') {
                $('#btn_Pass').attr('disabled', 'disabled');
                $('#btn_Pass').css({ 'background-color': 'Silver' });
                $('#btn_back').attr('disabled', 'disabled');
                $('#btn_back').css({ 'background-color': 'Silver' });
            }
        },
        onLoadError: function () {
            alertInfo('查询出错！', 'error');
        }
    });
};


var checkDateValid = function () {
    if (!$('#txtApplyTimeq').val() && $('#txtApplyTimeq2').val()
        || $('#txtApplyTimeq').val() && !$('#txtApplyTimeq2').val()) {
        alertInfo('申请开始日期和申请截止日期必须同时存在！');
        return false;
    }
    if ($('#txtApplyTimeq').datetimebox('isValid') && $('#txtApplyTimeq2').datetimebox('isValid'))
        return true;
    alertInfo('请输入正确的查询条件！');
    return false;
};


var saveWorkFlowInfo = function () {
    var row1 = $('#dg_StationReview').datagrid('getSelections');
    var row2 = $('#dg_HouseReview').datagrid('getSelections');
    var row3 = $('#dg_FsuReview').datagrid('getSelections');
    console.log(cookie)
    $.ajax({
        type: "POST",
        contentType: "application/json; charset=UTF-8",
        url: `${WORKFLOW_HANDLER}/NewWorkflow`,
        data: JSON.stringify({
            WorkflowName: $('#txtWorkflowNameq').val(),
            Remark: $('#TxtRejectReason').val(),
            WorkflowType:1,
            Station: row1,
            House: row2,
            Fsu: row3,
            OrderId:'',
            UserId: cookie['userinfo']['UserId']
        }),
        success: function (data) {
 
            if (data.Success) {
                WorkflowAutoId = data.AutoId
                $('#BackDialog').dialog('close');
                submitWorkFlow(WorkflowAutoId);
            } else {
                alertInfo(data, 'error');
                $(selector).datagrid('selectRow', editingIndexArr[0]);
                $(selector).datagrid('beginEdit', editingIndexArr[0]);
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
            $(selector).datagrid('selectRow', editingIndexArr[0]);
            $(selector).datagrid('beginEdit', editingIndexArr[0]);
        }
    });

    
}

var saveWorkFlowID = function (WorkflowID, WAutoId) {
    console.log(WorkflowID, WAutoId)
    $.ajax({
        type: "POST",
        contentType: "application/x-www-form-urlencoded",
        url: `${WORKFLOW_HANDLER}/saveWorkflowID`,
        data: {
            WorkflowID: WorkflowID,
            AutoId: WAutoId
        },
        success: function (data) {
            console.log(data)
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
            $(selector).datagrid('selectRow', editingIndexArr[0]);
            $(selector).datagrid('beginEdit', editingIndexArr[0]);
        }
    });
}

var submitWorkFlow = function (WorkflowAutoId) {
    var row1 = $('#dg_StationReview').datagrid('getSelections');
    var row2 = $('#dg_HouseReview').datagrid('getSelections');
    var row3 = $('#dg_FsuReview').datagrid('getSelections');

    var stationData = []
    var houseData = []
    var fsuData = []
    
    console.log(row1, row2, row3)
    if (row1.length>0) {
        row1.forEach((value, key) => {
            var data = {
                "title": "站址名称",
                "value": value.StationName,
                "field": "StationName"
            }
            stationData.push(data)
        })
    }
    if (row2.length > 0) {
        row2.forEach((value, key) => {
            var data = {
                "title": "机房名称",
                "value": value.HouseName,
                "field": "HouseName"
            }
            houseData.push(data)
        })
    }
    if (row3.length > 0) {
        row3.forEach((value, key) => {
            var data = {
                "title": "FSU名称",
                "value": value.FsuName,
                "field": "FsuName"
            }
            var data1 = {
                "title": "FSU编码",
                "value": value.FsuCode,
                "field": "FsuCode"
            }
            var data2 = {
                "title": "FSUIP",
                "value": value.IPAddress,
                "field": "IPAddress"
            }
            fsuData.push(data,data1,data2)
        })
    }
  
    var param = [
        {
            "businessId": "",
            "businessName": "站址申请",
            "businessData": stationData
        },
        {
            "businessId": "",
            "businessName": "机房申请",
            "businessData": houseData
        },
        {
            "businessId": "",
            "businessName": "FSU申请",
            "businessData": fsuData
        }
    ]
    var paras = {
        startUserId: cookie['userinfo']['UserId'],
        processDefinitionKey: "w_leave",
        processInstanceName: $('#txtWorkflowNameq').val(),
        variables: { data: param }
    };
    $.ajax({
        type: 'POST',
        url: `${BaseUrl}/siteweb-flow/api/processInstance/create`,
        contentType: 'application/json;charset=UTF-8',
        data: JSON.stringify(paras),
        async: true,
        success: function (data) {
            console.log(data, WorkflowAutoId)
            if (data.code === 200) {
                alertInfo('提交成功');
                saveWorkFlowID(data.data, WorkflowAutoId);
                $('#BackDialog').dialog('close');
            }else
                alertInfo(data.msg + ': 保存失败！');
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alertInfo(errorThrown ? decodeURI(errorThrown) : '出现错误！', 'error');
        }
    });

}

var init = function () {
    InitappSetting()
    $('#txtApplyTimeq').datetimebox('setValue', new Date(new Date().setDate(1)).format('yyyy-MM-dd') + ' 00:00:00');
    $('#txtApplyTimeq2').datetimebox('setValue', new Date(new Date().getTime() + 1000 * 60 * 60).format('yyyy-MM-dd hh:mm:ss'));

    //审核
    $('#btn_submitReview').click(function () {
        var row1 = $('#dg_StationReview').datagrid('getSelections');
        var row2 = $('#dg_HouseReview').datagrid('getSelections');
        var row3 = $('#dg_FsuReview').datagrid('getSelections');
        if (row1.length > 0 || row2.length > 0 || row3.length > 0) {
           
        }
        else {
            $.messager.alert("提示", "请选择需要审核的信息!", 'error');
            return;
        }

        $('#BackDialog').dialog('open');
    });

    //提交审核
    $('#btn_AppBackRe').click(function () {
        $.messager.confirm('提示框', '你确认要提交吗?', function (r) {
             if (r) {
                saveWorkFlowInfo();
                
              }
         });
    });


    //查询
    $('#btn_seach').click(function () {
        var beginDate = $('#txtApplyTimeq').val();
        var endDate = $('#txtApplyTimeq2').val();
        ///日期校验
        var result;
        if (beginDate.length > 0) {
            result = beginDate.match(/^(\d+)-(\d{1,2})-(\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2})$/);
            if (!result) {
                alertInfo('录入申请开始日期格式不正确！');
                return false;
            }
        }
        if (endDate.length > 0) {
            result = endDate.match(/^(\d+)-(\d{1,2})-(\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2})$/);
            if (!result) {
                alertInfo('录入申请截止日期格式不正确！');
                return false;
            }
            var d1 = new Date(beginDate.replace(/\-/g, '\/'));
            var d2 = new Date(endDate.replace(/\-/g, '\/'));
            if (beginDate && endDate && d1 > d2) {
                alertInfo('申请开始日期不能大于申请截止日期！');
                return false;
            }
        }
        if (!checkDateValid())
            return false;

        loadStationManagementData();
        loadHouseData();
        loadFsuManagement();
    });

}

$(function () {
    init();
    
    loadStationManagementData();
    loadHouseData();
    loadFsuManagement();

    $('div.mask').remove();
});