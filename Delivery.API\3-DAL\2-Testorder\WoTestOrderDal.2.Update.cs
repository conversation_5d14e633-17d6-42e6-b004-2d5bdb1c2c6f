﻿using DM.TestOrder.Entity;
using log4net;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using DM.TestOrder.DAL.Helper;
using testorder.dal;
using Delivery.DAL;
using Delivery.Common.NoProcedure.Utils;
using Delivery.Common.NoProcedure.Service;

namespace DM.TestOrder.DAL {

    public partial class WoTestOrderDal {
        private static readonly ILog loggerSql = LogManager.GetLogger("wosql");

        public static void Update(WO_TestOrder newOrder) {
            loggerSql.Info("");
            loggerSql.InfoFormat("工单修改;工单号={0},创建人={1}", newOrder.OrderId, newOrder.ApplyUserName);

            newOrder.EquipItems = string.Join(",", newOrder.EquipItemList.Select(o => o.EquipmentId).ToList());

            var oldOrder = WoTestOrderDal.GetOne(newOrder.OrderId);
            if (oldOrder == null) {
                throw new Exception(string.Format("工单修改失败,无此工单(id={0}).", newOrder.OrderId));
            }

            var oldState = oldOrder.OrderState;
            var newState = newOrder.OrderState;
            if (newState == 1 && oldState > 1) {
                throw new Exception("禁止修改已提交工单");
            }
            if (newState == 2 && oldState != 1) {
                throw new Exception("重复提交工单");
            }


            if(oldOrder.Latitude != newOrder.Latitude|| oldOrder.Longitude != newOrder.Longitude
            || oldOrder.InstallCompany != newOrder.InstallCompany|| oldOrder.InstallClerk != newOrder.InstallClerk
            || oldOrder.EquipItems != newOrder.EquipItems)
                UpdateEntityOfTestOrder(newOrder);

            //================================================================================
            //update equip
            var oldEqList = WoTestOrderEquipItemDal.GetAllByOrderId(oldOrder.OrderId);
            foreach (var e in newOrder.EquipItemList) {
                var oldEq = oldEqList.FirstOrDefault(o=> o.EquipmentId == e.EquipmentId);
                if (oldEq == null){
                    WoTestOrderEquipItemDal.DBAddOne(newOrder.OrderId, e.EquipmentId, e.UriProtocol, e.UriImage);
                }                    
            }

            //del equip
            foreach (var oldEq in oldEqList) {
                var bExist = newOrder.EquipItemList.Any(o => o.EquipmentId == oldEq.EquipmentId);
                if (!bExist)
                    WoTestOrderEquipItemDal.DBDeleteOne(oldOrder.OrderId, oldEq.EquipmentId);
            }
            //================================================================================
            //update 测试项目,仅检查用户可修改的1个字段
            var oldTestItems = WoTestOrderEquipItemCheckListDal.GetByOrderId(newOrder.OrderId);
            if (newOrder.TestOrderEquipItemCheckList != null) {

                foreach (var newTestItem in newOrder.TestOrderEquipItemCheckList) {
                    var oldC = oldTestItems.FirstOrDefault(o => o.OrderCheckId == newTestItem.OrderCheckId);
                    if (oldC != null && (oldC.PassFailReason != newTestItem.PassFailReason)) {
                        WoTestOrderEquipItemCheckListDal.DBUpdate(newTestItem.OrderCheckId, newTestItem.PassFailReason);
                    }
                }
            }
            //================================================================================
            //update 工艺测试项目,仅更新是否通过
            var oldArtItems = WoTestOrderArtSelfCheckListDal.GetByOrderId(newOrder.OrderId);
            if (newOrder.TestOrderArtSelfCheckList != null) {

                foreach (var newArtItem in newOrder.TestOrderArtSelfCheckList) {
                    var oldArtItem = oldArtItems.FirstOrDefault(o => o.OrderCheckId == newArtItem.OrderCheckId);
                    if (oldArtItem != null && (oldArtItem.IsPass != newArtItem.IsPass)) {
                        WoTestOrderArtSelfCheckListDal.DBUpdate(newArtItem);
                    }
                }
            }

            loggerSql.Info("");   
        }

        private static void UpdateEntityOfTestOrder(WO_TestOrder oneOrder) {
            //var sql = string.Format(@"update WO_TestOrder set Latitude={0},Longitude={1},EquipItems={2}, InstallCompany={3},InstallClerk={4},InstallCompanyId={5},InstallClerkId={6} where OrderId={7}",
            //     SHelper.GetPara(oneOrder.Latitude), SHelper.GetPara(oneOrder.Longitude), SHelper.GetPara(oneOrder.EquipItems),
            //     SHelper.GetPara(oneOrder.InstallCompany), SHelper.GetPara(oneOrder.InstallClerk), oneOrder.InstallCompanyId, oneOrder.InstallClerkId,
            //     oneOrder.OrderId);

            //DBHelper.ExecuteNonQuery(sql);
            if (CommonUtils.IsNoProcedure)
            {
                Public_ExecuteSqlService.Instance.NoStore_WOTestOrder_UpdateByOrderId(oneOrder.Latitude.ToString(),
                    oneOrder.Longitude.ToString(), oneOrder.EquipItems, oneOrder.InstallCompany, oneOrder.InstallClerk,
                    oneOrder.InstallCompanyId.ToString(), oneOrder.InstallClerkId.ToString(), oneOrder.OrderId.ToString());
            }
            else
            {
                var sql = "update WO_TestOrder set Latitude=@Latitude,Longitude=@Longitude,EquipItems=@EquipItems, InstallCompany=@InstallCompany,InstallClerk=@InstallClerk},InstallCompanyId=@InstallCompanyId,InstallClerkId=@InstallClerkId where OrderId=@OrderId";
                new ExecuteSql().ExecuteSQLNoQuery(sql, new QueryParameter[]
                {
                new QueryParameter("Latitude", DataType.Number, oneOrder.Latitude.ToString()),
                new QueryParameter("Longitude", DataType.Number, oneOrder.Longitude.ToString()),
                new QueryParameter("EquipItems", DataType.String, oneOrder.EquipItems),
                new QueryParameter("InstallCompany", DataType.String, oneOrder.InstallCompany),
                new QueryParameter("InstallClerk", DataType.String, oneOrder.InstallClerk),
                new QueryParameter("InstallCompanyId", DataType.Number, oneOrder.InstallCompanyId.ToString()),
                new QueryParameter("InstallClerkId", DataType.Number, oneOrder.InstallClerkId.ToString()),
                new QueryParameter("OrderId", DataType.Number, oneOrder.OrderId.ToString()),
                });
            }
        }

        public static void UpdateLocker(int orderid, int userid, int islock) {
            //var sql = string.Format(@"WO_TestOrder_Lock {0}, {1}, {2}",orderid, userid, islock);
            //var s = DBHelper.ExecuteScalar(sql);
            if (CommonUtils.IsNoProcedure)
            {
                WO_TestOrderDalService.Instance.TestOrderLock(orderid, userid, islock);
            }
            else
            {
                new ExecuteSql().ExecuteStoredProcedureNoQuery("WO_TestOrder_Lock", new QueryParameter[] {
                new QueryParameter("OrderId", DataType.Int, orderid.ToString()),
                new QueryParameter("UserId", DataType.Int, userid.ToString()),
                new QueryParameter("IsLock", DataType.Int, islock.ToString())
                });
            }
        }
    }
}