﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class SendHisAlarm : BMessage
    {
        public List<TAlarm> LTAlarm { get; set; }
        public SendHisAlarm(List<TAlarm> alarmList) : base()
        {
            MessageType = (int)BMessageType.SEND_HISALARM;
            LTAlarm = alarmList;
        }
        public SendHisAlarm():base()
        {
            MessageType = (int)BMessageType.SEND_HISALARM;
        }
        public static SendHisAlarm Deserialize(XmlDocument xmlDoc)
        {
            SendHisAlarm sendHisAlarm = null;

            string errMsg = string.Empty;
            string suId = string.Empty;
            string surId = string.Empty;
            try
            {
                suId = xmlDoc.SelectSingleNode("/Request/Info/SUId").InnerText.Trim();
                surId = xmlDoc.SelectSingleNode("/Request/Info/SURId").InnerText.Trim();
                XmlNodeList alarmNodeList = xmlDoc.SelectNodes("/Request/Info/Values/TAlarmList/TAlarm");
                List<TAlarm> list = new List<TAlarm>();
                foreach (XmlNode node in alarmNodeList)
                {
                    string serialNo = node.SelectSingleNode("SerialNo").InnerText.Trim();
                    //suId = node.SelectSingleNode("SUId").InnerText.Trim();
                    //surId = node.SelectSingleNode("SURId").InnerText.Trim();
                    string deviceId = node.SelectSingleNode("DeviceId").InnerText.Trim();
                    string deviceRId = node.SelectSingleNode("DeviceRId").InnerText.Trim();
                    //string id = node.SelectSingleNode("Id").InnerText.Trim();
                    XmlNode signalNode = node.SelectSingleNode("Signal");
                    string id = signalNode.Attributes["Id"].Value.Trim();
                    string strAlarmTime = node.SelectSingleNode("AlarmTime").InnerText.Trim();
                    DateTime? alarmTime;
                    if (string.IsNullOrEmpty(strAlarmTime))
                    {
                        errMsg = "SendHisAlarm TAlarm.AlarmTime is NullOrEmtpy";
                        break;
                    }
                    else
                    {
                        alarmTime = Convert.ToDateTime(strAlarmTime);
                    }
                    string alarmDesc = node.SelectSingleNode("AlarmDesc").InnerText.Trim();
                    string strTriggerVal = node.SelectSingleNode("TriggerVal").InnerText.Trim();
                    float? triggerVal;
                    if (string.IsNullOrEmpty(strTriggerVal))
                    {
                        errMsg = "SendHisAlarm TAlarm.TriggerVal is NullOrEmtpy";
                        break;
                    }
                    else
                    {
                        strTriggerVal = strTriggerVal.ToUpper();
                        strTriggerVal = strTriggerVal.Replace('V', ' ').Replace('A', ' ').Replace('℃', ' ').Replace('%', ' ').Replace('R', ' ').Replace('H', ' ');
                        strTriggerVal = strTriggerVal.Replace('K', ' ').Replace('B', ' ').Replace('Y', ' ').Replace('T', ' ').Replace('E', ' ').Replace('D', ' ');
                        strTriggerVal = strTriggerVal.Replace('D', ' ').Replace('M', ' ').Trim();
                        triggerVal = float.Parse(strTriggerVal);
                    }
                    string strAlarmFlag = node.SelectSingleNode("AlarmFlag").InnerText.Trim();
                    EnumFlag enumFlag;
                    if (string.IsNullOrEmpty(strAlarmFlag))
                    {
                        errMsg = "SendHisAlarm TAlarm.AlarmFlag is NullOrEmtpy";
                        break;
                    }
                    else
                    {
                        if (strAlarmFlag.ToUpper() == "BEGIN")
                        {
                            enumFlag = EnumFlag.BEGIN;
                        }
                        else if(strAlarmFlag.ToUpper() == "END")
                        {
                            enumFlag = EnumFlag.END;
                        }
                        else
                        { 
                            enumFlag = (EnumFlag)int.Parse(strAlarmFlag);
                        }
                    }
                    TAlarm alarm = new TAlarm(serialNo, suId, surId, id, deviceId, deviceRId, alarmTime, alarmDesc, triggerVal, enumFlag);
                    list.Add(alarm);
                }
                if (errMsg != string.Empty)
                {
                    entityLogger.ErrorFormat("SendHisAlarm.Deserialize():{0}", errMsg);
                    sendHisAlarm = GetErrorEntity(suId, surId, errMsg);
                    sendHisAlarm.ErrorMsg = errMsg;
                }
                else
                {
                    sendHisAlarm = new SendHisAlarm();
                    sendHisAlarm.LTAlarm = list;
                }
                sendHisAlarm.SUId = suId;
                sendHisAlarm.SURId = surId;
                entityLogger.DebugFormat("SendHisAlarm.Deserialize(),xml:\r\n{0}", FormatXml(xmlDoc.InnerXml));
                sendHisAlarm.StringXML = xmlDoc.InnerXml;
                return sendHisAlarm;
            }
            catch(Exception ex)
            {
                entityLogger.ErrorFormat("SendHisAlarm.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SendHisAlarm.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                sendHisAlarm = GetErrorEntity(suId, surId, ex.Message);
                sendHisAlarm.StringXML = xmlDoc.InnerXml;
                return sendHisAlarm;
            }
        }

        /// <summary>
        /// 反序列化异常时，构造带错误信息的实体
        /// </summary>
        /// <param name="errorMsg">解析错误信息</param>
        /// <returns></returns>
        private static SendHisAlarm GetErrorEntity(string suId, string surId, string errorMsg)
        {
            SendHisAlarm errorEntity = new SendHisAlarm();
            SendHisAlarmAck ackEntity = new SendHisAlarmAck(suId, surId, EnumResult.FAILURE);
            ackEntity.ErrorMsg = errorMsg;
            errorEntity.ErrorMsg = ackEntity.Serialize();
            return errorEntity;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            if (LTAlarm.Count > 0)
            {
                foreach (TAlarm ta in LTAlarm)
                {
                    sb.AppendFormat("{0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10}, {11}, {12}\n",
                         MessageId, (BMessageType)MessageType, MessageType, ta.SerialNo, ta.SUID, ta.SURId,
                        ta.ID, ta.DeviceID, ta.DeviceRId, ta.AlarmTime, ta.AlarmDesc,ta.TriggerVal, ta.AlarmFlag.ToString());
                }
            }
            else
            {
                sb.AppendFormat("{0},{1},{2},is null", MessageId, (BMessageType)MessageType, MessageType);
            }
            return sb.ToString();
        }
    }
}
