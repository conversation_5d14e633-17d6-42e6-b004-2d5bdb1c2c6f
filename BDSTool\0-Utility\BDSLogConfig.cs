﻿using Common.Logging.Pro;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace BDSTool.Utility
{
    public class BDSLogConfig
    {
        private static readonly log4net.ILog loggerConfig = LogManager.GetLogger("BDSToolConfig");

        
        public static void Info(string message) {
            loggerConfig.Info(message);
        }

        public static void InfoFormat(string message, params object[] args) {
            loggerConfig.InfoFormat(message, args);
        }

        public static void Warn(string message) {
            loggerConfig.Warn(message);
        }

        public static void WarnFormat(string message, params object[] args) {
            loggerConfig.WarnFormat(message, args);
        }

        public static void Error(string message) {
            loggerConfig.Error(message);
        }

        public static void ErrorFormat(string message, params object[] args) {
            loggerConfig.ErrorFormat(message, args);
        }
    }
}
