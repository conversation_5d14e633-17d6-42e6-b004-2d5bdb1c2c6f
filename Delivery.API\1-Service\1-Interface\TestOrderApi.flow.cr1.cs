﻿

using BLL;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using testorder.dal;
using DM.TestOrder.formData;
using DM.TestOrder.Model;
using DM.TestOrder.DAL;
using DM.TestOrder.Common;



namespace DM.TestOrder.Service.Interface {
    public partial class TestOrderApi {

        public string RemoveOrder(int orderId) {

            var errmsg = "OK";
            try {
                var rtn = WoTestOrderDal.QueryLock(orderId);
                if (rtn != "OK") {
                    errmsg = rtn;
                    return errmsg;
                }

                WoFlowDal.RemoveOrder(orderId);
            }
            catch (Exception ex) {

                errmsg = ex.Message;
                if (ex.InnerException != null)
                    errmsg += "; " + ex.InnerException.Message;

                return errmsg;
            }
            return errmsg;
        }
 
    }
}
