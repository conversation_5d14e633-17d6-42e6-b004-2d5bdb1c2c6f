﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ENPC.Kolo.Entity.NBIOT
{
    /// <summary>
    /// 请求监控点数据(异步)
    /// </summary>
    public class GetAsynData : BMessage
    {
        [Newtonsoft.Json.JsonProperty(Order = 1)]
        public List<string> DeviceList { get; set; }

        public GetAsynData() : base()
        {
            MessageType = (int)BMessageType.GET_ASYN_DATA;
        }

        public GetAsynData(string fsuId, List<string> deviceList) : base()
        {
            MessageType = (int)BMessageType.GET_ASYN_DATA;
            DeviceList = deviceList;
            FSUID = fsuId;
        }

        public override string Serialize()
        {
            string result = string.Empty;
            try
            {
                JsonSerializerSettings settings = new JsonSerializerSettings();
                settings.ContractResolver = new CustomContractResolver();
                TIotEntity iotEntity = new TIotEntity(this);
                result = JsonConvert.SerializeObject(iotEntity, settings);
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetAsynData.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetAsynData.Serialize():Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
            }
            return result;
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            string str = String.Format("{0}, {1}, {2}: ",
                MessageId, (BMessageType)MessageType, FSUID);
            sb.Append(str);

            foreach (string id in DeviceList)
            {
                sb.AppendFormat("[{0}]", id);
            }
            return sb.ToString();
        }
    }
}