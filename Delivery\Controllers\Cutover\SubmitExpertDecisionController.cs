﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web.Http;

using DM.TestOrder.Model;
using DM.TestOrder.Service.Interface;


namespace DM.TestOrder.Controllers {
        
    public class SubmitExpertDecisionController : BaseApiController{

        public HttpResponseMessage POST([FromBody]JObject value) {
            var sectionExpertApprove = Newtonsoft.Json.JsonConvert.DeserializeObject<SectionExpertApprove>(value.ToString());

            var rtnmsg=TestOrderApi.Instance.Section2_SubmitExpertDecision(sectionExpertApprove);

            var rtn = new {
                errormsg = rtnmsg
            };

            var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(rtn);
            Debug.WriteLine("输入: "+value.ToString()+"\r\n输出: " + jsonRtn.ToString());

            return new HttpResponseMessage() {
                Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            };
        }

    }
}
