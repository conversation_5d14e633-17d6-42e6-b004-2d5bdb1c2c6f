﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ENPC.Kolo.Entity.B_CUCC
{
    public sealed class SetFTP : BMessage
    {
        public string UserName { get; set; }
        public string Password { get; set; }
        public SetFTP(string suids, string surids, string userName, string password) : base()
        {
            MessageType = (int)BMessageType.SET_FTP;
            SUId = suids;
            SURId = surids;
            UserName = userName;
            Password = password;
        }
        public override string Serialize()
        {
            try
            {
                XmlDocument xmldoc = CreateXmlDocument();
                AppendPK_Type(xmldoc, "Request", BMessageType.SET_FTP.ToString(), MessageType.ToString());
                XmlElement xel = xmldoc.CreateElement("Info");
                XmlElement xel1 = xmldoc.CreateElement("SUId");
                XmlElement xel2 = xmldoc.CreateElement("SURId");
                XmlElement xel3 = xmldoc.CreateElement("UserName");
                XmlElement xel4 = xmldoc.CreateElement("Password");
                xel1.InnerText = SUId;
                xel2.InnerText = SURId;
                xel3.InnerText = UserName;
                xel4.InnerText = Password;
                xel.AppendChild(xel1);
                xel.AppendChild(xel2);
                xel.AppendChild(xel3);
                xel.AppendChild(xel4);
                XmlNode node = xmldoc.SelectSingleNode("Request");
                node.AppendChild(xel);
                StringXML = xmldoc.InnerXml;
                entityLogger.DebugFormat("SetFTP.Serialize(),xml:\r\n{0}", FormatXml(StringXML));
                return xmldoc.InnerXml;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("SetFTP.Serialize():{0}", ex.Message);
                entityLogger.ErrorFormat("SetFTP.Serialize() Stack=[{0}]", ex.StackTrace);
                ErrorMsg = ex.Message;
                return string.Empty;
            }
        }
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1},{2},{3},{4},{5}", MessageId, (BMessageType)MessageType, SUId, SURId, UserName, Password);
            return sb.ToString();
        }
    }
}
