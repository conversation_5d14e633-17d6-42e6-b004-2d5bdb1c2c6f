﻿using Delivery.Common.NoProcedure.AccessProvider;
using Delivery.Common.NoProcedure.Service.CMCC;
using Delivery.Common.NoProcedure.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace Delivery.Common.NoProcedure.Service
{
    public class SP_WRFsuService
    {
        private static SP_WRFsuService _instance = null;

        public static SP_WRFsuService Instance
        {
            get
            {
                if (_instance == null) _instance = new SP_WRFsuService();
                return _instance;
            }
        }
        public DataTable UpdWRFsu(string WRFsuId, string WRHouseId, string FsuCode, string FsuName, string IPAddress, string UserName, string Password, string FtpUserName, string FtpPassword, string Remark, string ContractNo, string ProjectName, string LogonId)
        {
            if (WRFsuId == null)
                return null;
            using (DbHelper dbHelper = new DbHelper())
            {
                //返回字段表
                DataTable ReturnTable = new DataTable();
                DataColumn newColumn = new DataColumn("ReturnValue", typeof(int));
                ReturnTable.Columns.Add(newColumn);

                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                //定义变量
                int? WRStationId = null, SWStationId = null, ManufacturerId = null, SWMonitorUnitId = null,  myStatus = null,  BInterfaceHostT = null;
                string StationCode = null, StationName = null, HouseCode = null, HouseName = null, OriFsuCode = null, UpdateString = null, LastString = null;
                try
                {
                    //获取各个字段的值
                    realParams.Add("WRHouseId", WRHouseId);
                    DataTable filedNameDt1 = executeHelper.ExecDataTable("SP_WRFsu_UpdWRFsu_GetSomeFiledName1", realParams);
                    if (filedNameDt1 != null && filedNameDt1.Rows.Count > 0)
                    {
                        WRStationId = filedNameDt1.Rows[0].Field<int?>("WRStationId");
                        SWStationId = filedNameDt1.Rows[0].Field<int?>("SWStationId");
                        HouseCode = filedNameDt1.Rows[0].Field<string>("HouseCode");
                        StationCode = filedNameDt1.Rows[0].Field<string>("StationCode");
                        HouseName = filedNameDt1.Rows[0].Field<string>("HouseName");
                        StationName = filedNameDt1.Rows[0].Field<string>("StationName");
                    }
                    realParams.Add("WRStationId",  CommonUtils.GetNullableValue(WRStationId));
                    realParams.Add("HouseCode", CommonUtils.GetNullableValue(HouseCode));
                    realParams.Add("HouseName", CommonUtils.GetNullableValue(HouseName));
                    realParams.Add("StationCode", CommonUtils.GetNullableValue(StationCode));
                    realParams.Add("StationName", CommonUtils.GetNullableValue(StationName));
                    //获取OriFsuCode值
                    realParams.Add("WRFsuId", WRFsuId);
                    DataTable filedNameDt2 = executeHelper.ExecDataTable("SP_WRFsu_UpdWRFsu_GetSomeFiledName2", realParams);
                    if (filedNameDt2 != null && filedNameDt2.Rows.Count > 0)
                    {
                        OriFsuCode = filedNameDt2.Rows[0].Field<string>("OriFsuCode");
                    }

                    realParams.Add("FsuCode", FsuCode);
                    object judgeExist1 = executeHelper.ExecuteScalar("SP_WRFsu_UpdWRFsu_JudgeExist1", realParams);
                    if (judgeExist1 != DBNull.Value && judgeExist1 != null)
                    {
                        DataRow newRow = ReturnTable.NewRow();
                        newRow["ReturnValue"] = -1;
                        ReturnTable.Rows.Add(newRow);
                        return ReturnTable;
                    }

                    realParams.Add("FsuName", CommonUtils.GetNullableValue(FsuName));
                    realParams.Add("SWStationId", CommonUtils.GetNullableValue(SWStationId));
                    realParams.Add("OriFsuCode", CommonUtils.GetNullableValue(OriFsuCode));
                    object judgeExist2 = executeHelper.ExecuteScalar("SP_WRFsu_UpdWRFsu_JudgeExist2", realParams);
                    object judgeExist3 = executeHelper.ExecuteScalar("SP_WRFsu_UpdWRFsu_JudgeExist3", realParams);
                    if ((judgeExist2 != DBNull.Value && judgeExist2 != null) || (judgeExist3 != DBNull.Value && judgeExist3 != null))
                    {
                        DataRow newRow = ReturnTable.NewRow();
                        newRow["ReturnValue"] = -2;
                        ReturnTable.Rows.Add(newRow);
                        return ReturnTable;
                    }

                    // 提取 FsuCode 的前两个字符
                    string firstTwoChars = FsuCode.Substring(0, 2);
                    // 将提取的字符串转换为整数
                    ManufacturerId = Convert.ToInt32(firstTwoChars);
                    realParams.Add("ManufacturerId", ManufacturerId);
                    if (ManufacturerId != null)
                    {
                        object judgeExist4 = executeHelper.ExecuteScalar("SP_WRFsu_UpdWRFsu_JudgeExist4", realParams);
                        if (judgeExist4 == DBNull.Value || judgeExist4 == null)
                        {
                            DataRow newRow = ReturnTable.NewRow();
                            newRow["ReturnValue"] = -3;
                            ReturnTable.Rows.Add(newRow);
                            return ReturnTable;
                        }
                    }
                    else
                    {
                        DataRow newRow = ReturnTable.NewRow();
                        newRow["ReturnValue"] = -3;
                        ReturnTable.Rows.Add(newRow);
                        return ReturnTable;
                    }

                    //获取SWMonitorUnitId值
                    DataTable filedNameDt3 = executeHelper.ExecDataTable("SP_WRFsu_UpdWRFsu_GetSomeFiledName3", realParams);
                    if (filedNameDt3 != null && filedNameDt3.Rows.Count > 0)
                    {
                        SWMonitorUnitId = (int)filedNameDt3.Rows[0]["SWMonitorUnitId"];
                    }

                    realParams.Add("IPAddress", IPAddress);
                    realParams.Add("SWMonitorUnitId", CommonUtils.GetNullableValue(SWMonitorUnitId));
                    object judgeExist5 = executeHelper.ExecuteScalar("SP_WRFsu_UpdWRFsu_JudgeExist5", realParams);
                    object judgeExist6 = executeHelper.ExecuteScalar("SP_WRFsu_UpdWRFsu_JudgeExist6", realParams);
                    if ((judgeExist5 != DBNull.Value && judgeExist5 != null) || (judgeExist6 != DBNull.Value && judgeExist6 != null))
                    {
                        DataRow newRow = ReturnTable.NewRow();
                        newRow["ReturnValue"] = -4;
                        ReturnTable.Rows.Add(newRow);
                        return ReturnTable;
                    }

                    //获取myStatus字段值
                    DataTable filedNameDt4 = executeHelper.ExecDataTable("SP_WRFsu_UpdWRFsu_GetSomeFiledName4", realParams);
                    if (filedNameDt4 != null && filedNameDt4.Rows.Count > 0)
                    {
                        myStatus = (int)filedNameDt4.Rows[0]["FsuStatus"];
                    }

                    //拼接LastString字段值
                    object LastStringObj = executeHelper.ExecuteScalar("SP_WRFsu_UpdWRFsu_GetSomeFiledName5", realParams);
                    if (LastStringObj != DBNull.Value && LastStringObj != null)
                        LastString = LastStringObj.ToString();
                    UpdateString = $"FsuCode:{FsuCode}-FsuName:{FsuName}-IPAddress:{IPAddress}-UserName:{UserName}-Password:{Password}-FtpUserName:{FtpUserName}-FtpPassword:{FtpPassword}-Remark:{Remark}-ContractNo:{ContractNo}-ProjectName:{ProjectName}";

                    //后续再调用Call SP_WR_OperationRecord (WRStationId,StationName,9,WRFsuId,FsuName,UpdateString,LastString,LogonId);
                    Public_StoredService.Instance.SP_WR_OperationRecord(WRStationId, StationName, 9, WRFsuId, FsuName, UpdateString, LastString, LogonId);

                    realParams.Add("UserName", CommonUtils.GetNullableValue(UserName));
                    realParams.Add("Password", CommonUtils.GetNullableValue(Password));
                    realParams.Add("FtpUserName", CommonUtils.GetNullableValue(FtpUserName));
                    realParams.Add("FtpPassword", CommonUtils.GetNullableValue(FtpPassword));
                    realParams.Add("Remark", CommonUtils.GetNullableValue(Remark));
                    realParams.Add("ContractNo", CommonUtils.GetNullableValue(ContractNo));
                    realParams.Add("ProjectName", CommonUtils.GetNullableValue(ProjectName));
                    if (myStatus != 3)
                    {
                        executeHelper.ExecuteNonQuery("SP_WRFsu_UpdWRFsu_UpdateStatusNotThree", realParams);
                        executeHelper.Commit();
                    }
                    else
                    {
                        executeHelper.ExecuteNonQuery("SP_WRFsu_UpdWRFsu_UpdateStatusIsThree", realParams);
                        executeHelper.ExecuteNonQuery("SP_WRFsu_UpdWRFsu_UpdateTSL_MonitorUnit", realParams);
                        executeHelper.ExecuteNonQuery("SP_WRFsu_UpdWRFsu_UpdateTSL_MonitorUnitCMCC", realParams);

                        //更新B接口诊断设备
                        object BInterfaceHostTObj = executeHelper.ExecuteScalar("SP_WRFsu_UpdWRFsu_GetBEquipmentInfo", null);
                        if (BInterfaceHostTObj != DBNull.Value && BInterfaceHostTObj != null)
                            BInterfaceHostT = (int)BInterfaceHostTObj;
                        realParams.Add("BInterfaceHostT", CommonUtils.GetNullableValue(BInterfaceHostT));
                        executeHelper.ExecuteNonQuery("SP_WRFsu_UpdWRFsu_UpdateTBL_Equipment", realParams);
                        //更新工程信息
                        executeHelper.ExecuteNonQuery("SP_WRFsu_UpdWRFsu_UpdateTBL_MonitorUnitProjectInfo", realParams);
                        executeHelper.ExecuteNonQuery("SP_WRFsu_UpdWRFsu_UpdateTBL_ConfigChangeMicroLog", realParams);
                        executeHelper.Commit();
                    }

                }
                catch (Exception ex)
                {
                    executeHelper.Rollback();
                    Logger.Log(ex);
                    return null;
                }
                DataRow newDtRow = ReturnTable.NewRow();
                newDtRow["ReturnValue"] = 1;
                ReturnTable.Rows.Add(newDtRow);
                return ReturnTable;
            }
        }

        public DataTable ApproveWRFsu(string WRFsuId)
        {
            //返回字段表
            DataTable ReturnTable = new DataTable();
            DataColumn newColumn = new DataColumn("ReturnValue", typeof(int));
            ReturnTable.Columns.Add(newColumn);
            DataRow newDtRow = ReturnTable.NewRow();
            if (WRFsuId == null)
            {
                newDtRow["ReturnValue"] = 0;
                ReturnTable.Rows.Add(newDtRow);
                return ReturnTable;
            }

            using (DbHelper dbHelper = new DbHelper())
            {
                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                //定义变量
                int? FsuStatus = null  , SWStationId  = null, StationStatus = null, HouseStatus = null;
                string FsuName = null;
                try
                {
                    //获取各个字段的值
                    realParams.Add("WRFsuId", WRFsuId);
                    DataTable filedNameDt1 = executeHelper.ExecDataTable("SP_WRFsu_ApproveWRFsu_GetSomeFiledName1", realParams);
                    if (filedNameDt1 != null && filedNameDt1.Rows.Count > 0)
                    {
                        FsuStatus = filedNameDt1.Rows[0].Field<int?>("FsuStatus");
                        FsuName = filedNameDt1.Rows[0].Field<string>("FsuName");
                        SWStationId = filedNameDt1.Rows[0].Field<int?>("SWStationId");
                        StationStatus = filedNameDt1.Rows[0].Field<int?>("StationStatus");
                        HouseStatus = filedNameDt1.Rows[0].Field<int?>("HouseStatus");
                    }
                    
                    realParams.Add("SWStationId", CommonUtils.GetNullableValue(SWStationId));
                    string swEquipmentName = "BInterface自诊断设备-" + FsuName;
                    //FSU所属机房申请尚未通过审核,不允许审核FSU
                    if (HouseStatus != 3) {
                        newDtRow["ReturnValue"] = -1;
                        ReturnTable.Rows.Add(newDtRow);
                        return ReturnTable;
                    }
                    //FSU所属站址申请尚未通过审核,不允许审核FSU
                    if (StationStatus != 3)
                    {
                        newDtRow["ReturnValue"] = -2;
                        ReturnTable.Rows.Add(newDtRow);
                        return ReturnTable;
                    }
                    //已审核过的FSU不能重复审核
                    if (FsuStatus.Value == 3)
                    {
                        newDtRow["ReturnValue"] = -2;
                        ReturnTable.Rows.Add(newDtRow);
                        return ReturnTable;
                    }
                    int SWMonitorUnitId = -1;
                    //调用存储过程call SP_GenerateSiteWebId('TSL_MonitorUnit',SWMonitorUnitId)获取SWMonitorUnitId值,
                    Public_StoredService.Instance.SP_GenerateSiteWebId("TSL_MonitorUnit", out SWMonitorUnitId, executeHelper);
                    realParams.Add("SWMonitorUnitId", CommonUtils.GetNullableValue(SWMonitorUnitId));
                    //写监控单元
                    executeHelper.ExecuteNonQuery("SP_WRFsu_ApproveWRFsu_InsertIntoTSLMonitorUnit", realParams);
                    //写监控单元CMCC表
                    executeHelper.ExecuteNonQuery("SP_WRFsu_ApproveWRFsu_InsertIntoTSLMonitorUnitCMCC", realParams);

                    int SWPortId = -1;
                    //调用存储过程call SP_GenerateSiteWebId('TSL_Port', SWPortId)获取SWPortId值
                    Public_StoredService.Instance.SP_GenerateSiteWebId("TSL_Port", out SWPortId, executeHelper);
                    realParams.Add("SWPortId", CommonUtils.GetNullableValue(SWPortId));
                    //写端口表
                    executeHelper.ExecuteNonQuery("SP_WRFsu_ApproveWRFsu_InsertIntoTSLPort", realParams);

                    int SWSamplerUnitId = -1;
                    //调用存储过程call SP_GenerateSiteWebId('TSL_SamplerUnit', SWSamplerUnitId)获取SWSamplerUnitId值
                    Public_StoredService.Instance.SP_GenerateSiteWebId("TSL_SamplerUnit", out SWSamplerUnitId, executeHelper);
                    realParams.Add("SWSamplerUnitId", CommonUtils.GetNullableValue(SWSamplerUnitId));

                    int? SWSamplerId = null;
                    string  SamplerUnitName = null, swDllPath = null;
                    DataTable filedNameDt2 = executeHelper.ExecDataTable("SP_WRFsu_ApproveWRFsu_GetSomeFiledName2", null);
                    if (filedNameDt2 != null && filedNameDt2.Rows.Count > 0)
                    {
                        SWSamplerId = (int)filedNameDt2.Rows[0].Field<int?>("SWSamplerId");
                        SamplerUnitName = filedNameDt2.Rows[0].Field<string>("SamplerUnitName");
                        swDllPath = filedNameDt2.Rows[0].Field<string>("swDllPath");
                    }
                    realParams.Add("SWSamplerId", CommonUtils.GetNullableValue(SWSamplerId));
                    realParams.Add("SamplerUnitName", CommonUtils.GetNullableValue(SamplerUnitName) );
                    realParams.Add("swDllPath", CommonUtils.GetNullableValue(swDllPath));
                    //写采集单元表
                    executeHelper.ExecuteNonQuery("SP_WRFsu_ApproveWRFsu_InsertIntoTSLSamplerUnit", realParams);

                    int swEquipmentId = -1;
                    //调用存储过程CALL SP_GenerateSiteWebId ('TBL_Equipment', swEquipmentId );获取swEquipmentId值
                    Public_StoredService.Instance.SP_GenerateSiteWebId("TBL_Equipment", out swEquipmentId, executeHelper);

                    realParams.Add("swEquipmentId", CommonUtils.GetNullableValue(swEquipmentId));
                    realParams.Add("swEquipmentName", CommonUtils.GetNullableValue(swEquipmentName));
                    object swEquipmentTemplateIdObj = executeHelper.ExecuteScalar("SP_WRFsu_ApproveWRFsu_GetEquTemplateId", null);
                    string swEquipmentTemplateId ;
                    if (swEquipmentTemplateIdObj != DBNull.Value && swEquipmentTemplateIdObj != null ) {
                        swEquipmentTemplateId = swEquipmentTemplateIdObj.ToString();
                        realParams.Add("swEquipmentTemplateId", swEquipmentTemplateId);
                    }
                    //写设备表
                    executeHelper.ExecuteNonQuery("SP_WRFsu_ApproveWRFsu_InsertIntoTBLEquipment", realParams);

                    //更新资源到已审核通过状态
                    executeHelper.ExecuteNonQuery("SP_WRFsu_ApproveWRFsu_UpdateWRFsuManagement", realParams);

                    //局房信息写TBL_ConfigChangeMicroLog
                    executeHelper.ExecuteNonQuery("SP_WRFsu_ApproveWRFsu_InsertIntoTBLConfigChangeMicroLog1", realParams);
                    executeHelper.ExecuteNonQuery("SP_WRFsu_ApproveWRFsu_InsertIntoTBLConfigChangeMicroLog2", realParams);
                    executeHelper.ExecuteNonQuery("SP_WRFsu_ApproveWRFsu_InsertIntoTBLConfigChangeMicroLog3", realParams);
                    executeHelper.ExecuteNonQuery("SP_WRFsu_ApproveWRFsu_InsertIntoTBLConfigChangeMicroLog4", realParams);

                    //信息写入TBL_ConfigChangeMacroLog
                    executeHelper.ExecuteNonQuery("SP_WRFsu_ApproveWRFsu_InsertIntoTBLConfigChangeMacroLog", realParams);

                    //局站更新
                    executeHelper.ExecuteNonQuery("SP_WRFsu_ApproveWRFsu_UpdateTBLConfigChangeMacroLog", realParams);

                    //写FSU合同信息表
                    executeHelper.ExecuteNonQuery("SP_WRFsu_ApproveWRFsu_InsertIntoTBLMonitorUnitProjectInfo", realParams);
                    executeHelper.Commit();
                }
                catch (Exception ex)
                {
                    executeHelper.Rollback();
                    Logger.Log(ex);
                    return null;
                }
                newDtRow["ReturnValue"] = 1;
                ReturnTable.Rows.Add(newDtRow);
                return ReturnTable;
            }
        }

        public DataTable UpdWRFsuCUCC(string WRFsuId, string WRHouseId, string FsuCode, string FsuName, string IPAddress, string UserName, string Password, string FtpUserName, string FtpPassword, string ManufacturerId, string Remark, string ContractNo, string ProjectName, string FsuRId, string LogonId)
        {
            if (WRFsuId == null)
                return null;
            using (DbHelper dbHelper = new DbHelper())
            {
                //返回字段表
                DataTable ReturnTable = new DataTable();
                DataColumn newColumn = new DataColumn("ReturnValue", typeof(int));
                ReturnTable.Columns.Add(newColumn);
                DataRow newDtRow = ReturnTable.NewRow();

                ExecuteHelper executeHelper = new ExecuteHelper(dbHelper, true);
                Dictionary<string, object> realParams = new Dictionary<string, object>();
                //定义变量
                int? WRStationId = null, SWStationId = null, FsuStatus = null, SWMonitorUnitId = null;
                string StationCode = null, StationName = null, HouseCode = null,  UpdateString = null, LastString = null;
                try
                {
                    realParams.Add("WRHouseId", CommonUtils.GetNullableValue(WRHouseId));
                    //获取变量值
                    DataTable filedNameDt1 = executeHelper.ExecDataTable("SP_WRFsu_UpdWRFsuCUCC_GetSomeFiledName1", realParams);
                    if (filedNameDt1 != null && filedNameDt1.Rows.Count > 0)
                    {
                        WRStationId = filedNameDt1.Rows[0].Field<int?>("WRStationId");
                        SWStationId = filedNameDt1.Rows[0].Field<int?>("SWStationId");
                        HouseCode = filedNameDt1.Rows[0].Field<string>("HouseCode");
                        StationCode = filedNameDt1.Rows[0].Field<string>("StationCode");
                    }

                    realParams.Add("WRFsuId", CommonUtils.GetNullableValue(WRFsuId));
                    realParams.Add("FsuCode", CommonUtils.GetNullableValue(FsuCode));
                    object judgeExist1 = executeHelper.ExecuteScalar("SP_WRFsu_UpdWRFsuCUCC_JudgeExist1", realParams);
                    if (judgeExist1 != DBNull.Value && judgeExist1 != null) {
                        newDtRow["ReturnValue"] = -1;
                        ReturnTable.Rows.Add(newDtRow);
                        return ReturnTable;
                    }


                    realParams.Add("WRStationId", CommonUtils.GetNullableValue(WRStationId));
                    realParams.Add("FsuName", CommonUtils.GetNullableValue(FsuName));
                    realParams.Add("SWStationId", CommonUtils.GetNullableValue(SWStationId));
                    object judgeExist2 = executeHelper.ExecuteScalar("SP_WRFsu_UpdWRFsuCUCC_JudgeExist2", realParams);
                    object judgeExist3 = executeHelper.ExecuteScalar("SP_WRFsu_UpdWRFsuCUCC_JudgeExist3", realParams);
                    if ((judgeExist2 != DBNull.Value && judgeExist2 != null) || (judgeExist3 != DBNull.Value && judgeExist3 != null))
                    {
                        newDtRow["ReturnValue"] = -2;
                        ReturnTable.Rows.Add(newDtRow);
                        return ReturnTable;
                    }

                    if (IPAddress != null && IPAddress.TrimStart() != "") {
                        realParams.Add("IPAddress", IPAddress);
                        object judgeExist4 = executeHelper.ExecuteScalar("SP_WRFsu_UpdWRFsuCUCC_JudgeExist4", realParams);
                        object judgeExist5 = executeHelper.ExecuteScalar("SP_WRFsu_UpdWRFsuCUCC_JudgeExist5", realParams);
                        if ((judgeExist4 != DBNull.Value && judgeExist4 != null) || (judgeExist5 != DBNull.Value && judgeExist5 != null)) {
                            newDtRow["ReturnValue"] = -4;
                            ReturnTable.Rows.Add(newDtRow);
                            return ReturnTable;
                        }
                    }
                    //获取FsuStatus和SWMonitorUnitId
                    DataTable filedNameDt2 = executeHelper.ExecDataTable("SP_WRFsu_UpdWRFsuCUCC_GetSomeFiledName2", realParams);
                    if (filedNameDt2 != null && filedNameDt2.Rows.Count > 0)
                    {
                        FsuStatus = filedNameDt2.Rows[0].Field<int?>("FsuStatus");
                        SWMonitorUnitId = filedNameDt2.Rows[0].Field<int?>("SWMonitorUnitId");
                    }
                    realParams.Add("SWMonitorUnitId", CommonUtils.GetNullableValue(SWMonitorUnitId));
                    realParams.Add("UserName", CommonUtils.GetNullableValue(UserName));
                    realParams.Add("Password", CommonUtils.GetNullableValue(Password));
                    realParams.Add("FtpUserName", CommonUtils.GetNullableValue(FtpUserName));
                    realParams.Add("FtpPassword", CommonUtils.GetNullableValue(FtpPassword));
                    realParams.Add("Remark", CommonUtils.GetNullableValue(Remark));
                    realParams.Add("FsuRId", CommonUtils.GetNullableValue(FsuRId));
                    realParams.Add("ContractNo", CommonUtils.GetNullableValue(ContractNo));
                    realParams.Add("ProjectName", CommonUtils.GetNullableValue(ProjectName));

                    //拼接LastString字段值
                    object LastStringObj = executeHelper.ExecuteScalar("SP_WRFsu_UpdWRFsuCUCC_GetSomeFiledName3", realParams);
                    if (LastStringObj != DBNull.Value && LastStringObj != null)
                        LastString = LastStringObj.ToString();
                    UpdateString = $"FsuCode:{FsuCode}-FsuName:{FsuName}-IPAddress:{IPAddress}-UserName:{UserName}-Password:{Password}-FtpUserName:{FtpUserName}-FtpPassword:{FtpPassword}-Remark:{Remark}-ContractNo:{ContractNo}-ProjectName:{ProjectName}";

                    //获取StationName
                    object StationNameObj = executeHelper.ExecuteScalar("SP_WRFsu_UpdWRFsuCUCC_GetSomeFiledName4", realParams);
                    if (StationNameObj != DBNull.Value && StationNameObj != null) {
                        StationName = StationNameObj.ToString();
                    }

                    //调用别人的存储过程CALL SP_WR_OperationRecord
                    Public_StoredService.Instance.SP_WR_OperationRecord(WRStationId, StationName, 9, WRFsuId, FsuName, UpdateString, LastString, LogonId);

                    realParams.Add("ManufacturerId", CommonUtils.GetNullableValue(ManufacturerId));
                    if (FsuStatus != null && FsuStatus != 3)
                    {
                        executeHelper.ExecuteNonQuery("SP_WRFsu_UpdWRFsuCUCC_UpdateWRFsuManagementCUCC_FsuStatusNotThree", realParams);
                    }
                    else {
                        executeHelper.ExecuteNonQuery("SP_WRFsu_UpdWRFsuCUCC_UpdateWRFsuManagementCUCC_FsuStatusIsThree", realParams);
                        executeHelper.ExecuteNonQuery("SP_WRFsu_UpdWRFsuCUCC_UpdateTSLMonitorUnit_FsuStatusIsThree", realParams);
                        executeHelper.ExecuteNonQuery("SP_WRFsu_UpdWRFsuCUCC_UpdateTSLMonitorUnitCUCC_FsuStatusIsThree", realParams);
                        executeHelper.ExecuteNonQuery("SP_WRFsu_UpdWRFsuCUCC_UpdateTBLMonitorUnitProjectInfo_FsuStatusIsThree", realParams);

                        string ObjectId = SWStationId + "." + SWMonitorUnitId;
                        realParams.Add("ObjectId", ObjectId);
                        executeHelper.ExecuteNonQuery("SP_WRFsu_UpdWRFsuCUCC_UpdateTBLConfigChangeMicroLog_FsuStatusIsThree", realParams);
                        executeHelper.ExecuteNonQuery("SP_WRFsu_UpdWRFsuCUCC_UpdateTBLConfigChangeMacroLog_FsuStatusIsThree", realParams);
                    }
                    executeHelper.Commit();
                }
                catch (Exception ex) {
                    executeHelper.Rollback();
                    Logger.Log(ex);
                    return null;
                }
                newDtRow["ReturnValue"] = 1;
                ReturnTable.Rows.Add(newDtRow);
                return ReturnTable;
            }
        }
    }
}
