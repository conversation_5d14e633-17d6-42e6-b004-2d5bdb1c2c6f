namespace BDSTool.Entity.S2
{
    using System;
    using System.Collections.Generic;
    
    public partial class TSL_Port
    {
        public int PortId { get; set; }
        public int MonitorUnitId { get; set; }
        public int PortNo { get; set; }
        public string PortName { get; set; }
        public int PortType { get; set; }
        public string Setting { get; set; }
        public string PhoneNumber { get; set; }
        public Nullable<int> LinkSamplerUnitId { get; set; }
        public string Description { get; set; }
    }
}
