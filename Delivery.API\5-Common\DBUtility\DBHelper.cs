﻿


using log4net;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace DM.TestOrder.Common.DBA
{
    public partial class DBHelper
    {
        private static readonly ILog loggerSql = LogManager.GetLogger("wosql");
        private static readonly ILog logger = LogManager.GetLogger("wo");

        private static DbConfig dbConfig = new DbConfig();
        
        static object lockDbConfig = new object();
        public static DbConfigPara GetDbConfig() {
            lock (lockDbConfig) {
                DbConfigPara dbConfigPara = new DbConfigPara();
                dbConfigPara.ConnectionString = dbConfig.ConnectionString;
                dbConfigPara.DataBaseTimeout  = dbConfig.DataBaseTimeout;
                dbConfigPara.ProviderName = dbConfig.ProviderName;
                return dbConfigPara;
            }
        }

        public static void SetDbConfig(DbConfig cfg) {
            dbConfig = cfg;
            logger.InfoFormat("DBHelper.SetDbConfig; ConnectionString={0}, DataBaseTimeout={1}", cfg.ConnectionString, cfg.DataBaseTimeout);
        }

        //可选初始化，默认从配置文件中读取
        //数据库超时时间，单位：秒 默认60秒
        public static void ManualSetDbConfig(string ConnectionString, int dataBaseTimeout = 60) {
            dbConfig.ConnectionString = ConnectionString;
            dbConfig.DataBaseTimeout = dataBaseTimeout;
        }

        public static string ExecuteScalar(string sql) {
            try {

                loggerSql.Debug("[sql] " + sql);
                //logger.Debug("[sql] " + sql);

                using (var dbHelper = new DatabaseHelper(GetDbConfig())) {
                    dbHelper.CreateTextCommand(sql);
                    var rtn = dbHelper.ExecuteScalar();

                    if (rtn != null)
                        return rtn.ToString();
                    else
                        return null;
                    }
            }
            catch (Exception e) {
                LogError(sql, e);
                throw e;
            }
        }

        public static void ExecuteNonQuery(string sql) {
            try {
                using (var dbHelper = new DatabaseHelper(GetDbConfig())) {
                     dbHelper.CreateTextCommand(sql);
                    dbHelper.ExecuteNoQuery();
                }
            }
            catch (Exception e) {
                LogError(sql, e);
                throw e;
            }

            loggerSql.Debug("[sql] " + sql);
            //logger.Debug("[sql] " + sql);
        }

        public static void ExecuteNonQueryInBatch(string sql) {
            try {
                using (var dbHelper = new DatabaseHelper(GetDbConfig())) {
                    dbHelper.CreateTextCommand(sql);
                    dbHelper.ExecuteNoQuery();
                }
            }
            catch (Exception e) {
                LogError(sql, e);
                throw e;
            }
            sql = sql.Replace("\r", "").Replace("\n", "");
            loggerSql.Debug("[sql] " + sql);
            //logger.Debug("[sql] " + sql);
        }

        public static DataTable GetTable(string sql) {
            DataTable dataTable = null;
            try {
                using (var dbHelper = new DatabaseHelper(GetDbConfig())) {
                    dbHelper.CreateTextCommand(sql);
                    dataTable= dbHelper.ExecuteDataTable();
                }
            }
            catch (Exception e) {
                LogError(sql, e);
                throw e;
            }

            loggerSql.Debug("[sql] " + sql);
            //logger.Debug("[sql] " + sql);

            return dataTable;
        }

        public static DataSet GetData(string sql) {
            DataSet ds = null;
            try {
                using (var dbHelper = new DatabaseHelper(GetDbConfig())) {
                    dbHelper.CreateTextCommand(sql);
                    ds=dbHelper.ExecuteDataSet();
                }
            }
            catch (Exception e) {
                LogError(sql, e);
                throw e;
            }

            loggerSql.Debug("[sql] " + sql);
            //logger.Debug("[sql] " + sql);
            return ds;
        }

        private static void LogError(string sql, Exception e) {
            logger.Error("[Error] sql: " + sql);
            logger.Error("[Error] error: " + e.Message);

            loggerSql.Error("[Error] sql: " + sql);
            loggerSql.Error("[Error] error: " + e.Message);
        }

        public static object SaveXmlData(string procName, string[] pNames, string[] pValues, string xmlName, string xmlValue) {
            object rtn = null;
            try {
                using (var dbHelper = new DatabaseHelper(GetDbConfig())) {
                    dbHelper.CreateProcedureCommand(procName);

                    for (int i = 0; i < pNames.Count(); i++) {
                        dbHelper.AddDbParameter(dbHelper.GetDbParameter(pNames[i], DbType.AnsiString, pValues[i]));
                    }

                    dbHelper.AddDbParameter(dbHelper.GetDbParameter(xmlName, DbType.Xml, xmlValue));
                    rtn= dbHelper.ExecuteScalar();
                }
            }
            catch (Exception e) {
                logger.Error   ("SaveXmlData(); error=" + e.Message);
                loggerSql.Error("SaveXmlData();error=" + e.Message);
                throw e;
            }


            xmlValue=xmlValue.Replace("\r", "").Replace("\n", "");

            var slog = string.Format("[sql] SaveXmlData(),存储过程:{0}; pNames={1},pValues={2}, xmlValue={3}",
               procName,
               string.Join(",",pNames), 
               string.Join(",",pValues),
               xmlValue);

            loggerSql.Debug(slog);
            return rtn;

        }
    }
}
