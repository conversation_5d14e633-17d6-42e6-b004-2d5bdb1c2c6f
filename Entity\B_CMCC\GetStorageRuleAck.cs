﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;


namespace ENPC.Kolo.Entity.B_CMCC
{
    /// <summary>
    /// 监控点存储规则应答报文
    /// </summary>
    public sealed class GetStorageRuleAck : BMessage
    {

        public EnumResult Result { get; private set; }

        public List<Device> Values { get; private set; }

        public string FailureCause { get; private set; }

        #region SiteWeb配置

        public int MyStationId { get; set; }

        #endregion

        public GetStorageRuleAck( EnumResult result, List<Device> values, string failureCause)
            : base()
        {
            MessageType = (int)BMessageType.GET_STORAGERULE_ACK;

            Result = result;
            Values = values;
            FailureCause = failureCause;
        }

        public GetStorageRuleAck()
            : base() 
        {
            MessageType = (int)BMessageType.GET_STORAGERULE_ACK;
        }


        public static GetStorageRuleAck Deserialize(XmlDocument xmlDoc)
        {
            GetStorageRuleAck getStorageRuleAck = null;
            string errorMsg = string.Empty;
            try
            {
                string strResult = xmlDoc.SelectSingleNode("/Response/Info/Result").InnerText.Trim().ToUpper();
                EnumResult result = CheckEnumResult(strResult, ref errorMsg);//字符串类型判别
                string failureCause = xmlDoc.SelectSingleNode("/Response/Info/FailureCause").InnerText.Trim();

                XmlNodeList nodelist = xmlDoc.SelectNodes("/Response/Info/Values/DeviceList/Device");
                List<Device> deviceList = new List<Device>();
                foreach (XmlNode nodeDevice in nodelist)
                {
                    string deviceId = nodeDevice.Attributes["ID"].Value.Trim();
                    List<TStorageRule> tStorageRuleList = new List<TStorageRule>();
                    foreach (XmlNode node in nodeDevice.ChildNodes)
                    {
                        EnumType type = (EnumType)int.Parse(node.Attributes["Type"].Value.Trim());
                        string id = node.Attributes["ID"].Value.Trim();
                        if (!CheckNumberValue(id))
                        {
                            errorMsg = "TStorageRule.ID is invalid";
                            entityLogger.ErrorFormat("GetStorageRuleAck.Deserialize():{0}, abandon record, ID = {1}", errorMsg, id);
                            continue;
                        }
                        string strSignalNumber = node.Attributes["SignalNumber"].Value.Trim();
                        if (!string.IsNullOrEmpty(strSignalNumber) && !CheckNumberValue(strSignalNumber))
                        {
                            errorMsg = "TStorageRule.SignalNumber is invalid";
                            entityLogger.ErrorFormat("GetStorageRuleAck.Deserialize():{0}, abandon record, SignalNumber = {1}", errorMsg, strSignalNumber);
                            continue;
                        }
                        string signalNumber = strSignalNumber;//short.Parse(strSignalNumber);

///////////////////////////////////////////////////////
                        string strAbsoluteVal = node.Attributes["AbsoluteVal"].Value.Trim();
                        float? absoluteVal = null;
                        if (string.IsNullOrEmpty(strAbsoluteVal))
                        {
                            errorMsg = "TStorageRule.AbsoluteVal is NullOrEmtpy";
                            entityLogger.DebugFormat("GetStorageRuleAck.Deserialize():{0}", errorMsg);
                        }
                        else
                        {
                            absoluteVal = float.Parse(strAbsoluteVal);
                        }

                        string strRelativeVal = node.Attributes["RelativeVal"].Value.Trim();
                        float? relativeVal = null;
                        if (string.IsNullOrEmpty(strRelativeVal))
                        {
                            errorMsg = "TStorageRule.RelativeVal is NullOrEmtpy";
                            entityLogger.DebugFormat("GetStorageRuleAck.Deserialize():{0}", errorMsg);
                        }
                        else
                        {
                            relativeVal = float.Parse(strRelativeVal);
                        }
//////////////////////////////////////////////////////

                        //StorageInterval可为空
                        string strStorageInterval = node.Attributes["StorageInterval"].Value.Trim();
                        long? storageIntervalVal = null;
                        if (!string.IsNullOrEmpty(strStorageInterval))
                        {
                            //strStorageInterval = (float.Parse(strStorageInterval)).ToString();
                            int index = strStorageInterval.IndexOf(".");//带小数点
                            if (index > 0)
                            {
                                strStorageInterval = strStorageInterval.Substring(0, index);
                            }
                            storageIntervalVal = long.Parse(strStorageInterval);
                        }
                        string strStorageRefTime = node.Attributes["StorageRefTime"].Value.Trim();
                        string storageRefTime = string.Empty;
                        if(!string.IsNullOrEmpty(strStorageRefTime))
                        {
                            storageRefTime = Convert.ToDateTime(strStorageRefTime).ToString("yyyy-MM-dd HH:mm:ss");
                        }                        
                        TStorageRule tStorageRule = new TStorageRule(type, id, signalNumber, absoluteVal, relativeVal, storageIntervalVal,storageRefTime);
                        tStorageRuleList.Add(tStorageRule);
                    }

                    Device device = new Device(deviceId, tStorageRuleList);
                    deviceList.Add(device);
                }
                getStorageRuleAck = new GetStorageRuleAck(result, deviceList, failureCause);

                entityLogger.DebugFormat("GetStorageRuleAck.Deserialize(),xml:\r\n" + FormatXml(xmlDoc.InnerXml));
                getStorageRuleAck.StringXML = xmlDoc.InnerXml;
                return getStorageRuleAck;
            }
            catch (Exception ex)
            {
                entityLogger.ErrorFormat("GetStorageRuleAck.Deserialize():{0}", ex.Message);
                entityLogger.ErrorFormat("GetStorageRuleAck.Deserialize():Stack=[{0}]", ex.StackTrace);
                entityLogger.ErrorFormat("XmlDocument:\r\n" + FormatXml(xmlDoc.InnerXml));
                getStorageRuleAck = new GetStorageRuleAck();
                getStorageRuleAck.ErrorMsg = ex.Message;
                getStorageRuleAck.StringXML = xmlDoc.InnerXml;
                return getStorageRuleAck;
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat("{0}, {1}, {2}, {3}", MessageId, MessageType, Result, FailureCause);
            sb.AppendLine();
            if (Values != null)
            {
                foreach (Device device in Values)
                {
                    sb.AppendFormat("{0}\r\n", device.ToString());
                }
            }
            return sb.ToString();
        }


    }
}
