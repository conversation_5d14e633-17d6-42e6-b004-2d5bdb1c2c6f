﻿using Delivery.API;
using DM.TestOrder.DAL;
using Microsoft.AspNetCore.Mvc;




namespace DM.TestOrder.Controllers {
        
    public class GeoController : BaseController{

        //http://localhost/orderapi/Geo
        [HttpGet]
        public JsonResult Get(decimal latitude, decimal longitude) {

            var rtnMsg = WoGeoDal.Validate(latitude, longitude);


            //Debug.WriteLine(latitude + "/t" + latitude);

            var rtn = new {
                errormsg = rtnMsg
            };
            //var jsonRtn = Newtonsoft.Json.JsonConvert.SerializeObject(rtn);

            //return new HttpResponseMessage() {
            //    Content = new StringContent(jsonRtn, Encoding.UTF8, "application/json"),
            //};
            return new JsonResult(rtn);

        }
    }
}
